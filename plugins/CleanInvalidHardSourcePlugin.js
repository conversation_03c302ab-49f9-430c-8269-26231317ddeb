const fsPromises = require('fs').promises
const path = require('path')

// 清理过期无效的 hard-source 缓存脏目录
class CleanInvalidHardSourcePlugin {
  constructor(config = {}) {
    this.config = {
      cacheHardSourcePath: './node_modules/.cache/hard-source',
      throwError: true,
      ...config
    }
  }

  apply(compiler) {
    compiler.hooks.done.tap('done', async () => {
      try {
        const dirPath = path.resolve(process.cwd(), this.config.cacheHardSourcePath)
        const dirList = await fsPromises.readdir(dirPath)

        if (Array.isArray(dirList) && dirList.length > 1) {
          let record = {
            timestamp: 0,
            dirName: ''
          }

          for (let dirName of dirList) {
            const stat = await fsPromises.stat(path.join(dirPath, dirName))
            const currentCreateTimestamp = +new Date(stat.ctime)

            if (record.timestamp < currentCreateTimestamp) {
              if (record.dirName) {
                fsPromises.rmdir(path.join(dirPath, record.dirName), {
                  recursive: true
                })
              }

              Object.assign(record, {
                timestamp: currentCreateTimestamp,
                dirName
              })
            } else {
              fsPromises.rmdir(path.join(dirPath, dirName), {
                recursive: true
              })
            }
          }
        }
      } catch (err) {
        if (this.config.throwError) {
          console.error('# CleanInvalidHardSourcePlugin: ', err)
        }
      }
    })
  }
}

module.exports = CleanInvalidHardSourcePlugin
