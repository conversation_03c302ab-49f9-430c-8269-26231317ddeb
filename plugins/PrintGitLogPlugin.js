const GitRevisionPlugin = require('git-revision-webpack-plugin')
const gitRevisionPlugin = new GitRevisionPlugin({
  versionCommand: 'show --pretty=format:"%ai, %h%d" -s HEAD'
})

class gitLogPlugin {
  constructor(options = {}) {
    this.options = {
      fileName: 'chunk-vue',
      ...options
    }
  }

  apply(compiler) {
    const that = this
    compiler.plugin('emit', function(compilation, callback) {
      compilation.chunks.forEach(function(chunk) {
        chunk.files.forEach(function(fileName) {
          if (fileName.match(that.options.fileName)) {
            let log = `;console.log('%c ${gitRevisionPlugin.version()}', 'color:blue; background-color: #ff0;');`
            let fileContent = log + compilation.assets[fileName].source()

            compilation.assets[fileName] = {
              source: () => {
                return fileContent
              },
              size: () => {
                return Buffer.byteLength(fileContent, 'utf8')
              }
            }
          }
        })
      })

      callback()
    })
  }
}

module.exports = gitLogPlugin
