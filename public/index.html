<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <script>
      // 确保在应用初始化前加载配置
      try {
        window.process = { env: JSON.parse(atob("$KL_ARGS_KV")) };
      } catch (e) {}
    </script>
    <title><%= title %></title>
    <link rel="icon" href="<%= iconHref %>" />
    <link rel="dns-prefetch" href="https://widget.cloudinary.com" />
    <link rel="dns-prefetch" href="https://upload-widget.cloudinary.com" />
    <link rel="dns-prefetch" href="https://www.google-analytics.com" />
    <link rel="dns-prefetch" href="https://www.googletagmanager.com" />
    <link rel="dns-prefetch" href="https://lh3.googleusercontent.com" />
    <link rel="dns-prefetch" href="https://log.klook.com" />
  </head>

  <body
    class="<%= isMerchant ? 'platform-body-merchant' : 'platform-body-admin' %>"
  >
    <noscript>
      <strong
        >We're sorry but <%= title %> doesn't work properly without JavaScript
        enabled. Please enable it to continue</strong
      >
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
    <script defer>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l !== "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(
        window,
        document,
        "script",
        "dataLayer",
        "<%= isMerchant ? 'GTM-WB3JMSH' : 'GTM-TN7G6HT' %>"
      );
    </script>
  </body>
</html>
