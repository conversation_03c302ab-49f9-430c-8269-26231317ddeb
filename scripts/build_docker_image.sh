 #!/usr/bin/env bash

 set -ex

if [[ -z "${CODEBUILD_WEBHOOK_TRIGGER}" ]]; then
  echo "trigger from console"
else
	export CODEBUILD_SOURCE_VERSION=$(echo $CODEBUILD_WEBHOOK_TRIGGER | sed 's/branch\///1')
  export CODEBUILD_SOURCE_VERSION=$(echo $CODEBUILD_SOURCE_VERSION | sed 's/tag\///1')
fi
export CODEBUILD_SOURCE_VERSION=$(echo $CODEBUILD_SOURCE_VERSION | sed 's/\//-/1')
echo "branch:" $CODEBUILD_SOURCE_VERSION

# build for production
$(aws ecr get-login --no-include-email --region $AWS_DEFAULT_REGION)


docker build  --build-arg COMMIT_HASH  -t temp:cdn --target cdn -f ./Dockerfile .
docker build  --build-arg COMMIT_HASH  -t $DOCKER_IMAGE -f ./Dockerfile .


# REMOTE_IMAGE_TAG=$HARBOR_REGISTRY/$DOCKER_IMAGE:$CODEBUILD_SOURCE_VERSION-${CODEBUILD_RESOLVED_SOURCE_VERSION:0:8}
REMOTE_IMAGE_TAG=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$DOCKER_IMAGE:$CODEBUILD_SOURCE_VERSION-${CODEBUILD_RESOLVED_SOURCE_VERSION:0:8}

docker tag $DOCKER_IMAGE $REMOTE_IMAGE_TAG
echo "Pushing the Docker image, $REMOTE_IMAGE_TAG"
docker push $REMOTE_IMAGE_TAG



