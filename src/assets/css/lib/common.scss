@import "./reset.scss";

/* kl全局变量 start */
$klBlue: #0091ff;
$klDanger: rgba(255, 86, 48, 1);
$klGreen: #36b37e;

$klLink: #437dff;
$klOrange2merchant: #faad14;
$klDanger2merchant: #ff4d4f;
$klGreen2merchant: #389e0d;

$klHeaderHeight: 64px;
@mixin resetNumberStyle() {
  input[type="number"] {
    -moz-appearance: textfield;
  }
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}

/* kl全局变量 end */

.common-875px {
  max-width: 875px;
}

.common-form-label-flex {
  .ant-form-item-label {
    display: block;
    label {
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }
  }
}

/* admin样式 */
.common-card-box-style {
  padding: 24px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  color: #212121;
}
.common-form-item-reset-style {
  .ant-form {
    > .ant-form-item {
      margin-bottom: 32px;
      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }
  .ant-form-item-label {
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    line-height: 28px;
    height: 28px;
    margin-bottom: 8px;
    label {
      font-size: 20px;
    }
  }
  .ant-form-item-label > label:after {
    display: none;
  }
}

.common-form-item-reset-card-style {
  .ant-form {
    .ant-form-item {
      margin-bottom: 24px;
      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }
  .ant-form-item-label {
    margin-bottom: 4px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    line-height: 28px;
    label {
      font-size: 16px;
    }
  }
  .ant-form-item-label > label:after {
    display: none;
  }
  .label-after {
    margin-left: 4px;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    text-decoration: underline;
  }
}

body.platform-body-admin {
  .app-wrapper .main-container-main {
    .fixed-header .navbar {
      height: $klHeaderHeight;
    }
    section.app-main {
      min-height: calc(100vh - #{$klHeaderHeight});
      padding: $klHeaderHeight 0 0 0;
      background-color: #fff;
    }
  }
  .app-main {
    padding: 0;
  }
}

/* kl全局样式覆盖 start */
.kl-layout.ant-layout {
  #klook-logo {
    height: 64px;
  }
  #klook-feedback .feadback-btn {
    bottom: 120px;
  }
}
main#klook-conetent.ant-layout-content {
  padding: 0;
  .klook-breadcrumb.ant-breadcrumb {
    display: none;
  }
}

.ant-layout-content {
  z-index: 1;
}

p {
  margin: 0;
}
/* kl全局样式覆盖 start */

/* ezreal ant全局样式 start */
.common-reset-number-style {
  @include resetNumberStyle();
}

.common-form-horizontal-style {
  @include resetNumberStyle();
  .ant-row.ant-form-item {
    display: inline-block;
    vertical-align: top;
    margin-bottom: 14px;
    .ant-form-item-control {
      &.has-error,
      &.has-success {
        .ant-form-explain {
          position: absolute;
          bottom: -16px;
          left: 0;
          font-size: 14px;
        }
      }
    }
    .ant-col.ant-form-item-label {
      label {
        display: inline-block;
        width: 120px;
        line-height: 16px;
        vertical-align: middle;
        text-align: right;
        padding-right: 6px;

        white-space: pre-wrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
    .ant-col.ant-form-item-control-wrapper {
      display: inline-block;
      width: 200px;
      .ant-input-suffix {
        padding: 0 0 0 6px;
        height: 90%;
        background: transparent;
        // background: #fff;
      }
    }
  }
  &.row2 {
    .ant-row.ant-form-item {
      width: 50%;
    }
  }
}
.common-anchor-style {
  .ant-anchor {
  }
  &.hide-all {
    .ant-anchor-wrapper {
      background-color: transparent;
    }
    .ant-anchor {
      .ant-anchor-ink {
        display: none;
      }
      .ant-anchor-link {
        padding: 0;
      }
    }
  }
  &.reset-padding {
    .ant-anchor {
      padding-left: 0;
    }
  }
}
.common-reset-style {
  &.ant-row.ant-form-item {
    .ant-col {
      &.ant-form-item-control-wrapper {
        max-width: 100%;
      }
    }
  }
}
.ant-cascader-picker {
  &.common-ascader-style {
    width: 252px;
  }
}
.ant-tabs {
  &.common-line-style {
    .ant-tabs-bar {
      box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.09);
    }
  }
  &.ant-tabs-card.common-tabs-style {
    .ant-tabs-bar {
      margin: 0 0 22px 0;
      .ant-tabs-tab {
        border: 1px solid rgba(0, 0, 0, 0.09);
        background-color: #fff;
        box-sizing: border-box;
        border-radius: 0;
        font-weight: 400;
        &.ant-tabs-tab-active {
          border-bottom: 2px solid $klBlue;
        }
      }
    }
  }
}
.ant-select {
  &.common-select-style {
    width: 252px;
  }
}
.common-icon-select-down {
  position: relative;
  &::after {
    display: inline-block;
    content: "∨";
    position: absolute;
    top: 7px;
    right: 11px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.25);
    transform: scale(1.3, 0.9) rotate(0);
    transition: transform 0.3s;
  }
  &.ant-select-open::after {
    transform: scale(1.3, 0.9) rotate(180deg);
  }
}

.ant-input,
.ant-input-search,
.ant-input-affix-wrapper {
  &.common-input-style {
    width: 252px;
  }
}
.ant-input-search {
  &.common-search-style {
    .ant-input-search-button {
      border: 1px solid rgba(0, 0, 0, 0.09);
      border-radius: 2px;
      color: rgba(0, 0, 0, 0.85);
      border-left: none;
    }
    .ant-input {
      border: 1px solid rgba(0, 0, 0, 0.09);
      border-radius: 2px;
      color: rgba(0, 0, 0, 0.85);
    }
    .ant-input-suffix i {
      color: rgba(0, 0, 0, 0.25);
    }
  }
}
.common-modal-hide-cancel {
  .ant-modal-footer {
    button:first-child {
      display: none;
    }
  }
}
.ant-modal-root {
  &.common-line-style {
    .ant-modal-footer {
      box-shadow: inset 0px 1px 0px rgba(0, 0, 0, 0.09);
    }
  }
  &.common-modal-style {
    .ant-modal-header,
    .ant-modal-footer {
      padding: 20px 24px;
    }
    .ant-modal-close-x {
      color: #979797;
      height: 70px;
      line-height: 70px;
    }
    button[class="ant-btn"] {
      border: 1px solid rgba(0, 0, 0, 0.09);
      color: #000;
    }
  }
  &.border {
    .ant-modal-body {
      border-top: 1px solid #f0f0f0;
      border-bottom: 1px solid #f0f0f0;
    }
  }
  &.no-scroll {
    .ant-modal-body {
      overflow: hidden;
    }
  }
}
.ant-radio-group {
  &.common-radio-style {
    width: 100%;
    .radio-box {
      width: 50%;
      margin: 0;
    }
  }
  &.col000 {
    .ant-radio-wrapper {
      color: #000;
    }
  }
  &.common-hide-radio {
    .ant-radio {
      display: none;
    }
  }
}

@import "./mixins.scss";

.ant-tooltip {
  &.common-tooltip-style {
    max-width: 320px;

    &.custom-poi-style {
      max-width: 680px;
    }
    &.width-auto {
      max-width: 100%;
    }
    &.hide-arrow {
      .ant-tooltip-arrow {
        display: none;
      }
    }
    .ant-tooltip-arrow::before {
      background-color: #fff;
    }

    .ant-tooltip-inner {
      position: relative;
      max-height: 500px;
      color: rgba(0, 0, 0, 0.65);
      background: #fff;
      overflow-x: hidden;
      overflow-y: auto;

      .content {
        overflow-x: hidden;
        overflow-y: hidden;
        max-height: 452px;
        // box-shadow: none;

        @include mixin-hover-display-scrollbar;
      }
    }

    &.tooltip-rich-text-padding .ant-tooltip-inner {
      padding: 15px 0;

      .content {
        padding: 0 24px;
      }
    }

    .ant-tooltip-arrow {
      z-index: 99;
    }

    &.--theme-yellow {
      .ant-tooltip-arrow::before,
      .ant-tooltip-inner {
        background: #ffe58f;
      }
    }
  }
}
.ant-popover {
  &.common-popover-style {
    .ant-popover-inner-content {
      padding: 0;
    }
    &.common-action-style {
      .ant-popover-inner-content {
        padding: 16px 12px;
      }
    }
  }
  &.hide-arrow {
    .ant-popover-arrow {
      display: none;
    }
  }
}
.ant-collapse {
  &.common-collapse-style {
    background-color: #fff;
    .ant-collapse-item {
      border: none;
      &:nth-of-type(n + 1) {
        // box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.09);
      }
      .ant-collapse-content {
        &-box {
          padding-bottom: 0 !important;
        }
      }
      .ant-collapse-header {
        font-size: 16px;
        line-height: 32px;
        background-color: #fff;
        padding: 10px !important;
      }
    }
  }
  &.common-collapse-style.hide-collapse-header {
    .ant-collapse-item {
      .ant-collapse-header {
        padding: 0 !important;
      }
    }
  }
  &.common-collapse-style.common-merchant-style {
    padding: 0 6px 4px 6px;
    background-color: #fff;
    .ant-collapse-item {
      .ant-collapse-header {
        padding: 0 !important;
      }
    }
  }
  &.common-collapse-style.pkg-title-header {
    background-color: #fff;
    .ant-collapse-item {
      .ant-collapse-header {
        font-size: 16px;
        line-height: 32px;
        background-color: #fff;
        padding: 8px 16px !important;
      }
    }
  }
  &.common-collapse-style.units-unpublished-header {
    background-color: #fff;
    .ant-collapse-item {
      .ant-collapse-header {
        font-size: 16px;
        line-height: 24px;
        background-color: transparent;
        padding: 0 !important;
      }
    }
  }
}
.ant-timeline {
  .ant-timeline-item-head {
    background-color: transparent;
  }
  .ant-timeline-item-last {
    padding: 0;
  }
  .common-cicle-style {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #979797;
    background-color: #fff;
    box-sizing: content-box;
  }
}

/* ezreal antd全局样式 end */

/* 通用样式 start */
.common-sub-title {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;

  &.m-t-8 {
    margin-top: -8px;
    margin-bottom: 8px;
  }
}

.dsp-block {
  display: block;
  width: 100%;
}
.dsp-none {
  display: none;
}
.dsp-ib {
  display: inline-block;
}
.vsb-hidden {
  visibility: hidden;
}
.fw-bold {
  font-weight: bold;
}
.fw-400 {
  font-weight: 400;
}
.flex-auto {
  flex: 1 1 auto;
}
.ta-right {
  text-align: right;
}
.ta-left {
  text-align: left;
}
.ta-center {
  text-align: center;
}
.va-middle {
  vertical-align: middle;
}
.cs-pointer {
  cursor: pointer;
}
.float-left {
  float: left;
}
.float-right {
  float: right;
}
.common-required-star {
  color: #ff5630;
  &.star-box {
    display: inline-block;
    width: 12px;
  }
}
.common-required-star-before {
  &::before {
    color: #f5222d;
    content: "*";
    margin-right: 4px;
    font-family: SimSun, sans-serif;
    font-size: 14px;
    line-height: 1;
    display: inline-block;
  }
}
.common-more-box {
  button {
    display: block;
  }
}

.common-spu-main-box {
  padding: 32px 48px;
}
@mixin packageArchived {
  &.archived {
    color: rgba(0, 0, 0, 0.25);
    // border: 1px solid rgba(0, 0, 0, 0.25);
    border-color: rgba(0, 0, 0, 0.04);
    background: rgba(0, 0, 0, 0.04);
  }
}
.common-status-style {
  display: inline-block;
  font-size: 14px;
  line-height: 22px;
  padding: 0 4px;
  border-radius: 2px;
  &.upload-price {
    color: #52c41a;
    background: #f6ffed;
    border: 1px solid #b7eb8f;
  }
  &.current-price {
    color: #2f54eb;
    background: #f0f5ff;
    border: 1px solid #2f54eb;
  }
  &.unpublished {
    // color: rgba(0, 0, 0, 0.85);
    background: rgba(0, 0, 0, 0.04);
    // border: 1px solid rgba(0, 0, 0, 0.25);
    @include packageArchived;
  }
  &.published {
    color: $klGreen;
    background: rgba(54, 179, 126, 0.05);
    // border: 1px solid rgba(54, 179, 126, 0.45);
    @include packageArchived;
  }
  &.emToEdit {
    color: #00b8d9;
    background: rgba(0, 184, 217, 0.05);
    // border: 1px solid rgba(54, 179, 126, 0.45);
    @include packageArchived;
  }
  &.inPreview {
    color: #6554c0;
    background: rgba(101, 84, 192, 0.05);
    // border: 1px solid rgba(54, 179, 126, 0.45);
    @include packageArchived;
  }
  &.suspended {
    color: #f5222d;
    background-color: #fff1f0;
    @include packageArchived;
  }
  &.edit,
  &.to_be_submitted {
    background-color: rgba(0, 184, 217, 0.05);
    color: #00b8d9;
  }
  &.draft,
  &.pending_approval,
  &.pendingApproval {
    color: #ffab00;
    background: rgba(255, 171, 0, 0.05);
    // border: 1px solid #ffab00;
    @include packageArchived;
  }
  &.rejected {
    color: #ff5630;
    background: rgba(255, 86, 48, 0.05);
    @include packageArchived;
  }
  &.approved {
    color: $klGreen;
    background: rgba(54, 179, 126, 0.05);
    // border: 1px solid rgba(54, 179, 126, 0.45);
    @include packageArchived;
  }
  &.remove {
    color: $klDanger2merchant;
  }
  &.add-back {
    color: $klLink;
  }
}
.common-merchant-status-style {
  display: inline-block;
  font-size: 14px;
  line-height: 22px;
  &.upload-price {
    color: #52c41a;
  }
  &.current-price {
    color: #2f54eb;
  }
  &.unpublished {
    color: $klOrange2merchant;
  }
  &.published {
    color: $klGreen2merchant;
  }
  &.emToEdit {
    color: #00b8d9;
  }
  &.inPreview {
    color: #6554c0;
  }
  &.suspended {
    color: #f5222d;
  }
  &.edit,
  &.to_be_submitted {
    color: #13c2c2;
  }
  &.draft,
  &.pending_approval,
  &.pendingApproval {
    color: $klOrange2merchant;
  }
  &.rejected {
    color: $klDanger2merchant;
  }
  &.approved {
    color: $klGreen2merchant;
  }
}
.common-btns-wrap {
  position: fixed;
  z-index: 8;
  left: 0;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  box-shadow: inset 0px 1px 0px rgba(0, 0, 0, 0.09);
  .common-btns-box {
    width: 100%;
    text-align: right;
    button {
      margin: 8px 0 8px 8px;
    }
    button:last-child {
      margin-right: 20px;
    }
  }
}
.common-main-basic-box {
  display: flex;
  flex-direction: column;
}
.common-tpl-basic-style {
  .ant-col {
    &.ant-form-item-control-wrapper {
      max-width: 440px;
      .ant-form-item-control {
        line-height: 32px;
      }
    }
    &.ant-form-item-label {
      padding: 16px 0 8px 0;
      line-height: 20px;
    }
  }
  .ant-row.ant-form-item {
    min-height: 76px;
    box-sizing: border-box;
    margin-bottom: 0;
  }
}
.common-button-danger {
  &.ant-btn-link {
    color: $klDanger;
    &:hover {
      color: $klDanger;
    }
  }
  &.ant-btn.ant-btn-danger.ant-btn-background-ghost {
    &.ghost-bgc-fff {
      background-color: #fff !important;
    }
  }
}
.common-row-archived {
  td:not(div) {
    color: rgba(0, 0, 0, 0.25);
    cursor: text;
  }
}
.common-row-current-price {
  background-color: #fafafa;
}
.common-spin-style {
  position: absolute;
  left: calc(50% - 10px);
  top: calc(50% - 12px);
}
.common-spin-box {
  padding: 24px 0;
  text-align: center;
}
.common-spin-block {
  display: block;
  width: 100%;
  text-align: center;
}
.common-basic-box {
  background-color: #fff;
  padding: 20px;
  margin-bottom: 20px;
  h2 {
    position: relative;
    font-weight: 600;
    font-size: 24px;
    line-height: 29px;
    margin: 0;
    padding-left: 16px;
    &::before {
      position: absolute;
      z-index: 11;
      left: 0;
      top: 4px;
      height: 20px;
      content: "";
      border-left: 4px solid #0091ff;
    }
  }
}
.common-guide-basic-box {
  h2 {
    position: relative;
    font-weight: 600;
    font-size: 20px;
    line-height: 26px;
    margin: 0 0 16px 0;
  }
  &:not(:last-of-type) {
    margin-bottom: 32px;
  }
}
.common-form-style {
  &.reset-form-item {
    .ant-row {
      &.ant-form-item {
        margin: 0;
        .ant-form-item-control {
          line-height: initial;
        }
      }
    }
  }
}
.common-basic-form-style {
  .ant-col.ant-form-item-label {
    padding: 16px 0 8px 0;
    line-height: 20px;
  }
  .ant-input,
  .ant-select {
    width: 440px;
  }
  .ant-checkbox-group {
    .ant-checkbox-wrapper {
      margin: 0 12px 0 0;
    }
  }
  .ant-row.ant-form-item {
    min-height: 76px;
    box-sizing: border-box;
    margin-bottom: 0;
    &.common-form-sub-style {
      min-height: auto;
    }
  }
  .form-width {
    width: 440px;
  }
}
// 固定 drawer 的头部
div.ant-drawer-header {
  position: absolute;
  padding: 16px 24px;
  z-index: 99999;
  color: rgba(0, 0, 0, 0.65);
  background: #fff;
  top: 0;
  left: 0;
  right: 0;
  border-bottom: 1px solid #e8e8e8;
  border-radius: 4px 4px 0 0;
}

div.ant-drawer-body {
  padding: 24px;
  // margin-top: 3rem;
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
}
.common-fullscreen-style.ant-modal {
  .ant-modal-content {
    // width: 100vw;
    text-align: center;
    .ant-modal-body {
      padding: 20px;
    }
  }
  .ant-modal-footer {
    display: none;
  }
}
.common-error-style {
  color: #f5222d;
  font-size: 14px;
}
.common-link {
  color: #1890ff;
  cursor: pointer;
}
.common-line-through {
  text-decoration: line-through;
}
.common-modal-error-style {
  .anticon.anticon-close-circle {
    color: #f5222d;
  }
}
.common-disabled-style-global {
  textarea,
  button,
  input {
    pointer-events: none;
    cursor: not-allowed;
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
    border: 1px solid #d9d9d9;
  }
}
.common-ellipsis-style {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.common-two-line {
  white-space: pre-wrap;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* autoprefixer: ignore next */
  -webkit-box-orient: vertical;
  &.break-word {
    white-space: normal;
    word-break: keep-all;
  }
}
.commom-checkbox-group {
  &.ant-checkbox-group {
    padding: 8px 0 0 0;
    label.ant-checkbox-wrapper {
      margin: 0 32px 16px 0;
    }
  }
}
.common-delete-btn {
  position: relative;
  display: inline-block;
  width: 16px;
  height: 16px;
  margin: 0 12px;
  border-radius: 16px;
  border: none;
  background-color: #ffab00;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  font-size: 0;
  svg {
    width: 10px;
    height: 10px;
    margin: 3px;
    color: #fff;
  }
}
.common-copy-wrap {
  padding: 12px 20px;
  background-color: #fff;
  margin-bottom: 12px;
}
.common-svg-wrap {
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  .common-svg-box {
    .common-svg-text {
      line-height: 20px;
    }
  }
}
.status-publish-style {
  color: $klGreen;
}
.status-unpublish-style {
  color: rgba(210, 210, 210, 1);
}
.status-suspended-style {
  color: #f5222d;
}
.common-main-box-style {
  min-height: calc(100vh - 268px);
  background-color: #fff;
}
.common-svg-delete {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 16px;
  border: none;
  background-color: #ffab00;
  &.svg-disabled {
    pointer-events: none;
    cursor: not-allowed;
    color: rgba(0, 0, 0, 0.25);
    background-color: #f5f5f5;
  }
}

.common-scrollbar {
  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: #bfbfbf;
  }
  &.border-slot::-webkit-scrollbar-track {
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    background-color: #fff;
  }
}

div.ant-modal-content .ant-modal-body {
  padding: 20px;
}

.common-disabled-hover {
  &:before {
    content: attr(data-title);
    position: absolute;
    width: 100px;
    top: 50%;
    transform: translateY(-50%);
    right: -110px;
    background-color: #fff;
    border-radius: 6px;
    border: 1px solid #ededed;
    padding: 4px 8px;
    word-break: break-word;
    color: #b6b6b6;
    display: none;
  }
  &:hover:before {
    display: inline-block;
  }
}
form .has-error {
  .no-error {
    input,
    textarea {
      &.ant-input:not([disabled]) {
        border-color: #d9d9d9;
        box-shadow: none;
        &:hover,
        &:focus {
          border-color: #d9d9d9;
        }
      }
    }
    .ant-select-selection:not([disabled]) {
      border-color: #d9d9d9;
      box-shadow: none;
      &:hover {
        border-color: #d9d9d9;
      }
    }
    .ant-select-arrow:not([disabled]) {
      color: #d9d9d9;
    }
  }
  .no-error-self:not([disabled]) {
    border-color: #d9d9d9;
    box-shadow: none;
    &:hover,
    &:focus {
      border-color: #d9d9d9;
    }
  }
}
.common-input-copy-clipboard {
  opacity: 0;
  position: absolute;
}
.common-table-style.table-antd {
  &.break-all {
    .ant-table .ant-table-thead > tr > th,
    .ant-table .ant-table-tbody > tr > td {
      word-wrap: break-word;
      word-break: break-all;
      white-space: inherit;
    }
  }
}
.common-suffix-count {
  position: relative;
  &.ant-input-affix-wrapper {
    input.ant-input {
      padding-right: 54px;
    }
  }
}
.common-suffix-count-large {
  position: relative;
  &.ant-input-affix-wrapper {
    input.ant-input {
      padding-right: 74px;
    }
  }
}
.common-text-count {
  min-width: 30px;
  text-align: right;
  color: rgba(0, 0, 0, 0.45);
  &.simple-md {
    position: absolute;
    bottom: 0;
    right: 12px;
    z-index: 9;
  }
}
.ant-form-fixed-label {
  .ant-form-item-label > label {
    padding-right: 8px;
    white-space: break-spaces;
    display: inline-block;
    line-height: normal;

    &:after {
      display: none;
    }
  }
}
.ant-alert {
  &.common-alert-style {
    &.ant-alert-warning {
      border: 1px solid #ffe1b6;
      border-radius: 4px;
      background: rgba(255, 171, 0, 0.05);
    }
  }
}
.common-alert-fixed {
  position: fixed;
  z-index: 10;
  top: 16px;
  left: 0;
  width: 100%;
  height: 0;
  text-align: center;
  .ant-alert {
    display: inline-block;
    max-width: 900px;
  }
}

// common icon style
.icon-trash {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin-right: 8px;
  background-color: #ffab00;
  padding: 4px;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  color: #fff;
  &.disabled {
    background-color: #eee;
  }
}

.gg-trash {
  box-sizing: border-box;
  position: relative;
  display: block;
  transform: scale(0.5);
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  box-shadow: 0 0 0 2px, inset 4px 0 0, inset 2px 0 0;
  border-bottom-left-radius: 1px;
  border-bottom-right-radius: 1px;
  margin-top: 3px;
}
.gg-trash::after,
.gg-trash::before {
  content: "";
  display: block;
  box-sizing: border-box;
  position: absolute;
}
.gg-trash::after {
  background: currentColor;
  border-radius: 3px;
  width: 18px;
  height: 2px;
  top: -4px;
  left: -7px;
}
.gg-trash::before {
  width: 10px;
  height: 4px;
  border: 2px solid;
  border-bottom: transparent;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
  top: -7px;
  left: -3px;
}

.gg-undo {
  box-sizing: border-box;
  position: relative;
  display: block;
  width: 14px;
  flex: 0 0 14px; // The compatible parent node style is flex
  height: 14px;
  border: 2px solid;
  border-left-color: transparent;
  border-radius: 100px;
  transform: scale(0.75);
  margin-left: 6px;
}
.gg-undo::before {
  content: "";
  display: block;
  box-sizing: border-box;
  position: absolute;
  width: 6px;
  height: 6px;
  border-top: 2px solid;
  border-left: 2px solid;
  top: -3px;
  left: -1px;
  transform: rotate(-68deg);
}

.gg-eye-off {
  position: relative;
  display: inline-flex;
  .eye-off {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

.gg-eye {
  position: relative;
  transform: scale(0.5);
  width: 24px;
  height: 18px;
  border-bottom-right-radius: 100px;
  border-bottom-left-radius: 100px;
  overflow: hidden;
  box-sizing: border-box;
  text-align: center;
  &.eye-on {
    color: #000;
  }
}

.gg-eye::after,
.gg-eye::before {
  content: "";
  display: block;
  border-radius: 100px;
  position: absolute;
  box-sizing: border-box;
}

.gg-eye::after {
  top: 2px;
  box-shadow: inset 0 -8px 0 2px, inset 0 0 0 2px;
  width: 24px;
  height: 24px;
}

.gg-eye::before {
  width: 8px;
  height: 8px;
  border: 2px solid;
  bottom: 4px;
  left: 8px;
}

.gg-pen {
  box-sizing: border-box;
  position: relative;
  display: block;
  transform: rotate(-45deg) scale(0.5);
  width: 14px;
  height: 4px;
  border-right: 2px solid transparent;
  box-shadow: 0 0 0 2px, inset -2px 0 0;
  border-top-right-radius: 1px;
  border-bottom-right-radius: 1px;
  margin-right: -2px;
}
.gg-pen::after,
.gg-pen::before {
  content: "";
  display: block;
  box-sizing: border-box;
  position: absolute;
}
.gg-pen::before {
  background: currentColor;
  border-left: 0;
  right: -6px;
  width: 3px;
  height: 4px;
  border-radius: 1px;
  top: 0;
}
.gg-pen::after {
  width: 8px;
  height: 7px;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-right: 7px solid;
  left: -11px;
  top: -2px;
}

.ant-modal-confirm {
  &.--title-is-empty {
    .ant-modal-confirm-body {
      .ant-modal-confirm-content {
        margin-top: 0;
      }
    }
  }
  // No style/class interface for ant design btn
  &.ok-btn--is-green {
    .ant-modal-confirm-btns .ant-btn:last-child {
      background-color: #00b371;
      border-color: #00b371;
    }
  }
}

div.ant-modal-content {
  .ant-modal-header {
    border-bottom: 1px solid #e8e8e8;
  }
  .ant-modal-footer {
    padding: 10px 16px;
    border-top: 1px solid #e8e8e8;
  }
}

.ant-layout.hide-header {
  .ant-layout-header {
    display: none !important;
  }
  .ant-layout-content {
    margin-top: 0 !important;
  }
}
// common icon style end
/* 通用样式 end */

/* driver.js reset style start */
#driver-popover-item.common-driver-popover-style {
  padding: 20px;
  min-width: 340px;
  max-width: 340px;
  .driver-popover-title {
    line-height: 24px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    font-weight: 400;
    .driver-popover-footer {
      button {
        border: none;
        background-color: transparent;
      }
    }
  }
  .custom-driver {
    &-box {
      display: flex;
      p {
        padding: 0;
        margin: 0;
        &.custom-driver-footer {
          margin-top: 12px;
        }
      }
    }
    &__left-box {
      flex: none;
      display: flex;
      align-items: flex-start;
      margin-right: 12px;
      .left-box__driver-icon {
        display: inline-block;
        margin-top: 2px;
        width: 20px;
        height: 20px;
      }
    }
    &-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    &-step {
      line-height: 22px;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.45);
    }
    &-btns {
      line-height: 22px;
      font-size: 16px;
      font-weight: 600;
      color: #437dff;
      &__btn {
        padding: 0 2px;
        cursor: pointer;
      }
    }
  }
}
/* driver.js reset style end */

.common-pkg-status-circle {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  &.draft {
    background-color: #faad14;
  }
  &.published {
    background-color: #389e0d;
  }
  &.unpublished {
    background-color: #bfbfbf;
  }
}

.js-dataset-field {
  border: 1px solid transparent;
}

.common-blink-scroll-into-view {
  animation: blink2view 5s cubic-bezier(0.55, 0.06, 0.68, 0.19) 1;
  border-radius: 4px;
}

@keyframes blink2view {
  0%,
  33%,
  66%,
  100% {
    border-color: transparent;
    background-color: transparent;
  }
  17%,
  50%,
  83% {
    border-color: #437dff;
    background-color: #edf5ff;
  }
}

.common-before-interceptor-confirm {
  .ant-modal-content {
    &:has(.ant-modal-close) {
      .ant-modal-confirm-content {
        padding-right: 32px;
      }
    }
  }

  .ant-modal-confirm-body {
    .ant-modal-confirm-content {
      display: flex;
    }

    .anticon {
      margin-top: 12px;
    }
  }
}

.v-tooltip-wrapper {
  &.ant-tooltip {
    .ant-tooltip-arrow::before,
    .ant-tooltip-inner {
      background-color: var(--background-color, rgba(0, 0, 0, 0.75));
    }

    &.common-tooltip-style .ant-tooltip-arrow::before,
    &.common-tooltip-style .ant-tooltip-inner {
      background-color: var(--background-color, #fff);
    }
  }
}

.common-manual-reviewd-box {
  margin-left: 16px;
  padding: 1px 8px;
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
  border-radius: 2px;
  border: 1px solid #d9d9d9;
  background: #fafafa;
}

// windows/mac os 兼容
.is-mac-os .ant-table-fixed-header .ant-table-scroll .ant-table-header {
  // the new version of chrome will have height issues on mac
  overflow: inherit !important;
}

.common-flex-row-center {
  display: inline-flex;
  flex-direction: row;
  align-items: center;
}
