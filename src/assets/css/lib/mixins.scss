@mixin mixin-hover-display-scrollbar {
  overscroll-behavior: contain !important;
  overflow-y: auto !important;

  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 1em;
    background-color: transparent;
  }

  &::-webkit-scrollbar-track {
    border-radius: 1em;
    background-color: transparent;
  }

  &::-webkit-scrollbar-vertical {
    width: 2px;
  }

  &:hover {
    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.4);
    }

    &::-webkit-scrollbar-track {
      background-color: #f0f0f0;
    }
  }
}

@mixin mixin-switch-footer-wrapper-animation {
  &.--non-activated {
    transition: transform 0s linear;
    transform: translateY(100%);
  }

  &.--has-activated {
    transition: transform 0.24s linear 0.8s;
    transform: translateY(0);
  }
}
