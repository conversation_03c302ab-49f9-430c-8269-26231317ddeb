let { env, isMerchant } = require('@/env')
let CONF = require('./conf.js')
const moment = require('moment')
window.global_goto_old_admin = {
  isNewAdminOn: true,
  isNewOpenWin: true,
  env: env,
  proxy: {
    development: 'http://localhost:1122',
    staging: `${window.location.origin}`,
    production: ''
  },
  name2path: {
    //am
    //em
  }
}
const URI = require('urijs')
const utils = require('@activity/utils')
// const lang_conf = require('lang_conf')
const setPathname = (pathname, cur_href = window.location.href) => {
  return new URI(cur_href).pathname(klook.getLangUrl(pathname)).toString()
}
const getUtcList = (fullTimezone) => {
  let utcList = [
    { label: '(GMT -12:00) Eniwetok, Kwajalein', value: -12, key: '-12:00' },
    { label: '(GMT -11:00) Midway Island, Samoa', value: -11, key: '-11:00' },
    { label: '(GMT -10:00) Hawaii', value: -10, key: '-10:00' },
    { label: '(GMT -9:00) Alaska', value: -9, key: '-09:00' },
    { label: '(GMT -8:00) Pacific Time (US &amp; Canada)', value: -8, key: '-08:00' },
    { label: '(GMT -7:00) Mountain Time (US &amp; Canada)', value: -7, key: '-07:00' },
    { label: '(GMT -6:00) Central Time (US &amp; Canada), Mexico City', value: -6, key: '-06:00' },
    { label: '(GMT -5:00) Eastern Time (US &amp; Canada), Bogota, Lima', value: -5, key: '-05:00' },
    { label: '(GMT -4:00) Atlantic Time (Canada), Caracas, La Paz', value: -4, key: '-04:00' },
    { label: '(GMT -3:30) Newfoundland', value: -3.5, key: '-03:30' },
    { label: '(GMT -3:00) Brazil, Buenos Aires, Georgetown', value: -3, key: '-03:00' },
    { label: '(GMT -2:00) Mid-Atlantic', value: -2, key: '-02:00' },
    { label: '(GMT -1:00 hour) Azores, Cape Verde Islands', value: -1, key: '-01:00' },
    { label: '(GMT) Western Europe Time, London, Lisbon, Casablanca', value: 0, key: '+00:00' },
    { label: '(GMT +1:00 hour) Brussels, Copenhagen, Madrid, Paris', value: 1, key: '+01:00' },
    { label: '(GMT +2:00) Kaliningrad, South Africa', value: 2, key: '+02:00' },
    { label: '(GMT +3:00) Baghdad, Riyadh, Moscow, St. Petersburg', value: 3, key: '+03:00' },
    { label: '(GMT +3:30) Tehran', value: 3.5, key: '+03:30' },
    { label: '(GMT +4:00) Abu Dhabi, Muscat, Baku, Tbilisi', value: 4, key: '+04:00' },
    { label: '(GMT +4:30) Kabul', value: 4.5, key: '+04:30' },
    { label: '(GMT +5:00) Ekaterinburg, Islamabad, Karachi, Tashkent', value: 5, key: '+05:00' },
    { label: '(GMT +5:30) Bombay, Calcutta, Madras, New Delhi', value: 5.5, key: '+05:30' },
    { label: '(GMT +5:45) Kathmandu', value: 5.75, key: '+05:45' },
    { label: '(GMT +6:00) Almaty, Dhaka, Colombo', value: 6, key: '+06:00' },
    { label: '(GMT +7:00) Bangkok, Hanoi, Jakarta', value: 7, key: '+07:00' },
    { label: '(GMT +8:00) Beijing, Perth, Singapore, Hong Kong', value: 8, key: '+08:00' },
    { label: '(GMT +9:00) Tokyo, Seoul, Osaka, Sapporo, Yakutsk', value: 9, key: '+09:00' },
    { label: '(GMT +9:30) Adelaide, Darwin', value: 9.5, key: '+09:30' },
    { label: '(GMT +10:00) Eastern Australia, Guam, Vladivostok', value: 10, key: '+10:00' },
    { label: '(GMT +11:00) Magadan, Solomon Islands, New Caledonia', value: 11, key: '+11:00' },
    { label: '(GMT +12:00) Auckland, Wellington, Fiji, Kamchatka', value: 12, key: '+12:00' },
    { label: '(GMT +13:00 Daylight Time) Auckland, Wellington, Fiji, Kamchatka', value: 13, key: '+13:00' },
    { label: '(GMT +14:00 Daylight Time) Pacific/Kiritimati', value: 14, key: '+14:00' }
  ]
  if (!fullTimezone) {
    utcList = utcList.filter((o) => {
      return o.value === parseInt(o.value) // 过滤不是整点时区
    })
  }
  return utcList
}
const Clipboard = (function (window, document, navigator) {
  let textArea, copy
  // 是否ios端
  function isOS() {
    return navigator.userAgent.match(/ipad|iphone/i)
  }
  //创建文本元素
  function createTextArea(text) {
    textArea = document.createElement('textArea')
    textArea.innerHTML = text
    textArea.value = text
    document.body.appendChild(textArea)
  }
  //选择内容
  function selectText() {
    let range, selection
    if (isOS()) {
      range = document.createRange()
      range.selectNodeContents(textArea)
      selection = window.getSelection()
      selection.removeAllRanges()
      selection.addRange(range)
      textArea.setSelectionRange(0, 999999)
    } else {
      textArea.select()
    }
  }
  //复制到剪贴板
  function copyToClipboard() {
    let isb = document.execCommand('Copy')
    document.body.removeChild(textArea)
    return isb
  }

  copy = function (text, callback) {
    createTextArea(text)
    selectText()
    callback && callback(copyToClipboard())
  }

  return {
    copy: copy
  }
})(window, document, navigator)

const klook = (function () {
  function getLangPath() {
    return KLK_LANG === 'en' ? '/' : KLK_LANG + '/'
  }
  function getLangUrl(url) {
    if (url.match(/^\/.*/)) {
      // `/act/activity` => `act/activity`
      url = url.substring(1, url.length)
    }
    return `/${getLangPath()}${url}` // ie /act => /zh-CN/act
  }

  function abort() {
    /*
     * HACK
     * we have a global error monitor function to watch every uncaught Error
     * but some times we want to abort execution from a (a)sync function and its' parents without triggering that watcher
     * so we throw a string here
     * NOTE abort won't terminate other async function tho ???
     */
    // for not caught by window.onerror method, do NOT throw Error type here.
    var call_stack = new Error().stack.split(/\n/)[2].trim()
    throw `Manually aborted ${call_stack}`
  }
  function getAPILang() {
    return lang_conf.getLangObj('F_LANG', 'B_LANG')[KLK_LANG]
    // if (['en', 'ja', 'ko', 'th'].includes(KLK_LANG)) {
    //   return 'en_US'
    // }
    // return KLK_LANG.replace('-', '_')
  }
  /*
   * 配置一个预览链接到 www.klook.com etc...
   */
  function getUrlByEnv(path) {
    const getUrl = () => {
      // cn 站 www-fat2.fat.klooktest.cn
      // global 站 www.fws.klooktest.io
      if (~location.origin.indexOf('klooktest')) {
        return location.origin.replace(new RegExp(`${isMerchant ? 'merchant' : 'admin'}(\\d+)`), 'www$1')
      }

      if (!~location.origin.indexOf('uat')) {
        return isMerchant
          ? location.origin.replace(/merchant(d)?/gi, (match, $1) => 't')
          : location.origin.replace(/admin(d)?/gi, (match, $1) => 't')
      }

      return location.origin.replace(isMerchant ? 'merchant-' : 'admin-', '')
    }
    const nodeEnv = window?.process?.env?.NODE_ENV || process?.env?.NODE_ENV || 'production'
    let origin =
      nodeEnv === 'production'
        ? location.origin.includes('stage')
          ? 'https://www.stage.klook.io'
          : 'https://www.klook.com'
        : nodeEnv === 'test'
        ? // 测试环境的配置有问题
          // admind2 => d2, admin3 => t3
          getUrl()
        : CONF.DEV_PRO_HOST
    return `${origin}${path}`
  }
  function strformat() {
    var format = /\{([\d\w.]+)\}/g
    var args = Array.prototype.slice.call(arguments),
      v
    var str = args.shift() + ''
    if (args.length == 1 && typeof args[0] == 'object') {
      args = args[0]
    }
    format.lastIndex = 0
    return str.replace(format, function (m, n) {
      v = args[n]
      return v === undefined ? m : v
    })
  }
  function urlParam(name, value, url) {
    if (typeof value == 'undefined') {
      return (
        decodeURIComponent(
          (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(url || location.search) || [
            undefined,
            ''
          ])[1].replace(/\+/g, '%20') // eslint-disable-line no-sparse-arrays
        ) || null
      )
    } else {
      url = url || window.location.href
      name = name.toString()
      value = encodeURIComponent(value.toString())
      var r = new RegExp('(^|\\W)' + name + '=[^&]*', 'g')
      var vUrl = url.split('#')
      vUrl[0] = vUrl[0].match(r)
        ? vUrl[0].replace(r, '$1' + name + '=' + value)
        : vUrl[0] + (vUrl[0].indexOf('?') == -1 ? '?' : '&') + name + '=' + value
      return vUrl.join('#')
    }
  }
  // goto old admin
  function gotoOldAdmin(fullPath, options, events) {
    let opt = _.cloneDeep(window.global_goto_old_admin)
    if (typeof options === 'function') {
      opt.cb = options
    } else if (utils.getType(options) === 'Object') {
      _.merge(opt, options)
    }
    let { isNewAdminOn, isNewOpenWin, env, proxy, cb } = opt
    if (isNewAdminOn) {
      let is_am_roles = { admin: 'true', em: 'false' }
      let searchParams = new URLSearchParams(fullPath.split('?')[1])
      let page_from = is_am_roles[searchParams.get('page_from')]
      // searchParams.delete("page_from");
      page_from && searchParams.append('is_am', page_from)
      if (events) {
        for (let k in events) {
          searchParams.append(k, events[k])
        }
      }
      let newPath = fullPath.split('?')[0] + '?' + searchParams.toString()
      let href = `${proxy[env] || proxy['production']}${newPath}`
      if (env === 'development' && window.location.origin.indexOf('localhost:') === -1) {
        href = `${proxy['staging']}${newPath}` //test env
      }
      isNewOpenWin ? klook.newOpenWin(href) : (window.location.href = href)
    } else {
      cb && cb()
    }
  }
  function getModifyId() {
    return new Date().valueOf() + '' + parseInt(Math.random() * 10000)
  }
  const getUtcTime = (timezone, datetime) => {
    // timezone  datetime="YYYY-MM-DD hh:mm:ss"
    if (!timezone || !datetime) return ''
    let str = moment(datetime).utcOffset(timezone, true).format('')
    return str
  }
  const fmtAmountPrecision = (amount, force = false) => {
    amount = (amount !== undefined && amount.toString()) || ''
    if (force || (amount && !isNaN(amount))) {
      let reg = /^(([1-9]\d*)|0)(\.\d{0,2})?/
      let result = amount.match(reg)

      if (result) {
        return result[0]
      }
    }
    return force ? '' : amount
  }
  function getModalData(type = 'cancel2confirm') {
    let obj = {
      cancelText: __('global_cancel'),
      okText: __('global_confirm'),
      destroyOnClose: true
    }
    switch (type) {
      case 'cancel2confirm':
        break
    }
    return obj
  }
  return {
    getUtcList,
    fmtAmountPrecision,
    getUtcTime,
    Clipboard,
    getModalData,
    getModifyId,
    gotoOldAdmin,
    getLangUrl,
    abort,
    getAPILang,
    getUrlByEnv,
    strformat,
    urlParam,
    ...utils,
    setPathname
  }
})()

module.exports = klook
