const platform = process.env.VUE_APP_APP_PLATFORM || 'ADMIN'

const isMerchant = process.env.VUE_APP_APP_PLATFORM === 'MERCHANT'
const isAdmin = !isMerchant
const env = process.env.NODE_ENV
const isDev = process.env.NODE_ENV === 'development'
const isTest = process.env.NODE_ENV === 'staging'
const isProd = process.env.NODE_ENV === 'production'

const xReqClient =
  process.env.VUE_APP_APP_PLATFORM === 'MERCHANT' ? 'experiencesmerchant' : 'experiencesadmincommon'

export { env, platform, isMerchant, isAdmin, isDev, isTest, isProd, xReqClient }
