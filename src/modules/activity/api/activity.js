import { parseApiUrl } from '@activity/utils'

const ACTIVITY_API = {
  // 向导创建模式 start
  get_package_info: '/v1/productadminbffsrv/{{platform}}/package_service/get_package_info', // 获取套餐 完整步骤数据
  getPackageModel: '/v1/productadminbffsrv/{{platform}}/calendar_service/get_package_model', // get price inventory
  postUpdatePackageModel: '/v1/productadminbffsrv/{{platform}}/calendar_service/update_package_model', // save price inventory
  get_basic_info_by_section2pkg:
    '/v1/productadminbffsrv/{{platform}}/package_service/get_basic_info_by_section', // 活动id查询接口
  get_basic_info_by_section2act:
    '/v1/productadminbffsrv/{{platform}}/activity_service/get_basic_info_by_section', // 活动id查询接口
  get_basic_info_by_category:
    '/v1/productadminbffsrv/{{platform}}/activity_service/get_basic_info_by_category', // 选择完类目后，调接口获取所有活动的基本属性
  postGuideCreate:
    '/v1/productadminbffsrv/{{platform}}/activity_service/create_activity_with_package_and_skus', // 创建活动、套餐和sku
  // 向导创建模式 end
  get_sku_rule_setting: '/v1/productadminbffsrv/{{platform}}/calendar_service/get_sku_rule_setting', // rule setting 查询接口
  get_price_influence: '/v1/productadminbffsrv/{{platform}}/activity_service/get_price_influence',
  // 根据坐标计算目的地城市并反推国家和page
  calculate_area_info_by_location: '/v1/productadminsrv/destination/service/calculate_area_info_by_location',
  // 上传接口：批量修改日历价格
  get_sku14_day_custom_price_batch_by_start_date:
    '/v1/pricingadminsrv/price/calendar_service/get_sku14_day_custom_price_batch_by_start_date',
  // 更新接口：批量修改日历价格
  update_sku_custom_price_batch: '/v1/pricingadminsrv/price/calendar_service/update_sku_custom_price_batch',
  auto_clone_chinese: '/prosrv/activies/multilang/clone_chinese_message',
  show_to_merchant_self_onboarding:
    '/v1/productadminbffsrv/{{platform}}/activity_audit_service/show_to_merchant_self_onboarding',
  // 切换编辑语言
  switch_eidt_language: '/v1/productadminbffsrv/{{platform}}/activity_audit_service/switch_eidt_language',
  // api文档：https://docs.google.com/spreadsheets/d/1FTrz7S1hXeb4zKIx6PXx0K5Nw3hYRbrt7PLqYflD3nE/edit#gid=1401326045
  // 编辑保护
  edit_protect: '/v1/productadminbffsrv/{{platform}}/activity_service/edit_protect',
  // 获取套餐步骤相关
  get_package_position: '/v1/productadminbffsrv/{{platform}}/package_service/get_package_position',
  /*** admin start ***/
  // 修改商户ID编辑价格弹窗 start
  // 更新价格
  reset_price_calendar: '/v1/productadminbffsrv/{{platform}}/pricing_service/reset_price_calendar',
  get_package_pricing_model: '/v1/productadminbffsrv/{{platform}}/pricing_service/get_package_pricing_model',
  get_packages_and_price: '/v1/productadminbffsrv/admin/activity_service/get_packages_and_price',
  get_activity_tier: '/v1/productadminbffsrv/{{platform}}/pricing_service/get_activity_tier',
  update_merchant_id: '/v1/productadminbffsrv/admin/package_service/update_merchant_id',
  // 获取价格--非loss leader
  get_package_valid_and_pending_valid_cost_and_sales:
    '/v1/productadminbffsrv/{{platform}}/pricing_service/get_package_valid_and_pending_valid_cost_and_sales',
  // 更新价格--非loss leader
  update_package_valid_and_pending_valid_cost_and_sales:
    '/v1/productadminbffsrv/{{platform}}/pricing_service/update_package_valid_and_pending_valid_cost_and_sales',
  // 获取价格--loss leader
  get_package_valid_and_pending_valid_cost:
    '/v1/productadminbffsrv/{{platform}}/pricing_service/get_package_valid_and_pending_valid_cost',
  // 更新价格--loss leader
  update_package_valid_and_pending_valid_cost:
    '/v1/productadminbffsrv/{{platform}}/pricing_service/update_package_valid_and_pending_valid_cost',
  // 修改商户ID编辑价格弹窗 end
  //更新活动状态
  update_status2act: '/v1/productadminbffsrv/{{platform}}/activity_service/update_status',
  //更新套餐状态
  update_status2pkg: '/v1/productadminbffsrv/{{platform}}/package_service/update_status',
  //notes start
  get_operate_note: '/v1/productadminbffsrv/{{platform}}/activity_service/get_operate_note',
  update_operate_note: '/v1/productadminbffsrv/{{platform}}/activity_service/update_operate_note',
  //notes end
  //seo start
  get_seo_fields: '/v1/productadminbffsrv/{{platform}}/activity_service/get_seo_fields',
  update_seo_fields: '/v1/productadminbffsrv/{{platform}}/activity_service/update_seo_fields',
  //seo end
  // 活动顶部信息
  get_floating_fields2act: '/v1/productadminbffsrv/{{platform}}/activity_service/get_floating_fields',
  // 套餐顶部信息
  get_floating_fields2pkg: '/v1/productadminbffsrv/{{platform}}/package_service/get_floating_fields',
  // 查询套餐财务模型
  get_financial: '/v1/productadminbffsrv/{{platform}}/package_service/get_financial',
  // 更新财务模型
  update_financial: '/v1/productadminbffsrv/{{platform}}/package_service/update_financial',
  // 发送PIC邮件
  send_financial_pic_email: '/v1/productadminbffsrv/{{platform}}/activity_service/send_financial_pic_email',
  // 获取商户列表
  get_merchant_list: '/v1/productadminbffsrv/{{platform}}/package_service/get_merchant_list',
  // 查询特殊库存
  get_package_special_inventory_info:
    '/v1/productadminbffsrv/{{platform}}/schedule_service/get_package_special_inventory_info',
  /*** admin end ***/

  /*** Choose Sub-category start ***/
  //查询类目销量前 N 的活动（en_US）
  get_sell_top_activity: '/v1/productadminbffsrv/{{platform}}/activity_service/get_sell_top_activity',
  /*** Choose Sub-category end ***/

  /*** 通用接口 start ***/
  //获取用户的所有权限
  get_user_access_permission_code:
    '/v1/productadminbffsrv/{{platform}}/auth_service/get_user_access_permission_code',
  //前端通知后端所有修改已经完成，可以开始进行数据快照
  modify_completed: '/v1/productadminbffsrv/{{platform}}/datasnapshot_service/modify_completed',
  //获取所有通用语言列表
  get_country_node_name_info_by_idlist:
    '/v1/productadminbffsrv/{{platform}}/area_service/get_country_node_name_info_by_idlist',
  get_all_cities_under_id: '/v1/productadminbffsrv/{{platform}}/area_service/get_all_cities_under_id',
  get_all_language: '/v1/productadminbffsrv/{{platform}}/bas_language_service/get_all_language',
  // 获取活动关联的类目ID
  get_all_countries: '/v1/productadminbffsrv/{{platform}}/area_service/get_all_countries',
  get_activity_category_id: '/v1/productadminbffsrv/{{platform}}/activity_service/get_activity_category_id',

  // 批量查询子分组列表
  query_group_item_by_parent_ids:
    '/v1/productadminbffsrv/{{platform}}/obtemplate_service/query_group_item_by_parent_ids',
  // 批量查询子分组列表
  query_title_list_by_group_ids:
    '/v1/productadminbffsrv/{{platform}}/otherinfo_service/query_title_list_by_group_ids',
  // 保存活动 Step 状态
  save_activity_step_status: '/v1/productadminbffsrv/{{platform}}/activity_service/save_activity_step_status',
  // 查询活动套餐列表 get
  get_activity_packages_info:
    '/v1/productadminbffsrv/{{platform}}/package_service/get_activity_packages_info_v2',
  // 返回活动各个页面的进度
  get_activity_position: '/v1/productadminbffsrv/{{platform}}/activity_service/get_activity_position',
  send_add_city_application_email:
    '/v1/productadminbffsrv/{{platform}}/area_service/send_add_city_application_email',
  /***  通用接口 end ***/

  /***  activity start ***/
  // 归档套餐
  archive_package: '/v1/productadminbffsrv/merchant/package_service/archive_package',
  // 取消归档套餐
  un_archive_package: '/v1/productadminbffsrv/merchant/package_service/un_archive_package',
  // 获取活动 Basic Info+基本属性配置
  get_activity_basic_info: '/v1/productadminbffsrv/{{platform}}/activity_service/get_activity_basic_info',
  // 套餐 保存基本属性 post
  save_package_basic_info: '/v1/productadminbffsrv/{{platform}}/package_service/save_basic_info',
  // 更新活动 Basic Info
  update_activity_basic_info: '/v1/productadminbffsrv/{{platform}}/activity_service/update_basic_info',
  // 查询 Step 状态
  get_activity_step_status: '/v1/productadminbffsrv/{{platform}}/activity_service/get_activity_step_status',
  //活动提交审核 post
  submit_to_klook: '/v1/productadminbffsrv/{{platform}}/activity_audit_service/submit_to_klook',
  //查询活动下已填写完信息但是未提交审核的套餐列表
  get_activity_to_submit_packages:
    '/v1/productadminbffsrv/{{platform}}/package_audit_service/get_activity_to_submit_packages',
  get_activity_submit_status:
    '/v1/productadminbffsrv/{{platform}}/activity_audit_service/get_activity_submit_status',
  //活动撤销审核提交 post
  withdraw2activity: '/v1/productadminbffsrv/{{platform}}/activity_audit_service/withdraw',
  //套餐撤销审核提交 post
  withdraw2package: '/v1/productadminbffsrv/{{platform}}/package_audit_service/withdraw',
  //查询套餐是否可提交审核get
  get_package_submit_status:
    '/v1/productadminbffsrv/{{platform}}/package_audit_service/get_package_submit_status',
  //套餐提交审核post
  submit_to_klook2package: '/v1/productadminbffsrv/{{platform}}/package_audit_service/submit_to_klook',
  // aid
  submit_aid_to_klook: '/v1/productadminbffsrv/{{platform}}/product_service/submit_to_klook',
  withdraw_aid: '/v1/productadminbffsrv/{{platform}}/product_service/withdraw',
  //查询活动是否可提交 get
  // 更新活动的 图片
  update_activity_photo: '/v1/productadminbffsrv/{{platform}}/activity_service/update_activity_photo',

  get_change_log2act: '/v1/productadminbffsrv/{{platform}}/activity_audit_service/get_change_log',
  get_change_log2pkg: '/v1/productadminbffsrv/{{platform}}/package_audit_service/get_change_log',
  //获取活动各个语言的结构化进度
  get_activity_status_by_langs:
    '/v1/productadminbffsrv/{{platform}}/activity_service/get_activity_status_by_langs',
  //查询所有变量(包括属性值）
  get_variable_list: '/v1/productadminbffsrv/{{platform}}/obtemplate_service/get_variable_list',
  //读取活动的 OBT Group 列表+所有 group 的 schema 和实例数据
  get_activity_group_list: '/v1/productadminbffsrv/{{platform}}/activity_service/get_group_list',
  get_package_group_list: '/v1/productadminbffsrv/{{platform}}/package_service/get_group_list',
  //获取部件对应实例数据free text多语言文案	Kolly
  get_activity_widget_free_text_i18n:
    '/v1/productadminbffsrv/{{platform}}/activity_service/get_activity_widget_free_text_i18n',
  // 保存分组实例数据	Kolly
  save_activity_instance_data:
    '/v1/productadminbffsrv/{{platform}}/activity_service/save_activity_instance_data',
  save_package_instance_data:
    '/v1/productadminbffsrv/{{platform}}/package_service/save_package_instance_data',
  get_attr_values_by_attr_item_idlist:
    '/v1/productadminbffsrv/{{platform}}/attribute_service/get_attr_values_by_attr_item_idlist',
  /***  activity end ***/

  //查询活动的所有发布语言状态
  get_activity_publish_status:
    '/v1/productadminbffsrv/{{platform}}/activity_service/get_activity_publish_status',
  // 列表查询接口：筛选审核活动
  filter_activity: '/v1/productadminbffsrv/{{platform}}/activity_audit_service/filter_activity',
  filter_package: '/v1/productadminbffsrv/{{platform}}/package_audit_service/filter_package',
  // 列表筛选Option数据
  get_filter_option: '/v1/productadminbffsrv/{{platform}}/activity_audit_service/get_filter_option',
  // 获取已发布 OBT 的二级类目列表
  get_published_category_info:
    '/v1/productadminbffsrv/{{platform}}/category_service/get_published_category_info',
  // 获取已发布 OBT 的二级类目列表
  get_ttd_published_category_info:
    '/v1/productadminbffsrv/{{platform}}/category_service/get_ttd_published_category_info',
  // 新建活动
  create_activity: '/v1/productadminbffsrv/{{platform}}/activity_service/create_activity',
  // 创建套餐 post
  create_package: '/v1/productadminbffsrv/{{platform}}/package_service/create_one',
  // 复制套餐 post
  copy_package: '/v1/productadminbffsrv/{{platform}}/package_service/copy_package',
  // 查询单个套餐的基本属性
  get_packages_basic_info: '/v1/productadminbffsrv/{{platform}}/package_service/get_basic_info',
  //套餐 查询 Step 状态 get
  get_package_step_status: '/v1/productadminbffsrv/{{platform}}/package_service/get_package_step_status_v2',
  //套餐 保存 Step 状态 get
  save_package_step_status: '/v1/productadminbffsrv/{{platform}}/package_service/save_package_step_status',

  save_aid_leaf_category_id: '/v1/productadminbffsrv/{{platform}}/product_service/create',
  save_act_leaf_category_id: '/v1/productadminbffsrv/{{platform}}/activity_service/save_act_leaf_category_id',
  delete_activity: '/v1/productadminbffsrv/{{platform}}/activity_service/delete_activity',
  delete_package: '/v1/productadminbffsrv/{{platform}}/package_service/delete_package',
  // 获取旧样式 通用Otherinfo
  get_old_common_otherinfos:
    '/v1/productadminbffsrv/{{platform}}/otherinfo_service/get_old_common_otherinfos',
  // 获取套餐的 旧样式 Otherinfo数据
  get_package_otherinfos_old_style:
    '/v1/productadminbffsrv/{{platform}}/otherinfo_service/get_package_otherinfos_old_style',
  // 获取活动下所有套餐的旧样式Otherinfo列表
  get_all_old_otherinfo_by_activity:
    '/v1/productadminbffsrv/{{platform}}/otherinfo_service/get_all_old_otherinfo_by_activity',
  // 获取活动下所有套餐的新Otherinfo列表
  get_package_otherinfos_by_activity:
    '/v1/productadminbffsrv/{{platform}}/otherinfo_service/get_package_otherinfos_by_activity_for_copy',
  copy_otherinfos_by_package: '/v1/productadminbffsrv/{{platform}}/otherinfo_service/copy_package_otherinfos',
  // 获取套餐的 Otherinfo数据
  get_package_otherinfos: '/v1/productadminbffsrv/{{platform}}/otherinfo_service/get_package_otherinfos',
  // 保存套餐绑定的Otherinfo数据
  save_package_otherinfos: '/v1/productadminbffsrv/{{platform}}/otherinfo_service/save_package_otherinfos',
  // 保存套餐绑定的旧样式Otherinfo数据(复制功能)
  bind_package_old_otherinfos:
    '/v1/productadminbffsrv/{{platform}}/otherinfo_service/bind_package_old_otherinfos',
  get_package_contact_config:
    '/v1/productadminbffsrv/{{platform}}/package_service/get_package_contact_config',
  // contact info 重构
  get_package_contact_config_v2:
    '/v1/productadminbffsrv/{{platform}}/package_service/get_package_contact_config_v2',
  update_package_contact_config:
    '/v1/productadminbffsrv/{{platform}}/package_service/update_package_contact_config',
  // contact info 重构
  update_package_contact_config_v2:
    '/v1/productadminbffsrv/{{platform}}/package_service/update_package_contact_config_v2',

  ////// ADMIN start
  // 活动 审核通过 post
  actApprove: '/v1/productadminbffsrv/{{platform}}/activity_audit_service/approve',
  // 活动 审核拒绝 post
  actReject: '/v1/productadminbffsrv/{{platform}}/activity_audit_service/reject',
  // 套餐 审核通过 post
  pkgApprove: '/v1/productadminbffsrv/{{platform}}/package_audit_service/approve',
  // 套餐 审核拒绝 post
  pkgReject: '/v1/productadminbffsrv/{{platform}}/package_audit_service/reject',
  ////// ADMIN end

  // 价格日历
  create_sku_and_price: '/v1/productadminbffsrv/{{platform}}/sku_service/create_sku_and_price',
  // update_sku: '/v1/productadminbffsrv/{{platform}}/sku_service/update_sku',
  // query_package_sku_list:
  //   '/v1/productadminbffsrv/{{platform}}/sku_service/query_package_sku_list',
  // published_sku:
  //   '/v1/productadminbffsrv/{{platform}}/sku_service/published_sku',
  // destroy_sku: '/v1/productadminbffsrv/{{platform}}/sku_service/destroy_sku',
  // update_sku_priority:
  //   '/v1/productadminbffsrv/{{platform}}/sku_service/update_sku_priority',
  switch_pricing_model_to_calendar:
    '/v1/productadminbffsrv/{{platform}}/pricing_service/switch_pricing_model_to_calendar',
  switch_pricing_model_to_fixed:
    '/v1/productadminbffsrv/{{platform}}/pricing_service/switch_pricing_model_to_fixed',
  create_schedule: '/v1/productadminbffsrv/{{platform}}/schedule_service/create_schedule',
  query_schedule_rule: '/v1/productadminbffsrv/{{platform}}/schedule_service/query_schedule_rule',
  delete_schedule_rule: '/v1/productadminbffsrv/{{platform}}/schedule_service/delete_schedule_rule',
  copy_package_schedule: '/v1/productadminbffsrv/{{platform}}/schedule_service/copy_package_schedule',
  query_sku_price_inventory_calendar:
    '/v1/productadminbffsrv/{{platform}}/calendar_service/query_sku_price_inventory_calendar',
  update_schedule: '/v1/productadminbffsrv/{{platform}}/schedule_service/update_schedule',
  destroy_schedule: '/v1/productadminbffsrv/{{platform}}/schedule_service/destroy_schedule',
  // 暂时没用到
  // update_schedule_by_filter:
  //   '/v1/productadminbffsrv/{{platform}}/schedule_service/update_schedule_by_filter',
  // destroy_schedule_by_filter:
  //   '/v1/productadminbffsrv/{{platform}}/schedule_service/destroy_schedule_by_filter',
  query_fixed_price: '/v1/productadminbffsrv/{{platform}}/pricing_service/query_fixed_price',
  update_fixed_price: '/v1/productadminbffsrv/{{platform}}/pricing_service/update_fixed_price',
  get_calendar_basic_setting:
    '/v1/productadminbffsrv/{{platform}}/pricing_service/get_calendar_basic_setting',
  update_calendar_basic_setting:
    '/v1/productadminbffsrv/{{platform}}/pricing_service/update_calendar_basic_setting',
  update_calendar_price: '/v1/productadminbffsrv/{{platform}}/pricing_service/update_calendar_price',
  // 暂时没用到
  // update_calendar_price_by_filter:
  //   '/v1/productadminbffsrv/{{platform}}/pricing_service/update_calendar_price_by_filter',
  query_activity_take_rate: '/v1/productadminbffsrv/{{platform}}/pricing_service/query_activity_take_rate',
  get_exchange_rate: '/v1/productadminbffsrv/{{platform}}/pricing_service/get_exchange_rate',
  audit_update_calendar_basic_setting:
    '/v1/productadminbffsrv/{{platform}}/pricing_service/audit_update_calendar_basic_setting',
  audit_update_fixed_price: '/v1/productadminbffsrv/{{platform}}/pricing_service/audit_update_fixed_price',
  audit_update_calendar_price:
    '/v1/productadminbffsrv/{{platform}}/pricing_service/audit_update_calendar_price',
  get_take_rate_for_update_calendar_price:
    '/v1/productadminbffsrv/{{platform}}/pricing_service/get_take_rate_for_update_calendar_price',
  get_sku_take_rate_by_time: '/v1/productadminbffsrv/{{platform}}/pricing_service/get_sku_take_rate_by_time',

  // 库存重构
  create_sku_model: '/v1/productadminbffsrv/{{platform}}/sku_service/create_sku_model',
  get_sku_model: '/v1/productadminbffsrv/{{platform}}/sku_service/get_sku_model',
  update_sku_model: '/v1/productadminbffsrv/{{platform}}/sku_service/update_sku_model',
  query_package_sku_list: '/v1/productadminbffsrv/{{platform}}/sku_service/query_package_sku_list',
  published_sku: '/v1/productadminbffsrv/{{platform}}/sku_service/published_sku',
  update_sku_priority: '/v1/productadminbffsrv/{{platform}}/sku_service/update_sku_priority',
  get_customize_unit_type: '/v1/productadminbffsrv/{{platform}}/sku_service/get_customize_unit_type',
  create_customize_unit_type: '/v1/productadminbffsrv/{{platform}}/sku_service/create_customize_unit_type',
  create_sku: '/v1/productadminbffsrv/{{platform}}/sku_service/create_sku',
  destroy_sku: '/v1/productadminbffsrv/{{platform}}/sku_service/destroy_sku',
  update_sku: '/v1/productadminbffsrv/{{platform}}/sku_service/update_sku',
  copy_sku: '/v1/productadminbffsrv/{{platform}}/sku_service/copy_sku',
  get_inv_share_rel_ship: '/v1/productadminbffsrv/{{platform}}/sku_service/get_inv_share_rel_ship',
  get_sku_simple_by_activity_id:
    '/v1/productadminbffsrv/{{platform}}/sku_service/get_sku_simple_by_activity_id',
  set_inv_share_rel_ship: '/v1/productadminbffsrv/{{platform}}/sku_service/set_inv_share_rel_ship',
  save_sku_step_status: '/v1/productadminbffsrv/{{platform}}/sku_service/save_sku_step_status',
  remove_share_rel_ship_by_main_sku:
    '/v1/productadminbffsrv/{{platform}}/sku_service/remove_share_rel_ship_by_main_sku',
  get_calendar_setting: '/v1/productadminbffsrv/{{platform}}/calendar_service/get_calendar_setting',
  update_calendar_setting: '/v1/productadminbffsrv/{{platform}}/calendar_service/update_calendar_setting',
  get_calendar_by_sku_id: '/v1/productadminbffsrv/{{platform}}/calendar_service/get_calendar_by_sku_id',
  // get_sku_take_rate_by_time:
  //   '/v1/productadminbffsrv/{{platform}}/calendar_service/get_sku_take_rate_by_time',
  creates_or_update_single_schedule:
    '/v1/productadminbffsrv/{{platform}}/calendar_service/creates_or_update_single_schedule',
  destroy_single_schedule: '/v1/productadminbffsrv/{{platform}}/calendar_service/destroy_single_schedule',
  get_sku_model_audit: '/v1/productadminbffsrv/{{platform}}/sku_audit_service/get_sku_model',
  get_calendar_by_sku_id_audit:
    '/v1/productadminbffsrv/{{platform}}/calendar_audit_service/get_calendar_by_sku_id',
  creates_or_update_single_schedule_audit:
    '/v1/productadminbffsrv/{{platform}}/calendar_audit_service/creates_or_update_single_schedule',
  update_calendar_setting_audit:
    '/v1/productadminbffsrv/{{platform}}/calendar_audit_service/update_calendar_setting',
  get_sku_list_for_batch_edit:
    '/v1/productadminbffsrv/{{platform}}/calendar_service/get_sku_list_for_batch_edit',
  audit_get_sku_list_for_batch_edit:
    '/v1/productadminbffsrv/admin/calendar_audit_service/get_sku_list_for_batch_edit',
  check_activity_operation: '/v1/productadminbffsrv/{{platform}}/activity_service/check_activity_operation',
  check_package_operation: '/v1/productadminbffsrv/{{platform}}/package_service/check_package_operation',
  check_sku_operation: '/v1/productadminbffsrv/{{platform}}/sku_service/check_sku_operation',

  // bulk
  get_calendar_by_filter: '/v1/productadminbffsrv/{{platform}}/calendar_service/get_calendar_by_filter',
  get_activity_timeslot_list:
    '/v1/productadminbffsrv/{{platform}}/calendar_service/get_activity_timeslot_list',
  get_act_basic_info_schema: '/v1/productadminbffsrv/{{platform}}/activity_service/get_basic_info',
  get_all_poitype: '/v1/productadminbffsrv/{{platform}}/area_service/get_all_poitype',
  getSpuDestinations: '/v1/productadminbffsrv/{{platform}}/area_service/get_wifi_destinations',
  create_or_update_multi_schedule:
    '/v1/productadminbffsrv/{{platform}}/calendar_service/create_or_update_multi_schedule',
  calendar_audit_get_calendar_by_filter:
    '/v1/productadminbffsrv/{{platform}}/calendar_audit_service/get_calendar_by_filter',

  // auto extend
  get_auto_extend_rule: '/v1/productadminbffsrv/{{platform}}/sku_service/get_auto_extend_rule',
  create_or_update_auto_extend_rule:
    '/v1/productadminbffsrv/{{platform}}/sku_service/create_or_update_auto_extend_rule',
  destroy_auto_extend_rule: '/v1/productadminbffsrv/{{platform}}/sku_service/destroy_auto_extend_rule',
  audit_get_auto_extend_rule: '/v1/productadminbffsrv/admin/sku_audit_service/get_auto_extend_rule',
  audit_create_or_update_auto_extend_rule:
    '/v1/productadminbffsrv/admin/sku_audit_service/create_or_update_auto_extend_rule',

  // tags
  // get_tags: '/v1/productadminbffsrv/admin/activity_service/get_tags',
  // update_tags: '/v1/productadminbffsrv/admin/activity_service/update_tags',
  //
  create_customized_other_info:
    '/v1/productadminbffsrv/{{platform}}/otherinfo_service/create_customized_other_info',
  update_customized_other_info:
    '/v1/productadminbffsrv/{{platform}}/otherinfo_service/update_customized_other_info',

  copy_package_instance_data:
    '/v1/productadminbffsrv/{{platform}}/package_service/copy_package_instance_data',
  // get_act_basic_info_schema:
  //   '/v1/productadminbffsrv/{{platform}}/activity_service/get_basic_info',
  // get_all_poitype:
  //   '/v1/productadminbffsrv/{{platform}}/area_service/get_all_poitype',
  // getSpuDestinations:
  //   '/v1/productadminbffsrv/{{platform}}/area_service/get_wifi_destinations',

  // tags
  get_tags: '/v1/productadminbffsrv/admin/activity_service/get_tags',
  update_tags: '/v1/productadminbffsrv/admin/activity_service/update_tags',
  //
  // create_customized_other_info:
  //   '/v1/productadminbffsrv/{{platform}}/otherinfo_service/create_customized_other_info',
  // update_customized_other_info:
  //   '/v1/productadminbffsrv/{{platform}}/otherinfo_service/update_customized_other_info'
  get_all_common_other_info_ids:
    '/v1/productadminbffsrv/{{platform}}/otherinfo_service/get_all_common_other_info_ids',
  res_protect_v2: '/v1/productadminbffsrv/{{platform}}/otherinfo_service/res_protect_v2',

  filter_poiwith_activity_total:
    '/v1/productadminbffsrv/{{platform}}/activity_service/get_activity_filter_poiwith_activity_total',

  get_calendar_over_view_by_month:
    '/v1/productadminbffsrv/{{platform}}/calendar_service/get_calendar_over_view_by_month',
  get_calendar_over_view_by_day:
    '/v1/productadminbffsrv/{{platform}}/calendar_service/get_calendar_over_view_by_day',
  get_sku_models_by_activity_id:
    '/v1/productadminbffsrv/{{platform}}/sku_service/get_sku_models_by_activity_id',
  update_sku_model_to_by_time: '/v1/productadminbffsrv/{{platform}}/sku_service/update_sku_model_to_by_time',
  get_has_completed_schedule:
    '/v1/productadminbffsrv/{{platform}}/calendar_service/get_has_completed_schedule',
  get_basic_group_list: '/v1/productadminbffsrv/{{platform}}/package_service/get_basic_group_list',

  // 0元配置相关接口
  get_sub_category_no_price_text_by_id:
    '/v1/productadminbffsrv/{{platform}}/category_service/get_sub_category_no_price_text_by_id',
  save_sub_category_no_price_text:
    '/v1/productadminbffsrv/{{platform}}/category_service/save_sub_category_no_price_text',
  get_category_config_common:
    '/v1/productadminbffsrv/{{platform}}/category_service/get_category_config_common',
  store_category_config_common:
    '/v1/productadminbffsrv/{{platform}}/category_service/store_category_config_common',
  // express checkout
  query_activity_config_list: '/v1/expadminsrv/fastpass/express_checkout_service/query_activity_config_list',
  allow_create_express_checkout:
    '/v1/expadminsrv/fastpass/express_checkout_service/allow_create_express_checkout',
  save_activity_config: '/v1/expadminsrv/fastpass/express_checkout_service/save_activity_config',

  // reservation products
  multi_pass_reservation_activities_skus:
    '/v1/productadminbffsrv/{{platform}}/sku_service/multi_pass_reservation_activities_skus',
  multi_pass_standard_activity_skus:
    '/v1/productadminbffsrv/{{platform}}/sku_service/multi_pass_standard_activity_skus',
  multi_pass_sku_reservation_setting:
    '/v1/productadminbffsrv/{{platform}}/sku_service/multi_pass_sku_reservation_setting',

  // msp
  get_msp: '/v1/productadminbffsrv/{{platform}}/pricing_service/get_msp',
  update_msp: '/v1/productadminbffsrv/{{platform}}/pricing_service/update_msp',

  // merchant 2.2
  filter_ticket: '/v1/productadminbffsrv/{{platform}}/ticket_service/filter_ticket',
  filter_ticket_option: '/v1/productadminbffsrv/{{platform}}/ticket_service/filter_ticket_option',
  filter_ticket_option_for_admin:
    '/v1/productadminbffsrv/admin/ticket_service/filter_ticket_option_for_admin',
  get_ticket_log: '/v1/productadminbffsrv/{{platform}}/ticket_service/get_ticket_log',
  get_ticket_by_activity: '/v1/productadminbffsrv/{{platform}}/ticket_service/get_ticket_by_activity',
  submit_cost_multi: '/v1/productadminbffsrv/{{platform}}/ticket_service/submit_cost_multi',
  update_ticket_status: '/v1/productadminbffsrv/{{platform}}/ticket_service/update_ticket_status',
  get_cost_ticket_detail: '/v1/productadminbffsrv/{{platform}}/ticket_service/get_cost_ticket_detail',
  approve_cost_ticket: '/v1/productadminbffsrv/{{platform}}/sku_service/approve_cost_ticket',
  get_ticket_related_info: '/v1/productadminbffsrv/{{platform}}/ticket_service/get_ticket_related_info',
  ticket_approve_cost_ticket: '/v1/productadminbffsrv/admin/ticket_service/approve_cost_ticket',

  // fresher
  inv_price_page_setting: '/v1/productadminbffsrv/{{platform}}/sku_service/inv_price_page_setting',
  sync_skus_inv_price: '/v1/productadminbffsrv/{{platform}}/sku_service/sync_skus_inv_price',
  mget_inv_price_page_setting: '/v1/productadminbffsrv/{{platform}}/sku_service/mget_inv_price_page_setting',

  // 套餐限制发布
  getAllCountries: '/v1/productadminsrv/destination/service/get_all_country',
  get_activity_packages_display_rules:
    '/v1/productadminbffsrv/{{platform}}/package_service/get_activity_packages_display_rules',
  create_or_update_display_rules:
    '/v1/productadminbffsrv/{{platform}}/package_service/create_or_update_display_rules',
  // combo 2.0
  filter_standalone_packages: '/v1/productadminbffsrv/{{platform}}/combo_service/filter_standalone_packages',
  filter_standalone_skus: '/v1/productadminbffsrv/{{platform}}/combo_service/filter_standalone_skus',
  get_combo_default_basic_info:
    '/v1/productadminbffsrv/{{platform}}/package_service/get_combo_default_basic_info',

  get_combo_package_otherinfos:
    '/v1/productadminbffsrv/{{platform}}/otherinfo_combo_service/get_combo_package_otherinfos',
  query_combo_sku_merge_info: '/v1/productadminbffsrv/{{platform}}/sku_service/query_combo_sku_merge_info',
  get_package_otherinfos_for_manual_merge:
    '/v1/productadminbffsrv/{{platform}}/otherinfo_combo_service/get_package_otherinfos_for_manual_merge',
  save_combo_package_otherinfos_by_manual_merge:
    '/v1/productadminbffsrv/{{platform}}/otherinfo_combo_service/save_combo_package_otherinfos_by_manual_merge',
  delete_combo_package_otherinfos_by_manual_merge:
    '/v1/productadminbffsrv/{{platform}}/otherinfo_combo_service/delete_combo_package_otherinfos_by_manual_merge',
  send_standalone_package_otherinfos_for_combo:
    '/v1/productadminbffsrv/{{platform}}/otherinfo_combo_service/send_standalone_package_otherinfos_for_combo',

  // 清除缓存
  cache_clear: '/v1/productadminsrv/activity/service/cache_clear',

  // 台湾履行保证明
  upload_package_escrow_guarantee_photos: '/v1/productadminbffsrv/{{platform}}/upload_escrow_guarantee_photo',
  download_package_escrow_guarantee_photos:
    '/v1/productadminbffsrv/{{platform}}/download_escrow_guarantee_photo',

  get_all_package_rules_value_list: '/v1/productadminsrv/packages/service/get_all_package_rules_value_list',

  // rate plan
  filter_rate_plan_product: '/v1/productadminbffsrv/{{platform}}/pricing_service/filter_rate_plan_product',
  create_rate_plan: '/v1/productadminbffsrv/{{platform}}/pricing_service/create_rate_plan',
  rate_plan_preview: '/v1/productadminbffsrv/{{platform}}/pricing_service/rate_plan_preview',
  get_child_nodes_by_id: '/v1/productadminbffsrv/{{platform}}/area_service/get_child_nodes_by_id',
  rate_plan_list: '/v1/productadminbffsrv/{{platform}}/pricing_service/rate_plan_list',
  update_rate_plan_all: '/v1/productadminbffsrv/{{platform}}/pricing_service/update_rate_plan_all',
  rate_plan_log: '/v1/productadminbffsrv/{{platform}}/pricing_service/rate_plan_log',
  get_all_continent_area_and_country:
    '/v1/productadminbffsrv/{{platform}}/area_service/get_all_continent_area_and_country',
  get_rate_plan_list_filter_param:
    '/v1/productadminbffsrv/{{platform}}/pricing_service/get_rate_plan_list_filter_param',
  delete_rate_plan: '/v1/productadminbffsrv/{{platform}}/pricing_service/delete_rate_plan',
  end_rate_plan: '/v1/productadminbffsrv/{{platform}}/pricing_service/end_rate_plan',
  recover_rate_plan: '/v1/productadminbffsrv/{{platform}}/pricing_service/recover_rate_plan',
  suspend_rate_plan: '/v1/productadminbffsrv/{{platform}}/pricing_service/suspend_rate_plan',
  update_rate_plan_end_time: '/v1/productadminbffsrv/{{platform}}/pricing_service/update_rate_plan_end_time',
  get_rate_plan_receivable_info_by_merchant_id:
    '/v1/productadminbffsrv/admin/pricing_service/get_rate_plan_receivable_info_by_merchant_id',
  get_campaign_list: '/v1/productadminbffsrv/admin/pricing_service/get_campaign_list',

  // 重定向
  get_activity_redirect_page:
    '/v1/productadminbffsrv/{{platform}}/activity_service/get_activity_redirect_page',
  create_activity_redirect: '/v1/productadminbffsrv/{{platform}}/activity_service/create_activity_redirect',
  delete_activity_redirect: '/v1/productadminbffsrv/{{platform}}/activity_service/delete_activity_redirect',
  copy_activity_participant_and_review:
    '/v1/productadminbffsrv/{{platform}}/activity_service/copy_activity_participant_and_review',
  sync_batch_create_activity_redirect_and_copy_review:
    '/v1/productadminbffsrv/{{platform}}/activity_service/sync_batch_create_activity_redirect_and_copy_review',
  // itinerary
  get_poi_list: '/v1/productadminbffsrv/{{platform}}/itinerary_service/get_poi_list',
  get_image_list: '/v1/productadminbffsrv/{{platform}}/itinerary_service/get_image_list',
  get_itinerary_by_package: '/v1/productadminbffsrv/{{platform}}/itinerary_service/get_itinerary_by_package',
  get_exist_itinerary_package_ids:
    '/v1/productadminbffsrv/{{platform}}/itinerary_service/get_exist_itinerary_package_ids',
  copy_package_itinerary_instance_data:
    '/v1/productadminbffsrv/{{platform}}/itinerary_service/copy_package_itinerary_instance_data',

  // addon
  get_activity_add_on_list: '/v1/productadminbffsrv/{{platform}}/sku_service/get_activity_add_on_list',
  filter_add_on_sku: '/v1/productadminbffsrv/{{platform}}/sku_service/filter_add_on_sku',
  append_add_on_sku: '/v1/productadminbffsrv/{{platform}}/sku_service/append_add_on_sku',
  update_add_on_price_strategy:
    '/v1/productadminbffsrv/{{platform}}/sku_service/update_add_on_price_strategy',
  delete_add_on_sku: '/v1/productadminbffsrv/{{platform}}/sku_service/delete_add_on_sku',
  save_add_on_sku: '/v1/productadminbffsrv/{{platform}}/sku_service/save_add_on_sku',
  package_exist_mapped_add_on: '/v1/productadminbffsrv/{{platform}}/sku_service/package_exist_mapped_add_on',

  clear_package_itinerary_instance_data:
    '/v1/productadminbffsrv/{{platform}}/itinerary_service/clear_package_itinerary_instance_data',

  // new merchant
  create_package_with_skus: '/v1/productadminbffsrv/{{platform}}/package_service/create_package_with_skus',
  create_multi_skus: '/v1/productadminbffsrv/{{platform}}/sku_service/create_multi_skus',
  destroy_schedule_by_date_range:
    '/v1/productadminbffsrv/{{platform}}/calendar_service/destroy_schedule_by_date_range',
  deactivate_schedule_by_date_range:
    '/v1/productadminbffsrv/{{platform}}/calendar_service/deactivate_schedule_by_date_range',
  copy_sku_calendar: '/v1/productadminbffsrv/{{platform}}/sku_service/copy_sku_calendar',
  creates_or_update_schedule_by_date_range:
    '/v1/productadminbffsrv/{{platform}}/calendar_service/creates_or_update_schedule_by_date_range',
  update_sku_rule_setting: '/v1/productadminbffsrv/{{platform}}/calendar_service/update_sku_rule_setting',

  unlock: '/v1/productadminbffsrv/{{platform}}/activity_service/unlock',

  // display attr ux: act
  get_group_list_v2: '/v1/productadminbffsrv/{{platform}}/activity_service/get_group_list_v2',
  save_activity_draft_instance_data:
    '/v1/productadminbffsrv/{{platform}}/activity_service/save_activity_draft_instance_data',
  reset_activity_instance_draft_data:
    '/v1/productadminbffsrv/{{platform}}/activity_service/reset_activity_instance_draft_data',
  sort_activity_instance_draft_data:
    '/v1/productadminbffsrv/{{platform}}/activity_service/sort_activity_instance_draft_data',
  publish_activity_instance_draft_data:
    '/v1/productadminbffsrv/{{platform}}/activity_service/publish_activity_instance_draft_data',
  reset_activity_instance_draft_data_by_group_ids:
    '/v1/productadminbffsrv/{{platform}}/activity_service/reset_activity_instance_draft_data_by_group_ids',
  // display attr ux: pkg
  get_package_group_list_v2: '/v1/productadminbffsrv/{{platform}}/package_service/get_package_group_list_v2',
  save_package_instance_draft_data:
    '/v1/productadminbffsrv/{{platform}}/package_service/save_package_instance_draft_data',
  reset_package_instance_draft_data:
    '/v1/productadminbffsrv/{{platform}}/package_service/reset_package_instance_draft_data',
  sort_package_instance_draft_data:
    '/v1/productadminbffsrv/{{platform}}/package_service/sort_package_instance_draft_data',
  publish_package_instance_draft_data:
    '/v1/productadminbffsrv/{{platform}}/package_service/publish_package_instance_draft_data',
  reset_package_instance_draft_data_by_group_ids:
    '/v1/productadminbffsrv/{{platform}}/package_service/reset_package_instance_draft_data_by_group_ids',

  // mer 3.0
  get_data_status: '/v1/productadminbffsrv/merchant/activity_service/get_data_status',
  //reject
  get_price_ticket_reject_reason:
    '/v1/productadminbffsrv/{{platform}}/ticket_service/get_price_ticket_reject_reason',
  get_act_reject_reason:
    '/v1/productadminbffsrv/{{platform}}/activity_audit_service/get_ticket_reject_reason',
  get_pkg_reject_reason: '/v1/productadminbffsrv/{{platform}}/package_audit_service/get_ticket_reject_reason',

  // mer 2.0 price && inv
  delete_sku_in_use_timeslot:
    '/v1/productadminbffsrv/{{platform}}/calendar_service/delete_sku_in_use_timeslot',
  destroy_multi_schedule_by_sku_id:
    '/v1/productadminbffsrv/{{platform}}/calendar_service/destroy_multi_schedule_by_sku_id',
  update_multi_schedule_publish_status_by_sku_id:
    '/v1/productadminbffsrv/{{platform}}/calendar_service/update_multi_schedule_publish_status_by_sku_id',

  get_standard_activity_sorts: '/v1/productadminbffsrv/admin/pass_service/get_standard_activity_sorts',
  save_standard_activity_sorts: '/v1/productadminbffsrv/admin/pass_service/save_standard_activity_sorts',
  get_standard_activity_sorts_history:
    '/v1/productadminbffsrv/admin/pass_service/get_standard_activity_sorts_history',

  // merchant 活动和套餐发布
  act_update_status_by_ticket:
    '/v1/productadminbffsrv/{{platform}}/activity_audit_service/update_status_by_ticket',
  pkg_update_status_by_ticket:
    '/v1/productadminbffsrv/{{platform}}/package_audit_service/update_status_by_ticket',
  update_ticket_status_v2: '/v1/productadminbffsrv/{{platform}}/ticket_service/update_ticket_status_v2',
  // copy
  copy_sku_model: '/v1/productadminbffsrv/{{platform}}/sku_service/copy_sku_model',
  // poi tool
  search_ttd_poi: '/v1/productadminbffsrv/admin/ttd_poi_service/search_ttd_poi',
  query_poi: '/v1/productadminbffsrv/admin/ttd_poi_service/query_poi',
  poi_mark: '/v1/productadminbffsrv/admin/ttd_poi_service/mark',
  merge_poi: '/v1/productadminbffsrv/admin/ttd_poi_service/merge',
  link_poi: '/v1/productadminbffsrv/admin/ttd_poi_service/link',
  unlink_poi: '/v1/productadminbffsrv/admin/ttd_poi_service/unlink',
  export_merge_record: '/v1/productadminbffsrv/admin/ttd_poi_service/export_merge_record',
  export_link_record: '/v1/productadminbffsrv/admin/ttd_poi_service/export_link_record',
  export_place_clean_record: '/v1/productadminbffsrv/admin/ttd_poi_service/export_place_clean_record',

  get_activity_poi_list: '/v1/productadminbffsrv/{{platform}}/activity_service/get_activity_poi_list',
  // 接载信息
  get_current_and_next_child: '/v1/productadminbffsrv/{{platform}}/area_service/get_current_and_next_child',
  get_geom_by_area_id_list: '/v1/productadminbffsrv/{{platform}}/area_service/get_geom_by_area_id_list',
  search_text_from_google: '/v1/productadminbffsrv/{{platform}}/otherinfo_service/search_text_from_google',
  get_package_group_field_value:
    '/v1/productadminbffsrv/{{platform}}/otherinfo_service/get_package_group_field_value',
  // admin - act management
  filter_activity_responsible_bd:
    '/v1/productadminbffsrv/{{platform}}/ticket_service/filter_activity_responsible_bd',
  // report error
  feedback_topic_list: '/v1/merchantapisrv/feedback/feedback_service/feedback_topic_list',
  tool_upload_file: '/v1/merchantapisrv/tool_service/upload_file',
  tool_delete_file: '/v1/merchantapisrv/tool/tool_service/delete_file',
  feedback_submit: '/v1/merchantapisrv/feedback/feedback_service/feedback_submit',

  get_grouping_list: '/v1/productadminbffsrv/admin/activity_grouping_service/get_grouping_list',
  get_grouping_info: '/v1/productadminbffsrv/admin/activity_grouping_service/get_grouping_info',
  create_or_update_groupping:
    '/v1/productadminbffsrv/admin/activity_grouping_service/create_or_update_grouping',
  delete_grouping: '/v1/productadminbffsrv/admin/activity_grouping_service/delete_grouping ',
  sort_grouping: '/v1/productadminbffsrv/admin/activity_grouping_service/sort_grouping',

  get_spu_list: '/v1/productadminbffsrv/admin/product_service/get_spu_list',
  sort_spu: '/v1/productadminbffsrv/admin/product_service/sort_spu',
  get_grouping_rels: '/v1/productadminbffsrv/admin/activity_grouping_service/get_grouping_rels',
  update_grouping_rels: '/v1/productadminbffsrv/admin/activity_grouping_service/update_grouping_rels',
  get_grouping_status: '/v1/productadminbffsrv/admin/activity_grouping_service/get_grouping_status',
  update_grouping_status: '/v1/productadminbffsrv/admin/activity_grouping_service/update_grouping_status',
  create_group_meegle_translate_task:
    '/v1/productadminbffsrv/admin/activity_grouping_service/create_group_meegle_translate_task',
  get_group_meegle_translate_task_info:
    '/v1/productadminbffsrv/admin/activity_grouping_service/get_group_meegle_translate_task_info',

  // itinerary departure map
  save_customized_area: '/v1/productadminbffsrv/{{platform}}/area_service/save_customized_area',
  query_customized_area: '/v1/productadminbffsrv/{{platform}}/area_service/query_customized_area',
  check_reservation: '/v1/productadminbffsrv/{{platform}}/package_service/check_reservation',

  // itinerary audio manage
  save_itinerary_audio: '/v1/productadminbffsrv/admin/itinerary_service/save_itinerary_audio',
  get_itinerary_audio: '/v1/productadminbffsrv/admin/itinerary_service/get_itinerary_audio',
  get_itinerary_audio_v2: '/v1/productadminbffsrv/admin/itinerary_v2_service/get_itinerary_audio_v2',
  // delete_itinerary_audio: '/v1/productadminbffsrv/admin/itinerary_service/delete_itinerary_audio',

  // supply api mapping
  get_supply_api_partner: '/v1/productadminbffsrv/{{platform}}/package_service/get_supply_api_partner',
  get_supply_api_mapping: '/v1/productadminbffsrv/{{platform}}/package_service/get_supply_api_mapping',
  save_supply_api_mapping: '/v1/productadminbffsrv/{{platform}}/package_service/save_supply_api_mapping',
  // esim
  get_esim_published_act_pkg: '/v1/productadminbffsrv/admin/package_service/get_esim_published_act_pkg',

  check_itinerary_map: '/v1/productadminbffsrv/{{platform}}/package_service/check_itinerary_map',
  get_standard_cancel_policy_sub_category_ids:
    '/v1/productadminbffsrv/{{platform}}/package_service/get_standard_cancel_policy_subcategory_ids',
  // 活动位置录入
  search_city: '/v1/productadminbffsrv/{{platform}}/activity_service/search_city',
  auto_complete: '/v1/productadminbffsrv/{{platform}}/activity_service/auto_complete',
  place_detail: '/v1/productadminbffsrv/{{platform}}/activity_service/place_detail',
  // aid
  get_immigrant_package_list:
    '/v1/productadminbffsrv/{{platform}}/product_service/get_immigrant_package_list',
  get_immigrant_activity_list:
    '/v1/productadminbffsrv/{{platform}}/product_service/get_immigrant_activity_list',
  check_support_spu_page: '/v1/productadminbffsrv/{{platform}}/product_service/check_support_spu_page',
  batch_get_calendar_setting:
    '/v1/productadminbffsrv/{{platform}}/calendar_service/batch_get_calendar_setting',
  batch_update_calendar_setting:
    '/v1/productadminbffsrv/merchant/calendar_service/batch_update_calendar_setting',
  update_audit_status: '/v1/productadminbffsrv/{{platform}}/product_service/update_audit_status'
}

const parseApiObj = parseApiUrl(ACTIVITY_API)

export default parseApiObj
