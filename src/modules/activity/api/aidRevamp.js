import { parseApiUrl } from '@activity/utils'

const APIS = {
  getUnitCategory: '/v1/productadminbffsrv/{{platform}}/unit_type_service/get_unit_category',
  getPageType: '/v1/productadminbffsrv/admin/product_service/get_page_type', // 获取page type数据
  getSpuFloatingFields: '/v1/productadminbffsrv/{{platform}}/product_service/get_floating_fields', // 获取spu顶部导航栏数据和状态
  getAttrFloatingFields: '/v1/productadminbffsrv/{{platform}}/attraction_service/get_floating_fields', // 获取spu顶部导航栏数据和状态
  getAttrSpuFloatingFields: '/v1/productadminbffsrv/{{platform}}/product_service/get_spu_floating_fields', // 获取spu顶部导航栏数据和状态
  saveSpuStatus: '/v1/productadminbffsrv/{{platform}}/product_service/update_status', // 更新spu状态
  saveAttractionStatus: '/v1/productadminbffsrv/{{platform}}/attraction_service/update_status', // 更新状态
  getMenuList: '/v1/productadminbffsrv/{{platform}}/product_service/get_menu_list',
  getSpuStepStatus: '/v1/productadminbffsrv/{{platform}}/product_service/get_spu_step_status',
  getAttrSpuStepStatus: '/v1/productadminbffsrv/{{platform}}/product_service/get_attraction_step_status',
  getProductData: '/v1/productadminbffsrv/{{platform}}/product_service/get_product_data',
  postSyncApiMapping: '/v1/productadminbffsrv/{{platform}}/itinerary_v2_service/sync_api_mapping', // api sync mapping
  getItineraryBySpu: '/v1/productadminbffsrv/{{platform}}/itinerary_v2_service/get_itinerary_by_spu', // 获取departure&return、itinerary数据
  checkItineraryData: '/v1/productadminbffsrv/{{platform}}/itinerary_v2_service/check_itinerary_data', // 保存前校验departure&return、itinerary数据
  saveSpuItinerary: '/v1/productadminbffsrv/{{platform}}/itinerary_v2_service/save_itinerary_data', // 保存departure&return、itinerary数据
  postSpuCreate: '/v1/productadminbffsrv/{{platform}}/product_service/create', // tours 新流程创建虚拟活动
  getActivities: '/prosrv/activities', //搜索 activities
  getUnitType: '/v1/productadminbffsrv/{{platform}}/product_service/get_unit_type_info_by_spu_id', // get unit type
  saveUnitType: '/v1/productadminbffsrv/{{platform}}/product_service/create_or_update_unit_type', // save unit type
  saveSpuStepStatus: '/v1/productadminbffsrv/{{platform}}/product_service/save_spu_step_status',
  saveAttractionStepStatus: '/v1/productadminbffsrv/{{platform}}/product_service/save_attraction_step_status',
  // include_not_include
  getIncludeNotInclude: '/v1/productadminbffsrv/{{platform}}/product_service/get_include_not_include',
  saveIncludeNotInclude: '/v1/productadminbffsrv/{{platform}}/product_service/save_include_not_include',
  //
  checkBasicInfoDataForVariant:
    '/v1/productadminbffsrv/{{platform}}/product_service/check_basic_info_data_for_variant',
  checkIncludeDataForVariant:
    '/v1/productadminbffsrv/{{platform}}/product_service/check_include_data_for_variant',
  sortBindedVariant: '/v1/productadminbffsrv/{{platform}}/product_service/sort_binded_variant',
  search_attraction_by_location:
    '/v1/productadminbffsrv/{{platform}}/attraction_service/search_attraction_by_location',
  get_page_version: '/v1/productadminbffsrv/{{platform}}/product_service/get_page_version',
  get_spu_submit_status: '/v1/productadminbffsrv/{{platform}}/product_service/get_spu_submit_status',
  // aid revamp mp
  // guidance video
  getGuidanceVideoList: '/v1/productadminbffsrv/{{platform}}/product_service/get_guide_video_list',
  uploadGuidanceVideo: '/v1/productadminbffsrv/{{platform}}/product_service/upload_guide_video',
  deleteGuidanceVideo: '/v1/productadminbffsrv/{{platform}}/product_service/delete_guide_video',
  saveGuidanceVideo: '/v1/productadminbffsrv/{{platform}}/product_service/save_guide_video',
  getGuidanceVideoMgnData: '/v1/productadminbffsrv/admin/product_service/get_guide_video_list'
}

const parseApiObj = parseApiUrl(APIS)

export default parseApiObj
