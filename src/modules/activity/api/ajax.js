import { request } from '@klook/admin-utils/lib/request'
import { message, Modal } from 'ant-design-vue'
import { isDev, xReqClient } from '@/env'
import { getVersionValue } from './versionFactory.js'
import { NEED_RISK_VERIFY_CODE, RISK_VERIFY_KEY } from '@merchant/utils/constant.js'

import * as Interceptor from './interceptor'

// 缓存版本号，上线如果需要强制刷新缓存可以更新这个（比如 OBT 变量更新）
const AJAX_CACHE_VERSION = 20250102

let mockData = {}
try {
  const req = require.context('./', true, /mockData\.js$/)
  const requireAll = (requireContext) => requireContext.keys().map(requireContext)
  mockData = requireAll(req)[0] || {}
} catch (error) {
  mockData = {}
}

const MESSAGE_DEF_CONF = {
  content: '', // 可选参数，提示内容
  duration: 4, // 自动关闭的延时，单位秒。设为 0 时不自动关闭
  // 下面是自定义参数，key不能与组件可选入参同名
  type: '', // 可选参数，提示类型
  isSucMsg: false, // 成功请求默认是否提示
  isErrMsg: true, // 失败请求默认是否提示
  defSucContent: __('global_success'),
  errContent: '',
  defErrContent: __('global_failure'),
  sucMsgType: 'success',
  errMsgType: 'error',
  catchContent: '服务器繁忙，请稍后重试！',
  isModal: false
}
const merchantMessage = (res, ajaxOpt = {}, isCatchErr = false) => {
  if (isCatchErr) {
    let opt = _.merge({}, MESSAGE_DEF_CONF, ajaxOpt.options.msgOpt)
    let str = typeof res === 'string' ? res : res.message || res.toString()
    opt.content = opt.errContent || str || opt.defErrContent
    if (opt.isModal) {
      opt.class = 'common-modal-error-style'
      Modal.error(opt)
    } else {
      opt.isErrMsg &&
        message.error({
          ...opt,
          content: opt.content
        })
    }
    return false
  }
  let { config, options } = ajaxOpt // 入参
  let { returnResKey } = config // 自定义配置参数
  let { msgOpt } = options // 自定义配置参数
  let { success, error = {} } = res // 响应数据
  let opt = _.merge({}, MESSAGE_DEF_CONF, msgOpt)
  let { type: msgType, isSucMsg, isErrMsg, sucMsgType, errMsgType } = opt

  let retData = res // 返回值
  if (success) {
    returnResKey && (retData = res[returnResKey])
    msgType = msgType || sucMsgType
    opt.content = opt.content || opt.defSucContent
    isSucMsg && message[msgType] && message[msgType](opt)
  } else {
    msgType = msgType || errMsgType
    opt.content = opt.errContent || error.debug_message || error.message || opt.defErrContent
    isErrMsg && message[msgType] && message[msgType](opt)
  }
  return retData
}

const REQUEST_UNIQ = true
const AJAX_DEF_CONF = {
  timeout: 10000 * 6,
  method: 'get',
  returnResKey: 'result',
  params: {},
  headers: {
    // contentType: 'application/x-www-form-urlencoded; charset=utf-8',
    // contentType: 'application/json; charset=utf-8',
    'Accept-Language': klook.getAPILang(),
    'x-req-client': xReqClient
  },
  requestUniq: REQUEST_UNIQ
}

async function proxyMockData({ url, config }) {
  if (!isDev) return
  !config.mock && (config.mock = mockData)
  !config.mockUrl && url && (config.mockUrl = url.split('/').pop())
  let res = await config.mock
  let retData = res[url] || res[config.mockUrl] || false
  if (retData) {
    console.log(`------ ${config.mockUrl} ------ =`, retData)
  }
  return retData
}
export const setLoading = function (options, isTrue = false) {
  if (!isTrue) return
  let defOpt = {
    show: false
  }
  let opt = _.merge({}, defOpt)
  if (getType(options) === 'Object') {
    _.merge(opt, options)
  } else {
    _.merge(opt, { show: !!options })
  }
  let { show } = opt
  window.setLoading2root(show)
}
export const ajax = function (url, config = {}, options = {}, mergeConf = {}) {
  if (getType(url) === 'Object') {
    // 格式兼容处理
    options = config
    config = url
    url = config.url
  } else if (typeof url === 'string') {
    config.url = url
  }
  config = _.merge({}, AJAX_DEF_CONF, config, mergeConf)
  // 默认值处理
  let { requestUniq = REQUEST_UNIQ } = config
  let { requestInterceptor = [], responseInterceptor = [], loading = false } = options

  // request 只支持 use 单个 Interceptor，所以如果有自定义的，则下面的不生效
  if (!requestInterceptor.length && !responseInterceptor.length) {
    const requestId = Interceptor.getRequestId()
    const { interceptorPromiseInstance, interceptorPromiseResolve } = Interceptor.getPromiseInterceptor()

    requestInterceptor = [
      async (config) => {
        const VERSION_KEY = 'headers.version'
        let version = _.get(config, VERSION_KEY, null)

        if (!version) {
          _.set(config, VERSION_KEY, await getVersionValue(config))
        }

        if (requestUniq) {
          Interceptor.handleRequest({
            config,
            interceptorPromiseResolve,
            requestId
          })
        }

        return config
      },
      null
    ]

    responseInterceptor = [
      (response) => {
        if (requestUniq) {
          Interceptor.handleResponse({
            response,
            config
          })
        }

        return response
      },
      async (err) => {
        if (requestUniq && Interceptor.hasInterceptorKey(config)) {
          const curr = Interceptor.getInterceptorByConf(config)

          if (_.get(curr, 'requestId', null) === requestId) {
            Interceptor.clearInterceptorByConf({ config })
            return Promise.reject(err)
          }

          try {
            const response = await interceptorPromiseInstance.promise

            return response
          } catch (e) {
            // eslint-disable no-empty
          }
        }

        console.info(`%cRequest Url: ${config.url} \nError info: ${err}`, 'color: red;')

        return Promise.reject(err)
      }
    ]
  }

  setLoading(loading, loading) // 第二个参数为真才处理
  // 兼容request参数
  return request(config, requestInterceptor, responseInterceptor)
    .then((res) => {
      setLoading(false, loading) // 第二个参数为真才处理
      // 设置 noDefaultResponseInterceptor 获取完整的Response信息
      // 错误埋点需要获取到接口 error 内容
      let result = {}
      if (config.noDefaultResponseInterceptor) {
        result = res?.data ?? {}
      } else {
        result = res
      }
      let retData = merchantMessage(result, { url, config, options })
      return retData
    })
    .catch(async (err) => {
      setLoading(false, loading) // 第二个参数为真才处理

      const errorCode = err?.code
      if (errorCode === NEED_RISK_VERIFY_CODE) {
        klook.bus.$emit(RISK_VERIFY_KEY, true)
        return Promise.reject(err)
      }

      let retData = await proxyMockData({ url, config, options })

      if (retData) {
        return merchantMessage(retData, { url, config, options })
      } else {
        return merchantMessage(err, { url, config, options }, true)
      }
    })
}

import localforage from 'localforage'

export const get = (ajax.get = async function (url, config = {}, options = {}) {
  config = {
    cache: false,
    version: AJAX_CACHE_VERSION,
    maxAge: 60 * 60 * 24 * 1 * 1000, // 1 days
    ...config
  }

  let cacheKey = `${url}?${new URLSearchParams(config.params || {}).toString()}#version=${config.version}`

  if (config.cache) {
    let CacheControl = (await localforage.getItem('CacheControl')) || {}
    let refreshTimestamp = CacheControl[cacheKey] || 0
    let currentTimestamp = +new Date()
    if (currentTimestamp >= refreshTimestamp) {
      // expired
      let response = await ajax(url, config, options, {
        method: 'get',
        returnResKey: AJAX_DEF_CONF.returnResKey
      })

      CacheControl = Object.entries({
        ...CacheControl,
        [cacheKey]: currentTimestamp + config.maxAge
      }).reduce((acc, curr) => {
        let [key, val] = curr

        try {
          if (+key.split('#')[1].split('=')[1] < AJAX_CACHE_VERSION) {
            return acc
          }
        } catch (error) {
          // pass
        }

        return currentTimestamp < val
          ? {
              ...acc,
              [key]: val
            }
          : acc
      }, {})
      localforage.setItem('CacheControl', CacheControl)
      localforage.setItem(cacheKey, response)

      return response
    } else {
      return localforage.getItem(cacheKey)
    }
  } else {
    return ajax(url, config, options, {
      method: 'get',
      returnResKey: AJAX_DEF_CONF.returnResKey
    })
  }
})
export const post = (ajax.post = function (url, config = {}, options = {}) {
  return ajax(url, config, options, {
    method: 'post',
    returnResKey: AJAX_DEF_CONF.returnResKey
  })
})
export const getResult = (ajax.getResult = function (url, config = {}, options = {}) {
  return ajax(url, config, options, {
    method: 'get',
    returnResKey: AJAX_DEF_CONF.returnResKey
  })
})
export const postResult = (ajax.postResult = function (url, config = {}, options = {}) {
  return ajax(url, config, options, {
    method: 'post',
    returnResKey: AJAX_DEF_CONF.returnResKey
  })
})
export const getBody = (ajax.getBody = function (url, config = {}, options = {}) {
  return ajax(url, config, options, { method: 'get', returnResKey: '' })
})
export const postBody = (ajax.postBody = function (url, config = {}, options = {}) {
  return ajax(url, config, options, { method: 'post', returnResKey: '' })
})

export const formHeaders = (ajax.formHeaders = {
  'Content-Type': 'application/x-www-form-urlencoded'
})

export const sucOptions = (ajax.sucOptions = {
  loading: true,
  msgOpt: {
    isSucMsg: true
  }
})

const getType = (value) => {
  return Object.prototype.toString.call(value).slice(8, -1)
}

export default ajax
