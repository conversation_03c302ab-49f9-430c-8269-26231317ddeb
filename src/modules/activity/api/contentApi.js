import { parseApiUrl } from '@activity/utils'

const CONTENT_API = {
  getTripResourceList: '/v1/apiconnwebserv/prodimport/trip/resource/list',
  getTripPoiDetail: '/v1/apiconnwebserv/prodimport/trip/poi/detail',
  getTripResourceDetail: '/v1/apiconnwebserv/prodimport/trip/resource/detail',
  saveTicketConfig:
    '/v1/apiconnwebserv/prodimport/changelog/ticket/config/save',

  // mapping
  getTripMappingList: '/v1/apiconnwebserv/prodimport/trip/mapping/list',
  getTripPkgMapping: '/v1/apiconnwebserv/prodimport/trip/packages/mapping',
  getTripActBase: '/v1/apiconnwebserv/prodimport/trip/activity/base',
  getTripActPackages: '/v1/apiconnwebserv/prodimport/trip/activity/packages',
  searchTripPoi: '/v1/apiconnwebserv/prodimport/trip/poi/search',
  searchTripPoiResources: '/v1/apiconnwebserv/prodimport/trip/poi/resources',
  saveTripMapping: '/v1/apiconnwebserv/prodimport/trip/mapping/save',

  createAct: '/prosrv/activities/create',
  updateActBasic: '/prosrv/activities/{activityId}/basic/update',
  updateActBasicAttr: '/prosrv/activities/{activityId}/basic_attr/update',
  createPkg: '/prosrv/packages/create',
  // notification
  getChangeLogTicketList: '/v1/apiconnwebserv/prodimport/changelog/ticket/list',
  updateChangeLogTicketStatus:
    '/v1/apiconnwebserv/prodimport/changelog/ticket/update_status',
  getChangeLogTicketDetails:
    '/v1/apiconnwebserv/prodimport/trip/changelog/ticket/detail',
  getChangeLogTicketTodoNotification:
    '/v1/apiconnwebserv/prodimport/changelog/ticket/todo_notification'
}

const parseApiObj = parseApiUrl(CONTENT_API)

export default parseApiObj
