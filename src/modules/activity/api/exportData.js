import { parseApiUrl } from '@activity/utils'

const api = {
  get_all_filter_items: '/v1/productadminbffsrv/admin/activity_service/get_all_filter_items',
  getActivityList:
    '/v1/productadminsrv/toolsapi/export_data_service/search_or_download_activity_basic_info_by_filter_items',
  exportActivityList:
    '/v1/productadminsrv/toolsapi/export_data_service/search_or_download_activity_basic_info_by_filter_items',
  get_export_sub_category_list:
    '/v1/productadminsrv/toolsapi/export_data_service/get_export_sub_category_list',
  getPackageConfig: '/v1/productadminbffsrv/admin/package_service/get_all_filter_items',
  getPackageList:
    '/v1/productadminbffsrv/admin/package_service/search_or_download_package_basic_info_by_filter_items',
  exportPackageLList:
    'v1/productadminbffsrv/admin/package_service/search_or_download_package_basic_info_by_filter_items',
  search_pass_activity_items: '/v1/productadminbffsrv/admin/pass_service/search_pass_activity_items',
  get_pass_sku_list:
    '/v1/productadminbffsrv/admin/pass_service/get_pass_sku_list_by_pass_and_reservation_sku_id',
  batch_update_pass_units: '/v1/productadminbffsrv/admin/pass_service/batch_update_pass_units'
}

const parseApiObj = parseApiUrl(api)

export default parseApiObj
