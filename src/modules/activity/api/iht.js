import klook from '@/assets/js/lib/core'

export const USER = {
  info: null
}

export function composeCommonExt() {
  const ext = {
    Language: klook.getAPILang(),
    ...(composeUserInfoExt() || {})
    // ...(composeUserLocationExt() || {})
  }

  return `oid=Merchant_${USER.info?.merchant_id}&ext=${encodeURIComponent(JSON.stringify(ext))}`
}

function composeUserInfoExt() {
  if (!USER.info) {
    console.warn('Init userInfo failed!')
    return {}
  }

  const userInfo = USER.info

  return {
    // 是否自入驻（注册的商户如果还需要完善信息，则还未完成入住）
    MerchantIsSelfRegister: !userInfo.is_application,
    AccountType: userInfo.user_type,
    MerchantType: userInfo.business_type === 3 ? 'group' : 'merchant',
    MerchantName: userInfo.merchant_name,
    Account: userInfo.username,
    bdTeam: userInfo.bd_team ? userInfo.bd_team.map((item) => item.team_name).join(',') : '',
    ProductType: userInfo.productType?.length ? userInfo.productType.join(',') : '',
    Classification: userInfo.categories?.length ? userInfo.categories.map((item) => item.name).join(',') : ''
  }
}

// 地址信息先留着
// async function getUserLocationInfo(merchant_id) {
//   const res = await ajax.get('/v1/merchantapisrv/merchant/merchant_service/get_basic_info', {
//     params: {
//       merchant_id
//     }
//   })
//   userLocationInfo = {
//     ...res
//   }
//   await getCountryBycityIds(res.merchant_city_id)
// }

// async function getCountryBycityIds(cityId) {
//   const { result, success } = await ajax.postBody({
//     url: '/v1/merchantapisrv/tool/tool_service/get_country_info_by_city_ids',
//     data: {
//       id_list: [cityId]
//     }
//   })

//   if (success) {
//     userLocationInfo = {
//       ...userLocationInfo,
//       countryInfo: result
//     }
//   }
// }

//   return `oid=Merchant_${USER_INFO.merchant_id}&ext=${encodeURIComponent(JSON.stringify(ext))}`
// }

// function composeUserLocationExt() {
//   return {
//     MerchantCity: userLocationInfo.merchant_city_id,
//     MerchantCountry: userLocationInfo.countryInfo?.length
//       ? userLocationInfo.countryInfo.map((item) => item.country.id).join(',')
//       : ''
//   }
// }
