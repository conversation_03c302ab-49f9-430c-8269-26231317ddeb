import { struct } from './struct'
import { request } from '@klook/admin-utils/lib/request'

import act from './activity'
import contentApi from './contentApi'
import solutionApi from './solution'
import poi from './poi'
import merchant from '@merchant/utils/urls.js'
import aidRevamp from './aidRevamp'

export { aidRevamp }
export { act }
export { struct }
export { contentApi }
export { solutionApi }
export { poi }
export { merchant }

export function getActVariables(params) {
  return request({
    url: '/v1/productadminbffsrv/merchant/category_service/get_published_category_info',
    params,
    method: 'get'
  })
    .then((res) => {
      return res
    })
    .catch((err) => {
      return err
    })
}

export function getActivityLanguageStatus(activityId) {
  return request({
    url: `/prosrv/activities/${struct('number!')(+activityId)}/status_all`,
    method: 'get'
  })
    .then((res) => {
      return res
    })
    .catch((err) => {
      return err
    })
}

export function getConstCountryCode(params) {
  return request({
    url: '/agent/adminserv/config/consts/country_code',
    params,
    method: 'get'
  })
    .then((res) => {
      return res
    })
    .catch((err) => {
      return err
    })
}

export default {
  struct,
  getActVariables,
  getActivityLanguageStatus,
  getConstCountryCode
}
