import axios from 'axios'

const CancelToken = axios.CancelToken
let requestQueue = new Map()

const getInterceptorPromiseInstance = function getInterceptorPromiseInstance(executor) {
  if (typeof executor !== 'function') {
    throw new TypeError('executor must be a function.')
  }

  let promiseResolve
  this.promise = new Promise((resolve) => {
    promiseResolve = resolve
  })

  executor((response) => {
    promiseResolve(response)
  })
}

export const getRequestId = () => {
  return (
    'request-id-xxxx-xxxx-'.replace(/[x]/g, () => ((Math.random() * 16) | 0).toString(16)) +
    parseInt(+new Date()).toString(32)
  )
}

export const getPromiseInterceptor = function getPromiseInterceptor() {
  let interceptorPromiseResolve

  let interceptorPromiseInstance = new getInterceptorPromiseInstance((resolveFunc) => {
    interceptorPromiseResolve = resolveFunc
  })

  return {
    interceptorPromiseInstance,
    interceptorPromiseResolve
  }
}

export const getKeyByConf = (config) => {
  let { url, params = {} } = config

  if (toString.call(params) === '[object Object]') {
    params = Object.keys(params)
      .sort()
      .reduce(
        (acc, curr) => ({
          ...acc,
          [curr]: params[curr]
        }),
        {}
      )
  }

  return JSON.stringify({
    url,
    params
  })
}

export const hasInterceptorKey = (config) => {
  return requestQueue.has(getKeyByConf(config))
}

export const getInterceptorByConf = (config) => {
  return requestQueue.get(getKeyByConf(config))
}

const setQueue = ({ config, interceptorPromiseResolve }) => {
  const key = getKeyByConf(config)
  const curr = requestQueue.get(key)

  requestQueue.set(key, {
    ...curr,
    queue: [...curr.queue, interceptorPromiseResolve]
  })
}

export const handleRequest = ({ config, interceptorPromiseResolve, requestId }) => {
  if (config.method.toLowerCase() !== 'get') {
    return
  }

  const key = getKeyByConf(config)
  const curr = requestQueue.get(key)

  if (curr) {
    setQueue({
      config,
      interceptorPromiseResolve
    })

    const { url, params } = url
    const note = `${url} - ${params}`
    console.log(`%c ${note}`, 'background-color: #ff0;')

    config.cancelToken = new CancelToken((cancel) => {
      cancel(note)
    })
  } else {
    requestQueue.set(key, {
      queue: [],
      requestId,
      response: null
    })
  }
}

export const handleResponse = ({ response, config }) => {
  if (config.method.toLowerCase() !== 'get') {
    return
  }

  const key = getKeyByConf(config)
  const curr = requestQueue.get(key)

  if (curr) {
    curr.queue.forEach((interceptorPromiseResolve) => {
      interceptorPromiseResolve(_.cloneDeep(response))
    })

    requestQueue.delete(key)
  }
}

export const clearInterceptorByConf = ({ config }) => {
  const curr = getInterceptorByConf(config)

  if (curr) {
    curr.queue.forEach((interceptorPromiseResolve) => {
      interceptorPromiseResolve(false)
    })

    requestQueue.delete(getKeyByConf(config))
  }
}
