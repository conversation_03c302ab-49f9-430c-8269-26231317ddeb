// 复制mockDataDemo.js变更文件名为mockData.js
// 开发示例 当测试环境接口报错，如果设置了mockData则会自动代理自定义返回数据
// 对应接口返回数据：新建活动
// create_activity:
//   '/v1/productadminbffsrv/{{platform}}/activity_service/create_activity',
// export let 最后一段接口名即可
// export let create_activity = {
//   error: {
//     // ,
//     code: 1, // ,
//     message: 'string' // ,
//   },
//   result: {
//     data: '成功数据'
//   }, // ,
//   success: true // ,
// }
// export let create_activity = {
//   error: {
//     // ,
//     code: 444, // ,
//     message: '请求失败' // ,
//   },
//   result: null, // ,
//   success: false // ,
// }

export let get_packages_basic_info2 = {
  error: {
    code: '',
    message: ''
  },
  result: {
    activity_id: 37053,
    field_items: [
      {
        access_permission: 2,
        config: '{"validation": 1}',
        field_key: 'pkg_name',
        required: true,
        value: [
          {
            language: 'en_US',
            name: 'open ticket pkg'
          },
          {
            language: 'zh_CN',
            name: 'open ticket pkg'
          }
        ]
      },
      {
        access_permission: 2,
        config:
          '{"validation": 2, "desc_multilang": [{"value": "", "language": "en_US"}, {"value": "", "language": "zh_CN"}, {"value": "", "language": "zh_TW"}]}',
        field_key: 'pkg_subname',
        required: true,
        value: [
          {
            language: 'en_US',
            name: ''
          },
          {
            language: 'zh_CN',
            name: ''
          }
        ]
      },
      {
        access_permission: 2,
        config:
          '{"validation": 2, "desc_multilang": [{"value": "This is a tip to Ops for attributes setting, and would fill in with package name by default. It\'s non-visible to customers.", "language": "en_US"}, {"value": "这是给Ops用于销售属性设置的提示，系统将默认填充套餐名（用户不可见）", "language": "zh_CN"}, {"value": "這是給Ops用於銷售内容設定的提示，系統將默認填充套餐名（用戶不可見）", "language": "zh_TW"}]}',
        field_key: 'package_tips',
        required: true,
        value: [
          {
            language: 'en_US',
            name: ''
          },
          {
            language: 'zh_CN',
            name: ''
          }
        ]
      },
      {
        access_permission: 2,
        config:
          '{"validation": 2, "desc_multilang": [{"value": "Auto publish and unpublish can be turned on independently or consistently. Auto publish cannot be cut off 30 minutes before the set-up time.", "language": "en_US"}, {"value": "自动发布/下架功能可以单独开启，也可以同时开始使用。在设置套餐为自动发布后，在设置好的时间的前30分钟之内，无法撤销自动发布。", "language": "zh_CN"}, {"value": "自動發佈/下架功能可以單獨開啟，也可以同時開始使用。在設定套餐為自動發佈後，在設定好的時間的前30分鐘之內，無法撤銷自動發佈。", "language": "zh_TW"}]}',
        field_key: 'auto_pub_unpub',
        required: true,
        value: {
          id: 0,
          package_id: 0,
          published: 0,
          published_time: '',
          unpublished: 0,
          unpublished_time: ''
        }
      },
      {
        access_permission: 2,
        config:
          '{"validation": 1, "desc_multilang": [{"value": "", "language": "en_US"}, {"value": "", "language": "zh_CN"}, {"value": "", "language": "zh_TW"}]}',
        field_key: 'cancellation_policy',
        required: true,
        value: 9
      },
      {
        access_permission: 2,
        config:
          '{"validation": 1, "desc_multilang": [{"value": "", "language": "en_US"}, {"value": "", "language": "zh_CN"}, {"value": "", "language": "zh_TW"}]}',
        field_key: 'confirmation_time',
        required: true,
        value: 1
      },
      {
        access_permission: 2,
        config:
          '{"validation": 1, "desc_multilang": [{"value": "", "language": "en_US"}, {"value": "", "language": "zh_CN"}, {"value": "", "language": "zh_TW"}]}',
        field_key: 'does_it_include',
        required: true,
        value: 0
      },
      {
        access_permission: 2,
        config:
          '{"validation": 1, "desc_multilang": [{"value": "Please confirm whether the merchant has provided the escrow/bank guarantee for any Taiwan local packages", "language": "en_US"}, {"value": "若该套餐是台湾当地产品，请确认商户是否已提供履约保证", "language": "zh_CN"}, {"value": "若该套餐是台湾当地产品，请确认商户是否已提供履约保证", "language": "zh_TW"}]}',
        field_key: 'escrow_guarantee',
        required: true,
        value: false
      },
      {
        access_permission: 2,
        config:
          '{"validation": 1, "desc_multilang": [{"value": "Make sure to apply for API mapping before you publish the package/activity", "language": "en_US"}, {"value": "请记得在活动/套餐发布前申请API Mapping", "language": "zh_CN"}, {"value": "請記得在活動/套餐發佈前申請API Mapping", "language": "zh_TW"}]}',
        field_key: 'inventory_type',
        required: true,
        value: 'KLOOKCODE'
      },
      {
        access_permission: 2,
        config:
          '{"validation": 2, "desc_multilang": [{"value": "To turn on, the Merchant self-confirmation needs to be turned on first", "language": "en_US"}, {"value": "如要打开该开关，需要先开启merchant页面的“确认出票开关”", "language": "zh_CN"}, {"value": "如要打開該開關，需要先開啟merchant頁面的“確認出票開關”", "language": "zh_TW"}]}',
        field_key: 'merchant_confirm',
        required: true,
        value: {
          merchant_confirm_col: 1,
          merchant_confirm_type: 1,
          merchant_self_confirm: 0
        }
      },
      {
        access_permission: 2,
        config:
          '{"validation": 2, "desc_multilang": [{"value": "", "language": "en_US"}, {"value": "", "language": "zh_CN"}, {"value": "", "language": "zh_TW"}]}',
        field_key: 'package_type',
        required: true,
        value: 1
      },
      {
        access_permission: 2,
        config: '{"validation": 2}',
        field_key: 'pkg_contact',
        required: true,
        value: null
      },
      {
        access_permission: 2,
        config: '{"validation": 1}',
        field_key: 'pkg_merchant',
        required: true,
        value: 1208
      },
      {
        access_permission: 2,
        config: '{"validation": 2}',
        field_key: 'price_displaying',
        required: true,
        value: 0
      },
      {
        access_permission: 2,
        config: '{"validation": 1}',
        field_key: 'sensitive_info',
        required: true,
        value: ''
      },
      {
        access_permission: 2,
        config: '{"validation": 1}',
        field_key: 'show_account_type',
        required: true,
        value: 0
      },
      {
        access_permission: 2,
        config:
          '{"validation": 2, "desc_multilang": [{"value": "", "language": "en_US"}, {"value": "", "language": "zh_CN"}, {"value": "", "language": "zh_TW"}]}',
        field_key: 'ticket_type',
        required: true,
        value: 3
      },
      {
        access_permission: 2,
        config:
          '{"validation": 1, "desc_multilang": [{"value": "Please specify whether the activity\'s voucher is transferable to another user. The voucher cannot be transferred if it includes the user\'s name and/or requires ID verification to redeem", "language": "en_US"}, {"value": "指购买后是否可以将凭证转让给其他人使用。假如凭证需要填写姓名，并且参与活动时需要姓名认证，那此活动就是不可转让。", "language": "zh_CN"}, {"value": "指購買後是否可以將憑證轉讓給其他人使用。假如憑證需要填寫姓名，並且參與活動時需要姓名認證，那此活動就是不可轉讓。", "language": "zh_TW"}]}',
        field_key: 'transferable',
        required: true,
        value: 0
      },
      {
        access_permission: 2,
        config:
          '{"validation": 1, "desc_multilang": [{"value": "", "language": "en_US"}, {"value": "", "language": "zh_CN"}, {"value": "", "language": "zh_TW"}]}',
        field_key: 'voucher_code_level',
        required: true,
        value: 'UniqueVoucher'
      },
      {
        access_permission: 2,
        config:
          '{"validation": 1, "desc_multilang": [{"value": "", "language": "en_US"}, {"value": "", "language": "zh_CN"}, {"value": "", "language": "zh_TW"}], "default_choices": 1}',
        field_key: 'voucher_get_method',
        required: true,
        value: 0
      },
      {
        access_permission: 2,
        config:
          '{"validation": 1, "desc_multilang": [{"value": "", "language": "en_US"}, {"value": "", "language": "zh_CN"}, {"value": "", "language": "zh_TW"}]}',
        field_key: 'voucher_type',
        required: true,
        value: 0
      },
      {
        access_permission: 2,
        config:
          '{"validation": 1, "desc_multilang": [{"value": "", "language": "en_US"}, {"value": "", "language": "zh_CN"}, {"value": "", "language": "zh_TW"}]}',
        field_key: 'voucher_usage',
        required: true,
        value: 0
      }
    ],
    merchant_id: 1208,
    package_id: 75422
  },
  success: true
}
