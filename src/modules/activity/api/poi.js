import { parseApiUrl } from '@activity/utils'

const CONTENT_API = {
  getPoiPageListParam: '/v1/productadminbffsrv/{{platform}}/poi_page_service/get_poi_page_list_param',
  getPoiPageList: '/v1/productadminbffsrv/{{platform}}/poi_page_service/get_poi_page_list',
  updatePoiPageStatus: '/v1/productadminbffsrv/{{platform}}/poi_page_service/update_poi_page_status',
  postGptResult: '/v1/productadminbffsrv/{{platform}}/poi_page_service/get_gpt_result',
  postGptHistory: '/v1/productadminbffsrv/{{platform}}/poi_page_service/get_gpt_history',
  postGptFatResult: '/v1/productadminbffsrv/{{platform}}/poi_page_service/get_gpt_faq_result',
  getPoiBasic: '/v1/productadminbffsrv/{{platform}}/poi_page_service/get_poi_basic',
  savePoiBasic: '/v1/productadminbffsrv/{{platform}}/poi_page_service/save_poi_basic',
  getPoiActs: '/v1/productadminbffsrv/{{platform}}/poi_page_service/get_poi_acts',
  savePoiActs: '/v1/productadminbffsrv/{{platform}}/poi_page_service/save_poi_acts',
  getPageSeo: '/v1/productadminbffsrv/{{platform}}/poi_page_service/get_page_seo',
  savePageSeo: '/v1/productadminbffsrv/{{platform}}/poi_page_service/save_page_seo',
  uploadPois: '/v1/productadminbffsrv/{{platform}}/poi_page_service/upload_pois'
}

const parseApiObj = parseApiUrl(CONTENT_API)

export default parseApiObj
