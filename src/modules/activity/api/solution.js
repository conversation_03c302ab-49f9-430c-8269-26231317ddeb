import { parseApiUrl } from '@activity/utils'

const api = {
  get_google_ttdproducts_info: '/v1/productkitsrv/googlettd/exp_admin_service/get_google_ttdproducts_info',
  query_fencing_list: '/v1/productkitsrv/googlettd/exp_admin_service/query_fencing_list',
  upsert_fencing_items: '/v1/productkitsrv/googlettd/exp_admin_service/upsert_fencing_items',
  del_fencing_items: '/v1/productkitsrv/googlettd/exp_admin_service/del_fencing_items',
  get_all_range_and_country: '/v1/productadminsrv/destination/service/get_all_range_and_country',
  get_poi_by_act: '/v1/productkitsrv/googlettd/exp_admin_service/get_poiby_act',
  query_act_fencing_item: '/v1/productkitsrv/googlettd/exp_admin_service/query_act_fencing_item',
  search_poi: '/v1/productkitsrv/googlettd/exp_admin_service/search_poi',
  update_poiby_act: '/v1/productkitsrv/googlettd/exp_admin_service/update_poiby_act',
  get_category_info: '/v1/productadminsrv/taxonomy/category_attr_service/get_category_info',
  update_gadmin_product_info: '/v1/productkitsrv/googlettd/exp_admin_service/update_gadmin_product_info',
  update_activity_config: '/v1/productkitsrv/googlettd/exp_admin_service/update_activity_config',
  get_activity_config: '/v1/productkitsrv/googlettd/exp_admin_service/get_activity_config',
  google_ttdmanual_feed: '/v1/productadminbffsrv/admin/google_ttdservice/google_ttdmanual_feed',
  // gttd 2.0
  get_modify_price_preview: '/v1/productkitsrv/googlettd/exp_admin_service/get_modify_price_preview',
  update_min_take_rate: '/v1/productkitsrv/googlettd/exp_admin_service/update_min_take_rate',
  update_price_preview_status: '/v1/productkitsrv/googlettd/exp_admin_service/update_price_preview_status',
  batch_update_min_take_rate: '/v1/productkitsrv/googlettd/exp_admin_service/batch_update_min_take_rate'
}
const parseApiObj = parseApiUrl(api)

export default parseApiObj
