let superstruct = require('superstruct').superstruct
let lang_conf = require('lang_conf')

import { SUPPORT_CURRENCY_SYMBO } from '../common/const'

// add custom structs #https://github.com/ianstormtaylor/superstruct/blob/master/docs/reference.md#custom-types
const struct = superstruct({
  types: {
    // email: value => isEmail(value) && value.length < 256,
    'string!': (v) => _.isString(v) && v !== '', // non-empty string,
    'number!': (v) => _.isNumber(v) && _.isInteger(v) && v !== 0,
    B_LANG: (v) => _.isString(v),
    language: (v) => lang_conf.getLangArray('B_LANG').includes(v),
    currency: (v) => SUPPORT_CURRENCY_SYMBO.map((e) => e[0]).includes(v),
    COUNTRY_CODE: (v) => lang_conf.getLangArray('COUNTRY_CODE').includes(v)
  }
})

export default struct

export { struct }
