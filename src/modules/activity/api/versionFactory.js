// import { isMerchant } from '@/env'
// import * as merLogin from '@klook/merchant-login'
// import * as adminLogin from '@klook/admin-login'

// const PRODUCTION_VERSION = '1'
const RELEASE_VERSION = '3'

// async function getUserInfo() {
//   const getUserInfo = isMerchant ? merLogin.getUserInfo : adminLogin.getUserInfo
//
//   return await getUserInfo()
// }

export const getVersionValue = async () => {
  // const userInfo = await getUserInfo()
  // const { account_name } = userInfo
  // const params = new URLSearchParams(location.search)
  //
  // if (account_name.includes('@klook') && params.get('release')) {
  //   return RELEASE_VERSION
  // }
  //
  // return PRODUCTION_VERSION

  return RELEASE_VERSION
}
