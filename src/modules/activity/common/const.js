/**
 * 存放常用配置
 */
const _ = require('lodash')
const siteConfig = require('@klook/site-config')

// this is serialized later
const SUPPORT_CURRENCY_SYMBO = Object.entries(siteConfig.currencyConfig.currencySymbolMap)

const NO_DOT_CURRENCY = [
  // commmon ones
  // source: http://apps.cybersource.com/library/documentation/sbc/quickref/currencies.pdf
  'BIF',
  'BYR',
  'CLP',
  'DJF',
  'GNF',
  'JPY',
  'KMF',
  'KRW',
  'MGA',
  'PYG',
  'RWF',
  'VND',
  'VUV',
  'XAF',
  'XOF',
  'XPF',
  // custom
  'IDR',
  'ISK',
  'LAK',
  'RUB',
  'HUF',
  'XPF',
  'TWD'
]

const WEB = {
  home: 'https://www.klook.com'
}

// 7: wifi, 8 sim, 9: Y<PERSON><PERSON>, 14: hotel voucher
const spu_template_ids = [7, 8, 9, 14]

const MONTHS = {
  en_US: [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December'
  ],
  zh_CN: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
  zh_TW: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
  ko_KR: ['1월', '2월', '3월', '4월', '5월', '6월', '7월', '8월', '9월', '10월', '11월', '12월'],
  th_TH: [
    'มกราคม',
    'กุมภาพันธ์',
    'มีนาคม',
    'เมษายน',
    'พฤษภาคม',
    'มิถุนายน',
    'กรกฎาคม',
    'สิงหาคม',
    'กันยายน',
    'ตุลาคม',
    'พฤศจิกายน',
    'ธันวาคม'
  ],
  vi_VN: [
    'Tháng 1',
    'Tháng 2',
    'Tháng 3',
    'Tháng 4',
    'Tháng 5',
    'Tháng 6',
    'Tháng 7',
    'Tháng 8',
    'Tháng 9',
    'Tháng 10',
    'Tháng 11',
    'Tháng 12'
  ],
  id_ID: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
  zh_HK: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
  ja_JP: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
  default: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
}

// this is used when user login cloudinary and don't know where the image belongs to
const photo_tags = {
  main_photos: 'admin_activity_main_photos',
  hot_to_use_photos: 'admin_activity_how_to_use_image',
  campaign_photos: 'admin_campaign_photos',

  seasonal_theme_photos: 'admin_seasonal_theme_photos',
  home_banner_photos: 'admin_home_banner_photos',
  city_theme_photo: 'admin_city_theme_photos',
  city_template_theme_photo: 'admin_city_template_theme_photos',
  help_center_photo: 'admin_help_center_photos',
  city_news: 'admin_city_news',
  city_articles: 'admin_city_articles',
  news_room: 'admin_news_room',
  fnb_content: 'admin_fnb_content',
  fnb_theme: 'admin_fnb_theme',
  activity_fnb: 'admin_activity_fnb',
  jrpass_entry: 'admin_jrpass_entry',
  country_banner: 'admin_country_banner',
  city_banner: 'admin_city_banner',
  rail_europe: 'admin_rail_europe',
  rail_cn_banner: 'admin_cn_banner',
  rail_cn_article: 'admin_cn_article',
  rail_cn_article_profile: 'admin_cn_article_profile',
  rail_cn_popular: 'admin_rail_cn_popular',
  cms_markdown: 'adimn_cms_markdown',
  act_markdown: 'admin_activity_markdown',
  country_experience: 'admin_country_experience',
  country_news: 'admin_country_news',

  act_room_imgs: 'admin_act_room_imgs',

  images_list_default: 'images_list_default',
  order_compensation_merchant_annex: 'admin_order_compensation_merchant_annex',
  transfer_banner: 'admin_transfer',
  transfer_theme_banner: 'admin_transfer_theme_banner',
  transfer_guide: 'admin_transfer_guide',
  car_rental_banner: 'admin_car_rental',
  car_rental_theme_banner: 'admin_car_rental_theme_banner',
  faq_center: 'admin_faq_center',
  city_top_activity: 'admin_city_top_activity',
  city_faq: 'admin_city_faq'
}

const HOSTS = {
  // the elb host is not as configuable as nignx and can only be reached internally.
  // so this is only used as internal service api.
  elb_api_host: 'http://elb-api.klook.io'
}

const gtag_id = 'UA-54803406-9'

const authErrorCode = 'Mail_60000'

// this file may use in server env, so use commonjs exports only
module.exports = {
  client_version: 'V3',
  HOSTS,
  SUPPORT_CURRENCY_SYMBO: _.sortBy(SUPPORT_CURRENCY_SYMBO, (v) => v[0]), // sort
  NO_DOT_CURRENCY,
  WEB,
  MONTHS,
  photo_tags,
  gtag_id,
  spu_template_ids,
  authErrorCode
}
