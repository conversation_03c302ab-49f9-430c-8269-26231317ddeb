/*
 * key: 'F_LANG', 'B_LANG', 'COUNTRY_CODE', 'country_code', 'LANG_TITLE'
 */
import { language_code as language_code_all } from './lang.config.server.js'

/*
 * use `defineProperty` because this should be only used in client env
 * we defer it so that there won't be `__` is not defined error
 */
Object.defineProperty(language_code_all, 'LANG_TITLE', {
  get() {
    return language_code_all.B_LANG.map((cc) => {
      if (cc === 'ms_MY') {
        return __('80439')
      }
      return __(`act_lan_${cc}`)
    })
  }
})

language_code_all.country_code = language_code_all.COUNTRY_CODE.map((ele) => ele.toLowerCase())

export const getLangArray = function(langString) {
  if (language_code_all[langString] instanceof Array) {
    return language_code_all[langString]
  } else {
    throw new Error('Function: ' + arguments.callee.name + ' arguments')
  }
}
export const getLangOptions = (key, value) => {
  let obj = getLangObj(key, value)
  let arr = []
  for (let key in obj) {
    arr.push({ [key]: obj[key] })
  }
  return arr
}
export const getLangObj = function(key, value) {
  // ([lang1...], [lang2...]) => {lang1: lang2,...}
  if (arguments.length !== 2) {
    throw new Error('Function: ' + arguments.callee.name + ' arguments ERROR!')
  }
  key = language_code_all[key]
  value = language_code_all[value]
  if (!(key instanceof Array && value instanceof Array)) {
    throw new Error('Function: ' + arguments.callee.name + ' arguments ERROR!')
  }
  if (key.length !== value.length) {
    throw new Error('Function: ' + arguments.callee.name + 'Language code length not match')
  }
  return key.reduce(
    (acc, curr, index) => ({
      ...acc,
      [curr]: value[index]
    }),
    {}
  )
}

export const getLangObjNoEn = function(key, value) {
  let langObj = getLangObj(key, value)

  for (let key in langObj) {
    if (key.includes('en') && key != 'en_US') {
      delete langObj[key]
    }
  }

  return langObj
}

/**
 *  获取系统支持的语言类型，主要是在多语言处理这块相关
 */
export const get_no_en_support_lang = function() {
  let langCopy = language_code_all.F_LANG.slice()
  let idx = langCopy.indexOf('en')
  langCopy.splice(idx, 1)
  return langCopy
}

export const getAllLangOption = function(lang = []) {
  return Object.entries(this.getLangObj('B_LANG', 'LANG_TITLE')).reduce((acc, curr) => {
    let [B_LANG, LANG_TITLE] = curr

    if (Array.isArray(lang) && lang.length) {
      if (!lang.includes(B_LANG)) {
        return acc
      }
    }

    return [
      ...acc,
      {
        B_LANG,
        options: LANG_TITLE
      }
    ]
  }, [])
}
