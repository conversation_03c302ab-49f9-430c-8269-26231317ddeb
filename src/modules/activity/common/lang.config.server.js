import { adminLanguage } from '@klook/site-config'

export const LANGUAGES_CONF = adminLanguage.getLanguagesConf().reduce((acc, curr) => {
  if (curr.B_LANG === 'en_US') {
    curr.F_LANG = 'en'
  }

  return [...acc, curr]
}, [])

export const en_lang_codes = adminLanguage.getEnLangCodes().reduce((acc, curr) => {
  if (curr.B_LANG === 'en_US') {
    return acc
  }

  if (curr.B_LANG === 'en_BS') {
    curr.F_LANG = 'en-BS'
  }

  return [...acc, curr]
}, [])

export const eu_lang_codes = adminLanguage.getEuLangCodes()

export const language_code = [...LANGUAGES_CONF, ...en_lang_codes, ...eu_lang_codes].reduce(
  (a, b) => {
    b.F_LANG && a.F_LANG.push(b.F_LANG)
    a.LANG_PATH.push(b.LANG_PATH)
    b.B_LANG && a.B_LANG.push(b.B_LANG)
    b.COUNTRY_CODE && a._COUNTRY_CODE_TEMP.push(b.COUNTRY_CODE)

    return a
  },
  {
    F_LANG: [],
    LANG_PATH: [],
    B_LANG: [],
    _COUNTRY_CODE_TEMP: [],
    get COUNTRY_CODE() {
      return this._COUNTRY_CODE_TEMP
    }
  }
)
export default language_code
