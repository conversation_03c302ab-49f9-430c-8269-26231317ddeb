<template>
  <a-button
    class="delete-btn"
    shape="circle"
    size="small"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <svg-icon icon-name="trash" style="color: #fff;" />
  </a-button>
</template>

<script>
export default {
  name: 'CircleDeleteButton'
}
</script>

<style lang="scss" scoped>
.delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background-color: #ffab00;

  &:hover {
    background-color: #ff4d4f;
  }
  &[disabled] {
    background-color: #d9d9d9 !important;
    // background-color: #ffab00;
  }
}
</style>
