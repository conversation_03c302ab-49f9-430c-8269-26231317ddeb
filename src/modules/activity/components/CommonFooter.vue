<template>
  <div
    class="common-footer"
    :class="{ '--has-activated': activateFooterWrapperState, '--non-activated': !activateFooterWrapperState }"
  >
    <slot />
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'CommonFooter',
  computed: {
    ...mapState(['activateFooterWrapperState'])
  },
  mounted() {
    this.$el.addEventListener('transitionend', this.onTransitionend)

    this.$once('hook:beforeDestroy', () => {
      this.$el.removeEventListener('transitionend', this.onTransitionend)
    })
  },
  methods: {
    onTransitionend() {
      if (this.activateFooterWrapperState) {
        //
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../assets/css/lib/mixins.scss';

.common-footer {
  &.merchant-footer {
    width: calc(100% - 280px);
    left: initial;
    right: 0;
    box-shadow: inset 0px 1px 0px rgb(0 0 0 / 9%);
  }

  @include mixin-switch-footer-wrapper-animation;
}
.common-footer {
  padding: 8px 20px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  box-shadow: 0px -4px 6px rgba(0, 0, 0, 0.06), 0px 0px 1px rgba(0, 0, 0, 0.05);
  background-color: #fff;
  z-index: 999;

  .ant-btn {
    margin-left: 12px;
  }
}
</style>
