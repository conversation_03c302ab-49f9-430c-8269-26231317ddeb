<template>
  <div class="slider-container">
    <a-tooltip placement="top" :title="tooltipTxt">
      <a-icon type="minus" :class="{ 'is-disabled': _value === min }" @click="handleDecrease" />
    </a-tooltip>

    <a-slider v-model="_value" :step="step" :min="min" :max="max" v-bind="$attrs" v-on="$listeners" />
    <a-tooltip placement="top" :title="tooltipTxt">
      <a-icon type="plus" :class="{ 'is-disabled': _value === max }" @click="handleIncrease" />
    </a-tooltip>
  </div>
</template>

<script>
export default {
  name: 'Slider',
  model: {
    event: 'change',
    prop: 'value'
  },
  props: {
    value: {
      type: Number,
      default: 1
    },
    step: {
      type: Number,
      default: 1
    },
    min: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 100
    }
  },
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('change', val)
      }
    },
    tooltipTxt() {
      return this._value.toFixed(1)
    }
  },
  methods: {
    handleIncrease() {
      this.$emit('change', Math.min(this.value + this.step, this.max))
    },
    handleDecrease() {
      this.$emit('change', Math.max(this.value - this.step, this.min))
    }
  }
}
</script>

<style lang="scss" scoped>
$color: #2073f9;

.slider-container {
  display: flex;
  align-items: center;
  width: 320px;
  margin: 24px auto;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);

  ::v-deep .ant-slider {
    flex: 1 0;
    margin: 0 12px;

    .ant-slider-track {
      background-color: $color;
    }

    .ant-slider-dot-active,
    .ant-slider-handle {
      border-color: $color;
    }
  }
}
</style>
