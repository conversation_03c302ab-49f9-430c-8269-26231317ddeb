<template>
  <div class="cropper-container">
    <div
      v-show="displayScrollBar"
      ref="virtualScrollBar"
      class="virtual-scroll-bar common-scrollbar border-slot"
      :style="virtualScrollBarStyle"
    >
      <div
        :style="{
          width: '1px',
          ...virtualScrollContainerStyle
        }"
      ></div>
    </div>
    <div
      ref="cropContent"
      class="common-scrollbar crop-content"
      :class="{ '--display-suggest': displaySuggest }"
    >
      <a-spin :spinning="spinning">
        <Cropper
          ref="cropper"
          style="width: 100%; height: 100%"
          drag-mode="move"
          :view-mode="1"
          :src="originImageUrl"
          :background="false"
          :aspect-ratio="aspectRatio"
          :initial-aspect-ratio="aspectRatio"
          :data="coordinates"
          :ready="hideLoading"
          :auto-crop-area="1"
          :crop-box-movable="true"
          :movable="true"
          :zoomable="true"
          :custom-zoom-func="customZoomFunc"
          :toggle-drag-mode-on-dblclick="false"
          @picDidCut="picDidCutFn"
          @initialized="handleInitialized"
          @cropped="cropped"
        />
      </a-spin>
    </div>

    <div v-if="!spinning" class="slider-box">
      <Slider
        v-if="displayZoom"
        v-model="sliderVal"
        :marks="marks"
        :step="0.1"
        :min="0.1"
        :max="5"
        :default-value="1"
      />
    </div>
  </div>
</template>

<script>
import { imageBaseUrl, folder } from '@activity/components/photo-group/const/index.js'
import { Cropper } from '@activity/components/Cropper'
import Slider from './components/slider'

const marks = [0.5, 1, 2, 3, 4].reduce((acc, curr) => {
  return {
    ...acc,
    [String(curr)]: ''
  }
}, {})

export default {
  name: 'CropperContainer',
  components: { Cropper, Slider },
  props: {
    value: {
      type: String,
      default: ''
    },
    imageName: {
      type: String,
      default: ''
    },
    ratio: {
      type: String,
      default: ''
    },
    preProcess: {
      type: String,
      default: ''
    },
    // 是否有裁剪数据。若没有，则图片能放大缩小。
    picDidCut: {
      type: Boolean,
      default: false
    },
    displaySuggest: {
      type: Boolean,
      default: false
    },
    displayZoom: {
      type: Boolean,
      default: false
    },
    displayScrollBar: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 可以通过滚动选图片的裁剪区域
      virtualScrollBarStyle: {
        opacity: 0.8
      },
      virtualScrollContainerStyle: {},

      //! 裁剪坐标
      coordinates: {
        x: undefined,
        y: undefined,
        width: undefined,
        height: undefined,
        zoom: undefined
      },

      spinning: true,

      marks,
      sliderVal: 1
    }
  },
  computed: {
    // 初始缩放比例
    aspectRatio() {
      let arr = this.ratio.split(':')
      //! undefined 标识为任意比例
      if (arr.length <= 1) {
        return
      }
      return arr[0] / arr[1]
    },
    // 原始图片地址, 作为裁剪基础
    originImageUrl() {
      return `${imageBaseUrl}fl_lossy.progressive,q_auto/${folder}${this.imageName}`
    }
  },
  watch: {
    //! 裁剪的参数
    // 1. c_crop,h_513,w_1296,x_323,y_566/c_fill,ar_1160:460,w_400/
    // 2. u_activities:ja6cz6rej05uaphxmagj,h_1.0,ar_1160:460,c_scale,e_blur:10000/c_fill,ar_1160:460,w_400
    preProcess: {
      immediate: true,
      handler(next) {
        //! 设置了不裁剪原图设置 banner, 坐标为空
        if (next.includes('u_activities')) {
          this.coordinates = {}
        } else {
          let { x, y, w, h, z } = this.transformCoordinates(next)
          const zoom = isNaN(z) ? 1 : +z
          this.coordinates = { x: +x, y: +y, width: +w, height: +h, zoom }
        }
      }
    },
    sliderVal(val) {
      if (this.$refs.cropper) {
        this.$refs.cropper.zoomTo(val)

        setTimeout(() => {
          this.initVirtualScrollBarFunc()
        })
      }
    }
  },
  beforeDestroy() {
    this.timer && clearTimeout(this.timer)
    this.onScroll && this.$refs.virtualScrollBar?.removeEventListener('scroll', this.onScroll)
    this.cropCanvasObserver?.disconnect?.()
    this.cropCanvasObserver = null
  },
  methods: {
    getPreProcess() {
      // c_crop,h_513,w_1296,x_323,y_566/c_fill,ar_1160:460,w_400/
      const cropper = this.$refs.cropper

      let { x, y, width, height } = cropper.getData()

      // 修复未初始化完, 点 confirm 问题
      if (!(width && height)) {
        this.$emit('crop', '')
        return ''
      }

      x = Math.floor(x)
      y = Math.floor(y)
      width = Math.floor(width)
      height = Math.floor(height)

      let zoom = cropper.getZoomValue()
      zoom = zoom.toFixed(1) || 1

      let pre_process = `c_crop,h_${height},w_${width},x_${x},y_${y},z_${zoom}/`
      this.$emit('crop', pre_process)

      return pre_process
    },
    // 转换 pre_process 为坐标
    transformCoordinates(pre_process) {
      if (!pre_process) {
        let { x, y, w, h, z } = {}

        return { x, y, w, h, z }
      }

      let str = pre_process.replace('/', '').replace(/_/g, '":"').replace(/,/g, '","')

      return JSON.parse(`{"${str}"}`)
    },
    hideLoading() {
      // const cropper = this.$refs.cropper

      // let { x, y, width, height, zoom } = this.coordinates
      // cropper.zoomTo(zoom)
      // this.sliderVal = zoom

      // cropper.setCropBoxData({
      //   left: x * zoom,
      //   top: y * zoom,
      //   width: width * zoom,
      //   height: height * zoom
      // })
      this.spinning = false
      this.$refs.cropper?.setData?.(this.coordinates)
    },
    // 裁剪完成
    cropped() {
      this.$emit('croppedPreProcess', this.getPreProcess())
    },
    picDidCutFn(value) {
      // picDidCut 为 true 之后就不能回滚了
      this.$emit('update:picDidCut', !!value || this.picDidCut)
    },
    async handleInitialized({ width, height, cropper }) {
      await this.$nextTick()

      if (width && height && this.$refs.cropContent) {
        document.querySelector('.cropper-face.cropper-move').scrollIntoView({
          block: 'center'
        })

        this.cropper = cropper
        this.initVirtualScrollBarFunc()
        this.cropCanvasMutationObserverFunc()
        this.$emit('initialized')
      }

      if (this.disabled) {
        cropper.disable()
      } else {
        cropper.enable()
      }
    },
    initVirtualScrollBarFunc() {
      const { cropper } = this
      const zeroPointTop = cropper.canvasData.minTop * -1 // 原点

      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        // Clipping height
        const cropperRemoveEle = document.querySelector('.cropper-face.cropper-move')
        if (cropperRemoveEle) {
          this.cropperMoveHight = cropperRemoveEle.getBoundingClientRect().height
          this.$set(this, 'virtualScrollBarStyle', {
            ...this.virtualScrollBarStyle,
            height: this.cropperMoveHight + 'px'
          })
        }

        // Crop the overall height of the picture
        this.cropperCanvasHeight = cropper.canvasData.height
        this.$set(this, 'virtualScrollContainerStyle', {
          height: this.cropperCanvasHeight + 'px'
        })

        const virtualScrollBar = this.$refs.virtualScrollBar
        if (!virtualScrollBar) {
          return
        }
        // Setting virtual scroll bar position
        virtualScrollBar.scrollTop = zeroPointTop
        this.preScrollPosition = zeroPointTop

        const thumbHeight = virtualScrollBar.scrollHeight - virtualScrollBar.clientHeight
        // The height of the draggable track area
        this.scrollbarTrackHeight = virtualScrollBar.scrollHeight - thumbHeight

        if (!this.onScroll) {
          this.onScroll = (event) => {
            this.virtualScrollBarCb({
              event,
              cropper
            })
          }

          this.$refs.virtualScrollBar.addEventListener('scroll', this.onScroll)
        }
      }, 150)
    },
    cropCanvasMutationObserverFunc() {
      let cropperCanvas = document.querySelector('.cropper-canvas')
      let config = { attributes: true, childList: false, subtree: false }
      let preDistance = null

      let callback = (mutationsList) => {
        const transform = _.get(mutationsList, '0.target.style.transform', '')

        if (transform) {
          const matchRes = transform.match(/translateY\(([^)px]+)p?x?\)/)

          if (Array.isArray(matchRes) && matchRes[1] && !isNaN(matchRes[1])) {
            const distance = Number(matchRes[1])
            if (preDistance === null) {
              preDistance = distance
            } else {
              this.counteractScrollTopEvent = true
              this.$refs.virtualScrollBar.scrollTop += preDistance - distance
              preDistance = distance
            }
          }
        }
      }

      this.cropCanvasObserver = new MutationObserver(callback)

      this.cropCanvasObserver.observe(cropperCanvas, config)
    },
    customZoomFunc({ ratio, context }) {
      ratio = Number(ratio)
      // ratio < 0: shrink -> up √
      // ratio > 0: zoom in -> down
      const distance = this.cropperCanvasHeight * ratio
      context.cropper.move(0, distance)

      this.$refs.virtualScrollBar.scrollTop -= ratio * this.scrollbarTrackHeight
      // Lock callback, look @virtualScrollBarCb
      this.counteractScrollTopEvent = true
    },
    virtualScrollBarCb({ event, cropper }) {
      // Offset the scroll event fired by @customZoomFunc
      if (this.counteractScrollTopEvent) {
        this.preScrollPosition = event.target.scrollTop
        this.counteractScrollTopEvent = false
        return
      }

      const currScrollPosition = event.target.scrollTop
      const offsetY = currScrollPosition - this.preScrollPosition
      const distance = -1 * (offsetY / this.scrollbarTrackHeight) * this.cropperCanvasHeight
      cropper.move(0, distance)
      // Record the last scroll position to calculate the offset before and after scrolling
      this.preScrollPosition = currScrollPosition
    }
  }
}
</script>

<style lang="scss">
.cropper-container {
  .virtual-scroll-bar {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 10px;
    width: 20px;
    overflow-y: auto;
  }

  .crop-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    padding: 0 10px;
    overflow: hidden;
    background-color: #707070;

    &.--display-suggest .cropper-face.cropper-move {
      opacity: 1;
      background-repeat: no-repeat;
      background-size: 100%;
      background-position: center;
      background-color: transparent;
      background-image: url('~@/assets/img/crop_suggestion_16_9.png');
    }
  }

  .cropper-modal {
    background-color: #828181;
  }
}
</style>
