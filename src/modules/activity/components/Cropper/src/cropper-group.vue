<template>
  <div v-if="loaded" :id="id" class="klk-cropper-group">
    <!-- 裁剪 -->
    <div class="klk-cropper-group__editor">
      <p class="klk-cropper-group__title">
        <span>{{ $t('taxonomy_destination_edit_image') }}</span>
        <span style="color: #888">
          - {{ $t('taxonomy_destination_image_size') }} - {{ imageData.naturalWidth }}*{{
            imageData.naturalHeight
          }}
        </span>
      </p>
      <div class="klk-cropper-group__editor__container">
        <template v-if="ready">
          <div
            v-for="(op, index) in config"
            v-show="isCurrent(op.id)"
            :key="op.id"
            :style="containerSizeLimit"
          >
            <Cropper
              :ref="op.id"
              :src="src"
              :preview="`#${op.id}`"
              :aspect-ratio="op.ratio"
              :background="false"
              :view-mode="2"
              :min-container-width="containerWidth"
              :min-container-height="containerHeight"
              :cropend="handleCrop"
              :data="cropData[index] || null"
              :zoomable="false"
              :auto-crop-area="1"
              :ready="finish"
            />
          </div>
        </template>
      </div>
    </div>
    <!-- 预览 -->
    <div class="klk-cropper-group__aside">
      <p class="klk-cropper-group__title">
        {{ $t('global_preview') }}
      </p>
      <div
        v-for="(op, i) in config"
        :key="op.id"
        class="klk-cropper-group__item"
        :class="{ 'is-current': isCurrent(op.id) }"
        @click="setCurrent(op.id, i)"
      >
        <i class="el-icon-loading resetbtn" @click="resetCropper(op.id)" />

        <div :id="op.id" class="klk-cropper-group__item__image" />
        <p>
          <span class="klk-cropper-group__item__label">{{ $t('taxonomy_destination_applications') }}:</span>
          <span class="klk-cropper-group__item__value">
            {{ op.description }}
          </span>
        </p>
        <p>
          <span class="klk-cropper-group__item__label">{{ $t('taxonomy_destination_ratio') }}:</span>
          <span class="klk-cropper-group__item__value">{{ op.ratioText }}</span>
        </p>
        <p>
          <span class="klk-cropper-group__item__label">{{ $t('taxonomy_destination_pixel') }}:</span>
          <span v-if="!isEditingItem(op.id)" class="klk-cropper-group__item__value">
            {{ showCurrentPixel(pixels[i], i) }}
            <i v-if="canEditPixel" class="el-icon-edit editbtn" @click="handleEdit(op, i)" />
          </span>
          <a-form-model :inline="true" @submit.native.prevent>
            <a-input
              v-if="isEditingItem(op.id) && isEdit"
              v-model="currentPixel"
              size="mini"
              placeholder="Width"
              :autofocus="true"
              @blur="cancelEdit"
              @keyup.enter.native="confirmEdit(op, i)"
            />
          </a-form-model>
        </p>
      </div>
    </div>
  </div>
</template>
<script>
import Cropper from './main'

export default {
  name: 'CropperGroup',
  components: { Cropper },
  props: {
    id: {
      type: String,
      default: () => _.uniqueId('klk_cropper_group_')
    },
    // width / height 比例
    options: {
      type: Array,
      default: () => []
    },
    // 图片地址
    src: {
      type: String,
      default: ''
    },
    cropData: {
      type: Array,
      default: () => []
    },
    canEditPixel: {
      type: Boolean,
      default: false
    },
    imageDataProp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      currentId: '',
      currentIndex: 0,
      containerHeight: 0,
      containerWidth: 0,
      ready: false,
      count: this.options.length,
      pixels: [],
      isEdit: false,
      editingId: -1,
      isFinish: false,
      loaded: true,
      imageData: {},
      containerSizeLimit: {}
    }
  },
  computed: {
    config() {
      let arr = this.options.map((v, index) => {
        return {
          id: _.uniqueId('klk_ratio_preview_'),
          ratio: v.ratio.split(':')[0] / v.ratio.split(':')[1],
          ratioText: v.ratio,
          description: v.description,
          pixel: v.pixel
        }
      })
      this.setCurrent(arr[0].id, 0)

      return arr
    },
    customConf() {
      return this.options.map((item) => ({
        scale: this.getCustomScale(item) || 1,
        customSize: item.size || []
      }))
    }
  },
  watch: {
    src(next, old) {
      next !== old && this.reload()
    }
  },
  mounted() {
    this.getContainerSize()
    let { naturalHeight, naturalWidth } = this.imageDataProp
    let ratio = naturalHeight / naturalWidth
    this.containerSizeLimit = {
      width: this.containerWidth + 'px',
      height: this.containerWidth * ratio + 'px'
    }
    this.containerHeight = this.containerWidth * ratio
    this.ready = true
  },
  methods: {
    showCurrentPixel(value, index) {
      let size = this.customConf[index].customSize
      if (Array.isArray(size) && size.length === 2) {
        return size[0] + '*' + size[1]
      }
      return value
    },
    fmtPixel(value, index) {
      if (value) {
        let scale = this.customConf[index].scale
        let [w, h] = value.split('*')

        w = Math.round(w * scale)
        h = Math.round(h * scale)
        return `${w}*${h}`
      }

      return ''
    },
    restorePixel(value, index) {
      if (value) {
        let scale = this.customConf[index].scale

        return Math.round(value / scale)
      }

      return ''
    },
    getCustomScale(v) {
      let scale = 1

      if (Array.isArray(v.size) && v.size.length) {
        let { naturalWidth, naturalHeight, aspectRatio } = this.imageData
        let customWidth = v.size[0],
          customHeight = v.size[1]

        if (customWidth && customHeight) {
          let customRatio = customWidth / customHeight

          if (customRatio <= aspectRatio) {
            scale = aspectRatio <= 1 ? customWidth / naturalWidth : customHeight / naturalHeight
          } else {
            scale = aspectRatio <= 1 ? customHeight / naturalHeight : customWidth / naturalWidth
          }
        } else if (customWidth) {
          scale = customWidth / naturalWidth
        } else if (customHeight) {
          scale = customHeight / naturalHeight
        }
      }

      return scale
    },
    // 是否当前
    isCurrent(id) {
      return id === this.currentId
    },
    // 是否当前编辑的item
    isEditingItem(id) {
      return id === this.editingId
    },
    // 取消编辑
    cancelEdit(id) {
      this.editingId = -1
      this.isEdit = false
      this.currentPixel = ''
    },
    // 确认编辑
    confirmEdit(op, index) {
      // 校验失败
      if (!this.validatePixel(this.currentPixel)) {
        this.cancelEdit(op.id)
        return
      }
      let arr = this.currentPixel.split('*')
      let { naturalHeight, naturalWidth } = this.imageData
      let data = this.$refs[op.id][0].getData()
      arr[0] = Math.min(naturalWidth, this.restorePixel(arr[0], index))
      arr[1] = Math.min(naturalHeight, this.restorePixel(arr[1], index))
      this.cancelEdit(op.id)
      data.width = arr[0]
      data.height = arr[1]
      this.$refs[op.id][0].setData(data)
      let { width, height } = this.$refs[op.id][0].getData(data)
      this.currentPixel = `${width}*${height}`
      this.pixels[index] = this.currentPixel
    },
    // 校验输入的pixel是否正确
    validatePixel(pixel) {
      return true
    },
    // 设置为当前选中
    setCurrent(id, index) {
      this.currentId = id
      this.currentIndex = index
    },
    // 获取外层宽度
    getContainerSize() {
      let container = document.querySelector(`#${this.id} .klk-cropper-group__editor__container`)
      this.containerWidth = container.offsetWidth
      this.containerHeight = container.offsetHeight
    },
    // 裁剪
    handleCrop() {
      console.log('crop')
      let datas = []
      this.pixels = []
      Object.keys(this.$refs).forEach((key, i) => {
        let data = this.$refs[key][0].getData()
        this.pixels[i] = `${Math.round(data.width)}*${Math.round(data.height)}`
        datas.push(data)
      })

      this.$nextTick(() => {
        this.$emit(
          'crop',
          datas.map((item, index) => {
            let scale = this.customConf[index].scale

            return {
              ...item,
              custom: {
                w: Math.round(item.width * scale),
                h: Math.round(item.height * scale),
                x: Math.max(+(item.x * scale).toFixed(2), 0),
                y: Math.max(+(item.y * scale).toFixed(2), 0),
                scale
              }
            }
          })
        )
      })
    },
    // 重置
    resetCropper(id) {
      this.$refs[id][0].reset()
      this.handleCrop()
    },
    // 编辑像素
    handleEdit(op, index) {
      this.editingId = op.id
      this.currentPixel = this.fmtPixel(this.pixels[index], index)
      this.isEdit = true
    },
    // finish
    finish() {
      this.count--
      if (!this.count) {
        this.isFinish = true
        this.handleCrop()
        let id = this.config[0].id
        this.imageData = this.$refs[id][0].getImageData()
        this.$emit('finish', true)
        this.$emit('original-image-data', this.imageData)
      }
    },
    // 重新加载
    reload() {
      this.loaded = false
      this.$nextTick((_) => {
        this.loaded = true
      })
    }
  }
}
</script>
<style lang="scss">
@use 'sass:color';
$asideWidth: 30%;
$previewBackground: #eff2f7;
$titleHeight: 45px;

.klk-cropper-group {
  position: relative;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: calc(100% - #{$asideWidth}) $asideWidth;
  // 预览
  &__aside {
    overflow: auto;
    .klk-cropper-group__item {
      position: relative;
      margin: 0 10px;
      padding: 10px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      align-items: center;
      transition: background-color 0.3s;
      cursor: pointer;
      // 重置按钮
      .resetbtn {
        position: absolute;
        animation: none;
        right: 10px;
        top: 10px;
        cursor: pointer;
        color: rgb(153, 153, 153);
        font-size: 10px;
        &:hover {
          color: rgb(196, 196, 196);
        }
      }
      // 编辑按钮
      .editbtn {
        font-size: 10px;
        color: #777;
      }
      p {
        width: 100%;
        text-align: left;
        color: #888;
        font-size: 12px;
      }
      &:hover {
        background-color: color.scale($previewBackground, $lightness: 2%);
      }
      // 图片
      &__image {
        width: 100%;
        height: 100px;
        margin: 20px;
        overflow: hidden;
      }
      // 标签
      &__label {
        color: rgb(70, 70, 70);
      }
      // 文字
      &__value {
        color: #6d6d6d;
      }
      // 选中
      &.is-current {
        background-color: $previewBackground;
      }
    }
  }
  // 裁剪
  &__editor {
    background-color: $previewBackground;
    width: 100%;
    height: 100%;
    &__container {
      margin: 0 20px 20px;
      line-height: calc(100% - #{$titleHeight} - 20px);
      height: calc(100% - #{$titleHeight} - 20px);
      width: calc(100% - #{$titleHeight});
    }
  }
  // 标题
  .klk-cropper-group__title {
    color: rgb(29, 29, 29);
    font-weight: 600;
    line-height: $titleHeight;
    height: $titleHeight;
    text-indent: 20px;
    font-size: 15px;
    margin: 0;
  }
}
</style>
