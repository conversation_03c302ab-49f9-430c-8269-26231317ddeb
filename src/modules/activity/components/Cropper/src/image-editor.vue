<template>
  <div :id="id" class="image-editor">
    <a-upload
      v-show="!previewSrc"
      ref="uploadBtn"
      :action="''"
      :show-file-list="false"
      :before-upload="getImage"
      accept=".jpg, .png, .jpeg"
    >
      <a-button :loading="loading" type="primary">
        {{ $t('taxonomy_destination_upload_images') }}
      </a-button>
    </a-upload>
    <a-button v-show="previewSrc" @click="showDialog">
      {{ $t('taxonomy_destination_edit_image') }}
    </a-button>
    <div
      v-if="previewSrc"
      class="image-preview"
      :style="{ 'background-image': `url(${previewSrc})` }"
    />
    <a-modal
      :maskClosable="false"
      title
      v-model="visible"
      class="image-editor-dialog"
      @close="hideDialog"
    >
      <div style="width: 100%; height: 100%">
        <div v-show="isUploading" class="upload-mask">
          <a-progress
            style="opacity: 0.6"
            :percent="percentage"
            :stroke-width="24"
            :text-inside="true"
            :status="percentage === 100 ? 'success' : ''"
          />
        </div>
        <CropperGroup
          :src="src"
          :options="options"
          :crop-data="cropData"
          :image-data-prop="imageDataProp"
          @crop="handleCrop"
          @original-image-data="originalImageDataHandle"
          @finish="loading = false"
        />
      </div>
      <div slot="footer">
        <a-button type="primary" @click="reupload">
          {{ $t('global_reupload') }}
        </a-button>
        <a-button @click="hideDialog">
          {{ $t('global_cancel') }}
        </a-button>
        <a-button type="primary" @click="confirmUpload">
          {{ $t('global_button_save') }}
        </a-button>
      </div>
    </a-modal>
  </div>
</template>
<script>
import CropperGroup from './cropper-group'
import { uploadFile, compress } from '../util'
import _ from 'lodash'

export default {
  name: 'ImageEditor',
  components: { CropperGroup },
  props: {
    id: {
      type: String,
      default: _.uniqueId('klk_image_editor')
    },
    // width / height 比例
    options: {
      type: Array,
      default: () => []
    },
    uploadOptions: {
      type: Object,
      default: () => ({})
    },
    initCropData: {
      type: Array,
      default: () => []
    },
    imageInitData: {
      // TODO validator
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      src: '',
      visible: false,
      loading: false,
      percentage: 0,
      isUploading: false,
      isUploaded: false,
      previewSrc: '',
      needUpload: true,
      originalImageData: {},
      file: {
        // real file data
        name: '',
        src: ''
      },
      imageDataProp: {}
    }
  },
  watch: {
    initCropData: {
      handler(v) {
        if (v) {
          this.cropData = v
        }
      },
      immediate: true,
      deep: true
    },
    src(next, old) {
      if (next && next !== old) {
        this.needUpload = true
        this.isUploaded = false
      }
    },
    imageInitData: {
      handler(v) {
        if (v.src) {
          let tempImage = new Image()
          tempImage.crossOrigin = '*'
          this.loading = true
          tempImage.onload = () => {
            let base64 = this.getBase64Image(tempImage)
            let file = this.dataURLtoBlob(base64)
            this.getImage(file, true)
            this.imageDataProp = {
              naturalHeight: tempImage.naturalHeight,
              naturalWidth: tempImage.naturalWidth
            }
            this.file && (this.file.name = v.name || '')
            this.previewSrc = base64
            this.hideDialog()
            this.loading = false
            document
              .querySelector(`#${this.id}`)
              .style.setProperty('--preview-image-label', `'${this.file.name}'`)
          }

          tempImage.src = v.src
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    originalImageDataHandle(data) {
      this.originalImageData = data
    },
    // 上传图片
    getImage(file, is_init = false) {
      !is_init && (this.loading = true)
      let _this = this
      let reader = new FileReader()
      reader.readAsDataURL(file)
      this.currentFile = file
      reader.onload = async (ev) => {
        // 压缩图片质量
        let imageInfo = await compress(ev.target.result, 'image/webp', 0.01)
        _this.imageDataProp = imageInfo
        _this.src = imageInfo.objURL
        // _this.src = await compress(ev.target.result, "image/webp", 0.01);
        // _this.src = ev.target.result;
        if (is_init) {
          this.$nextTick(() => {
            // after the watch src handler
            this.isUploaded = true
            this.needUpload = false
          })
        } else {
          this.cropData = []
          this.showDialog()
        }
      }
    },
    // 裁剪
    handleCrop(cropData) {
      this.cropData = cropData
      this.$emit('change', cropData)
    },
    // 显示弹窗
    showDialog() {
      this.visible = true
    },
    // 隐藏弹窗
    hideDialog() {
      this.visible = false
      this.loading = false
      if (!this.isUploaded) {
        !this.previewSrc && (this.src = '')
      } else {
        // this.file = this.currentFile;
        this.previewSrc = this.src
        document
          .querySelector(`#${this.id}`)
          .style.setProperty('--preview-image-label', `'${this.file.name}'`)
      }
    },
    // 确定上传
    async confirmUpload() {
      if (!this.needUpload) {
        this.$emit('save', {
          src: this.previewSrc,
          name: this.file.name,
          cropData: this.cropData
        })
        this.hideDialog()
        return
      }
      try {
        this.isUploading = true
        let uploadRes = await this.uploadImage(this.currentFile)
        this.file.name =
          _.findLast(uploadRes.public_id.split('/')) + '.' + uploadRes.format

        this.isUploaded = true
        this.needUpload = false
        this.hideDialog()

        this.$emit('original-image-data', this.originalImageData)
        this.$emit('save', {
          src: this.previewSrc,
          name: this.file.name,
          cropData: this.cropData
        })
      } catch (err) {
        this.$message.error('fail')
      } finally {
        this.isUploading = false
      }
    },
    // 重新上传
    reupload() {
      this.$refs.uploadBtn.$el.children[0].click()
    },
    // 上传图片
    async uploadImage(file) {
      let callback = this.setPercentage
      return uploadFile(file, {
        uploadConf: {
          folder: 'Mobile/Country',
          tags: 'admin_country_banner,2',
          upload_preset: 'k8xu3bkg',
          ...this.uploadOptions
        },
        setPercentage: this.setPercentage,
        callback: callback
      })
    },
    // 设置进度
    setPercentage(percentage) {
      this.percentage = percentage
    },
    getBase64Image(img) {
      let canvas = document.createElement('canvas')
      canvas.width = img.width
      canvas.height = img.height

      let ctx = canvas.getContext('2d')
      ctx.drawImage(img, 0, 0, img.width, img.height)
      this.$emit('original-image-data', {
        ...this.originalImageData,
        naturalWidth: img.width,
        naturalHeight: img.height
      })
      let ext = img.src.substring(img.src.lastIndexOf('.') + 1).toLowerCase()

      return canvas.toDataURL('image/' + ext)
    },
    dataURLtoBlob(base64) {
      let arr = base64.split(','),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n)

      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }

      return new Blob([u8arr], { type: mime })
    }
  }
}
</script>
<style lang="scss">
.image-editor {
  --preview-image-label: '';
  .image-preview {
    position: relative;
    margin-top: 20px;
    width: 320px;
    height: 180px;
    background-size: cover;
    &::after {
      content: var(--preview-image-label);
      position: absolute;
      width: 100%;
      height: 25px;
      line-height: 25px;
      color: #fff;
      bottom: 0;
      left: 0;
      background-color: rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(10px);
      text-indent: 10px;
    }
  }
}
.image-editor-dialog {
  // backdrop-filter: blur(6px);
  .el-dialog {
    width: 1000px;
    height: calc(100vh - 10%);
    max-height: calc(100vh - 10%);
    top: 5% !important;
    .el-dialog__header {
      display: none;
    }
    .el-dialog__body {
      padding: 0;
    }
    .el-dialog__footer {
      z-index: 2;
    }
    .upload-mask {
      width: 100%;
      position: absolute;
      height: 100%;
      padding: 40% 60px;
      background-color: rgba(255, 255, 255, 0.6);
      backdrop-filter: blur(5px);
      z-index: 1;
    }
  }
}
</style>
