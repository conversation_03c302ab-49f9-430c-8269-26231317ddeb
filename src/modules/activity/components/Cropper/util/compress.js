export const compress = async (dataURI, mime = 'image/webp', quality) => {
  let cvs = document.createElement('canvas')
  let img = new Image()
  img.src = dataURI
  let objURL = await new Promise((resolve) => {
    img.onload = async () => {
      cvs.width = img.width
      cvs.height = img.height
      cvs.getContext('2d').drawImage(img, 0, 0, cvs.width, cvs.height)
      let res = cvs.toDataURL('image/webp', 0.1)
      resolve(res)

      //   cvs.toBlob(
      //     blob => {
      //       resolve(URL.createObjectURL(blob));
      //     },
      //     mime,
      //     quality
      //   );
    }
  })
  // return objURL;
  return { objURL, naturalHeight: img.height, naturalWidth: img.width }
}
