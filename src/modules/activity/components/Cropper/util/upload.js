import axios from 'axios'

/**
 * 基础设置
 * cloudName 账户名
 * uploadPreset unsigned上传必传参数
 * tags 标签 - 可选
 * folder 文件夹分类 - 可选
 */
const CLOUDINARY_CONFIG = {
  cloudName: 'klook',
  uploadPreset: 'k8xu3bkg',
  tags: '',
  folder: ''
}

// 上传的url
const getUrl = (cloudName) => `https://api.cloudinary.com/v1_1/${cloudName}/upload`

/**
 * 上传图片
 * @param {File | String} file 上传的文件 - 可以是文件 | remote url
 */
const uploadFile = async (file, params) => {
  let { cloudName, uploadPreset, tags, folder } = {
    cloudName: CLOUDINARY_CONFIG.cloudName,
    uploadPreset: CLOUDINARY_CONFIG.uploadPreset,
    tags: CLOUDINARY_CONFIG.tags,
    folder: params.folder || CLOUDINARY_CONFIG.folder,
    max_file_size: 4000000, // 4M
    max_image_width: 3000,
    ...(params.uploadConf || {})
  }
  let url = getUrl(cloudName)
  let form = new FormData()
  form.append('upload_preset', uploadPreset)
  form.append('file', file)
  tags && form.append('tags', tags)
  folder && form.append('folder', folder)

  const config = {
    onUploadProgress: (e) =>
      typeof params.setPercentage === 'function' && params.setPercentage(((e.loaded / e.total) * 100) | 0)
  }
  try {
    let res = await axios.post(url, form, config)
    if (res.status === 200) {
      console.log('上传成功')
      return res.data
    }
  } catch (e) {
    console.log('上传失败')
    throw new Error(e)
  }
}

export { uploadFile }
