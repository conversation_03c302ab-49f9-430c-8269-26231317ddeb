<template>
  <a-tooltip v-if="isAsh" placement="top">
    <template slot="title">
      <span>{{ $t('26083') }}</span>
    </template>
    <div class="disabled-widgets-layer"></div>
  </a-tooltip>
</template>

<script>
export default {
  name: 'DisabledWidgetLayer',
  props: {
    data: {
      type: [Object, Array],
      default: () => ({})
    }
  },
  data() {
    return {
      isAsh: false
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler(val) {
        if (val && Object.prototype.hasOwnProperty.call(val, 'ash')) {
          this.isAsh = val.ash
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.disabled-widgets-layer {
  background-color: rgba(34, 34, 34, 0.1);
  display: block;
  z-index: 100;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  cursor: not-allowed;
}
</style>
