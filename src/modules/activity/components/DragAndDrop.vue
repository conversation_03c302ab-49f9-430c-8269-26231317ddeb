<template>
  <div
    class="drag-and-drop"
    :draggable="canDrag"
    :style="{ ...styleObj, opacity: moving ? 0.5 : 1 }"
    :class="[canDrag && 'can-drag']"
    @dragstart="handleDragstart"
    @dragend="handleDragend"
  >
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'DragAndDrop',
  props: {
    canDrag: {
      type: Boolean,
      default: true
    },
    initialPosition: {
      type: Object,
      default: null
    },
    storageName: {
      type: String,
      default: ''
    },
    useRightPosition: {
      type: Boolean,
      default: false
    },
    getBoundaryElement: {
      type: Function,
      default: () => document.documentElement
    }
  },
  data() {
    return {
      moving: false,

      styleObj: {
        right: '20px',
        bottom: '100px'
      },

      boundaryElementRect: {}
    }
  },
  computed: {
    boundaryElement() {
      return this.getBoundaryElement?.() ?? document.documentElement
    }
  },
  created() {
    this.styleObj = { ...this.initialPosition }

    const dragAndDropPosition = window.localStorage.getItem(this.storageName)
    if (dragAndDropPosition) {
      this.parseDragAndDropPosition(dragAndDropPosition)
    }
  },
  mounted() {
    this.resetDragAndDropPosition()

    if (document) {
      document.addEventListener('dragenter', this.handleDragenter)
      document.addEventListener('dragover', this.handleDragover)
      document.addEventListener('dragleave', this.handleDragleave)
      document.addEventListener('drop', this.handleDrop)
    }

    window.addEventListener('resize', this.resetDragAndDropPosition)
  },
  beforeDestroy() {
    if (document) {
      document.removeEventListener('dragenter', this.handleDragenter)
      document.removeEventListener('dragover', this.handleDragover)
      document.removeEventListener('dragleave', this.handleDragleave)
      document.removeEventListener('drop', this.handleDrop)
      window.removeEventListener('resize', this.resetDragAndDropPosition)
    }
  },
  methods: {
    handleDragstart(e) {
      if (this.canDrag && this.$el) {
        this.moving = true
        this.diffX = e.clientX - this.$el.offsetLeft
        this.diffY = e.clientY - this.$el.offsetTop
        // console.log('handleDragStart', e)
        this.$emit('dragstart')
      }
    },
    handleDragover(e) {
      e.preventDefault()
      // console.log('handleDragOver', e)
    },
    handleDragenter(e) {
      e.preventDefault()
      // console.log('handleDragenter', e);
    },
    handleDragleave(e) {
      e.preventDefault()
      // console.log('handleDragleave', e);
    },
    handleDragend() {
      this.moving = false
      this.$emit('dragend')
    },
    handleDrop(e) {
      if (this.moving) {
        const { boundaryElementRect } = this
        const { diffX, diffY } = this

        const minLeft = boundaryElementRect.left
        const maxLeft = boundaryElementRect.left + boundaryElementRect.width
        const minTop = boundaryElementRect.top

        let left = e.clientX - diffX
        let top = e.clientY - diffY

        // 控制拖拽物体的范围只能在浏览器视窗内
        if (left < minLeft) {
          left = minLeft
        } else if (left > maxLeft) {
          left = maxLeft
        }

        if (top < minTop) {
          top = minTop
        }

        let right = document.documentElement.clientWidth - left - this.$el.offsetWidth
        if (right < 0) {
          right = 0
        }

        this.styleObj = {
          top: `${top}px`,
          ...(this.useRightPosition ? { right: `${right}px` } : { left: `${left}px` })
        }

        this.moving = false
        this.saveToLocalStorage()
      }
    },
    saveToLocalStorage() {
      setTimeout(() => {
        const { top, left, right } = this.styleObj || {}
        if (this.useRightPosition) {
          top && right && window.localStorage.setItem(this.storageName, JSON.stringify({ top, right }))
        } else {
          top && left && window.localStorage.setItem(this.storageName, JSON.stringify({ top, left }))
        }
      }, 0)
    },
    parseDragAndDropPosition(position) {
      try {
        const parsedPosition = JSON.parse(position) || {}
        const { top, left, right } = parsedPosition

        if (top && (left || right)) {
          this.styleObj = parsedPosition
        }
      } catch (error) {
        console.log('dragAndDropPosition', error)
      }
    },
    resetDragAndDropPosition() {
      const boundaryElementRect = this.boundaryElement.getBoundingClientRect()
      this.boundaryElementRect = boundaryElementRect

      // 判断是否在窗口外，如果是，则重置位置
      const { left, top, right } = this.styleObj
      const width = 56
      const clientWidth = document.documentElement.clientWidth

      const invalidRightPosition =
        this.useRightPosition && parseInt(right) > clientWidth - boundaryElementRect.left - width
      const invalidLeftPosition =
        !this.useRightPosition &&
        (parseInt(left) < boundaryElementRect.left + width || parseInt(left) > clientWidth - width)
      const invalidTopPosition = parseInt(top) < boundaryElementRect.top

      if (invalidRightPosition || invalidLeftPosition || invalidTopPosition) {
        this.styleObj = { ...this.initialPosition }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.drag-and-drop {
  position: fixed;
  z-index: 3333;

  // 硬件加速
  transform: translateZ(0);

  &.can-drag {
    cursor: grab;
  }
}
</style>
