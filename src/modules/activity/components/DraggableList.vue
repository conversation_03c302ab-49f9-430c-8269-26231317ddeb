<template>
  <draggable
    v-model="dragList"
    v-bind="dragOptions"
    :disabled="disabled"
    :handle="handle ? '.handle' : ''"
    class="draggable-list"
    :class="{ disabled: disabled || handle }"
    @start="handleStart"
    @end="handleEnd"
    @change="handleChange"
  >
    <transition-group type="transition" :name="!dragging ? 'flip-list' : null">
      <div v-for="(item, index) in dragList" :key="getCurrKey(item, index)" class="list-group-item">
        <slot :item="item" :index="index" />
      </div>
    </transition-group>
  </draggable>
</template>

<script>
import draggable from 'vuedraggable'

export default {
  name: 'DraggableList',
  components: {
    draggable
  },
  props: {
    value: {
      type: Array,
      required: true
    },
    itemKey: {
      type: String,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    handle: {
      type: Boolean,
      default: false
    },
    getKey: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      dragOptions: {
        animation: 200,
        group: 'description',
        ghostClass: 'ghost'
      },
      dragging: false
    }
  },
  computed: {
    dragList: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    getCurrKey(item, index) {
      return this.getKey?.(item, index) ?? `${this.itemKey ? item[this.itemKey] : item}-${index}`
    },
    handleChange() {
      this.$emit('change')
    },
    handleStart() {
      this.dragging = true
      this.$emit('start')
    },
    handleEnd() {
      this.dragging = false
      this.$emit('end')
    }
  }
}
</script>

<style lang="scss">
.draggable-list {
  .ghost {
    opacity: 0.5;
    background: #c8ebfb;
  }
  .flip-list-move {
    transition: transform 0.5s;
  }
  .no-move {
    transition: transform 0s;
  }

  .handle,
  .list-group-item {
    cursor: move;
  }
  // .list-group-item i {
  //   cursor: pointer;
  // }

  &.disabled {
    .list-group-item {
      cursor: default;
    }
  }
}
</style>
