<!--
  <lazy-long-select
    v-model="item.from_station_code"
    :full-opts="stationOpts"
    :opt-label-key="'city_name'"
    :opt-value-key="'city_id'"
  />
  full-opts 是传入的长数据，但是表现出来下拉数据只有 100 条，
  通过搜索去查找 100 条以外的数据
-->
<template>
  <div class="lazy-long-select--container">
    <!-- 产品想增加一个反选功能 all -->
    <a-tree-select
      v-if="useTreeSelect"
      ref="treeSelect"
      v-model="treeValue"
      :tree-data="treeData"
      tree-checkable
      show-search
      :tree-node-filter-prop="optLabelKey"
      :placeholder="selectHint"
      :allow-clear="allowClear"
      :disabled="disabled"
      :replace-fields="replaceFields"
      :max-tag-count="maxTagCount"
      :tree-default-expand-all="true"
      :search-value.sync="searchValue"
      dropdown-class-name="tree-select___dropdown-class-name"
      @change="handleTreeChange"
    >
    </a-tree-select>
    <a-select
      v-else
      v-model="selectValue"
      v-bind="$attrs"
      :allow-clear="allowClear"
      show-search
      :mode="multiple ? 'multiple' : 'default'"
      :filter-option="filterOption"
      :disabled="disabled"
      :loading="loading"
      :placeholder="selectHint"
      :max-tag-count="maxTagCount"
      @search="remoteMethod"
      @change="emitChange"
      @dropdownVisibleChange="changeVisible"
    >
      <slot name="headerOptions" />
      <a-select-option
        v-for="(item, index) in headOpts"
        :key="`${item[optValueKey]}-${index}`"
        :value="item[optValueKey]"
        :disabled="Boolean(item.disabled)"
      >
        {{ typeof fmtLabelFn === 'function' ? fmtLabelFn(item) : item[optLabelKey] }}
      </a-select-option>
    </a-select>
  </div>
</template>

<script>
import Fuse from 'fuse.js'

export default {
  name: 'LazyLongSelect',
  install(Vue) {
    Vue.component(this.name, this)
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [String, Number, Array],
      default: ''
    },
    // 下拉框列表
    fullOpts: {
      type: Array,
      default: () => []
    },
    // 每一项的 opt 要指定的 value key
    optValueKey: {
      type: String,
      default: 'code'
    },
    // 每一项的 opt 要指定的 label key
    optLabelKey: {
      type: String,
      default: 'name'
    },

    selectHint: {
      type: String,
      default: __('global_select')
    },
    // 一次只显示 100 条数据
    opsLength: {
      type: Number,
      default: 100
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    fmtLabelFn: {
      type: Function,
      default: null
    },
    remote: {
      type: Boolean,
      default: true
    },
    allowClear: {
      type: Boolean,
      default: true
    },
    useTreeSelect: {
      type: Boolean,
      default: false
    },
    allValue: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      headOpts: [],
      visible: false,
      // 一次只显示 100 条数据
      // length: 100
      maxTagCount: 5,
      treeValue: [],
      searchValue: ''
    }
  },
  computed: {
    selectValue: {
      get() {
        if (this.value) {
          return this.value
        }
        return this.multiple ? [] : ''
      },
      set(v) {
        this.$emit('change', v)
      }
    },
    replaceFields() {
      const { optValueKey, optLabelKey } = this
      return {
        children: 'children',
        title: optLabelKey,
        value: optValueKey,
        key: optValueKey
      }
    },
    treeData() {
      const { optValueKey, optLabelKey, fullOpts, allValue } = this
      return [
        {
          [optLabelKey]: this.$t('global_button_all'),
          [optValueKey]: allValue,
          title: this.$t('global_button_all'),
          key: allValue,
          value: allValue,
          children: fullOpts
        }
      ]
    }
  },
  watch: {
    fullOpts: {
      immediate: true,
      deep: true,
      handler() {
        this.fuse = new Fuse(this.fullOpts, {
          tokenize: true,
          matchAllTokens: true,
          keys: [this.optValueKey, this.optLabelKey]
        })
        this.initHeadOpts()
      }
    },
    value: {
      deep: true,
      immediate: true,
      handler(v, oldV) {
        // 处理先赋 fullOpts 再赋 value 时，options为空的情况
        if ((oldV === '' && v) || !this.headOpts.length) {
          this.initHeadOpts()
        }

        if (this.useTreeSelect) {
          this.treeValue = v || []
        }
      }
    }
  },
  mounted() {
    this.bindInput()
  },
  methods: {
    bindInput() {
      if (this.useTreeSelect) {
        const input = this.getInput()
        input.addEventListener('input', this.handleInput)
      }
    },
    getInput() {
      const treeSelect = this.$refs.treeSelect.$el
      const input = treeSelect.querySelector('input')
      return input
    },
    handleInput(e) {
      const val = e.target.value || ''
      this.cacheSearchValue = val.trim()
    },
    handleTreeChange(v) {
      const { fullOpts, allValue } = this

      // 还原 search 历史记录
      this.searchValue = this.cacheSearchValue || ''

      if (v.length >= fullOpts.length) {
        this.$emit('change', [allValue])
        return
      }

      this.$emit('change', v)
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    changeVisible(v) {
      this.visible = v
    },
    initHeadOpts() {
      if (!this.remote || this.useTreeSelect) {
        this.headOpts = this.fullOpts
        return
      }
      this.headOpts = this.fullOpts
        .filter((item) => {
          let key = String(item[this.optValueKey])

          if (this.multiple) {
            return (this.selectValue && this.selectValue.includes(key)) || true
          }
          return key.includes(this.selectValue)
        })
        .slice(0, this.opsLength)
    },
    emitChange(value) {
      // reset options data
      if (!value) {
        this.remoteMethod('')
      }

      this.$emit('change', value)
      this.$emit('select-change', value)
    },
    remoteMethod: _.debounce(function(query) {
      if (!this.remote) {
        return
      }

      query = String(query)
      let result = []
      if (query.trim().length > 0) {
        result = this.fuse.search(query).slice(0, this.opsLength)
      }

      if (result.length <= 1) {
        result = _.uniqBy(
          [...result, ...this.fullOpts.slice(0, this.opsLength - result.length)],
          this.optValueKey
        )
      }

      this.headOpts = result
    }, 300),
    findCityName() {
      let data = this.fullOpts.find((item) => {
        return item.city_id === this.value
      })
      this.remoteMethod(data.city_name)
      // return data.city_name
    }
  }
}
</script>

<style lang="scss" scoped>
.lazy-long-select--container {
  position: relative;
  display: inline-block;
  .is-reverse {
    transform: translateY(-50%) rotate(180deg);
  }

  .break-spaces {
    white-space: break-spaces;
  }
}
</style>

<style>
.tree-select___dropdown-class-name {
  max-height: 50vh !important;
}
</style>
