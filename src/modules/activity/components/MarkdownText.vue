<template>
  <div v-if="!!text" class="markdown-text" v-html="transformedTips" />
</template>

<script>
import { parseMarkdown } from '@activity/utils'

export default {
  name: 'MarkdownText',
  props: {
    text: {
      type: String,
      default: ''
    },
    shouldParse: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    transformedTips() {
      let result
      if (this.shouldParse) {
        result = parseMarkdown(this.text)
      } else {
        result = this.text
      }

      return result.replace(/(<a.*?)>/g, '$1 target="_blank">')
    }
  }
}
</script>

<style lang="scss">
.markdown-text {
  font-size: 12px;
  line-height: 1.44;

  a {
    color: #2073f9;

    &:hover {
      text-decoration: underline;
    }
  }

  code {
    padding: 2px;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  li {
    margin: 0;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: bold;

    &:first-child {
      margin-top: 0;
    }
  }

  h5,
  h6 {
    margin-top: 8px;
    padding-bottom: 4px;
  }

  h1 {
    font-size: 32px;
    line-height: 1.31;
    margin-top: 12px;
    padding-bottom: 4px;

    &:before {
      content: none;
    }

    &:after {
      content: none;
    }
  }

  h2 {
    font-size: 24px;
    line-height: 1.33;
    padding-left: 0;
    margin-top: 12px;
    padding-bottom: 4px;

    &:before {
      content: none;
    }

    &:after {
      content: none;
    }
  }

  h3 {
    font-size: 20px;
    line-height: 1.4;
    margin-top: 12px;
    padding-bottom: 4px;

    &:before {
      content: none;
    }

    &:after {
      content: none;
    }
  }

  h4 {
    font-size: 18px;
    line-height: 1.38;
    margin-top: 8px;
    padding-bottom: 4px;
  }

  h5 {
    font-size: 16px;
  }

  h6 {
    font-size: 14px;
  }

  img {
    display: block;
    width: 100%;
    margin: 4px auto;
  }

  ul {
    // margin-left: 20px;
    // list-style-type: disc;
    list-style-type: none;

    li {
      position: relative;
      padding-left: 14px;
      list-style-type: none;

      &:before {
        position: absolute;
        content: '';
        left: 5px;
        top: 6px;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.65);
      }
    }
  }

  p {
    white-space: break-spaces;
  }
}
</style>
