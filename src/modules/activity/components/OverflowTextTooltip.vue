<template>
  <a-tooltip
    v-bind="$attrs"
    :visible="visible || (show && !!tooltip && switchIt)"
    :overlay-class-name="overlayClassName"
    :placement="placement"
  >
    <template slot="title">
      <slot name="title">
        {{ visible ? title : tooltip }}
      </slot>
    </template>
    <span
      ref="text"
      class="overflow-text"
      :class="{ 'multi-row': lineClamp > 1 }"
      :style="`--line-clamp: ${lineClamp};`"
      @mouseover="handleMouseOver($event)"
      @mouseleave="handleMouseLeave"
    >
      <slot />
    </span>
  </a-tooltip>
</template>

<script>
// 如果文字超出，则显示点点点，并且有tooltip
// 如果文字没有超出，则没有点点点，也没有tooltip
export default {
  name: 'OverflowTextTooltip',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    overlayClassName: {
      type: String,
      default: ''
    },
    placement: {
      type: String,
      default: 'top'
    },
    lineClamp: {
      type: Number,
      default: 1
    },
    switchIt: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    },
    delay: {
      type: Number,
      default: 30
    }
  },
  data() {
    return {
      show: false,
      tooltip: '',
      timer: null
    }
  },
  methods: {
    handleMouseOver(e) {
      // 可能有bug
      // scrollwidth property is incorrect (too low) when using text-overflow: ellipsis with box-sizing: border-box
      // https://bugzilla.mozilla.org/show_bug.cgi?id=1074543#c3
      // https://github.com/ElemeFE/element/blob/2830c0478331543ff51951cad349d66170fd0120/packages/table/src/table-body.js#L243
      // const domText = e.target
      // if (domText.scrollWidth > domText.offsetWidth) {
      //   this.tooltip = domText.innerText
      //   this.show = true
      // }

      const domText = this.$refs.text
      if (!domText) {
        return
      }

      if (
        (this.lineClamp > 1 && domText.scrollHeight > domText.offsetHeight) ||
        domText.scrollWidth > domText.offsetWidth
      ) {
        this.tooltip = domText.innerText

        this.clearTimer()
        this.timer = setTimeout(() => {
          this.show = true
          this.timer = null
        }, this.delay)
      }
    },
    handleMouseLeave() {
      this.show = false
      this.clearTimer()
    },
    clearTimer() {
      this.timer && clearTimeout(this.timer)
      this.timer = null
    }
  }
}
</script>
<style lang="scss" scoped>
.overflow-text {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  display: inline-block;
  max-width: 100%;

  &.multi-row {
    display: -webkit-box;
    white-space: inherit;
    -webkit-line-clamp: var(--line-clamp);
    -webkit-box-orient: vertical;
  }
}
</style>
