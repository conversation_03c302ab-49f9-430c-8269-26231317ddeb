<template>
  <div class="raw-table">
    <div class="raw-table-wrapper">
      <table class="raw-table-content" border="1" style="width: 1400px;">
        <slot />
      </table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RawTable'
}
</script>

<style lang="scss" scoped>
.raw-table {
  position: relative;
  flex: 1;
  width: 100%;
  max-width: 100%;
  overflow: hidden;

  &-wrapper {
    position: relative;
    width: 100%;
    overflow: hidden;
    overflow-x: auto;
  }

  &-content {
    font-size: 14px;
    font-weight: normal;
    border: 1px solid #e8e8e8;

    th {
      background-color: #fafafa;
    }

    th,
    td {
      padding: 12px 8px;
    }

    tr {
      &:hover td {
        background-color: #f5f7fa;
      }
    }
  }
}
</style>
