<template>
  <h3 class="section-header" :class="{ 'has-bar': bar }">
    <slot />
  </h3>
</template>

<script>
export default {
  name: 'SectionHeader',
  props: {
    bar: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style lang="scss" scoped>
.section-header {
  position: relative;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: #000;

  &.has-bar {
    font-size: 24px;
    line-height: 30px;
    padding-left: 16px;

    &:before {
      position: absolute;
      left: 0;
      top: 3px;
      content: '';
      height: 24px;
      width: 4px;
      background-color: #0091ff;
    }
  }
}
</style>
