<template>
  <div v-if="tips" class="strong-tips">
    <svg-icon icon-name="sound" class="strong-tips-icon" />
    <MarkdownText :text="tips" class="strong-tips-text" />
  </div>
</template>

<script>
import MarkdownText from '@activity/components/MarkdownText.vue'

export default {
  name: 'StrongTips',
  components: {
    MarkdownText
  },
  props: {
    tips: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.strong-tips {
  padding: 8px 8px 8px 30px;
  margin: 8px 0;
  position: relative;
  border: 1px solid #ffe1b6;
  border-radius: 8px;

  &-icon {
    position: absolute;
    left: 8px;
    top: 8px;
    width: 16px;
    height: 16px;
    color: #faad14;
  }

  &-text {
    line-height: 16px;
    font-size: 12px;
  }

  ::v-deep .markdown-text {
    img {
      max-width: 360px;
    }
  }
}
</style>
