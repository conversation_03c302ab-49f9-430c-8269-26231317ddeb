<template>
  <svg :class="svgClass" aria-hidden="true" v-on="$listeners">
    <use :xlink:href="name" />
  </svg>
</template>

<script>
export default {
  name: 'SvgIcon',
  props: {
    iconName: {
      // icon filename
      type: String,
      required: true
    },
    className: {
      // css class name
      type: String,
      default: ''
    }
  },
  computed: {
    name() {
      let icon = this.iconName
      return icon ? `#icon-${icon}` : ''
    },
    svgClass() {
      let className = this.className
      return className ? `svg-icon ${className}` : 'svg-icon'
    }
  }
}
</script>

<style>
.svg-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor; /* important */
  overflow: hidden;
}
</style>
