<template>
  <a-modal
    v-model="visible"
    title="Abnormal price set warning"
    :ok-text="'Confirm'"
    :cancel-text="'Go back to change'"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form-model layout="vertical" ref="ruleForm" :model="form" :rules="rules">
      <p>{{ warningMessage }}</p>
      <!-- Reason -->
      <a-form-model-item label="Reason for this setting" prop="reason">
        <a-select placeholder="Please select" v-model="form.reason">
          <a-select-option v-for="option in REASON_OPTIONS" :key="+option.value" :value="+option.value">
            {{ option.label }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <!-- Other Reason -->
      <a-form-model-item
        v-if="form.reason === +REASON_MAP.OTHERS"
        label="Please fill in the reason for 'Other'"
        prop="otherReason"
      >
        <a-textarea v-model="form.otherReason" placeholder="Please fill in the reason" rows="3" />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
import { REASON_OPTIONS, REASON_MAP, REASON_LABEL_MAP } from './constant'

export default {
  name: 'UnsavePriceReasonModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    warningMessage: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      form: {
        reason: '',
        otherReason: ''
      },
      REASON_OPTIONS,
      REASON_MAP,
      rules: {
        reason: [{ required: true, message: 'Reason cannot be empty', trigger: 'change' }],
        otherReason: [
          {
            required: false,
            message: 'Please fill in the reason for Other',
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (this.form.reason === REASON_MAP.OTHERS && !value) {
                return callback(new Error('Please fill in the reason for Other'))
              }
              callback()
            }
          }
        ]
      }
    }
  },

  methods: {
    async handleOk() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (!valid) {
          this.$message.error('Validation failed. Please check your inputs.')
          return
        }

        const { reason: reasonCode, otherReason = '' } = this.form || {}
        const reasonNote = otherReason || REASON_LABEL_MAP[reasonCode]

        const payload = {
          reasonCode: +reasonCode,
          reasonNote
        }
        this.$emit('confirm', payload)
        this.handleReset()
        this.$emit('update:visible', false)
      })
    },

    handleCancel() {
      this.$emit('update:visible', false)
      this.handleReset()
      this.$emit('cancel')
    },

    handleReset() {
      this.form = {
        reason: '',
        otherReason: ''
      }
    }
  }
}
</script>
