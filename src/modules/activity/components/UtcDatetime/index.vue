<template>
  <div class="utc-datetime">
    <div class="utc-datetime-box">
      <a-form-model-item prop="invalid_time_zone" class="timezone-wrap">
        <TimeZone v-model="model.invalid_time_zone" :disabled="disabled"></TimeZone>
      </a-form-model-item>
      <a-form-model-item prop="invalid_time_front" class="datetime-wrap">
        <a-date-picker
          :disabled="disabled"
          :disabled-date="disabledDate"
          format="YYYY-MM-DD HH:mm:ss"
          valueFormat="YYYY-MM-DD HH:mm:ss"
          style="width: 204px;"
          v-model="model.invalid_time_front"
          :showTime="getShowTime()"
          :placeholder="$t('30135')"
          :showToday="false"
        >
        </a-date-picker>
      </a-form-model-item>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import TimeZone from './time-zone.vue'
export default {
  props: {
    disabled: {
      type: Boolean,
      required: false,
      default: false
    },
    model: {
      type: Object,
      required: true,
      default() {
        return {
          invalid_time_zone: undefined,
          invalid_time_front: ''
        }
      }
    }
  },
  components: {
    TimeZone
  },
  data() {
    return {}
  },
  methods: {
    getShowTime() {
      return { defaultValue: moment('00:00:00', 'HH:mm:ss') }
    },
    disabledDate(current) {
      // Can not select days before today and today
      return (
        current &&
        (current < moment().endOf('day') ||
          current >
            moment()
              .add('days', 90 + 1)
              .startOf('day'))
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.utc-datetime {
  .utc-datetime-box {
    .timezone-wrap {
      display: inline-block;
      width: calc(100% - 220px);
      min-width: 80px;
    }
    .datetime-wrap {
      box-sizing: border-box;
      padding-left: 12px;
      display: inline-block;
      width: 220px;
    }
  }
}
</style>
