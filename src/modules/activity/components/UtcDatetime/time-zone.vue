<template>
  <a-select
    v-model="selectValue"
    name="DropDownTimezone"
    :placeholder="placeholder"
    :disabled="disabled"
    @change="emitChange"
  >
    <!-- https://gist.github.com/jonathanforsythe/1065260 -->
    <a-select-option v-for="item in utcList" :key="item.value">
      {{ item.label }}
    </a-select-option>
  </a-select>
</template>

<script>
const utcList = klook.getUtcList()
export default {
  name: 'time-zone',
  install(Vue) {
    // https://vuejs.org/v2/guide/plugins.html
    Vue.component(this.name, this)
  },
  model: {
    // https://vuejs.org/v2/guide/components.html#Form-Input-Components-using-Custom-Events
    prop: 'value', // this is actually bind to `this.time`
    event: 'change'
  },
  watch: {},
  props: {
    placeholder: {
      type: String,
      default: 'UTC'
    },
    value: [Number, String],
    disabled: {
      type: Boolean,
      default: false
    },
    fullTimezone: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      utcList
    }
  },
  methods: {
    emitChange(value) {
      this.$emit('time-zone-change', value)
    }
  },
  computed: {
    selectValue: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('change', v)
      }
    }
  },
  mounted() {}
}
</script>
