<template>
  <div class="optional-tools-wrap">
    <div class="optional-tools-head">{{ $t('79657') }}</div>
    <div
      v-for="item in options"
      :key="item.path"
      class="optional-tools-item"
      :class="{ disabled: disabled }"
      @click="handleGoRoute(item)"
    >
      <component :is="disabled ? 'a-tooltip' : 'div'" placement="topLeft">
        <div v-if="disabled" slot="title">{{ $t('79690') }}</div>
        {{ item.text }}
      </component>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    options: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  methods: {
    handleGoRoute(item) {
      if (this.disabled) {
        return
      }
      const {
        query,
        params: { id },
        path
      } = this.$route
      if (path.includes(item.path)) {
        return false
      }
      this.$router.replace({
        path: item.path + id,
        query: {
          ...query
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.optional-tools-wrap {
  background: #fff;
  border-top: 1px solid rgba(0, 0, 0, 0.09);
  .optional-tools-head {
    padding: 10px;
    font-weight: 600;
    font-size: 16px;
  }
  .optional-tools-item {
    padding: 20px 10px;
    font-weight: 400;
    font-size: 16px;
    color: #437dff;
    cursor: pointer;
    &.disabled {
      color: rgba(0, 0, 0, 0.11);
    }
  }
}
</style>
