// https://klook.larksuite.com/sheets/shtusmwfM48znctmlpMWEHrURTd
import SuspendBtn from '@activity/components/suspendBtn/index'

export default {
  components: {
    SuspendBtn
  },
  computed: {
    // -1   无效
    // 0    未发布
    // 1    已发布
    // 2    草稿
    // 3    content to edit
    // 4    in preview
    // 5    暂停销售
    actStatus() {
      return this.$store.state.actItemData?.activity_status ?? -1
    },
    actIsSuspended() {
      return this.actStatus === 5
    },
    includeSuspendActStatus() {
      // 5 表示支持预热/暂停销售
      return this.getActBtnDisplayStatus(5)
    }
  },
  methods: {
    getActBtnDisplayStatus(status) {
      const status_list = this.categoryInfo?.activity_next_status?.status_list ?? []

      return status_list.includes(status)
    },
    getActBtnDisabledTooltip(btn) {
      if (this.actIsSuspended && btn.key === 'publish_activity') {
        return this.$t('80297')
      }

      return ''
    }
  }
}
