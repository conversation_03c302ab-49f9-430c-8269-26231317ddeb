<template>
  <div id="act-info-editable" class="pkg-info">
    <div v-show="!categoryInfo.leaf_category_id" class="common-spin-style">
      <a-spin />
    </div>
    <rejectInfoAlert
      v-if="categoryInfo.reject_reason && categoryInfo.reject_reason.reject_reason_text"
      :reject-info="categoryInfo.reject_reason"
    />
    <div v-show="categoryInfo.leaf_category_id" class="pkg-info-box">
      <div class="flex-box act-info">
        <div class="title-box">
          <div class="block-box">
            <p class="text-box">{{ $t('activity_id_title') }}:</p>
            <p class="value-box">
              <span class="title">{{ calcTitle }}</span>
            </p>
          </div>
        </div>
        <div class="other-box">
          <div class="block-box">
            <p class="text-box">{{ $t('price_engine_sub-category') }}:</p>
            <p class="value-box">
              <span class="sub-category">{{ categoryInfo.sub_category_name }}</span>
            </p>
          </div>
          <div class="block-box">
            <p class="text-box">{{ $t('global_activity_status') }}:</p>
            <p class="value-box">
              <span
                :class="[
                  'common-status-style',
                  platformFn('platform_activity_status', categoryInfo.activity_status, 'class')
                ]"
              >
                {{ platformFn('platform_activity_status', categoryInfo.activity_status) }}
              </span>
              <a-icon
                v-if="actIsSuspended"
                v-tooltip="{
                  visible: true,
                  placement: 'bottomRight',
                  content: $t('79636')
                }"
                type="info-circle"
                style="margin-left: 6px"
              />
            </p>
          </div>
          <div class="block-box">
            <p class="text-box">{{ $t('actCreate.leafCategory') }}:</p>
            <p class="value-box">
              <span
                class="leaf-category cs-pointer"
                :data-spm-module="getActleafcatSpm"
                data-spm-virtual-item="__virtual"
                @click="clickLeafCategoryBtn"
              >
                {{ categoryInfo.leaf_category_name }}
                <a-icon type="down" />
              </span>
            </p>
          </div>
          <div v-if="$store.state.isMC2BD" class="block-box">
            <p class="text-box">{{ $t('merchant_approve_status') }}:</p>
            <p class="value-box">
              <span
                :class="[
                  'common-status-style',
                  mapsFilterFn('approval_status', categoryInfo.approval_status, 'class')
                ]"
              >
                {{ mapsFilterFn('approval_status', categoryInfo.approval_status) }}
              </span>
            </p>
          </div>
        </div>
      </div>
      <div class="flex-box submit-box">
        <template v-if="$root.isMerchant">
          <span v-auth="'actSubmit'">
            <a-button v-if="isSubmit" type="primary" :disabled="!isSubmit" @click="clickSubmitActivity">{{
              $t('merchant_submit_activity_action')
            }}</a-button>
          </span>
          <span v-auth="'actWithdraw'">
            <a-button
              v-if="categoryInfo.approval_status === 1"
              @click="handleWithdrawSubmit(categoryInfo, 'activity')"
            >
              {{ $t('btn.withdrawSubmit') }}
            </a-button>
          </span>
          <div
            v-if="categoryInfo.approval_status === 1"
            :title="$t('21837')"
            class="padding-approve-wrap common-two-line break-word"
          >
            {{ $t('21837') }}
          </div>
        </template>
        <div v-else-if="$store.state.isMC2BD && categoryInfo.approval_status === 1">
          <span v-auth="'actApprove'" style="margin-right: 12px">
            <a-button type="primary" @click="handleApprove">{{ $t('approve') }} </a-button>
          </span>
          <span v-auth="'actReject'">
            <a-button @click="handleReject">{{ $t('reject') }}</a-button>
          </span>
        </div>
        <template v-else-if="!$store.state.isMC2BD">
          <span v-for="btn in adminObj.actBtns" :key="'act' + btn.key" class="admin-btn-style">
            <a-tooltip placement="right" overlay-class-name="common-tooltip-style">
              <span
                v-if="categoryInfo.activity_next_status && !categoryInfo.activity_next_status.editable"
                slot="title"
                v-html="btn.authTip"
              ></span>
              <span v-if="getActBtnDisplayStatus(btn.status) || !!getActBtnDisabledTooltip(btn)">
                <a-button
                  v-bind="btn"
                  :disabled="!!getActBtnDisabledTooltip(btn)"
                  @click="adminObj.updateActStatus(btn)"
                >
                  {{ btn.text }}
                </a-button>
                <a-icon
                  v-if="!!getActBtnDisabledTooltip(btn)"
                  v-tooltip="{
                    visible: true,
                    placement: 'bottomRight',
                    content: getActBtnDisabledTooltip(btn)
                  }"
                  style="margin-left: 6px; cursor: pointer"
                  type="info-circle"
                />
              </span>
            </a-tooltip>
          </span>

          <SuspendBtn
            type="act"
            :btn-tooltip-msg="$t('79633')"
            :display-act-btn="includeSuspendActStatus"
            :display-pkg-btn="false"
            @submit="refreshPage"
          />
        </template>

        <a-button
          v-if="$store.state.isMC2BD"
          :data-spm-module="getApprovalSpm"
          data-spm-virtual-item="__virtual"
          type="link"
          @click="handleGoToApproveList"
        >
          {{ $t('48224') }}
        </a-button>
      </div>
    </div>
    <createModal :categoryInfo="categoryInfo" :modal-data="modalData" />
    <stkModal :modal-data="stkData" />

    <approveList
      v-if="approveList.visible"
      v-model="approveList.visible"
      style="z-index: 999"
      v-bind="approveList"
    />
    <div id="submit_activity_spm" v-bind="$root.bindIHTrack({ type: 'module', spm: 'SubmitActivity' })"></div>
  </div>
</template>

<script>
import createModal from '@activity/pages/activityManagement/create/modal'
import stkModal from '@activity/components/modalAntd/stkModal'
import handleLib from '@activity/pages/activityManagement/handleLib'
import { isMerchant } from '@/env'
import maps from '@activity/utils/maps.js'
import ApproveList from '../../pages/activityManagement/approveList/index'
import actSuspendMixin from '@activity/components/actInfo/actSuspendMixin'
import approveDraftMerchantActMixin from '@activity/components/actInfo/mixins/approveDraftMerchantActMixin.js'
import publishDraftMerchantActMixin from '@activity/components/actInfo/mixins/publishDraftMerchantActMixin.js'
import rejectInfoAlert from '@activity/components/reject-info-alert/index.vue'

export default {
  name: 'ActInfo',
  components: { ApproveList, createModal, stkModal, rejectInfoAlert },
  mixins: [actSuspendMixin, approveDraftMerchantActMixin, publishDraftMerchantActMixin],
  inject: ['refreshPage'],
  props: {
    categoryInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      approveList: {
        visible: false,
        data: {},
        title: '',
        autoFillData: undefined
      },

      adminObj: {
        actBtns: maps.act_btns2admin,
        updateActStatus: (btn) => {
          let { operation_reason, status, status_list } = this.categoryInfo.activity_next_status
          // 0: 'Unpublished',
          // 1: 'Published',
          // 2: 'Draft',
          // 3: 'EM to edit',
          // 4: 'Preview'
          btn.activity_next_status = status
          btn.status_list = status_list

          let data = {
            activity_id: this.activity_id, // ,
            language: klook.getEditLang(), // draft == > em to edit状态时，需要该字段用于检查数据状态，其他状态不需要,
            page_from: klook.getPlatformRoleKey(), // ,
            status: btn.status,
            reason: '',
            reason_code: undefined
          }

          let reasons = operation_reason ? operation_reason.reasons : []

          handleLib.methods.handlerUpdateStatus.call(this, btn, {
            isAct: true,
            data,
            reasons,
            item: this.categoryInfo,
            suc: async (response) => {
              const flag = await this.handlePublishDraftMerchantActSuccessCb(response)
              if (flag) return

              klook.bus.$emit('initQueryData2bus')
              if (btn.key === 'submit_to_em') {
                this.$router.push({
                  path: `/act/activity/seo/${this.activity_id}`,
                  query: this.$route.query
                })
              }
            },
            err: (err) => {
              if (data.status === 1) {
                this.$root.trackIHEvent('#submit_activity_spm', {
                  err_message: err?.error?.message ?? 'SubmitActivity Error'
                })
              }
            }
          })
        }
      },
      isMerchant,
      infoData: {
        title: ''
      },
      modalData: {
        visible: false
      },
      stkData: {
        form: {
          note: '', // 原因,
          package_id_list: [
            // 活动和套餐必须都传，并且套餐属于活动,
          ]
        },
        row: {},
        keyFlag: 'activity',
        visible: false,
        packageArr: [],
        on: {
          ok: async () => {
            let { stkData } = this
            // console.log(999, this.stkData.form, stkData.row)
            let result = await this.submit_to_klook2activity({
              activity_id: stkData.row.activity_id,
              ...stkData.form
            })
            stkData.visible = false
            if (result) {
              this.$set(this.categoryInfo, 'approval_status', 1)
              this.refreshPage()
            }
            stkData.form.note = ''
            stkData.form.package_id_list = []
          }
        }
      }
    }
  },
  computed: {
    calcTitle() {
      let { categoryInfo } = this
      let title = `${categoryInfo.activity_id || ''}-${categoryInfo.activity_title || ''}`
      this.$emit('calcTitle', title)
      return title
    },
    isSubmit() {
      let { categoryInfo } = this
      return (
        categoryInfo.activity_status !== 1 &&
        categoryInfo.package_count &&
        categoryInfo.approval_status !== 1 &&
        categoryInfo.approval_status !== 3
      )
    },
    activity_id() {
      return klook.parse2id(this.$route.params.id)
    },
    getActleafcatSpm() {
      const oid = `activity_${this.activityId || 0}`
      return `LeafCategoryChange?oid=${oid}&trg=manual`
    },
    getApprovalSpm() {
      const oid = `activity_${this.activityId || 0}`
      return `ApprovalList?oid=${oid}&trg=manual`
    }
  },
  mounted() {
    klook.bus
      .$off('updateActStatusByEventBus')
      .$on('updateActStatusByEventBus', this.adminObj.updateActStatus)
  },
  beforeDestroy() {
    klook.bus.$off('updateActStatusByEventBus', this.adminObj.updateActStatus)
  },
  methods: {
    platformFn: maps.platformFn,
    handleGoToApproveList() {
      this.$set(this, 'approveList', {
        visible: true,
        data: {
          activity_id: this.activity_id
        },
        title: __('48224'),
        autoFillData: {}
      })
    },
    async handleApprove() {
      const confirm = await this.preApproveDraftMerActConfirm()
      if (!confirm) return

      handleLib.methods.handleApprove.call(this, this.categoryInfo, {
        isAct: true,
        suc: () => {
          this.$set(this.categoryInfo, 'approval_status', 3)
          klook.bus.$emit('initQueryData2bus')
        }
      })
    },
    handleReject() {
      handleLib.methods.handleReject.call(this, this.categoryInfo, {
        isAct: true,
        suc: () => {
          this.$set(this.categoryInfo, 'approval_status', 2)
          klook.bus.$emit('initQueryData2bus')
        }
      })
    },
    async clickSubmitActivity() {
      let { activity_id } = this
      let result = await handleLib.methods.get_activity_submit_status(activity_id)
      if (result) {
        if (result.result?.status) {
          let obj = await ajax.get(
            {
              url: ADMIN_API.act.get_activity_to_submit_packages,
              params: { activity_id }
            },
            {
              loading: true
            }
          )
          if (obj && (obj.submited_package || obj.to_be_submit_packages)) {
            let arr = []
            if (obj.submited_package) {
              obj.submited_package.forEach((o) => (o.isFlag = true))
              arr.push(...obj.submited_package)
            }
            if (obj.to_be_submit_packages) {
              obj.to_be_submit_packages.length === 1 &&
                (this.stkData.form.package_id_list = obj.to_be_submit_packages.map((o) => o.package_id))
              arr.push(...obj.to_be_submit_packages)
            }
            this.stkData.keyFlag = 'activity'
            this.stkData.packageArr = arr
            this.stkData.visible = true
            this.stkData.row = this.categoryInfo
          }
        } else {
          this.$modal.error({
            title: this.$t('merchant_submit_activity_action'),
            content: this.$t('activity_required_fields')
          })
        }
      }
    },
    async submit_to_klook2activity(data) {
      return await ajax.post(
        ADMIN_API.act.submit_to_klook,
        {
          data
          // data: {
          //   activity_id,
          //   note: '', // 原因,
          //   package_id_list: [
          //     // 活动和套餐必须都传，并且套餐属于活动,
          //   ]
          // },
        },
        ajax.sucOptions
      )
    },
    mapsFilterFn: maps.filterFn,
    clickLeafCategoryBtn() {
      if (this.$root.superEditLock) return
      this.modalData.visible = !this.modalData.visible
    },
    async handleWithdrawSubmit(row, keyFlag) {
      handleLib.methods.handleWithdrawSubmit.call(this, row, {
        keyFlag,
        suc: () => {
          this.$set(this.categoryInfo, 'approval_status', 0)
          this.refreshPage()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
