<template>
  <div id="act-info-editable" class="pkg-info">
    <div v-show="!categoryInfo.leaf_category_id" class="common-spin-style">
      <a-spin />
    </div>
    <rejectInfoAlert
      v-if="categoryInfo.reject_reason && categoryInfo.reject_reason.reject_reason_text"
      :reject-info="categoryInfo.reject_reason"
    />
    <div v-show="categoryInfo.leaf_category_id" class="pkg-info-box">
      <div class="flex-box act-info">
        <div class="title-box">
          <span class="title">{{ calcTitle }}</span>
        </div>
        <div class="other-box">
          <div class="block-box">
            <p class="text-box">{{ $t('price_engine_sub-category') }}:</p>
            <p class="value-box">
              <span class="sub-category">{{ categoryInfo.sub_category_name }}</span>
            </p>
          </div>
          <div class="block-box">
            <p class="text-box">{{ $t('actCreate.leafCategory') }}:</p>
            <p class="value-box">
              <span
                class="leaf-category cs-pointer"
                :data-spm-module="getActleafcatSpm"
                data-spm-virtual-item="__virtual"
                @click="clickLeafCategoryBtn"
              >
                <span>{{ categoryInfo.leaf_category_name }}</span>
                <a-icon type="down" />
              </span>
            </p>
          </div>
          <div class="block-box">
            <p class="text-box">{{ $t('global_activity_status') }}:</p>
            <p class="value-box">
              <span
                :class="[
                  'common-merchant-status-style',
                  platformFn('platform_activity_status', categoryInfo.activity_status, 'class')
                ]"
              >
                {{ platformFn('platform_activity_status', categoryInfo.activity_status) }}
              </span>
              <a-icon
                v-if="actIsSuspended"
                v-tooltip="{
                  visible: true,
                  placement: 'bottomRight',
                  content: $t('79636')
                }"
                type="info-circle"
                style="margin-left: 6px"
              />
            </p>
          </div>
          <div v-if="$store.state.isMC2BD" class="block-box">
            <p class="text-box">{{ $t('merchant_approve_status') }}:</p>
            <p class="value-box">
              <span
                :class="[
                  'common-merchant-status-style',
                  mapsFilterFn('approval_status', categoryInfo.approval_status, 'class')
                ]"
              >
                {{ mapsFilterFn('approval_status', categoryInfo.approval_status) }}
              </span>
              <span
                v-if="isMerchant"
                class="common-btn-link"
                :style="{ marginLeft: '8px' }"
                v-bind="$root.bindIHTrack({ type: 'module', spm: 'Approval_List' })"
                @click="handleGoToApproveList"
              >
                <span class="btn-text">
                  <a-tooltip :title="$t('48224')">
                    <a-icon type="file-text"></a-icon>
                  </a-tooltip>
                </span>
              </span>
            </p>
          </div>
        </div>
      </div>
      <div class="flex-box submit-box">
        <div>
          <span
            v-if="!lockMerchantActEditing"
            v-bind="$root.bindIHTrack({ type: 'module', spm: 'Bulk_Delete' })"
            class="common-btn-link danger"
            @click="toggleDelete"
          >
            <a-icon class="btn-icon-left" type="delete" />
            <span class="btn-text">{{ $t('80919') }}</span>
          </span>
        </div>
        <div class="bottom-btns-box">
          <span
            v-if="!hideActSubmit"
            id="js-activity-submit"
            v-auth="'actSubmit'"
            class="common-btn-link"
            @click="clickSubmitActivity"
          >
            <a-button type="primary">
              {{ $t('merchant_submit_activity_action') }}
            </a-button>
          </span>
          <span v-for="btn in adminObj.actBtns" :key="'act' + btn.key" class="common-btn-link">
            <a-button
              v-if="getActBtnDisplayStatus(btn.status)"
              type="primary"
              :disabled="!!getActBtnDisabledTooltip(btn)"
              @click="adminObj.updateActStatus(btn)"
            >
              {{ btn.text }}
            </a-button>
          </span>
          <span
            v-if="categoryInfo.approval_status === 1"
            v-auth="'actWithdraw'"
            class="common-btn-link"
            @click="handleWithdrawSubmit(categoryInfo, 'activity')"
          >
            <span class="btn-text">{{ $t('btn.withdrawSubmit') }}</span>
          </span>
        </div>
      </div>
    </div>
    <createModal :categoryInfo="categoryInfo" :modal-data="modalData" />
    <stkModal :modal-data="stkData" />

    <approveList
      v-if="approveList.visible"
      v-model="approveList.visible"
      style="z-index: 999"
      v-bind="approveList"
    />

    <DeleteScheduleDrawer v-if="createDelete" :visible="showDelete" @cancel="showDelete = false" />

    <div id="submit_activity_spm" v-bind="$root.bindIHTrack({ type: 'module', spm: 'SubmitActivity' })"></div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import createModal from '@activity/pages/activityManagement/create/modal'
import stkModal from '@activity/components/modalAntd/stkModal'
import handleLib from '@activity/pages/activityManagement/handleLib'
import { isMerchant } from '@/env'
import maps from '@activity/utils/maps.js'
import { bestMatchLang } from '@activity/utils'
import ApproveList from '../../pages/activityManagement/approveList/index'
import actSuspendMixin from '@activity/components/actInfo/actSuspendMixin'
import DeleteScheduleDrawer from '@activity/pages/package/units/new-components/delete-schedule-drawer'
import { EVENT_BUS_KEY } from '@activity/utils/const.js'
import rejectInfoAlert from '@activity/components/reject-info-alert/index.vue'

export default {
  name: 'MerchantActInfo',
  components: { ApproveList, createModal, stkModal, DeleteScheduleDrawer, rejectInfoAlert },
  mixins: [actSuspendMixin],
  props: {
    categoryInfo: {
      type: Object,
      default: () => ({})
    }
  },
  provide() {
    return {
      activityId: this.activity_id
    }
  },
  inject: ['refreshPage'],
  computed: {
    ...mapState(['lockMerchantActEditing']),
    ...mapGetters({
      getPkgInfos2getters: 'getPkgInfos2getters'
    }),
    allPackages() {
      return this.getPkgInfos2getters?.packages || []
    },
    calcTitle() {
      let { categoryInfo } = this
      let title = `${categoryInfo.activity_id || ''}-${categoryInfo.activity_title || ''}`
      this.$emit('calcTitle', title)
      return title
    },
    hideActSubmit() {
      let { categoryInfo } = this
      return categoryInfo.activity_status === 1 || [1, 3].includes(categoryInfo.approval_status)
    },
    isSubmit() {
      let { categoryInfo } = this
      return (
        categoryInfo.activity_status !== 1 &&
        categoryInfo.package_count &&
        categoryInfo.approval_status !== 1 &&
        categoryInfo.approval_status !== 3
      )
    },
    activity_id() {
      return klook.parse2id(this.$route.params.id)
    },
    getActleafcatSpm() {
      const oid = `activity_${this.activityId || 0}`
      return `LeafCategoryChange?oid=${oid}&trg=manual`
    }
  },
  data() {
    return {
      showDelete: false,
      createDelete: false,
      approveList: {
        visible: false,
        data: {},
        title: '',
        autoFillData: undefined
      },

      adminObj: {
        actBtns: maps.act_btns2admin,
        updateActStatus: (btn) => {
          let { operation_reason, status, status_list } = this.categoryInfo.activity_next_status
          // 0: 'Unpublished',
          // 1: 'Published',
          // 2: 'Draft',
          // 3: 'EM to edit',
          // 4: 'Preview'
          btn.activity_next_status = status
          btn.status_list = status_list

          let data = {
            activity_id: this.activity_id, // ,
            language: klook.getEditLang(), // draft == > em to edit状态时，需要该字段用于检查数据状态，其他状态不需要,
            page_from: klook.getPlatformRoleKey(), // ,
            status: btn.status,
            reason: '',
            reason_code: undefined
          }

          let reasons = operation_reason ? operation_reason.reasons : []

          handleLib.methods.handlerUpdateStatus.call(this, btn, {
            isAct: true,
            data,
            reasons,
            item: this.categoryInfo,
            suc: () => {
              klook.bus.$emit('initQueryData2bus')
              if (btn.key === 'submit_to_em') {
                this.$router.push({
                  path: `/act/activity/seo/${this.activity_id}`,
                  query: this.$route.query
                })
              }
            }
          })
        }
      },
      isMerchant,
      infoData: {
        title: ''
      },
      modalData: {
        visible: false
      },
      stkData: {
        form: {
          note: '', // 原因,
          package_id_list: [
            // 活动和套餐必须都传，并且套餐属于活动,
          ]
        },
        row: {},
        keyFlag: 'activity',
        visible: false,
        packageArr: [],
        on: {
          ok: async () => {
            let { stkData } = this
            // console.log(999, this.stkData.form, stkData.row)
            let res = await this.submit_to_klook2activity({
              activity_id: stkData.row.activity_id,
              ...stkData.form
            })
            const extObj = {
              err_message: res.success ? '' : res?.error?.message || 'Error: false'
            }
            this.$root.trackIHEvent('#submit_activity_spm', extObj)
            stkData.visible = false
            if (res?.success) {
              this.$set(this.categoryInfo, 'approval_status', 1)
              // this.refreshPage()
              // klook.bus.$emit('initQueryData2bus')
            }
            stkData.form.note = ''
            stkData.form.package_id_list = []
          }
        }
      }
    }
  },
  mounted() {
    klook.bus
      .$off('updateActStatusByEventBus')
      .$on('updateActStatusByEventBus', this.adminObj.updateActStatus)
  },
  beforeDestroy() {
    klook.bus.$off('updateActStatusByEventBus', this.adminObj.updateActStatus)
  },
  methods: {
    toggleDelete() {
      this.showDelete = true
      this.createDelete = true
    },
    platformFn: maps.platformFn,
    handleGoToApproveList() {
      this.$set(this, 'approveList', {
        visible: true,
        data: {
          activity_id: this.activity_id
        },
        title: __('48224'),
        autoFillData: {}
      })
    },
    handleApprove() {
      handleLib.methods.handleApprove.call(this, this.categoryInfo, {
        isAct: true,
        suc: () => {
          this.$set(this.categoryInfo, 'approval_status', 3)
          klook.bus.$emit('initQueryData2bus')
        }
      })
    },
    handleReject() {
      handleLib.methods.handleReject.call(this, this.categoryInfo, {
        isAct: true,
        suc: () => {
          this.$set(this.categoryInfo, 'approval_status', 2)
          klook.bus.$emit('initQueryData2bus')
        }
      })
    },
    async clickSubmitActivity() {
      klook.bus.$emit(EVENT_BUS_KEY.closeSubmitDriver)

      let { activity_id } = this
      let res = await handleLib.methods.get_activity_submit_status(activity_id)
      const result = res?.result

      if (result) {
        if (result.status) {
          let obj = await ajax.get(
            {
              url: ADMIN_API.act.get_activity_to_submit_packages,
              params: { activity_id }
            },
            {
              loading: true
            }
          )
          if (obj && (obj.submited_package || obj.to_be_submit_packages)) {
            let arr = []
            if (obj.submited_package) {
              obj.submited_package.forEach((o) => (o.isFlag = true))
              arr.push(...obj.submited_package)
            }
            if (obj.to_be_submit_packages) {
              this.stkData.form.package_id_list = obj.to_be_submit_packages.map((o) => o.package_id)
              arr.push(...obj.to_be_submit_packages)
            }

            this.allPackages.forEach((pkg) => {
              if (!arr.find((item) => item.package_id === pkg.package_id)) {
                arr.push({
                  ...pkg,
                  package_title: bestMatchLang('name', 'language_type', pkg.package_local),
                  uncompleted: true
                })
              }
            })

            this.stkData.keyFlag = 'activity'
            this.stkData.packageArr = arr
            this.stkData.visible = true
            this.stkData.row = this.categoryInfo
          }
        } else {
          this.$modal.error({
            title: this.$t('merchant_submit_activity_action'),
            content: result.hint || this.$t('activity_required_fields')
          })
          this.$root.trackIHEvent('#submit_activity_spm', {
            err_message: result.hint || this.$t('activity_required_fields')
          })
        }
      } else {
        this.$root.trackIHEvent('#submit_activity_spm', {
          err_message: res?.error?.message || 'SubmitActivity Error'
        })
      }
    },
    async submit_to_klook2activity(data) {
      return await ajax.postBody(
        ADMIN_API.act.submit_to_klook,
        {
          data,
          noDefaultResponseInterceptor: true
        },
        ajax.sucOptions
      )
    },
    mapsFilterFn: maps.filterFn,
    clickLeafCategoryBtn() {
      if (this.$root.superEditLock) return
      this.modalData.visible = !this.modalData.visible
    },
    async handleWithdrawSubmit(row, keyFlag) {
      handleLib.methods.handleWithdrawSubmit.call(this, row, {
        keyFlag,
        suc: () => {
          this.$set(this.categoryInfo, 'approval_status', 0)
          this.refreshPage()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import './merchant-index.scss';
</style>
