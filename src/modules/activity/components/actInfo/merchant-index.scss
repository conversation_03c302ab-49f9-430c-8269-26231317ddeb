// actInfo.vue | pkgInfo.vue
.pkg-info {
  position: relative;
  min-height: 100px;
  .pkg-info-box {
    position: relative;
    background-color: #fff;
    padding: 12px 20px 12px 20px;
    display: flex;
    justify-content: space-between;
    .flex-box {
      &.title-box {
        flex: 1 1 auto;
      }
      &.other-box {
        flex: 0 1 auto;
      }
      &.submit-box {
        flex: 0 1 auto;
        width: 313px;
        padding-left: 20px;
        text-align: right;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    }
    .padding-approve-wrap {
      position: absolute;
      z-index: 1;
      bottom: 14px;
      right: 20px;
      height: 42px;
      color: rgba(255, 171, 0, 0.85);
      font-size: 12px;
      line-height: 14px;
      max-width: 260px;
      -webkit-line-clamp: 3;
      text-align: left;
    }
    .other-box {
      // display: flex;
      // align-items: center;
      font-size: 0;
    }
    .title-box {
      margin-bottom: 12px;
      .title {
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        color: #000;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 5;
        -webkit-box-orient: vertical;
      }
    }
    .block-box {
      font-size: 12px;
      display: inline-block;
      width: auto;
      margin-right: 72px;
      box-sizing: border-box;
      vertical-align: top;
      .text-box {
        line-height: 14px;
        padding-bottom: 4px;
      }
      .value-box {
        position: relative;
        font-size: 14px;
        color: #000;
        line-height: 22px;
        height: 22px;
      }
      p {
        margin: 0;
      }
      .leaf-category {
        i {
          margin-left: 8px;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.45);
        }
      }
    }
  }
}

.act-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.admin-btn-style {
  display: inline-block;
  margin-bottom: 12px;
  button {
    min-width: 152px;
    text-align: left;
  }
}

.common-btn-link {
  color: #437dff;
  cursor: pointer;
  font-size: 14px;
  line-height: 22px;
  .btn-icon-left {
    margin-right: 8px;
  }
  &.danger {
    color: #ff4d4f;
  }
}
.bottom-btns-box {
  .common-btn-link:not(:last-of-type) {
    margin-right: 24px;
  }
}
