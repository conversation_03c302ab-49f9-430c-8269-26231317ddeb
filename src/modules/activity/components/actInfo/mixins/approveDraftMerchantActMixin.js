import { pmsConfirm } from '@activity/utils/index'

export default {
  data() {
    return {
      nonFirstApprovalSubmission: false
    }
  },
  computed: {
    actItemData() {
      return this.$store.state.actItemData || {}
    }
  },
  methods: {
    // 审核草稿商户活动
    async preApproveDraftMerActConfirm() {
      if (this.$root.isAdmin) {
        const merchantInfoList = this.actItemData.merchant_info_list || []

        if (
          merchantInfoList.length &&
          !merchantInfoList.some((merchant) => merchant.status === 1) && // 0-草稿；1-已审核通过
          !this.nonFirstApprovalSubmission
        ) {
          // 第一次提交审核需提示
          this.nonFirstApprovalSubmission = true
          const flag = await pmsConfirm.call(this, {
            content: this.$t('120322'),
            okText: this.$t('120375'),
            cancelText: this.$t('120376')
          })

          if (flag) {
            const merchantInfo = merchantInfoList[0] || {}
            window.open(
              `${location.origin}/mspa/fulfillment/merchant/leads_management/accpet_leads?application_id=${merchantInfo.application_id}&auto_open_details=true`,
              '_blank'
            )
          }

          return Promise.resolve(false)
        }
      }

      return Promise.resolve(true)
    }
  }
}
