import { pmsConfirm } from '@activity/utils/index'

export default {
  methods: {
    async handlePublishDraftMerchantActSuccessCb(response) {
      if (response?.result?.code === 'P10008') {
        const title = this.$t('73574')
        const content = this.$t('120323')

        const flag = await pmsConfirm.call(this, {
          title,
          content,
          closable: true,
          cancelText: this.$t('120376'),
          okText: this.$t('120375'),
          cancelButtonProps: {
            style: {
              display: 'none'
            }
          }
        })

        if (flag) {
          const link = '/mspa/fulfillment/merchant/leads_management/accpet_leads'
          window.open(link, '_blank')

          return Promise.resolve(true)
        }
      }

      return Promise.resolve(false)
    }
  }
}
