<template>
  <div class="act-nav">
    <div class="act-nav-box">
      <a-anchor :offset-top="offsetTop" class="common-anchor-style hide-all">
        <a-collapse v-model="activeKeys" class="common-collapse-style" :bordered="false">
          <a-collapse-panel
            key="1"
            style="box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.09)"
            :header="$t('global_activity')"
            :show-arrow="false"
          >
            <ul class="menu-box menu-act">
              <li
                v-for="(item, i) in actObj.list"
                :key="item.key"
                :class="[
                  $route.path.includes(item.path) ? 'on' : '',
                  calcNavLock(i) || calcAuthLock(item) ? 'lock' : ''
                ]"
                @click="item.isLock || handleGoRoute(item, i)"
              >
                <!-- overlay-class-name="common-tooltip-style" -->
                <a-tooltip placement="right" :title="getTooltipTitle(item.title, i)">
                  <div>
                    <i v-if="item.require" class="common-required-star require">*</i>
                    <span class="text">{{ item.text }}</span>
                    <i v-if="item.hideFinish !== true" :class="['right-icon', item.finish ? 'finish' : '']">
                      <a-icon type="check-circle" theme="filled" />
                    </i>
                  </div>
                </a-tooltip>
              </li>
            </ul>
          </a-collapse-panel>
          <a-collapse-panel key="2" class="nav-pkg-box" :style="calcPkgStyle" :show-arrow="false">
            <template slot="header">
              <div class="header-box" @click="clickPkgHeader">
                <span class="field-text">{{ $t('global_package') }} </span>
                <div v-if="calcShowAction" class="action-box" @click.prevent.stop>
                  <a-input-search
                    v-model="pkgObj.searchVal"
                    class="search-box common-search-style"
                    :placeholder="$t('global_button_search')"
                    allow-clear
                  />
                  <span
                    v-if="calcShowAddBtn && !this.$root.isSPU"
                    class="btn-add-action common-ellipsis-style"
                    @click="clickAddPackage"
                  >
                    <a-icon type="plus" />{{ $t('global_add') }}
                  </span>
                </div>
              </div>
            </template>
            <a-collapse
              v-model="activePkgIds"
              class="common-collapse-style"
              :bordered="false"
              @change="handleChangeActive"
            >
              <div class="pkg-infos-box">
                <span>
                  {{ $t('global_total') }}(
                  <span class="length">
                    {{ pkgFilterList.length }}
                  </span>
                  )
                </span>
                <span>
                  {{ $t('global_published') }}(
                  <span class="length">
                    {{ pkgFilterList.filter((o) => o.publish_status === 1).length }}
                  </span>
                  )
                </span>
                <span v-if="!$store.state.isMC2BD" style="flex: 1 1 auto; text-align: right">
                  <a-tooltip
                    placement="top"
                    overlay-class-name="common-tooltip-style"
                    :title="$t('goto_archived_packages')"
                  >
                    <span class="link" @click="gotoArchivedPackages">
                      <svg-icon icon-name="archived" />
                    </span>
                  </a-tooltip>
                </span>
              </div>
              <a-collapse-panel
                v-for="pkg in allPackages"
                :key="'' + pkg.package_id"
                class="panel-menu-wrap"
                :class="calcOnPkgClass(pkg)"
                :show-arrow="false"
              >
                <template slot="header">
                  <a-tooltip placement="right">
                    <template slot="title">
                      <div>
                        {{ `${pkg.package_local ? pkg.package_local[0].name : ''}` }}
                      </div>
                      <div :class="package_status[pkg.publish_status].class">
                        {{ package_status[pkg.publish_status].label }}
                      </div>
                    </template>
                    <div class="left-line common-two-line" :class="getPkgStatusLineClass(pkg)">
                      {{ `${pkg.package_id} - ${pkg.package_local ? pkg.package_local[0].name : ''}` }}
                    </div>
                  </a-tooltip>
                </template>
                <ul class="menu-box">
                  <li
                    v-for="(item, i) in pkg.steps"
                    :key="item.key"
                    :class="[
                      calcNavLock(i, pkg.package_id) || calcAuthLock(item) ? 'lock' : '',
                      getPkgActive(pkg, i)
                    ]"
                    @click="item.isLock || handleGoRoute(item, i, pkg)"
                  >
                    <template v-if="checkSpecialNav(pkg, item)">
                      <!-- overlay-class-name="common-tooltip-style" -->
                      <a-tooltip placement="right" :title="getTooltipTitle(item.title, i, pkg.package_id)">
                        <div>
                          <i v-if="calcPkgRequired(pkg, item)" class="common-required-star require"> * </i>
                          <span class="text">{{ item.text }}</span>
                          <i
                            v-if="
                              item.key === 'sellingInfo'
                                ? !pkg.is_trip_mapped && item.hideFinish !== true
                                : item.hideFinish !== true
                            "
                            :class="[
                              'right-icon',
                              calcPkgFinished(pkg.package_id, item.step) ? 'finish' : ''
                            ]"
                          >
                            <a-icon type="check-circle" theme="filled" />
                          </i>
                        </div>
                      </a-tooltip>
                    </template>
                  </li>
                </ul>
              </a-collapse-panel>
            </a-collapse>
            <div
              v-show="!$route.query.package_id && $route.path.indexOf('/act/package/') !== -1"
              class="menu-wrap"
            >
              <h3>-</h3>
              <ul class="menu-box">
                <li
                  v-for="(item, i) in pkgObj.addList"
                  :key="item.key"
                  :class="[i !== 0 ? 'lock' : '', i === 0 ? 'pkg-active' : '']"
                  @click="handleGoRoute(item, i)"
                >
                  <i v-if="item.require" class="common-required-star require">*</i>
                  <span class="text">{{ item.text }}</span>
                  <i v-if="item.hideFinish !== true" :class="['right-icon', item.finish ? 'finish' : '']">
                    <a-icon type="check-circle" theme="filled" />
                  </i>
                </li>
              </ul>
            </div>
          </a-collapse-panel>
        </a-collapse>
        <ActOptionalTools
          v-if="actOptionalTools.length"
          :disabled="getDisabled"
          :options="actOptionalTools"
        />
      </a-anchor>
      <div v-show="navLoading" class="common-spin-style"><a-spin /></div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapMutations, mapState, mapGetters } from 'vuex'
import { parseStr, getEditLang } from '@activity/utils'
import { isMerchant } from '@/env'
import actNavLib from './actNavLib'
import ActOptionalTools from '@activity/components/act-optional-tools'

import { fnb_package_types } from '../../pages/package/package_const'

// 对等 maps.js/package_status
const package_status = {
  0: { class: 'status-publish-style', label: __('global_unpublished') },
  1: { class: 'status-unpublish-style', label: __('global_published') },
  2: { class: 'status-suspended-style', label: __('79629') },
  3: { class: 'status-suspended-style', label: __('79629') }
}

export default {
  name: 'ActNav',
  components: {
    ActOptionalTools
  },
  // inject: ['reloadPage2provide'],
  inject: {
    provideGlobalData: {
      default: {
        hiddenAddPkg: false
      }
    }
  },
  data() {
    this.package_status = package_status
    return {
      routeItem: {},
      isMerchant,
      navLoading: false,
      pkgStepStatusMap: {},
      activePkgIds: [],
      activeKeysDef: ['1', '2'],
      pkgInfos: {
        packages: [],
        package_info: []
      },
      activity_id: this.$route.params.id,
      package_id: +this.$route.query.package_id,
      actObj: {
        list: actNavLib.methods.getRoleData.call(this, 'actList')
      },
      pkgObj: {
        addFlag: true,
        searchVal: '',
        addList: [],
        list: []
      },
      actOptionalTools: actNavLib.methods.getOptionalTools.call(this, isMerchant)
    }
  },
  computed: {
    ...mapGetters({
      calcPkgInfos: 'getPkgInfos2getters',
      calcApiMappingSupportConfig: 'currSupplyApiMappingSupportConfigGetter'
    }),
    packages() {
      return this.calcPkgInfos?.packages || []
    },
    calcItinerarySupportConfig() {
      return this.$store.state.actItemData?.itinerary_support_config
    },
    shouldHideHeader() {
      return !isMerchant && this.$route.name.match(/detail/i)
    },
    // hide-header 不需要 offset top
    offsetTop() {
      return this.shouldHideHeader ? 4 : 72
    },
    calcOnPkgClass() {
      return (pkg) => {
        let { isActivity, $route } = this
        return !isActivity && pkg.package_id == $route.query.package_id ? 'on-pkg' : ''
      }
    },
    calcShowAddBtn() {
      if (this.provideGlobalData?.hiddenAddPkg) {
        return false
      }

      let page_from = klook.getPlatformRoleKey()
      return isMerchant || (page_from !== 'bd_audit' && this.$root.roles.is_am)
    },
    isActivity() {
      return this.$route.path.includes('/act/activity/')
    },
    calcShowAction() {
      let isTrue = false
      if (this.isMerchant) {
        isTrue = !!this.$store.state.actStepStatus.activity_detail_info
      } else if (this.$root.roles.is_am) {
        isTrue = !!this.$store.state.actStepStatus.operational_info
      }
      return isTrue
    },
    actInfo() {
      return this.$attrs.actInfo || {}
    },
    calcAuthLock() {
      return function (item) {
        let isLock = typeof item.getLock === 'function' ? item.getLock(this.actInfo.activity_status) : false
        this.$set(item, 'isLock', isLock)
        this.$set(item, 'title', isLock ? __('no_auth_edit') : '')
        return isLock
      }
    },
    calcPkgStyle() {
      let actLeng = this.actObj.list.length
      const actOptionalToolsLeng = this.actOptionalTools.length
      const pkgHeight = 198 + actLeng * 36 - (this.shouldHideHeader ? 72 : 0)
      const optHeight = actOptionalToolsLeng ? 54 + actOptionalToolsLeng * 64 : 0
      let height = `calc(100vh - ${pkgHeight + optHeight}px)`
      return {
        height
      }
    },
    calcActRequireLock() {
      let arr = this.actObj.list.filter((o) => o.require)
      return arr.length
    },
    activeKeys: {
      get() {
        return this.activeKeysDef
      },
      set() {
        this.activeKeysDef = ['1', '2']
      }
    },
    ...mapState({
      actStepStatusObj: 'actStepStatus',
      addonList: 'addonPackageList',
      pkgStepsSchemaData: 'pkgStepsSchemaData'
    }),
    showAddPkg() {
      return !this.$route.query.package_id && this.$route.path.indexOf('/act/package/') !== -1
    },
    tipText() {
      return parseStr(this.$t('actNav.regText'), { number: 446 })
    },
    pkgFilterList() {
      let {
        pkgObj: { searchVal },
        pkgInfos
      } = this
      if (!searchVal) {
        return pkgInfos.packages || []
      }
      let arr = (pkgInfos.packages || []).filter((pkg) => {
        return `${pkg.package_id} - ${pkg.package_local ? pkg.package_local[0].name : ''}`.includes(searchVal)
      })
      return arr
    },
    isPass() {
      const subCategoryId = this.$store.state.categoryInfo?.sub_category_id ?? 0
      const categoryIds = process.env.NODE_ENV === 'production' ? [527] : [446]
      return categoryIds.includes(subCategoryId)
    },
    getDisabled() {
      if (this.isPass) {
        // 有没有套餐
        return !this.pkgFilterList.length
      } else {
        return !this.addonList.length
      }
    },
    allPackages() {
      return (this.pkgFilterList || []).map((item) => {
        const { package_id } = item

        return {
          ...item,
          // 请保持与 @activity/components/merchant/menu.vue#266 calcCurrSteps 逻辑一致
          steps: this.pkgObj.list.reduce((accSteps, currStep) => {
            if (currStep.step === 'supply_api_mapping') {
              const supplyApiMappingSupportConfig =
                this.$store.getters.getSupplyApiMappingSupportConfigByPid(package_id)
              const { support_supply_api_mapping, is_required } = supplyApiMappingSupportConfig

              if (support_supply_api_mapping !== 0) {
                accSteps.push({ ...currStep, require: is_required })
              }
            } else {
              accSteps.push(currStep)
            }

            return accSteps
          }, [])
        }
      }, [])
    }
  },
  watch: {
    activePkgIds: {
      deep: true,
      handler(newV, oldV) {
        if (oldV && oldV.length < newV.length) {
          const packages = _.difference(newV, oldV).map((id) => ({
            package_id: +id
          }))
          this.initPackageFloatingFields(packages)
        }
      }
    },
    calcItinerarySupportConfig: {
      deep: true,
      immediate: true,
      handler() {
        this.updatePkgObjList()
      }
    },
    calcApiMappingSupportConfig: {
      deep: true,
      handler() {
        this.updatePkgObjList()
      }
    },
    '$route.query.package_id': {
      handler(v) {
        this.$store.commit('setPkgStepStatus2mutations', this.pkgStepStatusMap[v] || {})
      }
    },
    '$route.path': {
      immediate: true,
      handler(path) {
        let { actObj, pkgObj } = this
        let arr = [...actObj.list, ...pkgObj.list]
        let obj = arr.find((o) => path.indexOf(o.path) > -1)
        this.routeItem = obj
        this.$emit('change', this.routeItem)
      }
    },
    pkgStepStatusMap(obj) {
      this.setPkgStepStatusMap2mutations(obj)
    },
    actStepStatusObj: {
      handler() {
        this.updateFinishStatus()
      },
      deep: true
    },
    shouldHideHeader: {
      immediate: true,
      async handler(v) {
        await this.$nextTick()
        const target = document.querySelector('.inner-layout .ant-layout')
        if (v) {
          target?.classList?.add?.('hide-header')
        } else {
          target?.classList?.remove?.('hide-header')
        }
      }
    }
  },
  async created() {
    this.pkgObj.addList = this.pkgObj.list.map((item, i) => {
      let obj = {
        hideFinish: item.hideFinish,
        require: item.require,
        text: item.text
      }
      return obj
    })
    klook.bus.$off('updateActStepStatus2bus').$on('updateActStepStatus2bus', this.initActStepStatus)
    klook.bus.$off('updatePkgInfos2bus').$on('updatePkgInfos2bus', this.updatePkgInfos2bus)
    this.navLoading = true
    await this.initActStepStatus(true)
    await this.updatePkgInfos2bus()
    this.navLoading = false
    this.package_id && (this.activePkgIds = ['' + this.package_id])
    klook.bus.$off('gotoCreatePkg2bus').$on('gotoCreatePkg2bus', this.gotoCreatePkg2bus)
    if (!isMerchant) {
      this.actionGetAddon({
        activity_id: this.$route.params.id,
        language: getEditLang()
      })
    }
  },
  beforeDestroy() {
    klook.bus
      .$off('gotoCreatePkg2bus', this.gotoCreatePkg2bus)
      .$off('updateActStepStatus2bus', this.initActStepStatus)
      .$off('updatePkgInfos2bus', this.updatePkgInfos2bus)
  },
  mounted() {
    klook.bus
      .$off('changeNavPackageListDataByKey')
      .$on('changeNavPackageListDataByKey', this.updatePkgStepSchema)
    this.$once('hook:beforeDestroy', () => {
      klook.bus.$off('changeNavPackageListDataByKey')
    })
  },
  methods: {
    ...mapActions([
      'updateActivityStepStatusById',
      'updatePackageStepStatusById',
      'getPkgInfo2actions',
      'actionGetAddon'
    ]),
    ...mapMutations(['setPkgInfos2mutations', 'setPkgStepStatusMap2mutations']),
    updatePkgObjList: _.debounce(function () {
      this.pkgObj.list = actNavLib.methods.getRoleData.call(this, 'pkgList', {
        itinerary_support_config: this.calcItinerarySupportConfig,
        apiMappingSupportConfig: this.calcApiMappingSupportConfig
      })
    }, 30),
    updatePkgStepSchema(data = []) {
      const list = this.pkgObj.list.map((step) => {
        const res = data.find((item) => item.step === step.step) || {}

        return {
          ...step,
          ...res
        }
      })
      this.$set(this.pkgObj, 'list', list)
    },
    getTooltipTitle(title, index, package_id) {
      if (this.calcNavLock(index, package_id)) {
        return this.$t('80438')
      }
      return title
    },
    getPkgStatusLineClass(pkg) {
      return (
        {
          1: 'publish-line',
          0: 'unpublish-line',
          2: 'suspend-line',
          3: 'suspend-line'
        }[pkg.publish_status] ?? ''
      )
    },
    checkSpecialNav(pkg, item) {
      let { package_type } = pkg
      let { step } = item
      let isb = true
      switch (step) {
        case 'pkg_content':
          isb = [fnb_package_types.fnb_set_menu, fnb_package_types.fnb_single_dish].includes(package_type)
          break
      }
      return isb
    },
    gotoArchivedPackages() {
      klook.gotoOldAdmin(`/act/activity/${this.activity_id}/package_list_archive`)
    },
    gotoCreatePkg2bus() {
      let { query } = this.$route
      let packages = this.$store.getters.pkgList
      _.get(packages, '[0].package_id', '') ||
        this.$router.push({
          name: 'packageBasicInfo',
          query: {
            ...query,
            package_id: _.get(packages, '[0].package_id', ''),
            package_type: _.get(packages, '[0].package_type', '1')
          },
          params: {
            id: this.activity_id
          }
        })
    },
    clickPkgHeader() {
      if (this.activePkgIds.length) {
        this.activePkgIds2copy = _.clone(this.activePkgIds)
        this.activePkgIds = []
      } else {
        this.activePkgIds = this.activePkgIds2copy || []
      }
    },
    getPkgActive(pkg, i) {
      let { query, path } = this.$route
      let pkgActive = 'pkg-active'
      if (pkg.package_id == query.package_id) {
        let pkgList = this.pkgObj.list
        return path.includes(pkgList[i].path) ? pkgActive : ''
      }
      return ''
    },
    getFinished({ step, unionCheck }, obj) {
      let isb = false

      if (Array.isArray(unionCheck) && unionCheck.length && unionCheck.every((item) => obj[item])) {
        return true
      }

      if (typeof step === 'string') {
        isb = obj[step]
      } else if (klook.getType(step) === 'Array') {
        let arr = step.map((o) => obj[o])
        isb = arr.every((v) => !!v)
      }

      return !!isb
    },
    calcNavLock(i, id) {
      // id不存在则表示活动层级
      let { pkgStepStatusMap, actStepStatusObj, pkgObj, actObj } = this
      let maps = !id ? actStepStatusObj : pkgStepStatusMap
      let list = !id ? actObj.list : pkgObj.list
      let curItem = list[i]
      let preItem = this.getPreRequireItem(i - 1, list)
      let obj = !id ? maps : maps[id]
      if (!preItem || !obj) return false //第一个套餐基本信息不用锁
      if (this.getFinished(curItem, obj)) {
        return false //自己已完成绿标跳过
      }

      if (!preItem.require || preItem.passCheck) {
        return false
      }

      return !this.getFinished(preItem, obj)
    },
    getPreRequireItem(i, list) {
      let obj = list[i]
      if (i < 0) {
        return false
      }
      if (obj.require) {
        return obj
      } else {
        return this.getPreRequireItem(i - 1, list)
      }
    },
    calcPkgRequired(pkg, item) {
      const { package_id, is_trip_mapped } = pkg
      const { require, key, step } = item
      const required = require || (this.pkgStepsSchemaData[package_id]?.[step]?.required ?? false)

      return key === 'sellingInfo' ? !is_trip_mapped && required : required
    },
    calcPkgFinished(packageId, step) {
      const pgkSteps = this.pkgStepStatusMap[packageId]

      if (!pgkSteps) {
        return false
      }

      return Array.isArray(step) ? step.every((item) => !!pgkSteps[item]) : pgkSteps[step]
    },
    handleChangeActive(keys) {
      keys.forEach((key) => {
        this.getPkgStepStatus(key)
      })
    },
    async updatePkgInfos2bus(data, newPkgId) {
      if (data) {
        this.$set(this, 'pkgInfos', data)
      } else {
        this.$set(
          this,
          'pkgInfos',
          await this.initPkgInfos({
            refresh: true
          })
        )
        await this.getPkgStepStatus(newPkgId, true)
      }
    },
    clickAddPackage() {
      if (this.showAddPkg) return
      // !this.activeKeys.includes('2') && this.activeKeys.push('2')
      let { pkgObj } = this
      if (pkgObj.addFlag || this.$route.path.includes('/act/activity/')) {
        pkgObj.addFlag = false
        this.$router.replace({
          path: `/act/package/info/${this.$route.params.id}`,
          query: {
            ...this.$route.query,
            package_type: 1,
            package_id: ''
          }
        })
      }
    },
    async updateFinishStatus(save = false) {
      if (!isNaN(this.activity_id)) {
        let stepObj = this.actStepStatusObj
        this.initFinishStatus(this.actObj.list, stepObj, 'step')
        this.pkgObj.addFlag = !!stepObj.activity_detail_info
        if (
          save &&
          stepObj.activity_detail_info &&
          (!this.pkgInfos.packages || !this.pkgInfos.packages.length)
        ) {
          this.clickAddPackage()
        }
      }
    },
    async initActStepStatus(sync = false) {
      let data = {
        activity_id: +this.activity_id,
        language: getEditLang(),
        refresh: true
      }
      if (sync) {
        return await this.updateActivityStepStatusById(data)
      } else {
        return this.updateActivityStepStatusById(data)
      }
    },
    async initPkgInfos(data = {}) {
      let pkgInfos = await this.getPkgInfo2actions({
        activity_id: +this.activity_id,
        refresh: data.refresh
      })
      return klook.getType(pkgInfos) === 'Object' ? pkgInfos : {}
    },
    async getPkgStepStatus(packageId, refresh) {
      let package_id = +packageId || +this.$route.query.package_id
      if (!package_id || (this.pkgStepStatusMap[package_id] && !refresh)) return
      let stepObj = await this.updatePackageStepStatusById({
        activity_id: +this.activity_id,
        package_id,
        language: getEditLang(),
        refresh: true
      })
      this.$set(this.pkgStepStatusMap, package_id, stepObj || {})
      this.$route.query.package_id &&
        this.$store.commit(
          'setPkgStepStatus2mutations',
          this.pkgStepStatusMap[this.$route.query.package_id] || {}
        ) //保证使用当前套餐
      !this.activePkgIds.includes('' + package_id) && this.activePkgIds.push('' + package_id)
    },
    initFinishStatus(arr = [], stepObj = {}, stepKey = 'step') {
      let count = 1
      arr.forEach((item) => {
        let stepStatus = stepObj[item[stepKey]]
        this.$set(item, 'finish', stepStatus === 1)
        if (item[stepKey] in stepObj) {
          count++
        }
      })
      return count
    },
    handleGoRoute(item, i, pkg = {}) {
      if (this.calcNavLock(i, pkg.package_id)) {
        return false
      }
      let { route } = item
      if (this.$route.path.includes(item.path) && +pkg.package_id === +this.$route.query.package_id) {
        return false
      }
      this.pkgObj.addFlag = true
      if (typeof route === 'function') {
        route(pkg, item)
      } else {
        if (!item.path) return false
        this.$router.replace({
          path: item.path + this.activity_id,
          query: {
            ...this.$route.query,
            package_type: pkg.package_type,
            package_id: pkg.package_id
          }
        })
        // this.reloadPage2provide()
      }
    },
    async initPackageFloatingFields(packages) {
      for (let pkg of packages) {
        const response = await ajax.getBody(ADMIN_API.act.get_floating_fields2pkg, {
          params: {
            package_id: pkg.package_id,
            page_from: klook.getPlatformRoleKey(),
            language: getEditLang()
          }
        })
        this.$store.commit('setPackageFloatingFieldMap', response?.result || {})
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.act-nav {
  position: relative;
  z-index: 1;
  .btns-am-em-wrap {
    width: 100%;
    position: absolute;
    z-index: 11;
    bottom: 0;
    left: 0;
    .btns-am-em-box {
      width: 100%;
      text-align: right;
      padding: 8px 20px;
      box-shadow: inset 0px 1px 0px rgba(0, 0, 0, 0.09);
    }
  }
  .act-nav-box {
    .text {
      font-size: 14px;
      padding-left: 6px;
    }
    .right-icon {
      float: right;
      font-size: 16px;
      color: #e8e8e8;
      &.finish {
        color: #36b37e;
      }
    }
    .panel-menu-wrap {
      position: relative;
      background-color: rgba(0, 145, 255, 0.05);

      &.on-pkg ::v-deep .ant-collapse-header {
        // border: 1px solid #0091ff;
        .left-line {
          color: #0091ff;
        }
      }

      .left-line {
        font-size: 16px;
        font-weight: 400;
        border-left: 4px solid rgba(0, 0, 0, 0.09);
        padding-left: 10px;
        line-height: 20px;
        &.publish-line {
          border-left-color: #36b37e;
        }
        &.unpublish-line {
          border-left-color: rgba(0, 0, 0, 0.09);
        }
        &.suspend-line {
          border-left-color: #ff4d4f;
        }
      }
    }
    .menu-wrap {
      position: relative;
      padding: 10px 10px 10px 22px;
      background: rgba(0, 145, 255, 0.05);
      h3 {
        color: #0091ff;
        font-size: 16px;
        font-weight: 400;
      }
    }
    .menu-box {
      padding: 10px 10px 6px 22px;
      margin: 0;

      &.menu-act {
        padding: 0 0 8px 10px;
        li .text {
          max-width: 215px;
        }
      }

      li {
        position: relative;
        line-height: 36px;
        color: #000;
        margin-bottom: 4px;
        background-color: #fff;
        padding: 0 10px 0 10px;
        border-radius: 4px;
        cursor: pointer;
        box-sizing: border-box;

        &.pkg-active {
          border: 1px solid #0091ff;
          color: #0091ff;
        }

        .text {
          display: inline-block;
          max-width: 185px;
          height: 18px;
          line-height: 16px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          vertical-align: middle;
        }
        &:hover {
          background: rgba(0, 145, 255, 0.05);
        }
        &.on {
          color: #0091ff;
        }
        &.lock {
          color: #ccc;
          cursor: not-allowed;
        }
      }
    }
    .header-box {
      position: relative;
      display: flex;
      .field-text {
        padding-right: 6px;
      }
    }
    .action-box {
      flex: 1 0 auto;
      text-align: right;
      .search-box {
        width: 96px;
      }
      .btn-add-action {
        margin-left: 8px;
        display: inline-block;
        color: #0091ff;
        cursor: pointer;
        vertical-align: middle;
      }
    }
    .tip-box {
      background-color: #fff;
      padding: 0 22px;
      line-height: 37px;
      color: #000;
    }
  }
  .nav-pkg-box {
    height: calc(100vh - 270px);
    overflow: hidden;
  }
  .pkg-infos-box {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    padding: 6px 10px;
    .length {
      color: #000;
    }
    > span:not(:last-child) {
      margin-right: 20px;
    }
    .link {
      display: inline-block;
      width: 36px;
      height: 36px;
      line-height: 36px;
      cursor: pointer;
      color: #0091ff;
      background: #fafafa;
      border-radius: 100%;
      text-align: center;
      svg {
        width: 16px;
        height: 16px;
        vertical-align: middle;
      }
    }
  }
}
</style>
<style lang="scss">
.nav-pkg-box {
  .ant-collapse-content {
    height: calc(100% - 52px);
    overflow: hidden;
    overflow-y: auto;
  }
}
</style>
