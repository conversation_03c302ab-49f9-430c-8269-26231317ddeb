<!-- 说明: 限制活动某些语言不能发布 -->
<template>
  <div class="activity-blocklist">
    <a-form-model-item
      class="common-reset-style"
      :label="$t('act_blocklist_publish_conditions') + ' - ' + $t('act_blocklist_language_availability')"
      :required="required"
    >
      <DescMarkdownContent
        class="strong-tips"
        placement="right"
        :is-inline="false"
        :desc="schemaConfig && schemaConfig.strongTips"
        :line-clamp="3"
      />

      <span class="tips gray" style="display: block; line-height: 1.5em; color: #aaa">
        {{ $t('act_blocklist_note') }}
      </span>

      <ShimAntdTooltip
        :disabled="disabled"
        :title="$attrs.description || $t('language_publish_condition')"
        v-bind="$attrs"
      >
        <a-radio-group v-model="selected_type" :disabled="disabled" @change="onSelectedTypeChange">
          <a-radio value="all">
            {{ $t('act_blocklist_all_languages') }}
          </a-radio>
          <a-radio value="part">
            {{ $t('act_blocklist_selected_languages') }}
          </a-radio>
        </a-radio-group>

        <template v-if="selected_type === 'part'">
          <!-- <span class="tips gray">
            Note: Unselected locale(s) will be added to the blocklist and stay
            offline. You can come back to modify the setting anytime.
          </span> -->
          <!-- <br /> -->
          <a style="cursor: pointer" @click="selectAll">
            {{ $t('act_blocklist_select_all') }}
          </a>
          -
          <a style="cursor: pointer" @click="clearAll">
            {{ $t('act_blocklist_clear_all') }}
          </a>
          <div class="checkbox-wrap">
            <a-checkbox-group
              v-for="(options, key) of languages"
              :key="key"
              v-model="selected_languages[key]"
              @change="onChangeData"
            >
              <a-checkbox v-for="lang in options" :key="lang.val" :value="lang.val">
                {{ lang.name }}
              </a-checkbox>
            </a-checkbox-group>
          </div>
        </template>
      </ShimAntdTooltip>
    </a-form-model-item>

    <BlockListModal
      v-if="dialogVisible"
      :visible.sync="dialogVisible"
      :modified-data="modified_data"
      :lang-obj="lang_obj"
      @cancel="handleCancel"
      @confirm="handleConfirm"
    />
  </div>
</template>
<script>
import DescMarkdownContent from '@activity/pages/activityManagement/detailV2/components/descMarkdownContent'
import ShimAntdTooltip from '@activity/components/shimAntdTooltip'
import base_mixin from '~src/modules/activity/components/activity_blocklist/mixin.js'
// 正文
export default {
  name: 'ActivityBlocklist',
  components: {
    DescMarkdownContent,
    ShimAntdTooltip
  },
  mixins: [base_mixin]
}
</script>
<style lang="scss" scoped>
.activity-blocklist {
  .checkbox-wrap {
    line-height: 0;
    .ant-col.ant-form-item-control-wrapper {
      max-width: auto;
    }
    .ant-checkbox-wrapper {
      margin: 0 8px 0 0;
    }
  }
  .common-reset-style {
    margin-bottom: 0;
  }
}
</style>
