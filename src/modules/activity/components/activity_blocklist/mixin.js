// 所有语言
const lang_conf = require('lang_conf')
// 语言下拉
const lang_array = lang_conf.getAllLangOption().map((v) => ({
  name: v.options,
  val: v.B_LANG
}))
// key: B_LANG, value: LANG_TITLE
const lang_obj = lang_conf.getLangObj('B_LANG', 'LANG_TITLE')
import BlockListModal from './modal.vue'

export default {
  name: 'ActivityBlocklist',
  components: {
    BlockListModal
  },
  props: {
    form: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: true
    },
    schemaConfig: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    selected_languages: {
      deep: true,
      handler() {
        this.$emit('selectedChange', this.save())
      }
    },
    form: {
      immediate: true,
      deep: true,
      handler() {
        this.init()
      }
    }
  },
  data() {
    return {
      //! 全选 or 只选择部分语言
      selected_type: 'all', // all / part
      //! 分开展示 英文系列/中文系列/其他语言
      languages: {
        en: [],
        zh: [],
        other: []
      },
      //! 选中的语言
      selected_languages: {
        zh: [],
        en: [],
        other: []
      },
      lang_obj,
      //! 默认数据 or 请求拿到的数据, 用于对比哪一些是新添加的
      old_data: lang_array.map((v) => v.val),
      //! 是否是创建活动, true 为编辑, false 为创建
      isCreated: !_.isEmpty(klook.urlParam('lang')),
      dialogVisible: false,
      // 修改的数据
      modified_data: {
        added: [],
        moved: []
      },
      saveSucCallback: () => {}
    }
  },
  methods: {
    /**
     * !初始化
     * 1. 创建活动: 默认 all locales 都允许
     * 2. 编辑活动: 从接口获取当前活动的 blocklist
     */
    async init() {
      if (this.initFirst) return
      this.initFirst = true //只初始化一次
      // 生成语言分类
      lang_array.forEach((v) => {
        const { val } = v
        let is_other = true
        for (const key in this.languages) {
          // 判断属于哪个语言分类
          if (val.includes(key)) {
            is_other = false
            this.languages[key].push(v)
          }
        }
        // 不是 zh || en , 则存到 other
        if (is_other) {
          this.languages.other.push(v)
        }
      })
      // 创建
      if (!this.isCreated) {
        // 默认全部选中
        this.selectAll()
        this.selected_type = 'all'
        this.cacheData = _.cloneDeep(this.selected_languages)
        return
      }
      // 获取黑名单语言
      let { published_conditions } = this.form
      let res = published_conditions?.items
      if (res) {
        this.selected_type = 'part'
        const all_items = _.merge([], res || [])
        // 是否全选
        const langs = (all_items.filter((v) => !v.status) || []).map((v) => v.language)
        this.old_data = langs
        // 全选, 则切换到 all locales
        if (langs.length === lang_array.length) {
          this.selected_type = 'all'
        }
        let newLangObj = {
          en: [],
          zh: [],
          other: []
        }
        // 分类分别记录
        all_items.forEach((v) => {
          const { status, language } = v
          // status: 0 正常, 1 block
          if (status) {
            return
          }
          let flag = true
          for (const key in this.selected_languages) {
            // 判断属于哪个语言分类
            if (language.includes(key)) {
              flag = false
              newLangObj[key].push(language)
            }
          }
          // 不是 zh || en , 则存到 other
          flag && newLangObj.other.push(language)
        })
        this.selected_languages = newLangObj
      } else {
        //! 获取失败, 全部都允许
        this.selected_type = 'all'
        this.selectAll()
      }
      // 记录原始数据
      this.cacheData = _.cloneDeep(this.selected_languages)
    },
    // 清除
    clearAll() {
      for (let key in this.selected_languages) {
        this.selected_languages[key] = []
      }
    },
    // 全选
    selectAll() {
      for (let key in this.selected_languages) {
        this.selected_languages[key] = this.languages[key].map((v) => v.val)
      }
    },
    // 保存 blocklist
    // modify_id:一个button点击可能会产生多个提交请求，为所有请求添加一个modify id，表示请求都由同一个操作产生.
    save() {
      // 参数
      const data = {}
      if (this.selected_type === 'all') {
        // 传黑名单
        data.language_list = []
        data.status = 1 // 活动语言黑名单状态 0-正常, 1-黑名单,
      } else {
        // 传白名单, 未在language_list的语言会被更新为与请求status相反的状态
        data.language_list = Object.values(this.selected_languages).flat()
        data.status = 0 // 活动语言黑名单状态 0-正常, 1-黑名单,
        //! 检查是不是把所有已发布的语言都给block掉了, 是的话提示必须保留一个已发布语言
        // await this.checkSelectedLanguage(data.language_list);
      }

      // basic info接口保存成功后，需要父组件调用此函数
      this.saveSucCallback = (res) => {
        if (res) {
          this.old_data = Object.values(this.selected_languages).flat()
          this.cacheData = _.cloneDeep(this.selected_languages)
        }
      }
      return data
    },
    //! 检查是不是把所有已发布的语言都给block掉了, 是的话提示必须保留一个
    async checkSelectedLanguage(selected_list) {
      const resp = this.form.published_conditions.language_status
      const languages_status_all = _.get(resp, 'status') || []
      //! 找出已发布语言
      const all_published_languages = languages_status_all.reduce((acc, cur) => {
        if (cur.status === 3) {
          // all_published_languages.push(cur.language);
          return [...acc, cur.language]
        }
        return acc
      }, [])
      //! 判断是否包含至少一个已发布语言
      let bool =
        !all_published_languages.length || selected_list.some((v) => all_published_languages.includes(v))
      if (bool) {
        return Promise.resolve()
      }

      this.$success({
        title: this.$t('global_dialog_title'),
        content: this.$t('act_blocklist_at_least_one'),
        okText: this.$t('global_confirm'),
        onOk: () => {
          this.clearModifiedData()
        }
      })

      return Promise.reject()
    },
    // 关闭
    handleCancel() {
      this.clearModifiedData()
      this.remindPromise.reject()
    },
    // 确定
    handleConfirm() {
      this.clearModifiedData()
      this.remindPromise.resolve()
    },
    // 判断哪一些新增哪一些去勾选
    getChangedData() {
      // 新数据
      const new_data = Object.values(this.selected_languages).flat()
      lang_array.forEach((v) => {
        // 新数据中有, 但旧数据没有, 说明是新增
        if (new_data.includes(v.val) && !this.old_data.includes(v.val)) {
          this.modified_data.added.push(v.val)
        }
        // 旧数据中有, 但新数据没有, 说明是删除
        if (!new_data.includes(v.val) && this.old_data.includes(v.val)) {
          this.modified_data.moved.push(v.val)
        }
      })
    },
    // 关闭弹窗清除数据
    clearModifiedData() {
      this.modified_data = {
        added: [],
        moved: []
      }
      this.dialogVisible = false
    },
    // remind 提醒
    async remind() {
      this.getChangedData()
      //! 未做任何修改, 直接 resolve, 不打开弹窗
      if (
        // this.selected_type === "part" &&
        !this.modified_data.added.length &&
        !this.modified_data.moved.length
      ) {
        return true
      }
      //! 如果有做修改, 先判断"必须保留至少一个已发布语言"
      // 传白名单, 未在language_list的语言会被更新为与请求status相反的状态
      let selected_languages = Object.values(this.selected_languages).flat()
      //! 检查是不是把所有已发布的语言都给block掉了, 是的话提示必须保留一个已发布语言
      await this.checkSelectedLanguage(selected_languages)
      this.dialogVisible = true
      return new Promise((resolve, reject) => {
        this.remindPromise = {
          resolve,
          reject
        }
      })
    },
    // 选择改变
    onSelectedTypeChange(val) {
      if (val === 'all') {
        this.selectAll()
      } else {
        this.selected_languages = _.cloneDeep(this.cacheData)
      }

      this.onChangeData()
    },
    // 判断改变
    isChanged() {
      return _.isEqual(this.cacheData, this.selected_languages)
    },
    onChangeData() {
      this.$emit('changeData')
    }
  }
}
