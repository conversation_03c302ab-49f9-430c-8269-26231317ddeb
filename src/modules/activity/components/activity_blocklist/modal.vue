<template>
  <a-modal
    :visible.sync="_visible"
    size="tiny"
    :title="$t('global_tips')"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <div class="warning-message">
      <template v-if="modifiedData.added.length">
        <div style="font-weight: 500">
          {{ $t('act_blocklist_please_confirm') + $t('act_blocklist_system_will_notify') }}
        </div>
        <div v-for="lang in modifiedData.added" :key="lang">- {{ langObj[lang] }}</div>
      </template>
      <template v-if="modifiedData.moved.length">
        <div style="font-weight: 500">
          {{ $t('act_blocklist_not_publish') + $t('act_blocklist_will_not_publish') }}
        </div>
        <div v-for="lang in modifiedData.moved" :key="lang">- {{ langObj[lang] }}</div>
      </template>
      <!-- <br /> -->
      <!-- <div
        style="font-weight: 500"
        v-if="modifiedData.moved.length && modifiedData.added.length"
      >
        It will re-trigger the onboarding workflow.
      </div> -->
    </div>
    <span slot="footer">
      <a-button @click="handleCancel">
        {{ $t('global_cancel') }}
      </a-button>
      <a-button type="primary" @click="handleConfirm">
        {{ $t('global_confirm') }}
      </a-button>
    </span>
  </a-modal>
</template>

<script>
export default {
  name: 'BlockListModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    modifiedData: {
      type: Object,
      required: true
    },
    langObj: {
      type: Object,
      required: true
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(visible) {
        this.$emit('update:visible', visible)
      }
    }
  },
  methods: {
    handleCancel() {
      this.$emit('cancel')
    },
    handleConfirm() {
      this.$emit('confirm')
    }
  }
}
</script>
