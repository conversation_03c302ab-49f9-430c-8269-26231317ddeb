<template>
  <a-drawer
    wrap-class-name="anchor-preview-modal anchor-preview-modal__custom-style"
    :visible.sync="_visible"
    :mask="false"
    :closable="false"
    :get-container="drawerContainer"
    :width="width"
    :drawer-style="{
      maxHeight: 'calc(100vh - 64px)'
    }"
    :body-style="{
      marginTop: '55px',
      padding: '8px',
      height: 'calc(100% - 55px)'
    }"
    v-bind="$attrs"
    @close="onClose"
  >
    <template #title>
      <div class="header-title">
        {{ $t('73574') }}
        <a-icon class="fullscreen" type="fullscreen" @click="$emit('fullscreen')" />
      </div>
    </template>
    <div class="content">
      <div class="list">
        <div
          v-for="(link, index) of links"
          :key="index"
          :ref="link.field"
          class="item"
          :class="{ 'is-highlight': currField === link.field }"
          :data-field="link.field"
        >
          <a-icon type="pushpin" @click="handleScrollToByLink(link)" />
          <!-- <div class="item-title">{{ link.title }}</div> -->
          <MarkdownText class="item-content" :text="link.content" />
        </div>
      </div>
    </div>
  </a-drawer>
</template>

<script>
import {
  isDomElement,
  addEventListener,
  getDefaultContainer,
  getScroll,
  scrollToEle
} from '@activity/components/anchor-tips/utils.js'
import MarkdownText from '@activity/components/MarkdownText.vue'

export default {
  name: 'AnchorTipsDrawer',
  components: {
    MarkdownText
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    links: {
      type: Array,
      default: () => [],
      validator(links) {
        // 确保每一个都有 title / content / target
        return links.every((link) => ['content', 'target'].every((field) => hasOwnProperty.call(link, field)))
      }
    },
    // 容器 selector string | HTMLElement
    container: {
      type: [String, Object],
      default: ''
    },
    getContainer: {
      type: Function,
      default: () => {
        return window
      }
    },
    getDrawerContainer: {
      type: Function,
      default: null
    },
    rootMargin: {
      type: String,
      default: null
    },
    threshold: {
      type: Array,
      default: () => [1]
    },
    // 锚点区域边界
    bounds: {
      type: Number,
      default: 0
    },
    // 距离窗口顶部达到指定偏移量后触发
    offsetTop: {
      type: Number,
      default: 0
    },
    // 锚点滚动偏移量, 默认与 offsetTop 相同
    targetOffset: {
      type: Number,
      default: undefined
    },
    width: {
      type: Number,
      default: 320
    },
    shouldBindScrollEvent: {
      type: Boolean,
      default: false
    }
  },
  data() {
    this.highlightClassName = 'highlight'

    return {
      $scrollContainer: null,
      activeLinkTarget: null,

      currField: ''
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(v) {
        this.$emit('update:visible', v)
      }
    }
  },
  watch: {
    visible: {
      deep: true,
      immediate: true,
      handler(v) {
        if (v) {
          this.$scrollContainer = this.getScrollContainer()
          this.bindScrollEvent()
        }
      }
    }
  },
  mounted() {
    klook.bus
      .$off('highlightAnchorTipsField2bus')
      .$on('highlightAnchorTipsField2bus', this.highlightAnchorTipsField2bus)
  },
  updated() {
    const $scrollContainer = this.getScrollContainer()
    if (this.scrollEvent && this.scrollEvent._$scrollContainer !== $scrollContainer) {
      this.$scrollContainer = $scrollContainer
      this.bindScrollEvent()
    }
  },
  destroyed() {
    this.scrollEvent?.remove?.()
    this.scrollEvent = null
    klook.bus.$off('highlightAnchorTipsField2bus')
  },
  methods: {
    highlightAnchorTipsField2bus({ field }) {
      if (!field) {
        this.currField = ''

        const highlight = document.querySelector(`.anchor-preview-modal .item.${this.highlightClassName}`)
        if (highlight) {
          highlight.classList.remove(this.highlightClassName)
        }

        return
      }

      const link = this.links.find((link) => field === link.field)

      if (link) {
        this.scrollToLinkEle(link)
      } else {
        this.currField = ''
      }
    },
    getScrollContainer() {
      let { container, getContainer } = this

      let result
      if ('[object Function]' === toString.call(getContainer)) {
        result = getContainer()
      } else {
        result = isDomElement(container)
          ? container
          : document.querySelector(container) || getDefaultContainer()
      }

      return result
    },
    bindScrollEvent() {
      if (this.shouldBindScrollEvent) {
        this.scrollEvent?.remove?.()
        this.scrollEvent = addEventListener(this.$scrollContainer, 'scroll', this.handleScroll)
        this.scrollEvent._$scrollContainer = _.cloneDeep(this.$scrollContainer)
        this.handleScroll()
      }
    },
    handleScroll() {
      if (this.animating) {
        return
      }

      const { offsetTop, bounds, targetOffset } = this
      const currentActiveLink = this.getCurrentAnchor(
        targetOffset !== undefined ? targetOffset : offsetTop || 0,
        bounds
      )

      this.setCurrentActiveLink(currentActiveLink)
      this.scrollToLinkEle(currentActiveLink)
    },
    scrollToLinkEle(link) {
      const { field } = link

      this.currField = field

      const top = this.$refs[field]?.[0]?.getBoundingClientRect?.()?.top ?? null
      if (top !== null && this.preScrollField !== field) {
        const { offsetTop, targetOffset } = this
        const $body = document.querySelector('.anchor-preview-modal .ant-drawer-body')
        if (!$body) {
          return
        }
        // 55 header title // 20 padding
        let y = $body.scrollTop + top - 55 - 20
        y -= targetOffset !== undefined ? targetOffset : offsetTop || 0

        scrollToEle(y, {
          getContainer: () => $body
        })

        this.preScrollField = field
      }
    },
    getCurrentAnchor(offsetTop = this.offsetTop, bounds = this.bounds) {
      const { $scrollContainer } = this

      const linkSections = []
      this.links.forEach((link) => {
        const target = document.querySelector(link.target)
        if (isDomElement(target)) {
          const { top, height } = target.getBoundingClientRect()
          const scrollTop = getScroll($scrollContainer, true) + offsetTop + bounds
          // 坐标范围在 [top, top + height]
          if (top <= scrollTop && top + height >= scrollTop) {
            linkSections.push({
              link,
              top
            })
          }
        }
      })

      if (linkSections.length) {
        const maxSection = linkSections.reduce((prev, curr) => (curr.top > prev.top ? curr : prev))

        return maxSection.link
      }

      return ''
    },
    setCurrentActiveLink(link) {
      const { getCurrentAnchor } = this
      if (this.activeLinkTarget === link.target) {
        return
      }

      this.activeLinkTarget = typeof getCurrentAnchor === 'function' ? getCurrentAnchor() : link.target

      this.$emit('change', link)
    },
    handleScrollToByLink(link) {
      const target = document.querySelector(link.target)

      if (isDomElement(target)) {
        const { offsetTop, targetOffset, $scrollContainer } = this

        this.setCurrentActiveLink(link)
        const scrollTop = getScroll($scrollContainer, true)
        const { top } = target.getBoundingClientRect()
        let y = scrollTop + top
        y -= targetOffset !== undefined ? targetOffset : offsetTop || 0

        this.animating = true
        scrollToEle(y, {
          callback: () => {
            this.animating = false
          },
          getContainer: () => $scrollContainer
        })

        this.currField = ''
      }
    },
    drawerContainer() {
      return this.getDrawerContainer?.() || document.querySelector('#app')
    },
    onClose() {
      this._visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import './common.scss';
@import '../../../../../src/assets/css/lib/mixins';

.anchor-preview-modal {
  ::v-deep .ant-drawer-wrapper-body {
    .ant-drawer-body {
      position: relative;
      padding: 0;

      @include mixin-hover-display-scrollbar;
    }
  }

  .header-title {
    display: flex;
    justify-content: space-between;
  }

  .fullscreen {
    position: fixed;
    right: 16px;
    top: 16px;

    @include fullscreenStyle;
  }

  @include listStyle;

  .list {
    padding: 0;
  }

  .item {
    position: relative;
    padding: 16px;
    transition: background-color 0.3s;
    border-radius: 4px;
    cursor: default;

    ::v-deep .anticon {
      position: absolute;
      top: 4px;
      right: 4px;
      transition: color 0.3s;

      &:hover {
        color: #437dff;
      }
    }

    &.is-highlight {
      background-color: #fcf3de;
    }
  }

  .item-content {
    font-size: 14px;

    ::v-deep {
      li {
        margin-top: 4px;
      }
    }
  }

  ::v-deep .ant-drawer-content-wrapper {
    box-shadow: none;
    border-left: 1px solid #e0e0e0;
  }
}
</style>

<style lang="scss">
.anchor-preview-modal__custom-style {
  max-height: calc(100vh - 64px);
}
</style>
