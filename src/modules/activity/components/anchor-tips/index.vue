<template>
  <div class="AnchorTips">
    <Drawer
      v-if="!modal.visible"
      ref="drawer"
      :visible="_visible"
      :links="links"
      v-bind="$attrs"
      @fullscreen="fullscreen"
    />

    <Modal ref="modal" :visible.sync="modal.visible" :data="links" />
  </div>
</template>

<script>
import Drawer from './drawer.vue'
import Modal from './modal.vue'

export default {
  name: 'AnchorTips',
  components: { Drawer, Modal },
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    links: {
      type: Array,
      required: true,
      validator(links) {
        // 确保每一个都有 title / content / target
        return links.every((link) => ['content', 'target'].every((field) => hasOwnProperty.call(link, field)))
      }
    }
  },
  data() {
    return {
      modal: {
        visible: false
      }
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(v) {
        this.$emit('update:visible', v)
      }
    }
  },
  methods: {
    fullscreen() {
      this.modal.visible = true
    }
  }
}
</script>
