<template>
  <a-modal
    dialog-class="anchor-tips-modal-container"
    :visible="_visible"
    title=""
    :width="800"
    :footer="null"
    :closable="false"
    @cancel="handleCancel"
  >
    <div v-if="$slots.customTitle" slot="title">
      <slot name="customTitle" />
    </div>

    <a-icon type="fullscreen-exit" class="non-fullscreen" @click="handleCancel" />

    <slot name="modalContent">
      <div class="list">
        <div v-for="(item, index) of data" :key="index" class="item">
          <!-- <div class="item-title">{{ item.title }}</div> -->
          <MarkdownText class="markdown-content" :text="item.content || item" />
        </div>
      </div>
    </slot>
  </a-modal>
</template>

<script>
import MarkdownText from '@activity/components/MarkdownText.vue'

export default {
  name: 'Modal',
  components: {
    MarkdownText
  },
  props: {
    visible: {
      type: <PERSON>olean,
      required: true
    },
    data: {
      type: Array,
      required: true
    }
  },
  data() {
    return {}
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(v) {
        this.$emit('update:visible', v)
      }
    }
  },
  methods: {
    handleCancel() {
      this._visible = false
    }
  }
}
</script>

<style lang="scss">
@import './common.scss';
@import '../../../../../src/assets/css/lib/mixins';

.anchor-tips-modal-container {
  .ant-modal-content {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
  }

  .ant-modal-body {
    position: relative;
    max-height: calc(100vh - 350px);
    margin: 20px 0;
    padding: 0 20px !important;

    @include mixin-hover-display-scrollbar;
  }

  .non-fullscreen {
    position: sticky;
    float: right;
    top: 0px;
    font-size: 18px;
    color: rgba(0, 0, 0, 0.85);

    @include fullscreenStyle;
  }

  @include listStyle;

  .markdown-content {
    font-weight: 400;
    font-size: 14px;
    line-height: 150%;
    color: rgba(0, 0, 0, 0.85);
  }
}
</style>
