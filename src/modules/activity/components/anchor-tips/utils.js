export const getDefaultContainer = () => {
  return window
}

export const isDomElement = (element) => {
  return element && typeof element === 'object' && 'nodeType' in element
}

export const isWindow = (obj) => {
  return obj !== null && obj !== undefined && obj === obj.window
}

export const addEventListener = (target, eventType, cb) => {
  if (target?.addEventListener) {
    target.addEventListener(eventType, cb)
  }

  return {
    remove() {
      if (target?.removeEventListener) {
        target.removeEventListener(eventType, cb)
      }
    }
  }
}

// (HTMLElement | Window | Document | null, boolean): number
export const getScroll = (target, isTop) => {
  let result = 0
  const method = top ? 'scrollTop' : 'scrollLeft'

  if (isWindow(target)) {
    result = target[isTop ? 'pageYOffset' : 'pageXOffset']
  } else if (target instanceof Document) {
    result = target.documentElement[method]
  } else if (target) {
    result = target[method]
  }
  if (target && !isWindow(target) && typeof result !== 'number') {
    result = (target.ownerDocument || target.documentElement)?.[method]
  }

  return result
}

function easeInOutCubic(t, b, c, d) {
  const cc = c - b
  t /= d / 2
  if (t < 1) {
    return (cc / 2) * t * t * t + b
  }
  return (cc / 2) * ((t -= 2) * t * t + 2) + b
}

export const scrollToEle = (y, options = {}) => {
  const { getContainer = () => window, callback, duration = 450 } = options
  const container = getContainer()
  const scrollTop = getScroll(container, true)
  const startTime = Date.now()

  const frameFunc = () => {
    const timestamp = Date.now()
    const time = timestamp - startTime
    const nextScrollTop = easeInOutCubic(time > duration ? duration : time, scrollTop, y, duration)
    if (isWindow(container)) {
      container.scrollTo(window.pageXOffset, nextScrollTop)
    } else if (container.constructor.name === 'HTMLDocument') {
      container.documentElement.scrollTop = nextScrollTop
    } else {
      container.scrollTop = nextScrollTop
    }
    if (time < duration) {
      requestAnimationFrame(() => {
        frameFunc()
      })
    } else if (typeof callback === 'function') {
      callback()
    }
  }

  frameFunc()
}
