<template>
  <div class="video-upload" style="display: inline-block">
    <a-select
      v-model="discount"
      :placeholder="$t('114447')"
      :filter-option="true"
      allow-clear
      style="width: 440px"
    >
      <a-select-option v-for="item in 101" :key="item - 1" :value="String(item - 1)">
        {{ item - 1 }}
      </a-select-option>
    </a-select>
    <a-button type="link" size="small" @click="handlePreviewBanner">{{ $t('114449') }}</a-button>

    <a-modal
      :visible.sync="dialogVisible"
      :footer="null"
      @cancel="
        () => {
          dialogVisible = false
        }
      "
    >
      <div v-if="currentConfig">
        <div class="desktop">
          <h3>{{ $t('29633') }}</h3>
          <Crop v-model="currentConfig.desktop" class="photo-edit-image" :need-crop="false" />
        </div>
        <div class="mobile">
          <h3>{{ $t('29634') }}</h3>
          <Crop v-model="currentConfig.mobile" class="photo-edit-image" :need-crop="false" />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import Crop from '@activity/components/photo-group/src/crop'

const G_IMG_PREFIX = 'https://res.klook.com/image/upload/'
const G_IMG_FOLDER = 'activities/'

export default {
  name: 'BannerDiscount',

  components: { Crop },

  inject: ['getPhotoList'],

  model: {
    prop: 'value',
    event: 'change'
  },

  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      dialogVisible: false,
      reviewImg: {},
      currentConfig: null
    }
  },

  computed: {
    discount: {
      get() {
        return this.value?.discount || undefined
      },
      set(val) {
        let changeObj = undefined
        // 用户手动切换过重新生成图片
        if (val) {
          changeObj = {
            ...this.value, // 保留原始值
            discount: val,
            discount_images: this.handleComputedUrl(val)
          }
        }
        this.$emit('change', changeObj)
      }
    },

    discountText() {
      return this.value?.discount_text || 'Up to {discount}% off'
    }
  },

  methods: {
    handleComputedUrl(discount = this.discount) {
      this.currentConfig = null

      const photebanner = this.getPhotoList('photo_banner')
      if (photebanner.length && discount) {
        const name = photebanner[0].name || ''
        const originPrefix = G_IMG_PREFIX + (discount === '0' ? '' : this.handleGetText(discount))
        const folderName = G_IMG_FOLDER + name

        this.currentConfig = {
          origin: originPrefix + folderName,
          desktop:
            originPrefix + photebanner[0].DESKTOP.pre_process + 'q_80,c_fill,ar_16:9,w_400/' + folderName,
          mobile: originPrefix + photebanner[0].MOBILE.pre_process + 'q_80,c_fill,ar_4:3,w_400/' + folderName
        }
      }
      return this.currentConfig?.origin || ''
    },

    handleGetText(discount) {
      if (discount === '0') return ''
      // 文案位置固定，限制原图尺寸与设计稿一直
      // 原图基于 1920*1080
      // 坐标：基于图片中心文字中心进行偏移
      // x: (1920/2) - (370 + 662/2) = 259，临时调整为满足 1080 * 1080 能展示全，向右移动 50 = 209
      // y: (1080/2) - (452 + 124/2) = 26
      const font = 'Poppins_75_bold_center'
      const text = encodeURIComponent(this.discountText.replace('{discount}', discount))
      const color = 'fff'
      const x = -209
      const y = -26
      return `l_text:${font}:${text},co_rgb:${color},x_${x},y_${y}/`
    },

    handlePreviewBanner() {
      this.handleComputedUrl()
      if (!_.isEmpty(this.currentConfig)) {
        this.dialogVisible = true
      } else {
        this.$modal.error({
          content: this.$t('115832')
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.photo-edit-image {
  border-radius: 4px;
  height: 118px;
  width: 270px;
}
</style>
