<template>
  <a-form-model ref="form" :model="form" :colon="false">
    <DescMarkdownContent
      class="strong-tips"
      placement="right"
      :is-inline="false"
      :desc="schemaConfig && schemaConfig.strongTips"
      :line-clamp="3"
    />
    <CountryCitySelection
      v-bind="form"
      :country-label="$t('act_binding_destination_departure_country_or_region')"
      :city-label="$t('act_binding_destination_departure_city')"
      :multiple-city="multipleDepartureCity"
      :multiple-country="multipleDepartureCountry"
      :country-options="countryOptions"
      :required="isRequired"
      :disabled="disabled"
      class="common-tpl-basic-style"
      @change="handleChange"
    >
    </CountryCitySelection>
  </a-form-model>
</template>

<script>
import countryCityMixin from '@activity/components/binded-area/mixins/index.js'
export default {
  name: 'ActivityDeparture',
  mixins: [countryCityMixin],
  props: {
    areaData: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    multipleDepartureCity() {
      return this.schemaConfig?.support_multi_departure_city ?? false
    },
    multipleDepartureCountry() {
      return false
    }
  },
  methods: {
    initData(areaData = []) {
      const countryId = [...new Set(areaData.map((item) => item.country?.area_id ?? ''))]
      const cityId = [...new Set(areaData.map((item) => item.city?.area_id ?? ''))]
      this.initForm({
        country_id: this.multipleDepartureCountry ? countryId : countryId[0],
        city_id: this.multipleDepartureCity ? cityId : cityId[0]
      })
    },
    getAreaData() {
      const data = this.getData()
      return [
        {
          departure_id_list: data,
          area_type: 1
        }
      ]
    }
  }
}
</script>

<style scoped lang="scss">
.region-binding-2-header {
  &:after {
    content: '';
    display: inline-block;
    border-bottom: 3px solid #e0e0e0;
    padding-bottom: 10px;
    position: relative;
    left: 120px;
    width: calc(100% - 180px);
  }
}
</style>
