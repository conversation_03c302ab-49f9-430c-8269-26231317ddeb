<template>
  <a-form-model ref="form" :model="form" :colon="false">
    <DescMarkdownContent
      class="strong-tips"
      placement="right"
      :is-inline="false"
      :desc="schemaConfig && schemaConfig.strongTips"
      :line-clamp="3"
    />
    <template v-if="specialBinging">
      <a-form-model-item
        :label="$t('taxonomy_destination_destination')"
        prop="dest_ids"
        :rules="destIdsRequired"
        class="common-tpl-basic-style"
      >
        <ShimAntdTooltip v-bind="$attrs" :title="$attrs.description">
          <a-select
            v-model="form.dest_ids"
            :placeholder="$t('type_to_search')"
            allow-clear
            mode="multiple"
            :disabled="disabled"
            @change="$emit('changeData')"
          >
            <template v-for="area in destinations">
              <a-select-option
                v-for="{ id, locales } in area.destinations"
                :key="id"
                :label="`${id} - ${getName(locales)}`"
                :value="id"
              >
                {{ `${id} - ${getName(locales)}` }}
              </a-select-option>
            </template>
          </a-select>
        </ShimAntdTooltip>
      </a-form-model-item>
    </template>
    <CountryCitySelection
      v-else
      v-bind="form"
      :multiple-city="true"
      :multiple-country="multipleCountry"
      :country-options="countryOptions"
      :required="isRequired"
      :disabled="disabled"
      class="common-tpl-basic-style"
      @change="handleChange"
    />
  </a-form-model>
</template>

<script>
import countryCityMixin from '@activity/components/binded-area/mixins/index.js'
import ShimAntdTooltip from '@activity/components/shimAntdTooltip'

export default {
  name: 'ActivityDestination',
  components: {
    ShimAntdTooltip
  },
  mixins: [countryCityMixin],
  props: {
    areaData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      destinations: []
    }
  },
  computed: {
    multipleCountry() {
      return this.supportMultiDest
    },
    supportMultiDest() {
      return this.schemaConfig?.support_multi_dest ?? false
    },
    specialBinging() {
      const destination_type = this.schemaConfig?.destination_type ?? 0
      return destination_type === 1
    },
    destIdsRequired() {
      return {
        required: this.isRequired,
        message: 'Please fill this form'
      }
    }
  },
  async created() {
    if (this.specialBinging) {
      this.destinations = await ajax.get(ADMIN_API.act.getSpuDestinations)
    }
  },
  methods: {
    initData(areaData = {}) {
      const { area_items = [], dest_ids = [] } = areaData
      const countryId = [...new Set(area_items.map((item) => item.country?.area_id ?? ''))]
      const cityId = [...new Set(area_items.map((item) => item.city?.area_id ?? ''))]
      this.initForm({
        country_id: this.multipleCountry ? countryId : countryId[0],
        city_id: cityId,
        dest_ids
      })
    },
    getAreaData() {
      if (this.specialBinging) {
        return {
          binded_area_info: [],
          dest_ids: this.form.dest_ids
        }
      }
      const data = this.getData()
      const post = {
        binded_area_info: [
          {
            area_id_list: data,
            area_type: 1
          }
        ],
        dest_ids: []
      }
      return post
    },
    getName(locales) {
      return _.get(
        locales.find((v) => v.language === 'en_US'),
        'name',
        'Invalid Name'
      )
    }
  }
}
</script>

<style scoped lang="scss">
.region-binding-2-header {
  &:after {
    content: '';
    display: inline-block;
    border-bottom: 3px solid #e0e0e0;
    padding-bottom: 10px;
    position: relative;
    left: 120px;
    width: calc(100% - 180px);
  }
}
</style>
