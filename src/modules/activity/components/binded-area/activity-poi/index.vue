<template>
  <div class="activity-poi-wrap">
    <DescMarkdownContent
      class="strong-tips"
      placement="right"
      :is-inline="false"
      :desc="schemaConfig && schemaConfig.strongTips"
      :line-clamp="3"
    />
    <PoiSelection
      v-model="poiValue"
      :disabled="disabled"
      :poi-options="poiOptions"
      :required="isRequired"
      :activity-id="activityId"
      :country_id="countryId"
      class="common-tpl-basic-style"
      @changeData="$emit('changeData')"
    />
  </div>
</template>
<script>
import PoiSelection from '@activity/components/binded-area/components/poi-selection/index.vue'
import DescMarkdownContent from '@activity/pages/activityManagement/detailV2/components/descMarkdownContent'

export default {
  name: 'ActivityPOI',
  components: {
    PoiSelection,
    DescMarkdownContent
  },
  props: {
    poiData: {
      type: Array,
      default: () => []
    },
    schemaConfig: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    },
    activityId: {
      type: Number,
      default: 0
    },
    countryId: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      poiValue: []
    }
  },
  computed: {
    poiOptions() {
      return this.initOptions(this.poiData || [])
    },
    isRequired() {
      return this.schemaConfig?.required ?? false
    }
  },
  watch: {
    poiData: {
      immediate: true,
      deep: true,
      handler(v) {
        this.poiValue = this.initOptions(v || [])
      }
    }
  },
  methods: {
    initOptions(list = []) {
      return list.map((item) => ({
        ...(item.poi || {}),
        city_name: item.city?.area_name ?? '',
        country_name: item.city?.area_name ?? '',
        id: item.area_id,
        title: item.area_name
      }))
    },
    getAreaData() {
      return (this.poiValue || []).map((item) => {
        const { poi_id, location = '', place_id = '' } = item
        return { place_id, location, poi_id }
      })
    }
  }
}
</script>
