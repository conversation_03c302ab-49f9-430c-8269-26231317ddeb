<template>
  <div>
    <a-form-model-item
      :prop="countryField"
      :label="countryLabel"
      :rules="[
        {
          required: required,
          message: 'Please select',
          validator: validatorFn,
          trigger: 'blur'
        }
      ]"
    >
      <a-tooltip placement="right" overlay-class-name="common-tooltip-style" :title="$attrs.description">
        <lazy-long-select
          v-model="form[countryField]"
          opt-value-key="id"
          opt-label-key="name"
          :select-hint="$t('type_to_search')"
          :ops-length="50"
          :multiple="multipleCountry"
          :full-opts="countryOptions"
          :fmt-label-fn="fmtLabelFn"
          :disabled="disabled"
          @change="changeCountry"
        />
      </a-tooltip>
    </a-form-model-item>
    <template v-if="showCity">
      <a-form-model-item
        :prop="cityField"
        :label="cityLabel"
        :rules="[
          {
            required: required,
            message: 'Please select',
            validator: validatorFn,
            trigger: 'blur'
          }
        ]"
      >
        <a-tooltip placement="right" overlay-class-name="common-tooltip-style">
          <lazy-long-select
            v-model="form[cityField]"
            opt-value-key="id"
            opt-label-key="name"
            :select-hint="$t('type_to_search')"
            :disabled="$attrs.disabledCity || disabled || !form[countryField]"
            :multiple="multipleCity"
            :ops-length="50"
            :full-opts="cityOptions"
            :fmt-label-fn="fmtLabelFn"
            @change="changeCity"
          />
          <template slot="title">
            <slot name="city_note" />
          </template>
        </a-tooltip>
      </a-form-model-item>
    </template>
    <slot name="tips" />
    <slot name="more" :form="form" />
  </div>
</template>

<script>
import LazyLongSelect from '@activity/components/lazy-long-select'
export default {
  name: 'SelectionCountryCity',
  components: { LazyLongSelect },
  inheritAttrs: false,
  props: {
    countryLabel: {
      type: String,
      default: __('act_binding_destination_destination_country_or_region')
    },
    cityLabel: {
      type: String,
      default: __('product_list_page_destination')
    },
    multipleCountry: {
      type: Boolean,
      default: false
    },
    multipleCity: {
      type: Boolean,
      default: false
    },
    countryField: {
      type: String,
      default: 'country_id'
    },
    cityField: {
      type: String,
      default: 'city_id'
    },
    showCity: {
      type: Boolean,
      default: true
    },
    required: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      cityOptions: [],
      form: {}
    }
  },
  computed: {
    countryOptions() {
      const country_id = this.$attrs['country_id'] || []
      const countryList = Array.isArray(country_id) ? country_id : [country_id]
      const options = _.cloneDeep(this.$attrs['country-options'] || [])
      // 排序，让当前选中的选项排在前面，能正常显示出来
      return options.sort((item) => {
        return countryList.includes(item.id) ? -1 : 1
      })
    }
  },
  watch: {
    $attrs: {
      handler() {
        this.$set(this, 'form', {
          [this.countryField]: this.$attrs[this.countryField],
          [this.cityField]: this.$attrs[this.cityField]
        })
      },
      deep: true,
      immediate: true
    },
    country_id: {
      handler() {
        this.changeCountry()
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    fmtLabelFn(data) {
      return `${data.id} - ${data.full_name}`
    },
    validatorFn: (rule, value, callback) => {
      if (
        rule.required &&
        (value === '' || (Array.isArray(value) && value.length === 0) || value === undefined)
      ) {
        callback(new Error('Please select'))
      } else {
        callback()
      }
    },
    async changeCountry() {
      let countryVal = this.form[this.countryField]
      if (countryVal) {
        if (!Array.isArray(countryVal)) {
          countryVal = [countryVal]
        }

        if (this.showCity) {
          this.cityOptions = (
            await Promise.all(
              countryVal.map((item) => {
                return ajax.get(ADMIN_API.act.get_all_cities_under_id, {
                  params: { id: item }
                })
              })
            )
          ).reduce((acc, curr) => [...acc, ...(curr || [])], [])
          this.$emit('updateCitys', _.cloneDeep(this.cityOptions))

          let cityIds = this.cityOptions.map((item) => item.id)
          if (this.multipleCity) {
            if (Array.isArray(this.form[this.cityField])) {
              this.$nextTick(() => {
                this.$set(
                  this.form,
                  this.cityField,
                  this.form[this.cityField].filter((item) => cityIds.includes(item))
                )
              })
              // 多选的时候需要更新 city 数据
              this.$emit('change', {
                [this.cityField]: this.form[this.cityField]
              })
            }
          } else {
            if (!cityIds.includes(this.form[this.cityField])) {
              this.$emit('change', {
                [this.cityField]: undefined
              })
            }
          }
        }
      } else {
        // clear country selected
        this.$emit('change', {
          [this.cityField]: this.multipleCity ? [] : undefined
        })
      }

      this.$emit('change', {
        [this.countryField]: this.form[this.countryField]
      })
    },
    changeCity() {
      this.$emit('change', {
        [this.cityField]: this.form[this.cityField]
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
