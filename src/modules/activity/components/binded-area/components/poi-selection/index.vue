<template>
  <div class="poi-selection-wrap">
    <a-form-model-item
      :colon="false"
      :label="labelText"
      prop="activity_poi"
      :rules="[
        {
          required: required,
          message: 'Please select',
          validator: validator,
          trigger: 'blur'
        }
      ]"
    >
      <a-tooltip placement="right" overlay-class-name="common-tooltip-style" :title="$attrs.description">
        <a-select
          v-model="ids"
          remote
          show-search
          allow-clear
          mode="multiple"
          :disabled="disabled"
          :placeholder="$t('type_to_search')"
          :filter-option="false"
          @search="remoteMethod"
          @change="onChange"
        >
          <template v-if="notGroup">
            <a-select-option
              v-for="item in options"
              :key="item.id"
              :value="item.id"
              :label="item.name"
              class="act-poi-option"
              :disabled="item.special"
              :class="{ option__special: item.special }"
            >
              <OverflowTextTooltip class="overflow-text gap">
                <span class="poi_option_item__title">{{ item.poi_id }} - {{ item.title }}</span>
              </OverflowTextTooltip>
              <OverflowTextTooltip v-if="item.city_name || item.country_name" class="overflow-text">
                <span class="poi_option_item__description">{{ item.city_name }},{{ item.country_name }}</span>
              </OverflowTextTooltip>
            </a-select-option>
          </template>
          <template v-else>
            <a-select-opt-group v-for="(value, key) of options" :key="key" :label="textId[key]">
              <a-select-option
                v-for="item in value"
                :key="item.id"
                :value="item.id"
                :label="item.name"
                class="act-poi-option"
                :disabled="item.special"
                :class="{ option__special: item.special }"
              >
                <OverflowTextTooltip class="overflow-text gap">
                  <span class="poi_option_item__title">{{ item.poi_id }} - {{ item.title }}</span>
                </OverflowTextTooltip>
                <OverflowTextTooltip v-if="item.city_name || item.country_name" class="overflow-text">
                  <span class="poi_option_item__description">
                    {{ item.city_name }},{{ item.country_name }}
                  </span>
                </OverflowTextTooltip>
              </a-select-option>
            </a-select-opt-group>
          </template>
        </a-select>
      </a-tooltip>
    </a-form-model-item>
  </div>
</template>

<script>
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'

export default {
  name: 'BindedAreaPoiSelection',
  components: {
    OverflowTextTooltip
  },
  inheritAttrs: false,
  model: {
    prop: 'poiValue',
    event: 'change'
  },
  props: {
    poiValue: {
      type: Array,
      default: () => []
    },
    labelText: {
      type: String,
      default: __('act_binding_destination_poi')
    },
    poiOptions: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    },
    activityId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      options: [],
      notGroup: true,
      allOptions: []
    }
  },
  computed: {
    ids: {
      get() {
        return (this.poiValue || []).map((item) => item.poi_id)
      },
      set(v) {
        const options = this.allOptions
        const value = options.reduce((acc, curr) => {
          const { poi_id } = curr
          if (v.includes(poi_id) && !acc.find((item) => item.poi_id === poi_id)) {
            acc.push({ ...curr })
          }
          return acc
        }, [])
        this.$emit('change', value)
      }
    },
    countryId() {
      const country_id = this.$attrs?.country_id ?? []
      const list = Array.isArray(country_id) ? country_id : [country_id]
      return list.join(',')
    },
    textId() {
      return {
        most_recommended: this.$t('102291'),
        others: this.$t('102293')
      }
    }
  },
  watch: {
    poiOptions: {
      immediate: true,
      deep: true,
      handler() {
        this.options = this.poiOptions || []
        this.allOptions = [...this.poiOptions]
      }
    }
  },
  methods: {
    remoteMethod: _.debounce(async function (query) {
      const str = query.trim()
      if (str) {
        const result = await ajax.getBody(ADMIN_API.act.get_activity_poi_list, {
          params: {
            query: str,
            language: klook.getAPILang(),
            area_id_list: this.countryId,
            activity_id: this.activityId
          }
        })
        const list = (result?.result?.results ?? []).reduce((acc, item) => {
          const { group, poi_id } = item
          const data = { ...item, id: poi_id }
          if (acc[group]) {
            acc[group].push(data)
          } else {
            acc[group] = [data]
          }
          return acc
        }, {})
        const values = Object.values(list)
        this.allOptions = [...this.allOptions, ...values.flat()]
        if (values.length > 1) {
          this.notGroup = false
          this.$set(this, 'options', list)
        } else {
          this.notGroup = true
          this.$set(this, 'options', values?.[0] ?? [])
        }
      }
    }, 300),
    onChange() {
      this.$emit('changeData')
    },
    validator(rule, value, callback) {
      if (!this.required || (Array.isArray(this.poiValue) && this.poiValue.length)) {
        return callback()
      }
      return callback(new Error(this.$t('global_please_select')))
    }
  }
}
</script>

<style scoped lang="scss">
@import '../../../../pages/package/itinerary/mixins.scss';
@include mixin-poi-option-item;

.gap {
  margin-right: 8px;
}

.act-poi-option {
  height: fit-content;
}
</style>
