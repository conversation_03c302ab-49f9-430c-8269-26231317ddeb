import DescMarkdownContent from '@activity/pages/activityManagement/detailV2/components/descMarkdownContent'
import CountryCitySelection from '@activity/components/binded-area/components/country-city-selection/index.vue'

export default {
  components: {
    DescMarkdownContent,
    CountryCitySelection
  },
  props: {
    schemaConfig: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    },
    countryOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {}
    }
  },
  computed: {
    isRequired() {
      return !!this.schemaConfig.required
    }
  },
  watch: {
    areaData: {
      immediate: true,
      deep: true,
      handler(v) {
        this.initData(v)
      }
    }
  },
  methods: {
    getData() {
      const city_id = this.form.city_id
      if (!city_id) {
        return []
      }
      return Array.isArray(city_id) ? city_id : [city_id]
    },
    initForm(form) {
      this.form = this.$set(this, 'form', form)
    },
    handleChange(value) {
      const newForm = { ...this.form, ...value }
      const isChange = !_.isEqual(this.form, newForm)
      this.form = newForm
      this.$nextTick(() => {
        isChange && this.$refs.form && this.$refs.form.validate()
        isChange && this.$emit('changeData')
      })
      this.$emit('change', this.form)
    },
    validateForm() {
      return new Promise((resolve) => this.$refs.form.validate((valid) => resolve(valid)))
    }
  }
}
