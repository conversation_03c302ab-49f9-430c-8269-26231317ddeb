<!--
     this can not be used seperately,
     must specify a <a-checkbox-group> on top of this component to
     avoid el-checkbox get highjacked by higher-level el-checkbox-group
     due to element-ui internal realization using `dispatch`
     can refer to how this is used in global-picker component

   -->
<template>
  <span>
    <a-checkbox
      type="checkbox"
      :indeterminate="isIndeterminate"
      name="popup-checkbox"
      :value="parent_label"
      >{{ parent_name }}</a-checkbox
    >
    <a-icon class="cities-pop" type="caret-down" @click="showPop" />
    <a-drawer
      v-if="visible"
      :title="parent_name"
      width="320"
      :closable="false"
      :visible="visible"
      :key="parent_label"
      @close="visible = false"
    >
      <a-spin :spinning="spinning" :delay="500" tip="Loading...">
        <a-input-search
          placeholder="input search text"
          style="width: 250px; margin-bottom: 12px;"
          v-model="searchValue"
          @search="debounceSearch"
        ></a-input-search>

        <a-checkbox-group
          class="cities-list"
          v-if="visible"
          v-model="checked_items"
        >
          <a-checkbox
            class="cities-item"
            v-for="option in chosenResult"
            :key="option.city_id"
            :value="option.city_id"
            >{{ option.city_name }}</a-checkbox
          >
        </a-checkbox-group>
      </a-spin>
    </a-drawer>
  </span>
</template>

<script>
import Fuse from 'fuse.js'

export default {
  name: 'CityCheckbox',
  install(Vue) {
    // https://vuejs.org/v2/guide/plugins.html
    Vue.component(this.name, this)
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: ['value', 'parent_name', 'parent_label', 'language'],
  data() {
    return {
      options: [],
      chosenResult: [],
      // checked_items: [],
      checkAll: false,
      visible: false,
      spinning: true,
      searchValue: ''
    }
  },
  watch: {
    searchValue() {
      this.debounceSearch()
    }
  },
  computed: {
    isIndeterminate() {
      return (
        this.checked_items.length > 0 &&
        this.checked_items.length < this.options.length
      )
    },
    checked_items: {
      get() {
        return this.value
      },
      set(v) {
        if (!_.isEqual(v, this.checked_items)) {
          this.$emit('change', v)
        }
      }
    }
  },
  methods: {
    debounceSearch: _.debounce(function() {
      if (this.searchValue.trim()) {
        if (!this.fuse) {
          this.fuse = new Fuse(this.options, {
            tokenize: true,
            matchAllTokens: true,
            minMatchCharLength: 2,
            keys: ['city_id', 'city_name']
          })
        }

        this.chosenResult = this.fuse.search(this.searchValue)
      } else {
        this.chosenResult = this.options
      }
    }, 1000 / 60),
    async showPop() {
      this.visible = true
      let country_id = this.parent_label
      let result = await ajax.get(ADMIN_API.act.get_all_cities_under_id, {
        params: {
          language: this.language,
          id: country_id
        }
      })

      this.$emit('get_options', result)
      this.options = result.map((v) => ({
        city_name: v.name,
        city_id: v.id
      }))

      this.debounceSearch()
      this.spinning = false
    }
  }
}
</script>

<style lang="scss" scoped>
.cities-pop {
  line-height: 2;
}

.cities-list {
  display: flex;
  flex-wrap: wrap;
}

.cities-item {
  width: 100%;
  padding: 12px 0;
  margin-left: 0;
}
</style>
