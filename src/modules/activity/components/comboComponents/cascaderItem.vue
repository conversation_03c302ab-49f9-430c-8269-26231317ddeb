<template>
  <a-cascader
    :value="value"
    :options="options"
    :allow-clear="isPkg"
    class="cascader-select_base"
    :placeholder="$t('73098')"
    :disabled="readOnly"
    :show-search="isPkg ? showSearch : false"
    :change-on-select="false"
    popup-class-name="popup-class-name__item"
    :class="[className]"
    :display-render="displayRender"
    :not-found-content="$t('73099')"
    @change="change"
    @search="search"
  >
  </a-cascader>
</template>
<script>
export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    options: {
      type: Array,
      default: () => []
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    optionsItemLength: {
      type: Number,
      default: 1
    },
    className: {
      type: String,
      default: 'default-cascader'
    },
    cascaderType: {
      type: String,
      default: 'sku'
    }
  },
  data() {
    return {
      showSearch: {
        filter: this.searchFilter,
        sort: (a, b, v) => {
          const [item = {}] = a
          return item.activity_name.indexOf(v) > -1 || (item.activity_id + '').indexOf(v) > -1 ? -1 : 1
        },
        render: this.searchRender,
        matchInputWidth: true
      },
      modelValue: []
    }
  },
  computed: {
    isPkg() {
      return this.cascaderType === 'pkg'
    }
  },
  methods: {
    searchRender({ inputValue, path }) {
      const optionsItemLength = this.optionsItemLength
      const vn = path.map((item, index) => {
        return (
          <div class={`display-render-filter-item display-render-filter-item_${optionsItemLength}`}>
            <div class={'display-render-item-con'}>{item.label}</div>
            <div class="display-render-item-line">{index === path.length - 1 ? '' : '/'}</div>
          </div>
        )
      })
      return <div class="display-render-filter-wrap">{vn}</div>
    },
    displayRender({ labels, selectedOptions }) {
      const optionsItemLength = this.optionsItemLength
      const vn = labels.map((item, index) => {
        return (
          <div class={`display-render-item display-render-item_${optionsItemLength}`}>
            <div class="display-render-item-con">{item}</div>
            <div class="display-render-item-line">{index === labels.length - 1 ? '' : '/'}</div>
          </div>
        )
      })
      return <div class="display-render-wrap">{vn}</div>
    },
    searchFilter(inputValue = '') {
      const v = inputValue.trim()
      const len = this.options.filter((item) => {
        return item.activity_name.indexOf(v) > -1 || (item.activity_id + '').indexOf(v) > -1
      }).length
      const flag = len ? true : false
      return flag
    },
    search(v) {
      this.$emit('search', v)
    },
    change(value, selectedOptions) {
      this.$emit('change', value, selectedOptions)
    }
  }
}
</script>

<style lang="scss">
.display-render-wrap {
  display: flex;
  width: 100%;
  .display-render-item {
    max-width: 50%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    &.display-render-item_3 {
      max-width: 33%;
    }
    .display-render-item-con {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
    }
    .display-render-item-line {
      padding: 0 2px;
    }
    &:last-child {
      flex: 1;
      max-width: 100%;
    }
  }
}
.popup-class-name__item {
  .ant-cascader-menu:only-child .ant-cascader-menu-item {
    max-width: 100%;
  }
  .ant-cascader-menu-item {
    max-width: 302px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.ant-cascader-picker {
  &.cascader-select_base {
    margin: 0 8px 8px 0;
  }

  &.default-cascader {
    width: 450px;
  }

  &.pkgs-cascader {
    width: 400px;
  }
}
.display-render-filter-wrap {
  display: flex;
  .display-render-filter-item {
    display: flex;
    .display-render-item-line {
      padding: 0 2px;
    }
    .display-render-item-con {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }
    &.display-render-filter-item_2 {
      max-width: 50%;
    }
  }
}
</style>
