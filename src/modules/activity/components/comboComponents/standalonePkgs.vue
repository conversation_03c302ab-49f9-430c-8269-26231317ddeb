<template>
  <div class="stan-outer-root">
    <div v-for="(item, index) in list" :key="index" class="stan-sku-wrapper" :class="{ flex: isSingleInput }">
      <component
        :is="(item.value && item.value.length >= 2) || !isCreate ? 'a-popover' : 'div'"
        placement="topLeft"
        :class="{ 'flex-div': !item.value || !item.value.length < 2 }"
      >
        <template slot="content">
          <span style="font-size: 14px">{{ getPopoverCotent(item.value) }}</span>
        </template>
        <cascaderItem
          v-model="item.value"
          :options="options"
          :options-item-length="2"
          class-name="pkgs-cascader"
          :read-only="disabled"
          cascader-type="pkg"
          @change="changeCascader"
          @search="debounceOnSearch"
        />
      </component>
      <div class="right-ipt-wrap">
        <a-input
          v-for="(lang, i) in item.i18ns"
          :key="i"
          v-model="lang.name"
          class="input"
          :class="{ half: item.i18ns.length > 1 }"
          :allow-clear="true"
          :placeholder="getPlaceholder(lang, item)"
          @focus="inputFocus(lang, item)"
          @change="inputChange(lang)"
        />
        <i v-if="index > 0 && !disabled" class="common-delete-btn" @click="deleteItem(index)">
          <svg-icon icon-name="trash" />
        </i>
        <div v-else class="common-delete-btn hide"></div>
      </div>
    </div>
    <a-button v-if="!disabled" :disabled="list.length > 4" type="link" class="add-item-btn" @click="addItem">
      <a-icon type="plus" />
      {{ $t('73104') }}
    </a-button>
  </div>
</template>

<script>
import { getComboNameI18ns } from '@activity/utils'
import cascaderItem from './cascaderItem'
export default {
  components: {
    cascaderItem
  },
  model: {
    prop: 'list',
    event: 'change'
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      options: [],
      popoverCotent: []
    }
  },
  computed: {
    isSingleInput() {
      return !this.list.some((item) => item.i18ns.length > 1)
    },
    isCreate() {
      return !this.$route.query.package_id
    }
  },
  async mounted() {
    if (!this.isCreate) {
      this.options = this.optionsGroupBy()
    }
  },
  methods: {
    inputChange(lang) {
      lang.lock = true
    },
    getPlaceholder(lang, item) {
      if (lang.lock || lang.name) {
        return `${lang.language}-${this.$t('73081')}`
      }
      const plc = this.getPopoverCotent(item.value, true)
      if (plc) {
        return plc
      }
      return `${lang.language}-${this.$t('73081')}`
    },
    optionsGroupBy() {
      let optionsObj = {}
      this.list.forEach((item, index) => {
        const { activity_id, activity_name, package_id, package_name } = item
        let opt = optionsObj[activity_id]
        if (opt) {
          opt.children.push({
            value: package_id,
            label: `${package_id}-${package_name}`,
            package_name: package_name
          })
        } else {
          optionsObj[activity_id] = {
            value: activity_id,
            label: `${activity_id}-${activity_name}`,
            children: [
              {
                value: package_id,
                label: `${package_id}-${package_name}`,
                package_name: package_name
              }
            ]
          }
        }
      })
      return Object.values(optionsObj)
    },
    inputFocus(lang, item) {
      lang.name = lang.name || lang.lock ? lang.name : this.getPopoverCotent(item.value, true)
      lang.lock = true
    },
    debounceOnSearch: _.debounce(async function(v) {
      await this.onSearch(v)
    }, 500),
    async onSearch(v) {
      const list = await this.getComboPackage(v)
      const options = _.cloneDeep(this.options)
      list.forEach((item) => {
        const [o] = options.filter((it) => it.activity_id === item.activity_id)
        if (!o) {
          this.options.unshift({ ...item })
        }
      })
    },
    async changeCascader(value, selectedOptions) {
      this.setSelectDisabled()
    },
    setSelectDisabled() {
      let valueList = []
      this.list.forEach((item) => {
        if (Array.isArray(item.value) && item.value.length === 2 && item.value.every((item) => !!item)) {
          valueList.push(item.value.join('_'))
        }
      })
      this.options = this.options.map((item) => {
        const itemValue = `${item.value}_`
        item.children = item.children.map((it) => {
          const itValue = itemValue + it.value
          it.disabled = valueList.includes(itValue)
          return it
        })
        return item
      })
    },
    getPopoverCotent(v, isPkg = false) {
      if (!Array.isArray(v) || v.length < 2 || v.some((item) => !item)) {
        return ''
      }
      let [p = { children: [] }] = this.options.filter((item) => item.value === v[0])
      let [p1 = { children: [] }] = p.children.filter((item) => item.value === v[1])
      return isPkg ? p1.package_name : [p.label, p1.label].join('/')
    },
    async getComboPackage(keyword = '') {
      let kv = keyword.trim()
      if (!kv) {
        return []
      }
      const result = await ajax.getBody(ADMIN_API.act.filter_standalone_packages, {
        params: {
          keyword: kv
        }
      })
      if (!result.success || !result.result) {
        return []
      }
      const list = result.result.item || []
      return list.map((item) => {
        item.label = `${item.activity_id}-${item.activity_name}`
        item.value = item.activity_id
        item.children = item.packages.map((it) => {
          it.label = `${it.package_id}-${it.package_name}`
          it.value = it.package_id
          return it
        })
        return item
      })
    },
    deleteItem(index) {
      this.list.splice(index, 1)
      this.setSelectDisabled()
    },
    addItem() {
      this.list.push({
        package_id: undefined,
        i18ns: getComboNameI18ns()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@media screen and (max-width: 1400px) {
  .stan-outer-root {
    .stan-sku-wrapper {
      .input {
        width: 400px !important;
        margin-bottom: 8px;
        &.half {
          width: 196px !important;
          margin-bottom: 0px;
        }
      }
    }
  }
}
.stan-outer-root {
  .stan-sku-wrapper {
    margin-bottom: 8px;
    &.flex {
      display: flex;
      align-items: flex-start;
      flex-wrap: wrap;
      margin-bottom: 0px;
    }
    .right-ipt-wrap {
      display: flex;
      align-items: flex-start;
    }
    .flex-div {
      display: flex;
    }
    .input {
      width: 400px;
      margin-right: 8px;
      box-sizing: border-box;
      ::v-deep .ant-input {
        width: 100%;
        padding-right: 22px;
      }
      ::v-deep .ant-input-suffix {
        right: 7px;
      }
    }
    .common-delete-btn {
      width: 24px;
      height: 24px;
      margin: 4px 8px 4px 0;
      &.hide {
        opacity: 0;
        cursor: default;
      }
    }
  }
  .add-item-btn {
    padding: 0;
  }
}
</style>
