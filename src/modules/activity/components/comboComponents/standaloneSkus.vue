<template>
  <div class="stan-outer-root">
    <div v-for="(item, index) in list" :key="index" class="stan-sku-wrapper">
      <component
        :is="getComponent(item.value)"
        placement="topLeft"
        :class="{ 'flex-div': !item.value || !item.value.length !== 3 }"
      >
        <template slot="content">
          <span style="font-size: 14px">{{ getPopoverCotent(item.value) }}</span>
        </template>
        <cascaderItem
          v-model="item.value"
          :options="options"
          :options-item-length="3"
          :read-only="readOnly || disabled"
          @change="changeCascader"
          @search="debounceOnSearch"
        />
      </component>
      <a-input-number
        v-model="item.amount"
        class="input"
        :precision="precision"
        :min="min"
        :max="formatterType === 1 ? getMax(item.sku_id) : max"
        :formatter="formatter.formatter"
        :parser="formatter.parser"
        :disabled="disabled"
      />
      <i v-if="index > 1 && !readOnly" class="common-delete-btn" @click="deleteItem(index)">
        <svg-icon icon-name="trash" />
      </i>
    </div>
    <a-button v-if="!readOnly" :disabled="list.length > 4" type="link" class="add-item-btn" @click="addItem">
      <a-icon type="plus" />
      {{ $t('73104') }}
    </a-button>
  </div>
</template>

<script>
import cascaderItem from './cascaderItem'
export default {
  inject: ['packageId'],
  components: {
    cascaderItem
  },
  model: {
    prop: 'list',
    event: 'change'
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    options: {
      type: Array,
      default: () => []
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    max: {
      type: Number,
      default: Infinity
    },
    min: {
      type: Number,
      default: 0
    },
    precision: {
      type: Number,
      default: 0
    },
    formatterType: {
      type: Number,
      default: -1
    },
    sellingPriceAmountLimit: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      popoverCotent: []
    }
  },
  computed: {
    formatter() {
      if (this.formatterType === 2) {
        return {
          formatter: (v) => {
            v = `${v}`.replace('%', '')
            v = v ? v : 0
            return v + '%'
          },
          parser: (v) => {
            v.replace('%', '')
            return v ? v : 0
          }
        }
      }
      return {
        formatter: (v) => v,
        parser: (v) => v
      }
    }
  },
  methods: {
    getMax(sku_id) {
      return this.sellingPriceAmountLimit[sku_id] || 0
    },
    getComponent(value) {
      const validate = value && value.length === 3 && value.every((item) => !!item)
      if (validate) {
        return 'a-popover'
      }
      return 'div'
    },
    debounceOnSearch: _.debounce(async function(v) {
      await this.onSearch(v)
    }, 500),
    async onSearch(v) {},
    async changeCascader(value, selectedOptions) {},
    getPopoverCotent(v) {
      if (!Array.isArray(v) || v.length < 3 || v.some((item) => !item)) {
        return
      }
      let [p = { children: [] }] = this.options.filter((item) => item.value === v[0])
      let [p1 = { children: [] }] = p.children.filter((item) => item.value === v[1])
      let [p2 = {}] = p1.children.filter((item) => item.value === v[2])
      return [p.label, p1.label, p2.label].join('/')
    },
    deleteItem(index) {
      if (this.disabled) {
        this.$message.info(this.$t('73250'))
        return
      }
      this.list.splice(index, 1)
    },
    addItem() {
      if (this.disabled) {
        this.$message.info(this.$t('73250'))
        return
      }
      this.list.push({
        value: [],
        amount: 1
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.stan-outer-root {
  .stan-sku-wrapper {
    display: flex;
    align-items: flex-start;
    .flex-div {
      display: flex;
    }
    .select {
      width: 302px;
      margin: 0 8px 8px 0;
    }
    .input {
      width: 90px;
    }
    .common-delete-btn {
      width: 24px;
      height: 24px;
      margin: 4px 8px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .add-item-btn {
    padding: 0;
  }
}
</style>
