<!--
    common-cb:
        checkbox group with a default `All` option

    usage:

        <common-cb v-model="city" :items="all_cities"></common-cb>

    options:
        `key` `value`

   -->

<template>
  <div class="common-cb">
    <a-checkbox
      v-if="showAll"
      v-model="checkAll"
      :indeterminate="isIndeterminate"
      :checked="checked"
      :class="{ align: align, 'check-all-button': true }"
    >
      {{ $t('act_lan_all') }}
    </a-checkbox>
    <!-- <div></div> -->
    <a-checkbox-group
      v-model="current_items"
      style="display:inline"
      :class="{ vertical: type === 'vertical' }"
    >
      <a-checkbox
        v-for="(item, index) in items"
        :key="item.key"
        :value="item.value"
        :class="{ align: align }"
        :label="item.key"
      >
        <slot :data="item" :name="index">
          {{ item.value }}
        </slot>
      </a-checkbox>
    </a-checkbox-group>
  </div>
</template>

<script>
export default {
  name: 'CommonCb',
  install(Vue) {
    // https://vuejs.org/v2/guide/plugins.html
    Vue.component(this.name, this)
  },
  model: {
    // https://vuejs.org/v2/guide/components.html#Form-Input-Components-using-Custom-Events
    prop: 'value', // this is actually bind to `current_items`
    event: 'change'
  },
  props: {
    type: {
      type: String,
      default: 'horizontal' // vertical || horizontal
    },
    items: {
      type: Array,
      default: () => []
    },
    value: {
      type: Array
    },
    checked: {
      type: Boolean,
      default: false
    },
    showAll: {
      type: Boolean,
      default: true
    },
    align: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    checkAll: {
      get() {
        return this.current_items.length === this.items.length
      },
      set(checked) {
        let value = checked ? this.items.map((ele) => ele.key) : []
        this.current_items = value
        this.$emit('change', value)
      }
    },
    current_items: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('change', value)
      }
    },
    isIndeterminate() {
      return (
        this.current_items.length > 0 &&
        this.current_items.length < this.items.length
      )
    }
  }
}
</script>

<style lang="scss">
.common-cb {
  .ant-checkbox-wrapper.align {
    display: inline-flex;
    width: 170px;
    margin-right: 15px;
    margin-bottom: 15px;
  }
  .ant-checkbox-wrapper.check-all-button {
    margin-right: 12px;
  }
  span.el-checkbox__label {
    word-break: break-word;
    width: 100%;
    white-space: normal;
    text-overflow: ellipsis;
    overflow: hidden;
    line-height: 1.3;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
  .ant-checkbox-group.vertical {
    .ant-checkbox-wrapper,
    .ant-checkbox-wrapper.align {
      margin-left: 0;
      width: 100%;
    }
  }
}
</style>
