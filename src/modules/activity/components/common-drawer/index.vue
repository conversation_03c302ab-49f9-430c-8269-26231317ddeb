<template>
  <a-drawer
    :wrap-class-name="'component-common-drawer ' + wrapClassName"
    :visible.sync="_visible"
    :width="width"
    :body-style="calcBodyStyle"
    v-bind="$attrs"
    @close="onClose"
  >
    <template #title>
      <slot v-if="$slots.title" name="title"></slot>
      <template v-else>{{ title }}</template>
    </template>
    <a-spin :spinning="loading">
      <div class="body">
        <slot></slot>
      </div>
    </a-spin>
    <footer v-if="hasFooterSlot" class="footer">
      <slot name="footer"></slot>
    </footer>
  </a-drawer>
</template>

<script>
export default {
  name: 'CommonDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '-'
    },
    width: {
      type: Number,
      default: 500
    },
    bodyStyle: {
      type: Object,
      default: () => ({})
    },
    wrapClassName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(v) {
        this.$emit('update:visible', v)
      }
    },
    hasFooterSlot() {
      return this.$slots.footer
    },
    calcBodyStyle() {
      return {
        margin: '55px 0 0',
        padding: '0',
        height: `calc(100% - 55px - ${this.hasFooterSlot ? '50px' : 0})`,
        ...this.bodyStyle
      }
    }
  },
  methods: {
    onClose() {
      this.$emit('close')
      this._visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.component-common-drawer {
  ::v-deep {
    .ant-spin-nested-loading,
    .ant-spin-container {
      height: 100%;
    }
  }

  .body {
    height: 100%;
    padding: 16px 24px;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .footer {
    position: absolute;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 12px;
    width: 100%;
    height: 50px;
    padding: 0 24px;
    border-top: 1px solid #e9e9e9;
    background-color: #fff;
    z-index: 3;
  }
}
</style>
