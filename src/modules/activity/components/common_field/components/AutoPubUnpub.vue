<template>
  <div class="auto-pub-unpub-container">
    <a-form-model-item
      prop="auto_pub_unpub.is_set"
      class="--fit-width"
      :rules="rules"
      :colon="false"
      :label="$t('auto_publish_unpublish')"
    >
      <a-tooltip placement="right" overlay-class-name="common-tooltip-style" :title="description">
        <a-switch v-model="form.auto_pub_unpub.is_set" :disabled="disabled" />
      </a-tooltip>
      <StrongTips :tips="tips" />
    </a-form-model-item>

    <template v-if="form.auto_pub_unpub.is_set">
      <!-- 发布 -->
      <a-form-model-item
        v-tooltip="{
          visible: publishBtnDisplayConf.disabled,
          content: publishBtnDisplayConf.content,
          placement: 'right'
        }"
        class="--fit-width"
        :label="$t('schedule_publish')"
        :colon="false"
      >
        <a-switch
          :default-checked="openPublished"
          :disabled="disabled || lockAutoPublish || publishBtnDisplayConf.disabled"
          @change="(check) => handleSwitch(check, 'published')"
        />
        <a-icon
          v-tooltip="{
            visible: true,
            content: $t('auto_publish_unpublish_tips_2'),
            placement: 'right'
          }"
          style="margin-left: 8px"
          type="info-circle"
          theme="filled"
        />
        <LocalTimePicker
          :key="pubDefaultPickerValue"
          v-model="form.auto_pub_unpub.published_time"
          type="datetime"
          class="form-width"
          :local_fmt="true"
          :show_zone="false"
          :disabled-date="disabledPubDate"
          :disabled="disabledPublishedPicker || publishBtnDisplayConf.disabled"
          :default-picker-value="pubDefaultPickerValue"
        />
      </a-form-model-item>
      <!-- 预热 -->
      <div
        v-if="displayWarmUp"
        v-tooltip="{
          visible: true,
          placement: 'right',
          content: warmUpTooltipContent,
          calcStyleFunc: calcStyleFunc
        }"
        class="warming-up-box"
      >
        <a-form-model-item class="--fit-width" :label="$t('79644')" :colon="false">
          <a-switch
            :default-checked="!!form.auto_pub_unpub.suspended"
            :disabled="disabled || lockAutoWarmUp || pkgIsWarmingUp"
            @change="(check) => handleSwitch(check, 'suspended')"
          />
          <a-icon
            v-tooltip="{
              visible: true,
              content: $t('80270'),
              placement: 'right'
            }"
            style="margin-left: 8px"
            type="info-circle"
            theme="filled"
          />
          <LocalTimePicker
            :key="suspendDefaultPickerValue"
            v-model="form.auto_pub_unpub.suspended_config.suspended_start_time"
            class="form-width"
            type="datetime"
            :disabled-date="disabledWarmingUpDate"
            :show_zone="false"
            :disabled="disabledSwarmingUpPicker"
            :local_fmt="true"
            :default-picker-value="suspendDefaultPickerValue"
          />
        </a-form-model-item>
      </div>
      <!-- 取消发布 -->
      <a-form-model-item
        v-tooltip="{
          visible: unpublishBtnDisplayConf.disabled,
          content: unpublishBtnDisplayConf.content,
          placement: 'right'
        }"
        class="--fit-width"
        :label="$t('schedule_unpublish')"
        :colon="false"
      >
        <a-switch
          :disabled="disabled || !packageId || unpublishBtnDisplayConf.disabled || pkgIsWarmingUp"
          :default-checked="!!form.auto_pub_unpub.unpublished"
          @change="(check) => handleSwitch(check, 'unpublished')"
        />
        <LocalTimePicker
          v-model="form.auto_pub_unpub.unpublished_time"
          class="form-width"
          type="datetime"
          :show_zone="false"
          :disabled-date="disabledUnpubDate"
          :disabled="
            !form.auto_pub_unpub.unpublished ||
              disabled ||
              !packageId ||
              unpublishBtnDisplayConf.disabled ||
              pkgIsWarmingUp
          "
          :local_fmt="true"
        />
      </a-form-model-item>
    </template>
  </div>
</template>

<script>
import { LocalTimePicker } from '@activity/components/index'
import StrongTips from '@activity/components/StrongTips.vue'
import moment from 'moment'

export default {
  name: 'AutoPubUnpub',
  components: {
    LocalTimePicker,
    StrongTips
  },
  model: {
    event: 'change',
    value: 'data'
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    description: {
      type: String,
      default: ''
    },
    lockAutoPublish: {
      type: Boolean,
      default: false
    },
    // 这个时候其实已经是在预热状态了（提前30分钟）
    lockAutoWarmUp: {
      type: Boolean,
      default: false
    },
    rules: {
      type: Object,
      default: () => ({})
    },
    tips: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  computed: {
    warmUpTooltipContent() {
      if (this.actIsSuspendStatus) {
        return this.$t('79636')
      }

      if (this.pkgIsGeneralSuspended || this.pkgIsWarmingUp) {
        return this.$t('79635')
      }

      return `<p style='margin-bottom: 6px;'>${this.$t(
        '79645'
      )}</p><img src='https://res.klook.com/image/upload/c_fill,w_400/activities/nvolxdb4xiu6cs0iyj2i.jpg' />`
    },
    packageId() {
      return this.$route.query.package_id
    },
    form: {
      set() {},
      get() {
        return this.data
      }
    },
    displayWarmUp() {
      const { published, published_time } = this.form.auto_pub_unpub

      return (
        [1, 2, 3, 5, 103].includes(this.$store.state?.categoryInfo?.template_id) &&
        published &&
        published_time
      )
    },
    openPublished() {
      return !!this.form.auto_pub_unpub.published
    },
    disabledPublishedPicker() {
      return this.disabled || this.lockAutoPublish || !this.openPublished
    },
    disabledSwarmingUpPicker() {
      return (
        !this.form.auto_pub_unpub.suspended ||
        this.disabled ||
        this.lockAutoPublish ||
        this.lockAutoWarmUp ||
        this.pkgIsWarmingUp
      )
    },
    actStatus() {
      // -1   无效
      // 0    未发布
      // 1    已发布
      // 2    草稿
      // 3    content to edit
      // 4    in preview
      // 5    暂停销售
      return this.$store.state.actItemData?.activity_status ?? -1
    },
    actIsSuspendStatus() {
      return this.actStatus === 5
    },
    packageStatus() {
      // -1 无效状态
      // 0  未发布
      // 1  已发布
      // 2  暂停销售
      // 3  暂停销售 (预热中)
      return this.$store.state.currentPackageFloatingField?.package_status ?? -1
    },
    pkgIsPublished() {
      return this.packageStatus === 1 && this.packageId
    },
    pkgIsUnpublished() {
      return this.packageStatus === 0
    },
    // 不包含预热的暂停
    pkgIsGeneralSuspended() {
      return this.packageStatus === 2
    },
    pkgIsWarmingUp() {
      return this.packageStatus === 3
    },
    publishBtnDisplayConf() {
      let content = ''
      const { actIsSuspendStatus, pkgIsGeneralSuspended } = this

      if (actIsSuspendStatus || pkgIsGeneralSuspended) {
        content = this.$t('80075')
      }

      return {
        disabled: !!content,
        content
      }
    },
    unpublishBtnDisplayConf() {
      let content = ''
      const { pkgIsGeneralSuspended, actIsSuspendStatus } = this

      if (actIsSuspendStatus || pkgIsGeneralSuspended) {
        content = this.$t('80075')
      }

      return {
        disabled: !!content,
        content
      }
    },
    pubDefaultPickerValue() {
      if (this.pkgIsWarmingUp || this.lockAutoWarmUp) {
        const suspendedStartTime = this.form.auto_pub_unpub.suspended_config.suspended_start_time
        const date = Math.max(+moment(suspendedStartTime).startOf('date'), +moment().startOf('day'))

        return moment(date).format('YYYY-MM-DD HH:mm:ss')
      }

      return ''
    },
    suspendDefaultPickerValue() {
      const published_time = this.form.auto_pub_unpub.published_time
      const date = Math.max(+moment(published_time).add(-7, 'days'), +moment().startOf('day'))

      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  watch: {
    'form.auto_pub_unpub.is_set'() {
      Object.assign(this.form.auto_pub_unpub, {
        published: 0,
        unpublished: 0
      })
    },
    'form.auto_pub_unpub.unpublished'() {
      this.form.auto_pub_unpub.unpublished_time = ''
    },
    'form.auto_pub_unpub.published'() {
      this.form.auto_pub_unpub.published_time = ''
    },
    'form.auto_pub_unpub.published_time'() {
      // 预热前 30 分钟可修改 pub 时间但不清楚 suspend 时间
      if (!this.lockAutoWarmUp) {
        this.resetSuspendedConfig()
      }

      if (!this.form.auto_pub_unpub.published) {
        this.form.auto_pub_unpub.suspended = 0
        this.resetSuspendedConfig()
      }
    },
    'form.auto_pub_unpub.suspended'() {
      this.form.auto_pub_unpub.suspended_config.suspended_start_time = undefined
    }
  },
  methods: {
    calcStyleFunc(config, el) {
      let { width } = el.getBoundingClientRect()

      return {
        ...config,
        left: -1 * width + 440 + 16 + 'px'
      }
    },
    resetSuspendedConfig() {
      this.$set(this.form.auto_pub_unpub, 'suspended_config', {
        suspended_start_time: undefined, // 预热时间
        suspended_end_time: undefined, // 设置预热时，此值为空
        admin_text_id: 0, // 设置预热时，此值为空
        text_id: 0, // 设置预热时，此值为空
        reason: '' // 设置预热时，此值为空
      })
    },
    disabledPubDate(current) {
      if (this.pkgIsWarmingUp || this.lockAutoWarmUp) {
        const suspendedStartTime = this.form.auto_pub_unpub.suspended_config.suspended_start_time

        return (
          (current &&
            current < moment().startOf('day') &&
            current < moment(suspendedStartTime).startOf('day')) ||
          current > moment(suspendedStartTime).add(7, 'days')
        )
      }

      return current && current < moment().startOf('day')
    },
    disabledUnpubDate(current) {
      return current && current < moment().startOf('day')
    },
    disabledWarmingUpDate(current) {
      const published_time = this.form.auto_pub_unpub.published_time

      return (
        current &&
        (current < moment().startOf('day') ||
          current < moment(published_time).add(-7, 'days') ||
          current > moment(published_time))
      )
    },
    handleSwitch(check, field) {
      this.form.auto_pub_unpub[field] = check ? 1 : 0
    }
  }
}
</script>

<style lang="scss" scoped>
.warming-up-box {
  padding: 4px 20px 20px 20px;
  margin-top: 16px;
  background-color: #fafafa;
}

.--fit-width {
  width: fit-content;
}
</style>
