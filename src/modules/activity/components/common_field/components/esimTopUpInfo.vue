<template>
  <span class="esim-top-up-info-wrap">
    <a-form-model-item :label="$t('172382')" prop="esim_topup_info.support_topup" :colon="false" required>
      <DescMarkdownContent
        class="strong-tips"
        placement="right"
        :is-inline="false"
        :desc="description"
        :line-clamp="3"
      />

      <ShimAntdTooltip :title="tooltip" :rich-tips="tooltip">
        <a-radio-group
          v-model="form.esim_topup_info.support_topup"
          :disabled="disabled"
          @change="onChangeSupport"
        >
          <a-radio :value="true">{{ $t('global_yes') }}</a-radio>
          <a-radio :value="false">{{ $t('global_no') }}</a-radio>
        </a-radio-group>
      </ShimAntdTooltip>
    </a-form-model-item>

    <a-form-model-item
      v-if="form.esim_topup_info.support_topup"
      class="form-item-tree-select"
      prop="esim_topup_info.topup_pkg_list"
      :colon="false"
      :rules="rules"
    >
      <span slot="label">
        <span @click.prevent>{{ $t('172382') }}</span>
        <a-button v-if="treeValue.length" type="link" :disabled="disabled || !lock" @click="onSwitchLock">
          {{ $t('172384') }}
        </a-button>
      </span>
      <a-tree-select
        ref="treeSelect"
        :value="treeValue"
        tree-checkable
        show-search
        tree-default-expand-all
        :tree-data="treeData"
        :filter-tree-node="filterTreeNode"
        :show-checked-strategy="SHOW_CHILD"
        :disabled="disabled || !!(treeValue.length && lock)"
        :placeholder="$t('global_select')"
        :get-popup-container="getPopupContainer"
        :dropdown-style="{
          maxHeight: '300px'
        }"
        @change="onChangePackages"
      />
    </a-form-model-item>
  </span>
</template>

<script>
import { mapState } from 'vuex'
import { TreeSelect } from 'ant-design-vue'
const SHOW_CHILD = TreeSelect.SHOW_CHILD

import ShimAntdTooltip from '../../shimAntdTooltip'
import DescMarkdownContent from '@activity/pages/activityManagement/detailV2/components/descMarkdownContent'

import { getEditLang } from '@activity/utils'

export default {
  name: 'EsimTopUpInfo',
  components: {
    ShimAntdTooltip,
    DescMarkdownContent
  },
  props: {
    form: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    tooltip: {
      type: [String, Object],
      default: ''
    },
    description: {
      type: String,
      default: ''
    },
    required: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      lock: false,
      SHOW_CHILD,
      treeData: [],
      treeValue: [],
      rules: [
        {
          type: 'array',
          required: true,
          message: __('173156'),
          trigger: 'change'
        }
      ]
    }
  },
  computed: {
    ...mapState({
      clickTarget: (state) => state.clickTarget
    }),
    packageId() {
      return +this.$route.query.package_id
    }
  },
  watch: {
    clickTarget(clickTarget) {
      if (!(this.lock || this.$el.querySelector('.form-item-tree-select')?.contains?.(clickTarget))) {
        this.onSwitchLock()
      }
    }
  },
  beforeMount() {
    this.initData()
  },
  methods: {
    async initData() {
      this.treeValue = (this.form.esim_topup_info?.topup_pkg_list || []).reduce(
        (acc, curr) => [...acc, ...curr.pid_list],
        []
      )

      this.lock = !!this.treeValue.length

      const response = await ajax.get(ADMIN_API.act.get_esim_published_act_pkg, {
        params: {
          package_id: this.packageId,
          language: getEditLang()
        }
      })

      this.treeData = (response.activity_list || []).map((actItem) => {
        const { activity_id, activity_name, package_list = [] } = actItem
        const actTitle = `${activity_id} - ${activity_name}`

        return {
          key: activity_id,
          value: activity_id,
          title: actTitle,
          filterValue: actTitle.toLowerCase(),
          children: package_list.map((pkgItem) => {
            const pkgTitle = `${pkgItem.package_id} - ${pkgItem.package_name}`

            return {
              key: pkgItem.package_id,
              value: pkgItem.package_id,
              title: pkgTitle,
              filterValue: `${actTitle} - ${pkgTitle}`.toLowerCase()
            }
          })
        }
      })
    },
    filterTreeNode(inputValue, TreeNode) {
      inputValue = inputValue.trim().toLowerCase()

      return !inputValue || TreeNode.data.props.filterValue.includes(inputValue)
    },
    onSwitchLock() {
      this.lock = !this.lock
    },
    onChangeSupport() {
      this.$set(this.form.esim_topup_info, 'topup_pkg_list', [])
      this.treeValue = []
      this.lock = false
    },
    // @param {number[]} data
    onChangePackages(data) {
      const result = Object.create(null) // Array<{ aid: number, pid_list: number[] }>
      const { treeData } = this
      for (let actItem of treeData) {
        const aid = actItem.value
        for (let pkgItem of actItem.children) {
          const pkgId = pkgItem.value
          if (data.includes(pkgId)) {
            if (!hasOwnProperty.call(result, aid)) {
              result[aid] = {
                aid,
                pid_list: []
              }
            }

            result[aid].pid_list.push(pkgId)
          }
        }
      }

      this.$set(this.form.esim_topup_info, 'topup_pkg_list', Object.values(result))
      this.treeValue = data
    },
    getPopupContainer() {
      return this.$el.querySelector('.form-item-tree-select')
    }
  }
}
</script>
