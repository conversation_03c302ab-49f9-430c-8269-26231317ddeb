<template>
  <div class="count-limit-input">
    <a-input
      v-if="type === 'input'"
      v-model="comValue"
      v-bind="allAttrs"
      :class="getClassList"
      @change="handleChange"
    >
      <div slot="suffix" class="common-text-count">
        <span class="count-box">
          {{ getSuffixCount() }}
        </span>
      </div>
    </a-input>
    <template v-else>
      <a-textarea v-model="comValue" v-bind="allAttrs" @change="handleChange" />
      <div class="suffix-text">{{ getSuffixCount() }}</div>
    </template>
  </div>
</template>
<script>
import { getSuffixCount } from '@activity/utils'

export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    wordCountLimit: {
      type: Number,
      default: Infinity
    },
    type: {
      type: String,
      default: 'input'
    }
  },
  data() {
    return {
      comValue: ''
    }
  },
  computed: {
    // comValue: {
    //   get() {
    //     return this.value
    //   },
    //   set(v) {
    //     this.$emit('change', v)
    //   }
    // },
    allAttrs() {
      const { wordCountLimit, $attrs } = this
      return { ...$attrs, maxLength: wordCountLimit }
    },
    getClassList() {
      const max = this.wordCountLimit
      return [max > 99 && max < Infinity ? 'common-suffix-count-large' : 'common-suffix-count']
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(v) {
        this.comValue = v.trim()
      }
    }
  },
  methods: {
    handleChange() {
      this.$emit('change', this.comValue.trim())
    },
    getSuffixCount() {
      const { wordCountLimit, comValue } = this
      return getSuffixCount(wordCountLimit, comValue.length)
    }
  }
}
</script>

<style lang="scss" scoped>
.suffix-text {
  text-align: right;
  color: rgba(0, 0, 0, 0.45);
  height: 20px;
  line-height: 20px;
}
</style>
