<template>
  <a-form-model class="duration-widget" ref="form" :model="form" label-width="0">
    <DisabledWidgetLayer :data="value" />
    <a-form-model-item class="duration__item" prop="single" :rules="rules">
      <a-radio-group v-model="form.single">
        <a-radio :value="true" :disabled="readonly">{{ $t('global_fixed_time') }}</a-radio>
        <a-radio :value="false" :disabled="readonly">{{ $t('act_enter_duration_range') }}</a-radio>
      </a-radio-group>
    </a-form-model-item>

    <template v-if="form.single !== ''">
      <a-form-model-item v-if="form.single" class="duration__item" prop="from" :rules="rules">
        <duration v-model="form.from" :readonly="readonly" />
      </a-form-model-item>

      <template v-else>
        <a-form-model-item prop="from" class="duration__item" :rules="rules">
          <label class="duration__type">{{ $t('act_from') }}</label>
          <duration v-model="form.from" class="duration__body" :readonly="readonly" />
        </a-form-model-item>
        <a-form-model-item prop="to" class="duration__item" :rules="rules">
          <label class="duration__type">{{ $t('act_to') }}</label>
          <duration v-model="form.to" class="duration__body" :readonly="readonly" />
        </a-form-model-item>
      </template>
    </template>
  </a-form-model>
</template>

<script>
import duration from './tpl_duration.vue'
import DisabledWidgetLayer from '@activity/components/DisabledWidgetLayer.vue'

export default {
  name: 'DurationWidget',
  components: {
    duration,
    DisabledWidgetLayer
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    required: {
      type: Boolean,
      default: false
    },
    config: {
      type: Object,
      default: () => ({})
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        single: '',
        from: 0,
        to: 0
      }
    }
  },
  computed: {
    rules() {
      return {
        required: this.required,
        message: 'Please fill in this field.'
      }
    }
  },
  watch: {
    'form.single': {
      immediate: true,
      handler(v, pre) {
        if (v) {
          this.form.to = 0
        }
      }
    },
    form: {
      deep: true,
      handler(v) {
        let data
        if (v.single === '') {
          // init data
          data = {}
        } else if (v.single) {
          data = {
            single: true,
            from: v.from,
            to: 0
          }
        } else {
          data = {
            single: false,
            from: v.from,
            to: v.to
          }
        }

        const newV = {
          ...this.value,
          value: JSON.stringify(data),
          published: v.single === '' ? -1 : 0
        }

        if (this.value?.value !== newV.value) {
          this.$emit('change', newV)
          this.$emit('save2Draft')
        }
      }
    },
    value: {
      immediate: true,
      deep: true,
      handler() {
        let value = this.value.value || {}
        let initData =
          typeof value === 'string'
            ? JSON.parse(value)
            : toString.call(value) === '[object Object]'
            ? value
            : {}
        Object.assign(this.form, {
          from: 0,
          to: 0,
          // single: initData.hasOwnProperty('from')
          single: Object.prototype.hasOwnProperty.call(initData, 'from') ? initData.to === initData.from : '',
          ...initData
        })
      }
    }
  },
  methods: {
    validateForm() {
      return new Promise((resolve) => this.$refs.form.validate((valid) => resolve(valid)))
    }
  }
}
</script>

<style lang="scss" scoped>
$marginSize: 12px;

.duration-widget {
  position: relative;
}

.duration_widget__container {
  min-width: 520px;
}

.duration__item {
  margin-bottom: 12px;
  &:last-child {
    margin-bottom: 0;
  }
  ::v-deep .ant-form-item-children {
    display: flex;
  }
}

.duration__type {
  display: inline-block;
  width: 50px;
  margin-right: $marginSize;
}
</style>
