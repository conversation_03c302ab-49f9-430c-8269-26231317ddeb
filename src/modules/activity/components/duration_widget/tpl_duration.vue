<template>
  <div class="duration__container">
    <a-select v-model="form.days" class="tiny-select" :disabled="readonly" show-search>
      <a-select-option v-for="item in optsFactory(31)" :key="item" :label="item" :value="item"
        >{{ item }}
      </a-select-option>
    </a-select>
    <label class="duration__type">{{ $t('act_days') }}</label>
    <a-select v-model="form.hours" class="tiny-select" :disabled="readonly" show-search>
      <a-select-option v-for="item in optsFactory(24)" :key="item" :label="item" :value="item"
        >{{ item }}
      </a-select-option>
    </a-select>
    <label class="duration__type">{{ $t('act_hours') }}</label>
    <a-select v-model="form.minutes" class="tiny-select" :disabled="readonly" show-search>
      <a-select-option v-for="item in optsFactory(60, 5)" :key="item" :label="item" :value="item"
        >{{ item }}
      </a-select-option>
    </a-select>
    <label class="duration__type">{{ $t('act_minutes') }}</label>
  </div>
</template>

<script>
export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'Duration',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Number,
      required: true
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {}
    }
  },
  beforeMount() {
    this.form = new Proxy(
      {},
      {
        get: (target, key) => {
          let res = this.parseSecond(this.value)
          return res[key] || 0
        },
        set: (target, key, value) => {
          this.$emit(
            'change',
            this.getSecond({
              ...this.parseSecond(this.value),
              [key]: value
            })
          )
          return true
        }
      }
    )
  },
  methods: {
    parseSecond(time = 0) {
      let days = Math.floor(time / 1440 / 60)
      let hours = Math.floor((time - days * 1440 * 60) / 3600)
      let minutes = Math.floor((time - days * 1440 * 60 - hours * 3600) / 60)

      return {
        days: days,
        hours: hours,
        minutes: minutes
      }
    },
    getSecond(v) {
      let { days, hours, minutes } = v

      return (days || 0) * 24 * 60 * 60 + (hours || 0) * 60 * 60 + (minutes || 0) * 60
    },
    optsFactory(end, step = 1, start = 0) {
      let res = []

      if (isNaN(end) || isNaN(start) || end < start) return []

      while (start < end) {
        res = [...res, start]
        start += step
      }

      return res
    }
  }
}
</script>

<style lang="scss" scoped>
.tiny-select {
  width: 80px;
}

.duration__type {
  display: inline-block;
  margin: 0 12px 0 6px;
}
</style>
