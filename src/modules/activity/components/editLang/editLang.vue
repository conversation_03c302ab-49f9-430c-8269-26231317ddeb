<template>
  <!-- 对应老admin组件editLang.vue -->
  <div class="edit-lang">
    <div class="edit-lang-box">
      <div class="group-box">
        <p class="title">{{ __('28252') }}</p>
        <a-radio-group v-model="params.show">
          <a-radio :value="true">Yes</a-radio>
          <a-radio :value="false">No</a-radio>
        </a-radio-group>
      </div>
      <div class="group-box" v-show="params.show">
        <p class="title">{{ __('28254') }}</p>
        <a-radio-group :disabled="calcDisabled" v-model="params.language">
          <a-radio v-for="item in languages" :key="item.detail_language" :value="item.detail_language">{{
            getLangObj(item.detail_language)
          }}</a-radio>
        </a-radio-group>
        <p class="text">
          {{ __('28242') }}
          <b>
            <template v-for="activity_lang in merchantActLanguage">
              {{ getLangObj(activity_lang) }}
            </template>
          </b>
        </p>
        <p
          :disabled="calcConfirmDisabled"
          class="text"
          v-html="
            __('28255', {
              MULTILANG_URL_PLACEHOLDER: 'javascript:window.open(\'/act/merchant/merchant_list\')'
            })
          "
        ></p>
      </div>
    </div>
  </div>
</template>

<script>
let lang_conf = require('lang_conf')
export default {
  name: 'editLang',
  props: {
    languages: {
      type: Array,
      default() {
        return []
      }
    },
    form: {
      type: Object,
      default() {
        return {}
      }
    },
    merchant_use_language: {
      type: String,
      default: ''
    },
    merchant_info_list: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    form: {
      immediate: true,
      deep: true,
      handler(v) {
        this.$set(this, 'params', v)
      }
    },
    params: {
      immediate: true,
      deep: true,
      handler(v) {
        this.$emit('update', v, this.calcConfirmDisabled)
      }
    }
  },
  data() {
    return {
      params: {
        show: undefined,
        language: undefined
      }
    }
  },
  computed: {
    merchantActLanguage() {
      return [...new Set(this.merchant_info_list.map((item) => item.activity_lang))]
    },
    calcDisabled() {
      let { languages, params, merchant_use_language } = this
      let isTrue = false

      let needToLockLanguageList = []
      let useLangObj = languages.find((l) => l.detail_language === merchant_use_language)
      if (useLangObj) {
        needToLockLanguageList.push(merchant_use_language)
      }
      languages.forEach((item) => {
        let { detail_language } = item
        if (this.merchantActLanguage.includes(detail_language)) {
          needToLockLanguageList.push(detail_language)
        }
      })

      if (needToLockLanguageList.includes(params.language)) {
        isTrue = true
      }
      return isTrue
    },
    calcConfirmDisabled() {
      let { show, language } = this.params
      if (show === false) {
        return false
      } else {
        return !language
      }
    }
  },
  methods: {
    __: __,
    getLangObj(lang) {
      return lang_conf.getLangObj('B_LANG', 'LANG_TITLE')[lang]
    }
  }
}
</script>
<style lang="scss">
.edit-lang-modal-style {
  width: 572px;
}
</style>
<style lang="scss" scoped>
.edit-lang {
  .edit-lang-box {
    .group-box:first-child {
      margin-bottom: 20px;
    }
    .title {
      margin-bottom: 12px;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 18px;
    }
    .text {
      margin: 12px 0;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
      line-height: 18px;
    }
  }
}
</style>
