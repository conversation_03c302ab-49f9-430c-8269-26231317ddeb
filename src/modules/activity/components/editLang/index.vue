<template>
  <div class="edit-lang">
    <div class="edit-lang-box">
      <p class="title">{{ calcTitle }}</p>
      <div class="action-box">
        <span class="lang">{{ $t('28235') }}</span>
        <div @click="sendSwitchSpm">
          <template v-if="switchEditDisabeld">
            <a-popconfirm v-bind="tipObj" v-on="tipObj.on">
              <a-button
                v-show="!isEdit"
                :disabled="switchEditDisabeld"
                type="primary"
                @click="clickSwitchLang"
              >
                {{ $t('28236') }}
              </a-button>
            </a-popconfirm>
          </template>
          <template v-else>
            <a-button v-show="!isEdit" :disabled="switchEditDisabeld" type="primary" @click="clickSwitchLang">
              {{ $t('28236') }}
            </a-button>
          </template>
        </div>
      </div>
      <a-spin :spinning="spinning" />
      <a-radio-group
        v-model="form.language"
        :class="[!isEdit ? 'common-hide-radio' : '']"
        class="common-radio-style col000"
      >
        <a-radio
          v-for="item in isEdit ? languages.filter((o) => o.detail_status === 3) : languages"
          :key="item.detail_language"
          :value="item.detail_language"
          class="radio-box"
          :disabled="!isEdit || [5, -1].includes(item.detail_status)"
        >
          <a-tooltip
            placement="right"
            overlay-class-name="common-tooltip-style"
            :title="statusMap['' + item.detail_status].tip"
          >
            <span :class="[!isEdit ? 'text-style' : '']">{{ getLangObj(item.detail_language) }}</span>
            <span class="published">
              {{ getStatusText(item) }}
            </span>
            <span v-if="item.detail_language === row.language" class="edit-style">{{ $t('28261') }}</span>
          </a-tooltip>
        </a-radio>
      </a-radio-group>
      <!-- <p class="text">
        {{ $t('28242') }}<b>{{ getLangObj(row.merchant_use_language) }}</b>
      </p> -->
      <p class="text" style="margin-bottom: 5px">{{ $t('28243') }}</p>
      <div v-show="isEdit" class="btns-box">
        <a-button @click="handleEditLang">{{ $t('global_cancel') }}</a-button>
        <a-button type="primary" @click="handleEditLang('confirm')">{{ $t('global_confirm') }}</a-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    row: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      spinning: true,
      tipObj: {
        trigger: 'hover',
        title: __('28244'),
        overlayStyle: {
          width: '536px',
          color: 'rgba(0, 0, 0, 0.85)'
        },
        cancelText: __('28237'),
        onOk: __('btn.okay'),
        on: {
          cancel: () => {
            klook.bus.$emit('sendChangeLangSpm', this.row.activity_id)
            klook.newOpenWin('/merchant_info')
          }
        }
      },
      form: {
        language: ''
      },
      isEdit: false,
      switchEditDisabeld: true,
      languages: [],
      statusMap: {
        '3': {
          text: `(${__('global_published')})`
        },
        '5': {
          text: `(${__('28238')})`,
          tip: __('80733')
        },
        '-1': {
          text: `(${__('28240')})`,
          tip: __('28241')
        }
      }
    }
  },
  computed: {
    calcTitle() {
      let { row } = this
      let title = `${row.activity_id || ''}-${row.activity_title || ''}`
      return title
    }
  },
  watch: {
    'row.activity_id': {
      immediate: true,
      handler(aid) {
        let { row } = this
        this.switchEditDisabeld =
          !row.can_switch_language || !row.merchant_use_language || row.language === row.merchant_use_language
        this.form.language = row.language
        aid && this.initData(aid)
      }
    }
  },
  methods: {
    getStatusText(data) {
      const { detail_status } = data || {}
      const statusMap = this.statusMap
      const text = statusMap['' + detail_status].text
      if (detail_status === 5) {
        const s = this.$t('80728', { language: this.sourceLanguage })
        return `${text}(${s})`
      }
      return text
    },
    sendSwitchSpm() {
      klook.bus.$emit('sendSwitchSpm', this.row.activity_id)
    },
    async initData(activity_id) {
      let res = await ajax.getBody({
        url: ADMIN_API.act.get_activity_publish_status,
        params: {
          activity_id
        }
      })
      if (res && res.success) {
        let arr = res.result.details
        this.sourceLanguage = res.result.source_language || ''
        if (arr) {
          this.languages = arr.filter((o) => [3, 5, -1].includes(o.detail_status))
        }
        this.spinning = false
      }
    },
    getLangObj(lang) {
      return lang_conf.getLangObj('B_LANG', 'LANG_TITLE')[lang]
    },
    clickSwitchLang() {
      this.isEdit = true
    },
    async handleEditLang(type) {
      if (type === 'confirm') {
        await ajax.post(
          {
            url: ADMIN_API.act.switch_eidt_language,
            data: {
              activity_id: this.row.activity_id,
              language: this.form.language
            }
          },
          ajax.sucOptions
        )
      }
      this.isEdit = false
      this.$emit(type)
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-lang {
  .edit-lang-box {
    p {
      margin: 0;
    }
    .title {
      padding: 4px 12px;
      font-size: 16px;
      color: #000;
      line-height: 24px;
      background: #fafafa;
    }
    .action-box {
      margin: 28px 0;
      display: flex;
      align-items: center;
      font-size: 14px;
      line-height: 32px;
      .lang {
        display: inline-block;
        font-weight: 500;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        margin-right: 5px;
      }
      .temp {
        flex: 1 1 auto;
      }
      .btn-last {
        padding-right: 0;
      }
    }
    .text {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
    }
    .radio-box {
      margin-bottom: 28px;
      width: 50%;
    }
    .text-style {
      color: #000;
    }
    .published {
      color: rgb(70, 188, 22);
    }
    .edit-style {
      margin-left: 7px;
      padding: 1px 8px;
      position: relative;
      background: #fff7e6;
      // border: 1px solid #ffd591;
      box-sizing: border-box;
      border-radius: 2px;
      color: #faad14;
    }
    .btns-box {
      text-align: right;
      padding: 20px 0 0 0;
      button {
        margin-left: 8px;
      }
    }
  }
}
</style>
