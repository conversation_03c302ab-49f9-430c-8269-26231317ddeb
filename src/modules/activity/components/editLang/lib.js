import Vue from 'vue'
import editLang from './editLang.vue'
export const showMerchantOnboarding = async (actData, cb) => {
  if (!actData && !actData.show_on_merchant_self_onboarding) return
  let id = actData.activity_id
  let showObj = actData.show_on_merchant_self_onboarding || {}

  if (showObj.can_show) {
    let res = await ajax.get({
      url: ADMIN_API.act.get_activity_publish_status,
      params: {
        activity_id: id
      }
    })
    if (!res && !res.details) return
    let languages = res.details.filter((o) => [3, 4].includes(o.detail_status))
    let merchant_info_list = actData.merchant_info_list || []

    let language = undefined
    if (showObj.edit_language) {
      language = showObj.edit_language
    } else {
      let useLangObj = languages.find((l) => l.detail_language === showObj.merchant_use_language)
      if (useLangObj) {
        language = showObj.merchant_use_language
      } else {
        let detailLanguageList = languages.map((item) => item.detail_language)
        useLangObj = merchant_info_list.find((item) => detailLanguageList.includes(item.activity_lang))

        if (useLangObj) {
          language = useLangObj.activity_lang
        }
      }
    }

    let vm = new Vue({
      components: {
        editLang
      }
    })
    let h = vm.$createElement
    let form = {
      activity_id: id,
      language: language,
      show: showObj.show
    }
    let okButtonProps = {
      props: {
        disabled: true
      }
    }
    let type = await new Promise((resolve) => {
      vm.$modal.confirm({
        width: 572,
        okText: __('global_confirm'),
        cancelText: __('28256'),
        title: __('28251'),
        okButtonProps,
        content: h('editLang', {
          props: {
            languages,
            form,
            merchant_use_language: showObj.merchant_use_language,
            merchant_info_list
          },
          on: {
            update: (data, confirmDisabled) => {
              okButtonProps.props.disabled = confirmDisabled
              _.merge(form, data)
            }
          }
        }),
        onOk: () => {
          resolve('confirm')
        },
        onCancel: () => {
          resolve('cancel')
        }
      })
    })
    if (type === 'confirm') {
      await ajax.post(
        {
          url: ADMIN_API.act.show_to_merchant_self_onboarding,
          data: {
            items: [form]
          }
        },
        ajax.sucOptions
      )
      cb && cb()
    }
  }
}
