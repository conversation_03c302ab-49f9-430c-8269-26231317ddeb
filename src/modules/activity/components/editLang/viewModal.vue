<template>
  <a-modal v-model="editLang.visible" v-bind="viewObj" class="common-modal-style border">
    <editLang v-bind="editLang" @confirm="viewObj.onOk" />
    <!-- <div slot="footer">
      <a-button type="primary" @click="viewObj.onCancel">{{
        $t('global_cancel')
      }}</a-button>
    </div> -->
  </a-modal>
</template>
<script>
import editLang from './index.vue'
export default {
  components: { editLang },
  props: {
    editLang: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      viewObj: {
        footer: null,
        destroyOnClose: true,
        width: 1270,
        visible: false,
        maskClosable: true,
        keyboard: true,
        title: __('28235'),
        onOk: () => {
          this.$set(this.editLang, 'visible', false)
        },
        onCancel: () => {
          this.$set(this.editLang, 'visible', false)
        }
      }
    }
  },
  watch: {
    editLang: {
      deep: true,
      immediate: true,
      handler(v) {
        _.merge(this.viewObj, v)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-lang-modal {
  .edit-lang-modal-box {
    //
  }
}
</style>
