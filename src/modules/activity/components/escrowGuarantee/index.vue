<template>
  <div class="warapper">
    <div :style="styleObj">
      <a-upload
        :file-list="fileList"
        :custom-request="customRequest"
        list-type="picture-card"
        accept=".jpg,.jpeg,.png"
        :before-upload="beforeUpload"
        :remove="removeFile"
        :disabled="disabled"
        @preview="handlePreview"
      >
        <div v-if="fileList.length < 3">
          <a-icon type="plus" style="color: rgba(0, 0, 0, 0.85)" />
          <div style="margin-top: 8px">{{ $t('29160') }}</div>
        </div>
      </a-upload>
    </div>
    <div class="text-wrap" :style="getMargin" v-html="$t('74249')"></div>

    <a-modal
      v-model="modalObject.show"
      :footer="null"
      :centered="true"
      :closable="false"
      :width="800"
      :z-index="10000"
      @cancel="cancelPreview"
    >
      <a-spin :spinning="showLoading">
        <img :src="modalObject.url" class="pre-img" @load="imageLoad" />
      </a-spin>
    </a-modal>
  </div>
</template>
<script>
import { isDev } from '@/env'
export default {
  model: {
    prop: 'photoList',
    event: 'change'
  },
  props: {
    photoList: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      list: [],
      tempId: 0,
      modalObject: {
        show: false,
        url: ''
      },
      fileList: [],
      showLoading: false
    }
  },
  computed: {
    styleObj() {
      const length = this.fileList.length
      const num = length < 3 ? length + 1 : 3
      return { width: num * 150 + 'px' }
    },
    getMargin() {
      const length = this.fileList.length
      const num = length < 3 ? length + 1 : 3
      return { marginLeft: -(num * 38) + 'px' }
    }
  },
  watch: {
    photoList: {
      immediate: true,
      handler(v) {
        if (Array.isArray(v)) {
          this.getFileList(v)
        }
      }
    }
  },
  methods: {
    removeFile(file) {
      const list = this.photoList.filter((item) => item !== file.path)
      this.$emit('change', [...list])
    },
    async customRequest(e) {
      const file = e.file
      if (!file) {
        return
      }
      let form = new FormData()
      form.append('file', file)
      const newFile = {
        uid: file.uid,
        name: file.name,
        url: URL.createObjectURL(file),
        status: 'uploading',
        percent: 0,
        path: ''
      }
      this.fileList.push(newFile)
      const res = await ajax.post(ADMIN_API.act.upload_package_escrow_guarantee_photos, {
        data: form,
        onUploadProgress(e) {
          const percent = ((e.loaded / e.total) * 100) | 0
          newFile.percent = percent
        }
      })
      if (!res) {
        newFile.status = 'error'
        return
      }
      newFile.status = 'done'
      newFile.path = res
      this.$emit('change', [...(this.photoList || []), res])
    },
    getFileList(photoList = []) {
      const fileList = this.fileList || []
      const list = photoList.map((item) => {
        const [file] = fileList.filter((it) => it.path === item)
        if (!file) {
          const storage_key = encodeURIComponent(item)
          const path = isDev ? '/xos_api' : ''
          return {
            uid: item,
            name: item,
            url: `${path}${ADMIN_API.act.download_package_escrow_guarantee_photos}?storage_key=${storage_key}`,
            status: 'done',
            percent: 100,
            path: item
          }
        }
        file.status = 'done'
        return file
      })
      this.fileList = [...list]
    },
    beforeUpload(file) {
      const isLt4M = file.size / 1024 / 1024 < 4

      if (!isLt4M) {
        this.$info({
          title: 'Image must smaller than 4MB!',
          okText: this.$t('44915')
        })
        console.error('Image must smaller than 4MB!')
      }

      return isLt4M
    },
    handlePreview(file) {
      this.modalObject.url = file.url
      this.modalObject.show = true
      this.showLoading = true
    },
    imageLoad() {
      this.showLoading = false
    },
    cancelPreview() {
      this.modalObject.url = ''
      this.modalObject.show = false
      this.showLoading = false
    }
  }
}
</script>
<style lang="scss" scoped>
.warapper {
  display: flex;
  .text-wrap {
    width: 180px;
    line-height: 22px;
    padding: 0 8px;
    color: rgba(0, 0, 0, 0.45);
    white-space: pre-wrap;
  }
}
.pre-img {
  display: block;
  width: 100%;
  min-height: 300px;
}
</style>
