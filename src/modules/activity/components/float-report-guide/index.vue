<template>
  <div class="float-report-guide" :class="{ 'enter-back-off': enterBackOff, recover: recover }">
    <div
      ref="enter"
      class="float-report-guide-enter"
      :data-spm-module="getHelpSpm"
      @click="handleOpenHelp"
      @mouseover="onMouseover"
    >
      <a-icon class="float-report-guide-enter-icon" type="question-circle" theme="filled" />
      <span style="white-space: nowrap; font-weight: 600; font-size: 14px; line-height: 22px">{{
        $t('212410')
      }}</span>
    </div>

    <div
      ref="list"
      data-spm-page="Footer?trg=manual"
      class="float-report-guide-list"
      @mouseleave="onMouseleave"
    >
      <div data-spm-module="Feedback">
        <div
          data-spm-item="__default"
          class="float-report-guide-item item-report-error"
          @click="handleOpenReportErrorDrawer"
        >
          <a-icon type="exception" />{{ $t('120636') }}
        </div>
      </div>
      <div data-spm-module="Guidebook">
        <div data-spm-item="__default" class="float-report-guide-item item-help" @click="handleOpenHelp">
          <a-icon type="book" />{{ $t('117148') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { OPEN_REPORT_ERR_DRAWER_2BUS_KEY } from '@activity/utils/modal-message.js'
import { FLOAT_HELP_URL_MERCHANT, FLOAT_HELP_URL } from '@activity/pages/package/package_const.js'
import { addEventListener } from '@activity/utils/index.js'
import { merchantGuideMaps } from './utils'

export default {
  name: 'FloatReportGuide',
  props: {
    customHelpLink: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // enter back-off -> show list -> recover
      enterBackOff: false,
      recover: false
    }
  },
  computed: {
    calcHelpUrl() {
      if (this.customHelpLink) {
        return this.customHelpLink
      }

      const { path } = this.$route
      const config = merchantGuideMaps.find((item) => path?.indexOf(item.path) !== -1)
      if (config?.link) {
        return config.link
      }

      return this.$root.isMerchant ? FLOAT_HELP_URL_MERCHANT : FLOAT_HELP_URL
    },
    getHelpSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `Help?oid=${oid}&trg=manual`
    }
  },
  mounted() {
    const enterAnimationend = addEventListener(this.$refs.enter, 'animationend', () => {
      // 初始弹出动画不需要处理
      if (!this.initAnimEnd) {
        this.initAnimEnd = true
        return
      }

      if (this.recover) {
        this.enterBackOff = false
        this.recover = false
      }
    })

    this.$once('hook:beforeDestroy', () => {
      enterAnimationend.remove()
    })
  },
  methods: {
    handleOpenReportErrorDrawer() {
      this.onMouseleave()
      klook.bus.$emit(OPEN_REPORT_ERR_DRAWER_2BUS_KEY)
    },
    handleOpenHelp() {
      this.onMouseleave()
      window.open(this.calcHelpUrl, '_blank')
    },
    onMouseover() {
      if (this.recover) {
        return
      }

      this.enterBackOff = true
    },
    onMouseleave() {
      if (this.enterBackOff) {
        this.recover = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
$fixedBottom: 168px;
$listEnterRight: -60px;
$listLeaveRight: -180px;
$enterAnimDuration: 0.3s;
$listAnimDuration: 0.3s;

.float-report-guide {
  position: relative;
  z-index: 1001;

  &-enter {
    position: fixed;
    bottom: $fixedBottom;
    display: flex;
    gap: 6px;
    padding: 4px 8px;
    align-items: center;
    color: #fff;
    background-color: #437dff;
    border-radius: 100px;
    transition: all 1s;
    transform: translateZ(0);

    margin-right: 16px;
    right: $listEnterRight;
    opacity: 0;
    animation: recover-enter $enterAnimDuration $listAnimDuration 1 ease-in-out forwards;

    cursor: pointer;

    &-icon {
      font-size: 14px;
      color: white;
    }

    &-text {
      white-space: nowrap;
    }
  }

  &-list {
    position: fixed;
    bottom: $fixedBottom;
    right: $listLeaveRight;
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 170px;
    padding-right: 20px;
    opacity: 0;
    /* transition: all 1s; */
    transform: translateZ(0);
  }

  &-item {
    display: flex;
    align-items: center;
    gap: 12px;
    height: 40px;
    padding: 10px 16px;
    border-radius: 20px;
    font-weight: 600;
    cursor: pointer;

    &:hover {
      opacity: 0.82;
    }

    &.item-report-error {
      color: var(--White, var(--color-text-reverse, #fff));
      background-color: var(--color-error--, #f44622);
      box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.14);
    }

    &.item-help {
      color: var(--color-text-link, #2073f9);
      background-color: var(--color-text-reverse, #fff);
      box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.14);
    }
  }

  &.enter-back-off {
    .float-report-guide-enter {
      animation: back-off $enterAnimDuration 1 ease-in-out forwards;
    }

    .float-report-guide-list {
      animation: show-list $listAnimDuration $enterAnimDuration 1 ease-in-out forwards;
    }
  }

  &.recover {
    .float-report-guide-enter {
      right: $listEnterRight;
      /* back-off reverse 会有动画断层 */
      animation: recover-enter $enterAnimDuration $listAnimDuration 1 ease-in-out forwards;
    }

    .float-report-guide-list {
      animation: recover-list $listAnimDuration 1 ease-in-out forwards;
    }
  }

  @keyframes back-off {
    from {
      right: 0;
      opacity: 1;
    }
    to {
      right: $listEnterRight;
      opacity: 0;
    }
  }

  @keyframes show-list {
    from {
      right: $listLeaveRight;
      opacity: 0;
    }
    to {
      right: 0;
      opacity: 1;
    }
  }

  @keyframes recover-enter {
    from {
      right: $listEnterRight;
      opacity: 0;
    }
    to {
      right: 0;
      opacity: 1;
    }
  }

  @keyframes recover-list {
    from {
      right: 0;
      opacity: 1;
    }
    to {
      right: $listLeaveRight;
      opacity: 0;
    }
  }
}
</style>
