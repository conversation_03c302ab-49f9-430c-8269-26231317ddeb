<template>
  <div class="floor-timeline">
    <a-spin :spinning="spinning" :delay="200">
      <div class="floor-timeline-box">
        <a-anchor :offset-top="offsetTop" class="common-anchor-style hide-all" @change="handleChangeAnchor">
          <a-timeline class="common-timeline-style">
            <template v-for="(item, i) in floorData.list">
              <a-timeline-item v-if="item.text || item.title" :key="i" @click="handleClick(item)">
                <template slot="dot">
                  <div
                    class="common-cicle-style"
                    :style="getAnchorHref(item) == anchorId ? styleObj['finish'] : ''"
                  />
                </template>
                <a-anchor-link :href="getAnchorHref(item)">
                  <div slot="title" class="content-box">
                    <span class="common-required-star star-box">{{
                      item.require || item.requireCount ? '*' : ''
                    }}</span>
                    <span :title="item.text || item.title" class="text">
                      <div class="common-two-line">
                        {{ item.text || item.title }}
                      </div>
                    </span>
                    <span v-if="item.count" class="fields">{{ `${item.num}/${item.count}` }}</span>
                    <span class="require-fields">
                      <requireFields :num="item.requireNum" :count="item.requireCount" />
                    </span>
                  </div>
                </a-anchor-link>
              </a-timeline-item>
            </template>
          </a-timeline>
          <div v-if="!$route.meta.hideSummary" class="group-summary-box">
            <a-button type="primary" @click="toogleDrawer">{{ $t('act_group_summary') }}</a-button>
            <summaryDrawer
              v-if="drawerData.visible"
              :drawer-data="drawerData"
              :groups-summary="groupsSummary"
              :ref-groups-summary="refGroupsSummary"
            />
          </div>
        </a-anchor>
      </div>
    </a-spin>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import requireFields from '@activity/components/requireFields'
import summaryDrawer from '@activity/components/groupSummary/drawer'

export default {
  name: 'FloorTimeline',
  components: { requireFields, summaryDrawer },
  props: {
    offsetTop: {
      type: Number,
      default: 72
    }
  },
  data() {
    return {
      anchorId: '',
      drawerData: {
        visible: false
      },
      spinning: false,
      styleObj: {
        finish: { borderColor: '#0091FF' }
      },
      floorData: {
        list: [
          // {
          //   require: true,
          //   text: 'Basic Info',
          //   style: 'finish'
          // },
          // { require: true, text: 'Banner Photo' },
          // { require: false, text: 'Activity Photo' },
        ]
      }
    }
  },
  computed: {
    groupsSummary() {
      return this.getGroupsSummary()
    },
    refGroupsSummary() {
      return this.getRefGroupsSummary()
    }
  },
  watch: {
    $route: {
      deep: true,
      handler(v, oldV) {
        if (oldV.name && v.name !== oldV.name) {
          this.spinning = true
        }
      }
    }
  },
  created() {
    klook.bus
      .$off('floorTimeline2bus', this.floorTimeline2bus)
      .$on('floorTimeline2bus', this.floorTimeline2bus)
  },
  beforeDestroy() {
    klook.bus.$off('floorTimeline2bus', this.floorTimeline2bus)
  },
  methods: {
    getAnchorHref(item) {
      return `#${item.id || item.text || item.title}`
    },
    toogleDrawer() {
      this.drawerData.visible = !this.drawerData.visible
      // 埋点
      klook.bus.$emit('QuickViewClick_Spm')
    },
    handleClick({ id }) {
      // klook.bus.$emit('floorTimelineAnchorChange2bus', id) // 定位后不需要展开
    },
    handleChangeAnchor(link) {
      this.anchorId = link
    },
    ...mapGetters(['getGroupsSummary', 'getRefGroupsSummary']),
    floorTimeline2bus(data = {}, method = 'update') {
      typeof this[method] === 'function' && this[method](data)
      this.$nextTick(() => {
        this.spinning = false
      })
    },
    update(data) {
      for (let key in data) {
        this.$set(this, key, _.merge({}, data[key]))
      }
    },
    handleClickFloor(e, item, i) {
      // let titles = document.querySelectorAll('h2')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-affix .common-timeline-style {
  max-height: calc(100vh - 180px);
}

.common-timeline-style {
  max-height: calc(100vh - 338px);
  overflow-y: scroll;
  padding: 4px 6px 30px 10px;
  transition: all 0.5s ease 0.1s;
  &::-webkit-scrollbar {
    width: 4px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #999;
  }
}
.floor-timeline {
  position: relative;
  z-index: 1;
  .floor-timeline-box {
    .content-box {
      max-width: 256px;
      margin: 0 0 0 0;
      position: relative;
      // display: flex;
      // align-items: center;
      cursor: pointer;
      span {
        display: inline-block;
        vertical-align: top;
        // flex: 0 1 auto;
      }
      .star-box {
        line-height: 22px;
      }
      .text {
        width: 140px;
        line-height: 22px;
        color: #000;
      }
      .fields {
        min-width: 44px;
        padding: 0 2px;
        font-size: 12px;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.45);
      }
      .require-fields {
        min-width: 60px;
        font-size: 12px;
      }
    }
  }
}
</style>
