<template>
  <div class="drawer">
    <div class="drawer-box">
      <a-drawer
        v-bind="drawerProps"
        v-on="drawerProps.on"
        :width="switchRefData ? 900 : 450"
        wrapClassName="drawer-container"
      >
        <div class="group-summary-list">
          <div
            class="group-summary-item"
            :style="{
              width: switchRefData ? '50%' : '100%',
              minWidth: '426px'
            }"
          >
            <template v-for="group in groupsSummary">
              <groupSummary
                v-if="group.summary && group.summary.length"
                :group="group"
                :key="group.id"
              />
            </template>
          </div>
          <div class="group-summary-item" v-if="switchRefData">
            <template v-for="group in refGroupsSummary">
              <groupSummary
                v-if="group.summary && group.summary.length"
                :group="group"
                :key="group.id"
              />
            </template>
          </div>
        </div>
        <div class="footer-btn" v-if="refGroupsSummary.length">
          Ref.language <a-switch v-model="switchRefData" />
        </div>
      </a-drawer>
    </div>
  </div>
</template>

<script>
import groupSummary from '@activity/components/groupSummary'
export default {
  components: {
    groupSummary
  },
  props: {
    drawerData: {
      default() {
        return {}
      }
    },
    groupsSummary: {
      default() {
        return []
      },
      required: true,
      type: Array
    },
    refGroupsSummary: {
      default() {
        return []
      },
      required: true,
      type: Array
    }
  },
  data() {
    return {
      switchRefData: false,
      drawerProps: {
        visible: false,
        closable: true,
        width: 600,
        height: '100%',
        on: {
          close: () => {
            let { drawerData: dd } = this
            dd.visible = false
          }
        }
      }
    }
  },
  watch: {
    drawerData: {
      handler(dd) {
        this.drawerProps = _.merge(this.drawerProps, dd)
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-container {
  ::v-deep .ant-drawer-content-wrapper {
    transition: width 0.3s ease 0s;
    will-change: width;
  }
  .footer-btn {
    position: absolute;
    width: 100%;
    left: 0;
    bottom: 0;
    padding: 10px 22px;
    border: 1px solid #f0f0f0;
    text-align: right;
  }
  .group-summary-list {
    display: flex;
    .group-summary-item {
      width: 50%;
      flex-grow: 0;
      flex-shrink: 0;
      padding: 0 10px;
    }
  }
}
</style>
