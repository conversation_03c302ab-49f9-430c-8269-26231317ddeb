<template>
  <div class="group-summary">
    <div class="group-summary-box">
      <slot name="groupName">
        <h2>{{ group.name || '' }}</h2>
      </slot>

      <div v-for="(widget, index) of group.summary" :key="index" class="widget-summary__item">
        <!-- widget_type: 1,// widget 类型：1-Attribute 2-Single Text 3-Markdown 4-Image 5-Address, 6: duration -->
        <div class="summary__header">
          {{ widget.name || '' }}
        </div>
        <ul v-if="[1, 2, 4].includes(widget.widget_type)" class="widget__ul widget__content">
          <!-- eslint-disable vue/no-v-html -->
          <li v-for="(attr, idx) of widget._html" :key="idx" class="widget__li" v-html="attr" />
        </ul>
        <template v-if="[3, 6].includes(widget.widget_type)">
          <!-- eslint-disable vue/no-v-html -->
          <p v-html="widget._html" class="widget__content" />
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    group: {
      default() {
        return {}
      },
      required: true,
      type: Object
    }
  }
}
</script>

<style lang="scss" scoped>
.group-summary {
  ::v-deep .widget-summary__item ul {
    list-style-type: disc;
    padding-left: 40px;
  }
  .btns-wrap {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 8px 20px;
    text-align: right;
    left: 0;
    background: #fff;
  }
  .group-summary-box {
    h2 {
      position: relative;
      font-weight: 600;
      font-size: 24px;
      line-height: 29px;
      margin-bottom: 24px;
      padding-left: 16px;
      &::before {
        position: absolute;
        z-index: 11;
        left: 0;
        top: 4px;
        height: 20px;
        content: '';
        border-left: 4px solid #0091ff;
      }
    }
    .summary__header {
      font-weight: 600;
      font-size: 16px;
      margin-bottom: 24px;
    }
    .widget__content {
      margin-bottom: 24px;
    }
  }
  ::v-deep {
    .variable {
      color: #27aa7a;
    }
    .variable-complete {
      color: #16aa77;
      &.variable-input {
        background-color: rgba(198, 241, 221, 0.3);
      }
    }
    .variable-undone {
      color: #e64340;
      &.variable-input {
        background-color: rgba(255, 210, 210, 0.3);
      }
    }
  }
}
</style>
