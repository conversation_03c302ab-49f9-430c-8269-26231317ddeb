const TIMEZONE_OFFSET = new Date().getTimezoneOffset() * 60 * 1000
const commonmark = require('commonmark')
import moment from 'moment'
import xssFilters from 'xss-filters'

export const widgetType = {
  1: 'Attr',
  2: 'Sing',
  3: 'Rich',
  4: 'Img',
  5: 'Addr',
  6: 'Dura'
}

export function parseSecond(time = 0) {
  let days = Math.floor(time / 1440 / 60)
  let hours = Math.floor((time - days * 1440 * 60) / 3600)
  let minutes = Math.floor((time - days * 1440 * 60 - hours * 3600) / 60)

  return {
    days: days,
    hours: hours,
    minutes: minutes
  }
}

export function parseDuration(index, widgetsData) {
  let { value } = _.get(widgetsData, `${index}].attr_values[0]`, {
    value: '{}'
  })
  let current = toString.call(value) === '[object Object]' ? value : JSON.parse(value || '{}')

  if (current.single) {
    if (current.from === 0) {
      return ''
    }
    return fmtDate(parseSecond(current.from))
  } else if (
    (!current.single && current.from === 0 && current.to === 0) || //
    current.single === undefined // reset data
  ) {
    return ''
  } else {
    return `
            <span class="widget__span">${__('act_from')}</span>${fmtDate(parseSecond(current.from))}
            <br />
            <span class="widget__span">${__('act_to')}</span>${fmtDate(parseSecond(current.to))}
          `
  }
}

export function fmtDate({ days, hours, minutes }) {
  return (
    '<span class="widget__duration">' +
    '&nbsp;&nbsp;' +
    days +
    '&nbsp;&nbsp;' +
    __('act_days') +
    '&nbsp;&nbsp;' +
    hours +
    '&nbsp;&nbsp;' +
    __('act_hours') +
    '&nbsp;&nbsp;' +
    minutes +
    '&nbsp;&nbsp;' +
    __('act_minutes') +
    '</span>'
  )
}

export function parseImg(index, widgetsData) {
  let { attr_values } = widgetsData[index] || {}
  attr_values = attr_values || []

  return attr_values.map((item) => {
    let data = {}
    try {
      data = JSON.parse(item.value || '{}')
    } catch (e) {
      data = {}
    }
    data = toString.call(data) === '[object Object]' ? data : {}
    return `
            <li class="widget__li">
              <a class="widget__imagePath" href="https://res.klook.com/image/upload/activities/${data.url}" target="_blank">${data.url}</a>
            </li>
          `
  })
}

export function fmtMd(value, inline = false) {
  let reader = new commonmark.Parser()
  let writer = new commonmark.HtmlRenderer()

  value = value.replace(/javascript:/g, encodeURIComponent('javascript:'))

  let parsed = reader.parse(xssFilters.inHTMLData(value)) // parsed is a 'Node' tree
  let html = writer.render(parsed)

  if (inline) {
    return html.replace(/(<p)(\s*.*>\s*.*<\/)(p>)/g, '<p style="display: inline;"' + '$2' + 'p>')
  }
  return html
}

export function parseMd(index, widgetsData) {
  let value = _.get(widgetsData, `[${index}].attr_values[0].value`, '')
  return fmtMd(value)
}

export function parseTxt(index, widgetsData) {
  let value = _.get(widgetsData, `[${index}].attr_values[0].value`, '')
  return [fmtMd(value)].filter((item) => item)
}

export function parseAttr(index, widget, widgetsData, widgetsList, all_variable) {
  let { attr_values } = widgetsData[index] || {}
  attr_values = _.sortBy(attr_values || [], 'ui_index')

  return attr_values.map((attrVal) => {
    // 遍历widget的属性项
    let { variable_map, is_free_text } = attrVal
    let value = ''

    if (is_free_text) {
      value = `<span class="variable-input variable-complete">${fmtMd(attrVal.value)}</span>`
    } else {
      value = widgetsList.reduce((acc, curr) => {
        // 活动 widget list 的对应配置value
        let current = _.find(curr.attr_value_list, {
          id: attrVal.id
        })
        if (current) {
          return current
        }
        return acc
      }, {}).value
    }

    variable_map = variable_map || {}
    let allVariable = typeof value === 'string' ? value.match(/{{[^{}]+}}/g) || [] : [] // fixme 前一个 widgetsData 污染

    allVariable.forEach(async (variable) => {
      // 遍历所有的变量，并替换（若有）
      let currentVariableData = variable_map[variable]
      if (!currentVariableData) return

      let isSelect = [1, 4, 5, 6, 7].includes(currentVariableData.type)
      let className = isSelect ? 'variable-select' : 'variable-input'
      if (currentVariableData && Array.isArray(currentVariableData.values)) {
        let _html = _.get(attrVal, 'showText._html')
        if (_html) {
          value = _html
        } else {
          // Parse and get _html data
          let { attr_value_list, type } =
            _.find(all_variable, {
              id: currentVariableData.attr_item_id
            }) || {}

          value = value.replace(
            variable,
            `<span class="variable ${className} variable-complete">${currentVariableData.values
              .map((value) => {
                if (value.is_free_text) {
                  if ([4, 5].includes(type)) {
                    let temp = +value.value + TIMEZONE_OFFSET
                    return value
                      ? type === 4
                        ? moment(temp).format('YYYY-MM-DD')
                        : moment(temp).format('YYYY-MM-DD HH:mm:ss')
                      : ''
                  }
                  return fmtMd(value.value, true)
                }

                if (attr_value_list) {
                  return (
                    _.find(attr_value_list, {
                      id: value.id
                    }) || {}
                  ).value
                }

                return value.value || ''
              })
              .join('/')}</span>`
          )
        }
      } else {
        value = value.replace(variable, `<span class="${className} variable-undone">${variable}</span>`)
      }
    })

    return value
  })
}

// 计算 group  summary 需要先格式化 group instance data 才能用，待优化
export const computedGroupSummary = function(widgetsData, widgetsList, all_variable) {
  return (widgetsData || [])
    .map((widget, index) => {
      if (!widget.ui_conf) {
        widget.ui_conf = widgetsList.find((item) => item.id === widget.id) || {}
      }

      let html = ''
      switch (widget.ui_conf.widget_type) {
        case 1:
          html = parseAttr(index, widget, widgetsData, widgetsList, all_variable)
          break
        case 2: {
          html = parseTxt(index, widgetsData)
          break
        }
        case 3: {
          html = parseMd(index, widgetsData)
          break
        }
        case 4:
          html = parseImg(index, widgetsData)
          break
        case 6:
          html = parseDuration(index, widgetsData)
          break
      }
      let linkWarn = false
      if (widget.ui_conf.widget_type === 3) {
        try {
          linkWarn = html
            .replace(/<[^<>]*>/gi, '')
            .split(/\r|\n/g) // parse markdown DOM label
            .some((item) => {
              let res = item.match(/\[[^\]]+]\(([^)]+)\)/) // parse [xxx](url...) syntax
              if (
                res &&
                !(
                  res[1].match(/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?/) &&
                  -1 === res[1].indexOf(' ')
                ) // validate url // validate blank
              ) {
                let rep = `(${res[1]})`
                html = html.replace(
                  rep,
                  `
                      <span title="The wrong link. Please check." style="display: inline-block; background-color: red; padding: 0 4px; color: #fff;">
                        ${rep}
                      </span>
                    `
                )
                return true
              }
              return false
            })
        } catch (err) {
          console.warn('Parse link error: ', err)
        }
      }

      let currentWidget = _.find(widgetsList, {
        id: widget.id
      })

      return {
        ...widget.ui_conf,
        _title: widgetType[widget.ui_conf.widget_type],
        _html: html,
        linkWarn,
        name: currentWidget ? currentWidget.name : widget.ui_conf.name
      }
    })
    .filter(
      (item) =>
        (typeof item._html === 'string' && item._html !== '') ||
        (Array.isArray(item._html) && item._html.length)
    )
}
