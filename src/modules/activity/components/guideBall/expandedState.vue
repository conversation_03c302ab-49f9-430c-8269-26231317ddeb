<template>
  <div class="expanded-state-container">
    <header class="header">
      <label class="title">{{ $t('84092') }}</label>
      <a-tooltip placement="top" title="Collapse">
        <a-icon class="non-fullscreen" type="fullscreen-exit" @click="$emit('fold')" />
      </a-tooltip>
    </header>

    <div class="progress">
      <a-progress
        stroke-linecap="round"
        :percent="currPercent"
        :stroke-color="strokeColor"
        :show-info="isCompleted"
      />
      <label class="progress-content">
        <span class="progress-content-text">{{ $t('84093') }}</span>
        <!-- eslint-disable-next-line prettier/prettier -->
        <NumberIncrementAmt
          :value="percent"
          :style="progressNumberStyle"
          @transitionend="$emit('numIncrementTransitionend')"
          >%</NumberIncrementAmt
        >
      </label>

      <transition name="heighten">
        <div v-show="showSubmitTip" class="submit-tip-wrap">
          <div>{{ $t('174936') }}</div>
          <div class="submit-tip-checkbox">
            <a-checkbox v-model="notShowAgain">
              {{ $t('193304') }}
            </a-checkbox>
          </div>
          <a-button
            size="small"
            type="primary"
            class="submit-tip-btn"
            @click="$emit('confirm', notShowAgain)"
          >
            {{ $t('global_submit') }}
          </a-button>
        </div>
      </transition>
    </div>

    <div class="to-do-list-wrap">
      <div v-if="requiredSteps.length" class="wrap-item list-required-wrap">
        <div class="wrap-title">{{ $t('174938') }}</div>
        <div v-for="(item, index) in requiredSteps" :key="`${item.step}_${index}`" class="item">
          <div class="item-step-name">
            <span class="item-dot"></span>
            <label>{{ item.guideTitle }}&nbsp;×&nbsp;{{ item.amount }}</label>
          </div>
          <a-button v-bind="getBindTrack(item)" size="small" type="primary" @click="handleGoTo(item)">{{
            item.btnTxt
          }}</a-button>
        </div>
      </div>

      <div v-if="suggestSteps.length" class="wrap-item list-suggest-wrap">
        <div class="wrap-title">{{ $t('174940') }}</div>
        <div v-for="(item, index) in suggestSteps" :key="`${item.step}_${index}`" class="item is-suggest">
          <div class="item-step-name">
            <span class="item-dot"></span>
            <label>{{ item.guideTitle }}&nbsp;×&nbsp;{{ item.amount }}</label>
          </div>
          <a-button v-bind="getBindTrack(item)" size="small" type="default" @click="handleGoTo(item)">{{
            item.btnTxt
          }}</a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { getPercentColor, STEP_TYPE_DICT } from './utils'
import { getEditLang, addEventListener } from '@activity/utils'
import NumberIncrementAmt from './numberIncrementAnimation'
import { scrollIntoView } from '@activity/pages/activityManagement/detailV2/utils'

const BASIC_INFO_STEP = ['activity_basic_info', 'package_basic_info']
const DETAIL_STEP = ['activity_detail_info', 'package_detail_info']

export default {
  name: 'ExpandedState',
  components: { NumberIncrementAmt },
  props: {
    activityId: {
      type: Number,
      default: 0
    },
    percent: {
      type: Number,
      required: true
    },
    steps: {
      type: Array,
      default: () => []
    },
    showSubmitTip: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      STEP_TYPE_DICT,
      currPercent: 0,
      progressAnimationEnd: false,
      notShowAgain: false
    }
  },
  computed: {
    ...mapState({
      actStepStatusObj: 'actStepStatus'
    }),
    ...mapGetters(['getPkgInfos2getters']),
    isCompleted() {
      return this.currPercent === 100
    },
    progressNumberStyle() {
      return { color: this.strokeColor, fontSize: '14px', fontWeight: 600 }
    },
    strokeColor() {
      return getPercentColor(this.percent)
    },
    requiredSteps() {
      return this.steps.filter((item) => item.type === STEP_TYPE_DICT.required)
    },
    suggestSteps() {
      return this.steps.filter((item) => item.type === STEP_TYPE_DICT.suggest)
    }
  },
  watch: {
    percent() {
      this.currPercent = this.percent
    },
    showSubmitTip: {
      immediate: true,
      handler() {
        this.showSubmitTip && this.$emit('showSubmitTip')
      }
    }
  },
  mounted() {
    setTimeout(() => {
      this.currPercent = this.percent
    }, 60)
    this.animationend = addEventListener(this.$el.querySelector('.submit-tip-wrap'), 'animationend', () => {
      this.showSubmitTip && this.$emit('tipAnimationend')
    })
  },
  beforeDestroy() {
    this.animationend.remove?.()
  },
  methods: {
    scrollIntoView,
    getBindTrack(item) {
      const spm = item?.type === STEP_TYPE_DICT.required ? 'Todolist_required' : 'Todolist_todo'
      return this.$root.bindIHTrack({
        type: 'module',
        spm,
        exposure: false,
        query: {
          oid: `activity_${this.activityId}`
        }
      })
    },
    pmsInterceptor() {
      let pmsResolve
      let pms = new Promise((resolve) => {
        pmsResolve = resolve
      })

      pms.done = function done() {
        pmsResolve()
      }

      return pms
    },
    getTargetHeight(selector) {
      const target = document.querySelector(selector)

      return target?.getBoundingClientRect?.()?.height ?? 0
    },
    async handleDetailStep({ type }) {
      let selector = '.widget-item-container[data-is-empty="true"]'

      if (STEP_TYPE_DICT.required === type) {
        selector += '[data-is-required="true"]'
      } else {
        selector += '[data-is-suggest-filling="true"]'
      }

      const target = document.querySelector(selector)
      if (target?.__vue__?.emitUnfoldGroup) {
        target.__vue__.emitUnfoldGroup()
        await this.$nextTick()
        this.scrollIntoView(target, {
          offsetY: this.offsetY
        })
      } else {
        document.querySelector('.presenter-container')?.__vue__?.handleDisplaySectionList?.()
        await this.$nextTick()
        const newTarget = document.querySelector(
          '.chose-list-container .js-scroll-to-view[data-is-suggest-filling="true"][data-is-checked="false"]'
        )
        this.scrollIntoView(newTarget, {
          offsetY: 0,
          type: 'scrollIntoView'
        })
      }
    },
    async handleBasicInfo({ data, type }) {
      for (let item of data || []) {
        const { notFilled } = item

        for (let curr of notFilled) {
          let selector = `.js-dataset-field[data-field="${curr.field}"]`
          let flag = false

          if (STEP_TYPE_DICT.required === type && curr.required) {
            flag = true
            selector += '[data-is-required="true"]'
          } else if (STEP_TYPE_DICT.suggest === type && curr.config?.suggest_filling) {
            flag = true
            selector += '[data-is-suggest-filling="true"]'
          }

          if (flag) {
            const $target = document.querySelector(selector)

            if (!$target || $target.getBoundingClientRect().height < 4) {
              this.$message.info(`The current field is not visible.(${curr.field})`)

              return
            }

            // hack 若是 field 展示属性组件，则需要展开
            const fieldBindingGroupVm = $target.querySelector?.('.field-binding-group-container')?.__vue__
            if (fieldBindingGroupVm) {
              fieldBindingGroupVm.$emit?.('unfoldAllGroups')
              await this.$nextTick()
            }

            this.scrollIntoView(selector, {
              offsetY: this.offsetY
            })
            return
          }
        }
      }
    },
    async scrollToSuggestFieldEle(result = {}) {
      const { data } = result
      await this.$nextTick()

      if (this.offsetY === undefined) {
        // 4 是预留间隙
        this.offsetY =
          (this.getTargetHeight('#klook-header') + this.getTargetHeight('.js-anchor-act-menus') + 4) * -1
      }

      const { type, step } = this.currActionItem

      if (DETAIL_STEP.includes(step)) {
        this.handleDetailStep({ type })

        return
      }

      this.handleBasicInfo({ type, data })
    },
    async handleGoTo(item) {
      this.currActionItem = item

      const { $route } = this
      const pkgId = +$route.query.package_id
      const { activity_id, package_id } = item.items[0]
      const path = item.path + activity_id

      let sku_id = ''
      if (item.step === 'price_inventory') {
        let interceptor = this.pmsInterceptor()
        klook.bus.$emit('getPackageSkuDataBus', package_id, ({ response }) => {
          sku_id = response?.units?.[0]?.sku_id ?? ''
          interceptor.done()
        })
        await interceptor

        // 下面的判断逻辑复制于 src/modules/activity/components/merchant/nav.vue?gotoPkgUnit#L778
        if (
          this.actStepStatusObj?.activity_basic_info !== 1 &&
          this.getPkgInfos2getters.packages.find((pkg) => pkg.package_id === package_id)?.time_zone === ''
        ) {
          this.$message.warn(this.$t('83890'))
          return
        }
      }

      const isCurrPage = (item.isAct || pkgId === package_id) && path === $route.path

      if ([...BASIC_INFO_STEP, ...DETAIL_STEP].includes(item.step)) {
        if (isCurrPage) {
          if (BASIC_INFO_STEP.includes(item.step)) {
            klook.bus.$once('scrollToSuggestField2bus', this.scrollToSuggestFieldEle)
            // 基本信息页面需要重新出发 initFloorTimelineData 去计算填写情况
            document.querySelector('.js-basic-info-vm')?.__vue__?.initFloorTimelineData?.()
          } else {
            this.scrollToSuggestFieldEle()
          }

          return
        }

        klook.bus.$once('scrollToSuggestField2bus', this.scrollToSuggestFieldEle)
      } else if (isCurrPage) {
        // 如果是其他的步骤在当前页面，不需要处理
        return
      }

      this.$router.push({
        path,
        query: {
          ...this.$route.query,
          lang: getEditLang(),
          ...(item.isAct
            ? {}
            : {
                package_id,
                sku_id
              })
        }
      })
    },
    onTransitionend() {
      this.$emit('update:showSubmitTip', this.isCompleted)
    }
  }
}
</script>

<style lang="scss" scoped>
$requiredColor: #ff4d4f;
$suggestColor: #437dff;

.expanded-state-container {
  font-weight: 400;

  ::v-deep {
    .ant-progress-status-success .ant-progress-text {
      color: #08979c;
    }
  }

  .header {
    display: flex;
    justify-content: space-between;
    color: #212121;

    .title {
      font-weight: 600;
      font-size: 14px;
      line-height: 150%;
    }
  }

  .progress {
    margin-top: 14px;

    .progress-content {
      display: flex;
      justify-content: flex-end;
      margin-top: 6px;
      font-size: 14px;
      line-height: 21px;
    }

    .progress-content-text {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  ::v-deep .ant-progress-inner {
    .ant-progress-bg {
      transition: all 2s cubic-bezier(0.09, 0.41, 0.81, 1.03) 0s;
    }
  }

  .to-do-list-wrap {
    .wrap-item {
      margin-top: 12px;
    }

    .wrap-title {
      margin-bottom: 4px;
      font-size: 12px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.45);
    }

    .item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-top: 8px;
      color: $requiredColor;

      &:first-child {
        margin-top: 0;
      }

      &.is-suggest {
        color: $suggestColor;
      }

      .item-step-name {
        display: inline-flex;
        align-items: baseline;
        gap: 8px;
        margin-right: 8px;
        font-size: 12px;
        line-height: 1.5em;

        ::v-deep .anticon {
          margin-top: 4px;
          margin-right: 8px;
        }

        label {
          user-select: initial;
          cursor: default;
        }
      }

      .item-dot {
        width: 4px;
        height: 4px;
        flex: 0 0 4px;
        transform: translateY(-2px);
        border-radius: 4px;
        background-color: $requiredColor;
      }

      &.is-suggest .item-dot {
        background-color: $suggestColor;
      }

      ::v-deep .ant-btn {
        height: 20px;
        line-height: 18px;
      }
    }
  }

  .submit-tip-wrap {
    position: relative;
    width: 100%;
    padding: 8px 8px 36px 8px;
    margin-top: 8px;
    border-radius: 4px;
    background-color: #f5f5f5;
    font-size: 12px;
    line-height: 20px;
    color: rgba(0, 0, 0, 0.85);

    &.show {
      height: 84px;
    }

    &:before {
      content: '';
      position: absolute;
      right: 12px;
      top: -8px;
      width: 0px;
      height: 0px;
      border-right: 8px solid transparent;
      border-left: 8px solid transparent;
      border-bottom: 8px solid #f5f5f5;
    }

    .submit-tip-checkbox {
      display: flex;
      justify-content: flex-end;
      margin-top: 4px;

      ::v-deep .ant-checkbox-wrapper span {
        padding-right: 0;
      }
    }

    .submit-tip-btn {
      position: absolute;
      bottom: 8px;
      right: 8px;
      height: 18px;
      line-height: 16px;
      font-size: 12px;
      transition: all 0.3s;
      transform-origin: top right;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .heighten-enter-active {
    animation: heighten-in 0.6s ease-in-out 1 forwards;
  }

  .heighten-leave-active {
    animation: heighten-in 0.6s reverse ease-in-out 1 forwards;
  }

  @keyframes heighten-in {
    0% {
      height: 0;
      padding-top: 0;
      padding-bottom: 0;
      opacity: 0;
    }
    50% {
      opacity: 0.2;
    }
    100% {
      height: 84px;
      padding-top: 8px;
      padding-bottom: 36px;
      opacity: 1;
    }
  }
}
</style>
