<template>
  <div class="folded-state-container">
    <PercentCircle :percent="percent" @numIncrementTransitionend="$emit('numIncrementTransitionend')" />

    <div class="to-do">{{ $t('84091') }}</div>

    <a-tooltip placement="top" title="Extend">
      <a-icon class="fullscreen" type="fullscreen" @click="$emit('expand')" />
    </a-tooltip>
  </div>
</template>

<script>
import PercentCircle from './percentCircle.vue'
import { getPercentColor } from './utils'

export default {
  name: 'FoldedState',
  components: {
    PercentCircle
  },
  props: {
    percent: {
      type: Number,
      required: true
    }
  },
  data() {
    return {}
  },
  computed: {
    borderColor() {
      return getPercentColor(this.percent)
    },
    backgroundColor() {
      return getPercentColor(this.percent, true)
    }
  }
}
</script>

<style lang="scss" scoped>
.folded-state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #212121;

  .to-do {
    margin-top: 8px;
    font-weight: 600;
    font-size: 12px;
    line-height: 150%;
  }

  .fullscreen {
    margin-top: 12px;
    cursor: pointer;
  }
}
</style>
