<template>
  <Transition name="fade">
    <DragAndDrop
      v-if="active && switchActive"
      use-right-position
      storage-name="drag-and-drop-guide-ball"
      :can-drag="isFolded"
      :initial-position="{ right: '20px', top: '204px' }"
      :get-boundary-element="getBoundaryElement"
      @dragend="onDragend"
    >
      <div
        ref="guideBall"
        class="guide-ball-container"
        :class="{ 'is-folded': isFolded, 'is-expanded': !isFolded, 'open-animation': openAnimation }"
        :style="{
          '--container-height': expandContainerHeight ? expandContainerHeight + 'px' : 'initial',
          '--container-max-height': maxExpandContainerHeight + 'px',
          '--border-color': borderColor,
          '--background-color': backgroundColor
        }"
      >
        <a-popconfirm
          v-if="isFolded"
          :visible="confirmVisible"
          placement="leftTop"
          :ok-text="$t('174937')"
          :cancel-button-props="cancelButtonProps"
          @confirm="() => onConfirm(notShowAgain)"
        >
          <div slot="title" :style="{ maxWidth: '236px', fontSize: '12px' }">
            {{ $t('174936') }}

            <div style="margin-top: 4px">
              <a-checkbox v-model="notShowAgain">
                {{ $t('193304') }}
              </a-checkbox>
            </div>
          </div>
          <FoldedState
            ref="foldedState"
            :percent="percent"
            @expand="onExpand"
            @numIncrementTransitionend="onNumIncrementTransitionend"
          />
        </a-popconfirm>
        <ExpandedState
          v-else
          ref="expandedState"
          :activity-id="actId"
          :percent="percent"
          :steps="steps"
          :show-submit-tip="showSubmitTip"
          @numIncrementTransitionend="onNumIncrementTransitionend"
          @confirm="onConfirm"
          @fold="onFold"
          @tipAnimationend="onTipAnimationend"
        />

        <stkModal :modal-data="stkData" />
      </div>
    </DragAndDrop>
  </Transition>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import ExpandedState from './expandedState'
import FoldedState from './foldedState'
import { getPercentColor, STEP_TYPE_DICT } from './utils'
import { getEditLang } from '@activity/utils'
import actNavLib from '@activity/components/merchant/actNavLib.vue'
import DragAndDrop from '@activity/components/DragAndDrop.vue'
import { STORAGE_KEY_DICT, EVENT_BUS_KEY } from '@activity/utils/const.js'
import { scrollToAsync, getUuid, addEventListener } from '@activity/utils/index.js'
import merchantPkgInfoMixin from '@activity/components/pkgInfo/mixins/merchant-pkg-info.js'
import { bestMatchLang } from '@activity/utils'

import Driver from 'driver.js'
const driver = new Driver({
  allowClose: false,
  showButtons: false,
  overlayClickNext: false
})

const STATE_DICT = {
  fold: 'fold',
  expand: 'expand'
}

export default {
  name: 'GuideBall',
  components: {
    ExpandedState,
    FoldedState,
    DragAndDrop
  },
  mixins: [merchantPkgInfoMixin],
  data() {
    this.STATE_DICT = STATE_DICT

    return {
      driver,
      active: false,
      switchActive: true,
      percent: 0,
      state: STATE_DICT.expand,
      steps: [],
      // 用于展开的时候 height 动画
      expandContainerHeight: 0,
      maxExpandContainerHeight: 0, // 包含可能存在的 confirm tip height, 若没则为0
      // 初始化的时候不需要动画
      openAnimation: false,
      showSubmitTip: false,
      confirmVisible: false,
      requiredStep: [],
      cancelButtonProps: {
        style: {
          display: 'none'
        }
      },
      notShowAgain: false
    }
  },
  computed: {
    ...mapGetters({
      requiredPackageIds: 'requiredPackageIdsGetter',
      getPkgInfos2getters: 'getPkgInfos2getters',
      getActItemData2getters: 'getActItemData2getters'
    }),
    actStatus() {
      return this.getActItemData2getters?.activity_status || 0
    },
    actApprovalStatus() {
      return this.getActItemData2getters?.approval_status || 0
    },
    calcItinerarySupportConfig() {
      return this.$store.state.actItemData?.itinerary_support_config
    },
    allPackages() {
      return this.getPkgInfos2getters?.packages || []
    },
    actId() {
      return klook.parse2id(this.$route.params.id)
    },
    isFolded() {
      return this.state === STATE_DICT.fold
    },
    isCompleted() {
      return this.percent === 100
    },
    shouldShowSubmitTip() {
      // approval_status	活动审核状态：0:edit(此时 Editable）；1: pending approval(此时Non-Editable)；2: Rejected（此时 Editable）；3: Approved(此时Non-Editable)
      return ![1, 3].includes(this.actApprovalStatus) && this.isCompleted
    },
    borderColor() {
      return getPercentColor(this.percent)
    },
    backgroundColor() {
      return getPercentColor(this.percent, true)
    },
    actStepList() {
      return actNavLib.methods.getRoleData.call(this, 'actList').map((item) => ({
        ...item,
        isAct: true
      }))
    },
    pkgStepList() {
      const priceStep = {
        step: 'price_inventory',
        require: true,
        key: 'packageUnit',
        text: __('price_inventory_page'),
        path: '/act/package/unit/',
        passCheck: true,
        guideTitle: __('84086')
      }
      const itinerary_support_config = this.calcItinerarySupportConfig
      return [
        ...actNavLib.methods.getRoleData.call(this, 'pkgList', {
          itinerary_support_config,
          apiMappingSupportConfig: {
            support_supply_api_mapping: 2, // 0 隐藏, 1 只读, 2 读写
            is_required: true
          }
        }),
        priceStep
      ]
    }
  },
  watch: {
    'stkData.visible'() {
      this.showSubmitTip = this.confirmVisible = false
      this.switchGuideActiveState(true)
    }
  },
  mounted() {
    // emit refreshGuideStatus
    // 活动层加在 basicInfo/index、components/presenter.vue
    // 套餐层加在 updatePkgInfos2bus 方法里
    klook.bus.$off('refreshGuideStatus').$on('refreshGuideStatus', this.initData)
    this.$once('hook:beforeDestroy', () => {
      klook.bus.$off('refreshGuideStatus')
    })

    klook.bus.$off('switchGuideActiveState').$on('switchGuideActiveState', this.switchGuideActiveState)
    this.$once('hook:beforeDestroy', () => {
      klook.bus.$off('switchGuideState')
    })

    this.initData()
  },
  methods: {
    ...mapMutations(['setGuideDataStatus']),
    getBoundaryElement() {
      return document.querySelector('.js-action-act-main-warp')
    },
    switchGuideActiveState(value) {
      this.switchActive = value
    },
    initData: _.debounce(async function initData() {
      let response = await ajax.get(ADMIN_API.act.get_data_status, {
        params: {
          activity_id: this.actId,
          language: getEditLang()
        }
      })

      if (response) {
        this.setGuideDataStatus(response)
        const { completeness, required_step, suggest_step } = response
        this.percent = completeness
        this.requiredStep = required_step || []
        this.$set(this, 'steps', [
          ...this.formatStepData(required_step, STEP_TYPE_DICT.required, response),
          ...this.formatStepData(suggest_step, STEP_TYPE_DICT.suggest, response)
        ])

        if (completeness !== 100 || suggest_step.length) {
          this.active = true
          this.expandContainerHeight = 0

          if (this.isFolded) {
            this.openAnimation = false
          } else {
            await this.$nextTick()
            this.setExpandContainerHeight()
          }

          return
        }
      }

      this.active = false
    }, 100),
    formatStepData(steps, type, response) {
      steps = steps || []
      const dict = [...this.actStepList, ...this.pkgStepList]
      const { activity_steps, package_steps } = response

      return steps.reduce((acc, curr) => {
        let { items } = curr
        const result = dict.find((item) => item.step === curr.step) || {}

        const data = {
          ...curr,
          ...result,
          type,
          isAct: !!result.isAct,
          btnTxt: this.$t('174939')
        }

        let validItems = []
        if (result.isAct) {
          let validSteps = this.getValidSteps(activity_steps, this.actStepList, {
            initSteps: ['activity_basic_info']
          })

          if (validSteps.includes(curr.step)) {
            validItems = items
          }
        } else {
          validItems = items.filter((item) => {
            let validSteps = this.getValidSteps(package_steps[item.package_id] || [], this.pkgStepList, {
              initSteps: ['package_basic_info'],
              fixedValidSteps: ['price_inventory']
            })

            return validSteps.includes(curr.step)
          })
        }

        if (validItems.length) {
          data.amount = items.length
          data.items = validItems
          acc.push(data)
        }

        return acc
      }, [])
    },
    getValidSteps(guideSteps, navOrderSteps, { initSteps = [], fixedValidSteps = [] } = {}) {
      let validSteps = []
      const stepKeys = guideSteps.map((item) => item.step)

      for (let item of navOrderSteps) {
        const { step } = item
        if (
          !item.require || // 非必填
          item.passCheck || // 不需要检查的无顺序步骤
          stepKeys.includes(step) // 已有完成记录
        ) {
          validSteps.push(step)
        } else {
          // 下一步加上
          validSteps.push(step)
          break
        }
      }

      if (!validSteps.length) {
        // 如果为空, 则补充初始步骤
        validSteps.push(...initSteps)
      }

      return [...validSteps, ...fixedValidSteps]
    },
    setExpandContainerHeight() {
      setTimeout(() => {
        const height = this.getGuideBallHeight()
        if (height !== undefined) {
          this.cacheExpandContainerHeight = this.expandContainerHeight = height
        }
      }, 60)
    },
    async onExpand() {
      this.state = STATE_DICT.expand
      this.showSubmitTip = this.confirmVisible = false
      if (!this.openAnimation) {
        this.setExpandContainerHeight()
      }
    },
    onFold() {
      this.state = STATE_DICT.fold
      this.openAnimation = true
      this.showSubmitTip = this.confirmVisible = false
      // 收起时, 不需要最大高度, 由 onTipAnimationend 重新计算
      this.maxExpandContainerHeight = 0
    },
    setProgressByPercent(percent) {
      const circleDom = document.querySelector('.process-circle')
      const circleLen = circleDom.getTotalLength()

      circleDom.style.strokeDashoffset = circleLen * (1 - parseInt(percent) / 100)
    },
    async onNumIncrementTransitionend() {
      if (
        !localStorage.getItem(STORAGE_KEY_DICT.first_create_act) ||
        localStorage.getItem(STORAGE_KEY_DICT.first_create_pkg) === 'true'
      ) {
        this.showSubmitTip = this.confirmVisible = this.shouldShowSubmitTip
      }

      if (this.shouldShowSubmitTip && !this.isFolded) {
        this.expandContainerHeight = 0
      }
    },
    onTipAnimationend() {
      this.expandContainerHeight = this.cacheExpandContainerHeight

      const height = this.getGuideBallHeight()
      if (height !== undefined) {
        this.maxExpandContainerHeight = height
      }
    },
    getGuideBallHeight() {
      const $container = document.querySelector('.guide-ball-container')
      if ($container) {
        return $container.getBoundingClientRect().height
      }
    },
    onDragend() {
      this.confirmVisible = false
    },
    async onConfirm(notShowAgain) {
      if (notShowAgain) {
        if (!localStorage.getItem(STORAGE_KEY_DICT.first_create_act)) {
          localStorage.setItem(STORAGE_KEY_DICT.first_create_act, 'false')
        } else {
          localStorage.setItem(STORAGE_KEY_DICT.first_create_pkg, 'false')
        }
      }

      await scrollToAsync(0)
      const isActPath = location.pathname.includes('/act/activity/')
      const isUnitPath = this.$route.name === 'packageUnit'

      if (isActPath) {
        this.handleActPath()
      } else if (isUnitPath) {
        this.triggerPackageSubmit()
      } else {
        this.handlePkgPath()
      }
    },
    async handleActPath() {
      if (this.actStatus === 0) {
        // unpublished
        this.submitDriver('#js-activity-submit')
      } else {
        this.triggerPackageSubmit()
      }
    },
    // 在当前组件内提交套餐/活动
    triggerPackageSubmit() {
      const { activity_id, activity_title } = this.getActItemData2getters
      const { ids, list } = this.allPackages.reduce(
        (acc, curr) => {
          const { approval_status, package_id, package_local } = curr

          if (approval_status) return acc

          const uncompleted = this.requiredPackageIds.includes(package_id)
          if (!uncompleted) {
            acc.ids.push(package_id)
          }

          acc.list.push({
            ...curr,
            activity_id,
            activity_title,
            uncompleted,
            disabled: false,
            package_title: bestMatchLang('name', 'language_type', package_local)
          })

          return acc
        },
        { ids: [], list: [] }
      )

      this.stkData.packageArr = list
      this.stkData.form.package_id_list = ids
      if (list.length) {
        this.stkData.visible = true
      }
    },
    handlePkgPath() {
      this.submitDriver('#js-package-submit')
    },
    async submitDriver(element) {
      this.switchGuideActiveState(false)
      const closeId = 'js-driver-close-' + getUuid()
      const appClassList = document.body.classList
      const driverStackingClassName = 'js-class-driver-fix-stacking-container'
      const existDriverFixStackingClass = appClassList.contains(driverStackingClassName)

      let closeRemover = null
      const destroy = () => {
        this.driver.reset()

        closeRemover?.remove?.()
        klook.bus.$off(EVENT_BUS_KEY.closeSubmitDriver)

        setTimeout(() => {
          // 复原原有方法
          this.showSubmitTip = this.confirmVisible = false
          this.switchGuideActiveState(true)
          existDriverFixStackingClass && appClassList.add(driverStackingClassName)
        })
      }

      this.driver.options.onHighlighted = () => {
        setTimeout(() => {
          // 扔到下个宏任务, 避免 target Null
          const target = document.querySelector(`#${closeId}`)
          closeRemover = addEventListener(target, 'click', destroy)
        })

        return null
      }

      this.driver.defineSteps([
        {
          element,
          popover: {
            className: 'js-guide-ball-driver-package-submit',
            position: 'left',
            title: `
                <div class="driver-content">
                  <span>${this.$t('174941')}</span>
                  <span id="${closeId}" class="driver-close">x</span>
                </div>
                `
          }
        }
      ])

      if (existDriverFixStackingClass) {
        appClassList.remove(driverStackingClassName)
      }
      this.driver.start()

      klook.bus.$once(EVENT_BUS_KEY.closeSubmitDriver, destroy)
      this.$once('hook:beforeDestroy', destroy)
    }
  }
}
</script>

<style lang="scss">
#driver-popover-item.js-guide-ball-driver-package-submit {
  max-width: 450px !important;

  .driver-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
    font-size: 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
  }

  .driver-close {
    cursor: pointer;
  }
}
</style>

<style lang="scss" scoped>
.guide-ball-container {
  // position: fixed;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.14);
  border-radius: 16px;
  z-index: 999;

  &.is-folded {
    padding: 8px;
    animation: fold 2s cubic-bezier(0.69, 0, 0.62, 1.01) 0s 1 normal forwards;

    .folded-state-container {
      opacity: 0;
      animation: fold-container 1s cubic-bezier(0.69, 0, 0.62, 1.01) 1s normal forwards;
    }
  }

  @keyframes fold {
    0% {
      width: 280px;
      height: var(--container-height);
      border: 2px solid #fff;
      background-color: #fff;
    }
    50% {
      width: 56px;
      height: 118px;
      border: 2px solid #fff;
      background-color: #fff;
    }
    100% {
      width: 56px;
      height: 118px;
      border: 2px solid var(--border-color);
      background-color: var(--background-color);
    }
  }

  @keyframes fold-container {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  &.is-expanded {
    width: 280px;
    padding: 24px;
    background-color: #fff;
    border: 2px solid transparent;

    .expanded-state-container {
      opacity: 0;
      animation: expand-container 1.5s cubic-bezier(0.69, 0, 0.62, 1.01) 0.8s normal forwards;
    }

    &.open-animation {
      border: 2px solid var(--border-color);
      background-color: var(--background-color);
      animation: expand 1s cubic-bezier(0.69, 0, 0.62, 1.01) 0s normal forwards;
    }
  }

  @keyframes expand {
    0% {
      width: 56px;
      height: 118px;
      border: 2px solid var(--border-color);
      background-color: var(--background-color);
    }
    50% {
      width: 56px;
      height: 118px;
      border: 2px solid transparent;
      background-color: #fff;
    }
    100% {
      width: 280px;
      height: max(var(--container-height), var(--container-max-height));
      border: 2px solid transparent;
      background-color: #fff;
    }
  }

  @keyframes expand-container {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
}
</style>
