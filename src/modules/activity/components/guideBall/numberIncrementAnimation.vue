<template>
  <div class="number-increment-animation" :style="{ '--percent': currVal, '--duration': duration + 's' }">
    <slot />
  </div>
</template>

<script>
import { addEventListener } from '@activity/utils/index.js'

export default {
  name: 'NumberIncrementAnimation',
  props: {
    value: {
      type: Number,
      required: true
    },
    duration: {
      type: Number,
      default: 3
    }
  },
  data() {
    return {
      currVal: 0
    }
  },
  watch: {
    value() {
      this.currVal = this.value
    }
  },
  mounted() {
    setTimeout(() => {
      this.currVal = this.value
    }, 60)

    this.transitionend = addEventListener(this.$el, 'transitionend', () => {
      this.$emit('transitionend')
    })
  },
  beforeDestroy() {
    this.transitionend.remove()
  }
}
</script>

<style lang="scss">
.number-increment-animation {
  margin: 0 4px;
  counter-reset: count var(--percent);
  transition: --percent var(--duration);

  &::before {
    content: counter(count);
  }
}

@property --percent {
  syntax: '<integer>';
  inherits: true;
  initial-value: 0;
}
</style>
