<template>
  <div
    class="progress-container"
    :data-percent="`${percent}%`"
    :style="{ width: width + 'px', height: height + 'px', '--step': currPercent }"
  >
    <!-- eslint-disable-next-line prettier/prettier -->
    <NumberIncrementAmt
      class="number-increment"
      :value="percent"
      @transitionend="$emit('numIncrementTransitionend')"
    />

    <svg v-if="active" :width="width" :height="height" :viewBox="viewBox">
      <circle :cx="cx" :cy="cy" :r="r" :stroke-width="strokeWidth" :stroke="strokeBackground" fill="none" />

      <circle
        class="process-circle"
        :cx="cx"
        :cy="cy"
        :r="r"
        :transform="transform"
        :stroke-width="strokeWidth"
        :stroke="stroke"
        fill="none"
        stroke-linecap="round"
        :stroke-dasharray="strokeDasharray"
        :style="{
          strokeDashoffset: strokeDashoffset
        }"
      />
    </svg>
  </div>
</template>

<script>
import { getPercentColor } from './utils'
import NumberIncrementAmt from './numberIncrementAnimation'

export default {
  name: 'PercentCircle',
  components: { NumberIncrementAmt },
  props: {
    width: {
      type: Number,
      default: 50
    },
    height: {
      type: Number,
      default: 50
    },
    strokeWidth: {
      type: Number,
      default: 4
    },
    percent: {
      type: Number,
      default: 0
    },
    strokeBackground: {
      type: String,
      default: 'white'
    }
  },
  data() {
    return {
      active: false,
      strokeDashoffset: 0,
      stroke: '#fff',
      currPercent: 0
    }
  },
  computed: {
    viewBox() {
      return `0 0 ${this.width} ${this.height}`
    },
    cx() {
      return this.width / 2
    },
    cy() {
      return this.height / 2
    },
    r() {
      return this.cx - this.strokeWidth
    },
    transform() {
      return `rotate(-90 ${this.cx} ${this.cy})`
    },
    strokeDasharray() {
      return 2 * Math.PI * this.r
    }
  },
  watch: {
    percent(val) {
      this.currPercent = this.percent
      this.setProgressByPercent(val)
    }
  },
  async mounted() {
    this.strokeDashoffset = this.strokeDasharray
    this.active = true
    await this.$nextTick()
    this.currPercent = this.percent
    this.setProgressByPercent(this.percent)
  },
  methods: {
    setProgressByPercent(percent) {
      const circleEle = document.querySelector('.process-circle')
      const circleLen = (circleEle && circleEle.getTotalLength()) || 0

      this.strokeDashoffset = circleLen * (1 - parseInt(percent) / 100)
      this.stroke = getPercentColor(percent)
    }
  }
}
</script>

<style lang="scss">
.progress-container {
  position: relative;

  .number-increment {
    position: absolute;
    width: 100%;
    top: 50%;
    transform: translateY(-50%);
    margin: 0;
    text-align: center;
    font-weight: 400;
    font-size: 12px;
    line-height: 14px;
    color: rgba(0, 0, 0, 0.85);
  }

  .process-circle {
    transition: stroke-dashoffset 2s cubic-bezier(0.09, 0.41, 0.81, 1.03) 0s,
      stroke 2s cubic-bezier(0.09, 0.41, 0.81, 1.03) 0s;
  }
}
</style>
