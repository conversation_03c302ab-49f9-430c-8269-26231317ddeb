export const PERCENT_COLOR_DICT = {
  good: {
    bg: '#e6fffb',
    color: '#08979c'
  },
  bad: {
    bg: '#faf0f0',
    color: '#f44622'
  }
}

export const getPercentColor = (percent, isBackground = false) => {
  if (percent <= 50) {
    return isBackground ? PERCENT_COLOR_DICT.bad.bg : PERCENT_COLOR_DICT.bad.color
  }

  return isBackground ? PERCENT_COLOR_DICT.good.bg : PERCENT_COLOR_DICT.good.color
}

export const STEP_TYPE_DICT = {
  required: 'required',
  suggest: 'suggest'
}
