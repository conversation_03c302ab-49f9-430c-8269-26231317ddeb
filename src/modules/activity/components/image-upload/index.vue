<!--
     this component is specifically used for upload image to cloudinary and so forth
     the API is to pass `public_id` and `format`  as `file_name` to backend with size info
     but this may change.

i.e., {
     "alt": "string",
     "description": "string",
     "file_name": "string",
     "width": 0,
     "height": 0,
     "flag": 0,
     "id": 0,
     }

   -->
<template>
  <div class="image-upload">
    <div class="image-upload-dragger" @click="beforeUpload">
      <template v-if="imageUrl">
        <img :src="imageUrl" class="image-upload-img" />
        <a-icon
          v-if="hasCloseBtn && !disabled"
          type="close-circle"
          class="image-upload-close"
          @click.native.stop="handleClearImage"
        />
        <a-tooltip v-if="hasShareBtn" class="item" placement="topLeft" title="Copy URL to clipboard">
          <a-icon
            type="link"
            class="image-upload-copy image-upload-bottom test"
            @click.stop="copyUrl"
          ></a-icon>
        </a-tooltip>
      </template>
      <a-icon v-else type="cloud-upload" class="image-upload-icon"></a-icon>
      <div class="image-upload-text">
        <em>click to upload</em><br />
        {{ placeholder }} <br />
        <!-- <template v-if="width && height"> {{ width }}px * {{ height }}px </template> -->
      </div>
      <div></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'image-upload',
  install(Vue) {
    Vue.component(this.name, this)
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    hasCloseBtn: {
      type: Boolean,
      default: true
    },
    hasShareBtn: {
      type: Boolean,
      default: false
    },
    value: {
      type: Object
    },
    folder: {
      type: String,
      default: 'activities'
    },
    // width: {
    //   type: [Number, String]
    // },
    // height: {
    //   type: [Number, String]
    // },
    placeholder: {
      type: String,
      default: 'png, jpg, jpeg up to 4 MB'
    },
    cloudinaryOptions: {
      type: Object
      /* default: {},*/
    },
    disabled: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      file: {},
      hostname: 'https://res.klook.com/image/upload',
      cloudinaryIsOpen: false
    }
  },
  computed: {
    imageUrl() {
      const { url } = this.value || {}

      if (!url) {
        return ''
      }

      return url.startsWith('http') ? url : `${this.hostname}/q_85,c_fill/${this.folder}/${url}`
    }
  },
  watch: {
    file: {
      deep: true,
      handler(file) {
        const filename = `${file.public_id}.${file.format}`.replace(`${this.folder}/`, '')
        this.$emit('change', {
          url: filename
        })
        this.$emit('validateField')
      }
    }
  },
  methods: {
    copyUrl() {
      klook.Clipboard.copy(this.imageUrl, (isSuc) => {
        isSuc ? this.$message.success('Copy Success') : this.$message.error('复制失败')
      })
    },
    async beforeUpload() {
      if (this.disabled || this.cloudinaryIsOpen) {
        return
      }

      this.cloudinaryIsOpen = true
      var [result, error] = await new Promise((resolve) =>
        window.cloudinary.openUploadWidget(
          Object.assign(
            {
              folder: this.folder,
              cloud_name: 'klook',
              multiple: false,
              upload_preset: 'k8xu3bkg',
              sources: ['local'],
              client_allowed_formats: ['png', 'gif', 'jpg', 'jpeg', 'svg'],
              resource_type: 'image',
              max_file_size: 4000000, // 4M
              max_image_width: 3000
            },
            this.cloudinaryOptions
          ),
          (error, result) => {
            if (result.event === 'success') {
              resolve([[result.info || {}], error])
              // this.$message.success(__('global_success'))
            }
          }
        )
      )
      this.cloudinaryIsOpen = false

      if (error) {
        console.error(error)
        return
      }
      this.file = result[0]
      this.$emit('uploaded', this.file)
    },
    handleClearImage() {
      this.$emit('change', null)
      this.$emit('validateField')
    }
  }
}
</script>

<style lang="scss">
.common-field-container .ant-form-item-control.has-error .image-upload-dragger {
  border-color: #f5222d;

  &:hover {
    border-color: #f5222d;
  }
}
</style>
<style lang="scss" scoped>
.image-upload {
  max-width: 360px;

  &-close,
  &-copy {
    padding: 8px;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 2;
    :hover {
      filter: brightness(1.5); // NOT WORKING?
    }
  }

  &-dragger {
    background-color: #fff;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    box-sizing: border-box;
    width: 100%;
    max-width: 360px;
    height: 180px;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &:hover {
      border-color: #1079fb;
    }
  }

  &-img {
    max-width: 100%;
    height: 100%;
  }

  &-text {
    color: #99a9bf;
    font-size: 14px;
    text-align: center;

    ::v-deep em {
      color: #1079fb;
      font-style: normal;
    }
  }

  &-icon {
    font-size: 67px;
    color: #99a9bf;
    margin: 40px 0 16px;
    line-height: 50px;
  }
}
</style>
