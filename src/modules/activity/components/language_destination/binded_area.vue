<template>
  <a-form-model ref="form" :model="form" class="common-tpl-basic-style" :colon="false">
    <DescMarkdownContent
      class="strong-tips"
      placement="right"
      :is-inline="false"
      :desc="schemaConfig && schemaConfig.strongTips"
      :line-clamp="3"
    />

    <template v-if="specialBinging">
      <a-form-model-item :label="$t('taxonomy_destination_destination')" prop="dest_ids" :rules="isRequired">
        <ShimAntdTooltip v-bind="$attrs" :title="$attrs.description">
          <a-select
            v-model="form.dest_ids"
            :placeholder="$t('type_to_search')"
            allow-clear
            mode="multiple"
            :disabled="disabled"
          >
            <template v-for="area in destinations">
              <a-select-option
                v-for="{ id, locales } in area.destinations"
                :key="id"
                :label="`${id} - ${getName(locales)}`"
                :value="id"
              >
                {{ `${id} - ${getName(locales)}` }}
              </a-select-option>
            </template>
          </a-select>
        </ShimAntdTooltip>
      </a-form-model-item>
    </template>

    <!-- Destination Model Type -->
    <component
      :is="[1, 4].includes(templateType) ? 'tpl_region_binding_1' : 'tpl_region_binding_2'"
      v-else
      ref="region_binding"
      v-bind="{
        ...$attrs,
        ...form.area
      }"
      :customCountryLabel="countryLabel"
      :customCityLabel="cityLabel"
      :disabledCity="calcDisabledCity"
      :country-options="calcCountryOptions"
      :all-country-options="countryOptions"
      :city-options="cityOptions"
      :poi-options="poiOptions"
      :required="required"
      :disabled="calcFlowDisabled || disabled"
      :multiple-departure-city="calcMultiDepartureCity"
      :multiple-departure-country="calcMultiDepartureCountry"
      :multiple-country="supportMultiDest"
      :multiple-city="supportMultiCity"
      :activity-id="activity_id"
      @change="changeAreaHandle"
      @updateCitys="updateCitys"
    >
      <div v-if="calcDisabledCity && tipsType" slot="tips" class="linked-destination-page">
        <!-- start
          queryCityPageErr 没有查询到关联的城市页
          queryCityPageSuc 查询到关联的城市页
          queryNoCity 无法推算出城市的提示文案
          initCityNeq 历史数据清洗，城市不一致
          initNoCity 历史数据清洗，城市不存在时字段下方的提示文案
        end -->
        <a-alert type="warning">
          <template slot="message">
            <span v-html="getTextStr(tipsType, destinationInfosNodes)"></span>
          </template>
        </a-alert>
      </div>
    </component>
  </a-form-model>
</template>

<script>
import DescMarkdownContent from '@activity/pages/activityManagement/detailV2/components/descMarkdownContent'
import tpl_region_binding_1 from './tpl_region_binding_1'
import tpl_region_binding_2 from './tpl_region_binding_2'
// import tpl_region_binding_3 from "../components/tpl_region_binding_3";
import { shouldIncludeUnpublishedCountryByAIDs } from './const'
import ShimAntdTooltip from '@activity/components/shimAntdTooltip'

export default {
  components: {
    DescMarkdownContent,
    tpl_region_binding_1,
    tpl_region_binding_2,
    ShimAntdTooltip
    // tpl_region_binding_3
  },
  inheritAttrs: false,
  props: {
    countryLabel: {
      type: String,
      default: ''
    },
    cityLabel: {
      type: String,
      default: ''
    },
    tipsType: {
      type: String,
      required: false,
      default: ''
    },
    destinationInfosNodes: {
      type: String,
      required: false,
      default: ''
    },
    activity_id: {
      type: [Number, String],
      required: true
    },
    schemaConfig: {
      type: Object,
      default: () => {}
    },
    result: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      cacheObj: {
        unpublishId: 1017,
        cache_country_id: undefined,
        cache_city_id: undefined
      },
      countryOptions: [],
      cityOptions: [],
      poiOptions: [],

      destinations: [],

      form: {
        dest_ids: [],
        area: {
          poi_items: [],
          country_id: '',
          city_id: '',

          country_id_from: '',
          city_id_from: '',

          level: '',
          continent: ''
        }
      }
    }
  },
  watch: {
    schemaConfig: {
      deep: true,
      immediate: true,
      async handler() {
        this.getActivityBindingAreaFn()
        this.setCacheCountryCity()
      }
    },
    form: {
      deep: true,
      handler() {
        this.setCacheCountryCity()
        this.$emit('change', {
          isUnpublishId: this.form.area.country_id === this.cacheObj.unpublishId
        })
      }
    }
  },
  computed: {
    isCreated() {
      return !_.isEmpty(klook.urlParam('lang'))
    },
    calcDisabledCity() {
      return this.isNewFlowFlag2root && this.form.area.country_id !== this.cacheObj.unpublishId
    },
    isNewFlowFlag2root() {
      return this.$root.isNewFlowFlag
    },
    isMC2BD() {
      return this.$store.state.isMC2BD
    },
    location() {
      return _.get(this.result, 'activity_location.location', '')
    },
    calcFlowDisabled() {
      if (!this.isNewFlowFlag2root) return false
      if (this.isMC2BD || !this.location) return true
      let { country_id } = this.form.area
      if (!country_id || country_id === this.cacheObj.unpublishId) return false
      let isb = !this.cacheObj.cache_country_id
      return isb
    },
    calcCountryOptions() {
      if (!this.isNewFlowFlag2root) return this.countryOptions
      return this.countryOptions.filter((o) =>
        [this.cacheObj.unpublishId, this.cacheObj.cache_country_id].includes(o.id)
      )
    },
    templateType() {
      return this.schemaConfig.model || null
    },
    specialBinging() {
      return this.templateType === 3
    },
    required() {
      return !!this.schemaConfig.required
    },
    isRequired() {
      return {
        required: this.required,
        message: 'Please fill this form'
      }
    },
    subCategoryId() {
      return _.get(this.$store.state, 'categoryInfo.sub_category_id', 0)
    },
    calcMultiDepartureCity() {
      return this.schemaConfig?.support_multi_departure_city ?? false
    },
    calcMultiDepartureCountry() {
      return false
    },
    supportMultiDest() {
      return this.schemaConfig?.support_multi_dest ?? false
    },
    supportMultiCity() {
      return this.templateType === 4
    }
  },
  async created() {
    if (this.specialBinging) {
      this.destinations = await ajax.get(ADMIN_API.act.getSpuDestinations)
    } else {
      this.countryOptions = await ajax.get(ADMIN_API.act.get_all_countries, {
        params: {
          // 7, 8, 9, 206, 331 wifi/sim
          include_not_show_country:
            [7, 8, 9, 206, 331].includes(+this.subCategoryId) ||
            shouldIncludeUnpublishedCountryByAIDs.includes(+this.activity_id)
              ? 1
              : 0 // 0 不包含 Unpublished 国家
        }
      })
    }
  },
  methods: {
    setCacheCountryCity() {
      if (!this.isNewFlowFlag2root) return
      let { cacheObj, form } = this
      let countryId = form.area.country_id
      if (countryId && countryId !== cacheObj.unpublishId) {
        cacheObj.cache_country_id = countryId
        if (form.area.city_id) {
          cacheObj.cache_city_id = form.area.city_id
        } else {
          form.area.city_id = cacheObj.cache_city_id
        }
      }
    },
    updateCitys(arr) {
      this.childCityOptions = arr
    },
    getAreaFormData() {
      let { childCityOptions: cityOptions, countryOptions } = this
      return _.cloneDeep({ ...this.form, cityOptions, countryOptions })
    },
    getTextStr(type, content) {
      if (!type) return ''
      let map = {
        queryCityPageErr: this.$t('30439'),
        queryCityPageSuc: content,
        queryNoCity: klook.parseStr1(__('30433'), {
          // 无法推算出城市的提示文案
          click_here: `<a href="javascript:window.clickHandle2native('click_here')" class="common-link">${__(
            '30434'
          )}</a>`
        }),
        initCityNeq: content,
        initNoCity: klook.parseStr1(__('30441'), {
          create_a_new_city: `<a href="javascript:window.clickHandle2native('click_here')" class="common-link">${__(
            '30443'
          )}</a>`,
          modify_the_location_coordinates: `<a href="javascript:window.clickHandle2native('click_modify')" class="common-link">${__(
            '30444'
          )}</a>`
        })
      }
      return map[type]
    },
    getName(locales) {
      return _.get(
        locales.find((v) => v.language === 'en_US'),
        'name',
        'Invalid Name'
      )
    },
    validateForm() {
      return new Promise((resolve) => this.$refs.form.validate((valid) => resolve(valid)))
    },
    changeAreaHandle(v) {
      const isb = Object.keys(v).every((key) => {
        return klook.isLikeEqual(this.form.area?.[key], v[key])
      })
      Object.assign(this.form.area, v)
      if (isb) {
        // 过滤初始化加载时的校验
        return
      }
      this.$refs.form && this.$refs.form.validate()
    },
    async getActivityBindingAreaFn() {
      if (this.templateType === 2) {
        this.form.area.city_id = []
      }
      let result = this.result
      if (!(result && result.destination_model_type)) return

      let { binded_area_info, dest_ids } = result.destination_model_type
      let { area_items, departure_items, poi_items } = binded_area_info
      this.form.dest_ids = dest_ids
      switch (this.templateType) {
        case 1:
        case 2: {
          const supportMultiDest = this.supportMultiDest
          const list = Array.isArray(area_items) ? area_items : []
          const countryId = supportMultiDest
            ? [...new Set(list.map((item) => item.country?.area_id ?? ''))]
            : list[0]?.country.area_id ?? ''
          this.form.area.country_id = countryId
          // this.form.area.country_id = area_items.length
          //   ? (area_items[0].country && area_items[0].country.area_id) || ''
          //   : ''
          this.form.area.poi_items = poi_items.map((item) => item.poi)
          this.poiOptions = poi_items.map((item) => ({
            ...(item.poi || {}),
            id: item.area_id,
            title: item.area_name
          }))

          if (this.templateType === 1) {
            this.form.area.city_id = area_items.length
              ? (area_items[0].city && area_items[0].city.area_id) || ''
              : ''
          } else {
            this.form.area.city_id = area_items.map((item) => item.area_id)
            this.cityOptions = area_items.map((item) => ({
              id: item.area_id,
              name: item.area_name
            }))
            const { calcMultiDepartureCity, calcMultiDepartureCountry } = this
            this.form.area.city_id_from = calcMultiDepartureCity
              ? [...new Set(departure_items?.map((item) => item.area_id ?? ''))]
              : departure_items?.[0]?.area_id ?? ''
            this.form.area.country_id_from = calcMultiDepartureCountry
              ? [...new Set(departure_items?.map((item) => item?.country?.area_id ?? ''))]
              : departure_items?.[0]?.country?.area_id ?? ''
          }
          break
        }
        case 3: {
          let node_type = area_items.map((item) => item.node_type)
          if (node_type.includes(0)) {
            this.form.area.level = 0
            this.form.area.continent = area_items.length ? area_items[0].area_id : ''
          } else {
            if (node_type.includes(2)) {
              this.$set(this.form, 'area', {
                ...this.form.area,
                level: 2,
                country_id: area_items.map((item) => item.area_id)
              })
            } else {
              this.$set(this.form, 'area', {
                ...this.form.area,
                level: 3,
                country_id: area_items.map((item) => item.country.area_id),
                city_id: area_items.map((item) => item.area_id)
              })
              this.cityOptions = area_items.map((item) => ({
                id: item.area_id,
                name: item.area_name
              }))
            }
          }
          break
        }
        case 4: {
          this.form.area.country_id = area_items.length
            ? (area_items[0].country && area_items[0].country.area_id) || ''
            : ''
          this.form.area.city_id = area_items.map((item) => item.area_id)
          break
        }
      }
    },
    getAreaData() {
      let { destination_model_type } = this.result

      if (this.specialBinging) {
        return {
          binded_area_info: [],
          dest_ids: this.form.dest_ids
        }
      }

      let data
      let { city_id, city_id_from, country_id, level, continent, poi_items } = this.form.area

      if (this.templateType === 1) {
        data = [
          {
            area_id_list: [city_id],
            area_type: 1,
            departure_id_list: []
          }
        ]
      } else if (this.templateType === 2) {
        data = [
          {
            area_id_list: _.flatten([city_id]),
            area_type: 1,
            departure_id_list: _.flatten([city_id_from])
          }
        ]
      } else if (this.templateType === 3) {
        let area_id_list = []
        switch (level) {
          case 0: {
            area_id_list = [continent]
            break
          }
          case 2: {
            area_id_list = _.flatten([country_id])
            break
          }
          case 3: {
            area_id_list = _.flatten([city_id])
            break
          }
        }
        data = [
          {
            area_id_list,
            area_type: 1,
            departure_id_list: []
          }
        ]
      } else if (this.templateType === 4) {
        data = [
          {
            area_id_list: _.flatten([city_id]),
            area_type: 1,
            departure_id_list: []
          }
        ]
      }

      return {
        ...destination_model_type,
        binded_area_info: data,
        poi_items
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.linked-destination-page {
  margin: 16px 0 0 0;
  box-sizing: border-box;
  span {
    display: inline-block;
    font-size: 12px;
    line-height: 14px;
  }
}
</style>
