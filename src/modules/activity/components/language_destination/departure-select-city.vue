<template>
  <a-form-model ref="form" class="common-tpl-basic-style" :model="form" :colon="false">
    <a-form-model-item
      prop="value"
      :label="label"
      :rules="[
        {
          required: required,
          message: $t('type_to_search'),
          validator: validator,
          trigger: 'blur'
        }
      ]"
    >
      <a-select
        :value="form.value"
        class="map-content-select"
        show-search
        label-in-value
        :placeholder="placeholder"
        :mode="multiple ? 'multiple' : 'default'"
        :disabled="disabled"
        :filter-option="false"
        option-label-prop="label"
        @search="onSearch"
        @change="onChange"
      >
        <a-select-option
          v-for="option in options"
          :key="option.area_id"
          :value="option.area_id"
          :label="option.label"
          :disabled="getOptionDisabled(option)"
        >
          <component
            :is="getOptionDisabled(option) ? 'a-tooltip' : 'span'"
            class="map-content-select-option"
            placement="topRight"
            :title="$t('174967')"
          >
            <OverflowTextTooltip
              direction="vertical"
              placement="topRight"
              :line-clamp="1"
              :switch-it="!getOptionDisabled(option)"
            >
              {{ option.label }}
            </OverflowTextTooltip>
            <span v-if="option.tag && option.tag.text">
              &nbsp;<a-tag
                :style="{
                  color: option.tag.color || '#08979C',
                  borderColor: option.tag.border_color || '#87E8DE',
                  backgroundColor: option.tag.background_color || '#E6FFFB'
                }"
              >
                {{ option.tag.text }}
              </a-tag>
            </span>
          </component>
        </a-select-option>

        <div slot="dropdownRender" slot-scope="menu">
          <v-nodes :vnodes="menu" />
          <a-divider style="margin: 2px 0" />
          <div style="padding: 9px 12px">
            <div class="apply-feedback" @click="handleApply">
              {{ $t('120636') }}
            </div>
          </div>
        </div>
      </a-select>
    </a-form-model-item>

    <!-- <ApplyAddCityModal v-bind="$attrs" :visible.sync="visible" /> -->
  </a-form-model>
</template>

<script>
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'
import ApplyAddCityModal from './tpl_apply_add_city'

export default {
  name: 'DepartureSelectCity',
  components: {
    OverflowTextTooltip,
    ApplyAddCityModal,
    VNodes: {
      functional: true,
      render: (_, ctx) => ctx.props.vnodes
    }
  },
  model: {
    prop: 'areaData',
    event: 'change'
  },
  props: {
    areaData: {
      type: Array,
      default: () => []
    },
    label: {
      type: String,
      default: __('act_binding_destination_departure_city')
    },
    schemaConfig: {
      type: Object,
      default: () => ({})
    },
    required: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: __('174966')
    },
    fieldKey: {
      type: String,
      default: 'departure_id_list'
    },
    closeNationalRestrictions: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currCountryId: undefined,
      form: {
        value: []
      },
      options: [],
      visible: false
    }
  },
  computed: {
    areaIds() {
      return this.areaData.map((item) => item.area_id)
    },
    multiple() {
      return this.schemaConfig?.support_multi_departure_city ?? false
    }
  },
  created() {
    this.initData()
  },
  methods: {
    initData() {
      const areaData = this.areaData || []
      const getLabel = (data) => {
        return `${data.area_id} - ${(data.path_list || []).join(data.path_sep || ' > ')}`
      }
      const options = areaData.map((item) => ({
        key: item.area_id,
        area_id: item.area_id,
        dest_country_id: item.country?.area_id,
        label: getLabel(item)
      }))
      this.$set(this, 'options', options)
      if (this.multiple) {
        this.form.value = _.cloneDeep(this.options)
      } else {
        const data = this.areaData?.[0]
        if (data) {
          this.form.value = {
            ...data,
            key: data.area_id,
            label: getLabel(data)
          }
        }
      }
      this.currCountryId = this.options?.[0]?.dest_country_id
      this.$emit('change', [
        {
          area_type: 1,
          [this.fieldKey]: areaData.map((item) => item.area_id)
        }
      ])
    },
    onChange(data) {
      this.form.value = data
      if (!this.currCountryId) {
        const list = Array.isArray(data) ? data : [data]
        const curr = this.options.find((option) => list.some((item) => item.key === option.key))
        this.currCountryId = curr?.dest_country_id
      } else if (data === undefined || (Array.isArray(data) && !data.length)) {
        this.currCountryId = undefined
      }

      const arr = Array.isArray(data) ? data : data ? [data] : []
      this.$emit('change', [
        {
          area_type: 1,
          [this.fieldKey]: arr.map((item) => item.key)
        }
      ])
      this.$emit('changeData')
    },
    onSearch: _.debounce(async function (input) {
      this.currQueryInput = input
      const response = await ajax.get(ADMIN_API.act.search_city, {
        params: { input }
      })

      if (this.currQueryInput !== input) {
        return
      }

      const options = (response.data || []).map((item) => ({
        key: item.dest_city_id,
        area_id: item.dest_city_id,
        dest_country_id: item.dest_country_id,
        label: `${item.dest_city_id} - ${(item.path_list || []).join(item.path_sep)}`,
        tag: item.tag
      }))

      this.$set(this, 'options', options)
    }, 100),
    validateForm() {
      return new Promise((resolve) => this.$refs.form.validate(resolve))
    },
    validator(rule, value, callback) {
      if (!this.required) {
        return callback()
      }

      if (value === undefined || value === '' || (Array.isArray(value) && !value.length)) {
        return callback(new Error(this.$t('global_please_select')))
      }

      return callback()
    },
    handleApply() {
      const link = '/mspa/configtool/destination/feedbackcreate'
      window.open(link, '_blank')
    },
    getOptionDisabled(option) {
      if (this.closeNationalRestrictions || !this.multiple) {
        return false
      }

      return this.currCountryId ? this.currCountryId !== option.dest_country_id : false
    }
  }
}
</script>

<style lang="scss" scoped>
.map-content-select-option {
  display: flex;
  gap: 8px;
}
.apply-feedback {
  padding: 0 7px;
  display: inline-block;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #437dff;
  border-radius: 2px;
  border: 1px solid #2073f9;
  cursor: pointer;
}
</style>
