<template>
  <a-form-model ref="form" :model="form" class="common-tpl-basic-style" :colon="false">
    <!-- languages -->
    <a-form-model-item
      :label="$attrs.label || $t('global_language')"
      prop="language"
      :rules="{
        required: required,
        message: 'Please fill this language'
      }"
    >
      <DescMarkdownContent
        class="strong-tips"
        placement="right"
        :is-inline="false"
        :desc="schemaConfig && schemaConfig.strongTips"
        :line-clamp="3"
      />

      <ShimAntdTooltip
        v-bind="$attrs"
        :title="hideTooltip ? undefined : $attrs.description || $t('language_selection_onboard')"
      >
        <a-select
          v-model="form.language"
          filterable
          :disabled="isCreated || $attrs.disabled"
          :placeholder="$t('global_please_select')"
        >
          <a-select-option v-for="(name, value) in langObj" :key="value" :value="value">{{
            name
          }}</a-select-option>
        </a-select>
      </ShimAntdTooltip>
    </a-form-model-item>
    <slot />
  </a-form-model>
</template>

<script>
import ShimAntdTooltip from '@activity/components/shimAntdTooltip'
import DescMarkdownContent from '@activity/pages/activityManagement/detailV2/components/descMarkdownContent'

export default {
  name: 'activity_basic_info_language_destination',
  inject: ['refreshPage'],
  components: {
    DescMarkdownContent,
    ShimAntdTooltip
  },
  props: {
    hideTooltip: {
      type: Boolean,
      default: false
    },
    schemaConfig: {
      type: Object,
      default: () => {}
    },
    result: {
      type: Object,
      default: () => {}
    },
    required: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      form: {
        sales_channels: 0,
        language: undefined
      }
    }
  },
  computed: {
    isCreated() {
      return !!this.$route.query.lang
    },
    langObj() {
      return this.isCreated
        ? lang_conf.getLangObj('B_LANG', 'LANG_TITLE')
        : lang_conf.getLangObjNoEn('B_LANG', 'LANG_TITLE')
    },
    activity_id() {
      return parseInt(this.$route.params.id)
    }
  },
  watch: {
    'form.language': {
      immediate: true,
      deep: true,
      handler(v) {
        this.$emit('published_language', v)
      }
    },
    schemaConfig: {
      deep: true,
      immediate: true,
      async handler() {
        if (klook.urlParam('lang') && klook.getEditLang()) {
          this.form.language = klook.getEditLang()
        } else {
          if (this.$root.isMerchant) {
            let lang = klook.getAPILang(klook.getLocalStorage('language'))
            if (!this.form.language) {
              this.form.language = lang === 'zh_TW' ? undefined : lang
            }
          } else {
            this.form.language = this.result.language || undefined
          }
        }
      }
    }
  }
}
</script>
