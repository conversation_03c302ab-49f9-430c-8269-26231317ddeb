<template>
  <a-modal
    :visible.sync="_visible"
    :title="$t('act_binding_destination_city_request')"
    @cancel="
      () => {
        _visible = false
      }
    "
  >
    <a-form-model ref="form" :model="form" label-width="120px">
      <a-form-model-item
        :label="$t('act_binding_destination_country_or_region')"
        prop="country"
        :rules="{ required: true, message: 'Please select Country Or Region' }"
      >
        <a-select v-model="form.country" show-search allow-clear :placeholder="$t('type_to_search')">
          <a-select-option
            v-for="{ id, name } in countryOptions"
            :key="id"
            :label="`${id} - ${name}`"
            :value="name"
            >{{ `${id} - ${name}` }}</a-select-option
          >
        </a-select>
      </a-form-model-item>
      <a-form-model-item
        :label="$t('act_binding_destination_city_name')"
        prop="city"
        :rules="{ required: true, message: 'Please select City' }"
      >
        <a-input :placeholder="$t('global_please_input')" v-model="form.city" />
      </a-form-model-item>
    </a-form-model>
    <span slot="footer" style="text-align: center">
      <a-button @click="_visible = false">{{ $t('global_cancel') }}</a-button>
      <a-button type="primary" @click="submitHandle">{{ $t('global_submit') }}</a-button>
    </span>
  </a-modal>
</template>

<script>
export default {
  name: 'ApplyAddCityModal',
  props: {
    visible: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      form: {
        country: undefined,
        city: undefined
      }
    }
  },
  watch: {
    _visible() {
      this.form.country = this.form.city = undefined
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        if (val) {
          this.form = this.$options.data().form
        }
        this.$emit('update:visible', val)
      }
    },
    countryOptions() {
      return this.$attrs['all-country-options'] || this.$attrs['country-options'] || []
    }
  },
  methods: {
    submitHandle() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          let activity_id = klook.parse2id(this.$route.params.id)
          ajax
            .post(ADMIN_API.act.send_add_city_application_email, {
              data: {
                activity_id,
                area_name: this.form.country,
                city_name: this.form.city
              }
            })
            .then(() => {
              this.$message.open({
                duration: 5,
                content: (h) => (
                  <div class="ant-message-success ta-left">
                    <span>
                      <a-icon theme="filled" type="check-circle" />
                      {__('30436')}
                      <br />
                      {__('30437')}
                    </span>
                  </div>
                )
              })
            })
          this._visible = false
        } else {
          this.$message.warn('Please fill in all field.')
        }
      })
    }
  }
}
</script>

<style scoped lang="scss"></style>
