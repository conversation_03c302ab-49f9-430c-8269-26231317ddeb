<template>
  <div>
    <tpl_selection_country_city
      v-bind="$attrs"
      :country-label="customCountryLabel || $t('act_binding_destination_destination_country_or_region')"
      :city-label="customCityLabel || $t('act_binding_destination_city')"
      v-on="$listeners"
    >
      <template v-if="!isNewFlowFlag2root" #city_note>
        <label>
          {{ $t('act_binding_destination_not_find') }}
          <a href="javascript:void(0)" @click="applyHandle">{{ $t('act_binding_destination_click_me') }}</a>
          {{ $t('act_binding_destination_to_request') }}
        </label>
      </template>
      <template #tips>
        <slot name="tips"></slot>
      </template>
      <template #more>
        <tpl_selection_poi v-model="form[poiField]" v-bind="$attrs" :poi-field="poiField" v-on="$listeners" />
      </template>
    </tpl_selection_country_city>
    <tpl_apply_add_city v-bind="$attrs" :visible.sync="visible" />
  </div>
</template>

<script>
import tpl_selection_country_city from './tpl_selection_country_city'
import tpl_selection_poi from './tpl_selection_poi'
import tpl_apply_add_city from './tpl_apply_add_city'

export default {
  name: 'RegionBinding1',
  inheritAttrs: false,
  components: {
    tpl_selection_country_city,
    tpl_selection_poi,
    tpl_apply_add_city
  },
  props: {
    customCountryLabel: {
      type: String,
      default: ''
    },
    customCityLabel: {
      type: String,
      default: ''
    },
    poiField: {
      type: String,
      default: 'poi_items'
    }
  },
  data() {
    return {
      visible: false,
      form: {}
    }
  },
  watch: {
    $attrs: {
      handler() {
        this.form[this.poiField] = this.$attrs[this.poiField] || []
      },
      deep: true
    }
  },
  computed: {
    isNewFlowFlag2root() {
      return this.$root.isNewFlowFlag
    }
  },
  methods: {
    applyHandle() {
      this.visible = true
    }
  }
}
</script>
