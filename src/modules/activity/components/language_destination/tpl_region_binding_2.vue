<template>
  <div>
    <tpl_selection_country_city
      v-bind="$attrs"
      :country-label="$t('act_binding_destination_departure_country_or_region')"
      :city-label="$t('act_binding_destination_departure_city')"
      country-field="country_id_from"
      city-field="city_id_from"
      :multiple-city="multipleDepartureCity"
      :multiple-country="multipleDepartureCountry"
      v-on="$listeners"
    >
      <template #city_note>
        <label>
          {{ $t('act_binding_destination_not_find') }}
          <a href="javascript:void(0)" @click="applyHandle">{{ $t('act_binding_destination_click_me') }}</a>
          {{ $t('act_binding_destination_to_request') }}
        </label>
      </template>
    </tpl_selection_country_city>
    <tpl_selection_country_city
      v-bind="$attrs"
      :multiple-city="true"
      :multiple-country="multipleCountry"
      v-on="$listeners"
    />
    <tpl_selection_poi v-model="form[idsField]" v-bind="$attrs" :poi-field="idsField" v-on="$listeners" />
    <tpl_apply_add_city v-bind="$attrs" :visible.sync="visible" />
  </div>
</template>

<script>
import tpl_selection_country_city from './tpl_selection_country_city'
import tpl_selection_poi from './tpl_selection_poi'
import tpl_apply_add_city from './tpl_apply_add_city'

export default {
  inheritAttrs: false,
  components: {
    tpl_selection_country_city,
    tpl_selection_poi,
    tpl_apply_add_city
  },
  props: {
    idsField: {
      type: String,
      default: 'poi_items'
    },
    multipleDepartureCity: {
      type: Boolean,
      default: false
    },
    multipleDepartureCountry: {
      type: Boolean,
      default: false
    },
    multipleCountry: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {},
      visible: false
    }
  },
  watch: {
    $attrs: {
      handler() {
        this.$set(this, 'form', {
          [this.idsField]: this.$attrs[this.idsField] || []
        })
      },
      deep: true
    }
  },
  methods: {
    applyHandle() {
      this.visible = true
    }
  }
}
</script>

<style scoped lang="scss">
.region-binding-2-header {
  &:after {
    content: '';
    display: inline-block;
    border-bottom: 3px solid #e0e0e0;
    padding-bottom: 10px;
    position: relative;
    left: 120px;
    width: calc(100% - 180px);
  }
}
</style>
