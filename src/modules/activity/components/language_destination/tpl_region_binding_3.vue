<template>
  <div>
    <a-form-model-item
      prop="area.level"
      :label="$t('act_binding_destination_geographic_level')"
      :rules="[{ required: true, message: 'Please select' }]"
    >
      <a-select
        v-model="form.level"
        remote
        allow-clear
        :placeholder="$t('type_to_search')"
      >
        <a-select-option
          v-for="{ value, label } in levelOptions"
          :key="value"
          :label="label"
          :value="value"
          >{{ label }}</a-select-option
        >
      </a-select>
    </a-form-model-item>

    <template v-if="form.level === 0">
      <a-form-model-item
        prop="area.continent"
        :label="$t('act_binding_destination_scope')"
        :rules="[{ required: true, message: 'Please select' }]"
      >
        <a-select
          v-model="form.continent"
          show-search
          allow-clear
          :placeholder="$t('type_to_search')"
        >
          <a-select-option
            v-for="item in continentOptions"
            :key="item.id"
            :value="item.id"
            :label="`${item.id} - ${item.i18n.name}`"
            >{{ `${item.id} - ${item.i18n.name}` }}</a-select-option
          >
        </a-select>
      </a-form-model-item>
    </template>
    <template v-if="form.level === 2">
      <tpl_selection_country_city
        v-bind="$attrs"
        :show-city="false"
        :multiple-country="true"
        :multiple-city="true"
        v-on="$listeners"
      />
    </template>
    <template v-if="form.level === 3">
      <tpl_selection_country_city
        :key="form.level"
        v-bind="$attrs"
        :multiple-country="true"
        :multiple-city="true"
        v-on="$listeners"
      />
    </template>
  </div>
</template>

<script>
import tpl_selection_country_city from './tpl_selection_country_city'

export default {
  components: {
    tpl_selection_country_city
  },
  model: {
    prop: 'area',
    event: 'change'
  },
  props: {
    area: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      continentOptions: []
    }
  },
  computed: {
    levelOptions() {
      return [
        {
          value: 0,
          label: __('act_binding_destination_continent')
        },
        {
          value: 2,
          label: __('act_binding_destination_country')
        },
        {
          value: 3,
          label: __('act_binding_destination_city1')
        }
      ]
    },
    form: {
      get() {
        return this.area
      },
      set(v) {
        this.$emit('change:area', v)
      }
    }
  },
  async mounted() {
    this.continentOptions = (
      await ADMIN_API.getChildNodes({
        strative_area_id: 0
      })
    ).result
  }
}
</script>

<style scoped lang="scss">
.region-binding-2-header {
  &:after {
    content: '';
    display: inline-block;
    border-bottom: 3px solid #e0e0e0;
    padding-bottom: 10px;
    position: relative;
    left: 120px;
    width: calc(100% - 180px);
  }
}
</style>
