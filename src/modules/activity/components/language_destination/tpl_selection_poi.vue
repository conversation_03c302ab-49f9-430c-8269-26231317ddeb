<template>
  <div>
    <a-form-model-item :colon="false" :label="labelText" prop="ids">
      <a-tooltip placement="right" overlayClassName="common-tooltip-style" :title="$attrs.description">
        <a-select
          v-model="ids"
          remote
          show-search
          allow-clear
          mode="multiple"
          :disabled="!city_id_list.length || disabled"
          :placeholder="$t('type_to_search')"
          :filter-option="false"
          @search="remoteMethod"
        >
          <template v-if="notGroup">
            <a-select-option
              v-for="item in options"
              :key="item.id"
              :value="item.id"
              :label="item.name"
              class="act-poi-option"
              :disabled="item.special"
              :class="{ option__special: item.special }"
            >
              <OverflowTextTooltip class="overflow-text gap">
                <span class="poi_option_item__title">{{ item.poi_id }} - {{ item.title }}</span>
              </OverflowTextTooltip>
              <OverflowTextTooltip v-if="item.city_name || item.country_name" class="overflow-text">
                <span class="poi_option_item__description">{{ item.city_name }},{{ item.country_name }}</span>
              </OverflowTextTooltip>
            </a-select-option>
          </template>
          <template v-else>
            <a-select-opt-group v-for="(value, key) of options" :key="key" :label="textId[key]">
              <a-select-option
                v-for="item in value"
                :key="item.id"
                :value="item.id"
                :label="item.name"
                class="act-poi-option"
                :disabled="item.special"
                :class="{ option__special: item.special }"
              >
                <OverflowTextTooltip class="overflow-text gap">
                  <span class="poi_option_item__title">{{ item.poi_id }} - {{ item.title }}</span>
                </OverflowTextTooltip>
                <OverflowTextTooltip v-if="item.city_name || item.country_name" class="overflow-text">
                  <span class="poi_option_item__description">
                    {{ item.city_name }},{{ item.country_name }}
                  </span>
                </OverflowTextTooltip>
              </a-select-option>
            </a-select-opt-group>
          </template>
        </a-select>
      </a-tooltip>
    </a-form-model-item>
    <tpl_poi_edit_dialog
      :visible.sync="visible"
      :language="language"
      :show-higher-level="false"
      @submit="editSubmit"
    />
  </div>
</template>

<script>
import tpl_poi_edit_dialog from '@activity/components/taxonomy/tpl_poi_edit_dialog'
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'

export default {
  components: {
    tpl_poi_edit_dialog,
    OverflowTextTooltip
  },
  inheritAttrs: false,
  model: {
    prop: 'poiIds',
    event: 'change'
  },
  props: {
    poiIds: {
      type: Array,
      default: () => []
    },
    labelText: {
      type: String,
      default: __('act_binding_destination_poi')
    },
    poiOptions: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    activityId: {
      type: [Number, String],
      default: 0
    }
  },
  data() {
    return {
      options: [],
      language: 'en_US',
      visible: false,
      city_id_list: [],
      notGroup: true,
      allOptions: []
    }
  },
  computed: {
    ids: {
      get() {
        return this.poiIds.map((item) => item.poi_id)
      },
      set(v) {
        const options = this.allOptions
        const value = options.reduce((acc, curr) => {
          const { poi_id, place_id, location } = curr
          if (v.includes(poi_id) && !acc.find((item) => item.poi_id === poi_id)) {
            acc.push({ poi_id, place_id, location })
          }
          return acc
        }, [])

        this.$emit('change', {
          [this.$attrs['poi-field']]: value
        })
      }
    },
    countryId() {
      const country_id = this.$attrs?.country_id ?? []
      const list = Array.isArray(country_id) ? country_id : [country_id]
      return list.join(',')
    },
    textId() {
      return {
        most_recommended: this.$t('102291'),
        others: this.$t('102293')
      }
    }
  },
  watch: {
    ['$attrs.city_id']: {
      deep: true,
      immediate: true,
      handler(v1, v2) {
        if (v1 !== v2) {
          // $set will Out of watcher
          let city_id = this.$attrs.city_id
          if (!Array.isArray(city_id)) {
            this.city_id_list = (city_id && [city_id]) || []
          } else {
            this.city_id_list = city_id
          }
          if (v2 && !this.city_id_list.length) {
            //排除初始化被清空
            this.ids = []
            this.$set(this, 'options', [])
          }
        }
      }
    },
    poiOptions: {
      immediate: true,
      deep: true,
      handler() {
        this.options = this.poiOptions || []
        this.allOptions = [...this.poiOptions]
      }
    }
  },
  methods: {
    async editSubmit(data) {
      let res = await ADMIN_API.updatePoiNode({
        ...data,
        send_email: true
      })
      if (res.success) {
        this.visible = false
        this.$message.success(this.$t('global_success'))
      } else {
        if (res.error.code === '51510' && res.error.message) {
          this.$error({
            content: res.error.message,
            title: __('global_confirm')
          })
        } else {
          this.$confirm({
            content: 'Backend error'
          })
        }
      }
    },
    async addNewPoiHandle(disabled) {
      if (disabled) return
      await new Promise((resolve) => {
        this.$warning({
          title: 'Tips',
          content: this.$t('taxonomy_confirm_key_location'),
          onText: this.$t('global_submit'),
          cancelText: this.$t('global_cancel'),
          onOk: () => resolve()
        })
      })

      this.visible = true
    },
    remoteMethod: _.debounce(async function (query) {
      const str = query.trim()
      if (str) {
        const result = await ajax.getBody(ADMIN_API.act.get_activity_poi_list, {
          params: {
            query: str,
            language: klook.getAPILang(),
            area_id_list: this.countryId,
            activity_id: this.activityId
          }
        })
        const list = (result?.result?.results ?? []).reduce((acc, item) => {
          const { group, poi_id } = item
          const data = { ...item, id: poi_id }
          if (acc[group]) {
            acc[group].push(data)
          } else {
            acc[group] = [data]
          }
          return acc
        }, {})
        const values = Object.values(list)
        this.allOptions = [...this.allOptions, ...values.flat()]
        if (values.length > 1) {
          this.notGroup = false
          this.$set(this, 'options', list)
        } else {
          this.notGroup = true
          this.$set(this, 'options', values?.[0] ?? [])
        }
      }
    }, 300),
    remoteMethodOld: _.debounce(async function (query) {
      if (query.trim().length > 0) {
        let options = (
          await ajax.postBody(ADMIN_API.act.filter_poiwith_activity_total, {
            data: {
              page: 1,
              limit: 50,
              name: query,
              publish_status: 1,
              city_id_list: this.city_id_list,
              activity_publish_status: 1
            }
          })
        ).result.items.map((item) => ({
          ...item,
          id: item.poi_id
        }))

        this.$set(this, 'options', options)
      }
    }, 200)
  }
}
</script>

<style scoped lang="scss">
@import '../../pages/package/itinerary/mixins.scss';
@include mixin-poi-option-item;

.gap {
  margin-right: 8px;
}

.act-poi-option {
  height: fit-content;
}
</style>
