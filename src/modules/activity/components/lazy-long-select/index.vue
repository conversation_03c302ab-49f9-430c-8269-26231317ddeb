<!--
  <lazy-long-select
    v-model="item.from_station_code"
    :full-opts="stationOpts"
    :opt-label-key="'city_name'"
    :opt-value-key="'city_id'"
  />
  full-opts 是传入的长数据，但是表现出来下拉数据只有 100 条，
  通过搜索去查找 100 条以外的数据
-->
<template>
  <div class="lazy-long-select--container">
    <a-select
      v-model="selectValue"
      v-bind="$attrs"
      show-search
      allow-clear
      :remote="remote"
      :disabled="disabled"
      @search="remoteMethod"
      :filter-option="!remote"
      :loading="loading"
      :placeholder="selectHint"
      :mode="multiple ? 'multiple' : 'default'"
      option-filter-prop="children"
      @change="emitChange"
    >
      <slot name="headerOptions" />
      <a-select-option
        v-for="(item, index) in headOpts"
        :key="`${item[optValueKey]}-${index}`"
        :label="typeof fmtLabelFn === 'function' ? fmtLabelFn(item) : item[optLabelKey]"
        :value="item[optValueKey]"
        >{{ typeof fmtLabelFn === 'function' ? fmtLabelFn(item) : item[optLabelKey] }}</a-select-option
      >
    </a-select>
    <span v-if="isNewFlowFlag2root" class="right-wrap">
      <!-- 国家和城市字段旁边的Hover文案 -->
      <a-tooltip :title="$t('30432')" overlayClassName="common-tooltip-style">
        <a-icon type="info-circle" />
      </a-tooltip>
    </span>
  </div>
</template>

<script>
import Fuse from 'fuse.js'

export default {
  name: 'LazyLongSelect',
  install(Vue) {
    Vue.component(this.name, this)
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [String, Number, Array],
      default: ''
    },
    // 下拉框列表
    fullOpts: {
      type: Array,
      default: () => []
    },
    // 每一项的 opt 要指定的 value key
    optValueKey: {
      type: String,
      default: 'code'
    },
    // 每一项的 opt 要指定的 label key
    optLabelKey: {
      type: String,
      default: 'name'
    },

    selectHint: {
      type: String,
      default: __('global_select')
    },
    // 一次只显示 100 条数据
    opsLength: {
      type: Number,
      default: 100
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    fmtLabelFn: {
      type: Function,
      default: null
    },
    remote: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      loading: false,
      headOpts: []
      // 一次只显示 100 条数据
      // length: 100
    }
  },
  computed: {
    isNewFlowFlag2root() {
      return this.$root.isNewFlowFlag
    },
    selectValue: {
      get() {
        if (this.value) {
          return this.value
        }
        return this.multiple ? [] : undefined
      },
      set(v) {
        this.$emit('change', v)
      }
    }
  },
  watch: {
    fullOpts: {
      immediate: true,
      deep: true,
      handler() {
        this.fuse = new Fuse(this.fullOpts, {
          tokenize: true,
          matchAllTokens: true,
          keys: [this.optValueKey, this.optLabelKey]
        })
        this.initHeadOpts()
      }
    },
    value(v, oldV) {
      // 处理先赋 fullOpts 再赋 value 时，options为空的情况
      if ((oldV === '' && v) || !this.headOpts.length) {
        this.initHeadOpts()
      }
    }
  },
  methods: {
    initHeadOpts() {
      if (!this.remote) {
        this.headOpts = this.fullOpts
        return
      }
      this.headOpts = this.fullOpts.slice(0, this.opsLength)
      const check = this.checkList()
      if (this.value && !check) {
        // 处理赋值无法匹配选项问题
        let selectObj = this.findList()
        this.headOpts = [...selectObj, ...this.headOpts]
      }
    },
    checkList() {
      const { value, headOpts } = this
      const val = Array.isArray(value) ? value : [value]
      return val.every((item) => headOpts.find((it) => it.id === item))
    },
    findList() {
      const { multiple, value, headOpts, fullOpts } = this
      const val = multiple ? value : [value]
      const list = fullOpts.filter((o) => val.includes(o.id))
      return list.filter((item) => !headOpts.find((it) => it.id === item.id))
    },
    emitChange(value) {
      this.$emit('change', value || '')
      this.remoteMethod(value)
    },
    remoteMethod: _.debounce(function(query) {
      query = String(query)
      if (this.multiple && Array.isArray(this.value) && this.value.length) {
        // 在多选模式下，选值会引起搜索，导致 options 变化，影响体验，但输入框搜索不会带上已选值
        query = query.replace(new RegExp('^' + this.value.join(',')), '') // 选值引起的搜索排除
        if (!query && this.headOpts.length) return
      }
      let result = []
      if (query.trim().length > 0) {
        result = this.fuse.search(query).slice(0, this.opsLength)
      }

      if (result.length <= 1) {
        result = _.uniqBy(
          [...result, ...this.fullOpts.slice(0, this.opsLength - result.length)],
          this.optValueKey
        )
      }
      this.headOpts = result
    }, 300),
    findCityName() {
      let data = this.fullOpts.find((item) => {
        return item.city_id === this.value
      })
      this.remoteMethod(data.city_name)
      // return data.city_name
    }
  }
}
</script>
<style lang="scss" scoped>
.lazy-long-select--container {
  position: relative;
  ::v-deep .ant-select {
    width: 100%;
  }
  .right-wrap {
    position: absolute;
    top: 0;
    right: -26px;
    color: rgba(0, 0, 0, 0.45);
  }
}
</style>
