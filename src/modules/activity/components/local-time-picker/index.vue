<!-- <local-time-picker v-model="form.start_time"></local-time-picker> -->
<template>
  <div class="local-time-picker">
    <a-select
      v-show="show_zone"
      v-model="timezone"
      name="DropDownTimezone"
      placeholder="Select Timezone"
      style="flex: 1; margin-right: 8px"
      :options="selectOptions"
      :disabled="disabled"
    >
    </a-select>
    <a-date-picker
      v-bind="{
        ...$attrs,
        ...$props
      }"
      v-model="timezone_time"
      style="flex: 1"
      :picker-options="pickerOptions"
      showTime
    >
    </a-date-picker>
  </div>
</template>

<script>
var moment = require('moment')
window.moments = moment
const selectOptions = [
  { value: -12, label: '(GMT -12:00) Eniwetok, Kwajalein' },
  { value: -11, label: '(GMT -11:00) Midway Island, Samoa' },
  { value: -10, label: '(GMT -10:00) Hawaii' },
  { value: -9, label: '(GMT -9:00) Alaska' },
  { value: -8, label: '(GMT -8:00) Pacific Time (US &amp; Canada)' },
  { value: -7, label: '(GMT -7:00) Mountain Time (US &amp; Canada)' },
  {
    value: -6,
    label: '(GMT -6:00) Central Time (US &amp; Canada), Mexico City'
  },
  {
    value: -5,
    label: '(GMT -5:00) Eastern Time (US &amp; Canada), Bogota, Lima'
  },
  { value: -4, label: '(GMT -4:00) Atlantic Time (Canada), Caracas, La Paz' },
  { value: -3.5, label: '(GMT -3:30) Newfoundland' },
  { value: -3, label: '(GMT -3:00) Brazil, Buenos Aires, Georgetown' },
  { value: -2, label: '(GMT -2:00) Mid-Atlantic' },
  { value: -1, label: '(GMT -1:00 hour) Azores, Cape Verde Islands' },
  { value: 0, label: '(GMT) Western Europe Time, London, Lisbon, Casablanca' },
  { value: 1, label: '(GMT +1:00 hour) Brussels, Copenhagen, Madrid, Paris' },
  { value: 2, label: '(GMT +2:00) Kaliningrad, South Africa' },
  { value: 3, label: '(GMT +3:00) Baghdad, Riyadh, Moscow, St. Petersburg' },
  { value: 3.5, label: '(GMT +3:30) Tehran' },
  { value: 4, label: '(GMT +4:00) Abu Dhabi, Muscat, Baku, Tbilisi' },
  { value: 4.5, label: '(GMT +4:30) Kabul' },
  { value: 5, label: '(GMT +5:00) Ekaterinburg, Islamabad, Karachi, Tashkent' },
  { value: 5.5, label: '(GMT +5:30) Bombay, Calcutta, Madras, New Delhi' },
  { value: 5.75, label: '(GMT +5:45) Kathmandu' },
  { value: 6, label: '(GMT +6:00) Almaty, Dhaka, Colombo' },
  { value: 7, label: '(GMT +7:00) Bangkok, Hanoi, Jakarta' },
  { value: 8, label: '(GMT +8:00) Beijing, Perth, Singapore, Hong Kong' },
  { value: 9, label: '(GMT +9:00) Tokyo, Seoul, Osaka, Sapporo, Yakutsk' },
  { value: 9.5, label: '(GMT +9:30) Adelaide, Darwin' },
  { value: 10, label: '(GMT +10:00) Eastern Australia, Guam, Vladivostok' },
  { value: 11, label: '(GMT +11:00) Magadan, Solomon Islands, New Caledonia' },
  { value: 12, label: '(GMT +12:00) Auckland, Wellington, Fiji, Kamchatka' }
]
export default {
  name: 'local-time-picker',
  install(Vue) {
    // https://vuejs.org/v2/guide/plugins.html
    Vue.component(this.name, this)
  },
  model: {
    // https://vuejs.org/v2/guide/components.html#Form-Input-Components-using-Custom-Events
    prop: 'value', // this is actually bind to `this.time`
    event: 'change'
  },
  watch: {
    value: {
      immediate: true,
      handler(v) {
        this.time = v

        if (v) {
          this.timezone = window.moments.parseZone(this.time).utcOffset() / 60 || this.timezone
        }
      }
    }
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    show_zone: {
      type: Boolean,
      default: true
    },
    value: String,
    type: {
      type: String,
      default: 'datetime'
    },
    placeholder: {
      type: String,
      default: __('cms_datetime_picker_ph')
    },
    'disabled-date': Function,
    pickerOptions: Object,
    // offset: false means to use local time, set as `:offset="0"` for +8 timezone picker
    offset: {
      // initial timezone
      type: Number,
      default: -new Date().getTimezoneOffset() / 60
    },
    local_fmt: {
      // if this is true, then the displayed time will be the time data regardless of timezone info or  `offset`
      type: Boolean,
      default: false
    },
    // 为 true 时，时间以 RFC3339 格式存储，用于 cms
    rfc_fmt: {
      type: Boolean,
      default: false
    },
    // 时区改变不处理时间, 取消时区与时间的关联
    unlinkZone: {
      type: Boolean,
      default: false
    },
    format: String
  },
  data() {
    return {
      selectOptions,
      timezone: this.offset,
      time: ''
    }
  },
  computed: {
    // FIXME ? 用户改变时区后没有选择时间直接保存会无效,这是因为改变 timezone 只会改变 timezone_time，value 没变
    timezone_time: {
      get() {
        if (this.time) {
          var local_chosen_time = new Date(this.time)
          var local_offset = this.timezone * -60
          var offset_diff = new Date().getTimezoneOffset() - local_offset
          var local_destination_time = local_chosen_time.valueOf() + offset_diff * 60000
          var utc_time = new Date(local_destination_time).toJSON() || ''
          var fmt_time = Date.parse(utc_time) ? moment(utc_time).format('YYYY-MM-DD HH:mm:ss') : '' // local time
          // 时区与时间不关联
          if (this.unlinkZone) {
            return this.time
          } else {
            return this.local_fmt ? fmt_time : utc_time
          }
        } else {
          return ''
        }
      },
      set(value) {
        var local_chosen_time = new Date(value)
        var local_offset = this.timezone * -60
        var offset_diff = new Date().getTimezoneOffset() - local_offset
        var local_destination_time = local_chosen_time.valueOf() - offset_diff * 60000
        var utc_time = new Date(local_destination_time).toJSON() || ''
        var fmt_time = Date.parse(utc_time) ? moment(utc_time).format('YYYY-MM-DD HH:mm:ss') : '' // local time

        var rfcTime = moment
          .parseZone(value)
          .utcOffset(this.timezone)
          .format()
        var time = this.rfc_fmt ? rfcTime : utc_time
        // 时区与时间不关联
        if (this.unlinkZone) {
          this.$emit('change', moment(value).format('YYYY-MM-DD HH:mm:ss'))
        } else {
          this.$emit('change', this.local_fmt ? fmt_time : time)
        }

        this.time = value
      }
    }
  },
  mounted() {
    if (this.rfc_fmt) {
      // 使用 RFC3339 格式时，初始化 time 和 timezone
      this.time = this.value
      this.timezone = window.moments.parseZone(this.time).utcOffset() / 60 || this.timezone
    }
  }
}
</script>

<style lang="scss">
// @import "../../scss/commom/element_ui_custom";
</style>
