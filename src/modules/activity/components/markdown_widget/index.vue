<template>
  <a-form-model ref="form" class="markdown-widget" :model="form" label-width="0">
    <DisabledWidgetLayer :data="value" />
    <a-form-model-item prop="value" :rules="rules">
      <ObtMarkdown
        ref="markdown"
        v-model="form.value"
        :config="calcConfig"
        :disabled="readonly"
        :max-length="maxLength"
        :show-suffix-count="true"
        @blur="blurInput"
        @change="onChange"
      />
      <template v-if="refData">
        <ObtMarkdown v-model="refData.value" :config="calcConfig" style="margin-top: 12px" disabled />
      </template>
    </a-form-model-item>
  </a-form-model>
</template>

<script>
import { mapState } from 'vuex'
import DisabledWidgetLayer from '@activity/components/DisabledWidgetLayer.vue'
import ObtMarkdown from '@activity/components/obt-markdown/index'
import { getWordCountLimit, getWordCountLimitDisabled } from '@activity/utils'

export default {
  name: 'MarkdownWidget',
  components: {
    DisabledWidgetLayer,
    ObtMarkdown
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [Object, undefined],
      required: true,
      default: () => ({})
    },
    required: {
      type: Boolean,
      default: false
    },
    mdConfig: {
      type: Object,
      default: () => ({})
    },
    config: {
      type: Object,
      default: () => ({})
    },
    refData: {
      type: Object,
      default: null
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState({
      clickTarget: (state) => state.clickTarget
    }),
    rules() {
      const maxLength = this.maxLength
      const message = this.$t('83853', { num: maxLength })
      return {
        required: this.required,
        validator: (rule, value, callback) => {
          const val = value.trim()
          if (val.length > maxLength && getWordCountLimitDisabled(this.readonly)) {
            return callback(new Error(message))
          }
          if (this.required && !val) {
            return callback(new Error('Please fill in this field.'))
          }
          if (!this.$refs.markdown.validator()) {
            return callback(new Error(__('110654')))
          }
          callback()
        }
      }
    },
    conf() {
      return {
        allow_copy: false,
        allow_free_text: false,
        multiple_selection: false,
        ...this.config
      }
    },
    calcConfig() {
      return {
        ...this.conf,
        ...this.mdConfig
      }
    },
    maxLength() {
      const config = this.config
      const list = config?.word_count_limit ?? []
      return getWordCountLimit(list)
    }
  },
  data() {
    return {
      form: {}
    }
  },
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler(v, oldV) {
        if (this.cacheValue === undefined) {
          this.cacheValue = v?.value ?? ''
        }

        if (!_.isEqual(v, oldV)) {
          this.$set(this, 'form', v)
        }
      }
    },
    form: {
      deep: true,
      handler(v) {
        this.$emit('change', v)
      }
    }
  },
  methods: {
    onChange() {
      if (
        this.clickTarget &&
        !(this.$el.contains(this.clickTarget) || this.clickTarget.classList.contains('CodeMirror-line'))
      ) {
        this.blurInputThrottle()
      }
    },
    blurInputThrottle: _.throttle(function () {
      this.blurInput()
    }, 300),
    async blurInput() {
      const validate = await this.validateForm()

      if (!validate) {
        return
      }

      const isEqual = _.isEqual(this.cacheValue, this.form.value)
      if (!isEqual) {
        this.$emit('save2Draft')
        this.cacheValue = _.cloneDeep(this.form.value)
      }
    },
    validateForm() {
      return new Promise((resolve) => this.$refs.form.validate((valid) => resolve(valid)))
    }
  }
}
</script>
<style lang="scss" scoped>
.markdown-widget {
  position: relative;
}
</style>
