<script>
const getRoleData2actList = function (roleKey) {
  this.$root.template_id = _.get(this.$store.state, 'categoryInfo.template_id', 0)

  const arr = [
    {
      step: 'activity_basic_info', // Step 的编码,
      require: true,
      key: 'basicInfo',
      text: __('82053'),
      path: '/act/activity/basic/',
      route: () => {
        const { query } = this.$route
        query.package_id = undefined
        query.sku_id = undefined
        this.$router.push({
          path: `/act/activity/basic/${this.activity_id}`,
          query: {
            lang: klook.getEditLang(),
            ...query
          }
        })
      },
      finish: false,
      guideTitle: __('87282')
    },
    {
      step: 'activity_detail_info',
      require: true,
      key: 'activityDetail',
      text: __('act_activity_details'),
      path: '/act/activity/detail/',
      route: () => {
        const { query } = this.$route
        query.package_id = undefined
        query.sku_id = undefined
        this.$router.push({
          path: `/act/activity/detail/${this.activity_id}`,
          query: {
            lang: klook.getEditLang(),
            ...query
          }
        })
      },
      // 用于 guide ball step
      guideTitle: __('83965')
    },
    {
      roleKeys: ['admin', 'em'],
      step: 'operational_info',
      require: true,
      key: 'activityNotes',
      text: __('act_notes'),
      path: '/act/activity/notes/',
      route: () => {
        this.$router.push({
          path: `/act/activity/notes/${this.activity_id}`,
          query: {
            lang: klook.getEditLang(),
            ...this.$route.query
          }
        })
      }
    },
    ...(this.$root.isFNB
      ? [
          {
            roleKeys: ['admin', 'em'],
            key: 'activityTags',
            text: __('act_tags'),
            step: 'act_tags',
            path: '/act/activity/tags/',
            hideFinish: true,
            route: () => {
              this.$router.push({
                path: `/act/activity/tags/${this.activity_id}`,
                query: {
                  lang: klook.getEditLang(),
                  ...this.$route.query,
                  model: 'act_tags'
                }
              })
            }
          }
        ]
      : []),
    ...(this.$root.isHotel || this.$root.isFNB
      ? [
          {
            roleKeys: ['admin', 'em'],
            key: 'activityVertical',
            text: this.$root.isFNB ? 'Category Spec' : 'Hotel Voucher',
            step: this.$root.isFNB ? 'act_fnb_menu' : 'act_hotel_voucher',
            path: '/act/activity/vertical/',
            hideFinish: this.$root.isFNB,
            route: () => {
              this.$router.push({
                path: `/act/activity/vertical/${this.activity_id}`,
                query: {
                  lang: klook.getEditLang(),
                  ...this.$route.query,
                  model: this.$root.isFNB ? 'act_fnb_menu' : 'act_hotel_voucher'
                }
              })
            }
          }
        ]
      : []),
    ...(this.$root.isSPU
      ? [
          {
            roleKeys: ['admin', 'em'],
            step: 'activity_spu',
            require: true,
            key: 'activitySpu',
            text: 'Package/Specs',
            path: '/act/activity/special/',
            route: () => {
              this.$router.push({
                path: `/act/activity/special/${this.activity_id}`,
                query: {
                  lang: klook.getEditLang(),
                  ...this.$route.query,
                  model: 'activity_spu'
                }
              })
            }
          }
        ]
      : []),
    {
      pageEdit: 'em',
      roleKeys: ['admin', 'em'],
      step: 'activity_content',
      require: true,
      key: 'activitySeo',
      text: __('act_seo_fields'),
      path: '/act/activity/seo/',
      route: () => {
        this.$router.push({
          path: `/act/activity/seo/${this.activity_id}`,
          query: {
            lang: klook.getEditLang(),
            ...this.$route.query
          }
        })
      },
      getLock: (activity_status) => {
        return activity_status === 2
      }
    }
  ]

  let filterArr = klook.getRoleKeysData(arr, klook.getPlatformAuthKeys())
  //   console.log(999, roleKey, filterArr.length, filterArr);
  return filterArr
}
const getRoleData2pkgList = function (roleKey, options) {
  this.$root.template_id = _.get(this.$store.state, 'categoryInfo.template_id', 0)

  let packageVertical = this.$root.isSPU && !this.$root.isYSIM && !this.$root.isHotel
  const itineraryAct = options?.itinerary_support_config

  let apiMappingSteps = []
  const apiMappingSupportConfig = this.$store.getters.currSupplyApiMappingSupportConfigGetter
  if (apiMappingSupportConfig?.support_supply_api_mapping) {
    // @note support_supply_api_mapping - 0 隐藏, 1 只读, 2 读写
    apiMappingSteps = [
      {
        step: 'supply_api_mapping',
        require: apiMappingSupportConfig.is_required,
        key: 'packageSupplyApiMapping',
        text: __('172677'),
        path: '/act/package/mapping/',
        guideTitle: __('172677')
      }
    ]
  }

  const arr = [
    {
      step: 'package_basic_info',
      require: true,
      key: 'packageInfo',
      text: __('package_tag_info'),
      path: '/act/package/info/',
      guideTitle: __('84084')
    },
    ...(itineraryAct?.support_itinerary
      ? [
          {
            step: 'package_itinerary',
            require: itineraryAct.is_required,
            key: 'packageItinerary',
            text: __('78937'),
            path: '/act/package/itinerary/',
            passCheck: true,
            guideTitle: __('84089')
          }
        ]
      : []),
    {
      step: 'package_detail_info',
      require: false,
      key: 'packageDetails',
      text: __('package_tag_package_details'),
      path: '/act/package/detail/',
      guideTitle: __('84085')
    },
    ...(this.$root.isFNB || packageVertical
      ? [
          {
            roleKeys: this.$root.isFNB ? ['admin', 'em'] : this.$root.roles.is_am ? ['admin'] : ['pass'],
            key: 'packageVertical',
            text: this.$root.isFNB ? __('package_tag_fnb_content') : __('28917'),
            path: '/act/package/special/',
            step: this.$root.isFNB ? 'pkg_content' : 'pkg_pickup_return_time',
            route: (pkg, item) => {
              this.$router.push({
                path: `/act/package/special/${this.activity_id}`,
                query: {
                  lang: klook.getEditLang(),
                  ...this.$route.query,
                  package_id: pkg.package_id,
                  model: this.$root.isFNB ? 'pkg_content' : 'pkg_pickup_return_time'
                }
              })
            }
          }
        ]
      : []),
    // {
    //   step: 'price_inventory',
    //   require: true,
    //   key: 'sellingInfo',
    //   text: __('price_inventory_page'),
    //   path: '/act/package/schedule/',
    //   passCheck: true // 跳过步骤检查
    // },
    ...apiMappingSteps,
    {
      step: 'extra_information',
      require: true,
      key: 'extraInfomation',
      text: __('package_tag_extra_info'),
      path: '/act/package/extra/',
      unionCheck: ['price_inventory', 'extra_information'], // 步骤完成状态联合检查
      guideTitle: __('84088')
    },
    {
      pageEdit: 'admin',
      roleKeys: ['admin', 'em'],
      step: 'financial_model',
      require: true,
      key: 'financialModel',
      text: __('financial_model_financial_model'),
      path: '/act/package/financial_model/'
    }
  ]
  let filterArr = klook.getRoleKeysData(arr, klook.getPlatformAuthKeys())
  //   console.log(999, roleKey, filterArr.length, filterArr);
  return filterArr
}

const actOptionalTools = [
  {
    text: __('79656'),
    path: '/act/activity/addon/'
  }
]

const getOptional = function (isMerchant) {
  const subCategoryId = this.$store.state.categoryInfo?.sub_category_id ?? 0
  const categoryIds = process.env.NODE_ENV === 'production' ? [527] : [446]
  const isPass = categoryIds.includes(subCategoryId)
  return isPass || isMerchant ? [] : actOptionalTools
}

const vm = {
  _getRoleData2actList: getRoleData2actList,
  _getRoleData2pkgList: getRoleData2pkgList,
  data() {
    return {}
  },
  methods: {
    getRoleData(key, options) {
      let roleKey = options ? options.roleKey : klook.getPlatformRoleKey()
      // return vm['_getRoleData2' + key].call(this, 'admin') // test code
      return vm['_getRoleData2' + key].call(this, roleKey, options)
    },
    getOptionalTools(isMerchant) {
      return getOptional.call(this, isMerchant)
    }
  }
}
export default vm
</script>
