<template>
  <div class="act-nav">
    <div class="act-nav-box">
      <a-affix
        :offset-top="showDriverIng ? 0 : 76"
        :style="{ paddingTop: showDriverIng ? '12px' : 0 }"
        class="common-anchor-style hide-all"
      >
        <div ref="actTitleRef" :style="{ paddingBottom: '4px' }">
          <div class="filter-header-box">
            <div class="action-box">
              <a-input-search
                v-model="pkgObj.searchVal"
                class="search-box common-search-style"
                :placeholder="$t('package_id_title')"
                :allow-clear="true"
              >
                <a-button slot="enterButton" icon="search"></a-button>
              </a-input-search>
              <a-popover
                v-model="pkgFilterObj.show"
                overlayClassName="common-tooltip-style"
                :arrowPointAtCenter="true"
                placement="bottomRight"
                :getPopupContainer="getPopupContainer.bind(this, '.act-nav .filter-header-box')"
                trigger="click"
              >
                <span
                  :class="[
                    'pkg-filter__btn-action',
                    pkgFilterObj.show && 'on',
                    pkgFilterObj.valueMaps.length && 'has-filter'
                  ]"
                  v-bind="$root.bindIHTrack({ type: 'module', spm: 'Package_filter' })"
                >
                  <a-icon type="filter" />
                </span>
                <template slot="content">
                  <div class="pkg-status__list-box">
                    <a-checkbox-group v-model="pkgFilterObj.valueMaps" @click.prevent="">
                      <div
                        v-for="(status, i) in pkgFilterObj.list"
                        :key="`${getUuid}__${i}`"
                        class="pkg-status__line-box"
                        v-bind="getFilterBtnTrack(status, pkgFilterObj.valueMaps)"
                      >
                        <a-checkbox :value="status.value">
                          <span @click.stop="" data-click-stop="阻止两次埋点">
                            <i
                              :class="['common-pkg-status-circle', status.value]"
                              style="margin-right: 6px"
                            ></i>
                            {{ status.label }}
                          </span>
                        </a-checkbox>
                      </div>
                    </a-checkbox-group>
                  </div>
                </template>
              </a-popover>
            </div>
          </div>
          <div class="section-title-box act-title">
            {{ $t('global_activity') }}
          </div>
          <div
            :class="['act-title-box']"
            v-bind="$root.bindIHTrack({ type: 'module', spm: 'Act_Page' })"
            @click="gotoActBaseInfo"
          >
            <div class="act-title-box__act-box" :class="[isActivity && 'on']">
              <a-tooltip placement="right">
                <div class="title-text common-two-line">
                  {{ `${activity_id} - ${actInfo.activity_title || ''}` }}
                </div>
                <div slot="title" class="tooltip-title">
                  <div>
                    {{ mapsFilterFn('activity_status', actInfo.activity_status, 'label') }}
                  </div>
                  <div>{{ `${activity_id} - ${actInfo.activity_title || ''}` }}</div>
                </div>
              </a-tooltip>
            </div>
          </div>
          <div v-if="calcShowAction" class="section-title-box pkg-title">
            <span class="pkg-title-text">{{ $t('global_package') }}</span>
            <span
              v-if="calcShowAction && !lockMerchantActEditing"
              v-bind="$root.bindIHTrack({ type: 'module', spm: 'Create_pkg' })"
              class="pkg-title-btn"
              @click.stop="createPkgHandler"
            >
              <a-tooltip :title="$t('82183')">
                <span class="btn-add-action">
                  <a-icon type="plus"></a-icon>
                </span>
              </a-tooltip>
            </span>
          </div>
        </div>
        <div v-if="calcShowAction">
          <div class="nav-pkg-box" :style="calcPkgStyle" key="2" :show-arrow="false">
            <a-collapse
              v-model="activePkgIds"
              class="common-collapse-style hide-collapse-header common-merchant-style"
              :bordered="false"
              @change="handleChangeActive"
            >
              <div v-if="pkgInfos.packages && !pkgInfos.packages.length" class="none-pkg-box">
                <svg-icon class="not-found-icon" icon-name="addon-notdata" />
                <div class="not-found-text pointer" v-html="calcAddPkgNode" @click="createPkgHandler"></div>
              </div>
              <div v-else-if="pkgInfos.packages && !pkgFilterList.length" class="none-pkg-box">
                <svg-icon class="not-found-icon" icon-name="addon-notdata" />
                <div class="not-found-text">{{ $t('79674') }}</div>
              </div>
              <a-collapse-panel
                v-for="pkg in pkgFilterList"
                :key="'' + pkg.package_id"
                class="panel-menu-wrap"
                :class="[calcOnPkgClass(pkg), pkg.package_id === calcPid && 'js-driver-step-2']"
                :show-arrow="false"
                :disabled="true"
              >
                <template slot="header">
                  <div class="pkg-title-wrap">
                    <div
                      class="pkg-title-box"
                      :class="{
                        on: !isActivity && pkg.package_id === calcPid,
                        'is-expand': activePkgIds.includes(String(pkg.package_id))
                      }"
                      @click="handlerGoPkg(pkg)"
                    >
                      <span class="btn-icon btn-icon__left">
                        <i
                          :class="['common-pkg-status-circle', getPkgStatus(pkg.merchant_package_status)]"
                        ></i>
                      </span>
                      <div
                        class="pkg-text common-two-line"
                        :class="
                          !calcSkuId && !isActivity && +$route.query.package_id === +pkg.package_id
                            ? 'on'
                            : ''
                        "
                      >
                        <a-tooltip
                          class="common-tooltip-style"
                          placement="right"
                          :getPopupContainer="
                            getPopupContainer.bind(this, '.act-nav .nav-pkg-box .common-collapse-style')
                          "
                          :autoAdjustOverflow="false"
                        >
                          <div slot="title" class="tooltip-title">
                            <div class="status-wrap">
                              <i
                                :class="[
                                  'common-pkg-status-circle',
                                  getPkgStatus(pkg.merchant_package_status)
                                ]"
                              ></i>
                              <span class="status-text">
                                {{ getPkgStatus(pkg.merchant_package_status, 'label') }}
                              </span>
                            </div>
                            <div>{{ getPkgName(pkg) }}</div>
                          </div>
                          <div>
                            {{ getPkgName(pkg) }}
                          </div>
                        </a-tooltip>
                      </div>
                      <span
                        v-if="!lockMerchantActEditing"
                        class="btn-icon btn-icon__right"
                        v-bind="$root.bindIHTrack({ type: 'module', spm: 'Create_Unit' })"
                        @click.stop="handleCreateUnit(pkg.package_id)"
                      >
                        <a-tooltip :title="$t('82181')">
                          <a-icon type="plus" />
                        </a-tooltip>
                      </span>
                    </div>
                  </div>
                </template>
                <div class="units-menu-wrap">
                  <ul class="units-menu-box" :class="dragCursor">
                    <draggable
                      v-if="pkgsCacheState[pkg.package_id] && pkgsCacheState[pkg.package_id]['pkgUnitsMenus']"
                      v-model="pkgsCacheState[pkg.package_id]['pkgUnitsMenus']"
                      chosen-class="chosen"
                      force-fallback="true"
                      :group="`pkg-${pkg.package_id}-unit-published`"
                      animation="1000"
                      :disabled="lockMerchantActEditing"
                      @change="handleDragChange(pkg)"
                      @start="onStart"
                      @end="onEnd"
                    >
                      <transition-group :key="`published-transition-group-${pkg.package_id}`">
                        <li
                          v-for="element in pkgsCacheState[pkg.package_id]['pkgUnitsMenus']"
                          :key="'' + element.sku_id"
                          :class="{ 'unit-unpublished': !element.published }"
                          @click="gotoPkgUnit(element, pkg)"
                        >
                          <div
                            class="unit-box"
                            :class="[calcSkuId === element.sku_id && 'on']"
                            v-bind="$root.bindIHTrack({ type: 'module', spm: 'Unit_Page' })"
                          >
                            <span class="unit-text">
                              <!-- unit item text width flex-grow/flex-shrink = 1, 需适配宽度伸缩情况 -->
                              <a-tooltip
                                placement="right"
                                overlay-class-name="unit-item-tooltip"
                                :align="{ offset: calcCalendarCompleted(element) ? [74, 0] : [50, 0] }"
                              >
                                <span class="text">{{ element.name }}</span>
                                <div slot="title" class="title">
                                  <div>
                                    {{
                                      mapsFilterFn('sku_status2merchant', Number(element.published), 'label')
                                    }}
                                  </div>
                                  <div>{{ element.sku_id }} - {{ element.name }}</div>
                                  <!-- "share_rel_ship"`    // 0 无共享， 1 子sku, 2 主sku -->
                                  <div
                                    v-if="element.share_rel_ship !== 0"
                                    class="tag"
                                    :class="{ '--is-main': element.share_rel_ship === 2 }"
                                  >
                                    {{ element.share_rel_ship === 2 ? $t('package_unit_main') : $t('89149') }}
                                  </div>
                                </div>
                              </a-tooltip>
                            </span>
                            <span v-if="calcCalendarCompleted(element)" class="unit-icon">
                              <a-tooltip :title="$t('83108')" placement="right" :align="{ offset: [50, 0] }">
                                <a-icon type="carry-out"></a-icon>
                              </a-tooltip>
                            </span>
                            <span v-if="!lockMerchantActEditing" class="drag-icon-box">
                              <a-tooltip :title="$t('82192')" placement="right" :align="{ offset: [26, 0] }">
                                <i><svg-icon icon-name="custom-menu"></svg-icon></i>
                              </a-tooltip>
                            </span>
                          </div>
                        </li>
                      </transition-group>
                    </draggable>
                  </ul>
                </div>
              </a-collapse-panel>
            </a-collapse>
          </div>
        </div>
        <ActOptionalTools
          v-if="actOptionalTools.length"
          :disabled="!addonList.length"
          :options="actOptionalTools"
        />
      </a-affix>
      <div v-show="navLoading" class="common-spin-style"><a-spin /></div>
    </div>
    <CreatePackage :visible="showCreatePackage" @cancel="showCreatePackage = false" />
    <CreateUnit :visible="showCreateUnit" :package-id="currentPackage" @cancel="showCreateUnit = false" />
  </div>
</template>

<script>
import { mapActions, mapMutations, mapState, mapGetters } from 'vuex'
import { parseStr, getEditLang, matchLang } from '@activity/utils'
import { isMerchant } from '@/env'
import actNavLib from './actNavLib'
import ActOptionalTools from '@activity/components/act-optional-tools'
import draggable from 'vuedraggable'
import { action as pkgsAction, state as pkgsState } from '@activity/store/pkgs-cache-observable.js'

import { fnb_package_types } from '../../pages/package/package_const'
// 新建套餐
import CreatePackage from '@activity/pages/package/units/new-components/create-package'
import CreateUnit from '@activity/pages/package/units/new-components/create-unit-drawer'
import { UNIT_STEPS } from '@activity/pages/package/package_const.js'
import { getUuid } from '@activity/utils'
import maps from '@activity/utils/maps.js'

export default {
  name: 'MerchantNav',
  components: {
    draggable,
    ActOptionalTools,
    CreatePackage,
    CreateUnit
  },
  // inject: ['reloadPage2provide'],
  inject: {
    driver2provide: {
      default: {}
    },
    provideGlobalData: {
      default: {
        hiddenAddPkg: false
      }
    }
  },
  data() {
    return {
      dragCursor: '',
      pkgFilterObj: {
        show: false,
        valueMaps: ['draft', 'published', 'unpublished'], // 展示给商户的套餐状态 1：草稿(从未发布过&下架状态), 2: 已发布,3: 已下架(有发布过&下架状态)
        list: [
          { label: __('global_draft'), mapValue: 1, value: 'draft', track: { spm: 'Filter_Draft' } },
          {
            label: __('global_published'),
            mapValue: 2,
            value: 'published',
            track: { spm: 'Filter_Published' }
          },
          {
            label: __('global_unpublished'),
            mapValue: 3,
            value: 'unpublished',
            track: { spm: 'Filter_Unpublished' }
          }
        ]
      },
      showDriverSteps: false,
      showDriverIng: false,
      showCreatePackage: false,
      showCreateUnit: false,
      pkgsCacheState: pkgsState['pkgsCache'],
      actTitleHeight: 160,
      activePkgStatus: 'published',
      routeItem: {},
      isMerchant,
      navLoading: false,
      pkgStepStatusMap: {},
      activePkgIds: [],
      activeKeysDef: ['1', '2'],
      pkgInfos: {
        package_info: []
      },
      activity_id: this.$route.params.id,
      package_id: +this.$route.query.package_id,
      actObj: {
        list: actNavLib.methods.getRoleData.call(this, 'actList')
      },
      pkgObj: {
        addFlag: true,
        searchVal: '',
        addList: [],
        list: []
      },
      actOptionalTools: actNavLib.methods.getOptionalTools.call(this, isMerchant),
      currentPackage: 0
    }
  },
  watch: {
    calcItinerarySupportConfig: {
      immediate: true,
      deep: true,
      handler() {
        this.updatePkgObjList()
      }
    },
    calcApiMappingSupportConfig: {
      immediate: true,
      deep: true,
      handler() {
        this.updatePkgObjList()
      }
    },
    showDriverIng: {
      handler(isb) {
        document.body.style.overflow = isb ? 'hidden' : ''
      }
    },
    'actInfo.activity_title': {
      immediate: true,
      handler() {
        setTimeout(() => {
          const ref = this.$refs.actTitleRef
          ref?.offsetHeight && (this.actTitleHeight = ref?.offsetHeight)
        }, 1000)
      }
    },
    '$route.query.package_id': {
      handler(v) {
        this.$store.commit('setPkgStepStatus2mutations', this.pkgStepStatusMap[v] || {})
        this.activePkgIds = v ? ['' + v] : []
      }
    },
    '$route.path': {
      immediate: true,
      handler(path) {
        let { actObj, pkgObj } = this
        let arr = [...actObj.list, ...pkgObj.list]
        let obj = arr.find((o) => path.indexOf(o.path) > -1)
        this.routeItem = obj
        this.$emit('change', this.routeItem)
      }
    },
    pkgStepStatusMap(obj) {
      this.setPkgStepStatusMap2mutations(obj)
    },
    actStepStatusObj: {
      handler() {
        this.updateFinishStatus()
      },
      deep: true
    }
  },
  // eslint-disable-next-line vue/order-in-components
  computed: {
    ...mapGetters({
      calcApiMappingSupportConfig: 'currSupplyApiMappingSupportConfigGetter'
    }),
    calcItinerarySupportConfig() {
      return this.$store.state.actItemData?.itinerary_support_config
    },
    calcCalendarCompleted() {
      return (unitItem) => {
        const { step } = unitItem || {}
        return step?.includes(UNIT_STEPS[1])
      }
    },
    calcAddPkgNode() {
      const addNode =
        '<span class="js-create-pkg-handler" style="color: #437dff; cursor: pointer;"><span style="font-size: 16px; margin: 0 4px;">+</span></span>'
      return this.$t('83098') + addNode
    },
    calcSkuId() {
      if (this.$route.name !== 'packageUnit') {
        return
      }
      const arr = this.pkgsCacheState[this.calcPid]?.pkgUnitsMenus
      const skuId = Number(this.$route.query.sku_id)
      if (!arr || !arr.some((o) => o.sku_id === skuId)) {
        return
      }
      return skuId
    },
    calcOnPkgClass() {
      return (pkg) => {
        let { isActivity, $route } = this
        return !isActivity && pkg.package_id == $route.query.package_id ? 'on-pkg' : ''
      }
    },
    calcShowAddBtn() {
      if (this.provideGlobalData?.hiddenAddPkg) {
        return false
      }

      let page_from = klook.getPlatformRoleKey()
      return isMerchant || (page_from !== 'bd_audit' && this.$root.roles.is_am)
    },
    isActivity() {
      return this.$route.path.includes('/act/activity/')
    },
    calcShowAction() {
      let isTrue = false
      if (this.isMerchant) {
        // isTrue = !!this.$store.state.actStepStatus.activity_detail_info
        isTrue = true
      } else if (this.$root.roles.is_am) {
        isTrue = !!this.$store.state.actStepStatus.operational_info
      }
      return isTrue
    },
    actInfo() {
      return this.$attrs.actInfo || {}
    },
    pkgInfo() {
      return this.$attrs.pkgInfo || {}
    },
    calcAuthLock() {
      return function (item) {
        let isLock = typeof item.getLock === 'function' ? item.getLock(this.actInfo.activity_status) : false
        this.$set(item, 'isLock', isLock)
        this.$set(item, 'title', isLock ? __('no_auth_edit') : '')
        return isLock
      }
    },
    calcPkgStyle() {
      const hackHeight = 64 + 12 + this.actTitleHeight
      let height = `calc(100vh - ${hackHeight}px)`
      return {
        paddingBottom: '48px',
        height
      }
    },
    calcActRequireLock() {
      let arr = this.actObj.list.filter((o) => o.require)
      return arr.length
    },
    activeKeys: {
      get() {
        return this.activeKeysDef
      },
      set() {
        this.activeKeysDef = ['1', '2']
      }
    },
    ...mapState({
      actStepStatusObj: 'actStepStatus',
      addonList: 'addonPackageList',
      lockMerchantActEditing: 'lockMerchantActEditing'
    }),
    showAddPkg() {
      return !this.$route.query.package_id && this.$route.path.indexOf('/act/package/') !== -1
    },
    tipText() {
      return parseStr(this.$t('actNav.regText'), { number: 446 })
    },
    calcPid() {
      return klook.parse2id(this.$route.query.package_id)
    },
    pkgFilterList2published() {
      return this.pkgFilterList.filter((o) => o.publish_status === 1) || []
    },
    pkgFilterList2unpublished() {
      return this.pkgFilterList.filter((o) => o.publish_status === 0) || []
    },
    pkgFilterList() {
      const { pkgFilterObj, pkgInfos } = this
      const statusArr = pkgFilterObj.valueMaps.map((str) => {
        return pkgFilterObj.list.find((o) => o.value === str)?.mapValue
      })
      const pkgs = pkgInfos.packages?.filter?.((o) => statusArr.includes(o.merchant_package_status)) || []
      let {
        pkgObj: { searchVal }
      } = this
      if (!searchVal) {
        return pkgs || []
      }
      let arr = pkgs.filter((pkg) => {
        return `${pkg.package_id} - ${pkg.package_local ? pkg.package_local[0].name : ''}`.includes(searchVal)
      })
      return arr
    }
  },
  async created() {
    this.navLoading = true
    try {
      await this.initActStepStatus(true)
      await this.updatePkgInfos2bus()
      this.checkActIncludePkg(this.pkgInfos?.packages, this.package_id)
      await this.getPackageSkuData({ package_id: this.package_id })
      const skuId = Number(this.$route.query.sku_id)
      this.checkPkgIncludeSku(pkgsAction.get(this.package_id, 'pkgUnitsMenus'), skuId)
    } catch (error) {
      //
    }
    this.navLoading = false
    this.package_id && (this.activePkgIds = ['' + this.package_id])
    klook.bus.$off('createPkgAndUnit2bus').$on('createPkgAndUnit2bus', this.createPkgAndUnit)
    klook.bus.$off('getPackageSkuDataBus').$on('getPackageSkuDataBus', this.updatePackageSkuData)
  },
  beforeDestroy() {
    klook.bus.$off('getPackageSkuDataBus').$off('createPkgAndUnit2bus')
  },
  methods: {
    mapsFilterFn: maps.filterFn,
    ...mapActions([
      'updateActivityStepStatusById',
      'updatePackageStepStatusById',
      'getPkgInfo2actions',
      'actionGetAddon'
    ]),
    ...mapMutations(['setPkgInfos2mutations', 'setPkgStepStatusMap2mutations']),
    getUuid,
    updatePkgObjList: _.debounce(function () {
      this.pkgObj.list = actNavLib.methods.getRoleData.call(this, 'pkgList', {
        itinerary_support_config: this.calcItinerarySupportConfig,
        apiMappingSupportConfig: this.calcApiMappingSupportConfig
      })
    }, 30),
    checkActIncludePkg(arr, pid) {
      if (!pid || arr?.find((o) => o.package_id === pid)) {
        return true
      }
      const { query } = this.$route
      query.package_type = undefined
      query.package_id = undefined
      query.sku_id = undefined
      this.$router.replace({
        path: '/act/activity/basic/' + this.activity_id,
        query: {
          ...query
        }
      })
    },
    checkPkgIncludeSku(arr, sid) {
      if (!sid || arr?.find((o) => o.sku_id === sid)) {
        return true
      }
      const { query } = this.$route
      query.sku_id = undefined
      this.$router.replace({
        path: '/act/package/info/' + this.activity_id,
        query: {
          ...query
        }
      })
    },
    getFilterBtnTrack(status, values) {
      const obj = this.$root.bindIHTrack({
        type: 'module',
        ...(status.track || {}),
        query: {
          ext: JSON.stringify({
            ClickType: !values.includes(status.value) ? 'Select' : 'Deselect'
          })
        }
      })
      return obj
    },
    getPkgName(pkg) {
      if (!pkg.package_id) {
        return ''
      }
      return `${pkg.package_id} - ${pkg.package_local ? pkg.package_local[0].name : ''}`
    },
    getPkgStatus(val, key = 'value') {
      const { pkgFilterObj } = this
      const item = pkgFilterObj.list?.find((o) => o.mapValue === val)
      return item[key] || ''
    },
    async createPkgAndUnit(package_id) {
      if (!package_id) {
        return
      }
      const showDriverStepsCache = this.showDriverSteps
      await this.updatePkgInfos2bus(null, package_id)
      await this.getPackageSkuData({ package_id }, { refleshApiData: true })
      if (!showDriverStepsCache && this.showDriverSteps) {
        setTimeout(() => {
          this.showDriverHandler()
        }, 200)
      }
    },
    async updatePackageSkuData(pid, callback) {
      const package_id = pid || this.calcPid
      const activeConf = {
        pkgUnpublishActive:
          this.pkgsCacheState[package_id] && this.pkgsCacheState[package_id]['pkgUnpublishActive']
      }

      const response = await this.getPackageSkuData({ package_id }, { refleshApiData: true, ...activeConf })
      callback?.({ response })
    },
    showDriverHandlerTest() {
      // 测试driver
      setTimeout(() => {
        this.showDriverHandler()
      }, 200)
    },
    showDriverHandler() {
      if (!this.driver2provide?.start) {
        return
      }
      this.showDriverIng = true
      this.driver2provide?.defineSteps([
        {
          element: '.act-nav .js-driver-step-1',
          popover: {
            className: 'common-driver-popover-style',
            title: `<div class="custom-driver-box">
              <div class="custom-driver__left-box">
                <img class="left-box__driver-icon" src="https://res.klook.com/image/upload/w_400/Vector_yqmesm.svg"></img>
              </div>
              <div class="custom-driver__right-box">
                <p class="custom-driver-title">${this.$t('80930')}</p>
                <p class="custom-driver-footer">
                  <span class="custom-driver-step">1/2</span>
                  <span class="custom-driver-btns">
                    <span class="custom-driver-btns__btn driver-next-btn">${this.$t(
                      'global.user_guide_next'
                    )}</span>
                  </span>
                <p>
              </div>
            </div>`,
            position: 'bottom'
          }
        },
        {
          element: '.act-nav .js-driver-step-2',
          popover: {
            className: 'common-driver-popover-style',
            title: `<div class="custom-driver-box">
              <div class="custom-driver__left-box">
                <img class="left-box__driver-icon" src="https://res.klook.com/image/upload/w_400/Vector_yqmesm.svg"></img>
              </div>
              <div class="custom-driver__right-box">
                <p class="custom-driver-title">${this.$t('80931')}</p>
                <p class="custom-driver-footer">
                  <span class="custom-driver-step">2/2</span>
                  <span class="custom-driver-btns">
                    <span class="custom-driver-btns__btn driver-next-btn">${this.$t(
                      'global.user_guide_got_it'
                    )}</span>
                  </span>
                <p>
              </div>
            </div>`,
            position: 'right'
          },
          onNext: () => {
            this.showDriverIng = false
          }
        }
      ])
      this.driver2provide?.start()
    },
    handleCreateUnit(pkg) {
      this.showCreateUnit = true
      this.currentPackage = pkg
    },
    getPriorityList(pid) {
      const cacheData = this.pkgsCacheState[pid] || {}
      const unitList = [...(cacheData.pkgUnitsMenus || [])]
      return unitList.map((item, index) => ({
        priority: index,
        sku_id: item.sku_id
      }))
    },
    async handleDragChange(pkg) {
      if (!pkg?.package_id) {
        return
      }
      const { package_id } = pkg
      this.dragUnitsLoading = true
      const res = await ajax.postBody(ADMIN_API.act.update_sku_priority, {
        data: {
          package_id,
          priority_list: this.getPriorityList(package_id)
        }
      })
      if (res.success) {
        const activeConf = {
          pkgUnpublishActive:
            this.pkgsCacheState[package_id] && this.pkgsCacheState[package_id]['pkgUnpublishActive']
        }
        await this.getPackageSkuData(pkg, { refleshApiData: true, ...activeConf })
        this.dragUnitsLoading = false
        // this.$message.success(this.$t('global_success'))
      } else {
        // this.unitList = [...this.unitListCached]
        this.dragUnitsLoading = false
        // this.$message.error(this.$t('global_failure'))
      }
    },
    onStart() {
      this.dragCursor = 'grabbing'
    },
    onEnd() {
      this.dragCursor = ''
    },
    createPkgHandler() {
      this.showCreatePackage = true
    },
    async getPackageSkuData({ package_id }, options) {
      if (!package_id) {
        return false
      }
      const opt = options || {}
      const apiUrl = ADMIN_API.act.query_package_sku_list
      let res = opt?.refleshApiData ? null : pkgsAction.get(package_id, 'getQueryPackageSkuList', apiUrl)
      if (!res?.success) {
        res = await ajax.getBody(
          apiUrl,
          {
            params: { package_id: package_id, language: getEditLang() }
          },
          {
            ...opt,
            loading: false
          }
        )
      }
      let pkgUnitsMenus = null
      let pkgPublishUnits = null
      let pkgUnpublishUnits = null
      if (res?.success) {
        pkgUnitsMenus = (res.result?.units || []).map((item) => {
          const { local } = item
          const name = matchLang('unit_name', 'language', local, klook.getEditLang())
          const defaultName = local[0]?.unit_name
          item.unit_name_format = name || defaultName
          return {
            ...item,
            name: `${item.unit_name_format}`
          }
        })
        pkgPublishUnits = pkgUnitsMenus.filter((o) => o.published)
        pkgUnpublishUnits = pkgUnitsMenus.filter((o) => !o.published)
      }
      pkgsAction.set(package_id, 'getQueryPackageSkuList', res, apiUrl)
      pkgsAction.set(package_id, 'pkgUnitsMenus', pkgUnitsMenus)
      pkgsAction.set(package_id, 'pkgPublishUnits', pkgPublishUnits)
      pkgsAction.set(package_id, 'pkgUnpublishUnits', pkgUnpublishUnits)
      pkgsAction.set(
        package_id,
        'pkgUnpublishActive',
        opt?.pkgUnpublishActive ||
          (!pkgPublishUnits?.length || pkgUnpublishUnits.some((o) => o.sku_id === this.calcSkuId)
            ? [`unpublished-units-${package_id}`]
            : [])
      )

      return res.result
    },
    gotoActBaseInfo() {
      const item = this.actObj?.list?.find((o) => o.key === 'basicInfo')
      if (!item || this.$route.path.includes(item?.path)) {
        return
      }
      this.handleGoRoute(item, 0)
    },
    gotoPkgUnit(unit, pkg) {
      // 下面的判断逻辑改动需同步 src/modules/activity/components/guideBall/expandedState.vue?gotoPkgUnit#L210
      if (this.actStepStatusObj?.activity_basic_info !== 1 && !pkg?.time_zone) {
        this.$message.warn(this.$t('83890'))
        return
      }
      const { sku_id } = unit || {}
      if (!sku_id || this.calcSkuId === sku_id) {
        return
      }
      let { query } = this.$route
      this.$router.replace({
        path: `/act/package/unit/${this.activity_id}`,
        query: {
          ...query,
          package_type: pkg?.package_type,
          package_id: pkg?.package_id,
          sku_id: sku_id
        }
      })
    },
    getPkgStatusLineClass(pkg) {
      return (
        {
          1: 'publish-line',
          0: 'unpublish-line',
          2: 'suspend-line',
          3: 'suspend-line'
        }[pkg.publish_status] ?? ''
      )
    },
    checkSpecialNav(pkg, item) {
      let { package_type } = pkg
      let { step } = item
      let isb = true
      switch (step) {
        case 'pkg_content':
          isb = [fnb_package_types.fnb_set_menu, fnb_package_types.fnb_single_dish].includes(package_type)
          break
      }
      return isb
    },
    gotoArchivedPackages() {
      klook.gotoOldAdmin(`/act/activity/${this.activity_id}/package_list_archive`)
    },
    gotoCreatePkg2bus() {
      let { query } = this.$route
      let packages = this.$store.getters.pkgList
      _.get(packages, '[0].package_id', '') ||
        this.$router.push({
          name: 'packageBasicInfo',
          query: {
            ...query,
            package_id: _.get(packages, '[0].package_id', ''),
            package_type: _.get(packages, '[0].package_type', '1')
          },
          params: {
            id: this.activity_id
          }
        })
    },
    clickPkgHeader() {
      if (this.activePkgIds.length) {
        this.activePkgIds2copy = _.clone(this.activePkgIds)
        this.activePkgIds = []
      } else {
        this.activePkgIds = this.activePkgIds2copy
      }
    },
    getPkgActive(pkg, i) {
      let { query, path } = this.$route
      let pkgActive = 'pkg-active'
      if (pkg.package_id == query.package_id) {
        let pkgList = this.pkgObj.list
        return path.includes(pkgList[i].path) ? pkgActive : ''
      }
      return ''
    },
    getFinished({ step, unionCheck }, obj) {
      let isb = false

      if (Array.isArray(unionCheck) && unionCheck.length && unionCheck.every((item) => obj[item])) {
        return true
      }

      if (typeof step === 'string') {
        isb = obj[step]
      } else if (klook.getType(step) === 'Array') {
        let arr = step.map((o) => obj[o])
        isb = arr.every((v) => !!v)
      }

      return !!isb
    },
    calcNavLock(i, id) {
      // id不存在则表示活动层级
      let { pkgStepStatusMap, actStepStatusObj, pkgObj, actObj } = this
      let maps = !id ? actStepStatusObj : pkgStepStatusMap
      let list = !id ? actObj.list : pkgObj.list
      let curItem = list[i]
      let preItem = this.getPreRequireItem(i - 1, list)
      let obj = !id ? maps : maps[id]
      if (!preItem || !obj) return false //第一个套餐基本信息不用锁
      if (this.getFinished(curItem, obj)) {
        return false //自己已完成绿标跳过
      }

      if (!preItem.require || preItem.passCheck) {
        return false
      }

      return !this.getFinished(preItem, obj)
    },
    getPreRequireItem(i, list) {
      let obj = list[i]
      if (i < 0) {
        return false
      }
      if (obj.require) {
        return obj
      } else {
        return this.getPreRequireItem(i - 1, list)
      }
    },
    calcPkgFinished(packageId, step) {
      const pgkSteps = this.pkgStepStatusMap[packageId]

      if (!pgkSteps) {
        return false
      }

      return Array.isArray(step) ? step.every((item) => !!pgkSteps[item]) : pgkSteps[step]
    },
    async clickCollapaseHandler(pkg) {
      if (!pkg?.package_id) {
        return
      }
      await this.getPackageSkuData(pkg, { loading: true })
      const { activePkgIds } = this
      const { package_id } = pkg
      const pidStr = '' + package_id
      if (activePkgIds.includes(pidStr)) {
        // activePkgIds.splice(activePkgIds.indexOf(pidStr), 1)
      } else {
        activePkgIds.pop()
        activePkgIds.push(pidStr)
      }
      this.handleChangeActive(activePkgIds)
    },
    handleChangeActive(keys) {
      keys.forEach((key) => {
        this.getPkgStepStatus(key)
      })
    },
    async updatePkgInfos2bus(data, newPkgId) {
      if (data) {
        this.$set(this, 'pkgInfos', data)
      } else {
        const pkgInfos = await this.initPkgInfos({
          refresh: true
        })
        if (pkgInfos?.packages?.length) {
          this.showDriverSteps = true
        }
        this.$set(this, 'pkgInfos', pkgInfos)
        await this.getPkgStepStatus(newPkgId, true)
      }
      klook.bus.$emit('refreshGuideStatus')
    },
    clickAddPackage() {
      if (this.showAddPkg) return
      // !this.activeKeys.includes('2') && this.activeKeys.push('2')
      let { pkgObj } = this
      if (pkgObj.addFlag || this.$route.path.includes('/act/activity/')) {
        pkgObj.addFlag = false
        this.$router.replace({
          path: `/act/package/info/${this.$route.params.id}`,
          query: {
            ...this.$route.query,
            package_type: 1,
            package_id: '',
            sku_id: ''
          }
        })
      }
    },
    async updateFinishStatus(save = false) {
      if (!isNaN(this.activity_id)) {
        let stepObj = this.actStepStatusObj
        this.initFinishStatus(this.actObj.list, stepObj, 'step')
        this.pkgObj.addFlag = !!stepObj.activity_detail_info
        if (
          save &&
          stepObj.activity_detail_info &&
          (!this.pkgInfos.packages || !this.pkgInfos.packages.length)
        ) {
          this.clickAddPackage()
        }
      }
    },
    async initActStepStatus(sync = false) {
      let data = {
        activity_id: +this.activity_id,
        language: getEditLang(),
        refresh: true
      }
      if (sync) {
        return await this.updateActivityStepStatusById(data)
      } else {
        return this.updateActivityStepStatusById(data)
      }
    },
    async initPkgInfos(data = {}) {
      let pkgInfos = await this.getPkgInfo2actions({
        activity_id: +this.activity_id,
        refresh: data.refresh
      })
      return klook.getType(pkgInfos) === 'Object' ? pkgInfos : {}
    },
    async getPkgStepStatus(packageId, refresh) {
      let package_id = +packageId || +this.$route.query.package_id
      if (!package_id || (this.pkgStepStatusMap[package_id] && !refresh)) return
      let stepObj = await this.updatePackageStepStatusById({
        activity_id: +this.activity_id,
        package_id,
        language: getEditLang(),
        refresh: true
      })
      this.$set(this.pkgStepStatusMap, package_id, stepObj || {})
      this.$route.query.package_id &&
        this.$store.commit(
          'setPkgStepStatus2mutations',
          this.pkgStepStatusMap[this.$route.query.package_id] || {}
        ) //保证使用当前套餐
      !this.activePkgIds.includes('' + package_id) && this.activePkgIds.push('' + package_id)
    },
    initFinishStatus(arr = [], stepObj = {}, stepKey = 'step') {
      let count = 1
      arr.forEach((item) => {
        let stepStatus = stepObj[item[stepKey]]
        this.$set(item, 'finish', stepStatus === 1)
        if (item[stepKey] in stepObj) {
          count++
        }
      })
      return count
    },
    async handlerGoPkg(pkg) {
      if (!pkg?.package_id) {
        return
      }
      this.clickCollapaseHandler(pkg)
      const item = this.pkgObj?.list?.find((o) => o.key === 'packageInfo')
      item && this.handleGoRoute(item, 0, pkg)
      await this.getPackageSkuData(pkg)
    },
    handleGoRoute(item, i, pkg = {}) {
      if (this.calcNavLock(i, pkg.package_id)) {
        return false
      }
      let { route } = item
      if (this.$route.path.includes(item.path) && +pkg.package_id === +this.$route.query.package_id) {
        return false
      }
      this.pkgObj.addFlag = true
      if (typeof route === 'function') {
        route(pkg, item)
      } else {
        if (!item.path) return false
        const isActivity = item.path.includes('/act/activity/')
        const mergeQuery = isActivity
          ? { package_id: undefined, sku_id: undefined }
          : { package_id: pkg.package_id }
        this.$router.replace({
          path: item.path + this.activity_id,
          query: {
            ...this.$route.query,
            package_type: pkg.package_type,
            ...mergeQuery
          }
        })
      }
    },
    getPopupContainer(el) {
      return document.querySelector(el) || document.body
    }
  }
}
</script>

<style lang="scss">
.unit-item-tooltip {
  .title div {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .tag {
    width: fit-content;
    margin-top: 8px;
    padding: 2px 8px;
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
    border-radius: 2px;
    color: #faad14;

    &.--is-main {
      color: #437dff;
      background-color: #f0f7ff;
      border: 1px solid #bdd8ff;
    }
  }
}
</style>

<style lang="scss" scoped>
::v-deep .ant-collapse .ant-collapse-item-disabled > .ant-collapse-header,
.ant-collapse .ant-collapse-item-disabled > .ant-collapse-header > .arrow {
  cursor: pointer;
}

.section-title-box {
  padding: 0 16px;
  font-size: 14px;
  line-height: 22px;
  color: #8c8c8c;
  &.act-title {
    margin-top: 24px;
  }
  &.pkg-title {
    margin-top: 12px;
    display: flex;
    align-items: center;

    .pkg-title-text {
      flex: 1 1 auto;
    }

    .pkg-title-btn {
      color: #595959;
      cursor: pointer;

      &:hover {
        color: #437dff;
      }
    }
  }
}

.pkg-status {
  &__line-box {
    font-size: 14px;
    line-height: 22px;
    &:not(:last-of-type) {
      margin-bottom: 10px;
    }
    label {
      width: 100%;
    }
  }
}

.filter-header-box {
  display: flex;
  align-items: center;
  padding: 0 16px;
}
.pkg-filter__btn-action {
  position: relative;
  margin-left: 8px;
  padding: 6px;
  border-radius: 4px;
  font-size: 14px;
  line-height: 14px;
  cursor: pointer;
  &.has-filter {
    background: #e6f1ff;
    color: #437dff;
  }
  &.on,
  &:hover {
    color: #437dff;
  }
}
.units-title-box {
  display: flex;
  align-items: center;
  color: #437dff;
  padding: 0 16px 10px 40px;
  background-color: #fafafa;
  font-weight: 400;
  .arrow-icon {
    font-size: 14px;
    margin-left: 8px;
  }
}
.units-menu-wrap {
  // width: calc(100% - 12px);
  // margin: 0 auto;
  // padding: 0 6px 0 6px;
  // background-color: #fff;
}
.units-menu-box {
  margin-top: 8px;
  background-color: #fff;
  font-size: 16px;
  line-height: 24px;
  color: #000;
  &.grabbing,
  &.grabbing * {
    cursor: grabbing !important;
  }
  &.grabbing .sortable-drag {
    .unit-text,
    .drag-icon-box {
      color: #437dff;
    }
  }
  &.unpublished-style {
    li:first-of-type {
      padding-top: 0;
    }
  }
  li {
    padding: 0 6px 8px 6px;
    cursor: pointer;
    background-color: #e6f1ff;
    &.unit-unpublished {
      color: #8c8c8c;
    }
    &:hover {
      color: #437dff;
    }
  }
  .unit-box {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    background-color: #fff;
    border-radius: 6px;
    font-size: 14px;
    line-height: 22px;
    border: 1px solid transparent;
    &.on {
      border: 1px solid #437dff;
      .unit-text {
        color: #437dff;
      }
    }
  }
  .unit-text {
    flex: 1 1 auto;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    .text {
      display: block;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .unit-icon {
    font-size: 14px;
    margin-left: 10px;
    color: #36b37e;
  }
  .drag-icon-box {
    font-size: 14px;
    margin-left: 10px;
    color: #595959;
    cursor: grab;
    &:hover {
      color: #437dff;
    }
  }
}
.pkg-title-box {
  display: flex;
  align-items: flex-start;
  color: #000;
  padding: 8px 10px;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  &.on,
  &:hover {
    color: #437dff;
    background-color: #e6f1ff;
  }
  .btn-icon {
    display: flex;
    align-items: center;
    min-height: 24px;
    i {
      display: inline-block;
      font-size: 14px;
    }
    &__left {
      color: #bfbfbf;
      margin-right: 8px;
      i {
        font-size: 12px;
      }
    }
    &__right {
      display: inline-block;
      color: #595959;
      padding: 0 5px;
      margin: 0 -5px 0 10px;
      &:hover {
        color: #437dff;
      }
    }
  }
  .pkg-text {
    flex: 1 1 auto;
    &.on,
    &:hover {
      color: #437dff;
    }
  }
}
.none-pkg-box {
  width: 100%;
  .not-found-icon {
    display: block;
    margin: 16px auto 8px auto;
    width: 100px;
    height: 77px;
  }
  .not-found-text {
    font-size: 14px;
    line-height: 22px;
    text-align: center;
    color: rgba(0, 0, 0, 0.85);
    &.pointer {
      cursor: pointer;
    }
  }
}
.act-title-box {
  position: relative;
  color: #000;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  padding: 4px 6px 0 6px;
  &__act-box {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 8px 10px;
    cursor: pointer;
    border-radius: 8px;
    &:hover {
      background-color: #e6f1ff;
      color: #437dff;
    }
    &.on {
      color: #437dff;
      background-color: #e6f1ff;
    }
    .title-text {
      flex: 1 1 auto;
    }
    .btn-add-action {
      display: inline-block;
      padding: 0 5px;
      font-size: 14px;
      color: #595959;
      margin: 0 -5px 0 16px;
      &:hover {
        color: #437dff;
      }
    }
  }
}
.act-nav {
  position: relative;
  z-index: 1;
  height: 100%;
  background-color: #fff;
  .act-nav-box {
    background-color: #fff;
    overflow: hidden;
    .text {
      font-size: 14px;
      padding-left: 6px;
    }
    .right-icon {
      float: right;
      font-size: 16px;
      color: #e8e8e8;
      &.finish {
        color: #36b37e;
      }
    }
    .panel-menu-wrap {
      position: relative;
      background-color: #e6f1ff;
      border-radius: 8px;
      overflow: hidden;
      &:not(:last-of-type) {
        margin-bottom: 4px;
      }
      &.on-pkg ::v-deep .ant-collapse-header {
        // border: 1px solid #0091ff;
        .left-line {
          color: #0091ff;
        }
      }
      .left-line {
        font-size: 16px;
        font-weight: 400;
        border-left: 4px solid rgba(0, 0, 0, 0.09);
        padding-left: 10px;
        line-height: 20px;
        &.publish-line {
          border-left-color: #36b37e;
        }
        &.unpublish-line {
          border-left-color: rgba(0, 0, 0, 0.09);
        }
        &.suspend-line {
          border-left-color: #ff4d4f;
        }
      }
    }
    .menu-wrap {
      position: relative;
      padding: 10px 10px 10px 22px;
      background: rgba(0, 145, 255, 0.05);
      h3 {
        color: #0091ff;
        font-size: 16px;
        font-weight: 400;
      }
    }
    .menu-box {
      margin: 0;

      &.menu-act {
        padding: 0 0 8px 10px;
        li .text {
          max-width: 215px;
        }
      }
      li {
        position: relative;
        line-height: 36px;
        color: #000;
        margin-bottom: 4px;
        background-color: #fff;
        padding: 0 10px 0 10px;
        border-radius: 4px;
        cursor: pointer;
        box-sizing: border-box;

        &.pkg-active {
          border: 1px solid #0091ff;
          color: #0091ff;
        }

        .text {
          display: inline-block;
          max-width: 185px;
          height: 18px;
          line-height: 16px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          vertical-align: middle;
        }
        &:hover {
          background: rgba(0, 145, 255, 0.05);
        }
        &.on {
          color: #0091ff;
        }
        &.lock {
          color: #ccc;
          cursor: not-allowed;
        }
      }
    }
    .header-box {
      position: relative;
      display: flex;
      .field-text {
        padding-right: 6px;
      }
    }
    .action-box {
      display: flex;
      align-items: center;
      .search-box {
        flex: 1 1 auto;
      }
      .btn-add-action {
        flex: none;
        margin-left: 12px;
        display: inline-block;
        color: #0091ff;
        cursor: pointer;
      }
    }
    .tip-box {
      background-color: #fff;
      padding: 0 22px;
      line-height: 37px;
      color: #000;
    }
  }
  .nav-pkg-box {
    overflow: hidden;
    overflow-y: auto;
  }
  .pkg-infos-box {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #00000073;
    padding: 8px 16px 0 16px;
    .length {
      color: #000;
    }
    > span:not(:last-child) {
      margin-right: 20px;
    }
    .link {
      display: inline-block;
      width: 36px;
      height: 36px;
      line-height: 36px;
      cursor: pointer;
      color: #0091ff;
      background: #fafafa;
      border-radius: 100%;
      text-align: center;
      svg {
        width: 16px;
        height: 16px;
        vertical-align: middle;
      }
    }
  }
}
.tooltip-title {
  min-width: 225px;
  max-width: 100%;
  .status-wrap {
    display: flex;
    align-items: center;
    .status-text {
      padding-left: 4px;
    }
  }
}
</style>
