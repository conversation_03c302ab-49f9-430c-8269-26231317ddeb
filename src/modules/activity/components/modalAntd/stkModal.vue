<template>
  <div class="modal-antd">
    <div class="modal-antd-box">
      <a-modal v-bind="modalProps" v-on="modalProps.on">
        <template slot="title">
          {{ $t(isAct ? 'merchant_submit_activity_action' : 'merchant_submit_package_action') }}
        </template>
        <div class="modal-content-box">
          <div v-if="selectArr.length">
            <div v-if="isAct && selectArr.length" class="stk-text-box">
              {{ choosePackagesWithActivity }}
            </div>
            <div v-if="!isAct && !isSubmited2act" class="stk-text-box">
              {{ sureActivityWithPackage }}
            </div>
            <a-form-model-item>
              <div v-if="selectArr.length" class="modal-list-wrap common-flex-column">
                <a-checkbox disabled checked>
                  {{ `${selectArr[0].activity_id}-${selectArr[0].activity_title}` }}
                </a-checkbox>

                <a-checkbox-group v-model="modalData.form.package_id_list" class="common-flex-column">
                  <a-checkbox
                    v-for="(pkg, i) in selectArr"
                    :key="i"
                    v-tooltip="{
                      visible: pkg.uncompleted,
                      content: $t('174942'),
                      placement: 'right',
                      theme: 'dark'
                    }"
                    class="modal-package-item"
                    :disabled="pkg.disabled || pkg.uncompleted"
                    :value="pkg.package_id"
                  >
                    {{ `${pkg.package_id}-${pkg.package_title}` }}
                  </a-checkbox>
                </a-checkbox-group>
              </div>
            </a-form-model-item>
          </div>
          <a-form-model-item>
            <a-textarea
              class="textarea-box"
              v-model="modalData.form.note"
              v-bind="textareaProps"
              v-on="textareaProps.on"
            />
          </a-form-model-item>
        </div>
      </a-modal>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    modalData: {
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      modalProps: {
        destroyOnClose: true,
        visible: false,
        keyFlag: '',
        packageArr: [],
        okText: this.$t('global_confirm'),
        cancelText: this.$t('global_cancel'),
        okButtonProps: {
          props: {
            disabled: false
          }
        },
        on: {
          cancel: () => {
            let { modalData: md } = this
            md.form = {}
            md.visible = false
          },
          ok: () => {}
        }
      },
      textareaProps: {
        maxLength: 500,
        placeholder: this.$t('form.stkTextarea'),
        autoSize: { minRows: 3, maxRows: 7 },
        on: {}
      }
    }
  },
  computed: {
    choosePackagesWithActivity() {
      let row = this.modalData.row
      let str = row.activity_id ? `${row.activity_id}-${row.activity_title}` : ''
      return klook.parseStr1(this.$t('stkModal.choosePackagesWithActivity'), {
        'AID-Name': str
      })
    },
    sureActivityWithPackage() {
      let row = this.modalData.row
      let str = row.package_id ? `${row.package_id}-${row.package_title}` : ''
      return klook.parseStr1(this.$t('stkModal.sureActivityWithPackage'), {
        'PID-Name': str
      })
    },
    isAct() {
      return this.modalData.keyFlag === 'activity'
    },
    isSubmited2act() {
      // approval_status	活动审核状态：0:edit(此时 Editable）；1: pending approval(此时Non-Editable)；2: Rejected（此时 Editable）；3: Approved(此时Non-Editable)
      return [1, 3].includes(this.$store.state.actItemData?.approval_status)
    },
    selectArr() {
      let arr = this.modalData.packageArr.filter((item) => !item.isFlag)
      return arr
    }
  },
  watch: {
    modalData: {
      handler(md) {
        let props = this.modalProps.okButtonProps.props
        if (md.keyFlag === 'activity') {
          let isSubmitPkg = md.packageArr.some((item) => item.isFlag)
          if (isSubmitPkg) {
            props.disabled = false
          } else {
            props.disabled = !(md.form.package_id_list && md.form.package_id_list.length)
          }
        } else {
          // pkg不用锁
          props.disabled = false
        }
        _.merge(this.modalProps, md)
      },
      immediate: true,
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.modal-content-box {
  .common-flex-column {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  .textarea-box {
    margin-top: 20px;
  }
  .ant-form-item {
    margin: 0;
    border-radius: 4px;
  }

  .modal-list-wrap {
    width: 100%;
    background: #fafafa;
    font-size: 14px;
    line-height: 20px;
    color: #000;
    padding: 16px 8px;
    margin: 10px 0 0 0;

    .modal-package-item {
      display: flex;
      margin-left: 24px;
      width: fit-content;

      ::v-deep .ant-checkbox {
        height: fit-content;
        margin-top: 4px;
      }
    }
  }
}
</style>
