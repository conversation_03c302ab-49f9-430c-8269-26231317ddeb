<template>
  <div class="multiple-locations-wrap">
    <DescMarkdownContent
      class="strong-tips"
      placement="right"
      :is-inline="false"
      :desc="schemaConfig && schemaConfig.strongTips"
      :line-clamp="3"
    />
    <div class="multiple-locations-tip">{{ multiple ? $t('174972') : $t('174970') }}</div>

    <ShimAntdTooltip
      v-bind="$attrs"
      :title="$attrs.description"
      :auto-adjust-overflow="false"
      overlay-class-name="common-tooltip-style tooltip-rich-text-padding"
    >
      <a-button
        v-if="btnText"
        class="multiple-locations-btn"
        type="primary"
        :disabled="disabled"
        @click="handleEdit()"
      >
        {{ btnText }}
      </a-button>

      <a-radio-group class="location-list" :value="radioValue">
        <div
          v-for="item of list"
          :key="item.location"
          class="location-item"
          :class="{ 'is-main': item.is_main }"
        >
          <div class="location-item-content">
            <div class="location-item-name">{{ item.customer_location_name }}</div>

            <div class="location-item-address">
              <span class="location-item-label">{{ $t('174973') }}:&nbsp;</span>
              <span class="location-item-value">{{ item.address_desc }}</span>
            </div>

            <span>
              <div v-if="item.path_list && item.path_sep" class="location-item-path">
                <span class="location-item-label">{{ $t('174976') }}&nbsp;</span>
                <span class="location-item-value">{{
                  (item.path_list && item.path_list.join(item.path_sep)) || ''
                }}</span>
              </div>

              <div v-if="item.additional_path && item.path_sep" class="location-item-additional-path">
                <span class="location-item-label">{{ $t('174977') }}&nbsp;</span>
                <span class="location-item-value">{{
                  (item.additional_path && item.additional_path.join(item.path_sep)) || ''
                }}</span>
              </div>
              <!-- 本期不提供 -->
              <!-- <div class="location-item-question-entry" @click="openQuestionUrl">
                {{ $t('174978') }}
              </div> -->
            </span>

            <a-radio
              v-show="list.length > 1"
              :value="item.location"
              :disabled="disabled"
              @click="clickLocationRadio(item)"
            >
              {{ $t('174979') }}
              <a-tooltip v-if="!item.is_main" placement="right" overlay-class-name="common-tooltip-style">
                <template slot="title">{{ $t('174983') }}</template>
                <a-icon type="info-circle" />
              </a-tooltip>
            </a-radio>
          </div>

          <div v-if="!disabled" class="location-item-actions">
            <div class="location-item-action --edit" @click="handleEdit(item)">
              <i class="location-item-action-icon"><a-icon type="edit" /></i>
            </div>
            <a-popconfirm
              :title="$t('js_confirm_delete')"
              :ok-text="$t('global_button_ok')"
              :cancel-text="$t('global_button_cancel')"
              :overlay-style="{ maxWidth: '300px' }"
              placement="topRight"
              @confirm="handleDelete(item)"
            >
              <div class="location-item-action --delete">
                <i class="location-item-action-icon">
                  <svg-icon icon-name="trash" />
                </i>
              </div>
            </a-popconfirm>
          </div>
        </div>
      </a-radio-group>
    </ShimAntdTooltip>

    <multipleLocationsModal
      :visible.sync="modal.visible"
      :multiple="multiple"
      :activate-item="modal.activateItem"
      :list="modal.list"
      :freetext-disabled="freetextDisabled"
      @submit="onSubmit"
    />
  </div>
</template>

<script>
import multipleLocationsModal from './modal/index.vue'
import ShimAntdTooltip from '@activity/components/shimAntdTooltip'
import DescMarkdownContent from '@activity/pages/activityManagement/detailV2/components/descMarkdownContent'

export default {
  name: 'MultipleLocations',
  components: {
    multipleLocationsModal,
    ShimAntdTooltip,
    DescMarkdownContent
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [Array, Object],
      default: () => []
    },
    schemaConfig: {
      type: Object,
      required: true
    },
    required: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    freetextDisabled: {
      type: Boolean,
      default: false
    },
    language: {
      type: String,
      default: undefined
    }
  },
  data() {
    return {
      modal: {
        visible: false,
        activateItem: undefined,
        list: []
      },
      list: []
    }
  },
  computed: {
    multiple() {
      return !!this.schemaConfig.multiple_selection
    },
    radioValue() {
      for (let item of this.list) {
        if (item.is_main) {
          return item.location
        }
      }

      return undefined
    },
    btnText() {
      if (this.multiple) {
        return this.$t('200593')
      }

      return this.$t('200593')
    }
  },
  beforeMount() {
    this.list = (this.value || [])?.map?.((item) => this.fmtItemData(item)) || []
  },
  methods: {
    fmtItemData(item) {
      if (
        !hasOwnProperty.call(item, 'customer_location_name') ||
        (!item.customer_location_name && item.location_name) // 后端字段 customer_location_name 可能是空字符串
      ) {
        item.customer_location_name = item.location_name
      }

      return item
    },
    clickLocationRadio(data) {
      const list = this.list.map((item) => {
        item.is_main = item.location === data.location

        return item
      })
      this.$set(this, 'list', list)
    },
    openQuestionUrl() {
      const link = 'https://klook.com'
      window.open(link, '_blank')
    },
    handleEdit(item) {
      this.modal.visible = true
      this.modal.activateItem = item || {}
      this.modal.list = this.list
    },
    handleDelete(data) {
      const index = this.list.findIndex((item) => item.location === data.location)
      const delItem = this.list.splice(index, 1)

      if (delItem[0].is_main && this.list.length) {
        Object.assign(this.list[0], { is_main: true })
      }

      this.$emit('change', this.list)
    },
    onSubmit(list) {
      this.$set(this, 'list', list)
      this.$emit('change', this.list)
      this.$emit('changeData')
    }
  }
}
</script>

<style lang="scss" scoped>
.multiple-locations-wrap {
  .multiple-locations-tip {
    font-size: 12px;
    line-height: 15px;
    color: rgba(0, 0, 0, 0.65);
  }

  .multiple-locations-btn {
    margin-top: 8px;
  }

  .location-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 4px;
  }

  .location-item {
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border-radius: 4px;
    font-size: 14px;
    line-height: 22px;
    background-color: #fafafa;
    border: 1px solid #fafafa;

    ::v-deep .ant-radio-wrapper {
      width: fit-content;
      font-weight: 500;
    }

    &:hover {
      border-color: #d6d6d6;
    }

    &.is-main {
      background-color: #fff2e8;
      &:hover {
        border-color: #ffd2af;
      }
    }

    &-content {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    &-name {
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }

    &-additional-path {
      color: rgba(0, 0, 0, 0.45);

      .location-item-value {
        color: rgba(0, 0, 0, 0.65);
        background-color: rgba(0, 0, 0, 0.04);
      }
    }

    &-question-entry {
      margin-top: 8px;
      font-weight: 500;
      color: #0091ff;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }

    &-actions {
      display: flex;
      gap: 2px;
      color: #fff;
    }

    &-action {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      border-radius: 16px;
      border: none;
      transition: all 0.3s;
      cursor: pointer;

      &.--edit {
        &:hover {
          background-color: rgba(0, 145, 255, 0.1);
        }
        .location-item-action-icon {
          background-color: #0091ff;
        }
      }

      &.--delete {
        &:hover {
          background-color: rgba(255, 171, 0, 0.1);
        }
        .location-item-action-icon {
          background-color: #ffab00;
        }
      }

      &-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        border-radius: 16px;
        font-size: 10px;
      }
    }
  }
}
</style>
