<template>
  <a-modal
    v-model="_visible"
    class="multiple-location-modal"
    :width="980"
    :mask-closable="false"
    :keyboard="false"
    :title="multiple ? $t('174994') : $t('174995')"
    @cancel="onCancel"
  >
    <a-spin :spinning="loading">
      <div class="multiple-location-modal-wrap">
        <div class="location-search">
          <div class="common-bold-title">{{ $t('174996') }}</div>
          <MultipleLocationsMap
            :key="_visible"
            ref="multipleLocationsMap"
            v-model="currActivateItem"
            :loading.sync="loading"
          />
        </div>
        <div class="location-content">
          <div class="location-content-header">
            <span class="common-bold-title">Activity location(s)</span>
            <span class="location-content-sub-header">{{ multiple ? $t('174997') : $t('174998') }}</span>
          </div>

          <div class="location-content-box" :class="{ 'is-empty': !currList.length }">
            <ModalLocationList
              v-if="currList.length"
              :list.sync="currList"
              :activate-item="currActivateItem"
              :multiple="multiple"
              :freetext-disabled="freetextDisabled"
              @clickItem="onClickItem"
              @deleteItem="onDeleteItem"
            />
            <div v-else class="location-content-empty">
              <a-empty>
                <svg-icon slot="image" class="location-content-empty-image" icon-name="empty" />
                <span slot="description">{{ $t('175006') }}</span>
              </a-empty>
              <span class="location-content-empty-tip"><a-icon type="arrow-left" />{{ $t('175007') }}</span>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
    <template slot="footer">
      <a-button key="back" @click="onCancel">{{ $t('global_cancel') }}</a-button>
      <a-button key="submit" type="primary" :disabled="!currList.length" @click="onSubmit"> Submit </a-button>
    </template>
  </a-modal>
</template>

<script>
import { pmsConfirm } from '@activity/utils/index'
import ModalLocationList from './list'
import MultipleLocationsMap from '@activity/components/multiple-locations/modal/map.vue'

export default {
  name: 'MultipleLocationsModal',
  components: {
    ModalLocationList,
    MultipleLocationsMap
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => []
    },
    activateItem: {
      type: Object,
      default: () => ({})
    },
    multiple: {
      type: Boolean,
      default: false
    },
    freetextDisabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      currList: [],
      currActivateItem: {}
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(visible) {
        this.$emit('update:visible', visible)
      }
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(visible) {
        if (visible) {
          this.loading = false
          this.initData()
        } else {
          this.resetData()
        }
      }
    },
    currActivateItem: {
      deep: true,
      handler(data, pre) {
        // 新增按钮或者切换 type 是重置为 {}
        if (!data.location || this.currList.find((item) => item.location === data.location)) {
          return
        }

        if (data.location !== pre?.location) {
          if (this.multiple) {
            data.is_main = this.currList.length === 0
            this.$message.success(this.$t('toast_account_added_success'))
            this.currList.push(data)
          } else {
            data.is_main = true
            this.$set(this, 'currList', [data])
          }
        }

        this.scrollToActivateItem()
      }
    }
  },
  beforeDestroy() {
    this.timer && clearTimeout(this.timer)
  },
  methods: {
    initData() {
      this.currList = _.cloneDeep(this.list)
      this.cacheList = _.cloneDeep(this.list)
      this.currActivateItem = this.activateItem
      this.scrollToActivateItem()
    },
    onSubmit() {
      this.$emit('submit', this.currList)
      this._visible = false
    },
    async onCancel() {
      if (_.isEqual(this.cacheList, this.currList)) {
        this._visible = false
        return
      }

      this.$nextTick(() => {
        this._visible = true
      })

      const flag = await pmsConfirm.call(this, {
        title: this.$t('175008'),
        content: this.$t('175009'),
        okText: this.$t('175010'),
        cancelText: this.$t('global_cancel')
      })

      if (!flag) return

      this._visible = false
    },
    scrollToActivateItem() {
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.$nextTick(() => {
          document.querySelector('.location-item.is-activate')?.scrollIntoView?.({
            behavior: 'smooth'
          })
        })
      }, 300)
    },
    onClickItem(data) {
      this.currActivateItem = data

      this.$refs.multipleLocationsMap?.setCurrMapType?.(data)
      this.$refs.multipleLocationsMap?.setMapMasker?.(data)
    },
    onDeleteItem(data) {
      const index = this.currList.findIndex((item) => item.location === data.location)
      const delItem = this.currList.splice(index, 1)

      if (this.currActivateItem?.location === delItem[0].location) {
        this.$refs.multipleLocationsMap?.resetData?.()
        this.currActivateItem = {}
      }

      this.$message.success(this.$t('global_delete_success'))
      if (delItem[0].is_main && this.currList.length) {
        Object.assign(this.currList[0], { is_main: true })
      }
    },
    resetData() {
      this.currList = []
      this.$refs.multipleLocationsMap?.resetData?.()
    }
  }
}
</script>

<style lang="scss">
.multiple-location-modal-wrap {
  .gm-style-iw-chr {
    position: absolute;
    right: 0;
  }

  .gm-style-iw-d {
    padding: 12px 24px 0 0;

    .title {
      font-weight: 500;
      line-height: 24px;
      font-size: 14px;
    }
  }
}
</style>

<style lang="scss" scoped>
.multiple-location-modal-wrap {
  display: flex;
  gap: 8px;

  .common-bold-title {
    font-weight: 700;
    line-height: 22px;
    color: #000;
  }

  .location-search {
    flex: 0 0 540px;
    width: 540px;
    padding-right: 8px;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
  }

  .multiple-location-map-wrap {
    margin-top: 12px;
  }

  .location-content {
    flex: 0 0 400px;
    width: 400px;

    &-sub-header {
      margin-left: 4px;
      color: rgba(0, 0, 0, 0.45);
    }

    &-box {
      display: flex;
      height: 434px;
      margin: 12px 0 0;

      &.is-empty {
        align-items: center;
        justify-content: center;
      }
    }
  }

  .location-content-empty {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);

    ::v-deep .ant-empty-image {
      height: 88px;
      margin-bottom: 0;
    }

    &-image {
      font-size: 88px;
    }

    &-tip {
      display: flex;
      align-items: center;
      gap: 4px;
      justify-content: center;
      margin-top: 36px;
      font-size: 16px;
      font-weight: 600;

      ::v-deep .anticon {
        font-size: 14px;
      }
    }
  }
}
</style>
