<template>
  <a-radio-group class="location-list" :value="radioValue">
    <div
      v-for="(item, index) of list"
      :key="item.location + '_' + index"
      class="location-item"
      :class="{ 'is-activate': activateItem && item.location === activateItem.location }"
      @click="clickItem(item)"
    >
      <div class="location-item-box">
        <div class="location-item-title">
          <template v-if="item.klook_place_id">
            <span class="location-item-title-name">{{ item.customer_location_name }}</span>
            <span>&nbsp;(POI ID: {{ item.klook_place_id }})</span>
          </template>
          <a-input v-else v-model="item.customer_location_name" :disabled="freetextDisabled" @click.stop />
        </div>

        <div class="location-item-detail">
          <div class="location-item-detail-desc">{{ item.address_desc }}</div>
          <div class="location-item-detail-path">
            {{ (item.path_list && item.path_list.join(item.path_sep)) || '' }}
          </div>
        </div>

        <a-radio v-if="list.length > 1" :value="item.location" @click="clickLocationRadio(item)">
          {{ $t('174979') }}
          <a-tooltip v-if="!item.is_mai" placement="right" overlay-class-name="common-tooltip-style">
            <template slot="title">{{ $t('174983') }}</template>
            <a-icon type="info-circle" />
          </a-tooltip>
        </a-radio>
      </div>

      <span class="location-item-delete" @click.stop.prevent="$emit('deleteItem', item)">
        <a-icon type="close" />
      </span>
    </div>
  </a-radio-group>
</template>

<script>
export default {
  name: 'ModalLocationList',
  props: {
    list: {
      type: Array,
      default: () => []
    },
    activateItem: {
      type: Object,
      default: () => ({})
    },
    multiple: {
      type: Boolean,
      default: false
    },
    freetextDisabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    radioValue() {
      for (let item of this.list) {
        if (item.is_main) {
          return item.location
        }
      }

      return undefined
    }
  },
  methods: {
    clickItem(item) {
      if (item.location === this.activateItem.location) {
        return
      }

      this.$emit('clickItem', item)
    },
    clickLocationRadio(data) {
      const list = this.list.map((item) => {
        item.is_main = item.location === data.location

        return item
      })
      this.$emit('update:list', list)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../../../src/assets/css/lib/mixins';

@function opacityBlack($opacity) {
  @return rgba(0, 0, 0, calc($opacity / 100));
}

.location-list {
  width: 400px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-right: 2px;
  overflow: hidden auto;

  @include mixin-hover-display-scrollbar;
}

.location-item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 12px;
  border-radius: 4px;
  color: opacityBlack(85);
  background-color: #fafafa;
  border: 1px solid #fafafa;
  cursor: pointer;

  &:hover {
    border-color: opacityBlack(15);
  }

  &.is-activate {
    border-color: #437dff;
    background-color: #f0f5ff;
  }

  ::v-deep .ant-radio-wrapper {
    width: fit-content;
    font-weight: 500;
  }

  ::v-deep .ant-input {
    box-shadow: none;
    &:hover,
    &:focus {
      border-color: #d9d9d9;
    }
  }

  &-box {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 8px;
  }

  &-title {
    &-name {
      font-weight: 500;
    }
  }

  &-detail {
    font-size: 12px;
    color: opacityBlack(45);
  }

  &-delete {
    width: 24px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    border-radius: 50%;
    color: opacityBlack(45);
    cursor: pointer;
    &:hover {
      background-color: rgb(255, 77, 79, 0.1);
      color: #ff4d4f;
    }
  }
}
</style>
