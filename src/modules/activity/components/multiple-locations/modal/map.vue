<template>
  <div class="multiple-location-map-wrap">
    <a-spin :spinning="spinning">
      <KlkAntAreaMap
        ref="map"
        class="map-content"
        :language="language"
        @changeType="changeType"
        @initedMap="onInitedMap"
      >
        <a-select
          slot="searchInput"
          class="map-content-select"
          show-search
          :value="value"
          :placeholder="placeholder"
          :filter-option="false"
          @search="onSearch"
          @change="onChange"
        >
          <template v-if="multiGroup">
            <a-select-opt-group
              v-for="(options, key) of groupOptions"
              :key="key"
              :label="groupLabelDict[key]"
            >
              <a-select-option v-for="option in options" :key="option.key">
                <OverflowTextTooltip direction="vertical" placement="right" :line-clamp="1">
                  <strong>{{ option.customer_location_name }}</strong> {{ option.address_desc }}
                </OverflowTextTooltip>
              </a-select-option>
            </a-select-opt-group>
          </template>

          <a-select-option v-for="option in flatGroupOptions" v-else :key="option.key" :value="option.key">
            <OverflowTextTooltip direction="vertical" placement="right" :line-clamp="1">
              <strong>{{ option.customer_location_name }}</strong> {{ option.address_desc }}
            </OverflowTextTooltip>
          </a-select-option>
        </a-select>
      </KlkAntAreaMap>

      <MapPositionDetails class="map-details" :details="positionDetails" :type="currMapType" />
    </a-spin>
  </div>
</template>

<script>
import { KlkAntAreaMap } from '@klook/admin-ui'
import MapPositionDetails from '@activity/components/tpl_activity_map/position_details.vue'
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'
import { getFlang2Blang } from '@activity/utils'

const groupLabelDict = {
  priority: __('102291'),
  other: __('ob_others')
}

const typeDict = {
  google: 'google_maps',
  amap: 'amap'
}

const initedMap = {
  google: false,
  amap: false
}

export default {
  name: 'MultipleLocationsMap',
  components: {
    KlkAntAreaMap,
    MapPositionDetails,
    OverflowTextTooltip
  },
  model: {
    prop: 'data',
    event: 'change'
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    placeholder: {
      type: String,
      default: 'Please enter an address to find the coordinates'
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isInited: false,
      groupLabelDict,
      value: undefined,
      spinning: false,
      currMapType: typeDict.google,
      groupOptions: {
        priority: [],
        other: []
      },
      // { success: boolean, location: string, place_id: string, gmap_preview_url: string, place_id_url: string, add_missing_url: string }
      positionDetails: {
        success: false
      }
    }
  },
  computed: {
    multiGroup() {
      return this.groupOptions.priority.length && this.groupOptions.other.length
    },
    flatGroupOptions() {
      return [...this.groupOptions.priority, ...this.groupOptions.other]
    },
    language() {
      const lang = this.$route.query.lang || 'en_US'

      return getFlang2Blang(lang)
    }
  },
  watch: {
    // 触发: 列表点击、location_name 修改、选新 location
    data: {
      deep: true,
      immediate: true,
      async handler(data, pre) {
        if (!data?.location) return

        const option = {
          customer_location_name: data.location_name, // 保底
          ...data,
          key: `${data.place_source}_${data.place_id}`
        }

        if (pre?.location === undefined || pre.location !== data?.location) {
          // 数据来源，1 klook poi、2 google cache poi、3 google autocomplete 4、google detail 5、高德
          await this.setCurrMapType(data)
          this.value = option.key
          this.setMapDetails(data)
          this.setMapMasker(data)
        }

        this.$set(this, 'groupOptions', { priority: [option], other: [] })
      }
    }
  },
  methods: {
    onSearch: _.debounce(async function (input) {
      input = input.trim()
      this.currSearchInput = input

      if (!input) {
        this.$set(this, 'groupOptions', this.$options.data().groupOptions)
        return
      }

      const { suggestions } = await ajax.get(ADMIN_API.act.auto_complete, {
        params: {
          input,
          map_type: this.currMapType === typeDict.amap ? 1 : 0 // 0为google 1为高德，默认google
        }
      })

      if (this.currSearchInput !== input) {
        return
      }

      // place_source: 1 klook poi / 2 google cache poi / 3 google
      const groupOptions = (suggestions || []).reduce(
        (acc, curr) => {
          const key = curr.group_type === 'other' ? 'other' : 'priority'

          acc[key].push({
            ...curr,
            customer_location_name: hasOwnProperty.call(curr, 'customer_location_name')
              ? curr.customer_location_name
              : curr.location_name,
            key: `${curr.place_source}_${curr.place_id}`
          })

          return acc
        },
        {
          priority: [],
          other: []
        }
      )
      this.$set(this, 'groupOptions', groupOptions)
    }, 100),
    onChange(key) {
      this.value = key
      const option = this.flatGroupOptions.find((option) => option.key === key)
      option && this.changeOption(option)
    },
    async changeOption(option) {
      this.$emit('update:loading', true)
      const response = await ajax.get(ADMIN_API.act.place_detail, {
        params: {
          place_id: option.place_id,
          place_source: option.place_source
        }
      })

      const data = response.detail_data
      if (data) {
        data.customer_location_name = data.location_name
        this.setMapMasker(data)
        this.$emit('change', {
          ...data,
          place_source: option.place_source
        })
      }
      this.$emit('update:loading', false)
    },
    interceptor(waitInited = false) {
      return new Promise((resolve) => {
        if (waitInited && this.isInited) {
          return resolve()
        }

        this.$emit('update:loading', true)
        this.interceptorResolve = resolve
      })
    },
    setMapMasker: _.debounce(async function (data) {
      if (
        (this.currMapType === typeDict.google && !initedMap.google) ||
        (this.currMapType === typeDict.amap && !initedMap.amap)
      ) {
        await this.interceptor()
      }
      const mapRef = this.$refs?.map
      const location = this.fmtLatLng(data.location)
      mapRef?.setMarker?.(location)
      mapRef?.updateInfoWindow?.({
        name: data.customer_location_name,
        formatted_address: data.address_desc
      })
    }, 150),
    resetMap() {
      this.value = undefined
      this.$refs?.map?.resetMap()
      this.$set(this, 'positionDetails', { success: false })
    },
    resetData() {
      this.resetMap()
      this.$set(this, 'groupOptions', this.$options.data().groupOptions)
    },
    fmtLatLng(location) {
      if (typeof location === 'string') {
        const [lat, lng] = location.split(',')
        return {
          lat: +lat,
          lng: +lng
        }
      }

      return location
    },
    setMapDetails(data) {
      const { location, place_id } = data
      const place_id_url = 'https://developers.google.com/places/place-id'
      const add_missing_url = 'https://support.google.com/maps/answer/6320846'
      const gmap_preview_url = `https://www.google.com/maps/search/?api=1&query=${location}&query_place_id=${place_id}`

      this.$set(this, 'positionDetails', {
        location,
        place_id,
        gmap_preview_url,
        place_id_url,
        add_missing_url,
        success: true
      })
    },
    changeType(type) {
      this.currMapType = type
      this.$emit('changeType', type)
      this.$emit('change', {})
      this.resetData()
    },
    onInitedMap(type) {
      this.isInited = true
      Object.assign(initedMap, { [type]: true })
      if (this.interceptorResolve) {
        this.interceptorResolve()
        this.interceptorResolve = null
        this.$emit('update:loading', false)
      }
    },
    async setCurrMapType(data) {
      this.currMapType = data.place_source === 5 ? typeDict.amap : typeDict.google
      await this.interceptor(true) // 等待地图初始化完成
      this.$refs.map?.switchMapType?.(this.currMapType)
    }
  }
}
</script>

<style lang="scss" scoped>
.multiple-location-map-wrap {
  .map-content {
    height: 326px;

    &-select {
      width: 365px;
      margin-bottom: 4px;
    }
  }

  .map-details {
    margin-top: 4px;
  }
}
</style>
