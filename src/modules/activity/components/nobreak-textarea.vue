<template>
  <a-form-model ref="form" :model="form">
    <a-form-model-item prop="value" :rules="rules">
      <a-input
        v-model="currentValue"
        v-bind="$props"
        :max-length="maxLength"
        type="textarea"
        :disabled="disabled"
        @change="handleChange"
        @blur="handleBlur"
      />
      <div class="count-limit">{{ suffix }}</div>
      <a-alert v-if="warningText" :message="warningText" type="error" />
    </a-form-model-item>
  </a-form-model>
</template>

<script>
import { getWordCountLimit, getSuffixCount, getWordCountLimitDisabled } from '@activity/utils'
import { getMatchedHtmlTags } from './utils'

export default {
  name: 'NobreakTextarea',
  props: {
    value: {
      required: true,
      type: String
    },
    disabled: {
      type: Boolean,
      default: false
    },
    switch: {
      type: Boolean,
      default: true
    },
    wordCountLimit: {
      type: Array,
      default: () => []
    },
    isDefineMarkdown: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      currentValue: '',
      matchedHtmlTags: []
    }
  },
  computed: {
    rules() {
      const { maxLength, disabled, currentValue } = this
      const message = this.$t('83853', { num: maxLength })
      return {
        validator(r, v, callback) {
          const val = currentValue.trim()
          if (val.length > maxLength && getWordCountLimitDisabled(disabled)) {
            return callback(new Error(message))
          }
          // // 禁止markdown输入
          // if (isDefineMarkdown) {
          //   const matchedTags = getMatchedHtmlTags(val)
          //   if (matchedTags.length) {
          //     const msg = `The input content contains ${matchedTags
          //       ?.map((ele) => `<${ele}>`)
          //       ?.join(',')} tags, please do not do that.`
          //     return callback(new Error(msg))
          //   }
          // }
          callback()
        },
        trigger: 'change'
      }
    },
    form() {
      return { value: this.currentValue }
    },
    suffix() {
      const { maxLength, currentValue } = this
      return getSuffixCount(maxLength, currentValue.length)
    },
    maxLength() {
      return getWordCountLimit(this.wordCountLimit)
    },
    warningText() {
      const { matchedHtmlTags } = this
      return matchedHtmlTags.length
        ? `The input content contains ${matchedHtmlTags
            ?.map((ele) => `<${ele}>`)
            ?.join(',')} tags, please do not do that.`
        : ''
    }
    // currentValue: {
    //   get() {
    //     return this.switch ? this.value.replace(/\n/g, '') : this.value
    //   },
    //   set(v) {
    //     this.$emit('input', this.switch ? v.replace(/\n/g, '') : v)
    //   }
    // }
  },
  watch: {
    value: {
      immediate: true,
      handler(v) {
        const value = this.switch ? v.replace(/\n/g, '') : v
        this.currentValue = value
        this.handleCheckInputMarkdown(value)
      }
    }
  },
  methods: {
    handleBlur() {
      this.currentValue = this.currentValue.trim()
      // this.currentValue = this.currentValue.replace(/^\s+/gm, '')
    },
    handleChange() {
      const currentValue = this.currentValue
      const val = this.switch ? currentValue.replace(/\n/g, '') : currentValue
      this.$emit('input', val.trim())
    },
    validateForm() {
      return this.$refs.form.validate().catch(() => false)
    },
    handleCheckInputMarkdown(text) {
      const matchedTags = getMatchedHtmlTags(text) || []
      this.matchedHtmlTags = matchedTags?.length ? matchedTags : []
    }
  }
}
</script>
<style lang="scss" scoped>
.count-limit {
  text-align: right;
  height: 24px;
  line-height: 24px;
  color: rgba(0, 0, 0, 0.45);
}
</style>
