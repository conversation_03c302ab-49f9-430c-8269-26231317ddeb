<template>
  <div class="select-group">
    <a-select
      v-model="selectValue"
      :options="compOptions"
      class="select"
      :placeholder="$t('global_select')"
      :disabled="disabled"
    />
    <a-select
      v-model="unit"
      :options="compUnitOptions"
      class="select"
      :placeholder="$t('global_select')"
      :disabled="disabled"
    />
  </div>
</template>

<script>
export default {
  name: 'NumberUnitSelector',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    unitOptions: {
      type: Array,
      default: () => []
    },
    options: {
      type: Array,
      default: () => []
    },
    valueFiled: {
      type: String,
      default: 'time_num'
    },
    unitFiled: {
      type: String,
      default: 'time_unit'
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    selectValue: {
      get() {
        return _.get(this.value, this.valueFiled)
      },
      set(v) {
        const data = {
          [this.valueFiled]: v,
          [this.unitFiled]: this.unit
        }
        this.$emit('change', data)
      }
    },
    unit: {
      get() {
        return _.get(this.value, this.unitFiled)
      },
      set(v) {
        const data = {
          [this.valueFiled]: this.selectValue,
          [this.unitFiled]: v
        }
        this.$emit('change', data)
      }
    },
    compOptions() {
      const options = this.options
      if (Array.isArray(options) && options.length) {
        return options
      }
      return this.getDefaultOptions()
    },
    compUnitOptions() {
      const unitOptions = this.unitOptions
      if (Array.isArray(unitOptions) && unitOptions.length) {
        return unitOptions
      }
      return [
        {
          label: 'day(s)',
          value: 'days'
        },
        {
          label: 'hour(s)',
          value: 'hours'
        }
      ]
    }
  },
  methods: {
    getDefaultOptions(max = 100, min = 1, step = 1) {
      const list = []
      for (let i = min; i <= max; i += step) {
        list.push({
          label: i,
          value: i
        })
      }
      return list
    }
  }
}
</script>

<style lang="scss" scoped>
.select-group {
  display: flex;
  align-items: center;

  .select {
    width: 120px;
    margin-right: 16px;

    &:nth-last-child(1) {
      margin-right: 0;
    }
  }
}
</style>
