<template>
  <KlkAdminMarkdown
    ref="markdown"
    v-model="currValue"
    :config="mdeConfigs"
    :disabled="disabled"
    :custom-actions="customActions"
    :is-support-new-markdown="isSupportNewMarkdown"
    v-bind="$attrs"
    v-on="$listeners"
  />
</template>

<script>
import { mapGetters } from 'vuex'
import { KlkAdminMarkdown } from '@klook/admin-ui'
import { common_defaults } from '@activity/components/simple-md/config.js'
import { addPhoneCall } from '@activity/components/simple-md/utils/index.js'

export default {
  name: 'OBTMarkdown',
  components: {
    KlkAdminMarkdown
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: String,
      required: true,
      default: ''
    },
    // obt config
    config: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters(['markdownSupportConfigGetter']),
    currValue: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('change', value)
      }
    },
    isSupportNewMarkdown() {
      return this.markdownSupportConfigGetter?.support_new_markdown ?? false
    },
    markdownSupportConf() {
      const markdownSupportConf = (this.config.extension_ability || []).find(
        (item) => item.key === 'markdown_support'
      )

      return markdownSupportConf?.value ?? {}
    },
    customActions() {
      const format = [...(this.markdownSupportConf?.format ?? [])]
      if (format.includes('phone')) {
        return [addPhoneCall]
      }

      return []
    },
    mdeConfigs() {
      if (this.isSupportNewMarkdown) {
        return this.config
      }

      return {
        ...common_defaults,
        ...this.config
      }
    }
  },
  mounted() {
    klook.bus.$on('markdownCodemirrorRefresh', this.refreshMarkdownState)
  },
  beforeDestroy() {
    klook.bus.$off('markdownCodemirrorRefresh', this.refreshMarkdownState)
  },
  methods: {
    validator() {
      // disabled状态下不校验
      if (this.disabled) {
        return true
      }
      return this.$refs.markdown.validator()
    },
    refreshMarkdownState() {
      this.$refs.markdown?.$refs?.markdown?.markdownCodemirrorRefresh?.()
    }
  }
}
</script>
