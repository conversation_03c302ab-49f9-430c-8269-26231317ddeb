<template>
  <a-form-model ref="form" :model="form" :label-col="labelCol" :wrapper-col="wrapperCol" :rules="rules">
    <a-form-model-item :label="$t('act_copy_direction')" prop="type" required>
      <a-radio-group v-model="form.type" @change="handleTypeChange">
        <a-radio v-if="showTo" :value="1">{{ $t('act_copy_to_other_pkg') }}</a-radio>
        <a-radio v-if="showFrom" :value="2">{{ $t('act_copy_from_another_pkg') }}</a-radio>
      </a-radio-group>
    </a-form-model-item>
    <a-form-model-item :label="$t('act_select_to_from')" prop="value" required>
      <a-select
        v-model="form.value"
        :mode="selectMode"
        show-search
        :filter-option="filterOption"
        :placeholder="$t('global_select')"
        option-filter-prop="children"
        :max-tag-count="5"
        class="select"
      >
        <a-select-option v-for="item in options" :key="item.key" :value="item.key" :label="item.value">
          {{ item.package_id }} - {{ item.value }}
        </a-select-option>
      </a-select>
    </a-form-model-item>
    <a-form-model-item
      v-if="showSection && sectionOptions && sectionOptions.length"
      :label="$t('121725')"
      prop="section"
      required
    >
      <a-select v-model="form.section" show-search :placeholder="$t('global_select')" class="select">
        <a-select-option v-for="item in sectionOptions" :key="item.value" :value="item.value">
          {{ item.label }}
        </a-select-option>
      </a-select>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  name: 'PackageCopyForm',
  props: {
    options: {
      type: Array,
      default: () => []
    },
    sectionOptions: {
      type: Array,
      default: () => []
    },
    showTo: {
      type: Boolean,
      default: true
    },
    showFrom: {
      type: Boolean,
      default: true
    },
    defaultValue: {
      type: Number,
      default: -1
    },
    maxLength: {
      type: Number,
      default: 20
    },
    showSection: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validateFun = (r, v, callback) => {
      if (!v) {
        return callback(new Error(this.$t('global_select')))
      }
      if (Array.isArray(v) && !v.length) {
        return callback(new Error(this.$t('global_select')))
      }
      callback()
    }
    const validateValueFun = (r, v, callback) => {
      if (!v) {
        return callback(new Error(this.$t('global_select')))
      }
      if (Array.isArray(v)) {
        const len = v.length
        if (!len) {
          return callback(new Error(this.$t('global_select')))
        }
        const max = this.maxLength
        if (len > max) {
          return callback(new Error(this.$t('79625', { num: max })))
        }
      }
      callback()
    }
    return {
      form: {
        type: undefined,
        value: undefined,
        section: 'all'
      },
      labelCol: { span: 8 },
      wrapperCol: { span: 14 },
      rules: {
        type: [
          {
            validator: validateFun
          }
        ],
        value: [
          {
            validator: validateValueFun
          }
        ]
      }
    }
  },
  computed: {
    selectMode() {
      return this.form.type === 1 ? 'multiple' : 'default'
    }
  },
  mounted() {
    if (this.defaultValue > 0) {
      this.form.type = this.defaultValue
      this.$emit('change', this.form)
    }

    if (!this.showTo) {
      this.form.type = 2
      this.$emit('change', this.form)
    }

    if (!this.showFrom) {
      this.form.type = 1
      this.$emit('change', this.form)
    }
  },
  methods: {
    validateForm() {
      return this.$refs.form.validate().catch(() => false)
    },
    getData() {
      const sectionOptions = this.sectionOptions
      const { type, value, section } = this.form
      if (sectionOptions && sectionOptions.length) {
        return { type, value, section }
      }
      return { type, value }
    },
    handleTypeChange() {
      this.form.value = undefined
      this.$emit('change', this.form)
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }
  }
}
</script>

<style lang="scss" scoped>
.select {
  width: 300px;
}
</style>
