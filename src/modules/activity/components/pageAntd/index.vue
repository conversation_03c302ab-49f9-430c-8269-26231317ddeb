<script>
export default {
  props: {
    pageData: {
      default: () => {
        return {}
      }
    },
    selectData: {
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pageProps: {},
      defPateProps: {
        pagination: true,
        pageSize: 10
      },
      selectProps: {},
      defSelectData: {
        pageSizeOptions: [10, 25, 50],
        options: [],
        value: 10
      }
    }
  },
  created() {
    let { defSelectData } = this
    defSelectData.options = defSelectData.pageSizeOptions.map((v) => {
      return { value: v, title: v }
    })
  },
  render(h) {
    let {
      pageProps,
      defPateProps,
      pageData,
      selectProps,
      selectData,
      defSelectData
    } = this
    _.merge(pageProps, defPateProps, pageData)
    _.merge(selectProps, defSelectData, selectData)
    let pageNode = h('a-pagination', {
      props: pageProps,
      on: pageProps.on
    })
    let selectNode = h('a-select', {
      props: selectProps,
      on: selectProps.on
    })
    let vNode = (
      <div class="pagination-antd">
        <div class="pagination-antd-box">
          {pageNode}
          <span class="page-per-page">{this.$t('common.perPage')}</span>
          {selectNode}
          <span class="page-pieces">{this.$t('common.pieces')}</span>
        </div>
      </div>
    )
    return vNode
  }
}
</script>
<style lang="scss">
.pagination-antd {
  .ant-select.ant-select-enabled {
    width: 60px;
  }
}
</style>
<style lang="scss" scoped>
.pagination-antd {
  &-box {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
  }
  .page-per-page {
    margin: 0 4px 0 10px;
  }
  .page-pieces {
    margin: 0 calc(29px - 16px) 0 4px;
  }
}
</style>
