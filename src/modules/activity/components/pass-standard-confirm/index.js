import Vue from 'vue'
import PassStandardConfirm from './main.vue'

let instance

const getInstance = (i18n) => {
  if (instance) {
    return instance
  }

  const Ctor = Vue.extend(PassStandardConfirm)
  instance = new Ctor({ i18n }).$mount()
  document.body.appendChild(instance.$el)
  return instance
}

/**
 * 使用方法：checkPass({}, this.$i18n, () => console.log(111111))
 * 注意：这个是同步的，请把需要执行的代码放到 callback 里面传进去，返回值是一个 promise 这个不能变
 */
export const checkPassSync = (data, i18n, callback) => {
  const inst = getInstance(i18n)
  return inst.checkPass(data, callback)
}

/**
 * 使用方法：await checkPass({}, this.$i18n)
 * 注意：这个是异步的，返回值是：是否应该中断
 */
export const checkPassAsync = async (data, i18n, callback = null) => {
  const inst = getInstance(i18n)
  return new Promise((resolve) => {
    inst.checkPass(data, async (obj) => {
      // 这里要插入一些执行的东西并且让check pass弹窗显示loading
      if (callback) {
        await callback(obj)
      }

      resolve(obj)
    })
  })
}
