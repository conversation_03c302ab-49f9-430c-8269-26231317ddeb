<template>
  <a-modal
    v-model="visible"
    :title="$t('83297')"
    :width="580"
    :mask-closable="true"
    :cancel-text="$t('73080')"
    :ok-text="$t('83309')"
    :confirm-loading="loading"
    :ok-button-props="{
      props: {
        disabled: !finalText
      }
    }"
    class="pass-standard-confirm"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-alert v-if="warningMessage" type="warning" banner>
      <div slot="message" v-html="warningMessage"></div>
    </a-alert>

    <p class="pass-standard-confirm-title">{{ $t('83301') }}</p>

    <a-radio-group v-model="selectedReason" @change="handleChange">
      <a-radio
        v-for="reason in reasonList"
        :value="reason"
        :key="reason.text"
        class="pass-standard-confirm-radio"
      >
        <span class="pass-standard-confirm-item">
          {{ reason.text }}
        </span>
      </a-radio>
    </a-radio-group>

    <a-textarea
      v-if="showTextarea"
      v-model="otherReason"
      :placeholder="$t('83308')"
      :rows="2"
      style="margin-top: 12px;"
    />
  </a-modal>
</template>
<script>
export default {
  name: 'PassStandardConfirm',
  data() {
    return {
      loading: false,
      visible: false,
      selectedReason: null,
      otherReason: '',
      callback: null,
      reasonList: [],
      warningMessage: ''
    }
  },
  computed: {
    showTextarea() {
      return this.selectedReason && this.selectedReason.style === 'InputText'
    },
    finalText() {
      if (this.showTextarea) {
        return this.otherReason.trim()
      }
      return this.selectedReason ? this.selectedReason.text : ''
    },
    reasonCode() {
      return this.selectedReason ? Number(this.selectedReason.reason_code) : undefined
    }
  },
  methods: {
    handleChange() {
      this.otherReason = ''
    },
    clean() {
      this.selectedReason = null
      this.otherReason = ''
    },
    async continue(force = true) {
      const { finalText, reasonCode } = this
      if (this.callback) {
        await this.callback({ stop: false, reason: finalText, reasonCode, force })
      }
    },
    async abort() {
      if (this.callback) {
        await this.callback({ stop: true })
      }
    },
    async checkPass(data, callback) {
      this.callback = callback
      const reqData = await this.checkPassUnpublishStatus(data)
      // const reqData = { success: true, data }
      if (reqData && reqData.success && reqData.result && reqData.result.need_check_double) {
        this.reasonList = reqData.result.reason_options || []
        this.warningMessage = reqData.result.reason || ''
        this.visible = true
      } else {
        this.continue(false)
      }
    },
    checkPassUnpublishStatus(data) {
      // 这三个 api 都不能分开，因为需要鉴权校验
      const { activity_id, package_ids, sku_ids } = data || {}

      if (activity_id) {
        return ajax.getBody(ADMIN_API.act.check_activity_operation, {
          params: {
            activity_id,
            operate_type: 2
          }
        })
      }

      if (package_ids) {
        return ajax.getBody(ADMIN_API.act.check_package_operation, {
          params: {
            package_ids,
            operate_type: 2
          }
        })
      }

      if (sku_ids) {
        return ajax.getBody(ADMIN_API.act.check_sku_operation, {
          params: {
            sku_ids,
            operate_type: 2
          }
        })
      }

      throw new Error('checkPassUnpublishStatus 参数错误！')
    },
    async handleOk() {
      this.loading = true
      await this.continue()
      this.visible = false
      this.loading = false
      this.clean()
    },
    async handleCancel() {
      this.visible = false
      await this.abort()
      this.clean()
    }
  }
}
</script>
<style lang="scss" scoped>
.pass-standard-confirm {
  ::v-deep .ant-radio-wrapper {
    margin-top: 12px;

    &:first-child {
      margin-top: 0;
    }
  }

  &-title {
    margin-top: 12px;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #000000;
  }

  &-radio {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  &-item {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.85);
    white-space: normal;
  }
}
</style>
