// 图片地址
const imageBaseUrl = 'https://res.klook.com/image/upload/'
// 文件夹
const folder = 'activities/'
// 图片地址前缀
const imageUrlPrefix = imageBaseUrl + folder
// 预览
const getPreviewUrl = (ratio) => {
  if (ratio) {
    return `q_80,c_fill,ar_${ratio},w_400/`
  } else {
    return 'q_80,c_fill,w_400/'
  }
}
// 比例
const desktopRatio = '16:9'
const mobileRatio = '4:3'
// blur
const blur_pre_process = (name, ratio, s) => `u_activities:${name},${s}_1.0,ar_${ratio},c_scale,e_blur:10000/`
// cloudinary 配置
// doc https://cloudinary.com/documentation/upload_widget_reference
const cloudinaryOptions = (activityId, config = {}) => ({
  // 官方建议改为驼峰写法
  showPoweredBy: false,
  showUploadMoreButton: false,
  resourceType: 'image',
  clientAllowedFormats: ['image'],
  cropping: false,
  cloudName: 'klook',
  folder: 'activities',
  multiple: true,
  uploadPreset: 'k8xu3bkg',
  sources: ['local'],
  showAdvancedOptions: false,
  tags: [activityId],
  maxFileSize: 4 * 1024 * 1024, // 4M
  maxImageFileSize: 4 * 1024 * 1024,
  maxVideoFileSize: 50 * 1024 * 1024, // 50M,
  maxImageHeight: Math.pow(10, 4),
  maxImageWidth: 3000,
  validateMaxWidthHeight: true,
  ...config
})

const LARGER_PIC_CONF = {
  maxImageFileSize: 4 * 1024 * 1024,
  maxFileSize: 4 * 1024 * 1024, // 4M
  maxImageHeight: Math.pow(10, 4),
  maxImageWidth: 3000
}

const SUPPORT_LARGER_PIC_CONF = {
  maxImageFileSize: 1024 * 1024 * 50,
  maxFileSize: 1024 * 1024 * 80, // 80M
  maxImageHeight: Math.pow(10, 5),
  maxBannerImageWidth: 3000,
  maxBannerImageHeight: Math.pow(10, 4),
  maxBannerImageFileSize: 1024 * 1024 * 4,
  maxBannerImageFileSizeNumber: 4,
  maxCropSize: Math.pow(10, 8)
}

const HIGH_QUALITY_PIC_CONF = {
  minImageHeight: 720, // 最小高度
  minImageWidth: 1280 // 最小宽度
}

const HIGH_QUALITY_PIC_SIZE = 300 * 1024 // 500kb

const showCloudinaryUploadTips = () => {
  let div = document.createElement('div')
  const html = __('79049')
  div.innerHTML = html
  div.setAttribute('title', html)
  Object.assign(div.style, {
    position: 'fixed',
    top: 'calc((100vh - 700px) / 2)',
    left: 'calc((100vw - 767px) / 2)',
    width: '767px',
    padding: '5px 10px',
    borderRadius: '6px',
    backgroundColor: '#fffbe6',
    fontSize: '13px',
    color: '#212121',
    lineHeight: '16px',
    zIndex: 1001
  })
  document.body.append(div)

  return div
}

const PLATFORM_DICT = {
  DESKTOP: {
    key: 'DESKTOP',
    icon: 'desktop',
    label: __('29633')
  },
  MOBILE: {
    key: 'MOBILE',
    icon: 'mobile',
    label: __('29634')
  }
}

export {
  imageBaseUrl,
  folder,
  getPreviewUrl,
  desktopRatio,
  mobileRatio,
  blur_pre_process,
  cloudinaryOptions,
  imageUrlPrefix,
  LARGER_PIC_CONF,
  SUPPORT_LARGER_PIC_CONF,
  HIGH_QUALITY_PIC_CONF,
  HIGH_QUALITY_PIC_SIZE,
  showCloudinaryUploadTips,
  PLATFORM_DICT
}
