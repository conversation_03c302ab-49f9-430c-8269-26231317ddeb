<template>
  <div class="modal-photo-editor-container">
    <div class="editor-main">
      <header class="header">
        <label class="title">{{ $t('76522') }}</label>
        <!-- <a-switch v-model="displaySuggest" checked-children="Suggest on" un-checked-children="Suggest off" /> -->
      </header>

      <CropperContainer
        ref="cropperContainer"
        :key="refreshNum"
        v-model="value"
        :pre-process="preProcess"
        :ratio="ratio"
        :image-name="imageName"
        :pic-did-cut="picDidCut"
        :display-suggest="displaySuggest"
        :disabled="disabled"
        v-bind="$listeners"
        v-on="$listeners"
        @croppedPreProcess="onCroppedPreProcess"
      />
    </div>

    <div class="preview">
      <header class="header">
        <label class="title">{{ $t('global_preview') }}</label>
      </header>

      <ul class="preview-list">
        <li v-for="item of previewList" :key="item.class" class="preview-item">
          <div class="box" :class="item.class" :style="{ backgroundImage: `url(${previewUrl})` }"></div>
          <footer class="footer">
            <div class="title">{{ item.title }}</div>
            <div class="ratio">{{ item.ratio }}</div>
            <div class="pixel">{{ item.pixel }}</div>
          </footer>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { CropperContainer } from '@activity/components/Cropper/index'
import { imageBaseUrl, getPreviewUrl, folder } from '@activity/components/photo-group/const/index'

export default {
  name: 'ModalPhotoEditor',
  components: { CropperContainer },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    preProcess: {
      type: String,
      default: ''
    },
    ratio: {
      type: String,
      default: ''
    },
    imageName: {
      type: String,
      default: ''
    },
    picDidCut: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      displaySuggest: true,
      previewPreProcess: this.preProcess,
      refreshNum: 0
    }
  },
  computed: {
    previewUrl() {
      return imageBaseUrl + this.previewPreProcess + getPreviewUrl(this.ratio) + folder + this.imageName
    },
    previewList() {
      return [
        { title: 'Large activity card', ratio: '16:9', pixel: '1920 × 1080', class: 'ratio_16_9' },
        { title: 'Small activity card', ratio: '4:3', pixel: '1440 × 1080', class: 'ratio_4_3' },
        { title: 'Banner', ratio: '1:1', pixel: '1080 × 1080', class: 'ratio_1_1' }
      ]
    }
  },
  methods: {
    refreshView() {
      this.refreshNum += 1
    },
    getPreProcess() {
      return this.$refs.cropperContainer.getPreProcess()
    },
    onCroppedPreProcess: _.debounce(function onCroppedPreProcess(preProcess) {
      this.previewPreProcess = preProcess
    }, 300)
  }
}
</script>

<style lang="scss" scoped>
.modal-photo-editor-container {
  display: flex;
  color: rgba(0, 0, 0, 0.85);

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
  }

  .editor-main {
    width: 476px;
    margin-right: 24px;
  }

  .preview-item {
    margin-bottom: 8px;
    border-radius: 2px;
    border: 1px solid #f0f0f0;
    font-family: 'Roboto';
    line-height: 22px;

    &:last-child {
      margin-bottom: 0;
    }

    .box {
      width: 162px;
      height: 0;
      background-position: center;
      background-repeat: no-repeat;
      background-color: #f7f8f8;
      background-size: cover;

      &.ratio_16_9 {
        padding-bottom: calc(9 / 16 * 100%);
      }

      &.ratio_4_3 {
        padding-bottom: calc(3 / 4 * 100%);
      }

      &.ratio_1_1 {
        padding-bottom: 100%;
      }
    }

    .footer {
      padding: 16px;
      .title {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
      }

      .ratio,
      .pixel {
        color: #8c8c8c;
      }
    }
  }
}
</style>
