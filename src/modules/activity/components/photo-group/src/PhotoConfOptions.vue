<template>
  <div class="photo-conf-options-container">
    <!-- 展示为 banner -->
    <div v-if="needDisplayAsBanner" class="display-as-banner">
      <a-checkbox
        v-model="form.banner_display"
        v-tooltip="{
          visible: true,
          placement: 'right',
          content: disabledDisplayAsBanner ? disabledTips : ''
        }"
        :disabled="disabled || disabledDisplayAsBanner || disabledAddToBanner"
        @change="changeBannerCount"
      >
        {{ $t('display_on_banner') }}
      </a-checkbox>
    </div>

    <div v-if="needSetNoCropBanner && form.banner_display && needSetSize" class="set-not-crop">
      <!-- 不裁剪设置 banner -->
      <a-checkbox
        v-model="form.is_no_crop_banner"
        v-tooltip="{
          visible: true,
          placement: 'right',
          content: disabledSetSize ? disabledTips : ''
        }"
        :disabled="disabled || disabledSetSize"
        @change="changeIsNoCropBanner"
      >
        <span :data-spm-module="getSpm" data-spm-virtual-item="__virtual">{{ $t('no_size_limit') }}</span>
      </a-checkbox>
    </div>

    <div v-if="needDisplayIsCard" class="is-card">
      <a-radio
        v-tooltip="{
          visible: true,
          placement: 'right',
          content: disabledDisplayIsCard ? disabledTips : $t('75064')
        }"
        class="set_as_card"
        :checked="form.is_card"
        :disabled="disabledDisplayIsCard"
        @click.stop.prevent="changeIsCard"
      >
        {{ $t('75063') }}
      </a-radio>
    </div>
  </div>
</template>

<script>
import { HIGH_QUALITY_PIC_CONF, HIGH_QUALITY_PIC_SIZE, SUPPORT_LARGER_PIC_CONF } from '../const'

export default {
  name: 'PhotoConfOptions',
  model: {
    prop: 'config',
    event: 'change'
  },
  props: {
    config: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    needDisplayAsBanner: {
      type: Boolean,
      default: true
    },
    disabledDisplayAsBanner: {
      type: Boolean,
      default: false
    },
    disabledDisplayIsCard: {
      type: Boolean,
      default: false
    },
    // 不裁剪设置原图
    needSetNoCropBanner: {
      type: Boolean,
      default: true
    },
    needSetSize: {
      type: Boolean,
      default: true
    },
    disabledSetSize: {
      type: Boolean,
      default: false
    },
    activityId: {
      type: [Number, String],
      default: ''
    },
    needDisplayIsCard: {
      type: Boolean,
      default: false
    },
    // 图片是否一定需要清晰
    bannerMustBeClear: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    disabledTips() {
      const { maxBannerImageFileSizeNumber, maxBannerImageWidth, maxBannerImageHeight } =
        SUPPORT_LARGER_PIC_CONF
      return this.$t('193435', {
        file_size_limit: maxBannerImageFileSizeNumber,
        image_width_limit: maxBannerImageWidth,
        image_height_limit: maxBannerImageHeight
      })
    },
    imageParams() {
      const { ORIGIN = {} } = this.config || {}
      const { width, height, size } = ORIGIN || {}
      return {
        width,
        height,
        size,
        variance: this.variance
      }
    },
    disabledAddToBanner() {
      const { banner_display = false } = this.form || {}
      // 旧数据，如果已经是勾选了的，就不置灰，反选完以后赶紧置灰
      if (banner_display) {
        return false
      }
      const { bannerMustBeClear } = this
      if (!bannerMustBeClear) {
        return false
      }
      const { size, width, height } = this.imageParams || {}
      const { minImageHeight, minImageWidth } = HIGH_QUALITY_PIC_CONF
      const minFileSize = HIGH_QUALITY_PIC_SIZE
      return width < minImageWidth || height < minImageHeight || size < minFileSize
    },
    getSpm() {
      const oid = `activity_${this.activityId || 0}`
      return `WithoutCroppingSize?oid=${oid}&trg=manual`
    },
    form: {
      get() {
        return this.config
      },
      set(v) {
        this.$emit('change', v)
      }
    }
  },
  methods: {
    changeBannerCount(evt) {
      if (!evt.target.checked) {
        this.form.is_no_crop_banner = false
        this.changeIsNoCropBanner()
      }
      this.$emit('changeBannerCount')
    },
    changeIsNoCropBanner() {
      this.$emit('changeIsNoCropBanner')
    },
    changeIsCard(event) {
      this.$emit('changeIsCard', event.target.checked)
    }
  }
}
</script>

<style lang="scss" scoped>
$marginPixel: 12px;

.photo-conf-options-container {
  margin-bottom: $marginPixel;

  .display-as-banner {
    display: inline-block;
    margin-right: 20px;
  }

  .set-not-crop {
    display: inline-block;
  }

  .is-card {
    margin-top: $marginPixel;
  }
}
</style>
