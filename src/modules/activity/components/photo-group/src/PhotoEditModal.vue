<template>
  <a-modal
    :visible.sync="_visible"
    :mask-closable="false"
    :width="712"
    :title="title"
    dialog-class="photo-edit-modal"
    @cancel="handleCancel"
  >
    <slot name="config">
      <PhotoConfOptions
        v-model="currentConf"
        :bannerMustBeClear="bannerMustBeClear"
        @changeIsNoCropBanner="changeIsNoCropBanner"
      />
    </slot>

    <a-tabs v-model="activeKey" class="tabs" :animated="false">
      <a-tab-pane v-for="tab of PLATFORM_DICT" :key="tab.key">
        <span slot="tab">
          <a-icon :type="tab.icon" />
          {{ tab.label }}
        </span>

        <ModalPhotoEditor
          v-show="activeKey === tab.key"
          :ref="tab.key"
          :key="_visible + refreshNum"
          v-model="currentConf[tab.key].url"
          :pre-process="currentConf[tab.key].pre_process"
          :image-name.sync="currentConf[tab.key].name"
          :pic-did-cut.sync="picDidCut"
          :disabled="currentConf.is_no_crop_banner"
          :ratio="tab.key === PLATFORM_DICT.DESKTOP.key ? desktopRatio : mobileRatio"
          @initialized="onInitialized"
        />
      </a-tab-pane>
    </a-tabs>

    <template slot="footer">
      <a-button type="link" :loading="isUploading" @click="handleUpload">
        <a-icon type="upload" />
        <span>{{ $t('global_reupload') }}</span>
      </a-button>
      <a-button type="primary" @click="handleCancel">{{ $t('global_cancel') }}</a-button>
      <a-button type="primary" :disabled="!cropInitialized" @click="handleSubmit">
        {{ $t('global_confirm') }}
      </a-button>
    </template>
  </a-modal>
</template>

<script>
import { upload } from '@activity/components/photo-group/utils/index'
import ModalPhotoEditor from '@activity/components/photo-group/src/ModalPhotoEditor'
import PhotoConfOptions from '@activity/components/photo-group/src/PhotoConfOptions'
import { getIsNoCropBannerData, formatTextId, formatImageSize } from '../utils/index'
import {
  desktopRatio,
  mobileRatio,
  PLATFORM_DICT,
  HIGH_QUALITY_PIC_CONF,
  HIGH_QUALITY_PIC_SIZE,
  cloudinaryOptions as _cloudinaryOptions,
  SUPPORT_LARGER_PIC_CONF
} from '@activity/components/photo-group/const/index'

export default {
  name: 'PhotoEditModal',
  components: { ModalPhotoEditor, PhotoConfOptions },
  inject: {
    isInBannerBlock: {
      default: false
    }
  },
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    config: {
      type: Object,
      required: true
    },
    title: {
      type: String,
      default: 'Photo'
    },
    activityId: {
      type: [String, Number],
      default: ''
    },
    needUpload: {
      type: Boolean,
      default: false
    },
    initTarget: {
      type: String,
      default: PLATFORM_DICT.DESKTOP.key
    },
    imageData: {
      type: Object,
      default: () => {}
    },
    desktopRatio: {
      type: String,
      default: desktopRatio
    },
    // 图片是否一定需要清晰
    bannerMustBeClear: {
      type: Boolean,
      default: false
    }
  },
  data() {
    this.PLATFORM_DICT = PLATFORM_DICT
    this.mobileRatio = mobileRatio

    return {
      activeKey: undefined,

      picDidCut: false,
      // upload

      currentConf: {
        is_default: false,
        banner_display: false,
        alt: '',
        desc: '',
        display_order: 1,
        is_no_crop_banner: false,
        ORIGIN: {},
        DESKTOP: {},
        MOBILE: {}
      },

      refreshNum: 0,

      isUploading: false,
      cropInitialized: false
    }
  },
  computed: {
    ratio() {
      if (PLATFORM_DICT.DESKTOP.key === this.activeKey) {
        return this.desktopRatio
      }

      return mobileRatio
    },
    cloudinaryOptions() {
      const { bannerMustBeClear, isInBannerBlock } = this
      const currPhotoMustBeClear = bannerMustBeClear && isInBannerBlock
      const conf = currPhotoMustBeClear
        ? {
            ...SUPPORT_LARGER_PIC_CONF,
            ...HIGH_QUALITY_PIC_CONF
          }
        : {
            ...SUPPORT_LARGER_PIC_CONF
          }
      return _cloudinaryOptions(this.activityId, conf)
    },
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(v) {
        if (v) {
          this.isUploading = false
          this.$set(this, 'currentConf', _.cloneDeep(this.config))
        }
      }
    },
    activeKey: {
      async handler(v) {
        // 修复切换 tab 时，可能存在裁剪图片变小的问题
        const vm = this.$refs[v]?.[0]

        if (vm) {
          vm.refreshView()
        }
      }
    }
  },
  beforeMount() {
    this.activeKey = this.initTarget
  },
  methods: {
    changeIsNoCropBanner() {
      const data = getIsNoCropBannerData({
        config: this.currentConf,
        imageData: this.imageData,
        desktopRatio: this.desktopRatio
      })

      this.$set(this, 'currentConf', data)
      this.picDidCut = false
      this.refreshNum += 1
    },
    async handleUpload() {
      this.isUploading = true
      // 打开 ant-modal 会使 body.style.overflow = 'hidden'
      // 当上传图片创建 cloudinary 实例时，cloudinary 会保存当前 body.style.overflow 值
      // 在上传完成关闭时，重新赋值回去。如果此时保存的是 modal 的值会造成关闭后没法滚动页面
      // 所以在这里打开 cloudinary 前, 先使 overflow 为空
      document.body.style.overflow = ''
      const { success, error, info, previewUrl } = await upload({
        cloudinaryOptions: {
          ...this.cloudinaryOptions,
          multiple: false
        }
      })

      if (success) {
        const { bannerMustBeClear, isInBannerBlock } = this
        const currPhotoMustBeClear = bannerMustBeClear && isInBannerBlock
        const { bytes, width, height } = info
        const { minImageHeight, minImageWidth } = HIGH_QUALITY_PIC_CONF
        const minFileSize = HIGH_QUALITY_PIC_SIZE
        // Please re-upload a clearer photo! Minimum requirement: {minfilesize} {minsize} ({minwidth}x{minheight})
        const msg = formatTextId(this.$t('193820'), {
          minfilesize: formatImageSize(minFileSize),
          minwidth: minImageWidth,
          minheight: minImageHeight
        })
        if (
          currPhotoMustBeClear &&
          (bytes < HIGH_QUALITY_PIC_SIZE || width < minImageWidth || height < minImageHeight)
        ) {
          this.$message.error(msg)
          this.handleCancel()
          return
        }
        this.$emit('uploaded', info)
        this.$emit('change', previewUrl)
        this.handleCancel()
      } else if (error) {
        document.body.style.overflow = 'hidden'
        console.error(`upload error: ${error}`)
      }
      this.isUploading = false
    },
    handleCancel() {
      this.picDidCut = false
      this._visible = false
    },
    onInitialized() {
      this.cropInitialized = true
    },
    async handleSubmit() {
      // Update total banner display
      if (this.currentConf.banner_display) {
        this.$emit('changeBannerCount')
      }

      if (this.currentConf.is_no_crop_banner) {
        // 如果不需要裁剪
        this.$emit('changeIsNoCropBanner')
      } else {
        // 人工自定义裁剪
        Object.keys(this.PLATFORM_DICT).forEach((key) => {
          const vm = this.$refs[key]?.[0] ?? null

          if (vm) {
            this.currentConf[key].pre_process = vm.$refs.cropperContainer.getPreProcess()
            this.$emit('setNoCropBanner', key)
          }
        })
      }
      this.$emit('update:config', this.currentConf)
      this.handleCancel()
    }
  }
}
</script>

<style lang="scss">
.photo-edit-modal {
  .tabs .ant-tabs-bar {
    text-align: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  }
}
</style>
