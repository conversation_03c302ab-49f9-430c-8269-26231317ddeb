<template>
  <div class="image-crop-wrap">
    <div class="common-spin-style">
      <a-spin :spinning="spinning" />
    </div>
    <div class="image-crop">
      <div
        ref="previewImage"
        class="image-crop__preview"
        :style="{ 'background-image': `url(${backgroundImageUrl})` }"
        @click.stop.prevent
      />
      <!-- mask handle-icon -->
      <div v-if="!disabled" class="image-crop__mask">
        <a-icon class="handle-icon" type="zoom-in" @click.prevent="openPreview" />
        <a-icon v-if="needCrop" class="handle-icon" type="edit" @click.prevent="openDialog" />
        <a-icon v-if="needUpload" class="handle-icon" type="upload" @click.prevent="handleUpload" />
      </div>
    </div>

    <a-modal v-model="preview_show" :centered="true" dialog-class="common-fullscreen-style">
      <div class="image-preview-fullscreen">
        <img :src="backgroundImageUrl" />
      </div>
    </a-modal>
  </div>
</template>
<script>
import { upload } from '@activity/components/photo-group/utils/index'
import {
  cloudinaryOptions,
  SUPPORT_LARGER_PIC_CONF,
  HIGH_QUALITY_PIC_CONF
} from '@activity/components/photo-group/const/index'
import { HIGH_QUALITY_PIC_SIZE } from '../const'
import { formatImageSize, formatTextId } from '../utils'

export default {
  name: 'ImageCrop',
  inject: {
    isInBannerBlock: {
      default: false
    }
  },
  props: {
    //! 图片 url, v-model
    value: {
      type: String,
      default: ''
    },
    //! activityId 活动 ID
    activityId: {
      type: [String, Number],
      default: ''
    },
    //! 打开上传功能
    needUpload: {
      type: Boolean,
      default: false
    },
    //! 关闭裁剪功能
    needCrop: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 图片是否一定需要清晰
    bannerMustBeClear: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 是否有裁剪数据。若没有，则图片能放大缩小。
      picDidCut: false,
      //! 裁剪坐标
      coordinates: {
        x: undefined,
        y: undefined,
        width: undefined,
        height: undefined
      },
      //! 展示预览
      preview_show: false,
      //! cloudinary 配置 -> tag 为活动 ID
      cloudinaryOptions: cloudinaryOptions(this.activityId, SUPPORT_LARGER_PIC_CONF),
      //! cloudinary上传部件实例
      cloudinaryInstance: {},

      spinning: false,
      backgroundImageUrl: ''
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(v) {
        if (v) {
          this.spinning = true
          let backgroundImage = new Image()

          backgroundImage.onload = () => {
            this.spinning = false
            this.backgroundImageUrl = this.value.replace(
              'res.klook.com/image/upload',
              'res.klook.com/image/upload/fl_lossy.progressive,q_auto'
            )
            backgroundImage = null
          }

          backgroundImage.src = this.value
        }
      }
    }
  },
  methods: {
    //! 关闭弹窗
    closeDialog() {
      this.dialogVisible = false
    },
    //! 打开弹窗
    openDialog() {
      this.$emit('changeDialogVisible', true)

      this.dialogVisible = true
      this.spinning = true
    },
    async handleUpload() {
      const { bannerMustBeClear, isInBannerBlock } = this
      const currPhotoMustBeClear = bannerMustBeClear && isInBannerBlock
      const { success, error, info, previewUrl } = await upload({
        cloudinaryOptions: {
          ...this.cloudinaryOptions,
          multiple: false,
          ...(currPhotoMustBeClear ? HIGH_QUALITY_PIC_CONF : {})
        }
      })

      if (success) {
        const { bytes, width, height } = info
        const { minImageHeight, minImageWidth } = HIGH_QUALITY_PIC_CONF
        const minFileSize = HIGH_QUALITY_PIC_SIZE
        // Please re-upload a clearer photo! Minimum requirement: {minfilesize} {minsize} ({minwidth}x{minheight})
        const msg = formatTextId(this.$t('193820'), {
          minfilesize: formatImageSize(minFileSize),
          minwidth: minImageWidth,
          minheight: minImageHeight
        })
        if (
          currPhotoMustBeClear &&
          (bytes < HIGH_QUALITY_PIC_SIZE || width < minImageWidth || height < minImageHeight)
        ) {
          this.$message.error(msg)
          return
        }

        this.$emit('uploaded', info)
        this.$emit('change', previewUrl)
      } else if (error) {
        console.error(`upload error: ${error}`)
      }
    },
    openPreview() {
      this.preview_show = true
    },
    //! 关闭预览
    closePreview() {
      this.preview_show = false
    }
  }
}
</script>
<style lang="scss">
.submit-confirm-dialog {
  .ant-modal {
    top: 5% !important;
    max-height: calc(100vh - 10%);
  }
  .ant-modal-body {
    height: 90vh;
  }
}
.image-crop-wrap {
  position: relative;
  background-color: rgb(233, 233, 233);
}
.image-crop {
  border: 0;
  box-sizing: border-box;
  text-align: center;
  cursor: pointer;
  overflow: hidden;
  //   border-radius: 4px;
  position: relative;
  transition: all 0.2s;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  .image-crop__preview {
    width: 100%;
    height: 100%;
    background-size: contain;
    cursor: pointer;
    background-position: center;
    background-repeat: no-repeat;
  }
  .image-crop__mask {
    transition: all 0.2s;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
    opacity: 0;
    top: 0;
    left: 0;
    .handle-icon {
      padding: 10px;
      margin: 0;
      //   height: 45px;
      font-size: 25px;
      position: relative;
      // transition: all 0.2s;
      opacity: 0;
      z-index: 1;
      color: rgba(210, 210, 210, 1);
      top: 15px;
      &:nth-child(1) {
        top: 15px;
        transition: all 0.2s;
      }
      &:nth-child(2) {
        top: 20px;
        transition: all 0.4s;
      }
      &:nth-child(3) {
        top: 30px;
        transition: all 0.5s;
      }
      &:hover {
        color: #fff;
      }
    }
    &:hover {
      opacity: 1;
      backdrop-filter: blur(3px);
      .handle-icon {
        opacity: 1;
        top: 0;
      }
    }
  }
  .image-preview-fullscreen {
    // width: 100%;
    z-index: 9999;
    height: 100%;
    background-color: transparent;
    img {
      // width: 100%;
      height: 70vh;
      position: relative;
      top: 20vh;
    }
  }
}
</style>
