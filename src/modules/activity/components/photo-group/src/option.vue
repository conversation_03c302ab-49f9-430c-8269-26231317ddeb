<template>
  <div class="photo-edit">
    <ShimAntdTooltip :rich-tips="$attrs.photoCropRichTips || ''" placement="right">
      <PhotoConfOptions
        v-model="currentConfig"
        :disabled="disabled"
        :activity-id="activityId"
        v-bind="$attrs"
        :disabled-display-as-banner="calcDisabledDisplayAsBanner"
        :disabled-display-is-card="calcDisabledDisplayIsCard"
        :disabled-set-size="calcDisabledSetSize"
        :bannerMustBeClear="bannerMustBeClear"
        @changeBannerCount="changeBannerCount"
        @changeIsNoCropBanner="changeIsNoCropBanner"
        @changeIsCard="changeIsCard"
      />

      <a-row v-if="isSingle">
        <a-col>
          <Crop
            v-model="currentConfig.ORIGIN.url"
            v-bind="$attrs"
            class="photo-edit-image"
            style="width: 310px; height: 160px"
            :activity-id="+activityId"
            :pre-process.sync="currentConfig.ORIGIN.pre_process"
            :image-name.sync="currentConfig.ORIGIN.name"
            need-upload
            :need-crop="false"
            :disabled="disabled"
            :bannerMustBeClear="bannerMustBeClear"
            @uploaded="handleUploaded"
            @change="onChange"
          />
        </a-col>
      </a-row>
      <a-row v-else :gutter="5">
        <a-col class="base" :span="8">
          <div>{{ $t('29632') }}</div>
          <!-- base 裁剪 + 上传 -->
          <Crop
            v-model="currentConfig.ORIGIN.url"
            v-bind="$attrs"
            class="photo-edit-image"
            :activity-id="+activityId"
            :pre-process.sync="currentConfig.ORIGIN.pre_process"
            :image-name.sync="currentConfig.ORIGIN.name"
            need-upload
            :need-crop="false"
            :disabled="disabled"
            :original-data="currentConfig.ORIGIN"
            :bannerMustBeClear="bannerMustBeClear"
            @uploaded="handleUploaded"
            @change="onChange"
          />
        </a-col>
        <a-col class="desktop" :span="8">
          <div>{{ $t('29633') }}</div>
          <!-- desktop 裁剪 -->
          <Crop
            v-model="currentConfig.DESKTOP.url"
            v-bind="$attrs"
            class="photo-edit-image"
            :ratio="desktopRatio"
            :pre-process.sync="currentConfig.DESKTOP.pre_process"
            :disabled="disabled"
            :image-name.sync="currentConfig.DESKTOP.name"
            :original-data="currentConfig.DESKTOP"
            :bannerMustBeClear="bannerMustBeClear"
            @changeDialogVisible="(visible) => changeDialogVisible(visible, PLATFORM_DICT.DESKTOP.key)"
            @set-no-crop-banner="setNoCropBanner(PLATFORM_DICT.DESKTOP.key)"
          />
        </a-col>
        <a-col class="mobile" :span="8">
          <div>{{ $t('29634') }}</div>
          <!-- mobile 裁剪 -->
          <Crop
            v-model="currentConfig.MOBILE.url"
            v-bind="$attrs"
            class="photo-edit-image"
            :ratio="mobileRatio"
            :disabled="disabled"
            :pre-process.sync="currentConfig.MOBILE.pre_process"
            :image-name.sync="currentConfig.MOBILE.name"
            :original-data="currentConfig.MOBILE"
            :bannerMustBeClear="bannerMustBeClear"
            @changeDialogVisible="(visible) => changeDialogVisible(visible, PLATFORM_DICT.MOBILE.key)"
            @set-no-crop-banner="setNoCropBanner(PLATFORM_DICT.MOBILE.key)"
          />
        </a-col>
      </a-row>
      <a-alert :type="isLowQuality || isLargePhoto ? 'error' : 'success'" style="margin-top: 10px">
        <template #message>
          <span v-html="photoTips"></span>
        </template>
      </a-alert>
    </ShimAntdTooltip>

    <!-- 图片信息编辑 -->
    <section style="margin-top: 20px">
      <div v-if="needCustomSize">
        <a-radio-group v-model="currentConfig.customSize" :disabled="disabled">
          <a-radio value="custom-size"> Cut photo to {{ `${sizeConf.width} x ${sizeConf.height}` }} </a-radio>
          <a-radio value="origin-size"> No photo size limit </a-radio>
        </a-radio-group>
      </div>
      <!-- 下载原图, download 属性不生效, 因为不是相对地址 -->
      <div v-if="allowDownloadImage">
        <a class="el-icon-download" style="cursor: pointer" @click="downloadOriginalImage">
          {{ $t('act_photo_download_original_image') }}
        </a>
      </div>

      <!-- 图片顺序 -->
      <div v-if="needOrder" class="order-num">
        <a-select
          v-model="currentConfig.display_order"
          :class="
            current_list.filter((o) => o.display_order === currentConfig.display_order).length === 1
              ? 'no-error'
              : 'common-has-error'
          "
          size="small"
          placeholder="Please select order"
          :disabled="disabled"
        >
          <a-select-option v-for="num in orderOptions" :key="num" :label="num" :value="num">{{
            num
          }}</a-select-option>
        </a-select>
      </div>

      <ShimAntdTooltip :rich-tips="$attrs.captionsRichTips" placement="rightTop" :is-fit-content="true">
        <a-form-model
          ref="form"
          :model="form"
          :rules="rules"
          class="photo-captions"
          style="width: fit-content"
        >
          <!-- 图片 alt -->
          <a-form-model-item v-if="needAlt" :class="calcClass" class="fields" prop="alt">
            <CountLimitInput
              v-model="currentConfig.alt"
              v-tooltip="{
                visible: $root.isAdmin,
                placement: 'right',
                content: $t('122697')
              }"
              :placeholder="$t('122696')"
              :disabled="disabled || disabledAlt"
              :word-count-limit="wordCountLimit"
              :language="curLang"
            />
            <!-- 参考语言 -->
            <a-input
              v-if="refConfig && refConfig.alt"
              v-model="refConfig.alt"
              disabled
              :placeholder="$t('28800')"
            />
          </a-form-model-item>
          <!-- 图片 desc -->
          <a-form-model-item v-if="needDesc" class="fields no-error" prop="desc">
            <CountLimitInput
              v-model="currentConfig.desc"
              :placeholder="$t('act_photo_desc_hint')"
              :disabled="disabled || disabledDesc"
              :word-count-limit="wordCountLimit"
              :language="curLang"
              type="textarea"
            />
            <!-- 参考语言 -->
            <a-textarea
              v-if="refConfig && refConfig.desc"
              v-model="refConfig.desc"
              class="text-hint"
              disabled
              :placeholder="$t('act_photo_desc_hint')"
            />
          </a-form-model-item>
        </a-form-model>
      </ShimAntdTooltip>
      <!-- 删除按钮 -->
      <div v-if="allowRemove && !disabled" class="remove-buttons" @click.stop.prevent="removeOne">
        <i class="delete-btn">
          <svg-icon icon-name="trash" style="width: 10px; height: 10px; color: #fff" />
        </i>
      </div>
    </section>

    <PhotoEditModal
      v-if="editModal.visible"
      :visible.sync="editModal.visible"
      :activity-id="activityId"
      :init-target="editModal.initTarget"
      :config.sync="currentConfig"
      :image-data="imageData"
      :desktop-ratio="desktopRatio"
      v-bind="$attrs"
      :bannerMustBeClear="bannerMustBeClear"
      need-upload
      @uploaded="handleUploaded"
      @change="onChange"
      @setNoCropBanner="setNoCropBanner"
      @changeBannerCount="changeBannerCount"
      @changeIsNoCropBanner="changeIsNoCropBanner"
    />
  </div>
</template>

<script>
import {
  SUPPORT_LARGER_PIC_CONF,
  imageBaseUrl,
  folder,
  getPreviewUrl,
  desktopRatio,
  mobileRatio,
  imageUrlPrefix,
  PLATFORM_DICT,
  LARGER_PIC_CONF,
  HIGH_QUALITY_PIC_SIZE,
  HIGH_QUALITY_PIC_CONF
} from '../const'
import {
  calculateLaplacianVariance,
  formatImageSize,
  formatLaplacianVariance,
  formatTextId,
  getIsNoCropBannerData
} from '../utils/index'
import Crop from './crop'
import CountLimitInput from '@activity/components/count-limit-input/index.vue'
import PhotoConfOptions from '@activity/components/photo-group/src/PhotoConfOptions'
import PhotoEditModal from '@activity/components/photo-group/src/PhotoEditModal'
import ShimAntdTooltip from '@activity/components/shimAntdTooltip'

const photo_conf_options_mixin = {
  props: {
    disabledSetSize: {
      type: Boolean,
      default: false
    },
    disabledDisplayAsBanner: {
      type: Boolean,
      default: false
    },
    disabledDisplayIsCard: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    imageOriginData() {
      return _.get(this.currentConfig, 'ORIGIN', {})
    },
    displayAsBannerLimit() {
      const { size = 0, width = 0, height = 0 } = this.imageOriginData || {}
      const { maxBannerImageFileSize, maxBannerImageWidth, maxBannerImageHeight } = SUPPORT_LARGER_PIC_CONF
      return size > maxBannerImageFileSize || width > maxBannerImageWidth || height > maxBannerImageHeight
    },
    calcDisabledDisplayAsBanner() {
      const attrValue = this.disabledDisplayAsBanner
      const banner_display = this.config?.banner_display ?? false
      if (!banner_display && this.displayAsBannerLimit) {
        return true
      }
      return attrValue
    },
    calcDisabledDisplayIsCard() {
      const attrValue = this.disabledDisplayIsCard
      const { size = 0, width = 0, height = 0 } = this.imageOriginData || {}
      const is_card = this.config?.is_card ?? false
      const { maxBannerImageFileSize, maxBannerImageWidth, maxBannerImageHeight } = SUPPORT_LARGER_PIC_CONF
      if (
        !is_card &&
        (size > maxBannerImageFileSize || width > maxBannerImageWidth || height > maxBannerImageHeight)
      ) {
        return true
      }
      return attrValue
    },
    calcDisabledSetSize() {
      const attrValue = this.disabledSetSize
      const width = this.imageOriginData.width || 0
      const height = this.imageOriginData.height || 0
      const is_no_crop_banner = this.config?.is_no_crop_banner ?? false
      const { maxCropSize } = SUPPORT_LARGER_PIC_CONF
      if ((!is_no_crop_banner && height * width > maxCropSize) || this.displayAsBannerLimit) {
        return true
      }
      return attrValue
    }
  }
}

const photo_edit_modal_mixin = {
  data() {
    this.PLATFORM_DICT = PLATFORM_DICT

    return {
      editModal: {
        visible: false,
        initTarget: PLATFORM_DICT.DESKTOP.key
      }
    }
  },
  methods: {
    changeDialogVisible(visible, target) {
      this.$set(this, 'editModal', {
        visible,
        initTarget: target
      })
    }
  }
}

export default {
  name: 'PhotoEdit',
  inject: ['updateData'],
  components: { Crop, PhotoConfOptions, PhotoEditModal, CountLimitInput, ShimAntdTooltip },
  mixins: [photo_edit_modal_mixin, photo_conf_options_mixin],
  props: {
    current_list: {
      type: Array,
      default() {
        return []
      }
    },
    //! 配置 => 包含基础信息, 以及 ORIGIN, DESKTOP, MOBILE 三种尺寸图片
    config: {
      type: Object,
      default: () => ({
        is_default: false,
        banner_display: false,
        alt: '',
        desc: '',
        display_order: 1,
        is_no_crop_banner: false,
        ORIGIN: {},
        DESKTOP: {},
        MOBILE: {}
      })
    },
    // 参考配置
    refConfig: {
      type: Object,
      default: () => ({})
    },
    //! 类型
    type: {
      type: String,
      default: 'act'
    },
    //! 图片列表
    total: {
      type: Number,
      default: 0
    },
    //! activityId
    activityId: {
      type: [Number, String],
      default: ''
    },
    //! 展示删除按钮
    allowRemove: {
      type: Boolean,
      default: true
    },
    // 自定义size
    needCustomSize: {
      type: Boolean,
      default: false
    },
    // 需要图片描述
    needDesc: {
      type: Boolean,
      default: true
    },
    disabledDesc: {
      type: Boolean,
      default: false
    },
    // 需要图片alt
    needAlt: {
      type: Boolean,
      default: true
    },
    disabledAlt: {
      type: Boolean,
      default: false
    },
    // 需要图片排序
    needOrder: {
      type: Boolean,
      default: true
    },
    // 当前语言
    curLang: {
      type: String,
      default: ''
    },
    // 参考语种, 不传即不需要参考语言
    refLang: {
      type: String,
      default: ''
    },
    // 尺寸配置
    sizeConf: {
      type: Object,
      default: () => {}
    },
    // 只展示单张 , 不展示 desktop mobile - 活动指引图片
    isSingle: {
      type: Boolean,
      default: false
    },
    // 允许下载原图
    allowDownloadImage: {
      type: Boolean,
      default: true
    },
    // desktop时候的比例
    desktopRatio: {
      type: String,
      default: desktopRatio
    },
    disabled: {
      type: Boolean,
      default: false
    },
    needSetSize: {
      type: Boolean,
      default: true
    },
    wordCountLimit: {
      type: Number,
      default: Infinity
    },
    // 图片是否一定需要清晰
    bannerMustBeClear: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const wordCountLimit = this.wordCountLimit
    const message = this.$t('83853', { num: wordCountLimit })
    const validateAlt = (r, v, callback) => {
      const { needAlt, disabledAlt, disabled, currentConfig } = this
      const val = (currentConfig?.alt ?? '').trim()
      if (disabled || disabledAlt || !needAlt) {
        return callback()
      }
      if (val.length > wordCountLimit) {
        callback(new Error(message))
        return
      }
      callback()
    }
    const validateDesc = (r, v, callback) => {
      const { needDesc, disabledDesc, disabled, currentConfig } = this
      const val = (currentConfig?.desc ?? '').trim()
      if (disabled || disabledDesc || !needDesc) {
        return callback()
      }
      if (val.length > wordCountLimit) {
        callback(new Error(message))
        return
      }
      callback()
    }
    return {
      // mobile 图片比例
      mobileRatio,
      // 宽高
      imageData: {},
      //! 排序下拉框的值
      orderOptions: [],
      variance: '',
      rules: {
        alt: [{ validator: validateAlt }, { trigger: 'change' }],
        desc: [{ validator: validateDesc }, { trigger: 'change' }]
      }
    }
  },
  computed: {
    getSpm() {
      const oid = `activity_${this.activityId || 0}`
      return `WithoutCroppingSize?oid=${oid}&trg=manual`
    },
    //! 当前的图片配置
    currentConfig: {
      get() {
        return this.config
      },
      set(val) {
        Object.assign(this.config, val)
      }
    },
    imageRealSize() {
      const { size, ORIGIN = {} } = this.currentConfig || {}
      return size || ORIGIN.size || 0
    },
    imageParams() {
      const { width = 0, height = 0 } = this.imageOriginData || {}
      const size = this.imageRealSize
      return {
        width,
        height,
        size,
        variance: this.variance
      }
    },
    isLowQuality() {
      const { width = 0, height = 0, size = 0 } = this.imageParams || {}
      const { minImageHeight, minImageWidth } = HIGH_QUALITY_PIC_CONF
      const minFileSize = HIGH_QUALITY_PIC_SIZE
      return width < minImageWidth || height < minImageHeight || size < minFileSize
    },
    isLargePhoto() {
      const { width = 0, height = 0, size = 0 } = this.imageParams || {}
      const { maxImageHeight, maxImageFileSize, maxImageWidth } = LARGER_PIC_CONF
      return size > maxImageFileSize || height > maxImageHeight || width > maxImageWidth
    },
    photoTips() {
      const { isLowQuality, isLargePhoto } = this
      const { width = 0, height = 0, size = 0, variance: _variance } = this.imageParams || {}
      const { minImageHeight, minImageWidth } = HIGH_QUALITY_PIC_CONF
      const { maxImageHeight, maxImageFileSize, maxImageWidth } = LARGER_PIC_CONF
      const minFileSize = HIGH_QUALITY_PIC_SIZE
      const variance = formatLaplacianVariance(_variance)
      const fileSize = formatImageSize(size)

      // Low quality photo! Please submit another one.<br/>Current photo: {filesize}, ({width}x{height}), Laplacian: {variance}<br/>Minimum requirement: {minfilesize} ({minwidth}x{minheight})
      const lowQualityTips = formatTextId(this.$t('193817'), {
        filesize: fileSize,
        width,
        height,
        variance,
        minfilesize: formatImageSize(minFileSize),
        minwidth: minImageWidth,
        minheight: minImageHeight
      })

      // Photo is too large! Please submit another one. <br/>Current photo: {filesize}, ({width}x{height}), Laplacian: {variance}<br/>Max size: {maxfilesize} ({maxwidth}x{maxheight})
      const largePhotoTips = formatTextId(this.$t('193818'), {
        filesize: fileSize,
        width,
        height,
        variance,
        maxfilesize: formatImageSize(maxImageFileSize),
        maxwidth: maxImageWidth,
        maxheight: maxImageHeight
      })

      // Passed! Current photo: {filesize}, ({width}x{height}), Laplacian: {variance}<br/>Minimum: {minfilesize} ({minwidth}x{minheight})
      const tips = formatTextId(this.$t('193819'), {
        filesize: fileSize,
        width,
        height,
        variance,
        minfilesize: formatImageSize(minFileSize),
        minwidth: minImageWidth,
        minheight: minImageHeight
      })

      return isLowQuality ? lowQualityTips : (isLargePhoto ? largePhotoTips : tips) || ''
    },
    form: {
      get() {
        const { alt, desc } = this.currentConfig
        return { alt, desc }
      },
      set() {}
    },
    calcClass() {
      const { alt, desc } = this.currentConfig
      const { wordCountLimit } = this
      const validate = alt.trim().length > wordCountLimit || desc.trim().length > wordCountLimit

      return alt && !validate ? 'no-error' : 'common-has-error'
    }
  },
  watch: {
    'currentConfig.display_order': {
      handler() {
        this.$emit('changeOrder')
      }
    },
    //! 当 url, preprocess 改变时
    currentConfig: {
      immediate: true,
      deep: true,
      handler(next, old) {
        //! 生成图片信息
        // if (!_.isEqual(next, old)) {
        this.generateImageScheme(next)
        this.calculateImageVariance(next)
        this.$emit('update:config', next)

        //更新最外层 v-model
        this.updateData && this.updateData()
      }
    },
    //! 排序下拉框的值, 数量变化时, 重新计算order下拉 dropdown ([1,2,3,4,5...]), 从1开始
    total: {
      immediate: true,
      handler(next) {
        let arr = new Array(next + 1).fill('')
        let options = Object.keys(arr).map(Number)
        // 去掉 0, 从 1 开始
        options.shift()
        // return options;
        this.orderOptions = options
      }
    }
  },
  methods: {
    validateForm() {
      return this.$refs.form.validate().catch(() => false)
    },
    // 处理
    async generateImageScheme(config) {
      // base 没有比例 ------
      config.ORIGIN.url = this.getUrl(config.ORIGIN)
      // destop ------
      config.DESKTOP.url = this.getUrl(config.DESKTOP, this.desktopRatio)
      // mobile ------
      config.MOBILE.url = this.getUrl(config.MOBILE, mobileRatio)
      // 是否不裁剪设置banner
      if (this.isNoCropBanner()) {
        config.is_no_crop_banner = true
      }
      // 获取图片长宽
      let { width, height } = await this.getImageData()
      // 记录图片宽高
      this.imageData = { width, height }
      config.origin_size = { width, height }
    },
    // 生成 url
    getUrl(target, ratio) {
      return imageBaseUrl + target.pre_process + getPreviewUrl(ratio) + folder + target.name
    },
    // 是否不裁剪设置banner, 通过 DESKTOP.pre_process 是否包含 u_activities
    isNoCropBanner() {
      let pre_process = this.currentConfig.DESKTOP.pre_process
      return pre_process && pre_process.includes('u_activities')
    },
    // 图片重新上传以后
    onChange(url) {
      let conf = this.currentConfig
      let image_name = url.split('/').pop()
      conf.name = image_name
      conf.url = image_name
      conf.ORIGIN.name = image_name
      conf.ORIGIN.pre_process = ''
      conf.DESKTOP.name = image_name
      conf.DESKTOP.pre_process = ''
      conf.MOBILE.name = image_name
      conf.MOBILE.pre_process = ''
      conf.is_no_crop_banner = false
      // 更新尺寸
      conf.ORIGIN.width = conf.DESKTOP.width = conf.MOBILE.width = conf.origin_size?.width
      conf.ORIGIN.height = conf.DESKTOP.height = conf.MOBILE.height = conf.origin_size?.height
    },
    // 记录尺寸
    handleUploaded(info) {
      let { width, height, bytes } = info
      this.currentConfig.origin_size = { width, height }
      this.currentConfig.size = bytes
      this.inhouseUploadImage()
    },
    //! 删除, 抛出事件, 上级处理
    removeOne() {
      this.$emit('remove')
      this.$emit('banner-count-change')
    },
    changeBannerCount() {
      this.$emit('banner-count-change')
    },
    changeIsCard(checked) {
      this.$emit('changeIsCard', checked)
    },
    // 改变 "不裁剪设置banner"
    async changeIsNoCropBanner() {
      const conf = getIsNoCropBannerData({
        config: this.currentConfig,
        imageData: this.imageData,
        desktopRatio: this.desktopRatio
      })

      Object.assign(this.currentConfig, conf)
    },
    // 获取图片宽高
    async getImageData() {
      return new Promise((resolve) => {
        let image = new Image()
        image.onload = (e) => {
          resolve(e.target)
        }
        // image.src = this.currentConfig.ORIGIN.url;
        image.src = imageUrlPrefix + this.currentConfig.ORIGIN.name
      })
    },
    //! "不裁剪设置banner" 选项change方法
    setNoCropBanner(from) {
      if (this.currentConfig.is_no_crop_banner === false) {
        return
      }
      this.currentConfig.is_no_crop_banner = false
      if (from === PLATFORM_DICT.DESKTOP.key) {
        this.currentConfig.MOBILE.pre_process = ''
      } else {
        this.currentConfig.DESKTOP.pre_process = ''
      }
    },
    // 下载原图
    downloadOriginalImage() {
      // :href="imageUrlPrefix + currentConfig.name"
      // :download="currentConfig.name"
      let x = new XMLHttpRequest()
      x.open('GET', `${imageUrlPrefix + this.currentConfig.name}`, true)
      x.responseType = 'blob'
      x.onload = function (e) {
        var url = window.URL.createObjectURL(x.response)
        var a = document.createElement('a')
        a.href = url
        a.download = ''
        a.click()
      }
      x.send()
    },
    async inhouseUploadImage() {
      const { ORIGIN = {} } = this.currentConfig || {}
      const { height, width, size: _size, url } = ORIGIN || {}
      const maxSize = 20 * 1024 * 1024
      let variance = ''
      try {
        variance = _size < maxSize ? await calculateLaplacianVariance(url) : '-'
      } catch (error) {
        console.log('error', error)
        variance = '-'
      }
      this.$tracker.track('custom', '.js-basic-info-vm', {
        spm: 'Activity_Photo_Upload',
        oid: `aid_${this.activityId}`,
        ext: {
          bytes: _size,
          size: formatImageSize(_size),
          width,
          height,
          url,
          variance: Math.floor(variance)
        }
      })
    },
    async calculateImageVariance(currentConfig) {
      const { ORIGIN = {}, size = 0 } = currentConfig || {}
      const { url: imageUrl = '' } = ORIGIN || {}
      const maxSize = 20 * 1024 * 1024

      if (imageUrl) {
        try {
          const variance = size < maxSize ? await calculateLaplacianVariance(imageUrl) : '-'
          this.variance = parseFloat(variance.toFixed(2))
        } catch (error) {
          this.variance = '' // 发生错误时清空方差
        }
      } else {
        this.variance = '' // 发生错误时清空方差
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.photo-edit {
  position: relative;
  flex: 1;
  list-style: none;
  border-radius: 4px;
  background-color: #fafafa;
  margin-bottom: 12px;
  padding: 12px 40px 12px 70px;
  min-width: 520px;
  &:last-child {
    margin-bottom: 10px;
  }

  .order-num {
    position: absolute;
    top: 50%;
    left: 14px;
    transform: translateY(-50%);
  }

  .photo-edit-image {
    border-radius: 4px;
    height: 118px;
    width: 100%;
  }
  .fields {
    margin: 10px 0;
    max-width: 440px;
    .text-hint {
      margin-top: 10px;
      width: 100%;
      &:first-child {
        margin-top: 0;
      }
    }
  }
  .remove-buttons {
    position: absolute;
    top: 140px;
    right: 4px;
    display: flex;
    width: 30px;
    height: 30px;
    justify-content: center;
    align-items: center;
    border-radius: 30px;
    transition: background-color 0.3s;
    .delete-icon {
      color: #ffab00;
    }
    &:hover {
      background-color: rgba(255, 171, 0, 0.1);
    }
  }
  .delete-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border-radius: 16px;
    border: none;
    background-color: #ffab00;
  }
}
</style>
