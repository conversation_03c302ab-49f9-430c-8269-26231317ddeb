import { showCloudinaryUploadTips } from '../const'

export const upload = ({ cloudinaryOptions }) => {
  return new Promise((resolve) => {
    // sentry-fix Cannot read properties of undefined (reading 'createUploadWidget')
    if (!window.cloudinary) {
      return resolve({ success: false, error: 'window.cloudinary is undefined' })
    }
    let div
    let cloudinaryInstance = window.cloudinary.createUploadWidget(cloudinaryOptions, (error, result) => {
      if (error) {
        resolve({ success: false, error })

        return
      }
      if (result.event === 'source-changed') {
        div = showCloudinaryUploadTips()
      } else if (result.event === 'display-changed' && div) {
        div.style.display = result.info === 'minimized' ? 'none' : 'inherit'
      } else if (result.event === 'queues-end') {
        resolve(result.info || {})
      } else if (result.event === 'success') {
        const info = result.info || {}
        const previewUrl = info.url.replace('http', 'https')
        // const showName = info.original_filename + '.' + info.format

        resolve({
          success: true,
          info,
          previewUrl
        })
      } else if (result.event === 'close') {
        div?.remove?.()
        cloudinaryInstance?.destroy &&
          cloudinaryInstance.destroy({ removeThumbnails: true }).then(() => {
            console.log('Widget was destroyed')
          })
        resolve({ success: false })
      }
    })

    cloudinaryInstance.open()
  })
}

import { mobileRatio, blur_pre_process } from '../const'
export const getIsNoCropBannerData = ({ config, imageData, desktopRatio }) => {
  let conf = _.cloneDeep(config)
  // 图片名
  let name = conf.DESKTOP.name.split('.')[0]
  // 原图比例
  let ratio = imageData.width / imageData.height
  let desktop_arr = desktopRatio.split(':')
  let mobile_arr = mobileRatio.split(':')
  // destop 比例
  let desktop_ratio = desktop_arr[0] / desktop_arr[1]
  // mobile 比例
  let mobile_ratio = mobile_arr[0] / mobile_arr[1]
  // 是否选中, '不裁剪设置banner'
  if (conf.is_no_crop_banner) {
    // 设置了不裁剪，必须赋值pre_process，C端根据u_activities判断是否裁剪
    // 判断比例
    if (ratio > desktop_ratio) {
      conf.DESKTOP.pre_process = blur_pre_process(name, desktopRatio, 'w')
    } else {
      conf.DESKTOP.pre_process = blur_pre_process(name, desktopRatio, 'h')
    }
    // 判断比例
    if (ratio > mobile_ratio) {
      conf.MOBILE.pre_process = blur_pre_process(name, mobileRatio, 'w')
    } else {
      conf.MOBILE.pre_process = blur_pre_process(name, mobileRatio, 'h')
    }
  } else {
    conf.DESKTOP.pre_process = ''
    conf.MOBILE.pre_process = ''
  }

  return conf
}

export const formatImageSize = (bytes) => {
  const KB = 1024
  const MB = 1024 * KB
  if (bytes < KB) {
    return `${bytes} bytes`
  } else if (bytes < MB) {
    const kilobytes = (bytes / KB).toFixed(2)
    return `${kilobytes} KB`
  } else {
    const megabytes = (bytes / MB).toFixed(2)
    return `${megabytes} MB`
  }
}

export const formatTextId = (template, valueMap) => {
  return template.replace(/{(\w+)}/g, (match, key) => valueMap[key] || match)
}

// 计算拉普拉斯方差的函数
export const calculateLaplacianVariance = (imageUrl) => {
  return new Promise((resolve) => {
    const img = new Image()
    img.src = imageUrl
    img.crossOrigin = 'anonymous' // 解决跨域问题
    img.onload = () => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      canvas.width = img.width
      canvas.height = img.height
      ctx.drawImage(img, 0, 0, img.width, img.height)

      const imageData = ctx.getImageData(0, 0, img.width, img.height)
      const grayscaleData = convertToGrayscale(imageData.data)
      const variance = laplacianVariance(grayscaleData, img.width, img.height)

      resolve(variance)
    }
  })
}

// 将图片像素转换为灰度图
const convertToGrayscale = (pixels) => {
  const grayscale = []
  for (let i = 0; i < pixels.length; i += 4) {
    const grayValue = pixels[i] * 0.299 + pixels[i + 1] * 0.587 + pixels[i + 2] * 0.114
    grayscale.push(grayValue)
  }
  return grayscale
}

// 计算拉普拉斯方差
const laplacianVariance = (grayData, width, height) => {
  const kernel = [0, 1, 0, 1, -4, 1, 0, 1, 0]

  const laplacian = []
  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      let sum = 0
      sum += kernel[0] * grayData[(y - 1) * width + (x - 1)]
      sum += kernel[1] * grayData[(y - 1) * width + x]
      sum += kernel[2] * grayData[(y - 1) * width + (x + 1)]
      sum += kernel[3] * grayData[y * width + (x - 1)]
      sum += kernel[4] * grayData[y * width + x] // 中心
      sum += kernel[5] * grayData[y * width + (x + 1)]
      sum += kernel[6] * grayData[(y + 1) * width + (x - 1)]
      sum += kernel[7] * grayData[(y + 1) * width + x]
      sum += kernel[8] * grayData[(y + 1) * width + (x + 1)]

      laplacian.push(sum)
    }
  }

  // 计算方差
  const mean = laplacian.reduce((acc, val) => acc + val, 0) / laplacian.length
  const variance = laplacian.reduce((acc, val) => acc + (val - mean) ** 2, 0) / laplacian.length
  return variance
}

export const formatLaplacianVariance = (variance) => {
  if (!variance || isNaN(variance) || typeof variance !== 'number') {
    return ''
  }
  let description = ''

  if (variance < 100) {
    description = 'blurry'
  } else if (variance >= 100 && variance < 300) {
    description = 'might blurry'
  } else if (variance >= 300 && variance < 500) {
    description = 'might clear'
  } else {
    description = 'clear'
  }

  return `Variance: ${Math.floor(variance)} (${description})`
}
