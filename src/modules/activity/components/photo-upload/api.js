import { request } from '@klook/admin-utils/lib/request'
import { get } from 'lodash'

// URL
const apiURL = {
  apiGetPhotos: (activity_id) => `/prosrv/activities/${activity_id}/photo`, // 获取图片, 包含banner
  apiSavePhotos: (activity_id, modify_id) => {
    modify_id = modify_id
      ? modify_id
      : new Date().valueOf() + '' + parseInt(Math.random() * 10000)
    return `/prosrv/activities/${activity_id}/photo/update?modify_id=${modify_id}` // 保存图片草稿
  },
  apiSaveVideos: (activity_id) =>
    `/prosrv/activities/${activity_id}/video/update`, // 保存视频草稿
  apiUpdateLangStatus: (activity_id, edit_lang) =>
    `/prosrv/activities/${activity_id}/${edit_lang}/status/update`, // 更新语言状态
  apiGetPackageInfo: (activity_id, edit_lang) =>
    `/prosrv/activities/${activity_id}/packages?custom_language=${edit_lang}`, // 获取package信息
  //! 新增 - 获取所有语言下的图片
  apiGetAllImage:
    '/v1/productadminsrv/taxonomy/instance_data_service/get_widget_free_text_i18n'
}

// 处理错误
const apiHandler = async (req) => {
  try {
    let res = await req
    if (res.success) {
      return [res.result, null]
    } else {
      this.$message.error(get(res, 'error.message'))
      return [null, res.error]
    }
  } catch (e) {
    console.log(e)
    return [null, e]
  }
}

// 获取图片, 包含banner
const getPhotos = (activity_id) => {
  let q = request({
    method: 'get',
    url: apiURL.apiGetPhotos(activity_id)
  })
  return apiHandler(q)
}

// 保存图片为草稿
const savePhotos = (activity_id, data, modify_id) => {
  let q = request({
    method: 'post',
    url: apiURL.apiSavePhotos(activity_id, modify_id),
    data
  })
  return apiHandler(q)
}

// 获取所有图片 in 活动详情
const getAllImage = (params) => {
  let q = request({
    method: 'get',
    url: apiURL.apiGetAllImage,
    params
  })
  return apiHandler(q)
}

export {
  // getActNotes,
  // getActLangStatusAll,
  getPhotos,
  // getActVideos,
  savePhotos,
  // saveVideos,
  // updateLangStatus,
  // getPackageInfo,
  getAllImage
}
