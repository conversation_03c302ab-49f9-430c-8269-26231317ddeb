<template>
  <!-- 活动图片上传 -->
  <a-form-model ref="form" :model="form" label-width="200px" class="photo-activity">
    <a-form-model-item :rules="getRules()" prop="custom">
      <PhotoGroup
        ref="photo_group"
        v-bind="$attrs"
        :hide-copy="true"
        :activity-id="activityId"
        :cur-lang="curLang"
        :ref-lang="refLang"
        :list-all="list_all_format"
        :list="photo_list"
        :need-alt="isAdmin"
        :disabled-alt="!isEMpage || isPublishWithAI"
        :disabled-desc="isPublishWithAI"
        :need-desc="needDesc"
        :cur-lang-only.sync="cur_lang_only"
        :cur-lang-only-label="$t('create_act_photo_for_lang')"
        :need-valid-desc="false"
        :banner-count="bannerCount"
        :limit="limit"
        :trigger-banner-check="triggerBannerCheck"
        :need-display-is-card="needDisplayIsCard"
        :min-banner-count="minBannerCount"
        :bannerMustBeClear="bannerMustBeClear"
        :need-valid-alt="false"
        :need-set-no-crop-banner="needSetNoCropBanner"
        @current-list-change="changeListClearValidate"
        @copy-photo="copyPhoto"
        @validate-form="validate"
        @banner-count-change="bannerCountChange"
      />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
// activity 或 guide
//   am: photo desc 选填 , photo alt 隐藏
//   em: photo desc 选填 , photo alt 必填
import PhotoGroup from '../photo-group'
import { mapGetters } from 'vuex'

export default {
  name: 'PhotoActivity',
  components: { PhotoGroup },
  // am 不需要 alt 填写, 且 desc 非必填
  inject: ['activityId', 'refLang', 'isEMpage'],
  provide() {
    return {
      updateData: this.updateData
    }
  },
  props: {
    currentCreateLang: {
      default: ''
    },
    limit: {
      default: 20
    },
    minBannerCount: {
      default: 0
    },
    // 图片 list, 所有语言
    listAll: {
      type: Array,
      default: () => [],
      required: true
    },
    // 是否必填
    required: {
      type: Boolean,
      default: false
    },
    // banner总数
    bannerCount: {
      type: Number,
      default: 3
    },
    triggerBannerCheck: {
      type: Number,
      default: 0
    },
    needDisplayIsCard: {
      type: Boolean,
      default: true
    },
    // 图片是否一定需要清晰
    bannerMustBeClear: {
      type: Boolean,
      default: false
    },
    needDesc: {
      type: Boolean,
      default: true
    },
    needSetNoCropBanner: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 使图片仅作用于当前语言
      cur_lang_only: false,
      // 当前语言的图片 list
      photo_list: [],
      // 所有语言的图片列表, 用于弹窗
      list_all_format: [],
      form: {}
    }
  },
  computed: {
    ...mapGetters(['isPublishWithAI']),
    curLang() {
      return this.currentCreateLang
    },
    isAdmin() {
      return klook.getPlatformRoleKey() === 'admin'
    },
    onlyEMPermission() {
      return this.$root.onlyEMPermission
    }
  },
  watch: {
    cur_lang_only: {
      handler(next) {
        if (this.offsetEffect) {
          this.offsetEffect = null
          return
        }
        // !删除当前语言下的图片
        // ✔️ -> x
        if (!next) {
          // savePhotos(this.activityId, params)
          // 细节看 needCacheCurrLangData 的赋值注释
          if (this.needCacheCurrLangData) {
            this.cacheCurrEditLanguagePhotoData = _.find((item) => {
              item.language_type === this.curLang
            })
            this.needCacheCurrLangData = null
          }
        }

        // ✔️ -> x 或者 x -> ✔️
        // 都需要把当前语言数据清空，之前接口 savePhotos 实现，现在改由前端完成
        this.$emit(
          'update:listAll',
          this.listAll.map((item) => {
            if (item.language_type === this.curLang) {
              item.images_groups = []
            }

            return item
          })
        )

        this.getCurLangPhoto()
      }
    },
    listAll() {
      this.getCurLangPhoto()
    }
  },
  mounted() {
    // rainweb mock
    this.getCurLangPhoto({
      initCurLangOnly: true
    })
  },
  methods: {
    bannerCountChange() {
      this.$emit('banner-count-change')
    },
    async changeListClearValidate(data) {
      this.$refs.form && this.$refs.form.clearValidate()

      if (this.cacheCurrList) {
        if (!_.isEqualWith(data, this.cacheCurrList)) {
          this.$emit('changeData')
        }
      } else {
        this.setCacheCurrListDataDebounce(data)
      }
    },
    setCacheCurrListDataDebounce: _.debounce(async function (data) {
      await this.$nextTick()
      this.cacheCurrList = _.cloneDeep(data)
    }, 100),
    getCurLangPhoto({ initCurLangOnly = false } = {}) {
      if (this.cur_lang_only && this.cacheCurrEditLanguagePhotoData) {
        this.photo_list = _.cloneDeep(this.cacheCurrEditLanguagePhotoData)
        this.cacheCurrEditLanguagePhotoData = null

        return
      }

      // 统一格式
      this.list_all_format = this.listAll.map((v) => {
        return {
          language_type: v.language_type,
          list: v.images_groups && v.images_groups.length ? v.images_groups : []
        }
      })

      //! 活动图片 ------------- 当前语言没有的话, 取 all, all没有再取 en_US 下的图片
      //! 第一步, 找到当前语言的图片
      let act_list = (this.list_all_format.find((v) => v.language_type === this.curLang) || {}).list || []
      // is_default 为 0 标识应用于当前图片
      if (initCurLangOnly) {
        if (act_list && act_list.length && act_list[0].is_default === 0) {
          this.offsetEffect = true // 需要抵消掉 watch 的操作
          this.cur_lang_only = true
        } else {
          this.cur_lang_only = false
        }
        // 初始化的时候，直接读取当前语言图片
        this.photo_list = act_list
        // 没勾选但初始化的时候扔需取当前语言数据
        // 此时的当前语言数据等于: ALL 图片数据 + 当前语言的 desc + alt
        // 当 ×(取当前语言数据取代 ALL) -> ✔️(这个时候，需要把上一步的数据缓存) -> ×(赋值缓存数据，免得丢失 alt 和 desc 的填写)
        if (!this.cur_lang_only) {
          this.needCacheCurrLangData = true
        }

        return
      }

      //! 第二步, 判断是否勾选了 "创建当前语言的图片"
      if (this.cur_lang_only) {
        this.photo_list = act_list
        return
      }

      //! 如果不是, 先读取 All
      let all = (this.list_all_format.find((v) => v.language_type === 'ALL') || {}).list || []
      //! 如果 all 存在, 读取 All, 但是需要读取当前语言保存好的 desc && alt
      if (all.length) {
        this.photo_list = all
        return
      }
      //! 最后, 如果 all 没有, 则读取 en_US
      if (!all.length) {
        act_list = (this.list_all_format.find((v) => v.language_type === 'en_US') || {}).list || []
      }
      this.photo_list = act_list
    },
    //! 从上传的图片中获取(复制图片)
    copyPhoto(images) {
      // console
      this.photo_list = [
        ...this.photo_list,
        ...images.map((v, i) => {
          // 图片排序 === 原图片顺序基础上递增
          v.display_order = this.photo_list.length + i + 1
          return v
        })
      ]
    },
    //! 获取数据
    getList() {
      return this.$refs.photo_group.getList()
    },
    // 校验
    validate() {
      return new Promise((resolve) => {
        this.$refs.form.validate(resolve)
      })
    },
    //! 获取接口需要的参数
    getFormatParams() {
      let params = []
      let cur_list = _.cloneDeep(this.getList())
      // ! 是否保存到当前语言, 是的话 is_default: 0
      let is_default = +!this.cur_lang_only
      let image = cur_list.reduce((acc, cur) => {
        let base = {
          alt: cur.alt,
          banner_display: +cur.banner_display,
          desc: cur.desc,
          display_order: cur.display_order,
          img_id: 0,
          is_default,
          is_card: cur.is_card ? 1 : 0
        }
        // delete cur.ORIGIN.url;
        // delete cur.DESKTOP.url;
        // delete cur.MOBILE.url;
        return [
          ...acc,
          ...[
            { ...cur.ORIGIN, ...base },
            { ...cur.DESKTOP, ...base },
            { ...cur.MOBILE, ...base }
          ]
        ]
      }, [])
      params = [
        {
          language_type: this.curLang,
          image
        }
      ]
      if (!this.cur_lang_only) {
        // !是否保存到当前语言, 否时, 同时添加到 ALL
        // } else {
        // ! All 不需要保留 alt , desc
        params.push({
          language_type: 'ALL',
          image: _.cloneDeep(image).map((v) => {
            v.desc = ''
            v.alt = ''
            return v
          })
        })
      }
      return params
    },
    // 校验
    getRules() {
      // 非必填(指数量), 即可以没有活动图片, 但是图片的字段是必填的
      let validation = [
        {
          required: true,
          validator: async (rule, value, callback) => {
            let res = await this.$refs.photo_group.validateFields()
            res.valid ? callback() : callback(new Error(res.message))
          }
        }
      ]
      if (this.required) {
        validation.push({
          required: true,
          validator: (rule, value, callback) => {
            let res = this.$refs.photo_group.validateLength()
            res.valid ? callback() : callback(new Error(res.message))
          }
        })
      }
      return validation
    }
  }
}
</script>
<style lang="scss">
.photo-activity {
  .ant-form-item-control-wrapper {
  }
}
</style>
