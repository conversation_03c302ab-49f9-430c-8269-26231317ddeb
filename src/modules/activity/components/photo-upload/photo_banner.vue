<template>
  <!-- banner 图片上传 -->
  <a-form-model ref="form" :model="form" label-width="200px" class="photo-banner">
    <a-form-model-item :rules="getRules()" prop="custom">
      <PhotoGroup
        ref="photo_group"
        :hide-copy="!!photo_list.length"
        :activity-id="activityId"
        :cur-lang="curLang"
        :ref-lang="refLang"
        :list="photo_list"
        :need-order="false"
        :need-desc="false"
        :need-alt="isAdmin"
        :disabled-alt="!isEMpage || isPublishWithAI"
        :need-display-as-banner="false"
        :allow-add="true"
        :allow-replace="false"
        :allow-remove="true"
        :list-all="list_all_format"
        :cur-lang-only.sync="cur_lang_only"
        :limit="1"
        :banner-count="bannerCount"
        :trigger-banner-check="triggerBannerCheck"
        :need-display-is-card="true"
        :upload-view="uploadView"
        :need-valid-alt="false"
        :bannerMustBeClear="bannerMustBeClear"
        v-bind="$attrs"
        v-on="$listeners"
        @current-list-change="changeListClearValidate"
        @copy-photo="copyPhoto"
        @validate-form="validate"
        @banner-count-change="$emit('banner-count-change')"
      />
      <p v-if="$attrs.description" class="text--italic">
        <!--{{ $attrs.description }}-->
      </p>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
// banner
//    am: photo desc 隐藏 , photo alt 隐藏
//    em: photo desc 隐藏 , photo alt 必填
import PhotoGroup from '../photo-group'
import { mapGetters } from 'vuex'

export default {
  name: 'PhotoBanner',
  components: { PhotoGroup },
  inject: ['activityId', 'refLang', 'isEMpage'],
  provide: function () {
    return {
      updateData: this.updateData
    }
  },
  props: {
    uploadView: {
      type: Object,
      default: null
    },
    currentCreateLang: {
      default: ''
    },
    // 图片 list, 所有语言
    listAll: {
      type: Array,
      default: () => [],
      required: true
    },
    // 是否必填
    required: {
      type: Boolean,
      default: false
    },
    // banner总数
    bannerCount: {
      type: Number,
      default: 3
    },
    triggerBannerCheck: {
      type: Number,
      default: 0
    },
    // 图片是否一定需要清晰
    bannerMustBeClear: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 使图片仅作用于当前语言
      cur_lang_only: false,
      // 当前语言的图片 list
      photo_list: [],
      // 所有语言的图片列表, 用于弹窗
      list_all_format: [],
      form: {}
    }
  },
  computed: {
    ...mapGetters(['isPublishWithAI']),
    curLang() {
      return this.currentCreateLang
    },
    isAdmin() {
      return klook.getPlatformRoleKey() === 'admin'
    },
    onlyEMPermission() {
      return this.$root.onlyEMPermission
    }
  },
  watch: {
    cur_lang_only: {
      handler(next) {
        if (this.offsetEffect) {
          this.offsetEffect = null
          return
        }
        // !删除当前语言下的图片
        // ✔️ -> x
        if (!next) {
          // savePhotos(this.activityId, params)
          // 细节看 needCacheCurrLangData 的赋值注释
          if (this.needCacheCurrLangData) {
            this.cacheCurrEditLanguagePhotoData = _.find((item) => {
              item.language_type === this.curLang
            })
            this.needCacheCurrLangData = null
          }
        }

        // ✔️ -> x 或者 x -> ✔️
        // 都需要把当前语言数据清空，之前接口 savePhotos 实现，现在改由前端完成
        this.$emit(
          'update:listAll',
          this.listAll.map((item) => {
            if (item.language_type === this.curLang) {
              item.images_groups = []
            }

            return item
          })
        )

        this.getCurLangPhoto()
      }
    },
    listAll() {
      this.getCurLangPhoto()
    }
  },
  mounted() {
    // rainweb mock
    this.getCurLangPhoto({
      initCurLangOnly: true
    })
  },
  methods: {
    changeListClearValidate(data) {
      this.$refs.form && this.$refs.form.clearValidate()

      if (this.cacheCurrList) {
        if (!_.isEqualWith(data, this.cacheCurrList)) {
          this.$emit('changeData')
        }
      } else {
        this.setCacheCurrListDataDebounce(data)
      }
    },
    setCacheCurrListDataDebounce: _.debounce(async function (data) {
      await this.$nextTick()
      this.cacheCurrList = _.cloneDeep(data)
    }, 100),
    getCurLangPhoto({ initCurLangOnly = false } = {}) {
      if (this.cur_lang_only && this.cacheCurrEditLanguagePhotoData) {
        this.photo_list = _.cloneDeep(this.cacheCurrEditLanguagePhotoData)
        this.cacheCurrEditLanguagePhotoData = null

        return
      }

      // 转换所有语言 banner 的格式
      this.list_all_format = this.listAll.map((v) => {
        return {
          language_type: v.language_type,
          list: v.image && v.image.length ? [{ ...v.image[0], image: v.image }] : []
        }
      })
      //! 找到当前语言的图片列表 -------------
      //! 第一步, 找到当前语言的图片
      let banner_list = (this.list_all_format.find((v) => v.language_type === this.curLang) || {}).list || []
      //! is_default 为 0 标识应用于当前图片
      if (initCurLangOnly) {
        if (banner_list && banner_list.length && banner_list[0].is_default === 0) {
          this.offsetEffect = true // 由 false -> true 会触发 watch，需要抵消掉
          this.cur_lang_only = true
        } else {
          this.cur_lang_only = false
        }
        // 初始化的时候，直接读取当前语言图片
        this.photo_list = banner_list
        // 没勾选但初始化的时候扔需取当前语言数据
        // 此时的当前语言数据等于: ALL 图片数据 + 当前语言的 desc + alt
        // 当 ×(取当前语言数据取代 ALL) -> ✔️(这个时候，需要把上一步的数据缓存) -> ×(赋值缓存数据，免得丢失 alt 和 desc 的填写)
        if (!this.cur_lang_only) {
          this.needCacheCurrLangData = true
        }

        return
      }

      //! 第二步, 判断是否勾选了 "创建当前语言的图片"
      if (this.cur_lang_only) {
        this.photo_list = banner_list
        return
      }

      //! 如果不是, 先读取 All
      let all = (this.list_all_format.find((v) => v.language_type === 'ALL') || {}).list || []
      //! 如果 all 存在, 读取 All, 但是需要读取当前语言保存好的 desc && alt
      if (all.length) {
        this.photo_list = all
        return
      }
      //! 最后, 如果 all 没有, 则读取 en_US
      if (!all.length) {
        banner_list = (this.list_all_format.find((v) => v.language_type === 'en_US') || {}).list || []
      }
      this.photo_list = banner_list
    },
    //! 从上传的图片中获取(复制图片)
    copyPhoto(images) {
      images[0].display_order = 1
      this.photo_list = [images[0]]
    },
    //! 获取数据
    getList() {
      return this.$refs.photo_group.getList()
    },
    // 校验
    validate() {
      return new Promise((resolve) => {
        this.$refs.form.validate(resolve)
      })
    },
    //! 转换为参数格式的数据
    getFormatParams() {
      let params = []
      let cur_list = _.cloneDeep(this.getList())
      // ! 是否保存到当前语言, 是的话 is_default: 0
      let is_default = +!this.cur_lang_only
      let image = cur_list.map((v) => {
        let base = {
          alt: v.alt,
          banner_display: +v.banner_display,
          desc: v.desc,
          display_order: v.display_order,
          img_id: 0,
          is_default,
          is_card: v.is_card ? 1 : 0
        }
        // delete v.ORIGIN.url;
        // delete v.DESKTOP.url;
        // delete v.MOBILE.url;
        return [
          { ...v.ORIGIN, ...base },
          { ...v.DESKTOP, ...base },
          { ...v.MOBILE, ...base }
        ]
      })[0]

      // 如果只作用于当前语言, 只传到当前语言
      params = [
        {
          language_type: this.curLang,
          image
        }
      ]
      if (!this.cur_lang_only) {
        // !是否保存到当前语言, 否时, 同时添加到 ALL
        // } else {
        // ! All 不需要保留 alt , desc
        params.push({
          language_type: 'ALL',
          image: _.cloneDeep(image || []).map((v) => {
            v.desc = ''
            v.alt = ''
            return v
          })
        })
        // }
      }
      return params
    },
    // 校验
    getRules() {
      // 非必填(指数量), 即可以没有活动图片, 但是图片的字段是必填的
      let validation = [
        {
          required: this.required,
          validator: async (rule, value, callback) => {
            let res = await this.$refs.photo_group.validateFields()
            res.valid ? callback() : callback(new Error(res.message))
          }
        }
      ]
      if (this.required) {
        validation.push({
          required: true,
          validator: (rule, value, callback) => {
            let res = this.$refs.photo_group.validateLength()
            res.valid ? callback() : callback(new Error(res.message))
          }
        })
      }

      return validation
    }
  }
}
</script>
<style lang="scss">
.photo-banner {
  .ant-form-item-control-wrapper {
  }
}
</style>
