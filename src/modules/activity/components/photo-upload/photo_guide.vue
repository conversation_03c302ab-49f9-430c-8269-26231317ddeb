<template>
  <!-- banner 图片上传 -->
  <div>
    <a-form-model :model="form" ref="form" label-width="0" class="photo-guide">
      <DisabledWidgetLayer :data="data ? data[0] : {}" />
      <a-form-model-item :rules="getRules()" prop="custom">
        <!-- <div>
          Images uploaded in en_US will be displayed at frontend in all
          languages by default, except when images are being uploaded in the
          language then the frontend will customise. After deleting the
          per-language customise image, and if there are images uploaded in
          en_US, then frontend will display the one in en_US instead.
        </div>-->
        <PhotoGroup
          ref="photo_group"
          v-bind="$attrs"
          :activity-id="activity_id"
          :cur-lang="edit_lang"
          :ref-lang="ref_lang"
          :list="photo_list"
          :need-order="true"
          :need-desc="true"
          :need-alt="isEMpage"
          :need-custom-size="true"
          :need-set-no-crop-banner="false"
          :need-display-as-banner="false"
          :allow-add="allow_add_and_remove"
          :allow-replace="false"
          :allow-remove="allow_add_and_remove"
          :list-all="list_all_format"
          :cur-lang-only.sync="cur_lang_only"
          :show-cur-lang-only="false"
          :allow-download-image="false"
          :is-single="true"
          :need-valid-desc="false"
          :limit="limit"
          :size-conf="sizeConf"
          :disabled-desc="isPublishWithAI"
          :disabled-alt="isPublishWithAI"
          @copy-photo="copyPhoto"
        />
      </a-form-model-item>
    </a-form-model>
  </div>
</template>
<script>
// activity 或 guide
//   am: photo desc 选填 , photo alt 隐藏
//   em: photo desc 选填 , photo alt 必填
import PhotoGroup from '../photo-group'
import { getAllImage } from './api'
import DisabledWidgetLayer from '@activity/components/DisabledWidgetLayer.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'PhotoGuide',
  components: { DisabledWidgetLayer, PhotoGroup },
  inject: ['activity_id', 'edit_lang', 'ref_lang', 'extra_id', 'extra_type', 'page_from'],
  provide: function () {
    return {
      updateData: this.updateData
    }
  },
  // v-model
  model: {
    prop: 'data',
    event: 'change'
  },
  props: {
    // 所有语言的图片
    listAll: {
      type: Array,
      default: () => []
    },
    //! attr_values 当前语言数据
    data: {
      type: Array,
      default: () => []
    },
    //! 参考语言数据
    // eslint-disable-next-line
    refData: {
      type: Array,
      default: () => []
    },
    // 尺寸设置
    sizeConf: {
      type: Object,
      default: () => ({
        width: 1295,
        height: 720
      })
    },
    //! widget 配置
    ui: {
      type: Object,
      default: () => ({})
    },
    //! 是否必填
    required: {
      type: Boolean,
      default: false
    },
    // 所有语言
    languageAll: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {},
      // 使图片仅作用于当前语言
      cur_lang_only: false,
      // 当前语言的图片 list
      photo_list: [],
      // 所有语言的图片列表, 用于弹窗
      list_all_format: []
    }
  },
  computed: {
    ...mapGetters(['isPublishWithAI']),
    isEMpage() {
      return this.$root.roles.is_em
    },
    limit() {
      //! 图片数量限制
      return Number(_.get(this.ui, 'ui_conf.config.img_upload_limit') || 1000)
    },
    //! 当前活动有什么语言
    // ...mapGetters({ all_language: "getLanguage" }),
    // 只有已添加的语言才作为判断
    all_allow_language() {
      return (this.languageAll.filter((v) => v.status !== 0) || []).map((v) => v.language)
    },
    //! 当只有一个语言或者当前语言为英语时, 才能新增 or 删除
    allow_add_and_remove() {
      let all = this.all_allow_language
      //! 只有一个, 且是当前语言, 允许编辑/增加
      if (all.length === 1 && all[0] === this.edit_lang) {
        return true
      }
      //! 有多个语言, 只允许英语编辑/增加
      if (all.length > 1 && this.edit_lang === 'en_US') {
        return true
      }
      // 其他情况不允许添加
      return false
    }
  },
  watch: {
    data: {
      immediate: true,
      handler(next, old) {
        if (!_.isEqual(next, old)) {
          this.getCurLangPhoto()
          this.mounted && this.$emit('changeList', next)
        }
      }
    }
  },
  async created() {
    this.getAllImage()
  },
  async mounted() {
    this.$emit('widget-mounted')
    this.getCurLangPhoto()
    await this.$nextTick()
    this.mounted = true
  },
  methods: {
    // getCurLangPhoto
    async getCurLangPhoto() {
      // 处理当前语言格式
      //! 需要展示value为空的, 为占位符
      this.photo_list = this.data.map((v, i) => this.formatPhotoConfig(v, i, v.id))
    },
    // 格式转换, 取value字段
    formatPhotoConfig(config, i, id) {
      let v = {
        ...(typeof config.value === 'object' ? config.value : JSON.parse(config.value || '{}'))
      }
      return {
        ...v,
        id,
        ash: config.ash,
        create: config.create || false,
        alt: v.alt_multilang || '',
        desc: v.desc_multilang || '',
        name: v.url || '',
        display_order: config.order || i + 1,
        order: config.order || i + 1,
        height: v.height || '',
        width: v.width || '',
        published: config.published === undefined ? -1 : config.published,
        instance_id: config.instance_id || '',
        image: [
          {
            ...v,
            height: v.height || '',
            width: v.width || '',
            name: v.url || '',
            pre_process: '',
            size_type: 'ORIGIN'
          },
          {
            ...v,
            height: v.height || '',
            width: v.width || '',
            name: v.url || '',
            pre_process: '',
            size_type: 'DESKTOP'
          },
          {
            ...v,
            height: v.height || '',
            width: v.width || '',
            name: v.url || '',
            pre_process: '',
            size_type: 'MOBILE'
          }
        ]
      }
    },
    //! 获取所有语言的图片
    async getAllImage() {
      let [res] = await getAllImage({
        extra_id: this.extra_id,
        extra_type: this.extra_type,
        widget_id: this.ui.id
      })
      // 处理接口错误
      if (res) {
        //! "从其他语言复制", 需要去掉 value 为空的
        let list_all_format = []
        res.free_text_language_groups.forEach((v) => {
          //! value 为空的图片不展示, 该语言下没有图片? 复制窗口不展示该语言
          let list = v.free_text_list.filter((v) => v.value)
          if (list) {
            list_all_format.push({
              language_type: v.language,
              list: list.map((v, i) => this.formatPhotoConfig(v, i, v.attr_value_id))
            })
          }
        })
        this.list_all_format = list_all_format
      }
      //  else {
      // this.$message.error("Fail to get Images");
      // }
    },
    //! 从上传的图片中获取(复制图片), 在指引图片,  该复制的逻辑是替换id相同的占位图
    copyPhoto(images) {
      let photo_list = [...this.photo_list]
      images.forEach((v) => {
        //! 根据id判断是不是已存在占位符
        let existIndex = photo_list.findIndex((photo) => photo.id === v.id)
        // 已存在, 替换
        if (existIndex > -1) {
          v.display_order = existIndex + 1
          photo_list[existIndex] = v
          return
        }
        v.display_order = photo_list.length + 1
        //! 不存在, 添加
        photo_list.push(v)
      })
      this.photo_list = photo_list
    },
    //! 获取数据
    getList() {
      return this.$refs.photo_group ? this.$refs.photo_group.getList() : []
    },
    //! 校验
    validateForm() {
      return new Promise((resolve) => {
        this.$refs.form.validate(resolve)
      })
    },
    //! 获取接口需要的参数
    getFormatParams() {
      let params = {}
      let cur_list = _.cloneDeep(this.getList())
      // cur_list = _.sortBy(cur_list, "order");
      params = cur_list.map((v) => {
        let obj = {
          id: +v.id || 0,
          ash: v.ash,
          create: v.create || false, // 是否为新建,
          order: +v.display_order,
          published: v.published === undefined ? -1 : v.published,
          instance_id: v.instance_id || '',
          value: JSON.stringify({
            alt_multilang: v.alt,
            desc_multilang: v.desc,
            height: v.height,
            width: v.width,
            url: v.name
          })
        }
        // 占位图片 value 为空
        if (!v.name) {
          obj.value = ''
        }
        return obj
      })
      return params
    },
    // 更新 v-model
    updateData() {
      if (this.$refs.photo_group) {
        this.$emit('change', this.getFormatParams())
      }
    },
    // 校验
    getRules() {
      // 非必填(指数量), 即可以没有活动图片, 但是图片的字段是必填的
      let validation = [
        {
          required: this.required,
          validator: async (rule, value, callback) => {
            let res = await this.$refs.photo_group.validateFields()
            res.valid ? callback() : callback(new Error(res.message))
          }
        }
      ]
      if (this.getList().length && this.limit) {
        validation.push({
          required: this.required,
          validator: (rule, value, callback) => {
            let res = this.$refs.photo_group.validateLimit()
            res.valid ? callback() : callback(new Error(res.message))
          }
        })
      }
      if (this.required) {
        validation.push({
          required: true,
          validator: (rule, value, callback) => {
            let res = this.$refs.photo_group.validateLength()
            res.valid ? callback() : callback(new Error(res.message))
          }
        })
      }
      return validation
    }
  }
}
</script>
<style lang="scss">
.photo-guide {
  position: relative;

  .ant-form-item-control-wrapper {
    width: 100%;
  }
}
</style>
