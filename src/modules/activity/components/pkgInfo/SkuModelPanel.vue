<template>
  <div class="sku-model-wrap">
    <div v-if="skuModelFinished" class="sku-model-panel">
      <div class="sku-model-panel-title">
        <SkuModelPanelBox
          :title="$t('27725')"
          :text="timeslotType"
          :info="timeslotInfo"
          style="width: 100%;"
        />
        <a-button type="primary" @click="handleViewAndEdit">
          {{ $t('package_list_modify') }}
        </a-button>
      </div>
      <div class="sku-model-panel-content">
        <SkuModelPanelBox v-if="!isPICSku" :title="$t('27732')" :text="inventoryModel" />
        <SkuModelPanelBox :title="$t('pm_price_model')" :text="priceModel" />
        <SkuModelPanelBox :title="$t('package_merchant_currency')" :text="skuModel.merchant_currency" />
        <SkuModelPanelBox :title="$t('package_retail_selling_currency')" :text="skuModel.selling_currency" />
      </div>
      <SkuModelDrawer :visible.sync="skuModelVisible" />
    </div>
    <template v-if="skuModelFinished">
      <a-alert
        v-if="isPICSku"
        type="warning"
        style="margin-top: 10px;font-size: 12px;"
        :message="$t('48292')"
      ></a-alert>
      <a-alert v-if="promoTipData.is_price_engine" style="margin: 10px 0;" :show-icon="true" type="warning">
        <div slot="message" v-html="$t('30720')"></div>
      </a-alert>
      <a-alert v-if="promoTipData.is_promotion" style="margin: 10px 0;" :show-icon="true" type="warning">
        <div slot="message" v-html="calcTipNodes"></div>
      </a-alert>
    </template>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { getWeekNames } from '@activity/pages/package/units/utils/sku-model'
import SkuModelDrawer from '@activity/pages/package/units/components/SkuModelDrawer.vue'
import { INVENTORY_MODELS, PRICE_MODELS } from '@activity/pages/package/package_const.js'
import SkuModelPanelBox from './SkuModelPanelBox'

export default {
  name: 'SkuModelPanel',
  components: {
    SkuModelPanelBox,
    SkuModelDrawer
  },
  data() {
    return {
      promoTipData: {
        is_price_engine: false,
        is_promotion: false,
        promotion_event_name: null
      },
      skuModelVisible: false
    }
  },
  computed: {
    ...mapState({
      skuModel: (state) => state.skuModel
    }),
    isPICSku() {
      return this.skuModel.inv_model === 4
    },
    ...mapGetters(['skuModelFinished']),
    activity_id() {
      return klook.parse2id(this.$route.params.id)
    },
    calcTipNodes() {
      let arr = this.promoTipData.promotion_event_name || []
      let url =
        'https://docs.google.com/spreadsheets/d/16RfAWFZTDUGeEszBQSkBhnMAxG37_QV5oqO8U5HnKAQ/edit#gid=423486044'
      let nodes = __('30722', {
        event_name: arr.join(','),
        MULTILANG_URL_PLACEHOLDER: `javascript:window.open('${url}')`
      })
      return nodes
    },
    timeslotType() {
      return this.skuModel.timeslot_model.timeslot_type === 0 ? this.$t('27726') : this.$t('27727')
    },
    timeslotInfo() {
      return `(${getWeekNames(this.skuModel)})`
    },
    inventoryModel() {
      const target = INVENTORY_MODELS.find((item) => item.value === this.skuModel.inv_model)
      return target ? target.text : ''
    },
    priceModel() {
      const target = PRICE_MODELS.find((item) => item.value === this.skuModel.price_model)
      return target ? target.text : ''
    }
  },
  watch: {
    skuModelFinished: {
      immediate: true,
      handler(val) {
        val && this.get_price_influence()
      }
    }
  },
  methods: {
    get_price_influence() {
      if (this.$store.state.isMC2BD) return
      let cacheData = klook.getLocalStorage(ADMIN_API.act.get_price_influence) || {}
      if (cacheData.activity_id == this.activity_id && klook.isCacheTime(cacheData)) {
        this.promoTipData = cacheData
        return
      }
      ajax
        .get(ADMIN_API.act.get_price_influence, {
          params: {
            activity_id: this.activity_id
          }
        })
        .then((res) => {
          if (klook.getType(res) === 'Object') {
            this.promoTipData = res
            klook.setLocalStorage(ADMIN_API.act.get_price_influence, {
              ...res,
              maxAge: 60 * 1000 * 15, // 15 mins
              cacheTime: new Date().getTime()
            })
          }
        })
    },
    handleViewAndEdit() {
      this.skuModelVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.sku-model-panel {
  position: relative;
  padding: 12px 20px;
  background-color: #fff;

  &:before {
    content: '';
    position: absolute;
    left: 20px;
    top: 0;
    width: calc(100% - 40px);
    height: 0;
    border-top: 1px solid #e8e8e8;
  }

  &-border {
    width: 100%;
    height: 0;
    border-top: 1px solid #e8e8e8;
  }

  &-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &-content {
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
}
</style>
