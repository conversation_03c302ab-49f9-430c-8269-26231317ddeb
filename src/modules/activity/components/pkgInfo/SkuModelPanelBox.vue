<template>
  <div class="sku-model-panel-box">
    <p class="title">{{ title }}</p>
    <slot name="content-slot">
      <p class="text">
        {{ text }}
        <span v-if="info" class="info">{{ info }}</span>
      </p>
    </slot>
  </div>
</template>

<script>
export default {
  name: 'SkuModelPanelBox',
  props: {
    title: {
      type: String,
      required: true
    },
    text: {
      type: String,
      required: true
    },
    info: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.sku-model-panel-box {
  font-size: 12px;
  line-height: 14px;
  width: 240px;

  p {
    margin: 0;
  }

  .title {
    padding-bottom: 4px;
    color: rgba(0, 0, 0, 0.65);
  }

  .text {
    font-weight: 600;
    color: #000;
  }

  .info {
    color: rgba(0, 0, 0, 0.45);
  }
}
</style>
