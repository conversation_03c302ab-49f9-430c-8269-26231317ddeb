<template>
  <div id="pkg-info-editable" class="pkg-info">
    <div v-show="!categoryInfo.leaf_category_id" class="common-spin-style">
      <a-spin />
    </div>
    <rejectInfoAlert
      v-if="categoryInfo.reject_reason && categoryInfo.reject_reason.reject_reason_text"
      :reject-info="categoryInfo.reject_reason"
    />
    <div v-show="categoryInfo.leaf_category_id" class="pkg-info-box">
      <div class="flex-box act-info">
        <div class="title-box">
          <div class="block-box">
            <p class="text-box">{{ $t('package_info_name') }}:</p>
            <p class="value-box">
              <span class="title">{{ calcTitle }}</span>
            </p>
          </div>
        </div>
        <div class="other-box">
          <div class="block-box">
            <p class="text-box">{{ $t('price_engine_sub-category') }}:</p>
            <p class="value-box">
              <span class="sub-category">{{ categoryInfo.sub_category_name }}</span>
            </p>
          </div>
          <div class="block-box">
            <p class="text-box">{{ $t('package_list_package_status') }}:</p>
            <p class="value-box">
              <span
                :class="[
                  'common-status-style',
                  mapsFilterFn('package_status', categoryInfo.package_status, 'class')
                ]"
              >
                {{ mapsFilterFn('package_status', categoryInfo.package_status) || '--' }}
              </span>
              <span v-if="pkgIsGeneralSuspend || pkgIsWithWarmUp" class="with-warm-up">
                <span v-if="pkgIsWithWarmUp">{{ $t('79648') }}</span>
                <a-icon
                  v-tooltip="{
                    visible: true,
                    placement: 'bottomRight',
                    content: pkgIsGeneralSuspend ? $t('79635') : $t('79649')
                  }"
                  style="margin-left: 6px"
                  type="info-circle"
                />
              </span>
            </p>
          </div>
          <div class="block-box">
            <p class="text-box">{{ $t('actCreate.leafCategory') }}:</p>
            <p class="value-box">
              <span
                class="leaf-category cs-pointer"
                :data-spm-module="getActleafcatSpm"
                data-spm-virtual-item="__virtual"
                @click="clickLeafCategoryBtn"
              >
                {{ categoryInfo.leaf_category_name }}
                <a-icon type="down" />
              </span>
            </p>
          </div>
          <div v-if="$store.state.isMC2BD" class="block-box">
            <p class="text-box">{{ $t('merchant_approve_status') }}:</p>
            <p class="value-box">
              <span
                :class="[
                  'common-status-style',
                  mapsFilterFn('approval_status', categoryInfo.approval_status, 'class')
                ]"
                >{{ mapsFilterFn('approval_status', categoryInfo.approval_status) }}
              </span>
            </p>
          </div>
          <div v-if="!$store.state.isMC2BD" class="block-box">
            <!-- am em 展示 -->
            <p class="text-box">{{ $t('merchant_id_tier') }}:</p>
            <p class="value-box common-two-line">
              <span
                >{{
                  `${categoryInfo.merchant_id || ''}-${categoryInfo.merchant_name || ''} (${
                    mapsFilterFn('merchant_tier', categoryInfo.merchant_tier) || ''
                  })`
                }}
              </span>
            </p>
          </div>
        </div>
      </div>
      <div class="flex-box submit-box">
        <template v-if="$root.isMerchant">
          <span v-auth="'pkgSubmit'">
            <a-button
              v-if="isSubmit"
              id="js-package-submit"
              type="primary"
              :disabled="!isSubmit"
              @click="clickSubmitPackage"
              >{{ $t('merchant_submit_package_action') }}</a-button
            >
          </span>
          <span v-auth="'pkgWithdraw'">
            <a-button
              v-if="categoryInfo.approval_status === 1"
              @click="handleWithdrawSubmit(categoryInfo, 'package')"
              >{{ $t('btn.withdrawSubmit') }}</a-button
            >
          </span>
          <div
            :title="$t('21837')"
            v-if="categoryInfo.approval_status === 1"
            class="padding-approve-wrap common-two-line break-word"
          >
            {{ $t('21837') }}
          </div>
        </template>
        <div v-else-if="$store.state.isMC2BD && categoryInfo.approval_status === 1">
          <span v-auth="'pkgApprove'" style="margin-right: 12px">
            <a-button type="primary" @click="handleApprove">{{ $t('approve') }} </a-button>
          </span>
          <span v-auth="'pkgReject'">
            <a-button @click="handleReject">{{ $t('reject') }}</a-button>
          </span>
        </div>
        <template v-else-if="!$store.state.isMC2BD">
          <span class="admin-btn-style" v-for="btn in adminObj.actBtns" :key="'act' + btn.key">
            <a-tooltip placement="right" overlayClassName="common-tooltip-style">
              <span
                slot="title"
                v-if="categoryInfo.activity_next_status && !categoryInfo.activity_next_status.editable"
                v-html="btn.authTip"
              ></span>
              <span v-if="getActBtnDisplayStatus(btn.status) || !!getActBtnDisabledTooltip(btn)">
                <a-button
                  v-bind="btn"
                  :disabled="
                    (categoryInfo.activity_next_status && !categoryInfo.activity_next_status.editable) ||
                    !!getActBtnDisabledTooltip(btn)
                  "
                  @click="adminObj.updateActStatus(btn)"
                >
                  {{ btn.text }}
                </a-button>
                <a-icon
                  v-if="!!getActBtnDisabledTooltip(btn)"
                  v-tooltip="{
                    visible: true,
                    placement: 'bottomRight',
                    content: getActBtnDisabledTooltip(btn)
                  }"
                  style="margin-left: 6px; cursor: pointer"
                  type="info-circle"
                />
              </span>
            </a-tooltip>
          </span>
          <span v-for="btn in adminObj.pkgBtns" :key="'pkg' + btn.key" class="admin-btn-style">
            <a-tooltip placement="right" overlay-class-name="common-tooltip-style">
              <span v-if="!pkgBtnEditable" slot="title" v-html="btn.authTip"></span>
              <span v-if="getPkgBtnDisplayStatus(btn.status) || !!getPkgBtnDisabledTooltip(btn)">
                <!-- 活动暂停销售，所有的套餐都不能发布 -->
                <a-button
                  v-bind="btn"
                  :disabled="!pkgBtnEditable || !!getPkgBtnDisabledTooltip(btn)"
                  @click="adminObj.updatePkgStatus(btn)"
                >
                  {{ btn.text }}
                </a-button>
                <a-icon
                  v-if="!!getPkgBtnDisabledTooltip(btn)"
                  v-tooltip="{
                    visible: true,
                    placement: 'bottomRight',
                    content: getPkgBtnDisabledTooltip(btn)
                  }"
                  style="margin-left: 6px; cursor: pointer"
                  type="info-circle"
                />
              </span>
            </a-tooltip>
          </span>
        </template>

        <SuspendBtn
          type="pkg"
          :btn-tooltip-msg="$t('79634')"
          :display-act-btn="includeSuspendActStatus"
          :display-pkg-btn="includeSuspendPkgStatus"
          @submit="refreshPage"
        />

        <a-button
          v-if="$store.state.isMC2BD"
          :data-spm-module="getApprovalSpm"
          data-spm-virtual-item="__virtual"
          type="link"
          @click="handleGoToApproveList"
        >
          {{ $t('48224') }}
        </a-button>
      </div>
    </div>
    <SkuModelPanel v-if="!is2_1Combo" />
    <createModal :categoryInfo="categoryInfo" :modal-data="modalData" />
    <stkModal :modal-data="stkData" />

    <approveList
      style="z-index: 999"
      v-if="approveList.visible"
      v-model="approveList.visible"
      v-bind="approveList"
    />
    <div id="submit_activity_spm" v-bind="$root.bindIHTrack({ type: 'module', spm: 'SubmitActivity' })"></div>
    <div id="submit_package_spm" v-bind="$root.bindIHTrack({ type: 'module', spm: 'SubmitPackage' })"></div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import createModal from '@activity/pages/activityManagement/create/modal'
import stkModal from '@activity/components/modalAntd/stkModal'
import handleLib from '@activity/pages/activityManagement/handleLib'
import SkuModelPanel from '@activity/components/pkgInfo/SkuModelPanel'
import maps from '@activity/utils/maps.js'
import ApproveList from '../../pages/activityManagement/approveList/index'
import actSuspendMixin from '@activity/components/actInfo/actSuspendMixin'
import approveDraftMerchantActMixin from '@activity/components/actInfo/mixins/approveDraftMerchantActMixin.js'
import publishDraftMerchantActMixin from '@activity/components/actInfo/mixins/publishDraftMerchantActMixin.js'
import rejectInfoAlert from '@activity/components/reject-info-alert/index.vue'

const suspend_mixin = {
  mixins: [actSuspendMixin],
  computed: {
    packageStatus() {
      // -1 // 无效状态
      // 0  // 未发布
      // 1  // 已发布
      // 2  // 暂停销售
      // 3  // 暂停销售 (预热中)
      if (!this.package_id) {
        return -1
      }

      return this.$store.state.currentPackageFloatingField?.package_status ?? -1
    },
    pkgIsWithWarmUp() {
      return this.packageStatus === 3
    },
    pkgIsGeneralSuspend() {
      return this.packageStatus === 2
    },
    includeSuspendPkgStatus() {
      return this.getPkgBtnDisplayStatus(2)
    },
    pkgBtnEditable() {
      return this.categoryInfo.package_next_status?.editable ?? false
    },
    isCombo() {
      return this.categoryInfo.product_type === 1
    },
    comboVersion() {
      return this.categoryInfo.combo_version || ''
    },
    is2_1Combo() {
      return this.isCombo && this.comboVersion === 'Combo2.1'
    }
  },
  methods: {
    getPkgBtnDisplayStatus(status) {
      const status_list = this.categoryInfo?.package_next_status?.status_list ?? []

      return status_list.includes(status)
    },
    getPkgBtnDisabledTooltip(btn) {
      if (this.pkgIsWithWarmUp) {
        return this.$t('79647')
      }

      if (btn.key === 'publish_package' && this.actIsSuspended) {
        return this.$t('80127')
      }

      return ''
    }
  }
}

export default {
  name: 'pkg-info',
  mixins: [suspend_mixin, approveDraftMerchantActMixin, publishDraftMerchantActMixin],
  props: {
    categoryInfo: {
      default: () => {}
    }
  },
  inject: ['refreshPage'],
  components: { ApproveList, createModal, stkModal, SkuModelPanel, rejectInfoAlert },
  computed: {
    ...mapState({
      pkgStepStatusMap: 'pkgStepStatusMap'
    }),
    calcTitle() {
      let { categoryInfo } = this
      let title = `${categoryInfo.package_id || ''}-${categoryInfo.package_title || ''}`
      this.$emit('calcTitle', title)
      return title
    },
    activity_id() {
      return klook.parse2id(this.$route.params.id)
    },
    package_id() {
      return klook.parse2id(this.$route.query.package_id)
    },
    isSubmit() {
      let package_id = this.package_id
      if (!package_id) return false
      let { pkgStepStatusMap, statusObj, categoryInfo } = this
      let pkgStepStatus = pkgStepStatusMap[package_id] || {}
      for (let key in statusObj) {
        if (statusObj[key] !== pkgStepStatus[key]) return false
      }
      return categoryInfo.approval_status !== 1 && categoryInfo.approval_status !== 3
    },
    getActleafcatSpm() {
      const oid = `activity_${this.activityId || 0}`
      return `LeafCategoryChange?oid=${oid}&trg=manual`
    },
    getApprovalSpm() {
      const oid = `activity_${this.activityId || 0}`
      return `ApprovalList?oid=${oid}&trg=manual`
    }
  },
  data() {
    return {
      approveList: {
        visible: false,
        data: {},
        title: '',
        autoFillData: undefined
      },
      adminObj: {
        actBtns: maps.act_btns2admin,
        updateActStatus: (btn) => {
          let { operation_reason, status, status_list } = this.categoryInfo.activity_next_status
          // 0: 'Unpublished',
          // 1: 'Published',
          // 2: 'Draft',
          // 3: 'EM to edit',
          // 4: 'Preview'
          btn.activity_next_status = status
          btn.status_list = status_list

          let data = {
            activity_id: this.activity_id, // ,
            language: klook.getEditLang(), // draft == > em to edit状态时，需要该字段用于检查数据状态，其他状态不需要,
            page_from: klook.getPlatformRoleKey(), // ,
            status: btn.status,
            reason: '',
            reason_code: undefined
          }

          let reasons = operation_reason ? operation_reason.reasons : []
          handleLib.methods.handlerUpdateStatus.call(this, btn, {
            isAct: true,
            data,
            reasons,
            item: this.categoryInfo,
            suc: async (response) => {
              const flag = await this.handlePublishDraftMerchantActSuccessCb(response)
              if (flag) return

              klook.bus.$emit('initQueryData2bus')
              if (btn.key === 'submit_to_em') {
                this.$router.push({
                  path: `/act/activity/seo/${this.activity_id}`,
                  query: this.$route.query
                })
              }
            },
            err: (err) => {
              if (data.status === 1) {
                this.$root.trackIHEvent('#submit_activity_spm', {
                  err_message: err?.error?.message ?? 'SubmitActivity Error'
                })
              }
            }
          })
        },
        pkgBtns: maps.pkg_btns2admin,
        updatePkgStatus: async (btn) => {
          let data = {
            package_id: this.package_id, // ,
            status: btn.status, // ,
            page_from: klook.getPlatformRoleKey()
          }
          handleLib.methods.handlerUpdateStatus.call(this, btn, {
            isAct: false,
            data,
            suc: () => {
              this.refreshPage()
            },
            err: (err) => {
              if (data.status === 1) {
                this.$root.trackIHEvent('#submit_package_spm', {
                  err_message: err?.error?.message ?? 'SubmitPackage Error'
                })
              }
            }
          })
        }
      },
      statusObj: {
        extra_information: 1,
        package_basic_info: 1,
        package_detail_info: 1,
        price_inventory: 1
      },
      infoData: {
        title: ''
      },
      modalData: {
        visible: false
      },
      stkData: {
        activity_item: {},
        form: {
          note: '', // 原因,
          package_id_list: [
            // 活动和套餐必须都传，并且套餐属于活动,
          ]
        },
        row: {},
        keyFlag: 'package',
        visible: false,
        packageArr: [],
        on: {
          ok: async () => {
            let { stkData } = this
            // console.log(999, this.stkData.form, stkData.row)
            let actApproveStatus = stkData.activity_item.approval_status
            let isAct = stkData.keyFlag === 'activity'
            let isFlag = !isAct && [0, 2].includes(actApproveStatus) //特殊处理
            if (isFlag) {
              // 提交套餐判断活动是否可提交，可提交则调用提交活动接口，否则直接调用提交套餐接口
              stkData.form.package_id_list = [stkData.row.package_id]
            }
            let result =
              isAct || isFlag
                ? await handleLib.methods.submit_to_klook2activity({
                    activity_id: stkData.row.activity_id,
                    ...stkData.form
                  })
                : await handleLib.methods.submit_to_klook2package({
                    activity_id: stkData.row.activity_id,
                    note: stkData.form.note,
                    package_id: stkData.row.package_id
                  })
            stkData.visible = false
            if (result) {
              this.$set(this.categoryInfo, 'approval_status', 1)
              this.refreshPage()
            }
            stkData.form.note = ''
            stkData.form.package_id_list = []
          }
        }
      }
    }
  },
  methods: {
    mapsFilterFn: maps.filterFn,
    handleGoToApproveList() {
      this.$set(this, 'approveList', {
        visible: true,
        data: {
          activity_id: this.activity_id
        },
        title: __('48224'),
        autoFillData: {
          package_id: this.package_id
        }
      })
    },
    async handleApprove() {
      const confirm = await this.preApproveDraftMerActConfirm()
      if (!confirm) return

      handleLib.methods.handleApprove.call(this, this.categoryInfo, {
        isAct: false,
        suc: () => {
          this.$set(this.categoryInfo, 'approval_status', 3)
          klook.bus.$emit('initQueryData2bus')
        }
      })
    },
    handleReject() {
      handleLib.methods.handleReject.call(this, this.categoryInfo, {
        isAct: false,
        suc: () => {
          this.$set(this.categoryInfo, 'approval_status', 2)
          klook.bus.$emit('initQueryData2bus')
        }
      })
    },
    async clickSubmitPackage() {
      let row = this.categoryInfo
      let { activity_id, package_id } = this
      if (!package_id || !activity_id) return
      let result = await handleLib.methods.get_package_submit_status(row.package_id)
      if (result && result.status) {
        let arr = [{ ...row, checked: true }]
        this.stkData.keyFlag = 'package'
        this.stkData.packageArr = arr
        this.stkData.visible = true
        this.stkData.row = this.categoryInfo
        this.stkData.activity_item = result.activity_item
      }
    },
    clickLeafCategoryBtn() {
      if (this.$root.superEditLock) return
      this.modalData.visible = !this.modalData.visible
    },
    async handleWithdrawSubmit(row, keyFlag) {
      handleLib.methods.handleWithdrawSubmit.call(this, row, {
        keyFlag,
        suc: () => {
          this.$set(row, 'approval_status', 0)
          this.refreshPage()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../actInfo/index.scss';
.with-warm-up {
  margin-left: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #757575;

  .anticon {
    cursor: pointer;
  }
}
</style>
