<template>
  <div id="pkg-info-editable" class="pkg-info">
    <div v-show="!categoryInfo.leaf_category_id" class="common-spin-style">
      <a-spin />
    </div>
    <rejectInfoAlert
      v-if="categoryInfo.reject_reason && categoryInfo.reject_reason.reject_reason_text"
      :reject-info="categoryInfo.reject_reason"
    />
    <MerchantUnitHeader v-if="$route.name === 'packageUnit'" :data-unit-title="calcTitle" />
    <div v-else v-show="categoryInfo.leaf_category_id" class="pkg-info-box">
      <div class="flex-box act-info">
        <div class="title-box">
          <span class="title">{{ calcTitle }}</span>
        </div>
        <div class="other-box">
          <div class="block-box">
            <p class="text-box">{{ $t('package_list_package_status') }}:</p>
            <p class="value-box">
              <span
                :class="[
                  'common-merchant-status-style',
                  mapsFilterFn('package_status', categoryInfo.package_status, 'class')
                ]"
              >
                {{ mapsFilterFn('package_status', categoryInfo.package_status) || '--' }}
              </span>
              <span v-if="pkgIsGeneralSuspend || pkgIsWithWarmUp" class="with-warm-up">
                <span v-if="pkgIsWithWarmUp">{{ $t('79648') }}</span>
                <a-icon
                  v-tooltip="{
                    visible: true,
                    placement: 'bottomRight',
                    content: pkgIsGeneralSuspend ? $t('79635') : $t('79649')
                  }"
                  style="margin-left: 6px"
                  type="info-circle"
                />
              </span>
            </p>
          </div>
          <div v-if="$store.state.isMC2BD" class="block-box">
            <p class="text-box">{{ $t('merchant_approve_status') }}:</p>
            <p class="value-box">
              <span
                :class="[
                  'common-merchant-status-style',
                  mapsFilterFn('approval_status', categoryInfo.approval_status, 'class')
                ]"
              >
                {{ mapsFilterFn('approval_status', categoryInfo.approval_status) }}
              </span>
              <span
                v-if="$root.isMerchant"
                class="common-btn-link"
                :style="{ marginLeft: '8px' }"
                v-bind="$root.bindIHTrack({ type: 'module', spm: 'Approval_List' })"
                @click="handleGoToApproveList"
              >
                <a-tooltip :title="$t('48224')">
                  <a-icon type="file-text"></a-icon>
                </a-tooltip>
              </span>
            </p>
          </div>
        </div>
      </div>
      <div class="flex-box submit-box">
        <div class="dont-delete-this-div"></div>
        <div class="bottom-btns-box">
          <span
            v-if="!hidePkgSubmit"
            v-auth="'pkgSubmit'"
            id="js-package-submit"
            class="common-btn-link"
            @click="clickSubmitPackage"
          >
            <a-button type="primary">
              {{ $t('merchant_submit_package_action') }}
            </a-button>
          </span>
          <span
            v-if="categoryInfo.approval_status === 1"
            v-auth="'pkgWithdraw'"
            class="common-btn-link"
            @click="handleWithdrawSubmit(categoryInfo, 'package')"
          >
            {{ $t('btn.withdrawSubmit') }}
          </span>
          <span v-for="btn in adminObj.pkgBtns" :key="btn.status" class="common-btn-link">
            <a-button
              v-if="getPkgBtnDisplayStatus(btn.status)"
              v-bind="btn"
              :disabled="!pkgBtnEditable || !!getPkgBtnDisabledTooltip(btn)"
              @click="adminObj.updatePkgStatus(btn)"
            >
              {{ btn.text }}
            </a-button>
          </span>
        </div>
      </div>
    </div>
    <createModal :categoryInfo="categoryInfo" :modal-data="modalData" />
    <stkModal :modal-data="stkData" />

    <SubmitPkgResponseTable v-model="responseTable.visible" v-bind="responseTable" />

    <approveList
      style="z-index: 999"
      v-if="approveList.visible"
      v-model="approveList.visible"
      v-bind="approveList"
    />
    <div id="submit_package_spm" v-bind="$root.bindIHTrack({ type: 'module', spm: 'SubmitPackage' })"></div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import createModal from '@activity/pages/activityManagement/create/modal'
import handleLib from '@activity/pages/activityManagement/handleLib'
import MerchantUnitHeader from '@activity/components/pkgInfo/merchant-unit-header'
import maps from '@activity/utils/maps.js'
import ApproveList from '../../pages/activityManagement/approveList/index'
import actSuspendMixin from '@activity/components/actInfo/actSuspendMixin'
import SubmitPkgResponseTable from '@activity/components/pkgInfo/submit-pkg-response-table.vue'
import { EVENT_BUS_KEY } from '@activity/utils/const.js'
import merchantPkgInfoMixin from '@activity/components/pkgInfo/mixins/merchant-pkg-info.js'
import { bestMatchLang } from '@activity/utils'
import rejectInfoAlert from '@activity/components/reject-info-alert/index.vue'

const suspend_mixin = {
  mixins: [actSuspendMixin],
  computed: {
    packageStatus() {
      // -1 // 无效状态
      // 0  // 未发布
      // 1  // 已发布
      // 2  // 暂停销售
      // 3  // 暂停销售 (预热中)
      if (!this.package_id) {
        return -1
      }

      return this.$store.state.currentPackageFloatingField?.package_status ?? -1
    },
    pkgIsWithWarmUp() {
      return this.packageStatus === 3
    },
    pkgIsGeneralSuspend() {
      return this.packageStatus === 2
    },
    includeSuspendPkgStatus() {
      return this.getPkgBtnDisplayStatus(2)
    },
    pkgBtnEditable() {
      return this.categoryInfo.package_next_status?.editable ?? false
    }
  },
  methods: {
    getPkgBtnDisplayStatus(status) {
      const status_list = this.categoryInfo?.package_next_status?.status_list ?? []

      return status_list.includes(status)
    },
    getPkgBtnDisabledTooltip(btn) {
      if (this.pkgIsWithWarmUp) {
        return this.$t('79647')
      }

      if (btn.key === 'publish_package' && this.actIsSuspended) {
        return this.$t('80127')
      }

      return ''
    }
  }
}

const response_table_mixin = {
  components: {
    SubmitPkgResponseTable
  },
  data() {
    return {
      responseTable: {
        data: [],
        visible: false
      }
    }
  }
}

export default {
  name: 'MerchantPkgInfo',
  mixins: [suspend_mixin, response_table_mixin, merchantPkgInfoMixin],
  props: {
    categoryInfo: {
      default: () => {}
    }
  },
  inject: ['refreshPage'],
  components: { ApproveList, createModal, MerchantUnitHeader, rejectInfoAlert },
  computed: {
    ...mapState({
      pkgStepStatusMap: 'pkgStepStatusMap'
    }),
    ...mapGetters({
      requiredPackageIds: 'requiredPackageIdsGetter',
      getPkgInfos2getters: 'getPkgInfos2getters'
    }),
    allPackages() {
      return this.getPkgInfos2getters?.packages || []
    },
    calcTitle() {
      let { categoryInfo } = this
      let title = `${categoryInfo.package_id || ''}-${categoryInfo.package_title || ''}`
      this.$emit('calcTitle', title)
      return title
    },
    activity_id() {
      return klook.parse2id(this.$route.params.id)
    },
    package_id() {
      return klook.parse2id(this.$route.query.package_id)
    },
    hidePkgSubmit() {
      let package_id = this.package_id
      if (!package_id) return true
      let { categoryInfo } = this
      return categoryInfo.package_status === 1 || [1, 3].includes(categoryInfo.approval_status)
    },
    isSubmit() {
      let package_id = this.package_id
      if (!package_id) return false
      let { pkgStepStatusMap, statusObj, categoryInfo } = this
      let pkgStepStatus = pkgStepStatusMap[package_id] || {}
      for (let key in statusObj) {
        if (statusObj[key] !== pkgStepStatus[key]) return false
      }
      return categoryInfo.approval_status !== 1 && categoryInfo.approval_status !== 3
    },
    getActleafcatSpm() {
      const oid = `activity_${this.activityId || 0}`
      return `LeafCategoryChange?oid=${oid}&trg=manual`
    }
  },
  // eslint-disable-next-line vue/order-in-components
  data() {
    return {
      approveList: {
        visible: false,
        data: {},
        title: '',
        autoFillData: undefined
      },
      adminObj: {
        actBtns: maps.pkg_btns2admin,
        updateActStatus: (btn) => {
          let { operation_reason, status, status_list } = this.categoryInfo.activity_next_status
          // 0: 'Unpublished',
          // 1: 'Published',
          // 2: 'Draft',
          // 3: 'EM to edit',
          // 4: 'Preview'
          btn.activity_next_status = status
          btn.status_list = status_list

          let data = {
            activity_id: this.activity_id, // ,
            language: klook.getEditLang(), // draft == > em to edit状态时，需要该字段用于检查数据状态，其他状态不需要,
            page_from: klook.getPlatformRoleKey(), // ,
            status: btn.status,
            reason: '',
            reason_code: undefined
          }

          let reasons = operation_reason ? operation_reason.reasons : []
          handleLib.methods.handlerUpdateStatus.call(this, btn, {
            isAct: true,
            data,
            reasons,
            item: this.categoryInfo,
            suc: () => {
              klook.bus.$emit('initQueryData2bus')
              if (btn.key === 'submit_to_em') {
                this.$router.push({
                  path: `/act/activity/seo/${this.activity_id}`,
                  query: this.$route.query
                })
              }
            }
          })
        },
        pkgBtns: maps.pkg_btns2admin,
        updatePkgStatus: async (btn) => {
          let data = {
            package_id: this.package_id, // ,
            status: btn.status, // ,
            page_from: klook.getPlatformRoleKey()
          }
          handleLib.methods.handlerUpdateStatus.call(this, btn, {
            isAct: false,
            data,
            suc: () => {
              this.refreshPage()
            }
          })
        }
      },
      statusObj: {
        extra_information: 1,
        package_basic_info: 1,
        package_detail_info: 1,
        price_inventory: 1
      },
      infoData: {
        title: ''
      },
      modalData: {
        visible: false
      }
    }
  },
  methods: {
    mapsFilterFn: maps.filterFn,
    handleGoToApproveList() {
      this.$set(this, 'approveList', {
        visible: true,
        data: {
          activity_id: this.activity_id
        },
        title: __('48224'),
        autoFillData: {
          package_id: this.package_id
        }
      })
    },
    handleApprove() {
      handleLib.methods.handleApprove.call(this, this.categoryInfo, {
        isAct: false,
        suc: () => {
          this.$set(this.categoryInfo, 'approval_status', 3)
          klook.bus.$emit('initQueryData2bus')
        }
      })
    },
    handleReject() {
      handleLib.methods.handleReject.call(this, this.categoryInfo, {
        isAct: false,
        suc: () => {
          this.$set(this.categoryInfo, 'approval_status', 2)
          klook.bus.$emit('initQueryData2bus')
        }
      })
    },
    async clickSubmitPackage() {
      klook.bus.$emit(EVENT_BUS_KEY.closeSubmitDriver)

      // 校验活动状态
      let actStatusResp = await handleLib.methods.get_activity_submit_status(this.activity_id)
      if (!actStatusResp.result?.status) {
        this.$modal.error({
          title: __('merchant_submit_activity_action'),
          content: __('activity_required_fields')
        })
        return
      }

      let row = this.categoryInfo
      let { activity_id, package_id } = this
      if (!package_id || !activity_id) return

      const res = await handleLib.methods.get_package_submit_status(row.package_id)
      const result = res?.result ?? {}
      if (result && result.status) {
        const packageIdList = [row.package_id]
        const others = this.allPackages.reduce((acc, curr) => {
          if (curr.package_id !== row.package_id && !curr.approval_status) {
            const uncompleted = this.requiredPackageIds.includes(curr.package_id)
            !uncompleted && packageIdList.push(curr.package_id) // 已完成的需要自动勾选上
            acc.push({
              ...curr,
              disabled: false,
              package_title: bestMatchLang('name', 'language_type', curr.package_local),
              uncompleted
            })
          }

          return acc
        }, [])
        this.stkData.keyFlag = 'package'
        this.stkData.packageArr = [{ ...row, disabled: true }, ...others]
        this.stkData.form.package_id_list = packageIdList
        this.stkData.visible = true
        this.stkData.row = this.categoryInfo
        this.stkData.activity_item = result.activity_item
      } else {
        this.$modal.error({
          title: this.$t('merchant_submit_package_action'),
          content: result.hint || this.$t('81930')
        })
        this.$root.trackIHEvent('#submit_package_spm', {
          err_message: result.hint || this.$t('81930')
        })
      }
    },
    clickLeafCategoryBtn() {
      if (this.$root.superEditLock) return
      this.modalData.visible = !this.modalData.visible
    },
    async handleWithdrawSubmit(row, keyFlag) {
      handleLib.methods.handleWithdrawSubmit.call(this, row, {
        keyFlag,
        suc: () => {
          this.$set(row, 'approval_status', 0)
          this.refreshPage()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../actInfo/merchant-index.scss';
.with-warm-up {
  margin-left: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #757575;

  .anticon {
    cursor: pointer;
  }
}
</style>
