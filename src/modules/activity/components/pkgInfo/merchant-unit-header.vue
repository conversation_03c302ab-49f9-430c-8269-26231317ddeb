<template>
  <div class="unit-model-wrap">
    <div class="unit-model-panel">
      <div v-if="showTimeslotRule" class="flex-style">
        <div>
          <div v-if="calcRepeatTimeslot.length === 1" class="unit-model-panel-title">
            <SkuModelPanelBox class="custom-style" :title="$t('80901')" :text="calcRepeatDays" />
            <SkuModelPanelBox
              v-if="calcRepeatTimeslot[0].repeatTimeslotStr"
              class="custom-style"
              :title="$t('81819')"
              :text="calcRepeatTimeslot[0].repeatTimeslotStr"
            />
          </div>
          <div v-if="calcRepeatTimeslot.length > 1">
            <div class="unit-model-panel-title">
              <SkuModelPanelBox class="custom-style" :title="$t('81820')" :text="''">
                <div
                  slot="content-slot"
                  ref="repeatTimeslotRef"
                  :class="['repeat-timeslot-box', moreObj.status === 'more' && 'see-more']"
                >
                  <div class="text">
                    <span v-for="(obj, i) in calcRepeatTimeslot" :key="i" class="text-group">
                      {{ `${obj.groupList.join(', ')}: ${obj.repeatTimeslotStr}` }}
                    </span>
                  </div>
                  <span v-if="moreObj.status" class="more-btn" @click="clickMoreBtn(moreObj.status)">
                    <span class="more-text">
                      {{ $t(moreObj.status === 'more' ? '18210' : '17049') }}
                    </span>
                    <a-icon class="arrow-icon" :type="moreObj.status === 'more' ? 'down' : 'up'"></a-icon>
                  </span>
                </div>
              </SkuModelPanelBox>
            </div>
          </div>
        </div>
        <div class="btn-rule-settings-box">
          <span
            v-bind="$root.bindIHTrack({ type: 'module', spm: 'Edit_Rule' })"
            class="common-btn-link"
            @click="showSettings"
          >
            <a-icon class="btn-icon-left" type="setting" />
            <span class="btn-text">{{ $t('81802') }}</span>
          </span>
        </div>
      </div>
      <div v-if="ruleSetting && calcUnitData" class="unit-model-panel-content">
        <MerchantUnitInfo :unit-data="calcUnitData" />
        <div v-if="isSaved && showApprovalOperatorBar" class="approval-btn-box">
          <ApprovalOperatorBar
            ref="approvalOperatorBar"
            :unitData="calcUnitData"
            :actionsIsInline="true"
            :size="'default'"
            class="custom-btn-style"
          />
        </div>
      </div>
    </div>
    <div>
      <a-alert
        v-if="isPICSku"
        type="warning"
        style="margin-top: 10px; font-size: 12px"
        :message="$t('48292')"
      ></a-alert>
      <a-alert v-if="promoTipData.is_price_engine" style="margin: 10px 0" :show-icon="true" type="warning">
        <div slot="message" v-html="$t('30720')"></div>
      </a-alert>
      <a-alert v-if="promoTipData.is_promotion" style="margin: 10px 0" :show-icon="true" type="warning">
        <div slot="message" v-html="calcTipNodes"></div>
      </a-alert>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex'
import { get } from 'lodash'
import SkuModelPanelBox from './SkuModelPanelBox'
import MerchantUnitInfo from './merchant-unit-info.vue'
import { state as pkgsState } from '@activity/store/pkgs-cache-observable.js'
import ApprovalOperatorBar from '@activity/pages/package/units/unit/calendar-setting/components/approvalOperatorBar.vue'

import { INTERCEPTOR_BTN_TYPES } from '@activity/utils/const'
import { getApprovalOperatorBarFlag, getRepeatTimeslot } from '@activity/pages/package/units/utils/index.js'

export default {
  name: 'SkuModelPanel',
  props: {
    showTimeslotRule: {
      type: Boolean,
      default: false
    }
  },
  inject: ['beforeInterceptorConfirm', 'setBeforeEachSaveConfirmInterceptor'],
  components: {
    ApprovalOperatorBar,
    MerchantUnitInfo,
    SkuModelPanelBox
  },
  data() {
    return {
      moreObj: {
        status: ''
      },
      pkgsSkuListCacheState: pkgsState[ADMIN_API.act.query_package_sku_list],
      promoTipData: {
        is_price_engine: false,
        is_promotion: false,
        promotion_event_name: null
      },
      skuModelVisible: false
    }
  },
  computed: {
    ...mapState({
      skuModel: (state) => state.skuModel,
      ruleSetting: (state) => state.skuRuleSetting
    }),
    isSaved() {
      return !!this.sku_id
    },
    isToBeSubmitted() {
      return this.calcUnitData?.ticket_status === 'ToBeSubmitted'
    },
    showApprovalOperatorBar() {
      return getApprovalOperatorBarFlag(this.calcUnitData?.ticket_status)
    },
    isPICSku() {
      return this.skuModel?.inv_model === 4
    },
    ...mapGetters(['skuModelFinished']),
    package_id() {
      return klook.parse2id(this.$route.query.package_id)
    },
    calcUnitData() {
      const { pkgsSkuListCacheState, package_id, sku_id } = this
      const units = get(pkgsSkuListCacheState, `${package_id}.getQueryPackageSkuList.result.units`, [])
      const unitData = units.find((o) => o.sku_id === sku_id)
      return unitData
    },
    calcRepeatDays() {
      const { repeat_day } = this.ruleSetting || {}
      return repeat_day.join(', ')
    },
    calcRepeatTimeslot() {
      const { by_time_repeat_timeslot, repeat_day } = this.ruleSetting || {}
      return getRepeatTimeslot(by_time_repeat_timeslot, repeat_day)
    },
    sku_id() {
      return klook.parse2id(this.$route.query.sku_id)
    },
    activity_id() {
      return klook.parse2id(this.$route.params.id)
    },
    calcTipNodes() {
      let arr = this.promoTipData.promotion_event_name || []
      let url =
        'https://docs.google.com/spreadsheets/d/16RfAWFZTDUGeEszBQSkBhnMAxG37_QV5oqO8U5HnKAQ/edit#gid=423486044'
      let nodes = __('30722', {
        event_name: arr.join(','),
        MULTILANG_URL_PLACEHOLDER: `javascript:window.open('${url}')`
      })
      return nodes
    }
  },
  watch: {
    sku_id: {
      handler() {
        this.getSkuRuleSetting()
      }
    },
    calcRepeatTimeslot: {
      handler(val) {
        this.$nextTick(() => {
          this.initView()
        })
      }
    },
    skuModelFinished: {
      immediate: true,
      handler(val) {
        val && this.get_price_influence()
      }
    }
  },
  async created() {
    await this.getSkuRuleSetting()
  },
  mounted() {
    this.setBeforeEachSaveConfirmInterceptor({
      customConf: {
        before: this.beforeUnloadInterceptor.bind(this)
      }
    })
  },
  methods: {
    ...mapActions(['actionGetSkuRuleSetting']),
    async beforeUnloadInterceptor() {
      if (this.isSaved && this.isToBeSubmitted) {
        return await this.beforeInterceptorConfirm({
          title: this.$t('174943'),
          content: this.$t('175129'),
          closable: false,
          cancelText: this.$t('174945'),
          okText: this.$t('174946'),
          ok: {
            value: INTERCEPTOR_BTN_TYPES.stay,
            callback: () => {
              this.$refs.approvalOperatorBar?.handleSubmitCost?.()
            }
          }
        })
      }

      return true
    },
    showSettings() {
      klook.bus.$emit('showRuleSetting')
    },
    clickMoreBtn(str) {
      this.moreObj.status = str === 'more' ? 'less' : 'more'
    },
    initView() {
      const ref = this.$refs.repeatTimeslotRef
      if (ref?.clientHeight > 25) {
        this.moreObj.status = 'more'
      }
    },
    async getSkuRuleSetting() {
      const res = await this.actionGetSkuRuleSetting({
        sku_id: this.sku_id,
        language: klook.getEditLang()
      })
      if (res?.unfinished) {
        klook.bus.$emit('showRuleSetting')
      }
    },
    get_price_influence() {
      if (this.$store.state.isMC2BD) return
      let cacheData = klook.getLocalStorage(ADMIN_API.act.get_price_influence) || {}
      if (cacheData.activity_id == this.activity_id && klook.isCacheTime(cacheData)) {
        this.promoTipData = cacheData
        return
      }
      ajax
        .get(ADMIN_API.act.get_price_influence, {
          params: {
            activity_id: this.activity_id
          }
        })
        .then((res) => {
          if (klook.getType(res) === 'Object') {
            this.promoTipData = res
            klook.setLocalStorage(ADMIN_API.act.get_price_influence, {
              ...res,
              maxAge: 60 * 1000 * 15, // 15 mins
              cacheTime: new Date().getTime()
            })
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.approval-btn-box {
  padding: 0 20px 12px 20px;
  background-color: #fff;
}
.repeat-timeslot-box {
  display: flex;
  align-items: flex-end;
  &.see-more {
    .text {
      display: inline-block;
      overflow: hidden;
      height: 22px;
    }
  }
  .more-btn {
    flex: none;
    color: #437dff;
    cursor: pointer;
    font-size: 0;
    .more-text {
      font-size: 14px;
    }
    .arrow-icon {
      margin-left: 8px;
      font-size: 12px;
    }
  }
}
.common-btn-link {
  color: #437dff;
  cursor: pointer;
  font-size: 14px;
  line-height: 22px;
  .btn-icon-left {
    margin-right: 8px;
  }
  &.danger {
    color: #ff4d4f;
  }
}
.btn-rule-settings-box {
  margin: 0 0 0 32px;
  flex: none;
}
.flex-style {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}
.unit-model-panel {
  position: relative;

  &-border {
    width: 100%;
    height: 0;
    border-top: 1px solid #e8e8e8;
  }

  &-title {
    display: flex;
    align-items: flex-start;
    .custom-style {
      width: auto;
      &:not(:last-of-type) {
        margin-right: 72px;
      }
      ::v-deep .title {
        font-size: 12px;
        line-height: 20px;
        padding-bottom: 0;
      }
      .text,
      ::v-deep .text {
        font-size: 14px;
        line-height: 22px;
      }
      .text .text-group:not(:last-of-type) {
        margin-right: 32px;
        &::after {
          content: '';
          position: relative;
          left: 16px;
          display: inline-block;
          width: 1px;
          height: 14px;
          background-color: #d9d9d9;
          vertical-align: middle;
        }
      }
    }
  }

  &-content {
    margin-top: 16px;
  }
}
</style>
