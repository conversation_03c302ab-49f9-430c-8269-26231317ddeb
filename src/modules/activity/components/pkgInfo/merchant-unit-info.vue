<template>
  <div id="pkg-info-editable" class="pkg-info">
    <div class="pkg-info-box">
      <div class="flex-box act-info">
        <div class="title-box">
          <span class="title">{{ calcSkuInfo.sku_name }}</span>
          <!-- "share_rel_ship"`    // 0 无共享， 1 子sku, 2 主sku -->
          <div
            v-if="unitData.share_rel_ship !== 0"
            class="tag"
            :class="{ '--is-main': unitData.share_rel_ship === 2 }"
          >
            {{ unitData.share_rel_ship === 2 ? $t('package_unit_main') : $t('89149') }}
          </div>
        </div>
        <div class="other-box">
          <div class="block-box">
            <p class="text-box">{{ $t('29171') }}</p>
            <p class="value-box">
              <span>{{ calcSkuInfo.sku_id }}</span>
            </p>
          </div>
          <div class="block-box">
            <p class="text-box">{{ $t('48223') }}</p>
            <p class="value-box">
              <span
                :class="[
                  'common-merchant-status-style',
                  mapsFilterFn('sku_status2merchant', Number(calcSkuInfo.published), 'class')
                ]"
              >
                {{ mapsFilterFn('sku_status2merchant', Number(calcSkuInfo.published)) || '--' }}
              </span>
            </p>
          </div>
          <div v-if="calcSkuInfo.ticket_status" class="block-box">
            <p class="text-box">{{ $t('48068') }}</p>
            <p class="value-box">
              <span
                v-if="calcSkuInfo.ticket_status"
                :class="[
                  'common-merchant-status-style',
                  mapsFilterFn('ticket_status', calcSkuInfo.ticket_status, 'class')
                ]"
              >
                {{ mapsFilterFn('ticket_status', calcSkuInfo.ticket_status) }}
              </span>
              <span v-else>{{ emptyStr }}</span>
            </p>
          </div>
        </div>
      </div>
      <div class="flex-box submit-box">
        <div class="dont-delete-this-div">
          <span v-if="!lockMerchantActEditing" class="common-btn-link">
            <a-button v-if="calcSkuInfo.published" :loading="loading" type="primary" @click="handlePublish">
              {{ $t('package_unit_unpublish') }}
            </a-button>
            <a-button v-if="!calcSkuInfo.published" :loading="loading" type="primary" @click="handlePublish">
              {{ $t('package_unit_publish') }}
            </a-button>
          </span>
        </div>
        <div v-if="!lockMerchantActEditing">
          <template v-if="!calcSkuInfo.published">
            <a-popconfirm
              :title="$t('js_confirm_delete')"
              :ok-text="$t('global_button_ok')"
              :cancel-text="$t('global_button_cancel')"
              :disabled="!canEdit || isAdminEm"
              placement="top"
              @confirm="handleDeleteSku(calcSkuInfo.sku_id)"
            >
              <span
                class="common-btn-link danger"
                type="danger"
                :ghost="canEdit && !isAdminEm"
                :disabled="!canEdit || isAdminEm"
                :loading="isDeleteLoading"
                :style="{ marginRight: '24px' }"
                v-bind="$root.bindIHTrack({ type: 'module', spm: 'Delete_Unit' })"
              >
                {{ $t('81903') }}
              </span>
            </a-popconfirm>
          </template>
          <UnitSettingsPopover
            :visible="showSettings"
            :unit-data="unitData"
            :sku-id="calcSkuInfo.sku_id"
            @close="showSettings = false"
          >
            <span
              class="common-btn-link"
              v-bind="$root.bindIHTrack({ type: 'module', spm: 'Unit_Setting' })"
              @click="showSettings = true"
            >
              <span class="btn-text">{{ $t('81803') }}</span>
            </span>
          </UnitSettingsPopover>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import maps from '@activity/utils/maps.js'
import UnitSettingsPopover from '@activity/pages/package/units/new-components/unit-settings-popover'
import { getEditLang } from '@activity/utils'
import { checkPassAsync } from '@activity/components/pass-standard-confirm'
import { state as pkgsState } from '@activity/store/pkgs-cache-observable.js'

export default {
  name: 'MerchantUnitInfo',
  components: {
    UnitSettingsPopover
  },
  props: {
    unitData: {
      type: Object,
      default: () => {}
    },
    emptyStr: {
      type: String,
      default: '--'
    }
  },
  inject: ['refreshPage'],
  data() {
    return {
      isDeleteLoading: false,
      showSettings: false,
      pkgsCacheState: pkgsState['pkgsCache'],
      loading: false
    }
  },
  computed: {
    ...mapState({
      canEdit: (state) => state.canEdit,
      isAdminEm: (state) => state.isAdminEm,
      lockMerchantActEditing: 'lockMerchantActEditing'
    }),
    activityId() {
      return klook.parse2id(this.$route.params.id)
    },
    packageId() {
      return klook.parse2id(this.$route.query.package_id)
    },
    calcSkuInfo() {
      const { emptyStr } = this
      const {
        unit_name_format: sku_name = emptyStr,
        sku_id = emptyStr,
        published,
        ticket_status,
        step
      } = this.unitData || {}
      const obj = {
        sku_name,
        sku_id,
        published,
        ticket_status,
        step
      }
      return obj
    }
  },
  methods: {
    ...mapActions(['getPkgInfo2actions']),
    mapsFilterFn: maps.filterFn,
    getEditLang,
    async handlePublish() {
      let { step, sku_id: skuId, published } = this.calcSkuInfo || {}
      if (!step.includes('sku_calendar') && !published) {
        let res = await ajax.get({
          url: ADMIN_API.act.get_has_completed_schedule,
          params: {
            sku_id: skuId
          }
        })

        if (_.get(res, 'has_completed_schedule', false)) {
          await Promise.all([
            klook.bus.$emit('saveSkuStep', 'sku_calendar', true),
            klook.bus.$emit('savePkgStep', 'price_inventory')
          ])
        } else {
          this.$message.warn(this.$t('29268'))
          return
        }
      }

      const unitList = _.get(this.pkgsCacheState, `${this.packageId}.pkgUnitsMenus`, [])
      const publishedUnitNum = unitList.reduce((accu, curr) => {
        if (curr.published) {
          accu += 1
        }
        return accu
      }, 0)
      // FND类目把unit上限改为100
      let max = this.$root.isFNB ? 100 : 8
      if (this.$route.params.id == '15699') {
        max = 30
      }
      if (!published && publishedUnitNum >= max) {
        this.$error({
          title: 'Error',
          content: this.$t('28993')
        })
        return
      }

      let checkPassRes = {}
      if (published) {
        // 下架活动、套餐、sku的时候需要检查是否是 pass standard
        checkPassRes = await checkPassAsync({ sku_ids: skuId }, this.$i18n)

        if (checkPassRes && checkPassRes.stop) {
          return
        }
      }

      this.loading = true
      const res = await ajax.postBody(
        ADMIN_API.act.published_sku,
        {
          noDefaultResponseInterceptor: true,
          data: {
            sku_id: skuId,
            reason: checkPassRes.reason,
            reason_code: checkPassRes.reasonCode,
            force: !!checkPassRes.force,
            language: getEditLang(),
            published: !published
          }
        },
        {
          msgOpt: {
            isErrMsg: false
          }
        }
      )
      this.loading = false

      if (res.success) {
        this.$message.success(this.$t('global_success'))
        klook.bus.$emit('getPackageSkuDataBus')
      } else if (res?.error?.code === '030007') {
        this.$message.error({
          content: this.$t('193305'),
          duration: 5
        })
      } else {
        this.$message.error(this.$t('global_failure'))
      }
    },
    async handleDeleteSku(skuId) {
      this.isDeleteLoading = true
      const res = await ajax.postBody(ADMIN_API.act.destroy_sku, {
        data: {
          sku_id: skuId
        }
      })
      this.isDeleteLoading = false

      if (res.success) {
        this.$message.success(this.$t('global_delete_success'))
        klook.bus.$emit('getPackageSkuDataBus')
        this.$router.replace({
          path: `/act/package/info/${this.$route.params.id}`,
          query: {
            ...this.$route.query,
            sku_id: ''
          }
        })

        // if (!this.$root.isMerchant) {
        //   this.actionGetAddon({
        //     activity_id: this.activityId,
        //     language: getEditLang()
        //   })
        // }
      } else {
        this.$message.error(this.$t('global_delete_error'))
      }
    },
    getDeleteSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `DeleteUnit?oid=${oid}&trg=manual`
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../actInfo/merchant-index.scss';
.with-warm-up {
  margin-left: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #757575;

  .anticon {
    cursor: pointer;
  }
}
.btn-text {
  padding: 8px 0;
}

.title-box {
  display: flex;
  align-items: center;
  gap: 12px;

  .tag {
    width: fit-content;
    padding: 2px 8px;
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
    border-radius: 2px;
    color: #faad14;

    &.--is-main {
      color: #437dff;
      background-color: #f0f7ff;
      border: 1px solid #bdd8ff;
    }
  }
}
</style>
