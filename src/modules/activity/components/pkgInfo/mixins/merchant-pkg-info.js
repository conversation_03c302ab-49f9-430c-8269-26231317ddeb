import handleLib from '@activity/pages/activityManagement/handleLib'
import stkModal from '@activity/components/modalAntd/stkModal'

export default {
  inject: ['refreshPage'],
  components: {
    stkModal
  },
  data() {
    return {
      stkData: {
        activity_item: {},
        form: {
          note: '', // 原因,
          package_id_list: [
            // 活动和套餐必须都传，并且套餐属于活动,
          ]
        },
        row: {},
        keyFlag: 'package',
        visible: false,
        packageArr: [],
        on: {
          ok: async () => {
            let { stkData } = this
            let isAct = stkData.keyFlag === 'activity'
            let isFlag = !isAct && this.includeActToSubmit // 需要跟活动一起提交
            let res = []
            const packages = stkData.packageArr.filter((pkg) =>
              stkData.form.package_id_list.includes(pkg.package_id)
            )
            if (isAct || isFlag) {
              res = await Promise.all([
                handleLib.methods.submit_to_klook2activity(
                  {
                    activity_id: this.activityId,
                    ...stkData.form,
                    package_id_list: stkData.form.package_id_list
                  },
                  'postBody'
                )
              ])
            } else {
              res = await Promise.all(
                packages.map((pkg) =>
                  handleLib.methods.submit_to_klook2package(
                    {
                      activity_id: this.activityId,
                      note: stkData.form.note,
                      package_id: pkg.package_id
                    },
                    'postBody'
                  )
                )
              )
            }

            stkData.visible = false
            if (res.every((item) => item.success)) {
              this.$root.trackIHEvent('#submit_package_spm', {
                err_message: ''
              })
              // 兼容 guide ball 调用
              this.categoryInfo && this.$set(this.categoryInfo, 'approval_status', 1)
              this.refreshPage()
              // klook.bus.$emit('initQueryData2bus')
            } else {
              this.$set(this, 'responseTable', {
                visible: true,
                data: res.map((item, index) => ({
                  title: packages[index].package_id + packages[index].package_title,
                  result: item.success ? this.$t('174950') : this.$t('174951'),
                  reason: item.error?.message || '/'
                }))
              })

              this.$root.trackIHEvent('#submit_package_spm', {
                err_message: 'SubmitPackage Error'
              })
            }
            stkData.form.note = ''
            stkData.form.package_id_list = []
          }
        }
      }
    }
  },
  computed: {
    activityId() {
      return +this.$route.params.id
    },
    actApproveStatus() {
      return this.$store.state.actItemData?.approval_status
    },
    includeActToSubmit() {
      return [0, 2].includes(this.actApproveStatus)
    }
  }
}
