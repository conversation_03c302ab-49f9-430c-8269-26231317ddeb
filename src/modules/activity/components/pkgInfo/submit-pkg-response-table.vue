<template>
  <a-modal
    :title="$t('174948')"
    :visible="_visible"
    :width="720"
    :cancel-button-props="{
      style: {
        display: 'none'
      }
    }"
    :ok-text="$t('161225')"
    @ok="handleOk"
  >
    <a-table
      class="submit-pkg-response-table"
      :columns="columns"
      :data-source="data"
      :pagination="false"
    ></a-table>

    <div class="submit-pkg-response-alert">{{ $t('174953') }}</div>
  </a-modal>
</template>

<script>
export default {
  name: 'SubmitPkgResponseTable',
  model: {
    prop: 'visible',
    event: 'change'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(visible) {
        this.$emit('change', visible)
      }
    },
    columns() {
      return [
        {
          title: 'Package',
          dataIndex: 'title',
          key: 'title',
          width: 250
        },
        {
          title: __('174949'),
          dataIndex: 'result',
          key: 'result',
          width: 100
        },
        {
          title: __('174952'),
          dataIndex: 'reason',
          key: 'reason',
          width: 250
        }
      ]
    }
  },
  methods: {
    handleOk() {
      this._visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.submit-pkg-response-table {
  ::v-deep .ant-table-row-cell-break-word {
    white-space: break-spaces;
  }
}
.submit-pkg-response-alert {
  margin-top: 12px;
}
</style>
