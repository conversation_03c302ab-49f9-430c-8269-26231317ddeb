<template>
  <div class="popconfirm-switch-container">
    <a-popconfirm
      :visible="visible"
      :ok-text="okText"
      :cancel-text="cancelText"
      overlay-class-name="popconfirm-switch-class"
      @confirm="onConfirm"
      @cancel="onCancel"
    >
      <template slot="title">
        <div class="title">{{ title }}</div>
        <div class="content">{{ content }}</div>
      </template>
      <a-switch :checked="currValue" @change="onChange" />
    </a-popconfirm>

    <div v-if="!checked && noTips" class="tips">{{ noTips }}</div>
  </div>
</template>

<script>
export default {
  name: 'PopConfirmSwitch',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    title: {
      type: String,
      default: __('93284')
    },
    content: {
      type: String,
      default: __('93285')
    },
    okText: {
      type: String,
      default: __('47526')
    },
    cancelText: {
      type: String,
      default: __('28633')
    },
    noTips: {
      type: String,
      default: ''
    },
    checked: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      visible: false
    }
  },
  computed: {
    currValue: {
      get() {
        return this.checked
      },
      set() {}
    }
  },
  methods: {
    onChange(value) {
      if (value) {
        this.$emit('change', true)
      } else {
        this.visible = true
      }
    },
    onConfirm() {
      this.visible = false
      this.$emit('change', false)
    },
    onCancel() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss">
.popconfirm-switch-class {
  .title,
  .content {
    max-width: 250px;
  }

  .title {
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    font-size: 16px;
    line-height: 1.4;
  }
}
</style>

<style lang="scss" scoped>
.popconfirm-switch-container {
  .tips {
    padding: 8px;
    background-color: rgba(255, 171, 0, 0.05);
    font-size: 12px;
    line-height: 14px;
  }
}
</style>
