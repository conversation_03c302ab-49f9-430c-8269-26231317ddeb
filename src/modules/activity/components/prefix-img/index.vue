<template>
  <div class="prefix-img-contain" :style="calcStyles">
    <img v-bind="$attrs" :src="calcSrc" class="prefix-img-contain__prefix-img" />
  </div>
</template>

<script>
export default {
  props: {
    platform: {
      type: String,
      default: 'desktop'
    },
    src: {
      type: String,
      default: ''
    },
    crop: {
      type: String,
      default: ''
    },
    whRate: {
      type: String,
      default: '1:1' // '1:1' | '4:3' | '16:9'
    },
    realHeight: {
      type: Number,
      default: 0
    },
    cropScale: {
      type: Number,
      default: 1.5 // 放大比率
    }
  },
  data() {
    return {
      whRateMaps: {
        '1:1': {
          hwRate: 1,
          styles: {
            paddingBottom: '100%'
          }
        },
        '4:3': {
          hwRate: 0.75,
          styles: {
            paddingBottom: '75%'
          }
        },
        '16:9': {
          hwRate: 0.5625,
          styles: {
            paddingBottom: '56.25%'
          }
        }
      }
    }
  },
  computed: {
    calcRateObj() {
      const { whRateMaps, whRate } = this
      const obj = whRateMaps[whRate]
      return obj
    },
    calcStyles() {
      return this.calcRateObj?.styles || {}
    },
    calcCrop() {
      const { platform, calcRateObj, crop, realHeight, cropScale } = this
      if (crop) {
        return crop
      }
      if (!realHeight) {
        const obj = {
          mobile: '/c_fill,w_800',
          desktop: '/c_fill,w_1920'
        }
        return obj[platform]
      }
      const ch = Math.round(realHeight * cropScale)
      const cw = Math.round(ch / calcRateObj.hwRate)
      const autoCrop = `/c_fill,w_${cw},h_${ch}`
      return autoCrop
    },
    calcSrc() {
      const { calcCrop, src: simg } = this
      if (!simg) {
        return this.$attrs.src
      }
      const regStr = '//res.klook.com/image/upload'
      const src = calcCrop ? simg?.replace(regStr, `${regStr}${calcCrop}`) : simg
      return src
    }
  }
}
</script>

<style lang="scss" scoped>
.prefix-img-contain {
  position: relative;
  width: 100%;
  &__prefix-img {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
