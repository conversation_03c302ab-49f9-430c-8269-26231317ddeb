import Vue from 'vue'
import PreviewValidity from './main.vue'

let instance

const getInstance = (i18n) => {
  if (instance) {
    return instance
  }

  const Ctor = Vue.extend(PreviewValidity)
  instance = new Ctor({
    i18n
  }).$mount()
  document.body.appendChild(instance.$el)
  return instance
}

export const getPreviewValiditySync = (activityId, i18n, callback) => {
  const inst = getInstance(i18n)
  return inst.getPreviewValidity(activityId, callback)
}
