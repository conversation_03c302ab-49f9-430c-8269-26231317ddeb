<template>
  <a-modal
    v-model="visible"
    :title="$t('101026')"
    :width="580"
    :mask-closable="true"
    :cancel-text="$t('cancel')"
    :ok-text="$t('101028')"
    class="preview-validity"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div v-html="warningMessage"></div>

    <div class="preview-validity-select">
      <span class="preview-validity-label">{{ $t('101029') }}</span>
      <a-select v-model="validity" :placeholder="$t('global_please_select')">
        <a-select-option v-for="{ value, text } in validityOptions" :key="value" :value="value">
          {{ text }}
        </a-select-option>
      </a-select>
    </div>
  </a-modal>
</template>
<script>
import moment from 'moment'
import { aesEncrypt } from '@activity/utils/aes'

export default {
  name: 'PreviewValidity',
  data() {
    const validityOptions = Array(30)
      .fill(0)
      .map((_, index) => ({
        value: index + 1,
        text: __('101030', { num: index + 1 })
      }))
    return {
      validityOptions,
      validity: 7,
      callback: null,
      visible: false,
      activityId: 0
    }
  },
  computed: {
    warningMessage() {
      return this.$t('101027', { yyyy_mm_dd: '<b>2023-07-24</b>' })
    }
  },
  methods: {
    handleOk() {
      if (this.callback) {
        this.callback(this.getVadility())
      }
      this.visible = false
    },
    handleCancel() {
      if (this.callback) {
        this.callback()
      }
      this.visible = false
    },
    getPreviewValidity(activityId, callback) {
      this.activityId = activityId
      this.callback = callback
      this.validity = 7
      this.visible = true
    },
    getVadility() {
      const expiredDate = moment()
        .add(this.validity + 1, 'days')
        .format('YYYY-MM-DD')
      return aesEncrypt(
        `${moment(expiredDate)
          .utc()
          .format()}::${this.activityId}`
      )
    }
  }
}
</script>
<style lang="scss" scoped>
.preview-validity {
  &-select {
    margin-top: 16px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  &-label {
    margin-right: 6px;
  }
}
</style>
