<template>
  <div class="reject-info-box">
    <a-icon class="reject-info-icon" type="close-circle" />
    <div class="reject-info-right">
      <div class="reject-reason">
        {{ isMerchant ? $t('orderdetail_text_reject_reason') : $t('210328') }}:
        {{ rejectInfo.reject_reason_text }}
      </div>
      <div v-if="rejectInfo.reject_reason_other_text" class="reject-specification">
        <a-tooltip>
          <template slot="title">
            {{ rejectInfo.reject_reason_other_text }}
          </template>
          <span> {{ $t('booking_report.details') }}: {{ rejectInfo.reject_reason_other_text }} </span>
        </a-tooltip>
      </div>
    </div>
  </div>
</template>
<script>
import { isMerchant } from '@/env'
export default {
  name: 'RejectInfoAlert',
  props: {
    rejectInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isMerchant
    }
  }
}
</script>
<style lang="scss" scoped>
.reject-info-box {
  background-color: #fff1f0;
  border: 1px solid #ffccc7;
  color: #000000d9;
  padding: 9px 16px;
  border-radius: 2px;
  position: relative;

  .reject-info-icon {
    color: red;
    margin-right: 10px;
    font-size: 21px;
    position: absolute;
    top: 9px;
    left: 16px;
  }
  .reject-info-right {
    margin-left: 35px;
  }

  .reject-title-text {
    font-size: 16px;
    line-height: 24px;
    margin-bottom: 6px;
    margin-top: -2px;
  }
  .reject-reason {
    line-height: 22px;
    font-size: 14px;
  }
  .reject-specification {
    line-height: 22px;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-top: 6px;
  }
}
</style>
<style>
.actReject-popover {
  width: 490px;

  .ant-popover-title {
    color: rgba(0, 0, 0, 0.85) !important;
    border-bottom: 1px solid #e8e8e8 !important;
    min-width: 177px !important;
    min-height: 32px !important;
    padding: 5px 16px 4px !important;
    font-weight: 500 !important;
    text-align: left !important;
    font-size: 16px !important;
  }

  span {
    color: #000000d9;
  }

  .reject-popover-item {
    display: flex;
    font-size: 16px;
    .title {
      min-width: 140px;
      color: #00000073;
    }
    &:first-child {
      margin-bottom: 6px;
    }
  }
}
</style>
./reject-info-popover.vue
