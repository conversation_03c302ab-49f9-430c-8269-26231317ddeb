<template>
  <a-popover v-model="visible" trigger="click" placement="right" overlayClassName="actReject-popover">
    <template slot="content">
      <div><span style="color: #00000073">Last reject reason: </span> {{ rejectInfo.note }}</div>
    </template>
    <slot name="trigger">
      <div class="specification-view-more">{{ $t('18718') }}</div>
    </slot>
  </a-popover>
</template>
<script>
export default {
  name: 'RejectInfoPopover',
  props: {
    ticketId: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {
      visible: false,
      rejectInfo: {
        note: ''
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getRejectInfo()
      }
    }
  },
  methods: {
    hide() {
      this.visible = false
    },
    async getRejectInfo() {
      try {
        const res =
          (await ajax.get(ADMIN_API.act.get_ticket_log, {
            params: {
              ticket_id: this.ticketId
            }
          })) || []
        if (res && res.length > 0) {
          this.rejectInfo.note = res.find((item) => item.ticket_event === 'Reject')?.note || ''
        }
      } catch (error) {
        console.log(error)
      }
    }
  }
}
</script>
<style>
.actReject-popover {
  /* width: 490px; */

  .ant-popover-title {
    display: none !important;
  }
  /* .ant-popover-content {
    width: fit-content !important;
  } */
  span {
    color: #000000d9;
  }
  /* .reject-content {
    display: flex;

    .reject-popover-left {
      width: max-content;
      font-size: 16px;
      color: #00000073;
      margin-right: 20px;
      div:first-child {
        margin-bottom: 12px;
      }
    }
    .reject-popover-right {
      flex: 1;
      font-size: 16px;
      color: #000000d9;
      div:first-child {
        margin-bottom: 12px;
      }
    }
  } */
}
</style>
