<template>
  <a-form-model ref="form" class="report-error-drawer-form" :model="form" layout="vertical">
    <a-form-model-item
      :label="$t('119826')"
      prop="topic_id"
      :rules="[{ required: true, message: $t('119831'), trigger: 'change' }]"
    >
      <a-select
        v-model="form.topic_id"
        :placeholder="$t('119841')"
        disabled
        :options="questionOptions"
        :show-arrow="false"
      />
    </a-form-model-item>

    <a-form-model-item
      :label="$t('119833')"
      prop="feedback_content"
      :rules="[{ required: true, message: $t('119832'), trigger: 'change' }]"
    >
      <span @click="$tracker.track('action', '#description-click-spm')">
        <CountLimitINput
          v-model="form.feedback_content"
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 6 }"
          :word-count-limit="5000"
          :placeholder="$t('119834')"
        />
      </span>
    </a-form-model-item>

    <a-form-model-item :label="$t('119835')" prop="reference_no">
      <a-input
        v-model="form.reference_no"
        :placeholder="$t('119836')"
        @click="$tracker.track('action', '#reference-number-click-spm')"
      />
    </a-form-model-item>

    <a-form-model-item :label="$t('119837')" prop="file_list">
      <Uploader
        :file.sync="file"
        auto-upload-screen-shoot
        :screen-shoot-element="screenShootElement"
        :shoot-options="shootOptions"
      />
    </a-form-model-item>

    <a-form-model-item v-if="infoSchema" :label="$t('119838')">
      <Information :schema="infoSchema" />
    </a-form-model-item>

    <div
      id="description-click-spm"
      data-spm-virtual-item="__virtual"
      :data-spm-module="`DescriptionClick?trg=manual${spmExt}`"
    ></div>

    <div
      id="reference-number-click-spm"
      data-spm-virtual-item="__virtual"
      :data-spm-module="`ReferenceNumberClick?trg=manual${spmExt}`"
    ></div>
  </a-form-model>
</template>

<script>
import CountLimitINput from '@activity/components/count-limit-input/index.vue'
import Uploader from './uploader'
import Information from './information'
import spmMixin from '@activity/components/report-error-drawer/mixins/spm-mixin.js'

export default {
  name: 'ReportErrorForm',
  components: {
    CountLimitINput,
    Uploader,
    Information
  },
  mixins: [spmMixin],
  props: {
    infoSchema: {
      type: Array,
      default: null
    }
  },
  data() {
    const screenShootElement = document.querySelector('.merchant-container')
    return {
      screenShootElement,
      shootOptions: {
        height: screenShootElement.clientHeight,
        scrollX: document.documentElement.scrollLeft,
        scrollY: document.documentElement.scrollTop
      },
      questionOptions: [],
      file: {},
      form: {
        topic_id: undefined,
        reference_no: '',
        feedback_content: '',
        file_list: [],
        send_copy: 1
      }
    }
  },
  watch: {
    file: {
      deep: true,
      handler(val) {
        if (Object.values(val).length) {
          this.form.file_list[0] = {
            file_name: val.file_name,
            file_id: val.file_id
          }
        } else {
          this.form.file_list = []
        }
      }
    }
  },
  created() {
    this.initData()
  },
  methods: {
    validateForm() {
      return new Promise((resolve) => {
        this.$refs.form.validate(resolve)
      })
    },
    async initData() {
      const response = (await ajax.get(ADMIN_API.act.feedback_topic_list)) || []
      this.questionOptions = response.reduce(
        (acc, curr) => [
          ...acc,
          ...curr.child_topic.map((item) => ({
            label: item.topic_title_i18n,
            value: item.topic_id
          }))
        ],
        []
      )

      this.form.topic_id = 3 // 3: Activities/products
    }
  }
}
</script>

<style lang="scss" scoped>
.report-error-drawer-form ::v-deep .suffix-text {
  position: absolute;
  bottom: -22px;
  right: 0;
}
</style>
