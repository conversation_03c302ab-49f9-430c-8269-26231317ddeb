<template>
  <div v-if="isValid" class="report-error-information">
    <div
      v-for="(item, index) of schema"
      :key="index"
      class="information-box"
      :class="`is-${item.layout || 'inline'}`"
    >
      <div v-for="(child, idx) of item.children" :key="idx" class="information-item">
        <div class="information-item-label">{{ child.label }}</div>
        <div class="information-item-content" v-html="child.content"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ReportErrorFormInformation',
  props: {
    schema: {
      type: Array,
      default: null
    }
  },
  computed: {
    isValid() {
      const { schema } = this

      return Array.isArray(schema) && schema.length
    }
  }
}
</script>

<style lang="scss" scoped>
$gapPixel: 8px;

.report-error-information {
  padding: 16px 20px;
  border-radius: $gapPixel;
  border: 1px solid var(--color-border-normal, #e6e6e6);

  .information-box {
    display: flex;
    flex-direction: column;
    gap: $gapPixel;
    margin-bottom: $gapPixel;

    &.is-inline {
      flex-direction: row;
      justify-content: space-between;

      .information-item {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        flex: 1;
      }
    }

    &:last-child {
      margin-bottom: none;
    }
  }

  .information-item {
    font-size: 14px;
    font-weight: 400;
    line-height: 150%;

    &-label {
      color: var(--color-text-secondary, #757575);

      &:after {
        content: ':';
        position: relative;
        top: -0.5px;
        margin-left: 2px;
      }
    }

    &-content {
      word-break: break-all;
      color: var(--color-text-primary, #212121);
    }
  }
}
</style>
