<template>
  <div class="uploader">
    <a-upload-dragger
      ref="uploader"
      name="user_file"
      accept=".jpg,.png,.jpeg"
      :action="action"
      :data="uploadData"
      :before-upload="beforeUpload"
      :show-upload-list="false"
      :class="{ '--is-uploaded': imgUrl }"
      @change="handleChange"
    >
      <div
        v-if="imgUrl"
        class="uploader__image-preview"
        :style="{ backgroundImage: `url('${imgUrl}')` }"
        @click.stop="openPreview"
      />

      <template v-else>
        <p class="ant-upload-drag-icon">
          <a-icon type="inbox" />
        </p>
        <p class="ant-upload-text">
          Click or drag file to this area to upload
        </p>
      </template>
    </a-upload-dragger>

    <div v-if="imgUrl" class="uploader__actions">
      <a-button icon="upload" @click="handleReupload">Reupload</a-button>
      <a-button icon="delete" @click="handleDelete">Delete</a-button>
    </div>

    <a-modal
      :visible="preview.visible"
      :title="preview.title"
      :footer="null"
      :width="820"
      @cancel="closePreview"
    >
      <img
        alt="preview"
        :style="{ display: 'block', maxWidth: '100%', margin: '0 auto' }"
        :src="preview.src"
      />
    </a-modal>

    <div
      id="delete-click-spm"
      data-spm-virtual-item="__virtual"
      :data-spm-module="`DeleteClick?trg=manual${spmExt}`"
    ></div>

    <div
      id="upload-click-spm"
      data-spm-virtual-item="__virtual"
      :data-spm-module="`UploadClick?trg=manual${spmExt}`"
    ></div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import html2canvas from 'html2canvas'
import spmMixin from '@activity/components/report-error-drawer/mixins/spm-mixin.js'

const preview_mixin = {
  data() {
    return {
      preview: {
        visible: false,
        src: '',
        title: ''
      }
    }
  },
  methods: {
    openPreview() {
      Object.assign(this.preview, {
        visible: true,
        src: this.imgUrl,
        title: this.currFile.file_name
      })
    },
    closePreview() {
      this.$set(this, 'preview', this.$options.data().preview)
    }
  }
}

export default {
  name: 'ReportErrorFormUploader',
  mixins: [spmMixin, preview_mixin],
  props: {
    autoUploadScreenShoot: {
      type: Boolean,
      default: false
    },
    screenShootElement: {
      type: HTMLElement,
      default: null
    },
    shootOptions: {
      type: Object,
      default: () => ({})
    },
    file: {
      type: Object,
      required: true
    },
    maxSize: {
      type: Number,
      default: 4
    }
  },
  data() {
    return {
      uploadUrl: ADMIN_API.act.tool_upload_file
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo || {}
    }),
    merchantId() {
      return this.userInfo?.merchant_id || ''
    },
    uploadData() {
      return {
        merchant_id: this.merchantId,
        file_type: 5,
        ignore_check_name: 1
      }
    },
    currFile: {
      get() {
        return this.file
      },
      set(val) {
        this.$emit('update:file', val)
      }
    },
    action() {
      return this.uploadUrl
    },
    imgUrl() {
      if (!this.currFile?.file_id) {
        return ''
      }

      const params = new URLSearchParams()
      params.set('merchant_id', this.uploadData.merchant_id)
      params.set('file_type', this.uploadData.file_type)
      params.set('file_id', this.currFile.file_id)

      return `${location.origin}/v1/merchantapisrv/tool_service/download_file?${params.toString()}`
    }
  },
  mounted() {
    if (this.autoUploadScreenShoot) {
      this.uploadScreenShoot()
    }
  },
  methods: {
    async uploadScreenShoot() {
      if (!this.screenShootElement) {
        this.$message.warn('Element is invalid')
        return
      }

      const canvas = await html2canvas(this.screenShootElement, this.shootOptions)
      const blob = await this.getCanvas2BlobPms(canvas)
      const data = this.getFormData(blob)
      this.uploadImage(data)
    },
    getCanvas2BlobPms(canvas) {
      return new Promise((resolve) => {
        canvas.toBlob((blob) => {
          resolve(new File([blob], this.getImgFileName(), { type: 'image/png' }))
        })
      })
    },
    getFormData(blob) {
      let data = new FormData()
      for (let key in this.uploadData) {
        data.append(key, this.uploadData[key])
      }

      data.append('user_file', blob)

      return data
    },
    async uploadImage(data) {
      const response = await ajax.post(this.uploadUrl, {
        data,
        headers: { 'content-type': 'application/x-www-form-urlencoded' }
      })

      this.$set(this, 'currFile', response)
    },
    async customRequest({ file }) {
      const data = this.getFormData(file)

      this.uploadImage(data)
    },
    handleReupload() {
      this.$tracker.track('action', '#upload-click-spm')
      this.$refs.uploader.$children[0].$el.querySelector('input').click()
    },
    async handleDelete() {
      await ajax.post(ADMIN_API.act.tool_delete_file, {
        data: {
          file_id: this.currFile.file_id,
          file_type: this.uploadData.file_type
        }
      })

      this.$tracker.track('action', '#delete-click-spm')
      this.$message.success(this.$t('global_delete_success'))
      this.$set(this, 'currFile', {})
    },
    handleChange(info) {
      let fileList = [...info.fileList]
        .map((file) => {
          file.filetype = 5

          if (file.response?.result) {
            file.url = file.response.result.file_path
            file.path = file.response.result.file_path
            file.file_name = file.response.result.file_name
            file.id = 0
            file.file_id = file.response.result.file_id
          }

          return file
        })
        .filter((file) => !(!file.response?.success || file.isTrash))

      if (fileList.length) {
        const response = fileList[fileList.length - 1]
        this.$set(this, 'currFile', response)
        this.$emit('update:file', response)
        this.$tracker.track('action', '#upload-click-spm')
      }
    },
    beforeUpload(file) {
      const isOverSize = file.size / 1024 / 1024 > this.maxSize

      if (isOverSize) {
        file.isTrash = 1
        this.$message.warn(`Images can be uploaded up to ${this.maxSize}M.`)
        return false
      }

      return true
    },
    getImgFileName() {
      return `mid${this.merchantId}_screen_shoot_${Date.now()}.png`
    }
  }
}
</script>

<style lang="scss" scoped>
.uploader {
  ::v-deep {
    .--is-uploaded .ant-upload.ant-upload-drag {
      height: 250px;
    }

    .ant-upload-btn {
      padding: 0;
    }

    .ant-upload {
      height: 172px;
    }
  }

  &__image-preview {
    height: 100%;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
  }

  &__actions {
    display: flex;
    gap: 16px;
    align-items: center;
    justify-content: flex-end;
    margin-top: 8px;
  }
}
</style>
