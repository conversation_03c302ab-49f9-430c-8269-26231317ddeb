<template>
  <CommonDrawer
    v-if="visible"
    class="report-error-drawer"
    :title="$t('119839')"
    :visible.sync="visible"
    :data-spm-page="getPageSpm"
  >
    <ReportErrorForm ref="form" :info-schema="infoSchema" />
    <template #footer>
      <a-button @click="visible = false">{{ $t('global_cancel') }}</a-button>
      <a-button type="primary" @click="handleSubmit">{{ $t('global_button_ok') }}</a-button>
    </template>

    <div
      id="ok-click-spm"
      data-spm-virtual-item="__virtual"
      :data-spm-module="`OkClick?trg=manual${spmExt}`"
    ></div>
    <div
      id="error-toast-spm"
      data-spm-virtual-item="__virtual"
      :data-spm-module="`ErrorToast?trg=manual${toastSpmExt}`"
    ></div>
  </CommonDrawer>
</template>

<script>
import CommonDrawer from '@activity/components/common-drawer/index.vue'
import ReportErrorForm from './components/form'
import { OPEN_REPORT_ERR_DRAWER_2BUS_KEY, REPORT_ERROR_FROM_DICT } from '@activity/utils/modal-message.js'
import spmMixin from '@activity/components/report-error-drawer/mixins/spm-mixin.js'

export default {
  name: 'ReportErrorDrawer',
  components: {
    CommonDrawer,
    ReportErrorForm
  },
  mixins: [spmMixin],
  data() {
    return {
      errorMsg: '',
      visible: false,
      errorContentTxt: ''
    }
  },
  computed: {
    infoSchema() {
      if (!this.errorMsg) {
        return null
      }

      return [
        {
          layout: 'inline',
          children: [
            { label: 'Merchant ID', content: this.merchantId },
            { label: 'Activity ID', content: this.actId }
          ]
        },
        {
          layout: 'vertical',
          children: [
            {
              label: 'Error Message',
              content: this.errorMsg
            },
            {
              label: 'Path',
              content: location.href
            }
          ]
        }
      ]
    },
    getPageSpm() {
      return 'Feedback?trg=manual'
    },
    toastSpmExt() {
      return `&ext=${JSON.stringify({
        activity_id: this.actId,
        merchant_id: this.merchantId,
        content_text: this.errorContentTxt
      })}`
    }
  },
  mounted() {
    klook.bus
      .$off(OPEN_REPORT_ERR_DRAWER_2BUS_KEY)
      .$on(OPEN_REPORT_ERR_DRAWER_2BUS_KEY, ({ visible = true, message = '', from = '' } = {}) => {
        if (Object.values(REPORT_ERROR_FROM_DICT).includes(from)) {
          this.errorContentTxt = encodeURIComponent(message)
          setTimeout(() => {
            this.$tracker.track('action', '#error-toast-spm')
          }, 300)
        }
        this.visible = visible
        this.errorMsg = message
      })
    this.$once('hook:beforeDestroy', () => {
      klook.bus.$off(OPEN_REPORT_ERR_DRAWER_2BUS_KEY)
    })
  },
  methods: {
    async handleSubmit() {
      const valid = await this.$refs.form.validateForm()
      this.$tracker.track('action', '#ok-click-spm')

      if (!valid) {
        return
      }

      const otherInfo = this.errorMsg
        ? {
            merchant_id: String(this.merchantId),
            activity_id: String(this.actId),
            error_message: this.errorMsg
          }
        : {}

      const { query } = this.$route
      const { categoryInfo } = this.$store.state
      const response = await ajax.post(ADMIN_API.act.feedback_submit, {
        data: {
          ...this.$refs.form.form,
          path: location.href,
          sub_category_id: +categoryInfo?.sub_category_id || 0,
          is_spu_package: +location.pathname.includes('/aid/'),
          activity_id: +this.actId || 0,
          package_id: +query.package_id || 0,
          url: location.href,
          other_info: {
            path: location.href,
            ...otherInfo
          }
        }
      })

      if (response) {
        this.$message.success(this.$t('global_success'))
        this.visible = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.report-error-drawer {
  ::v-deep .ant-drawer-header {
    background-color: #2073f9;
    border-radius: 0;

    .ant-drawer-title,
    .ant-drawer-close {
      color: #fff;
    }
  }
}
</style>
