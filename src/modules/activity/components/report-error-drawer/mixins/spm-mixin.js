import { mapState } from 'vuex'

export default {
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo || {}
    }),
    merchantId() {
      return this.userInfo?.merchant_id || ''
    },
    actId() {
      const { query, params } = this.$route

      return this.$route.name === 'actCreate' ? query.activity_id : params.id ?? ''
    },
    spmExt() {
      return `&ext=${JSON.stringify({
        activity_id: this.actId,
        merchant_id: this.merchantId
      })}`
    }
  }
}
