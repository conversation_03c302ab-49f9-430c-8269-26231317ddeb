<template>
  <div v-if="count >= 0 && parseInt(count)" class="require-fields">
    <div class="require-fields-box">
      <template v-if="num < count">
        <span class="info-box"
          ><a-icon type="info-circle" theme="filled" />
          <span>{{ num }}/{{ count }}</span>
        </span>
      </template>
      <template v-else>
        <span class="check-box"
          ><a-icon type="check-circle" theme="filled" />
          <span>{{ num }}/{{ count }}</span></span
        >
      </template>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    num: {
      default: 0
    },
    count: {
      default: 0
    }
  },
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.require-fields-box {
  display: inline-block;
  .info-box,
  .check-box {
    display: flex;
    align-items: center;
    font-size: 12px;
    padding: 2px;
    height: 22px;
    border-radius: 11px;
    i {
      font-size: 16px;
      padding-right: 4px;
    }
  }
  .info-box {
    color: #ffab00;
    background: rgba(255, 171, 0, 0.1);
  }
  .check-box {
    color: #36b37e;
    background: rgba(54, 179, 126, 0.1);
  }
}
</style>
