<!-- 修复当里面的输入组件是 disabled 时，ant tooltip 不会触发的问题 -->
<template>
  <div class="shim-antd-tooltip-container">
    <a-tooltip
      ref="tooltip"
      v-model="visible"
      v-bind="$attrs"
      :placement="placement"
      :overlay-class-name="currOverlayClassName"
      :auto-adjust-overflow="autoAdjustOverflow"
      :disabled="disabled"
      @visibleChange="visibleChange"
    >
      <template v-if="(tooltipContent || $slots.title) && !disabled" slot="title">
        <slot name="title">
          <div class="content">
            <a-icon
              v-if="(richContent && canFullscreen) || displayFullscreenBtn"
              class="fullscreen"
              type="fullscreen"
              @click="handleFullscreen"
            />
            <slot name="content">
              <MarkdownText class="markdown-content" :text="tooltipContent" />
            </slot>
          </div>
        </slot>
      </template>

      <!-- 触发 tooltip 会造成 safari browser 滚动（将 tooltip 出现在窗口底部） -->
      <slot v-if="isSafariBrowser"></slot>

      <!-- label标签嵌套导致checkbox点击问题：label -> span -->
      <span v-else :class="{ 'is-fill-content': isFitContent }">
        <slot></slot>
      </span>
    </a-tooltip>

    <Modal v-if="modal.visible" :visible.sync="modal.visible" :data="[tooltipContent]">
      <template v-if="$slots.content" #modalContent>
        <slot name="content"></slot>
      </template>
    </Modal>
  </div>
</template>

<script>
import Modal from '@activity/components/anchor-tips/modal.vue'
import MarkdownText from '@activity/components/MarkdownText.vue'

export default {
  name: 'ShimAntdTooltip',
  components: {
    Modal,
    MarkdownText
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    // @param richTips: object = { content: string }
    richTips: {
      type: [Object, String],
      default: null
    },
    placement: {
      type: String,
      default: 'right'
    },
    overlayClassName: {
      type: String,
      default: 'common-tooltip-style'
    },
    // 有部分字段需要 fit-content 让 tooltip 更贴近字段
    isFitContent: {
      type: Boolean,
      default: false
    },
    displayFullscreenBtn: {
      type: Boolean,
      default: false
    },
    autoAdjustOverflow: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      modal: {
        visible: false
      }
    }
  },
  computed: {
    richContent() {
      return this.richTips?.content || this.richTips
    },
    canFullscreen() {
      return this.richTips?.canFullscreen ?? true
    },
    compactTooltip() {
      return this.richTips?.compactTooltip ?? false
    },
    tooltipContent() {
      let tooltipContent = this.richContent || this.title || ''

      return tooltipContent.replace(/<br\/>/g, '\n')
    },
    isSafariBrowser() {
      return /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent)
    },
    currOverlayClassName() {
      // 富文本 padding 会比较宽大
      return `shim-antd-tooltip ${this.overlayClassName} ${
        this.richContent && !this.compactTooltip ? 'tooltip-rich-text-padding' : ''
      }`
    }
  },
  methods: {
    visibleChange(v) {
      this.visible = v
    },
    handleFullscreen() {
      this.modal.visible = true
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.is-fill-content {
  display: block;
  width: fit-content;
}

.fullscreen {
  position: absolute;
  top: 16px;
  right: 16px;
  margin-right: -10px;
  font-size: 18px;
  color: rgba(0, 0, 0, 0.85);
  z-index: 99;
}

.markdown-content {
  font-size: 14px;
  line-height: 150%;
}
</style>
