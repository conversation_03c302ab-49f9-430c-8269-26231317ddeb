import xssFilters from 'xss-filters'
import { isMerchant } from '@/env'
import { uploadImage, addPhoneCall } from './utils/index'
import { replacePoiMark } from '@activity/pages/poiManagement/utils/index.js'

export const previewRender = function(plainText, preview, poiConfig = null) {
  const commonmark = require('commonmark')
  const reader = new commonmark.Parser()
  const writer = new commonmark.HtmlRenderer()

  plainText = plainText.replace(/javascript:/g, encodeURIComponent('javascript:'))

  const parsed = reader.parse(xssFilters.inHTMLData(plainText))

  return writer
    .render(parsed)
    .split('\n')
    .map((item) => {
      let text = item
        .replace(/~~(.*?)~~/g, '<del>$1</del>')
        .replace(/<a(.*?)href="(.*?)"(.*?)>/g, '<a$1href="$2"$3 target="_blank">')
        .replace(
          /<img(.*?)src="(.*?)"(.*?)alt="(.*?)"(.*?)\/?>/g,
          '<p class="img-box"><img$1src="$2"$3alt="$4"$5 /></p>'
        )

      // poi page 有特殊逻辑：预览时要把文案中的{{poi}}占位替换成真实的poi
      if (poiConfig) {
        const varMap = poiConfig?.varMap ?? []
        const highlight = poiConfig?.highlight ?? false
        text = replacePoiMark(text, varMap, highlight)
      }
      return text
    })
    .join('\n')
}

const toolbar = ['bold', 'heading', 'unordered-list', 'link', uploadImage, addPhoneCall, 'preview']

const toolbarMerchant = ['bold', 'heading', 'unordered-list', addPhoneCall, 'preview']

const common_defaults = {
  previewRender: previewRender,
  spellChecker: false,
  toolbar: isMerchant ? toolbarMerchant : toolbar,
  forceSync: true,
  status: false
}

export { common_defaults }
