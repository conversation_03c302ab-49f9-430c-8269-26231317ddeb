<!-- transplant the original simple-mde here -->
<!-- have not consider extendsibility yet -->
<!--
  When u use and refresh in the </a-form-model-item>, u must configure the :key to </a-form-model-item>.
-->
<template>
  <div class="simple-md-container" @mouseenter="onMouseenter" @mouseleave="onMouseleave">
    <markdown-editor
      ref="markdownEditor"
      v-model="child_value"
      :class="['common-md-style', 'md-editor', styleType]"
      :configs="mde_options"
      v-bind="{
        ...$attrs,
        ...$props
      }"
      :disabled="isDisabled"
      @input="handleInput"
      @initialized="onInitialized"
    />
    <div v-if="showSuffixCount && child_value.length" class="common-text-count simple-md">
      <span class="count-box">{{ getSuffixCount(child_value.length || 0) }}</span>
    </div>
  </div>
</template>

<script>
import './style/simplemde.scss'
import Vue from 'vue'
import VueSimplemde from './markdown-editor/index.js'
import { getSuffixCount } from '@activity/utils'

Vue.use(VueSimplemde)

import { common_defaults } from './config.js' // share same default

export default {
  name: 'SimpleMde',
  install(Vue) {
    // https://vuejs.org/v2/guide/plugins.html
    Vue.component(this.name, this)
  },
  model: {
    // https://vuejs.org/v2/guide/components.html#Form-Input-Components-using-Custom-Events
    prop: 'value', // this is actually bind to `current_items`
    event: 'change'
  },
  props: {
    customClass: {
      type: String,
      default: ''
    },
    styleType: {
      type: String,
      default: ''
    },
    showSuffixCount: {
      type: Boolean,
      required: false,
      default: false
    },
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    config: {
      type: Object,
      default: () => ({})
    },
    // eslint-disable-next-line vue/prop-name-casing
    lang_text: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      child_value: '',
      mde_options: {
        ...common_defaults,
        // Init status bar DOM
        status: [
          {
            className: 'lang_text',
            defaultValue: (el) => {
              el.innerHTML = this.lang_text
            }
          }
        ],
        autoDownloadFontAwesome: false,
        ...this.config
      },
      observer: null,
      emitDisabled: false
    }
  },
  computed: {
    isDisabled() {
      return this.disabled || this.emitDisabled
    },
    maxLength() {
      return this.$attrs['max-length'] || Infinity
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        this.child_value = val
      }
    },
    child_value(value) {
      this.$emit('change', value)
    },
    lang_text(newVal) {
      this.$children[0].simplemde.gui.statusbar.innerText = newVal
    },
    isDisabled: {
      immediate: true,
      handler(v) {
        this.$nextTick(() => {
          let codemirror = this.$refs.markdownEditor.simplemde.codemirror
          codemirror.doc.cantEdit = Boolean(v)
          codemirror.options.readOnly = Boolean(v)
          codemirror.display.wrapper.style.backgroundColor = v ? '#eee' : '#fff'
          this.$el.querySelector('.editor-preview').style.backgroundColor = v ? '#eee' : '#fff'
          const eyeDOM = this.$el.querySelector('.fa-eye')

          if (!eyeDOM) return

          if (v) {
            !eyeDOM.classList.contains('active') && eyeDOM.click()
            eyeDOM.style.display = 'none'
          } else {
            eyeDOM.classList.contains('active') && eyeDOM.click()
            eyeDOM.style.display = 'inline-block'
          }
        })
      }
    }
  },
  async mounted() {
    let codemirror = this.$refs.markdownEditor.simplemde.codemirror
    codemirror.on('focus', this.handleFocus)
    codemirror.on('blur', this.handleBlurEvent)

    const mdEditor = this.$el.querySelector('.md-editor')
    this.emitDisabled = this.getIsDisabledByAttribute(mdEditor)

    const config = { attributes: true, childList: false, subtree: false }
    const observer = new MutationObserver((mutationsList) => {
      for (let item of mutationsList) {
        const { target, attributeName } = item

        if ('disabled' === attributeName) {
          this.emitDisabled = this.getIsDisabledByAttribute(target)
          break
        }
      }
    })
    observer.observe(mdEditor, config)
    this.observer = observer

    this.handleInput()

    klook.bus.$on('markdownCodemirrorRefresh', this.markdownCodemirrorRefresh)

    await this.$nextTick()
    this.markdownCodemirrorRefresh()
  },
  beforeDestroy() {
    this.observer && this.observer.disconnect()
    klook.bus.$off('markdownCodemirrorRefresh', this.markdownCodemirrorRefresh)

    let codemirror = this.$refs.markdownEditor?.simplemde?.codemirror
    codemirror?.off?.('blur', this.handleBlurEvent)
  },
  methods: {
    onMouseenter() {
      this.isEnter = true
    },
    onMouseleave() {
      this.isEnter = false
    },
    handleFocus() {
      this.$emit('focus')
    },
    handleBlurEvent() {
      if (!this.isEnter) {
        this.$emit('blur')
      }
    },
    markdownCodemirrorRefresh() {
      this.$refs.markdownEditor.simplemde?.codemirror?.refresh?.()
    },
    getSuffixCount(length = 0) {
      const max = this.maxLength
      return getSuffixCount(max, length)
    },
    getIsDisabledByAttribute(target) {
      return target && ['disabled', 'true'].includes(target.getAttribute('disabled'))
    },
    handleInput: _.debounce(function() {
      // 限制输入的长度
      const valueLength = this.child_value?.trim()?.length
      const max = this.maxLength
      const isInfinity = max === Infinity
      const mdEditor = this.$refs.markdownEditor?.$el
      const textarea = mdEditor?.querySelectorAll('textarea') ?? []
      textarea.forEach((item) => {
        const len = max - valueLength
        if (!isInfinity) {
          item.maxLength = len > 0 ? len : 0
        }
      })
    }, 600),
    onInitialized() {
      this.$emit('initialized')
    }
  }
}
</script>

<style lang="scss">
.simple-md-container {
  &.disabled-hide-tool-style {
    .editor-toolbar.disabled-for-preview {
      display: none;
    }
  }
}
.markdown-editor {
  &.common-md-style {
    position: relative;

    &[disabled] {
      cursor: not-allowed;
    }

    .CodeMirror {
      min-height: 200px;
      line-height: 24px;
      .CodeMirror-scroll {
        min-height: 180px;
        max-height: 300px;
      }
    }
    &.small-type .CodeMirror {
      min-height: 82px;
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      color: #141414;
      .CodeMirror-scroll {
        min-height: 60px;
        max-height: 200px;
      }
    }
  }
  ul {
    padding-inline-start: 40px !important;
    li {
      list-style-type: disc;
    }
  }
}

.simple-md-container .common-text-count.simple-md {
  right: 0;
  bottom: -22px;
  height: 22px;
  line-height: 22px;
}
</style>
