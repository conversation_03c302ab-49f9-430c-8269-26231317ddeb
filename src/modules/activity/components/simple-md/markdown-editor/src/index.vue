<!-- https://github.com/F-loat/vue-simplemde/blob/v0.4.0/src/markdown-editor.vue -->
<!-- 待升级 vue-simplemde -->
<template>
  <div class="markdown-editor">
    <textarea></textarea>
  </div>
</template>

<script>
import SimpleMDE from 'simplemde'

export default {
  name: 'MarkdownEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    previewClass: {
      type: String,
      default: ''
    },
    autoinit: {
      type: Boolean,
      default() {
        return true
      }
    },
    customTheme: {
      type: Boolean,
      default() {
        return false
      }
    },
    configs: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  watch: {
    value(val) {
      if (val === this.simplemde.value()) return
      this.simplemde.value(val)
    }
  },
  mounted() {
    if (this.autoinit) this.initialize()
  },
  activated() {
    const editor = this.simplemde
    if (!editor) return
    const isActive = editor.isSideBySideActive() || editor.isPreviewActive()
    if (isActive) editor.toggleFullScreen()
  },
  destroyed() {
    this.simplemde = null
  },
  methods: {
    initialize() {
      const configs = {}
      Object.assign(configs, this.configs)
      configs.element = configs.element || this.$el.firstElementChild
      configs.initialValue = configs.initialValue || this.value

      // 实例化编辑器
      this.simplemde = new SimpleMDE(configs)

      // 判断是否开启代码高亮
      if (configs.renderingConfig && configs.renderingConfig.codeSyntaxHighlighting) {
        import('highlight.js').then((hljs) => {
          window.hljs = hljs
        })
      }

      // 添加自定义 previewClass
      const className = this.previewClass || ''
      this.addPreviewClass(className)

      // 绑定事件
      this.bindingEvents()

      this.$emit('initialized')
    },
    bindingEvents() {
      this.simplemde.codemirror.on('change', () => {
        this.$emit('input', this.simplemde.value())
      })
    },
    addPreviewClass(className) {
      const wrapper = this.simplemde.codemirror.getWrapperElement()
      const preview = document.createElement('div')
      wrapper.nextSibling.className += ` ${className}`
      preview.className = `editor-preview ${className}`
      wrapper.appendChild(preview)
    }
  }
}
</script>

<style>
.markdown-editor .markdown-body {
  padding: 0.5em;
}

.markdown-editor .editor-preview-active,
.markdown-editor .editor-preview-active-side {
  display: block;
}
</style>
