const Vue = require('vue').default
import phoneboxVue from './main.vue'
const PhoneBoxConstructor = Vue.extend(phoneboxVue)
let instance

const initInstance = () => {
  instance = new PhoneBoxConstructor({
    el: document.createElement('div')
  })

  document.body.appendChild(instance.$el)
}

const PhoneBox = function() {
  return new Promise((resolve, reject) => {
    // eslint-disable-line
    if (!instance) {
      initInstance()
    }
    instance.dialogVisible = true
    instance.callback = resolve
  })
}

export default PhoneBox
export { PhoneBox }
