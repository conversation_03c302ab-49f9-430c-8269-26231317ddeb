<template>
  <a-modal v-model="dialogVisible" title="Phone Number">
    <a-form :form="phoneboxForm" layout="inline">
      <a-form-item label="" prop="countryCode">
        <a-select
          v-decorator="[
            'countryCode',
            {
              rules: [
                { required: true, message: 'Please select an area code.' }
              ]
            }
          ]"
          show-search
          style="width: 200px"
          placeholder="Select a area code"
        >
          <a-select-option
            v-for="(item, index) in countryCodeList"
            :key="index"
            :value="item.country_number + item.full_name"
            >(+{{ item.country_number }}){{ item.full_name }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item label="" prop="phoneNumber">
        <a-input
          v-decorator="[
            'phoneNumber',
            {
              rules: [{ required: true, message: 'Please input phone number.' }]
            }
          ]"
          placeholder="Phone Number"
        />
      </a-form-item>
    </a-form>
    <div slot="footer" class="dialog-footer">
      <a-button @click="dialogVisible = false">cancel</a-button>
      <a-button type="primary" @click="confirm()">confirm</a-button>
    </div>
  </a-modal>
</template>

<script>
import { parsePlatform } from '@activity/utils'
export default {
  data() {
    return {
      dialogVisible: false,
      countryCodeList: []
    }
  },
  beforeCreate() {
    this.phoneboxForm = this.$form.createForm(this, { name: 'phoneboxForm' })
  },
  async mounted() {
    await this.loadCountryCode()
  },
  methods: {
    closeModal() {
      this.phoneboxForm.resetFields()
      this.dialogVisible = false
    },
    confirm() {
      this.phoneboxForm.validateFields((err, values) => {
        if (!err) {
          this.callback({
            prefix: `+${values.countryCode.match(/\d+/g)[0]}`,
            phoneNumber: values.phoneNumber
          })
          this.closeModal()
        }
      })
    },
    async loadCountryCode() {
      await ajax
        .get(parsePlatform(ADMIN_API.act.get_all_countries))
        .then((data) => {
          this.countryCodeList = data
        })
    }
  }
}
</script>

<style lang="scss" scoped></style>
