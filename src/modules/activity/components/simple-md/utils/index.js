import { cloudinaryOptions } from '@activity/components/photo-group/const/index.js'
import { photo_tags } from '@activity/common/const'
import PhoneBox from '@activity/components/simple-md/phoneBox/main.js'
import { toggleHeading, toggleSideBySide } from './toggle'
export { toggleHeading, toggleSideBySide }

export const newMarkdownDefaultFormatValue = [
  'h1',
  'h2',
  'bold',
  'hyperlink',
  'image',
  'bullet_list',
  'phone',
  'strike'
]

export const sideBySidePreview = {
  name: 'sideBySidePreview',
  className: 'fa fa-columns fa-side-by-side-to-preview',
  action: toggleSideBySide
}

export const uploadImage = {
  name: 'image',
  title: 'Upload image',
  className: 'fa fa-picture-o',
  action: async function customFunction(editor, value) {
    var [result, error] = await new Promise((resolve) =>
      window.cloudinary.openUploadWidget(
        Object.assign(cloudinaryOptions(), {
          folder: 'admin-markdown',
          client_allowed_formats: ['png', 'jpg', 'jpeg', 'svg'],
          tags: [photo_tags.act_markdown],
          multiple: false
        }),
        (error, result) => {
          if (result.event === 'success') {
            resolve([[result.info || {}], error])
            // this.$message.success(__('global_success'))
          }
        }
      )
    )

    if (error) {
      // console.error(error)
      return
    }
    var cm = editor.codemirror
    var img = result[0]
    var imageName = img.original_filename
    var url = img.url.replace('http', 'https')
    var output = `![${imageName}](${url})`
    cm.replaceSelection(output)
  }
}

export const addPhoneCall = {
  name: 'phoneCall',
  title: 'phone call',
  className: 'fa fa-phone',
  action: function(editor) {
    let cm = editor.codemirror
    PhoneBox().then((result) => {
      let output = `[${result.prefix} ${result.phoneNumber}](tel:${result.prefix}${result.phoneNumber})`
      cm.replaceSelection(output)
      cm.focus()
    })
  }
}

export function getHeadingConf(level) {
  return {
    name: `heading-${level}`,
    className: `fa fa-header fa-header-x fa-header-${level}`,
    action: function(editor) {
      toggleHeading(editor.codemirror, undefined, level)
    }
  }
}

export const fullToolbar = [
  'bold',
  'italic',
  {
    obtValue: 'strike',
    value: 'strikethrough',
    cmClass: ['cm-formatting-strong']
  },
  // 'heading',
  // 'heading-smaller',
  // 'heading-bigger',
  ...Array(6)
    .fill()
    .map((_, i) => {
      const level = i + 1
      return {
        obtValue: `h${level}`,
        value: getHeadingConf(level)
      }
    }),
  {
    obtValue: 'bullet_list',
    value: 'unordered-list'
  },
  {
    obtValue: 'ordered_list',
    value: 'ordered-list'
  },
  {
    obtValue: 'hyperlink',
    value: 'link'
  },
  {
    obtValue: 'image',
    value: uploadImage
  },
  {
    obtValue: 'phone',
    value: addPhoneCall
  },
  {
    obtValue: 'sideBySidePreview',
    value: sideBySidePreview
  }
  // 'code',
  // 'quote',
  // 'clean-block',
  // 'table',
  // 'horizontal-rule',
  // 'preview',
  // 'side-by-side',
  // 'fullscreen',
  // 'guide'
]

export function getToolbarByNodes(nodes = []) {
  if (!nodes.length) {
    return fullToolbar
  }

  return fullToolbar.reduce(
    (acc, curr) => {
      const obtValue = typeof curr === 'string' ? curr : curr.obtValue
      const value = typeof curr === 'string' ? curr : curr.value

      if (nodes.includes(obtValue)) {
        acc.toolbar.push(value)
      } else {
        acc._invalid.push(obtValue)
      }

      return acc
    },
    {
      toolbar: [],
      _invalid: []
    }
  )
}
