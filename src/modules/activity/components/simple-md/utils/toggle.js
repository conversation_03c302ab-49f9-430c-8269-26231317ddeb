export function toggleHeading(cm, direction, size) {
  if (/editor-preview-active/.test(cm.getWrapperElement().lastChild.className)) return

  var startPoint = cm.getCursor('start')
  var endPoint = cm.getCursor('end')
  for (var i = startPoint.line; i <= endPoint.line; i++) {
    ;(function(i) {
      var text = cm.getLine(i)
      var currHeadingLevel = text.search(/[^#]/)

      if (direction !== undefined) {
        if (currHeadingLevel <= 0) {
          if (direction == 'bigger') {
            text = '###### ' + text
          } else {
            text = '# ' + text
          }
        } else if (currHeadingLevel == 6 && direction == 'smaller') {
          text = text.substr(7)
        } else if (currHeadingLevel == 1 && direction == 'bigger') {
          text = text.substr(2)
        } else {
          if (direction == 'bigger') {
            text = text.substr(1)
          } else {
            text = '#' + text
          }
        }
      } else {
        const prefix =
          Array(size)
            .fill('#')
            .join('') + ' '
        if (currHeadingLevel <= 0) {
          text = prefix + text
        } else if (currHeadingLevel == size) {
          text = text.substr(currHeadingLevel + 1)
        } else {
          text = prefix + text.substr(currHeadingLevel + 1)
        }
      }

      cm.replaceRange(
        text,
        {
          line: i,
          ch: 0
        },
        {
          line: i,
          ch: 99999999999999
        }
      )
    })(i)
  }
  cm.focus()
}

export function toggleSideBySide(editor) {
  const toolbarButton = editor?.toolbarElements?.sideBySidePreview

  if (!editor || !toolbarButton) return

  const cm = editor.codemirror
  const wrapper = cm.getWrapperElement()
  const preview = wrapper.nextSibling
  const sideBySideContainerEle = editor.gui.sideBySide
  let useSideBySideListener = false
  if (/activate/.test(toolbarButton.className)) {
    preview.className = preview.className.replace(/\s*editor-preview-active-side\s*/g, '')
    toolbarButton.className = toolbarButton.className.replace(/\s*activate\s*/g, '')
    wrapper.className = wrapper.className.replace(/\s*CodeMirror-sided\s*/g, ' ')
    sideBySideContainerEle.style.display = 'none'
    editor.gui.toolbar.parentNode.parentNode.classList.remove('--is-preview')
  } else {
    toolbarButton.className += ' activate'
    wrapper.className += ' CodeMirror-sided'
    useSideBySideListener = true
    sideBySideContainerEle.style.display = 'block'
    editor.gui.toolbar.parentNode.parentNode.classList.add('--is-preview')
  }

  // Hide normal preview if active
  const previewNormal = wrapper.lastChild
  if (/editor-preview-active/.test(previewNormal.className)) {
    previewNormal.className = previewNormal.className.replace(/\s*editor-preview-active\s*/g, '')
    const toolbar = editor.toolbarElements.preview
    const toolbar_div = wrapper.previousSibling
    toolbar.className = toolbar.className.replace(/\s*active\s*/g, '')
    toolbar_div.className = toolbar_div.className.replace(/\s*disabled-for-preview*/g, '')
  }

  // poi page 有特殊逻辑：预览时要把文案中的{{poi}}占位替换成真实的poi
  const poiConfig = editor?.options?.poiConfig ?? null

  const sideBySideRenderingFunction = function() {
    preview.innerHTML = editor.options.previewRender(editor.value(), preview, poiConfig)
  }

  if (!cm.sideBySideRenderingFunction) {
    cm.sideBySideRenderingFunction = sideBySideRenderingFunction
  }

  if (useSideBySideListener) {
    preview.innerHTML = editor.options.previewRender(editor.value(), preview, poiConfig)
    cm.on('update', cm.sideBySideRenderingFunction)
  } else {
    cm.off('update', cm.sideBySideRenderingFunction)
  }

  // Refresh to fix selection being off (#309)
  cm.refresh()
}
