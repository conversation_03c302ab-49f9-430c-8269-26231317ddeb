<template>
  <a-form-model ref="form" class="single-widget" :model="form" label-width="0">
    <DisabledWidgetLayer :data="value" />
    <a-form-model-item prop="value" :rules="rules">
      <a-input
        v-model="form.value"
        :max-length="maxLength"
        :class="getClassList"
        :disabled="readonly"
        @blur="blurInput"
      >
        <span slot="suffix">{{ suffix }}</span>
      </a-input>
      <template v-if="refData">
        <a-input v-model="refData.value" style="margin-top: 12px;" disabled />
      </template>
    </a-form-model-item>
  </a-form-model>
</template>

<script>
import DisabledWidgetLayer from '@activity/components/DisabledWidgetLayer.vue'
import AFormModelItem from 'ant-design-vue/es/form-model/FormItem'
import { getWordCountLimit, getSuffixCount, getWordCountLimitDisabled } from '@activity/utils'

export default {
  name: 'SingleWidget',
  components: {
    DisabledWidgetLayer,
    AFormModelItem
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [Object, undefined],
      required: true,
      default: () => ({})
    },
    required: {
      type: Boolean,
      default: false
    },
    config: {
      type: Object,
      default: () => ({})
    },
    refData: {
      type: Object,
      default: null
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        value: ''
      }
    }
  },
  computed: {
    suffix() {
      const {
        maxLength,
        form: { value = '' }
      } = this
      return getSuffixCount(maxLength, value.length)
    },
    maxLength() {
      const config = this.conf
      const list = config?.word_count_limit ?? []
      return getWordCountLimit(list)
    },
    getClassList() {
      const max = this.maxLength
      return [max > 99 && max < Infinity ? 'common-suffix-count-large' : 'common-suffix-count']
    },
    rules() {
      const maxLength = this.maxLength
      const message = this.$t('83853', { num: maxLength })
      return {
        required: this.required,
        // message: 'Please fill in this field.',
        validator: (rule, value, callback) => {
          const val = value.trim()
          if (val.length > maxLength && getWordCountLimitDisabled(this.readonly)) {
            return callback(new Error(message))
          }
          if (this.required && !val) {
            return callback(new Error('Please fill in this field.'))
          }
          callback()
        }
      }
    },
    conf() {
      return {
        allow_copy: false,
        allow_free_text: false,
        multiple_selection: false,
        ...this.config
      }
    }
  },
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler(v, oldV) {
        if (this.cacheValue === undefined) {
          this.cacheValue = v?.value ?? ''
        }

        if (!_.isEqual(v, oldV)) {
          this.$set(this, 'form', {
            value: '',
            ...v
          })
        }
      }
    },
    form: {
      deep: true,
      handler(v) {
        this.$emit('change', v)
      }
    }
  },
  methods: {
    blurInput() {
      // 数据没更新不需要发送 save2Draft
      const isEqual = _.isEqual(this.cacheValue, this.form.value)
      if (!isEqual || this.hasModified) {
        this.$emit('save2Draft')
        this.hasModified = !isEqual
      }
    },
    validateForm() {
      return new Promise((resolve) => this.$refs.form.validate((valid) => resolve(valid)))
    }
  }
}
</script>
<style lang="scss" scoped>
.single-widget {
  position: relative;
}
</style>
