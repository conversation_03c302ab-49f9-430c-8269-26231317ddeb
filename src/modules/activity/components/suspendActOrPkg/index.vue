<template>
  <a-modal :visible.sync="_visible" :width="920" :title="title" @cancel="() => (_visible = false)">
    <div class="tips">{{ $t('79637') }}</div>

    <a-form-model ref="form" :model="form">
      <a-form-model-item :label="$t('79638')" required prop="suspended_end_time">
        <LocalTimePicker
          v-model="form.suspended_end_time"
          class="form-width"
          type="datetime"
          placeholder="Date & time"
          :disabled-date="disabledDate"
          :show_zone="false"
          :local_fmt="true"
        />
      </a-form-model-item>

      <a-form-model-item :label="$t('79639')" required prop="admin_text_id">
        <a-radio-group v-model="form.admin_text_id" class="reason-list">
          <a-radio v-for="opt of reasonOpts" :id="`reason-${opt.value}`" :key="opt.value" :value="opt.value">
            <label class="opt-label-box" :for="`reason-${opt.value}`">
              <span>{{ opt.label }}</span>
              <span v-if="opt.tips" class="__tips">{{ opt.tips }}</span>
            </label>
          </a-radio>

          <a-input
            v-if="form.admin_text_id === 79642"
            v-model="form.reason"
            type="textarea"
            :placeholder="$t('79643')"
          />
        </a-radio-group>
      </a-form-model-item>
    </a-form-model>
    <template slot="footer">
      <a-button
        type="primary"
        @click="handleSubmit"
        :data-spm-module="getSpmModuleStr(spuTrack)"
        data-spm-virtual-item="__virtual?trg=manual"
        :class="spuTrack.spuModule"
        >OK</a-button
      >
    </template>
  </a-modal>
</template>

<script>
import moment from 'moment'
import { LocalTimePicker } from '@activity/components/index'
import { checkAuth } from '@/plugins/authDirective'

const reasonOpts = [
  {
    label: __('79640'),
    value: 79640
  },
  {
    label: __('79641'),
    value: 79641
  },
  {
    label: __('79653'),
    value: 79653
  },
  {
    label: __('79642'),
    value: 79642
  }
]

export default {
  inject: ['rootChangeLoading'],
  name: 'SuspendActOrPkgModal',
  components: {
    LocalTimePicker
  },
  props: {
    spuId: {
      type: Number
    },
    type: {
      type: String,
      // act / pkg
      default: 'act',
      validator(value) {
        return ['act', 'pkg'].includes(value)
      }
    },
    title: {
      type: String,
      default: 'Suspend activity/package'
    },
    visible: {
      type: Boolean,
      required: true
    }
  },
  data() {
    this.reasonOpts = reasonOpts

    return {
      spuTrack: {
        spuModule: 'SPUSuspend'
      },
      form: {
        suspended_end_time: undefined,
        admin_text_id: 79641,
        reason: ''
      }
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(v) {
      if (v) {
        this.$set(this, 'form', this.$options.data().form)
      }
    }
  },
  methods: {
    disabledDate(current) {
      const maxDays = checkAuth('superSuspendEdit') ? 365 : 90

      return (
        current && (current < moment().startOf('day') || current > moment().add(maxDays, 'days').endOf('day'))
      )
    },
    getSpmModuleStr(item) {
      const moduleName = item.spuModule
      return `${moduleName}?trg=manual`
    },
    sendStatusTrack(item, resp) {
      if (item?.spuModule) {
        const extObj = {
          IsValid: Number(!!resp?.success) // 表示前端校验是否通过
        }
        this.$root.trackIHEvent(`.${item.spuModule}`, extObj)
      }
    },
    async handleSubmit() {
      const valid = await new Promise((resolve) => this.$refs.form.validate(resolve))

      if (valid) {
        this.rootChangeLoading(true)
        const suspendedConfig = {
          ...this.form,
          reason: this.form.admin_text_id === 79642 ? this.form.reason : ''
        }
        let data
        const { spuId } = this
        if (spuId) {
          data = {
            status: 5,
            activity_id: +this.$route.params.id,
            spu_id: +spuId,
            language: klook.getEditLang(),
            page_from: klook.getPlatformRoleKey()
          }
        } else if (this.type === 'act') {
          data = {
            status: 5,
            activity_id: +this.$route.params.id,
            language: klook.getEditLang(),
            page_from: klook.getPlatformRoleKey()
          }
        } else {
          data = {
            status: 2,
            package_id: +this.$route.query.package_id,
            page_from: klook.getPlatformRoleKey()
          }
        }
        let url = this.type === 'act' ? ADMIN_API.act.update_status2act : ADMIN_API.act.update_status2pkg
        if (spuId) {
          url = ADMIN_API.aidRevamp.saveSpuStatus
        }
        const response = await ajax.postBody({
          url,
          data: {
            ...data,
            suspended_config: suspendedConfig
          }
        })

        if (response) {
          if (spuId) {
            klook.bus.$emit('getSpuFloatingFields2bus')
            this.sendStatusTrack(this.spuTrack, response)
          } else {
            klook.bus.$emit('initQueryData2bus')
          }
          this.$emit('submit', this.form)
          this._visible = false
        }
        this.rootChangeLoading(false)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.tips {
  padding: 8px 16px;
  margin-bottom: 24px;
  border: 1px solid #ffe58f;
  border-radius: 2px;
  background-color: #fffbe6;
}

.reason-list {
  display: flex;
  flex-direction: column;

  ::v-deep .ant-radio-wrapper {
    margin-bottom: 8px;
    line-height: 22px;
  }
}

.opt-label-box {
  display: inline-flex;
  flex-direction: column;
  word-break: break-word;
  white-space: break-spaces;

  .__tips {
    color: #999;
  }
}
</style>
