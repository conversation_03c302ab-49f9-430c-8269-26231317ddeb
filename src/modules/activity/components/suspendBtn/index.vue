<template>
  <span v-if="displaySelf" class="admin-btn-style">
    <a-dropdown :disabled="disabled" @visibleChange="(v) => (dropdown = v)">
      <span>
        <a-button type="danger" ghost>
          {{ $t('79630') }}
          <a-icon :type="dropdown ? 'up' : 'down'" style="font-size: 13px" />
        </a-button>
      </span>
      <a-menu slot="overlay" @click="handleSuspendMenu">
        <template v-if="spuId">
          <!-- spu: key="pkg" -->
          <a-menu-item key="pkg">
            {{ $t('200790') }}
            <a-icon
              v-tooltip="{
                visible: true,
                placement: 'bottomRight',
                content: $t('200791')
              }"
              style="margin-left: 6px"
              type="info-circle"
            />
          </a-menu-item>
        </template>
        <template v-else>
          <a-menu-item v-if="currDisplayAct" key="act">
            {{ $t('79631') }}
            <a-icon
              v-tooltip="{
                visible: true,
                placement: 'bottomRight',
                content: $t('79633')
              }"
              style="margin-left: 6px"
              type="info-circle"
            />
          </a-menu-item>
          <a-menu-item v-if="currDisplayPkg" key="pkg">
            {{ $t('79632') }}
            <a-icon
              v-tooltip="{
                visible: true,
                placement: 'bottomRight',
                content: $t('79634')
              }"
              style="margin-left: 6px"
              type="info-circle"
            />
          </a-menu-item>
        </template>
      </a-menu>
    </a-dropdown>
    <SuspendActOrPkgModal
      v-if="visible"
      :visible.sync="visible"
      :title="title"
      :type="key"
      :spuId="spuId"
      :data-spm-page="dataSpmPage"
      @submit="$emit('submit')"
    />
  </span>
</template>

<script>
import SuspendActOrPkgModal from '@activity/components/suspendActOrPkg/index'

export default {
  name: 'SuspendBtn',
  components: {
    SuspendActOrPkgModal
  },
  props: {
    // spu兼容参数 start
    dataSpmPage: {
      type: String
    },
    spuId: {
      type: Number
    },
    firstPublishTime: {
      default: ''
    },
    // spu兼容参数 end
    type: {
      type: String,
      default: 'act',
      validator(value) {
        return ['act', 'pkg'].includes(value)
      }
    },
    btnTooltipMsg: {
      type: String,
      default: ''
    },
    displayActBtn: {
      type: Boolean,
      default: false
    },
    displayPkgBtn: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dropdown: false,
      key: '',
      visible: false
    }
  },
  computed: {
    title() {
      if (this.spuId) {
        return this.$t('200790')
      }
      return this.key === 'act' ? this.$t('79631') : this.$t('79632')
    },
    currDisplayAct() {
      // 第一次发布后的才能暂停
      const firstPublishTime =
        this.firstPublishTime || (this.$store.state?.actItemData?.first_publish_time ?? '')
      return this.displayActBtn && !!firstPublishTime
    },
    currDisplayPkg() {
      return this.displayPkgBtn && !!this.$route.query.package_id
    },
    displaySelf() {
      return this.currDisplayAct || this.currDisplayPkg
    }
  },
  methods: {
    handleSuspendMenu({ key }) {
      this.key = key
      this.visible = true
    }
  }
}
</script>
