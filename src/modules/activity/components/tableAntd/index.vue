<script>
import Vue from 'vue'
import VueDraggableResizable from 'vue-draggable-resizable'
Vue.component('vue-draggable-resizable', VueDraggableResizable)

import pageAntd from '@activity/components/pageAntd'

export default {
  components: {
    pageAntd
  },
  props: {
    styleType: {
      type: String,
      default: '' // auto-line-break
    },
    isDragOn: {
      default: true
    },
    hideScroll: {
      default: false
    },
    // 表格配置
    tableData: {
      default: () => {
        return {}
      }
    },
    // 分页配置
    pageData: {
      default: () => {
        return {}
      }
    },
    // 分页select组件配置
    selectData: {
      default: () => {
        return {}
      }
    },
    displayTotal: {
      type: Boolean,
      default: false
    },
    showPagination: {
      type: Boolean,
      default: true
    }
  },
  data() {
    const draggingMap = {}
    this.tableData.columns.forEach((col) => {
      this.$set(col, 'width', col.width ? col.width : 160)
      this.$set(col, 'ellipsis', col.ellipsis === false ? false : true)
      draggingMap[col.key] = col.width
    })
    const draggingState = Vue.observable(draggingMap)
    const resizeableTitle = (h, props, children) => {
      let thDom = null
      const { key, ...restProps } = props
      const col = this.tableData.columns.find((col) => {
        const k = col.dataIndex || col.key
        return k === key
      })
      if (!col.width) {
        return <th {...restProps}>{children}</th>
      }
      const onDrag = (x) => {
        draggingState[key] = 0
        col.width = Math.max(x, 1)
      }

      const onDragstop = () => {
        draggingState[key] = thDom.getBoundingClientRect().width
      }
      return (
        <th {...restProps} v-ant-ref={(r) => (thDom = r)} width={col.width} class="resize-table-th">
          {children}
          <vue-draggable-resizable
            key={col.key}
            class="table-draggable-handle"
            w={10}
            x={draggingState[key] || col.width}
            z={1}
            axis="x"
            draggable={true}
            resizable={false}
            onDragging={onDrag}
            onDragstop={onDragstop}
          ></vue-draggable-resizable>
        </th>
      )
    }
    this.components = this.isDragOn
      ? {
          header: {
            cell: resizeableTitle
          }
        }
      : {}
    return {
      tableProps: {
        // locale: {
        //   emptyText: <a-empty :image="" description="No Data Fauld"></a-empty>
        // },
        bordered: true,
        pagination: {
          pageSize: 10,
          current: 1
        },
        scroll: { x: '100%' }
      },
      pageProps: {
        pageSize: 10,
        current: 1,
        total: this.tableData.total || (this.tableData.dataSource && this.tableData.dataSource.length) || 0,
        on: {
          change: (page, pageSize) => {
            let { tableProps, pageProps } = this
            tableProps.pagination.current = page
            pageProps.current = page
            this.$emit('pageChange', page, pageSize)
          }
        }
      },
      selectProps: {
        value: 10,
        on: {
          change: (value, option) => {
            let { tableProps, pageProps, selectProps } = this
            tableProps.pagination.pageSize = pageProps.pageSize = selectProps.value = value
            pageProps.on.change(1, value)
          }
        }
      }
    }
  },
  render(h) {
    let { tableProps, tableData, pageData, selectData, pageProps, selectProps } = this

    // 不使用table默认的分页，样式不支持，使用自己封装的pageAntd分页组件
    let newTableProps = _.merge({}, tableProps, tableData)
    if (newTableProps.dataSource && newTableProps.dataSource.length > 0) {
      let hasRejectReason = newTableProps.dataSource.some((item) => item.tag)
      if (hasRejectReason) {
        newTableProps.columns.forEach((item) => {
          if (item.dataIndex === 'ticket_status') {
            item.width = 280
          }
        })
      }
    }
    let newPageProps = _.merge({}, pageProps, pageData)
    let newSelectProps = _.merge({}, selectProps, { value: newPageProps.pageSize }, selectData)
    let slots = Object.keys(this.$slots).map((slot) => {
      return <template slot={slot}>{this.$slots[slot]}</template>
    })
    let tableNode = (
      <a-table
        props={newTableProps}
        on={newTableProps.on}
        components={this.components}
        scopedSlots={this.$scopedSlots}
      >
        {slots}
      </a-table>
    )
    let vNode = (
      <div class={`table-antd ${this.styleType}`}>
        <div class={this.hideScroll ? 'table-antd-box table-hide-scroll' : 'table-antd-box'}>{tableNode}</div>
        {newTableProps.pagination && (
          <div class="page-antd-box table-footer">
            <span class="table-footer-count">
              {this.displayTotal ? this.$t('112744', { num: this.pageData.total }) : ''}
            </span>
            {this.showPagination ? (
              <pageAntd pageData={newPageProps} selectData={newSelectProps}></pageAntd>
            ) : (
              ''
            )}
          </div>
        )}
      </div>
    )
    return vNode
  }
}
</script>
<style lang="scss">
.table-antd {
  .ant-pagination.ant-table-pagination {
    display: none;
  }
}
.resize-table-th {
  position: relative;
  .table-draggable-handle {
    height: 100% !important;
    bottom: 0;
    left: auto !important;
    right: -5px;
    cursor: col-resize;
    touch-action: none;
    transform: none !important;
    position: absolute;
  }
}
</style>
<style lang="scss" scoped>
.auto-line-break.table-antd ::v-deep {
  td {
    word-break: break-word;
    white-space: normal;
  }
}
.table-antd {
  position: relative;
  .page-antd-box {
    padding: 24px 0 0 0;

    &.table-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .table-footer-count {
        font-weight: bold;
      }
    }
  }
}
</style>
<style lang="scss">
.table-antd-box {
  &.table-hide-scroll {
    .ant-table-header.ant-table-hide-scrollbar {
      overflow: scroll auto !important;
    }
  }
}
</style>
