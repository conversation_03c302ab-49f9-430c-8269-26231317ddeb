<!-- usage:  -->
<!--    -->

<!-- https://github.com/vuejs-tips/v-money/issues/53 -->

<template>
  <div style="min-height: 300px; margin-top: 52px" class="country">
    <a-spin :spinning="spinning" :delay="500" tip="Loading...">
      <a-input-search
        placeholder="input search text"
        style="width: 250px; margin-bottom: 12px"
        v-model="searchValue"
        @search="debounceSearch"
      />

      <a-checkbox-group v-model="checkedCountries" class="countries-list">
        <div v-for="(country, index) in chosenResult" :key="country.country_id" class="countries-item">
          <a-checkbox
            v-if="countryonly"
            type="checkbox"
            name="popup-checkbox"
            class="__checkbox"
            :value="country.country_id"
          >
            {{ country.country_id }} - {{ country.country_name }}
          </a-checkbox>

          <city-checkbox
            v-else
            class="__city-checkbox"
            v-model="country.city_ids"
            :language="language"
            :parent_name="country.country_name"
            :parent_label="country.country_id"
            @change="(v) => changeCity(country, index, v)"
            @get_options="(options) => getOptions(options, country)"
          />
        </div>
      </a-checkbox-group>
    </a-spin>
  </div>
</template>

<script>
import PopupCheckbox from './city-checkbox.vue'
import { mapGetters, mapMutations } from 'vuex'
import Fuse from 'fuse.js'

export default {
  // store,
  name: 'TaxonomyPicker',
  install(Vue) {
    Vue.component(this.name, this)
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    language: { type: String, required: false },
    type: String,
    countryonly: Boolean,
    value: Array
    // type: Array
  },
  components: {
    CityCheckbox: PopupCheckbox
  },
  data() {
    return {
      fuse: null,
      chosen: [],
      chosenResult: [],
      checkedCountries: [],
      spinning: true,
      searchValue: ''
    }
  },
  computed: {
    global_data() {
      return this.getAllCountries()
    }
  },
  created() {
    window.checkedCountries = this.checkedCountries
  },
  watch: {
    async checkedCountries(v, oldv) {
      if (_.isEqual(v, oldv)) {
        return
      }
      let chosen
      if (this.type === 'country') {
        chosen = this.global_data.filter((cc) => v.includes(cc.country_id))
      } else {
        chosen = await Promise.all(
          _.cloneDeep(this.chosen).map(async (country) => {
            if (v.includes(country.country_id)) {
              let cities = await ajax.get(ADMIN_API.act.get_all_cities_under_id, {
                params: {
                  language: this.language,
                  id: country.country_id
                }
              })

              this.global_data.find((cc) => cc.country_id === country.country_id).cities = cities

              return {
                ...country,
                cities,
                city_ids: country.city_ids.length ? country.city_ids : cities.map((v) => v.id)
              }
            } else {
              return { cities: [], ...country, city_ids: [] }
            }
          })
        )
        // this.chosen = chosen;
      }
      this.$emit('change', chosen, this)
    },
    value: {
      immediate: true,
      deep: true,
      async handler(v) {
        this.spinning = true
        // init global data
        if (_.isEmpty(this.global_data)) {
          let result = await ajax.get(ADMIN_API.act.get_all_countries, {
            params: {
              language: this.language
            }
          })
          let items = (result || []).map((item) => ({
            country_id: item.id,
            country_name: item.name
          }))

          this.setAllCountries(_.sortBy(items, 'country_name'))
        }

        let ids = v.map((item) => item.id)
        if (this.type === 'country') {
          this.checkedCountries = v.map((item) => item.id)
          this.chosen = this.global_data
        } else {
          if (ids.length) {
            let city_details = await ajax.post(ADMIN_API.act.get_country_node_name_info_by_idlist, {
              data: {
                language: this.language,
                id_list: ids
              }
            })

            this.chosen = this.global_data.map((country) => ({
              ...country,
              city_ids: city_details
                .filter((matched) => matched.country.id == country.country_id)
                .map((matched) => matched.id)
            }))
          } else {
            this.chosen = this.global_data.map((item) => ({
              cities: [],
              ...item,
              city_ids: []
            }))
          }

          this.checkedCountries = this.chosen
            .filter((item) => item.city_ids.length >= 1)
            .map((item) => item.country_id)
        }
        this.spinning = false
      }
    },
    chosen: {
      immediate: true,
      deep: true,
      handler(v) {
        if (v?.length) {
          this.debounceSearch()
          this.spinning = false
        }
      }
    },
    searchValue() {
      this.debounceSearch()
    }
  },
  methods: {
    ...mapGetters(['getAllCountries']),
    ...mapMutations(['setAllCountries']),

    getOptions(options, country) {
      this.chosen.find((cc) => +cc.country_id === +country.country_id).cities = options
      this.global_data.find((cc) => +cc.country_id === +country.country_id).cities = options
    },
    changeCity(country, index, v) {
      let chosen = _.cloneDeep(this.chosen)
      chosen.splice(index, 1, {
        ...chosen[index],
        city_ids: v
      })
      // this.chosen = chosen;
      this.$emit('change', chosen)
    },
    debounceSearch: _.debounce(function () {
      if (this.searchValue.trim()) {
        if (!this.fuse) {
          this.fuse = new Fuse(this.chosen, {
            tokenize: true,
            matchAllTokens: true,
            minMatchCharLength: 2,
            keys: ['country_id', 'country_name']
          })
        }

        this.chosenResult = this.fuse.search(this.searchValue)
      } else {
        this.chosenResult = this.chosen
      }
    }, 1000 / 60)
  }
}
</script>

<style lang="scss" scoped>
.countries-list {
  display: flex;
  flex-wrap: wrap;
  .countries-item {
    width: 50%;
    padding: 12px 0;
  }
  span.__city-checkbox {
    display: flex;
    ::v-deep .ant-checkbox-wrapper {
      display: flex;
      word-break: break-all;
      vertical-align: text-top;
      .ant-checkbox {
        padding-top: 5px;
      }
    }
  }
}
</style>
