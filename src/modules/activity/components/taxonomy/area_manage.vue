<template>
  <div style="margin-top: 20px">
    <a-form-model ref="searchForm" :rules="searchRules" :model="searchForm" label-width="200px">
      <a-form-model-item prop="name" :label="$t('taxonomy_filter_admin_region')">
        <a-input v-model="searchForm.name" :placeholder="$t('taxonomy_filter_admin_region_placeholder')" />
      </a-form-model-item>

      <a-form-model-item prop="parent_name" :label="$t('taxonomy_filter_parent_region')">
        <a-input v-model="searchForm.parent_name" />
      </a-form-model-item>

      <a-form-model-item prop="is_city" :label="$t('taxonomy_filter_city')">
        <a-select v-model="searchForm.is_city" :placeholder="$t('global_please_select')">
          <a-select-option :label="$t('taxonomy_filter_all')" :value="''">{{
            $t('taxonomy_filter_all')
          }}</a-select-option>
          <a-select-option :label="$t('taxonomy_filter_city_yes')" :value="1">{{
            $t('taxonomy_filter_all')
          }}</a-select-option>
          <a-select-option :label="$t('taxonomy_filter_city_no')" :value="0">{{
            $t('taxonomy_filter_all')
          }}</a-select-option>
        </a-select>
      </a-form-model-item>

      <a-form-model-item prop="publish_status" :label="$t('taxonomy_filter_status')">
        <a-select v-model="searchForm.publish_status" :placeholder="$t('global_please_select')">
          <a-select-option :label="$t('taxonomy_filter_all')" :value="''">{{
            $t('taxonomy_filter_all')
          }}</a-select-option>
          <a-select-option :label="$t('taxonomy_filter_available')" :value="1">{{
            $t('taxonomy_filter_available')
          }}</a-select-option>
          <a-select-option :label="$t('taxonomy_filter_unavailable')" :value="0">{{
            $t('taxonomy_filter_unavailable')
          }}</a-select-option>
        </a-select>
      </a-form-model-item>

      <a-form-model-item>
        <a-button type="primary" @click="handleSearch">{{ $t('taxonomy_filter') }}</a-button>
        <a-button type="primary" @click="handleResetSearch">{{ $t('taxonomy_reset') }}</a-button>
      </a-form-model-item>
    </a-form-model>

    <a-button type="primary" @click="handleAdd">+ {{ $t('taxonomy_create_admin_region') }}</a-button>
    <div style="margin-top: 10px">
      <tree-table
        ref="tree-table"
        :columns="columns"
        :tree-structure="true"
        :data-source="dataSource"
        :default-expand-all="expandAll"
        @get-child-node="handleInsertNode"
      >
        <slot>
          <a-table-column :label="$t('taxonomy_list_actions')" width="120">
            <template slot-scope="scope">
              <a-button type="text" @click="handleEdit(scope.row)">
                {{ $t('global_button_handle') }}
              </a-button>
              <a-button type="text" @click="handleDelete(scope.row)">
                {{ $t('global_delete') }}
              </a-button>
            </template>
          </a-table-column>
        </slot>
      </tree-table>
    </div>
    <!-- <common-pagination v-model="page" @refresh="refresh"></common-pagination> -->

    <a-modal :title="title" :visible.sync="showAreaForm" :before-close="areaDialogHide" size="large">
      <a-form-model ref="dialogForm" :model="dialogForm" label-width="120px">
        <a-row>
          <!-- 左边一列 -->
          <a-col :span="12">
            <a-form-model-item prop="name" :label="$t('taxonomy_dialog_name')" :rules="{ required: true }">
              <a-input v-model="dialogForm.name" :placeholder="$t('global_validate_text_required')" />
            </a-form-model-item>
            <a-form-model-item
              prop="parentName"
              :label="$t('taxonomy_dialog_parent_region')"
              :rules="{ required: true }"
            >
              <a-autocomplete
                v-model="dialogForm.parentName"
                popper-class="autocomplete-popper"
                custom-item="auto-complete-item"
                :disabled="disableParentName"
                :fetch-suggestions="querySearchAsync"
                :placeholder="$t('taxonomy_dialog_parent_region_placeholder')"
                @select="handleSelectSearchResult"
              />
            </a-form-model-item>
            <a-form-model-item prop="country_code_thd" :label="$t('taxonomy_dialog_country_code_3')">
              <a-input v-model="dialogForm.country_code_thd" :disabled="disableCountryCodeThd" />
            </a-form-model-item>
            <a-form-model-item prop="country_code" :label="$t('taxonomy_dialog_country_code_2')">
              <a-input v-model="dialogForm.country_code" :disabled="disableCountryCode" />
            </a-form-model-item>
            <a-form-model-item prop="currency" :label="$t('taxonomy_dialog_currency')">
              <a-select v-model="dialogForm.currency" :disabled="disableCurrency" show-search>
                <a-select-option
                  v-for="item in currencyOpts"
                  :key="item.currency"
                  :label="`${item.currency_i18n}(${item.currency})`"
                  :value="item.currency"
                  >{{ `${item.currency_i18n}(${item.currency})` }}</a-select-option
                >
              </a-select>
            </a-form-model-item>
            <a-form-model-item prop="is_city" :label="$t('taxonomy_dialog_city')" :rules="{ required: true }">
              <a-select v-model="dialogForm.is_city" :disabled="disableIsCity">
                <a-select-option :label="$t('taxonomy_filter_city_yes')" :value="1">{{
                  $t('taxonomy_filter_city_yes')
                }}</a-select-option>
                <a-select-option :label="$t('taxonomy_filter_city_no')" :value="0">{{
                  $t('taxonomy_filter_city_no')
                }}</a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item
              prop="publish_status"
              :label="$t('taxonomy_dialog_status')"
              :rules="{ required: true }"
            >
              <a-select v-model="dialogForm.publish_status" :disabled="disablePublishStatus">
                <a-select-option :label="$t('taxonomy_filter_available')" :value="1">{{
                  $t('taxonomy_filter_available')
                }}</a-select-option>
                <a-select-option :label="$t('taxonomy_filter_unavailable')" :value="0">{{
                  $t('taxonomy_filter_unavailable')
                }}</a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item prop="alias_name" :label="$t('taxonomy_synonyms')">
              <input-tag
                v-model="dialogForm.alias_name"
                :placeholder="$t('taxonomy_dialog_nicknames_placeholder')"
              />
            </a-form-model-item>
          </a-col>
          <!-- 右边一列 -->
          <a-col :span="12">
            <a-form-model-item prop="address" :label="$t('taxonomy_dialog_coordinates')">
              <a-row>
                <a-col :span="16">
                  <a-input
                    v-model="dialogForm.address"
                    :placeholder="$t('act_gps_location')"
                    :disabled="disableAddress"
                  />
                </a-col>
                <a-col :span="6" :offset="2">
                  <a-button type="primary" :disabled="disableAddress" @click="handleCheckCoordinate">
                    查询
                  </a-button>
                </a-col>
              </a-row>
              <area-map
                ref="map"
                :key="dialogForm.place_id"
                :address="dialogForm.address"
                :place="dialogForm.place_id"
                @map="getMapInfo"
              />
              <div>
                <span>Google Place ID: </span>
                <span>{{ dialogForm.place_id }}</span>
              </div>
              <div>
                <span>{{ $t('taxonomy_dialog_time_zone') }}: </span>
                <span>{{ dialogForm.time_zone }}</span>
              </div>
              <a-table :data="fixLocation" style="width: 100%">
                <a-table-column prop="lng" :label="$t('taxonomy_dialog_longitude')" />
                <a-table-column prop="lat" :label="$t('taxonomy_dialog_latitude')" />
              </a-table>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <span slot="footer">
        <a-button type="primary" @click="handleSaveForm">{{ $t('global_button_save') }}</a-button>
        <a-button @click="showAreaForm = false">{{ $t('global_button_cancel') }}</a-button>
      </span>
    </a-modal>
    <!-- 编辑语言 -->
    <a-modal :title="$t('act_pub_lan')" :visible.sync="showEditLang">
      <a-table :data="langTable" style="width: 100%">
        <a-table-column :label="$t('global_language')" prop="value" width="180" />
        <a-table-column :label="$t('global_button_handle')">
          <template slot-scope="scope">
            <a-button size="small" type="text" @click="handleEditDialog(scope.row)"
              >{{ $t('global_button_edit') }}
            </a-button>
          </template>
        </a-table-column>
      </a-table>
    </a-modal>
  </div>
</template>

<script>
import Vue from 'vue'
import AreaMap from './area_map.vue'
import InputTag from '@activity/components/input_tag'

import { KEY, findNodeOption, insertChildNode, formatRawOffset, formatToTree } from './taxonomy_utils.js'
import TreeTable from './tree_table.vue'

Vue.component('auto-complete-item', {
  functional: true,
  props: {
    item: { type: Object, required: true }
  },
  render: function (h, ctx) {
    const { item } = ctx.props
    return h('div', ctx.data, [
      h('div', { attrs: { class: 'name' } }, [item.i18n.name]),
      h('span', { attrs: { class: 'parent' } }, [item.parent_i18n.name && item.parent_i18n.name + ', ']),
      h('span', { attrs: { class: 'parent' } }, [item.country_code_thd])
    ])
  }
})

const initLangTable = () => {
  return Object.entries(lang_conf.getLangObj('B_LANG', 'LANG_TITLE')).map(([key, value]) => ({
    key,
    value
  }))
}

const dialogMixin = {
  data() {
    return {
      showAreaForm: false,
      title: this.$t('taxonomy_dialog_title'),
      // 别称
      aliasTags: [],
      fileData: [],
      currencyOpts: [],
      nodeOpts: [
        {
          ...KEY.root,
          children: []
        }
      ],
      rootOpts: [
        {
          id: KEY.root.id,
          value: this.$t('taxonomy_root_menu'),
          i18n: {
            name: this.$t('taxonomy_root_menu')
          },
          parent_i18n: {
            name: ''
          },
          path: KEY.root.id.toString(),
          country_code_thd: '',
          country_code: '',
          currency: '',
          publish_status: 1,
          is_city: 0
        }
      ],
      dialogForm: this.initDialogForm(),
      // 表示是新增或者更新
      isEdit: true,
      // 父级目录变动控制某些表单项无效
      disableParentName: false,
      disableCountryCodeThd: false,
      disableCountryCode: false,
      disableCurrency: false,
      disableIsCity: false,
      disablePublishStatus: false,
      disableAddress: false
    }
  },
  watch: {
    'dialogForm.path': function (val) {
      if (!val) {
        return
      }
      const self = this
      const mapper = {
        // 选择主目录或者 0 级别（亚洲）失效情况
        0: function () {
          self.disableCountryCodeThd = true
          self.disableCountryCode = true
          self.disableCurrency = true
          self.disableIsCity = true
          self.dialogForm.is_city = 0
          self.disablePublishStatus = true
          self.dialogForm.publish_status = 1
          self.disableAddress = true
          // NOTE: 第一次上线这里暂时注释，等待上线之后调整原有的数据之后，再取消注释
          // if (self.isEdit) {
          //   self.disableParentName = true;
          // }
        },
        // 选择一级目录（东亚）失效情况
        1: function () {
          self.disableCountryCodeThd = false
          self.disableCountryCode = false
          self.disableCurrency = false
          self.disableIsCity = true
          self.dialogForm.is_city = 0
          self.disablePublishStatus = false
          self.disableAddress = false
        },
        // 选择大于一级（中国）目录失效情况
        other: function () {
          self.disableCountryCodeThd = true
          self.disableCountryCode = true
          self.disableCurrency = true
          self.disableIsCity = false
          self.disablePublishStatus = false
          self.disableAddress = false
        }
      }
      let path = val.split('/')
      // 选择主目录或者 0 级别目录
      // path 长度为 2 说明是洲级别，
      // path 长度为 1 说明是主目录
      if (path.length < 3) {
        mapper[0]()
        // 选择 1 级别目录
      } else if (path.length === 3) {
        mapper[1]()
      } else if (path.length > 3) {
        mapper.other()
      }
    },
    showAreaForm: function (val) {
      if (!val) {
        this.editLang = KEY.defaultLang
        this.title = this.$t('taxonomy_dialog_title')
        this.isEdit = true
        this.editDataSrc = {}
        this.disableParentName = false
        // this.$refs["dialogForm"].resetFields()
        // this.dialogForm.parent_id = []
        // this.dialogForm.place_id = ""
        // this.dialogForm.time_zone = ""
        this.initDisableStatus()
        this.dialogForm = this.initDialogForm()
        this.$refs['dialogForm'].resetFields()
      }
    }
  },

  created() {
    this.initCurrencyOpts()
  },

  methods: {
    initDialogForm() {
      return {
        // 地理行政区域名称
        name: '',
        // 父级目录名称
        parentName: '',
        // 层级目录
        path: '',
        // 三位国家编码
        country_code_thd: '',
        // 两位国家编码
        country_code: '',
        // 货币
        currency: '',
        // 是否为城市
        is_city: null,
        // 状态 0:未发布, 1:发布
        publish_status: null,
        // 别名
        alias_name: [],
        // goole geocoding api获取到的place_id
        place_id: '',
        time_zone: '',
        location: ''
      }
    },
    async initCurrencyOpts() {
      const resp = await ADMIN_API.getAllCurrency()
      this.currencyOpts = resp.result
    },
    async initNodeOpts(nodes, nodeIdArray) {
      let nodeOptions = findNodeOption(nodes, nodeIdArray)
      let id = parseInt(_.last(nodeIdArray), 10)
      // 如果是叶子节点，就不需要再请求了
      if (nodeOptions.is_leaf === KEY.isLeaf) {
        delete nodeOptions.children
        return
      }
      const resp = await ADMIN_API.getChildNodes({ strative_area_id: id })
      if (resp.result && nodeOptions) {
        nodeOptions.children = this.formatNodeOpts(resp.result)
      }
    },

    areaDialogHide() {
      this.showAreaForm = false
      this.$refs['dialogForm'].resetFields()
    },

    async handleAdd() {
      const content = this.$t('taxonomy_alert_content')
      const title = this.$t('taxonomy_alert_title')
      await this.$confirm({
        content,
        title
      })
      this.showAreaForm = true
      this.isEdit = false
    },
    formatSaveData() {
      const { dialogForm } = this
      const { alias_name, name } = dialogForm
      let res = _.cloneDeep(dialogForm)
      res.i18n = {
        alias_name: alias_name.join(','),
        language: this.editLang,
        name
      }
      return res
    },
    formatNodeOpts(list) {
      let res = []
      res = list.map((item) => {
        let { i18n, is_city, publish_status } = item
        item.label = i18n.name
        item.children = []
        item.isCityText = this.isCityMapper[is_city]
        item.statusText = this.statusMapper[publish_status]
        return item
      })
      return res
    },
    saveDisableStatus() {
      const {
        disableParentName,
        disableCountryCodeThd,
        disableCountryCode,
        disableCurrency,
        disableIsCity,
        disablePublishStatus,
        disableAddress
      } = this
      this.editDataStatus = {
        disableParentName,
        disableCountryCodeThd,
        disableCountryCode,
        disableCurrency,
        disableIsCity,
        disablePublishStatus,
        disableAddress
      }
    },
    initDisableStatus() {
      Object.keys(this.editDataStatus).map((key) => {
        this[key] = this.editDataStatus[key]
      })
    },
    async handleSaveForm() {
      this.$refs['dialogForm'].validate(async (valid) => {
        if (valid) {
          let data = this.formatSaveData()
          if (!this.isEdit) {
            data.id = 0
          }
          try {
            let current_id = (await ADMIN_API.insertOrUpdateNode({ data })).result.id
            this.showAreaForm = false
            // this.refresh();
            if (this.isEdit) {
              if (this._dialogForm.path === this.dialogForm.path) {
                this.handleInsertNode(null, [this.dialogForm.parent_id], current_id)
              } else {
                this.handleInsertNode(null, this.dialogForm.path.split('/'), current_id)
              }
            } else {
              this.handleInsertNode(null, this.dialogForm.path.split('/'), current_id)
            }
          } catch (error) {
            this.initEditData(this.editDataSrc)
            this.initDisableStatus()
          }
        }
      })
    },
    handleCheckCoordinate() {
      this.$refs.map.$emit('submit-address')
    },
    async handleInsertNode(row, parent_id = [], current_id = null, grandpa_id = 0) {
      // grandpa_id 用于删除后，其父节点无叶子节点的情况, 再向上追溯到爷节点
      let ids = parent_id.length ? parent_id : [row.id]
      let lastId = ids.slice(-1)[0]

      for (let id of ids) {
        id = +id
        if (id === 0) {
          await this.refresh()
          continue
        }
        const result = (await ADMIN_API.getChildNodes({ strative_area_id: id })).result

        if (Array.isArray(result) && result.length) {
          insertChildNode(id, this.dataSource, this.formatNodeOpts(result))
        } else {
          this.handleInsertNode(null, [grandpa_id], id)
          break
        }
      }

      this.setCurrent(current_id ? current_id : lastId)
      this.expandAll = false
    },
    handleItemChange(val) {
      this.initNodeOpts(this.nodeOpts, val)
    },
    getMapInfo(val) {
      const { place_id, time_zone, location, place_image, time_zone_id, time_zone_name } = val
      this.dialogForm.place_id = place_id
      this.dialogForm.time_zone = formatRawOffset(time_zone)
      this.dialogForm.place_image = place_image
      this.dialogForm.time_zone_id = time_zone_id
      this.dialogForm.time_zone_name = time_zone_name
      this.initLocation(location)
    },
    formatSearchData() {
      let data = _.cloneDeep(this.searchForm)
      const { publish_status, is_city } = data
      // 搜索结果要展示所有父节点
      data.parent_node_show_type = 0
      // 除了 0 和 1 的情况其他都不需要传
      if (!_.isNumber(publish_status)) {
        delete data.publish_status
      }
      // 除了 0 和 1 的情况其他都不需要传
      if (!_.isNumber(is_city)) {
        delete data.is_city
      }
      return data
    },
    querySearchAsync: _.debounce(async function (queryString, cb) {
      let result = this.rootOpts
      if (!_.isEmpty(queryString)) {
        let data = {
          name: queryString,
          parent_node_show_type: 1
        }
        let resp = await ADMIN_API.searchNode({ data })
        result = resp.result
      }
      // let src = resp.result.map((item) => {
      //   const {parent_id, i18n, country_code_thd} = item
      //   item.value = i18n.name
      //   return item
      // })
      if (!_.isEmpty(result)) {
        // 性能优化，数据默认显示 100 条
        cb(result.slice(0, 100))
      }
    }, 800),
    handleSelectSearchResult(item) {
      const { i18n, id, path, country_code_thd, country_code, currency, publish_status } = item
      this.dialogForm.parentName = i18n.name
      this.dialogForm.parent_id = id
      // 这里加上当前节点 id 是因为这里取得是父节点路径
      this.dialogForm.path = `${path}/${id}`
      // 如果本身已经有值，就不需要从父级继承值了
      const { dialogForm } = this
      if (!dialogForm.country_code_thd) {
        this.dialogForm.country_code_thd = country_code_thd
      }
      if (!dialogForm.country_code) {
        this.dialogForm.country_code = country_code
      }
      if (!dialogForm.currency) {
        this.dialogForm.currency = currency
      }
      if (dialogForm.publish_status != '') {
        this.dialogForm.publish_status = publish_status
      }
    },
    async handleSearch() {
      this.$refs['searchForm'].validate(async (valid) => {
        if (valid) {
          let data = this.formatSearchData()
          let resp = await ADMIN_API.searchNode({ data })
          let re = this.formatNodeOpts(resp.result)
          this.expandAll = true
          this.dataSource = formatToTree(re)
        }
      })
    },
    handleResetSearch() {
      this.$refs['searchForm'].resetFields()
      this.refresh()
    }
  }
}

export default {
  components: {
    TreeTable,
    AreaMap,
    InputTag
  },

  mixins: [dialogMixin],
  data() {
    return {
      // 控制表格是否展开
      expandAll: false,
      searchForm: {
        is_city: null, // 是否是城市，0:否；1:是,
        name: '', // 行政区域名称,
        parent_name: '', // 行政区域父级名称,
        publish_status: null // 地区发布状态, 0:未发布, 1:发布,
      },
      searchRules: {
        name: [
          {
            trigger: 'blur',
            validator: (rule, value, cb) => {
              this.validatorFn(rule, value, cb)
            }
          }
        ],
        parent_name: [
          {
            trigger: 'blur',
            validator: (rule, value, cb) => {
              this.validatorFn(rule, value, cb)
            }
          }
        ]
      },
      columns: [
        {
          text: this.$t('taxonomy_filter_admin_region'),
          dataIndex: 'label',
          minWidth: '300',
          className: 'first-column'
        },
        {
          text: 'ID',
          dataIndex: 'id',
          minWidth: '100'
        },
        {
          text: this.$t('taxonomy_list_country_code_3'),
          dataIndex: 'country_code_thd',
          minWidth: '100'
        },
        {
          text: this.$t('taxonomy_filter_city'),
          dataIndex: 'isCityText',
          minWidth: '70'
        },
        {
          text: this.$t('taxonomy_filter_status'),
          dataIndex: 'statusText',
          minWidth: '100'
        }
      ],
      dataSource: [],
      langTable: initLangTable(),
      // 编辑语言弹窗显示与否
      showEditLang: false,
      // 编辑数据回显用，主要用到这里面的 id
      editData: {},
      // 缓存之前拉取的回显信息
      editDataSrc: {},
      // 缓存之前编辑信息的失效情况
      editDataStatus: {},
      editLang: KEY.defaultLang,
      isCityMapper: {
        0: this.$t('taxonomy_filter_city_no'),
        1: this.$t('taxonomy_filter_city_yes')
      },
      statusMapper: {
        0: this.$t('taxonomy_filter_unavailable'),
        1: this.$t('taxonomy_filter_available')
      }
    }
  },

  computed: {
    fixLocation: function () {
      let result = [
        {
          lat: '--',
          lng: '--'
        }
      ]
      let { location } = this.dialogForm
      if (location) {
        let [lat, lng] = this.dialogForm.location.split(',')
        result[0] = {
          lat,
          lng
        }
      }
      return result
    }
  },

  async mounted() {
    this.initTableData()
  },

  methods: {
    setCurrent(id) {
      this.$nextTick(() => {
        // FIXME setCurrentRow can't use
        let table = this.$refs['tree-table'].$refs['table']
        let index = _.findIndex(table.data, {
          id
        })

        document.querySelector('.current-row') &&
          document.querySelector('.current-row').classList.remove('current-row')
        let table_row = document.querySelectorAll('.el-table__row')
        table_row[index].classList.add('current-row')
        document.querySelector('.current-row').scrollIntoView()
      })
    },
    async initTableData() {
      // 默认加载一级目录
      let id = parseInt(KEY.root.id, 10)
      const resp = await ADMIN_API.getChildNodes({ strative_area_id: id })
      this.dataSource = this.formatNodeOpts(resp.result)
      this.expandAll = false
    },
    refresh() {
      this.initTableData()
    },
    // 当行政区域名称和父级名称都是空就不通过
    validatorFn(prop, value, cb) {
      const { name, parent_name } = this.searchForm
      let bothEmpty = _.isEmpty(name) && _.isEmpty(parent_name)
      if (!bothEmpty) {
        cb()
      } else {
        cb(new Error(this.$t('global_pls_input')))
      }
    },
    async handleEdit(row) {
      this.showEditLang = true
      this.editData = row
    },
    async handleDelete(row) {
      await this.$confirm({
        content: this.$t('js_confirm_delete')
      })
      await ADMIN_API.deleteNode({
        strative_area_id: row.id
      })
      let father_id = row.parent_i18n.strative_area_id
      let ids_path = row.path.split('/')
      let grandpa_index = ids_path.indexOf('' + father_id) - 1
      let grandpa_id = -1 === grandpa_index ? 0 : +ids_path[grandpa_index]
      this.handleInsertNode(null, [row.parent_i18n.strative_area_id], null, grandpa_id)
    },
    initLocation(locationStr) {
      if (!_.isEmpty(locationStr)) {
        let [lat, lng] = locationStr.split(',')
        this.dialogForm.location = _.ceil(lat, 6) + ',' + _.ceil(lng, 6)
      }
    },
    initParentName(parentObj, parent_id) {
      const { name } = parentObj
      this.dialogForm.parent_id = parent_id
      this.dialogForm.parentName = name
      if (parent_id === KEY.root.id) {
        this.dialogForm.parentName = this.$t('taxonomy_root_menu')
      }
    },
    initEditData(resData) {
      const {
        id,
        path,
        parent_id,
        country_code_thd,
        country_code,
        currency,
        is_city,
        publish_status,
        i18n,
        parent_i18n,
        location,
        place_id,
        time_zone,
        place_image
      } = resData
      this.dialogForm.id = id
      this.dialogForm.name = i18n.name
      // 父级目录
      this.initParentName(parent_i18n, parent_id)
      // 这里 path 拿到的就是父级目录路径，不包含当前节点
      this.dialogForm.path = path
      this.dialogForm.country_code = country_code
      this.dialogForm.country_code_thd = country_code_thd
      this.dialogForm.currency = currency
      this.dialogForm.is_city = is_city
      this.dialogForm.publish_status = publish_status
      this.dialogForm.is_city = is_city
      this.dialogForm.place_id = place_id
      this.dialogForm.time_zone = time_zone
      this.dialogForm.place_image = place_image
      this.dialogForm.alias_name = i18n.alias_name ? i18n.alias_name.split(',') : []
      this.initLocation(location)
    },
    async handleEditDialog(row) {
      this.showAreaForm = true
      this.editLang = row.key
      this.title = this.$t('taxonomy_edit_dialog_title')
      this.showEditLang = false
      const { id } = this.editData
      const resp = await ADMIN_API.getNodesByIdList({
        strative_area_id_list: id.toString(),
        language: row.key,
        // 1: 直接使用原始语言获取数据
        not_use_default_en: 1
      })
      this.editDataSrc = resp.result[0]
      this.initEditData(resp.result[0])
      this.saveDisableStatus()
      this._dialogForm = _.cloneDeep(this.dialogForm)
    }
  }
}
</script>

<style lang="scss">
.autocomplete-popper {
  .name {
    margin-left: 10px;
    font-weight: bold;
  }
  .parent {
    margin-left: 10px;
    color: grey;
  }
}
</style>
