<!-- set this page as plugin for map -->
<!-- this page is moved from activity detial js with minor modification -->

<!-- map section -->
<template>
  <div class="area-map-wrap">
    <div v-show="position" id="mapWrapper" class="map-container"></div>
    <div v-show="!position" class="common-svg-wrap">
      <div class="common-svg-box">
        <svg xmlns="http://www.w3.org/2000/svg" width="90" height="64" viewBox="0 0 90 64" fill="none">
          <path
            d="M85.7912 42.3347C87.2464 42.3347 88.2145 43.8394 87.6112 45.1638L79.1221 63.8001L0.899903 63.8001L10.1443 43.5057C10.4692 42.7925 11.1807 42.3347 11.9644 42.3347L85.7912 42.3347Z"
            fill="url(#paint0_linear)"
          />
          <path
            d="M85.7912 42.3347C87.2464 42.3347 88.2145 43.8394 87.6112 45.1638L79.1221 63.8001L0.899903 63.8001L10.1443 43.5057C10.4692 42.7925 11.1807 42.3347 11.9644 42.3347L85.7912 42.3347Z"
            fill="url(#paint1_linear)"
          />
          <path
            d="M0.899902 21.4656L10.1443 1.17109C10.4692 0.457889 11.1807 0.000152139 11.9644 0.000152104L88.8999 0.000148741L79.1221 21.4656L0.899902 21.4656Z"
            fill="url(#paint2_linear)"
          />
          <path
            d="M0.899902 21.4656L10.1443 1.17109C10.4692 0.457889 11.1807 0.000152139 11.9644 0.000152104L88.8999 0.000148741L79.1221 21.4656L0.899902 21.4656Z"
            fill="url(#paint3_linear)"
          />
          <path
            d="M88.8999 42.3347L79.1221 21.4656L0.899901 21.4656L10.6777 42.3347L88.8999 42.3347Z"
            fill="url(#paint4_linear)"
          />
          <path
            d="M88.8999 42.3347L79.1221 21.4656L0.899901 21.4656L10.6777 42.3347L88.8999 42.3347Z"
            fill="url(#paint5_linear)"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M89.0999 42C89.0999 33.0302 83.2558 24 71.4998 24C59.7439 24 53.8999 33.0302 53.8999 42C53.8999 49.2717 58.5072 54.9633 68.3924 62.621L69.4843 63.4346C70.0873 63.8086 70.7861 63.9969 71.4849 64C72.3013 64.0035 73.1178 63.7543 73.7819 63.2528C84.2155 55.3749 89.0999 49.4745 89.0999 42ZM77.4999 40C77.4999 36.6863 74.8136 34 71.4999 34C68.1862 34 65.4999 36.6863 65.4999 40C65.4999 43.3137 68.1862 46 71.4999 46C74.8136 46 77.4999 43.3137 77.4999 40Z"
            fill="url(#paint6_linear)"
          />
          <defs>
            <linearGradient
              id="paint0_linear"
              x1="95.8213"
              y1="52.5302"
              x2="0.899902"
              y2="52.5302"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECECEC" />
              <stop offset="1" stop-color="#E4E4E4" />
            </linearGradient>
            <linearGradient
              id="paint1_linear"
              x1="95.8213"
              y1="52.5302"
              x2="0.899902"
              y2="52.5302"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#ECECEC" />
              <stop offset="1" stop-color="#E4E4E4" />
            </linearGradient>
            <linearGradient
              id="paint2_linear"
              x1="-6.02145"
              y1="11.2701"
              x2="88.8999"
              y2="11.2701"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#E4E4E4" />
              <stop offset="1" stop-color="#ECECEC" />
            </linearGradient>
            <linearGradient
              id="paint3_linear"
              x1="-6.02145"
              y1="11.2701"
              x2="88.8999"
              y2="11.2701"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#E4E4E4" />
              <stop offset="1" stop-color="#ECECEC" />
            </linearGradient>
            <linearGradient
              id="paint4_linear"
              x1="75.5328"
              y1="26.2512"
              x2="36.5993"
              y2="59.8718"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#E4E4E4" />
              <stop offset="1" stop-color="#D1D1D1" />
            </linearGradient>
            <linearGradient
              id="paint5_linear"
              x1="75.5328"
              y1="26.2512"
              x2="36.5993"
              y2="59.8718"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#E4E4E4" />
              <stop offset="1" stop-color="#D1D1D1" />
            </linearGradient>
            <linearGradient
              id="paint6_linear"
              x1="71.4999"
              y1="24"
              x2="71.4999"
              y2="64"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#DDDDDD" />
              <stop offset="1" stop-color="#A6A6A6" />
            </linearGradient>
          </defs>
        </svg>
        <div class="common-svg-text">{{ $t('21817') }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import geolocator from 'geolocator'
import { initGoogleMaps, googleApiKey } from './gmaps.js'

export default {
  name: 'AreaMap',
  install(Vue) {
    // https://vuejs.org/v2/guide/plugins.html
    Vue.component(this.name, this)
  },
  props: {
    address: {
      type: String,
      default: ''
    },
    place: {
      type: String,
      default: ''
    },
    position: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      subarea_id_list: [],
      city_id: 0,
      place_id: '',
      // 时区
      offset: '',
      locationData: [
        {
          lng: '--',
          lat: '--'
        }
      ],
      // 地图点
      marker: null
    }
  },
  async mounted() {
    geolocator.config({
      google: {
        key: googleApiKey()
      }
    })
    try {
      const google = await initGoogleMaps()
      const geocoder = new google.maps.Geocoder()
      this.map = new google.maps.Map(document.getElementById('mapWrapper'))
      this.geocodeAddress(geocoder, this.map, {
        placeId: this.place,
        address: this.address || this.position
      })
      this.$on('submit-address', (val = null) => {
        const config = {
          address: val || this.address
        }
        if (config.address) {
          this.geocodeAddress(geocoder, this.map, config)
        }
      })
    } catch (error) {
      console.error(error)
    }
  },
  methods: {
    geocodeAddress(geocoder, resultsMap, config) {
      if (config && !config.address) {
        return
      }

      geocoder.geocode(config, async (results, status) => {
        if (status === google.maps.GeocoderStatus.OK) {
          let res = results[0]
          let { location, viewport } = res.geometry
          resultsMap.setCenter(location)
          resultsMap.fitBounds(viewport)
          this.markOnePoint(location, resultsMap)
          // will be scroll to map
          // this.renderInfoWindow(
          //   __('act_current_location') + res.formatted_address,
          //   this.marker
          // )
          let timezone = (await this.renderTimeZone(location.lat(), location.lng())) || {}
          // get google map image
          let imgSrc = geolocator.getStaticMap({
            center: {
              latitude: location.lat(),
              longitude: location.lng()
            }
          })

          this.$emit('map', {
            success: true,
            place_id: res.place_id,
            formatted_address: res.formatted_address,
            time_zone: timezone.rawOffset,
            time_zone_id: timezone.id,
            time_zone_name: timezone.name,
            location: `${location.lat()},${location.lng()}`,
            place_image: 'https:' + imgSrc
          })
        } else if (status === google.maps.GeocoderStatus.ZERO_RESULTS) {
          this.$message.info('Zero Results')
          this.$emit('map', {
            success: false
          })
        } else {
          this.$message.info(status)
          this.$emit('map', {
            success: false
          })
        }
      })
    },
    markOnePoint(position, resultsMap) {
      // 移除上一次的点
      this.marker && this.marker.setMap(null)
      this.marker = new google.maps.Marker({
        map: resultsMap,
        position
      })
    },
    renderInfoWindow(content, marker) {
      let infoWindow = new google.maps.InfoWindow({
        content
      })
      infoWindow.open(this.map, marker)
    },
    renderTimeZone(lat, lng) {
      return new Promise((resolve, reject) => {
        try {
          geolocator.getTimeZone(
            {
              latitude: lat,
              longitude: lng
            },
            (err, timeZone) => {
              resolve(timeZone)
              // this.offset = timeZone.rawOffset;
            }
          )
        } catch (error) {
          reject(error)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.area-map-wrap {
  position: relative;
  margin-top: 10px;
  width: 100%;
  height: 250px;
  background: #fafafa;
  border-radius: 4px;
}
.map-container {
  height: 100%;
}
.common-svg-wrap {
  position: absolute;
  z-index: 4;
  width: 100%;
  height: 100%;
}
</style>
