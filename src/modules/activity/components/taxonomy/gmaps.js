const API_KEY = 'AIzaSyByoaOJMATcSHo6iZ-cofp9vlHU8t64ukw'
const CALLBACK_NAME = 'gmaps_callback'

const googleApiKey = function () {
  return API_KEY
}

const librariesDict = {
  places: 'places',
  drawing: 'drawing'
}

let librariesLoadRecords = {}
let googleInstance = null

const bulkResolveItem = (item) => {
  if (item) {
    // 避免因 @klook/admin-ui KlkAntAreaMap 加载而导致的 window.google 污染
    googleInstance = _.cloneDeep(window.google)

    item.resolve(googleInstance)
    // resolve 相同 library 的加载
    item.waitingQueue.forEach((resolve) => resolve(googleInstance))

    if (Array.isArray(item.relatedItems)) {
      // 批量 resolve 优先级低的加载 如 place
      item.relatedItems.forEach((record) => bulkResolveItem(record))
    }
  }
}

const initGoogleMaps = async function ({ libraries = librariesDict.drawing } = {}) {
  // 已加载对应 google map 则直接返回
  if (googleInstance && (libraries !== librariesDict.drawing || googleInstance.maps?.drawing)) {
    librariesLoadRecords = {}

    return Promise.resolve(googleInstance)
  }

  // 存在正在加载的 library, 则推入等待队列
  if (Object.prototype.hasOwnProperty.call(librariesLoadRecords, libraries)) {
    return new Promise((resolve) => {
      librariesLoadRecords[libraries].waitingQueue.push(resolve)
    })
  } else {
    librariesLoadRecords[libraries] = {}
  }

  // google map 加载回调
  const callbackFieldKey = `${libraries}_${CALLBACK_NAME}`
  librariesLoadRecords[libraries].callbackFieldKey = callbackFieldKey
  window[callbackFieldKey] = () => {
    bulkResolveItem(librariesLoadRecords[libraries])
    window[callbackFieldKey] = null
  }

  // 当前加载 drawing library 且还存在加载低优先级如 places 的情况, 则取消加载 places
  if (librariesDict.drawing === libraries && Object.keys(librariesLoadRecords).length > 1) {
    Object.values(librariesLoadRecords).forEach((record) => {
      // 当前的不需要处理
      if (record.callbackFieldKey === callbackFieldKey) {
        return
      }
      // 取消之前 places 加载
      clearTimeout(record.timer)
      window[record.callbackFieldKey] = null
      // 推入关联项, 待当前 drawing 加载完后一起 resolve
      if (!Object.prototype.hasOwnProperty.call(librariesLoadRecords[libraries], 'relatedItems')) {
        librariesLoadRecords[libraries].relatedItems = []
      }

      librariesLoadRecords[libraries].relatedItems.push(record)
    })
  }

  // 宏任务加载，方便处理 library 优先级问题
  librariesLoadRecords[libraries].timer = setTimeout(() => {
    const script = document.createElement('script')
    script.async = true
    script.defer = true
    script.src = `https://maps.googleapis.com/maps/api/js?key=${API_KEY}&language=${KLK_LANG.toLowerCase()}&libraries=${libraries}&callback=${callbackFieldKey}`
    script.onerror = () => {
      librariesLoadRecords[libraries].reject()
    }
    document.querySelector('head').appendChild(script)
  })

  return new Promise((resolve, reject) => {
    Object.assign(librariesLoadRecords[libraries], {
      waitingQueue: [],
      resolve,
      reject
    })
  })
}

export { initGoogleMaps, googleApiKey, librariesDict }
