<template>
  <div class="poi_manage-container">
    <br />
    <br />

    <a-form-model ref="form" :model="form" style="position: relative" label-width="120px">
      <a-form-model-item :label="$t('taxonomy_poi_name') + ': '">
        <a-input
          v-model="form.name"
          :placeholder="$t('taxonomy_poi_manage_placeholder')"
          style="width: 300px"
          @keydown.enter.native="searchHandle"
        />
      </a-form-model-item>
      <a-form-model-item :label="$t('taxonomy_type') + ': '">
        <a-select v-model="form.type" @change="searchHandle">
          <a-select-option
            v-for="(item, index) of this.typeOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
            >{{ item.label }}</a-select-option
          >
        </a-select>
      </a-form-model-item>
      <a-form-model-item :label="$t('taxonomy_belong_city') + ': '">
        <a-select
          v-model="form.belong"
          show-search
          remote
          allow-clear
          placeholder="Type to Search"
          :remote-method="remoteCity"
          @change="searchHandle"
        >
          <a-select-option
            v-for="(item, index) of cityOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
            >{{ item.label }}</a-select-option
          >
        </a-select>
      </a-form-model-item>
      <a-form-model-item :label="$t('status') + ': '">
        <a-select v-model="form.publish_status" @change="searchHandle">
          <a-select-option
            v-for="(item, index) of statusOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
            >{{ item.label }}</a-select-option
          >
        </a-select>
      </a-form-model-item>

      <a-form-model-item style="width: 100%">
        <a-button type="primary" @click="searchHandle">{{ $t('taxonomy_query') }}</a-button>
        <a-button type="primary" @click="clearHandle">{{ $t('taxonomy_reset') }}</a-button>
      </a-form-model-item>

      <a-button
        type="primary"
        class="add-new-btn"
        style="position: absolute; right: 0; bottom: 0"
        @click="addNewHandle"
      >
        +&nbsp;{{ $t('taxonomy_create_key_location') }}
      </a-button>
    </a-form-model>

    <!-- <full-table
      ref="table"
      :data="tableData"
      :columns="columns"
      :pagination="pagination"
      @size-change="sizeChange"
      @current-change="currentChange"
    /> -->

    <!-- <select_lang_dialog
      ref="lang"
      :visible.sync="langVisible"
      @confirm="langConfirm"
    /> -->

    <tpl_poi_edit_dialog
      v-if="edit.visible"
      :id="edit.id"
      :key="edit.visible"
      ref="edit"
      :visible.sync="edit.visible"
      :language="edit.language"
      @submit="editSubmit"
    />
  </div>
</template>

<script>
import moment from 'moment'
import { typeDict, special_type, statusOptions } from './taxonomy_utils.js'
// import FullTable from '../../../components/table/main'
// import select_lang_dialog from '../../../components/tpl_select_lang_dialog'
import tpl_poi_edit_dialog from './tpl_poi_edit_dialog'

export default {
  components: {
    tpl_poi_edit_dialog
    // select_lang_dialog,
    // FullTable
  },
  data() {
    return {
      typeDict: typeDict,
      cityOptions: [],
      typeOptions: [],
      form: {
        name: '',
        type: null,
        belong: null,
        publish_status: null
      },

      tableData: [],
      columns: [
        {
          label: 'ID',
          prop: 'id'
        },
        {
          label: __('taxonomy_create_location_name'),
          prop: 'i18n.name'
        },
        {
          label: __('taxonomy_type'),
          render: (h, params) => {
            return <span>{this.typeDict[params.row.type]}</span>
          }
        },
        {
          label: __('taxonomy_belong_city'),
          render: (h, params) => {
            return <span>{params.row.cities_i18n.map((item) => item.name).join()}</span>
          }
        },
        {
          label: __('create_time'),
          prop: 'create_date',
          render: (h, params) => {
            return <span>{moment(params.row.create_time_utc || '').format('YYYY-MM-DD')}</span>
          }
        },
        {
          label: __('handle'),
          minWidth: '120px',
          render: (h, params) => {
            if (special_type.includes(params.row.type)) {
              // dining room or hotel
              return ''
            }
            return (
              <div>
                <a-button type="primary" size="small" on-click={(e) => this.editHandle(params.row)}>
                  {this.$t('global_button_edit')}
                </a-button>
                <a-button type="danger" size="small" on-click={(e) => this.deleteHandle(params.row)}>
                  {this.$t('global_button_del')}
                </a-button>
              </div>
            )
          }
        }
      ],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },

      langVisible: false,
      currentRow: null,

      edit: {
        visible: false,
        id: null,
        language: null
      }
    }
  },
  computed: {
    statusOptions() {
      return [
        {
          label: __('all'),
          value: null
        },
        ...statusOptions
      ]
    }
  },
  created() {
    this.initPoiTypeList()
  },
  methods: {
    remoteCity: _.debounce(function (query) {
      this.getCustomFilterNode(query)
    }, 800),
    async getCustomFilterNode(query) {
      let data = {
        show: {
          limit: 50,
          page: 1,
          parent_node_show_type: 1
        },
        term: {
          name: query,
          type: 3 // city
        }
      }

      let result = (await ADMIN_API.getCustomFilterNode(data)).result
      this.cityOptions = result.nodes.map((item) => ({
        label: item.i18n.name,
        value: item.id
      }))
    },
    sizeChange(size) {
      this.pagination.pageSize = size
      this.searchHandle()
    },
    currentChange(val) {
      this.pagination.currentPage = val
      this.searchHandle()
    },
    async clearHandle() {
      this.$set(this, 'form', this.$options.data().form)
      this.searchHandle()
    },
    async searchHandle() {
      let { name, publish_status, belong, type } = this.form
      let { pageSize, currentPage } = this.pagination
      let data = {
        name,
        type,
        publish_status,
        city_id_list: !belong ? null : [belong],
        limit: pageSize,
        page: currentPage
      }
      let resp = (await ADMIN_API.getPoiFilterNode(data)).result

      this.pagination.total = resp.total
      this.tableData = resp.nodes
    },

    async deleteHandle(row) {
      this.$confirm({
        content: this.$t('js_confirm_delete'),
        okText: this.$t('global_confirm'),
        cancelText: this.$t('global_cancel'),
        type: 'warning'
      })
        .then(async () => {
          await ADMIN_API.delPoiNode({
            poi_id: row.id
          })

          this.$message.success(this.$t('global_success'))
          await this.searchHandle()
        })
        .catch(() => {})
    },

    async addNewHandle() {
      await this.$confirm({
        content: this.$t('taxonomy_confirm_key_location'),
        title: 'Tips',
        okText: this.$t('global_submit'),
        cancelText: this.$t('global_cancel'),
        type: 'warning'
      })

      this.$set(this, 'edit', {
        id: null,
        visible: true,
        language: 'en_US'
      })
    },
    async editSubmit(data) {
      let res = await ADMIN_API.updatePoiNode(data)
      if (res.success) {
        this.$set(this, 'edit', this.$options.data().edit)
        this.$message.success(this.$t('global_success'))
        this.searchHandle()
      } else {
        if (res.error.code === '51510' && res.error.message) {
          this.$confirm({
            content: res.error.message,
            title: __('global_confirm'),
            type: 'info'
          })
        } else {
          this.$confirm({
            content: 'Backend error'
          })
        }
      }
    },

    editHandle(row) {
      this.langVisible = true
      this.currentRow = row
    },
    langConfirm(data) {
      this.langVisible = false
      this.$set(this, 'edit', {
        id: this.currentRow.id,
        visible: true,
        language: data.B_LANG
      })
    },
    async initPoiTypeList() {
      this.typeOptions.push({
        label: __('all'),
        value: null
      })

      let res = await ajax.get(ADMIN_API.act.get_all_poitype)
      if (res.all_types) {
        for (let tp of res.all_types) {
          if (tp.show_in_admin) {
            this.typeOptions.push({
              label:
                window.KLK_LANG === 'zh-CN'
                  ? tp.name_cn
                  : window.KLK_LANG === 'zh-TW'
                  ? tp.name_tw
                  : tp.name_en,
              value: tp.poi_type
            })
          }
        }
      }
    }
  },
  beforeMount() {
    this.searchHandle()
  }
}
</script>
