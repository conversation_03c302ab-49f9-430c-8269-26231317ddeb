let { isProd } = require('@/env')
const log = isProd ? function() {} : console.log.bind(console)

const KEY = {
  // 新增区域管理默认美国英文
  defaultLang: 'en_US',
  // 根节点
  root: {
    id: 0
  },
  isLeaf: 1
}

// -28800 => 'UTC-8:00'
const formatRawOffset = (num) => {
  // 舍去小数
  let h = parseInt(num / 3600, 10)
  let m = Math.abs((num / 60) % 60)
  if (h > 0) {
    h = '+' + h
  }
  // 补零
  if (m < 10) {
    m = `0${m}`
  }
  return `UTC${h}:${m}`
}

const findNodeOption = (regionOptions, regionArr) => {
  if (_.isEmpty(regionArr) || _.isEmpty(regionOptions)) {
    return null
  }

  let regionId = _.first(regionArr)
  let regionOption = _.find(regionOptions, (regionOption) => {
    return regionOption.id === regionId
  })

  if (!regionOption) {
    return null
  }

  // lodash的tail方法，获取除了array数组第一个元素以外的全部元素。
  let tailRegionArr = _.tail(regionArr)

  if (_.isEmpty(tailRegionArr)) {
    return regionOption
  }
  return findNodeOption(regionOption.children, tailRegionArr)
}

// 在 nodeArr 找到对应的 id，然后插入 children
const insertChildNode = (childNodeId, nodeArr, children) => {
  nodeArr.map((item) => {
    const { id } = item
    if (id === childNodeId) {
      item.children = children
      item._expanded = true
    } else {
      insertChildNode(childNodeId, item.children, children)
    }
  })
}

const formatToTree = (arr) => {
  var tree = [],
    mappedArr = {},
    arrElem,
    mappedElem

  // First map the nodes of the array to an object -> create a hash table.
  for (var i = 0, len = arr.length; i < len; i++) {
    arrElem = arr[i]
    mappedArr[arrElem.id] = arrElem
    mappedArr[arrElem.id]['children'] = []
  }

  for (var id in mappedArr) {
    // if (mappedArr.hasOwnProperty(id)) {
    if (Object.prototype.hasOwnProperty.call(mappedArr, id)) {
      mappedElem = mappedArr[id]
      // If the element is not at the root level, add it to its parent array of children.
      if (mappedElem.parent_id) {
        mappedArr[mappedElem['parent_id']]['children'].push(mappedElem)
      }
      // If the element is at the root level, add it to first level elements array.
      else {
        tree.push(mappedElem)
      }
    }
  }
  return tree
}

const typeDict = {
  // 0: '未设置',
  1: __('taxonomy_scenic_spot'),
  2: __('taxonomy_airport'),
  3: __('taxonomy_railway_station'),
  4: __('taxonomy_bus_station'),
  5: __('taxonomy_ferry'),
  6: __('taxonomy_mall'),
  7: __('taxonomy_theme_park'),
  8: __('taxonomy_nature'),
  9: __('taxonomy_dining_room'),
  10: __('taxonomy_hotel')
}

const typeOptions = Object.keys(typeDict).map((key) => ({
  label: typeDict[key],
  value: +key
}))

const special_type = [9, 10]

const statusDict = {
  1: __('taxonomy_filter_available'),
  0: __('taxonomy_filter_unavailable')
}

const statusOptions = Object.keys(statusDict).map((key) => ({
  label: statusDict[key],
  value: +key
}))

export {
  log,
  KEY,
  findNodeOption,
  insertChildNode,
  formatRawOffset,
  formatToTree,
  typeDict,
  typeOptions,
  special_type,
  statusDict,
  statusOptions
}
