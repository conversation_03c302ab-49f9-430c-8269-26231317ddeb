<template>
  <a-modal
    :visible.sync="_visible"
    :width="920"
    :title="$t('taxonomy_create_key_location')"
    @cancel="() => (_visible = false)"
  >
    <a-form-model ref="form" :model="form" label-width="120px">
      <a-row type="flex" justify="space-around" align="middle">
        <a-col :span="10">
          <a-form-model-item v-if="create_time_utc" :label="$t('create_time')">
            {{ create_time_utc }}
          </a-form-model-item>

          <a-form-model-item :label="$t('taxonomy_poi_name')" prop="name" :rules="inputRule">
            <a-input v-model.trim="form.name" />
          </a-form-model-item>
          <a-form-model-item prop="country" :label="$t('taxonomy_belong_country')" :rules="selectRule">
            <a-select
              v-model="form.country"
              show-search
              allow-clear
              remote
              placeholder="Type to Search"
              :remote-method="remoteCountry"
            >
              <a-select-option
                v-for="item of countryOptions"
                :key="item.strative_area_id"
                :label="item.name"
                :value="item.strative_area_id"
                >{{ item.name }}</a-select-option
              >
            </a-select>
          </a-form-model-item>
          <a-form-model-item prop="city" :label="$t('taxonomy_belong_city')" :rules="multipleSelectRule">
            <a-select
              v-model="form.city"
              show-search
              remote
              multiple
              :multiple-limit="multipleLimit"
              :disabled="!form.country"
              placeholder="Type to Search"
              :remote-method="remoteCity"
            >
              <a-select-option
                v-for="item of cityOptions"
                :key="item.strative_area_id"
                :label="item.name"
                :value="item.strative_area_id"
                >{{ item.name }}</a-select-option
              >
            </a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('taxonomy_type')" prop="type" :rules="selectRule">
            <a-select v-model="form.type">
              <a-select-option
                v-for="item of typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                >{{ item.label }}</a-select-option
              >
            </a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('global_status')" type="status" :rules="selectRule">
            <a-select v-model="form.status">
              <a-select-option
                v-for="item of statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                >{{ item.label }}</a-select-option
              >
            </a-select>
          </a-form-model-item>

          <a-form-model-item :label="$t('taxonomy_synonyms')">
            <input-tag v-model="form.aliasName" :placeholder="$t('taxonomy_dialog_nicknames_placeholder')" />
          </a-form-model-item>

          <a-form-model-item
            v-if="showHigherLevel"
            prop="high_priority_location"
            :label="$t('taxonomy_higher_level')"
          >
            <a-switch
              v-model="form.high_priority_location"
              on-text=""
              off-text=""
              :on-value="1"
              :off-value="0"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item v-if="create_user_name" :label="$t('cms_created_by')">
            {{ create_user_name }}
          </a-form-model-item>

          <a-form-model-item prop="address" :rules="inputRule" :label="$t('taxonomy_dialog_coordinates')">
            <a-row>
              <a-col :span="16">
                <a-input
                  v-model="form.address"
                  :placeholder="$t('act_gps_location')"
                  @keyup.native.enter="handleCheckCoordinate"
                />
              </a-col>
              <a-col :span="6" :offset="2">
                <a-button type="primary" @click="handleCheckCoordinate">
                  {{ $t('taxonomy_query') }}
                </a-button>
              </a-col>
            </a-row>
            <!---->
            <area-map
              ref="map"
              :key="form.place_id"
              :address="form.address"
              :place="form.place_id"
              @map="getMapInfo"
            />
            <div>{{ $t('taxonomy_full_address') }}: {{ form.formatted_address }}</div>
            <div>
              <span>Google Place ID: </span>
              <span>{{ form.place_id }}</span>
            </div>
            <div>
              <span>{{ $t('taxonomy_dialog_time_zone') }}: </span>
              <span>{{ form.time_zone }}</span>
            </div>
            <a-table :data="locationTable" style="width: 100%">
              <a-table-column prop="lng" :label="$t('taxonomy_dialog_longitude')" />
              <a-table-column prop="lat" :label="$t('taxonomy_dialog_latitude')" />
            </a-table>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>

    <div slot="footer">
      <a-button @click="_visible = false">{{ $t('global_cancel') }}</a-button>
      <a-button type="primary" @click="submitHandle">{{ $t('global_submit') }}</a-button>
    </div>
  </a-modal>
</template>

<script>
import InputTag from '@activity/components/input_tag'
import AreaMap from './area_map.vue'
import { special_type, statusOptions, formatRawOffset } from './taxonomy_utils.js'

export default {
  components: {
    InputTag,
    AreaMap
  },
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    id: {
      type: [Number, String],
      default: null
    },
    language: {
      type: String,
      required: true
    },
    showHigherLevel: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      multipleLimit: 5,
      countryOptions: [],
      cityOptions: [],

      create_time_utc: null,
      create_user_name: null,

      form: {
        name: '',
        country: '',
        city: [],
        type: '',
        status: 1,
        aliasName: [],
        high_priority_location: 0,
        //
        address: '',
        formatted_address: '',
        place_id: '',
        time_zone: '',
        location: ''
      },

      inputRule: {
        required: true,
        message: 'Please input',
        trigger: 'blur,change'
      },
      selectRule: {
        required: true,
        message: 'Please select',
        trigger: 'change',
        type: 'number'
      },
      multipleSelectRule: {
        required: true,
        validator: (rule, value, callback) => {
          let len = value.length
          if (len >= 1 && len <= 5) {
            callback()
          } else {
            callback(new Error('Max number of cities is 5.'))
          }
        },
        trigger: 'change'
      },

      locationTable: [],
      typeOptions: []
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    statusOptions() {
      return statusOptions
    },
    fixLocation: function() {
      let result = [
        {
          lat: '--',
          lng: '--'
        }
      ]
      let { location } = this.form
      if (location) {
        let [lat, lng] = location.split(',')
        result[0] = {
          lat,
          lng
        }
      }
      return result
    }
  },
  watch: {
    id: {
      async handler(v) {
        if (v) {
          let result = (
            await ADMIN_API.getPoiNodesByIdList({
              poi_id_list: '' + v,
              language: this.language,
              not_use_default_en: 1
            })
          ).result[0]

          this.$set(this, 'form', {
            name: result.i18n.name,
            country: result.country_i18n.strative_area_id,
            city: result.cities_i18n.map((item) => item.strative_area_id),
            type: result.type,
            status: result.publish_status,
            aliasName: result.i18n.alias_name.split(',').filter((item) => item !== ''),
            //
            address: result.i18n.address,
            place_id: result.place_id,
            time_zone: formatRawOffset(result.time_zone),
            location: this.initLocation(result.location),
            high_priority_location: result.high_priority_location
          })

          this.$set(this, 'countryOptions', [result.country_i18n])
          this.$set(this, 'cityOptions', result.cities_i18n)

          this.create_time_utc = result.create_time_utc
          this.create_user_name = result.create_user_name
        }
      },
      immediate: true
    },
    async ['form.country'](newVal, oldVal) {
      if (newVal) {
        if (oldVal) {
          this.form.city = ''
          this.cityOptions = this.cityOptions.filter()
        }
        await this.getCustomFilterNode('', false, true)
      }
    }
  },
  created() {
    this.initPoiTypeList()
  },
  methods: {
    submitHandle() {
      this.$refs.form.validate((v) => {
        if (v) {
          let {
            city,
            address,
            aliasName,
            name,
            location,
            place_id,
            status,
            type,
            high_priority_location
          } = this.form
          let data = {
            city_id_list: city,
            i18n: {
              address,
              alias_name: aliasName.join(),
              language: this.language,
              name
            },
            ...(this.id
              ? {
                  id: this.id
                }
              : {}),
            location,
            place_id,
            publish_status: status,
            type,
            high_priority_location,
            send_email: false
          }
          this.$emit('submit', data)
        } else {
          this.$message.warn('Please fill in all fields')
        }
      })
    },
    remoteCountry: _.debounce(function(query) {
      this.getCustomFilterNode(query, true)
    }, 800),
    remoteCity: _.debounce(function(query) {
      this.getCustomFilterNode(query, false)
    }, 800),
    async getCustomFilterNode(query, is_country, force) {
      if (force || !!query.length) {
        let data = {
          show: {
            limit: 50,
            page: 1,
            parent_node_show_type: 1
          },
          term: {
            name: query,
            publish_status: 1,
            ...(is_country
              ? {
                  type: 2
                }
              : {
                  type: 3, // city
                  parent_id_in_path: this.form.country
                })
          }
        }

        let options = (await ADMIN_API.getCustomFilterNode(data)).result.nodes.map((item) => item.i18n)

        if (is_country) {
          this.countryOptions = options
        } else {
          let selected = this.cityOptions.filter((item) => this.form.city.includes(item.strative_area_id))
          this.cityOptions = _.uniqBy([...options, ...selected], 'strative_area_id')
        }
      }
    },
    initLocation(location) {
      if (!_.isEmpty(location)) {
        let [lat, lng] = location.split(',')
        lat = _.ceil(lat, 6)
        lng = _.ceil(lng, 6)

        this.locationTable = [
          {
            lat,
            lng
          }
        ]

        return lat + ',' + lng
      }
    },
    getMapInfo(data) {
      const { place_id, time_zone, location, formatted_address } = data

      this.form.place_id = place_id
      this.form.time_zone = formatRawOffset(time_zone)
      this.form.location = this.initLocation(location)
      this.form.formatted_address = formatted_address
    },
    handleCheckCoordinate() {
      this.$refs.map.$emit('submit-address')
    },
    async initPoiTypeList() {
      this.typeOptions.push({
        label: __('all'),
        value: null
      })

      let res = await ajax.get(ADMIN_API.act.get_all_poitype)
      if (res.all_types) {
        for (let tp of res.all_types) {
          if (tp.show_in_admin) {
            this.typeOptions.push({
              label:
                window.KLK_LANG === 'zh-CN'
                  ? tp.name_cn
                  : window.KLK_LANG === 'zh-TW'
                  ? tp.name_tw
                  : tp.name_en,
              value: tp.poi_type
            })
          }
        }
      }

      this.typeOptions = this.typeOptions.filter((item) => !special_type.includes(item.value))
    }
  }
}
</script>

<style scoped lang="scss"></style>
