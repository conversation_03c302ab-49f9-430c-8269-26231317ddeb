<template>
  <a-table ref="table" :data="data" border :row-style="showTr" @row-click="rowClickHandle">
    <a-table-column
      v-for="(column, index) in columns"
      :key="column.dataIndex"
      :label="column.text"
      :width="column.width"
      :min-width="column.minWidth"
      :class-name="column.className"
    >
      <template slot-scope="scope">
        <template v-if="spaceIconShow(index, scope.row)">
          <span v-for="(space, levelIndex) in scope.row._level" :key="levelIndex" class="ms-tree-space" />
        </template>
        <span v-if="toggleIconShow(index, scope.row)" @click="toggle(scope.$index, scope.row)">
          <i v-if="!scope.row._expanded" class="el-icon-arrow-right" aria-hidden="true" />
          <i v-if="scope.row._expanded" class="el-icon-arrow-down" aria-hidden="true" />
        </span>
        <span v-else-if="index === 0" class="ms-tree-space" />
        {{ scope.row[column.dataIndex] }}
      </template>
    </a-table-column>
    <slot />
  </a-table>
</template>
<script>
import DataTransfer from './dataTranslate.js'
export default {
  name: 'TreeTable',
  props: {
    // 该属性是确认父组件传过来的数据是否已经是树形结构了，如果是，则不需要进行树形格式化
    treeStructure: {
      type: Boolean,
      default: function () {
        return false
      }
    },
    // 这是相应的字段展示
    columns: {
      type: Array,
      default: function () {
        return []
      }
    },
    // 这是数据源
    dataSource: {
      type: Array,
      default: function () {
        return []
      }
    },
    // 是否默认展开所有树
    defaultExpandAll: {
      type: Boolean,
      default: function () {
        return false
      }
    }
  },
  data() {
    return {}
  },
  computed: {
    // 格式化数据源
    data: function () {
      let me = this
      if (me.treeStructure) {
        let data = DataTransfer.treeToArray(me.dataSource, null, null, me.defaultExpandAll)
        // log('computed data', data)
        return data
      }
      return me.dataSource
    }
  },
  methods: {
    // 显示行
    showTr: function (row, index) {
      let show = row._parent ? row._parent._expanded && row._parent._show : true
      row._show = show
      return show ? '' : 'display:none;'
    },
    // 展开下级
    toggle: _.debounce(async function (trIndex, row) {
      let children = this.data[trIndex].children
      if (!row._expanded && _.isEmpty(children)) {
        this.$emit('get-child-node', row)
      }

      let me = this
      let record = me.data[trIndex]
      record._expanded = !record._expanded
    }, 300),
    // 显示层级关系的空格和图标
    spaceIconShow(index, record) {
      let me = this
      if (me.treeStructure && index === 0) {
        return true
      }
      return false
    },
    // 点击展开和关闭的时候，图标的切换
    toggleIconShow(index, record) {
      let me = this
      if (me.treeStructure && index === 0 && record.is_leaf === 0) {
        return true
      }
      return false
    },
    rowClickHandle(row) {
      let index = _.findIndex(this.data, {
        id: row.id
      })
      if (this.toggleIconShow(0, row)) {
        this.toggle(index, row)
      }
    }
  }
}
</script>
<style lang="scss">
.ms-tree-space {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: 'Glyphicons Halflings';
  font-style: normal;
  font-weight: 400;
  line-height: 1;
  width: 18px;
  height: 14px;
}
.ms-tree-space::before {
  content: '';
}
table td {
  line-height: 26px;
}
td.first-column {
  .cell {
    text-align: left;
  }
}
</style>
