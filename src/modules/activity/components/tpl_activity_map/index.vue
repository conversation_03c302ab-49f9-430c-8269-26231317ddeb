<template>
  <div v-disabled="{ lock: initialized && disabled }" class="activity_map-container">
    <DescMarkdownContent
      class="strong-tips"
      placement="right"
      :is-inline="false"
      :desc="schemaConfig && schemaConfig.strongTips"
      :line-clamp="3"
    />

    <ShimAntdTooltip
      v-bind="$attrs"
      :title="$attrs.description"
      :display-fullscreen-btn="displayDetails2tooltip"
      :auto-adjust-overflow="false"
      overlay-class-name="common-tooltip-style tooltip-rich-text-padding"
    >
      <!-- have-rich-tips 会 padding 撑开 -->
      <template v-if="displayDetails2tooltip" #content>
        <MarkdownText class="description" :text="$t('88450')" />
        <MapPositionDetails class="map-details" :details="positionDetails" />
      </template>

      <klk-ant-area-map
        v-if="initialized"
        style="height: 400px;"
        class="ant-area-map"
        :initial-map="initialMap"
        :initial-location="initialLocation"
        :language="language"
        :disabled="initialized && disabled"
        @change="getMapInfo"
      />

      <slot name="position_details" :data="positionDetails">
        <MapPositionDetails v-if="!displayDetails2tooltip" :details="positionDetails" />
      </slot>
    </ShimAntdTooltip>
  </div>
</template>

<script>
import { KlkAntAreaMap } from '@klook/admin-ui'
import ShimAntdTooltip from '@activity/components/shimAntdTooltip'
import MapPositionDetails from './position_details'
import MarkdownText from '@activity/components/MarkdownText.vue'
import DescMarkdownContent from '@activity/pages/activityManagement/detailV2/components/descMarkdownContent'

export default {
  components: {
    DescMarkdownContent,
    KlkAntAreaMap,
    ShimAntdTooltip,
    MapPositionDetails,
    MarkdownText
  },
  props: {
    language: {
      type: String,
      default: undefined
    },
    init: {
      type: Object,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    schemaConfig: {
      type: Object,
      default: () => {}
    },
    displayDetails2tooltip: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      initGoogleMaps: null,
      reloadPosition: true,
      positionDetails: {
        success: false
      },
      form: {
        address: '',
        place_id: '',
        location: '',
        location_wgs84: ''
      },
      initialMap: 'google_maps',
      initialized: false,
      initialLocation: null
    }
  },
  watch: {
    init: {
      immediate: true,
      deep: true,
      handler(v) {
        if (v && !this.initialized) {
          this.$set(this, 'form', {
            ...this.form,
            ...v
          })

          if (this.form.location) {
            const [lat, lng] = this.form.location.split(',')

            if (lng !== undefined) {
              this.initialLocation = {
                lat: +lat,
                lng: +lng
              }
            }

            this.initialMap = this.form.place_id ? 'google_maps' : 'amap'
            this.$nextTick(() => {
              let details = this.getMapDetail({
                place_id: this.form.place_id,
                location: this.initialLocation
              })

              this.$set(this, 'positionDetails', details)
              this.initialized = true
            })
          } else {
            this.initialized = true
          }
        }
      }
    }
  },
  methods: {
    getMapDetail(data) {
      let location_wgs84 = ''
      let { place_id, location, location_details, name, formatted_address } = data

      if (location_details) {
        location_wgs84 = _.find(location_details.by_location_system, {
          // search result
          system: 'WGS84'
        }).location

        location_wgs84 = this.initLocation(location_wgs84)
        this.$set(this.form, 'location_wgs84', location_wgs84)
      }

      location = this.initLocation(location)

      let place_id_url = 'https://developers.google.com/places/place-id'
      let add_missing_url = 'https://support.google.com/maps/answer/6320846'
      let gmap_preview_url = `https://www.google.com/maps/search/?api=1&query=${location}&query_place_id=${place_id}`

      this.form.location = location
      let inputDOM = document.querySelector('.activity_map-container .pac-target-input')
      this.form.address = (inputDOM && inputDOM.value) || ''

      return {
        name,
        location,
        place_id,
        gmap_preview_url,
        place_id_url,
        add_missing_url,
        success: true,
        location_wgs84,
        formatted_address
      }
    },
    getMapInfo(data) {
      let details = this.getMapDetail(data)
      this.$set(this, 'positionDetails', details)
      this.$emit('change', details)
    },
    initLocation(location) {
      if (!_.isEmpty(location)) {
        let { lat, lng } = location
        lat = _.ceil(lat, 6)
        lng = _.ceil(lng, 6)

        return lat + ',' + lng
      }
    },
    handleCheckCoordinate(event) {
      let val = typeof event === 'string' ? event : undefined
      this.$refs.map.$emit('submit-address', val) // 修复第一次值不精确问题
    }
  }
}
</script>

<style lang="scss" scoped>
$box-width: 820px;

.address__header {
  display: flex;
  margin-bottom: 16px;
  .address__queryBtn {
    margin-left: 16px;
  }
}
.ant-area-map {
  ::v-deep .ant-input {
    width: inherit !important;
  }
}

.description {
  font-weight: 600;
  font-size: 16px;
  line-height: 150%;
  color: rgba(0, 0, 0, 0.85);
}

.map-details {
  padding: 0;
  font-size: 14px;
  line-height: 150%;
  background-color: #fff;
}
</style>
