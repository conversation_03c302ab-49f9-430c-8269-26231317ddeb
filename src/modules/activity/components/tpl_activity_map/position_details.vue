<template>
  <div v-if="details.success" class="position-detail">
    <p>
      {{ $t('global_location') }}:&nbsp;
      <span class="colorful-position">
        {{ details.location }}
      </span>
      <br />
    </p>
    <template v-if="details.place_id && type === 'google_maps'">
      <p>
        {{ $t('global_place_id') }}:&nbsp;
        <span class="colorful-position">
          {{ details.place_id }}
        </span>
        <br />
        <a target="_blank" :href="details.gmap_preview_url" @click="handleA(details.gmap_preview_url)">
          Preview by Google Map URL↗
        </a>
      </p>
      <p class="tips">
        {{ $t('global_tips') }}
        <a target="_blank" :href="details.place_id_url" @click="handleA(details.place_id_url)">
          Google Place ID
        </a>
        can help our users find the place easier using Google map app with more info provided. You can also
        <a target="_blank" :href="details.add_missing_url" @click="handleA(details.add_missing_url)">
          Add a Missing Place
        </a>
        in Google Map if the prompted results are not desired.
      </p>
    </template>
  </div>
</template>

<script>
export default {
  name: 'MapPositionDetails',
  props: {
    details: {
      type: Object,
      required: true
    },
    type: {
      type: String,
      default: 'google_maps'
    }
  },
  methods: {
    handleA(url) {
      document.location = url
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.position-detail {
  margin-top: 16px;
  background-color: rgba(255, 171, 0, 0.05);
  padding: 8px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  line-height: 14px;
  p {
    margin: 0;
  }
  .tips {
    margin-top: 16px;
  }
  a {
    color: #2073f9;
    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
