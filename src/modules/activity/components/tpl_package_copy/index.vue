<template>
  <div v-if="showTo || showFrom" class="tpl_package_copy-container common-copy-wrap">
    <!-- act_copy_from_another -->
    <a-button
      :disabled="disabledCopyButton"
      :data-spm-module="getSpm"
      data-spm-virtual-item="__virtual"
      @click="copyHandle"
    >
      <slot>{{ $t('act_pkg_copy_price') }}</slot>
    </a-button>
    <a-modal
      :visible.sync="copyDialog.visible"
      :title="$t('package_copy_schedule_dialog_title')"
      width="780px"
      @cancel="handleCancel"
    >
      <PackageCopyForm
        ref="packageCopyForm"
        :options="copyOptions"
        :section-options="sectionOptions"
        :show-to="showTo"
        :show-from="showFrom"
        :show-section="showSection"
        :default-value="isPublished ? 1 : 2"
        @change="handleCopyChange"
      />
      <span slot="footer">
        <a-button @click="handleCancel">{{ $t('global_cancel') }}</a-button>
        <a-button :loading="submitLoading" type="primary" @click="submitCopyHandle">
          {{ $t('global_submit') }}
        </a-button>
      </span>
    </a-modal>
  </div>
</template>

<script>
import PackageCopyForm from '@activity/components/package-copy-form/index.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'TplPackageCopy',
  components: {
    PackageCopyForm
  },
  props: {
    package_id: {
      required: true
    },
    url: {
      type: String,
      required: true
    },
    klk_gvars: {
      type: Object,
      required: klk_gvars
    },
    handleSave: {
      type: Function,
      default: null
    },
    copy_type: String,
    fieldKeys: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      submitLoading: false,
      copyDialog: {
        visible: false,
        selection: null,
        options: []
      },
      copyOptions: [],
      sectionOptions: [
        {
          label: 'All',
          value: 'all'
        },
        {
          label: __('121699'),
          value: 'reservation_config'
        }
      ]
    }
  },
  computed: {
    ...mapGetters(['isSimOrWifi', 'isSpa']),
    showTo() {
      return !this.isCreate && (this.isSimOrWifi || this.isSpa)
    },
    hasReservationFields() {
      return this.fieldKeys?.includes('reservation_required')
    },
    showSection() {
      return this.hasReservationFields
    },
    showFrom() {
      if (!this.copyPkgList.length) {
        return false
      }

      if (this.isSpa && this.isPublished) {
        return false
      }

      return true
    },
    isPublished() {
      return _.get(this.currentPackage, 'publish_status') === 1
    },
    isMerchant() {
      return this.$root.isMerchant
    },
    getSpm() {
      const id = this.$route.params.id || 0
      const oid = `activity_${id}`
      const spm = this.isMerchant ? 'Copy_Pkg' : 'CopyPackageInfo'
      return `${spm}?trg=manual&oid=${oid}`
    },
    currentPackage() {
      const list = this.allPackages
      const package_id = +this.package_id
      return list.find((item) => item.package_id === package_id)
    },
    allPackages() {
      const list = this.klk_gvars.package_all || []
      return list.map((pkg) => ({
        ...pkg,
        key: pkg.package_id,
        value: this.getName(pkg)
      }))
    },
    copyPkgList() {
      const list = this.allPackages
      const package_id = +this.package_id
      const currentPackage = this.currentPackage || {}
      const { ticket_type, unschedule } = currentPackage
      // 只能copy相同类型的套餐
      if (this.isCreate) {
        return list.filter((item) => item.package_id !== package_id)
      }
      return list.filter((item) => {
        const isNotCurrent = item.package_id !== package_id
        // ticket_type = -1 表示未填写
        const isSameTicket = item.ticket_type === ticket_type || ticket_type == -1
        const isSameUnschedule = item.unschedule === unschedule
        return isNotCurrent && isSameTicket && isSameUnschedule
      })
    },
    copyToPkgList() {
      const list = this.allPackages
      const package_id = +this.package_id
      const currentPackage = this.currentPackage || {}
      const { ticket_type, unschedule } = currentPackage
      return list.filter((item) => {
        const isNotCurrent = item.package_id !== package_id
        // ticket_type = -1 表示未填写
        const isSameTicket = item.ticket_type === ticket_type || item.ticket_type == -1
        const isSameUnschedule = item.unschedule === unschedule
        return isNotCurrent && isSameTicket && isSameUnschedule
      })
    },
    isCreate: {
      catch: false,
      get: function () {
        return !this.$route.query.package_id
      }
    },
    disabledCopyButton() {
      const { isCreate, isMerchant, copyPkgList } = this
      const length = copyPkgList.length
      return (isCreate || isMerchant) && length <= 0
    }
  },
  methods: {
    handleCopyChange({ type }) {
      this.copyOptions = type === 1 ? this.copyToPkgList : this.copyPkgList
    },
    handleCancel() {
      this.submitLoading = false
      this.copyDialog.visible = false
    },
    getName(pkg) {
      return pkg.package_local && pkg.package_local[0].name
    },
    async copyHandle() {
      if (this.copy_type === 'price') {
        let ids = []
        for (let i of this.copyPkgList) {
          ids.push(i.package_id)
        }
        let pkgModel = {}
        let res = await ajax.get({
          url: ADMIN_API.act.get_package_pricing_model,
          package_ids: ids.join(',')
        })
        res.map((i) => (pkgModel[i.package_id] = i.pricing_model))
        this.$set(this, 'copyDialog', {
          visible: true,
          options: this.copyPkgList.map((pkg) => ({
            key: pkg.package_id,
            value: this.getName(pkg),
            disabled: !!pkgModel[pkg.package_id]
          })),
          selection: null
        })
      } else {
        this.$set(this, 'copyDialog', {
          visible: true,
          options: this.copyPkgList.map((pkg) => ({
            key: pkg.package_id,
            value: this.getName(pkg)
          })),
          selection: null
        })
      }
    },
    async submitCopyHandle() {
      const packageCopyForm = this.$refs.packageCopyForm
      const validate = await packageCopyForm.validateForm()
      if (!validate) {
        return
      }

      const data = packageCopyForm.getData()

      this.submitLoading = true
      if (typeof this.handleSave === 'function') {
        await this.handleSave(data)
        this.handleCancel()
        return
      }
    }
  }
}
</script>

<style scoped lang="scss"></style>
