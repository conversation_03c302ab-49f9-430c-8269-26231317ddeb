<template>
  <a-checkbox-group v-model="e_levy" class="package-levy-box" :disabled="disabled">
    <a-checkbox :value="0b0001">
      <span v-html="$t('82050')"></span>
    </a-checkbox>
    <a-checkbox :value="0b0010">
      <span v-html="$t('82051')"></span>
    </a-checkbox>
    <!-- <a-checkbox :value="0b0100"> {{ $t('elevy_act_outside_hk') }}</a-checkbox> -->
    <a-checkbox :value="0b0000" :disabled="checkboxDisabled">
      {{ $t('elevy_none') }}
    </a-checkbox>
  </a-checkbox-group>
</template>

<script>
export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Number,
      required: true
    },
    disabled: {
      type: Boolean,
      required: false
    }
  },
  data() {
    return {}
  },
  computed: {
    checkboxDisabled() {
      // 注释
      // return !!this.value && this.value !== 0b1000
      return false
    },
    e_levy: {
      get() {
        if (this.value === 0) {
          return [0]
        } else {
          return [0b0001, 0b0010].filter((e) => this.value & e)
          // return [0b0001, 0b0010, 0b0100].filter((e) => this.value & e)
        }
      },
      set(v) {
        // 最后一个做反选
        const check = v.includes(0) && this.value
        const value = check ? 0 : v.reduce((acc, cur) => acc | cur, 0b0000)
        this.$emit('change', value)
      }
    }
  }
}
</script>

<style lang="scss">
.package-levy-box.ant-checkbox-group {
  padding: 8px 0 0 0;
  font-size: 0;
  label.ant-checkbox-wrapper {
    margin: 0 32px 16px 0;
    display: block;
  }
}
</style>
