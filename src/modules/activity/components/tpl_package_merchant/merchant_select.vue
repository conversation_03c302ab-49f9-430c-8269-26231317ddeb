<template>
  <div class="merchant-select-wrap">
    <a-form-model-item :label="$t('act_merchant')" prop="pkg_merchant" :rules="rules" :colon="false">
      <shim-antd-tooltip placement="rightTop" overlay-class-name="common-tooltip-style" :title="$attrs.title">
        <a-input v-if="isMC2BD" disabled :value="defValue" />
        <a-select
          v-else
          v-model="merchant_id"
          v-check-val="
            () => {
              return merchant_id > 0
            }
          "
          :placeholder="$t('global_please_input')"
          :disabled="disabledMerchantSelect"
          :filter-option="false"
          show-search
          option-filter-prop="children"
          @search="remoteMethod"
        >
          <a-select-option
            v-for="item of merchants"
            :key="item.id"
            :disabled="disableMerchant(item.confirm_order, item.settlement_time)"
            :value="item.id"
          >
            {{ `${item.id} - ${item.en_name} - ${item.ch_name}` }}
          </a-select-option>
        </a-select>
        <p v-if="published">{{ $t('29045') }}</p>
      </shim-antd-tooltip>
      <StrongTips :tips="strongTips || $attrs.description" />
    </a-form-model-item>
  </div>
</template>

<script>
import StrongTips from '@activity/components/StrongTips.vue'
import { bestMatchLang, pmsConfirm } from '@activity/utils'
import ShimAntdTooltip from '../shimAntdTooltip'
import { mapMutations } from 'vuex'

var change_merchant_mixin = {
  data() {
    return {
      isCreateMerchant: false,
      new_currency: '',
      dialogVisible: false,
      pkg: {},
      canSubmit: true
    }
  },
  methods: {
    closeDialog() {
      this.$emit('close-dialog', false)
    },
    bestMatchLang: bestMatchLang
  }
}

const tier_mixin = {
  data() {
    return {
      dialogTierVisible: false
    }
  },
  methods: {
    async cancelMerchantChange(isConfirm, ajaxOptions) {
      this.dialogTierVisible = false
      if (!isConfirm) {
        this.merchant_id = this.cache_merchant_id
      } else {
        await ajax.post(
          {
            url: ADMIN_API.act.update_merchant_id,
            data: {
              merchant_id: this.merchant_id,
              package_id: this.package_id
            }
          },
          ajaxOptions || ajax.sucOptions
        )

        this.cache_merchant_id = this.merchant_id
      }
    }
  }
}

export default {
  name: 'MerchantSelect',
  components: {
    ShimAntdTooltip,
    StrongTips
  },
  mixins: [change_merchant_mixin, tier_mixin],
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    activityId: {
      type: Number,
      default: 0
    },
    packageInfo: {
      type: Object,
      required: true
    },
    value: {
      type: [Number, String, undefined],
      default: undefined
    },
    rules: {
      type: [Array, Object],
      default: () => ({
        required: true
      })
    },
    disabled: {
      type: Boolean,
      default: false
    },
    strongTips: {
      type: String,
      default: ''
    }
  },
  install(Vue) {
    // https://vuejs.org/v2/guide/plugins.html
    Vue.component(this.name, this)
  },
  data() {
    return {
      temp_new_merchant_id: '',
      merchants: [],
      all_merchants: [],

      merchant_id: undefined,
      cache_merchant_id: undefined,
      published: false,
      unit_ids: [],
      pkg_model: '',
      changeM: false,
      pkg_base_currency: ''
    }
  },
  computed: {
    // ...mapGetters(['currentPackageIsCreateByMerchant']),
    page_from() {
      return klook.getPlatformRoleKey()
    },
    disabledMerchantSelect() {
      // return this.disabled || this.isCreateMerchant || this.published
      return !!(
        (this.disabled || this.published)
        // || (this.page_from === 'admin' &&
        // this.$route.query.package_id && // 修复新增 pkg 的情况
        //   this.currentPackageIsCreateByMerchant)
      )
    },
    isMC2BD() {
      return this.$store.state.isMC2BD
      // return !this.$store.state.isMC2BD // test code
    },
    package_id() {
      return klook.parse2id(this.$route.query.package_id)
    },
    defValue() {
      let obj = this.all_merchants.find((o) => o.id === this.merchant_id) || {}
      let { id, en_name, ch_name } = obj
      return `${id} - ${en_name} - ${ch_name}`
    }
  },
  watch: {
    packageInfo: {
      immediate: true,
      deep: true,
      handler(v) {
        this.isCreateMerchant = !!_.get(v, 'merchant_create_author_id', false)
        this.published = !!_.get(v, 'auto_pub_unpub.package_published', false)
      }
    },
    merchant_id: {
      async handler(newVal, oldVal) {
        if (newVal && !oldVal) {
          this.cache_merchant_id = newVal
        } else if (oldVal && newVal !== this.cache_merchant_id) {
          await this.changeMerchant(newVal)
        }
        this.$emit('change', newVal, (this.all_merchants || []).filter((v) => v.id == newVal)[0] || {})
      },
      immediate: true
    }
  },
  beforeDestroy() {
    this.all_merchants = null
    this.merchants = null
    klook.bus.$off('updateMerchants', this.updateMerchants)
  },
  async created() {
    this.merchant_id = this.value
    this.all_merchants = await this.getAllMerchants()
    this.merchants = this.getMerchantsIncludesCurrentMerchant()
    klook.bus.$off('updateMerchants', this.updateMerchants).$on('updateMerchants', this.updateMerchants)
  },
  methods: {
    ...mapMutations({
      setDisabledVarOptionOnField: 'attr/setDisabledVarOptionOnField'
    }),
    getMerchantsIncludesCurrentMerchant() {
      if (this.merchant_id) {
        return this.all_merchants
          .sort((v) => {
            return v.id === this.merchant_id ? -1 : 1
          })
          .slice(0, 30)
      }
      return this.all_merchants.slice(0, 30)
    },
    updateDisableField() {
      if (this.merchant_id) {
        const currency = this.all_merchants.find((o) => o.id === this.merchant_id)?.currency
        if (currency) {
          // 有条件退改中的货币选择要根据当前选择的商家的货币来进行过滤
          this.setDisabledVarOptionOnField({
            field: 'cancellation_policy_currency',
            data: currency
          })
        }
      }
    },
    remoteMethod: _.debounce(function (query) {
      this.merchants = (this.all_merchants || [])
        .filter((v) => {
          return `${v.id}${v.en_name}${v.cn_name}`.toLowerCase().includes(String(query).toLowerCase())
        })
        .slice(0, 30)
    }, 200),
    disableMerchant(confirm_order, settlement_time) {
      return (
        this.packageInfo.inventory_type === 'OTHERS' &&
        this.packageInfo.voucher_type === 3 &&
        settlement_time === 1
      ) // merchant pdf can not use  pay by redemption merchant
    },
    async changeMerchant(merchant_id) {
      if (!this.package_id || !merchant_id) {
        return
      }
      var originalMerchantId = this.value
      if (!originalMerchantId) {
        return
      }

      this.new_currency = this.merchants.find((ele) => ele.id === merchant_id).currency

      let originalMerchant = this.all_merchants.find((ele) => ele.id === originalMerchantId)
      let originalCurrency = (originalMerchant && originalMerchant.currency) || null
      if (!originalCurrency) {
        return
      }

      // 二次确认框
      const confirm = await this.confirmFun()

      if (confirm) {
        await this.updateMerchantId()
      } else {
        this.merchant_id = this.cache_merchant_id
        this.merchants = this.getMerchantsIncludesCurrentMerchant()
      }
    },
    async confirmFun() {
      const confirm = await pmsConfirm.call(this, {
        title: this.$t('29046'),
        okText: this.$t('global_confirm'),
        cancelText: this.$t('global_cancel'),
        type: 'warning'
      })

      if (confirm) {
        return await this.checkMerchantOtherInfo()
      }

      return false
    },
    async checkMerchantOtherInfo() {
      const resp = await ajax.getBody(ADMIN_API.act.check_itinerary_map, {
        params: {
          pkg_id: this.package_id
        }
      })

      if (resp && resp.success) {
        const result = resp.result || {}
        const need_user_confirm = result.need_user_confirm
        if (need_user_confirm) {
          const cofirm = await pmsConfirm.call(this, {
            content: result.note
          })
          return cofirm
        }
        return true
      }
      return false
    },
    async updateMerchantId() {
      const res = await ajax.postBody({
        url: ADMIN_API.act.update_merchant_id,
        data: {
          merchant_id: this.merchant_id,
          package_id: this.package_id
        }
      })

      if (res.success) {
        this.cache_merchant_id = this.merchant_id
        this.$message.success((h) =>
          h('span', {
            domProps: {
              innerHTML: this.$t('27747').replace('<br/><br/>', '<br/>')
            }
          })
        )
      } else {
        this.merchant_id = this.cache_merchant_id
        this.$message.error(this.$t('global_modify_error'))
      }
    },
    updateMerchants(merchant_id) {
      this.merchant_id = merchant_id
      this.merchants = this.all_merchants.filter((o) => o.id === this.merchant_id)
    },
    async getAllMerchants() {
      const res = await ajax.get(ADMIN_API.act.get_merchant_list, {
        cache: true,
        version: 3,
        maxAge: 60 * 10 * 1000 // 10 mins
      })
      return res || []
    }
  }
}
</script>
