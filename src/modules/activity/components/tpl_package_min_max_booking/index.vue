<template>
  <span>
    <a-input v-model.number="form.min_participants" class="short-input" :disabled="disabled"></a-input>
    -
    <a-select v-model="show_max_par" class="short-select" :disabled="disabled" @change="$emit('changeData')">
      <a-select-option :value="1">{{ $t('package_info_unlimited') }} </a-select-option>
      <a-select-option :value="2">
        {{ $t('package_info_limited') }}
      </a-select-option>
    </a-select>
    <a-input
      :disabled="disabled"
      v-show="show_max_par === 2"
      v-model.number="form.max_participants"
      class="short-input"
      style="margin-left: 6px;"
    ></a-input>

    <!-- <span class="widespan">{{ $t('package_info_participants') }}</span> -->
  </span>
</template>

<script>
export default {
  props: {
    value: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      required: false
    }
  },
  data() {
    return {
      force_show_max_par: 0
    }
  },
  computed: {
    form: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('update:value', v)
        this.$emit('changeData')
      }
    },
    show_max_par: {
      get() {
        if (this.force_show_max_par) {
          return this.force_show_max_par
        } else {
          return this.form.max_participants === 9999 ? 1 : 2
        }
      },
      set(v) {
        if (v === 1) {
          this.form.max_participants = 9999
        }
        this.force_show_max_par = v
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.short-input {
  width: 100px;
}
.short-select {
  width: 326px;
}
.widespan {
  margin-left: 12px;
}
</style>
