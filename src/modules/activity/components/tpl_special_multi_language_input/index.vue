<template>
  <component
    :is="isFormModel ? 'a-form-model' : 'div'"
    ref="form"
    :model="form"
    class="special_multi_language_input-container"
    :class="{
      'common-tpl-basic-style': isFormModel
    }"
    @submit="handleSubmit"
  >
    <a-form-model-item
      v-for="(current, index) in form[dataField]"
      :key="current[langField]"
      :label="index === 0 ? label : ''"
      :prop="`${dataField}.${index}.${valField}`"
      :rules="getRules(current, index)"
      :colon="false"
      :data-current="JSON.stringify(form[dataField])"
    >
      <DescMarkdownContent
        v-if="index === 0"
        class="strong-tips"
        placement="right"
        :is-inline="false"
        :desc="$attrs.strongTips"
        :line-clamp="3"
      />
      <ShimAntdTooltip v-bind="$attrs" :title="$attrs.description">
        <component
          :is="type === 'markdown' ? 'obt-markdown' : 'a-input'"
          ref="inputItem"
          v-model="current[valField]"
          style="width: 100%"
          :type="type"
          :style="index ? inputStyle : {}"
          :auto-size="{ minRows: 3, maxRows: 6 }"
          :placeholder="`${placeholder} (${getLangTitle(current[langField])})`"
          :config="{
            placeholder: `${placeholder}`,
            ...config,
            ...(mdConfig || {})
          }"
          :disabled="disabledFactory(current)"
          :class="getClassList(current[langField])"
          :show-suffix-count="showSuffixCount"
          :max-length="getMaxLength(current[langField])"
          @blur="onBlur(index)"
          @change="onChange(index)"
        >
          <div v-if="showSuffixCount" slot="suffix" class="common-text-count">
            <span class="count-box">
              {{ getSuffixCount(current[valField].length, current[langField]) }}
            </span>
          </div>
        </component>
      </ShimAntdTooltip>
      <div v-if="showCustomSuffixCount" class="custom-text-count">
        {{ getSuffixCount(current[valField].length, current[langField]) }}
      </div>
    </a-form-model-item>
  </component>
</template>

<script>
import {
  getLangTitle,
  getEditLang,
  getRefLang,
  getWordCountLimit,
  getSuffixCount,
  getWordCountLimitDisabled,
  onlyEnglishTexts
} from '@activity/utils'
import ObtMarkdown from '@activity/components/obt-markdown/index.vue'
import ShimAntdTooltip from '../shimAntdTooltip'
import { isAdmin } from '@/env'
import DescMarkdownContent from '@activity/pages/activityManagement/detailV2/components/descMarkdownContent'

export default {
  name: 'SpecialMultiLanguageInput',
  components: { ShimAntdTooltip, ObtMarkdown, DescMarkdownContent },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    mdConfig: {
      type: Object,
      default: () => {}
    },
    config: {
      type: Object,
      default: () => {}
    },
    showSuffixCount: {
      type: Boolean,
      required: false,
      default: false
    },
    value: {
      type: Array,
      required: true
    },
    // 当 isFormModel 为 false 的时候，formItem prop 需要跟上层的 formModel 匹配，不然会 warn
    // 当 isFormModel 为 true 的时候，组件自身就是独立的 form，则不存在问题
    dataField: {
      type: String,
      default: 'data'
    },
    langField: {
      type: String,
      default: 'language'
    },
    valField: {
      type: String,
      default: 'value'
    },
    requiredEn: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: __('global_please_input')
    },
    type: {
      type: String,
      default: 'input'
    },
    initSchema: {
      type: Object,
      default: () => ({})
    },
    label: {
      type: String,
      default: ''
    },
    required: {
      type: Boolean,
      default: false
    },
    onlyEditLanguage: {
      // 用于控制仅仅显示当前编辑语言
      type: Boolean,
      default: false
    },
    currentCreateLang: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    isFormModel: {
      type: Boolean,
      default: true
    },
    inputStyle: {
      type: Object,
      default: () => ({
        marginTop: '12px'
      })
    },
    greenLight: {
      type: Boolean,
      default: false
    },
    typeFlag: {
      type: Number,
      default: 0
    },
    defaultMaxInputLength: {
      type: Number,
      default: 50
    }
  },
  data() {
    return {}
  },
  computed: {
    showCustomSuffixCount() {
      const typeList = ['textarea']
      const { showSuffixCount, type } = this
      return showSuffixCount && typeList.includes(type)
    },
    wordCountLimit() {
      return this.config?.word_count_limit ?? []
    },
    editLanguage() {
      return this.currentCreateLang || getEditLang()
    },
    refLanguage() {
      return getRefLang()
    },
    currentRefLanguage() {
      if (this.editLanguage === 'en_US' || this.refLanguage) {
        return this.refLanguage
      }

      if (this.requiredEn) {
        return 'en_US'
      }

      return ''
    },
    allEditLanguage() {
      let allLanguage = [this.editLanguage, this.currentRefLanguage]

      if (this.requiredEn && !allLanguage.includes('en_US')) {
        allLanguage = [...allLanguage, 'en_US']
      }

      return allLanguage.filter((item) => item)
    },
    form: {
      get() {
        return {
          [this.dataField]: this.allEditLanguage
            .filter(
              (language) =>
                (this.onlyEditLanguage && language === this.editLanguage) || !this.onlyEditLanguage //
            )
            .map((language) => this.getValue(language))
        }
      },
      set(v) {
        this.$emit('change', v)
      }
    }
  },
  methods: {
    validateFieldByIndex(index) {
      const { dataField, valField } = this
      const prop = `${dataField}.${index}.${valField}`
      this.$refs.form?.validateField?.(prop)
    },
    onBlur(index) {
      this.$emit('blur', this.form[this.dataField], index)
      this.validateFieldByIndex(index)
    },
    onChange(index) {
      this.$emit('change', this.form[this.dataField])
      this.validateFieldByIndex(index)
    },
    getClassList(language) {
      const { showSuffixCount } = this
      if (!showSuffixCount) {
        return ['']
      }
      const max = this.getMaxLength(language)
      return [max > 99 && max < Infinity ? 'common-suffix-count-large' : 'common-suffix-count']
    },
    getSuffixCount(length = 0, language) {
      const max = this.getMaxLength(language)
      return getSuffixCount(max, length)
    },
    getMaxLength(language) {
      const wordCountLimit = this.wordCountLimit
      return getWordCountLimit(wordCountLimit, language)
    },
    handleSubmit(e) {
      e.preventDefault()
      // this.form.validateFields((err, values) => {
      //   if (!err) {
      //     console.log('Received values of form: ', values)
      //   }
      // })
    },
    getLangTitle: getLangTitle,
    disabledFactory(current) {
      let currentLang = current[this.langField]

      if (this.greenLight || (!this.disabled && this.requiredEn && currentLang === 'en_US')) {
        return false
      }

      return (
        this.disabled ||
        (this.refLanguage && currentLang !== this.editLanguage) || // 有参考语言的时候都需要锁定
        (currentLang === this.currentRefLanguage && this.currentRefLanguage !== 'en_US')
      )
    },
    validateForm() {
      return new Promise((resolve) => this.$refs.form.validate((valid) => resolve(valid)))
    },
    markdownContentValidator() {
      return [...this.$refs.inputItem].every((item) => {
        if (item.$attrs.type === 'markdown') {
          return item.validator(true)
        }

        return true
      })
    },
    getRules(current, index) {
      let disabled = this.disabledFactory(current)
      const maxLength = this.getMaxLength(current[this.langField])
      const text = this.$t('83853', { num: maxLength })
      return {
        required: this.required && (index === 0 || !disabled),
        validator: (rule, value, callback) => {
          value = _.get(this.form, rule.fullField, '').trim()
          // 超过最大字数限制
          if (value.length > maxLength && getWordCountLimitDisabled(disabled)) {
            return callback(new Error(text))
          }
          let typeFlag = this.typeFlag
          let field = rule.field
          let flag = typeFlag === 6 && (field === 'info.1.name' || field === 'info.0.name')
          let valueLength = value.length > this.defaultMaxInputLength

          // 校验 en_US 活动标题只能输入英文、数字和特殊字符
          if (this.dataField === 'activity_title' && isAdmin) {
            const lang = this.getLangugeKeys(rule.fullField)
            if (lang === 'en_US' && !onlyEnglishTexts(value)) {
              return callback(new Error(__('80734')))
            }
          }

          // 校验 en_US 套餐名称只能输入英文、数字和特殊字符
          if (this.dataField === 'pkg_name') {
            const lang = this.getLangugeKeys(rule.fullField)
            if (lang === 'en_US' && !onlyEnglishTexts(value)) {
              return callback(new Error(__('112910')))
            }
          }

          if (rule.required && flag && valueLength) {
            callback(new Error(__('83853', { num: this.defaultMaxInputLength })))
          } else if (this.type === 'markdown' && !this.$refs.inputItem[index].validator()) {
            callback(new Error(__('110654')))
          } else if (rule.required && !value) {
            callback(new Error(__('package_error_mandatory')))
          } else {
            callback()
          }
        }
      }
    },
    getLangugeKeys(field = '') {
      const [key, index] = field.split('.')
      const langField = this.langField
      return _.get(this.form[key], `${index}.${langField}`)
    },
    getValue(language, data = this.value) {
      let index = _.findIndex(data, {
        [this.langField]: language
      })

      if (-1 === index) {
        this.value.push({
          ...this.initSchema,
          [this.langField]: language,
          [this.valField]: ''
        })
        return _.last(this.value)
      }

      return this.value[index]
    }
  }
}
</script>
<style lang="scss">
.special_multi_language_input-container {
  .ant-row.ant-form-item:not(:first-child) {
    min-height: auto;
  }
}
</style>
<style lang="scss" scoped>
.special_multi_language_input-container {
  display: flex;
  flex-direction: column;
}

.special-input-item {
  margin-bottom: 20px;
  &:first-child {
    margin-bottom: 0;
  }
}
.custom-text-count {
  text-align: right;
  color: rgba(0, 0, 0, 0.45);
  height: 20px;
  line-height: 20px;
}
</style>
