<template>
  <div class="upload_excel-container">
    <input
      type="file"
      ref="file_input"
      style="display: none;"
      @change="onFileChange"
      accept=".csv, .xls, .xlsx"
    />
    <a-tooltip v-if="show_tips" placement="top" overlayClassName="common-tooltip-style" :title="tips">
      <slot name="btn-upload">
        <a-button icon="upload" class="btn-upload" ghost @click="$refs['file_input'].click()">
          {{ btn_text }}
          <template v-if="show_file_name && fileName"> ({{ fileName }}) </template>
        </a-button>
      </slot>
    </a-tooltip>
  </div>
</template>

<script>
export default {
  name: 'upload_excel',
  props: {
    show_tips: {
      type: Boolean,
      default: true
    },
    tips: {
      type: String,
      default: 'Supports CSV/XLS/XLSL formats only'
    },
    show_file_name: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    btn_text: {
      type: String,
      default: function() {
        return this.$t('29162')
      }
    },
    // If false, the first row is an array of items. e.g. [[row[0][0]: value ]]
    // If true, the first is the field of array item. e.g. [{ row[0][0]: value }]
    header_is_field: {
      type: Boolean,
      default: false
    },
    // XLSX default value is empty
    default_val: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fileName: ''
    }
  },
  methods: {
    // https://github.com/SheetJS/js-xlsx
    async onFileChange(e) {
      const XLSX = await import('xlsx')
      let file = e.target.files[0],
        reader = new FileReader()
      this.fileName = file.name
      reader.onload = (event) => {
        let workbook = XLSX.read(event.target.result, {
          type: 'binary'
        })

        let sheetData = workbook.SheetNames.map((sheetName) => {
          let ws = workbook.Sheets[sheetName],
            data = XLSX.utils.sheet_to_json(ws, {
              header: this.header_is_field ? 0 : 1,
              defval: this.default_val
            })

          return {
            sheetName,
            data: data.filter((item) => !item.every((v) => v === ''))
          }
        })

        this.$emit('allData', sheetData)
        this.$emit('file_data', _.get(sheetData, '0.data', [])) // original logical
        this.$refs['file_input'].value = ''
      }

      reader.readAsBinaryString(file)
    }
  }
}
</script>
<style lang="scss" scoped>
.upload_excel-container {
  display: inline-block;
  .btn-upload {
    color: #0091ff;
    border: 1px solid #0091ff;
    margin: 0;
  }
}
</style>
