const marked = require('marked')
const customRenderer = new marked.Renderer()
// 重写 renderer 的 paragraph 方法，使其不生成 <p> 标签
customRenderer.paragraph = function(text) {
  return text // 返回原始文本，不包装 <p> 标签
}
// 设置 marked 的配置，使用自定义渲染器
marked.setOptions({
  renderer: customRenderer,
  sanitize: false
})
export function handleTrans2Html(value) {
  value = value.replace(/javascript:/g, encodeURIComponent('javascript:'))
  const htmlOutput = marked(value)
  return htmlOutput
}

export function matchHtmlTags(textWithHTML) {
  const htmlTags = textWithHTML.match(/<\/?[^>]+(>|$)/g)
  const tagNames = htmlTags ? htmlTags.map((tag) => tag.replace(/<\/?([^>\s]+).*?>/g, '$1')) : []
  return tagNames?.reduce((acc, currentValue) => {
    if (!acc.includes(currentValue)) {
      acc.push(currentValue)
    }
    return acc
  }, [])
}

export function getMatchedHtmlTags(text) {
  const htmlStr = handleTrans2Html(text)
  const matchedTags = matchHtmlTags(htmlStr)
  return matchedTags
}
