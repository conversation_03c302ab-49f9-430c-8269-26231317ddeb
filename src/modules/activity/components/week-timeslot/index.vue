<template>
  <div class="reservation-time-picker-wrap">
    <div class="time-picker-select-wrap">
      <a-tree-select
        v-model="selectWeek"
        :tree-data="options"
        tree-checkable
        :placeholder="$t('121717')"
        :max-tag-count="1"
        :disabled="disabled"
        class="time-picker-select"
      />
    </div>
    <div class="time-picker-separator">:</div>
    <div class="time-picker-group">
      <div v-for="(time, index) in weekTimeslot" :key="index" class="time-picker-item-wrap">
        <a-time-picker
          :value="initValue(time.start)"
          format="HH:mm"
          :default-open-value="defaultOpenValue"
          :minute-step="30"
          :placeholder="$t('121718')"
          :disabled="disabled"
          class="time-picker-item"
          @change="(e) => handleTimeSlotChange(e, index, 'start')"
        >
          <span slot="suffixIcon"></span>
        </a-time-picker>
        <div class="time-picker-separator">~</div>
        <a-time-picker
          :value="initValue(time.end)"
          :class="{
            'is-next-day': calcNextDay(time)
          }"
          format="HH:mm"
          :default-open-value="defaultOpenValue"
          :minute-step="30"
          :placeholder="$t('121719')"
          :disabled="disabled"
          class="time-picker-item"
          @change="(e) => handleTimeSlotChange(e, index, 'end')"
        />
        <div v-if="index > 0" class="icon-wrap" @click="handleDelete(index)">
          <a-icon type="close-circle" size="14" theme="filled" />
        </div>
      </div>
      <a-icon type="plus-circle" class="time-picker-item-add" @click="handleAdd" />
    </div>
  </div>
</template>
<script>
import { weekOptions } from '@activity/pages/package/package_const.js'
import moment from 'moment'

export default {
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    otherWeekData: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      defaultOpenValue: moment('00:00', 'HH:mm')
    }
  },
  computed: {
    options() {
      const { otherWeekData = [] } = this
      return weekOptions.map((item) => {
        const { value } = item
        item.disabled = otherWeekData.includes(value)
        return { ...item }
      })
    },
    selectWeek: {
      get() {
        return _.cloneDeep(_.get(this.data, 'week_day', []))
      },
      set(v) {
        this.$emit('change', {
          week_day: v,
          timeslot: this.weekTimeslot
        })
      }
    },
    weekTimeslot() {
      return _.get(this.data, 'timeslot', [])
    },
    ymd() {
      return moment().format('YYYY-MM-DD')
    }
  },
  methods: {
    calcNextDay({ start, end }) {
      if (start && end) {
        return moment(`${this.ymd} ${end}`).isBefore(`${this.ymd} ${start}`)
      }

      return false
    },
    initValue(value) {
      return value ? moment(value, 'HH:mm') : null
    },
    handleTimeSlotChange(e, index, field) {
      const time = e && e.format('HH:mm')
      const list = _.cloneDeep(this.weekTimeslot)
      const data = list[index]
      list.splice(index, 1, { ...data, [field]: time })
      this.$emit('change', {
        week_day: this.selectWeek,
        timeslot: list
      })
    },
    handleDelete(index) {
      const list = _.cloneDeep(this.weekTimeslot)
      list.splice(index, 1)
      this.$emit('change', {
        week_day: this.selectWeek,
        timeslot: list
      })
    },
    handleAdd() {
      const list = _.cloneDeep(this.weekTimeslot)
      list.push({ start: undefined, end: undefined })
      this.$emit('change', {
        week_day: this.selectWeek,
        timeslot: list
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.reservation-time-picker-wrap {
  margin: 8px 0;
  display: flex;

  .time-picker-separator {
    margin: 0 8px;
    height: 32px;
    line-height: 30px;
  }

  .time-picker-group {
    display: flex;
    max-width: 480px;
    flex-wrap: wrap;

    .time-picker-item-wrap {
      display: flex;
      align-items: center;
      margin-right: 8px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      margin-bottom: 8px;
      background: #fff;
      position: relative;

      // &:hover {
      //   .icon-wrap {
      //     display: flex;
      //   }
      // }

      .icon-wrap {
        position: absolute;
        z-index: 1;
        top: -7px;
        right: -7px;
        color: #8a8a8a;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .time-picker-separator {
        color: #d9d9d9;
        font-size: 18px;
      }

      .time-picker-item {
        width: 100px;

        ::v-deep .ant-time-picker-input {
          border: none;
        }
      }
    }

    .time-picker-item-add {
      font-size: 20px;
      color: #2073f9;
      cursor: pointer;
      margin-top: 6px;
      margin-right: 16px;
    }
  }

  .time-picker-select-wrap {
    display: flex;
    height: 32px;
    position: relative;

    .time-picker-select {
      width: 200px;
    }
  }

  .is-next-day {
    position: relative;

    &::after {
      content: '+1';
      position: absolute;
      top: 4px;
      right: 36px;
      font-size: 10px;
      color: #000;
    }
  }
}
</style>

<style lang="scss">
.has-error {
  .time-picker-item-wrap {
    border: 1px solid #f5222d !important;
  }
}
</style>
