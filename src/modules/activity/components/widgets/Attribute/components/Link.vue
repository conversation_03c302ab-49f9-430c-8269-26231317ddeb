<template>
  <a-form-model-item :prop="row.key" :class="{ disabled: disabled }" :label="formItemLabel" :rules="getRule">
    <div style="display: inline-block; width: 100%;">
      <a-input
        v-model.trim="linkName"
        size="small"
        placeholder="Link Text"
        :disabled="disabled || $attrs.readonly"
        @blur="debounceValidate"
      />
      <a-input
        v-model.trim="linkUrl"
        size="small"
        placeholder="Link URL"
        :disabled="disabled || $attrs.readonly"
        @blur="debounceValidate"
      />
      <template v-if="ref_lang && chosen">
        <a-input
          v-model.trim="ref_linkName"
          type="textarea"
          size="small"
          :placeholder="`Reference Link Text (${ref_lang})`"
          :disabled="true"
        />
        <a-input
          v-model.trim="ref_linkUrl"
          type="textarea"
          size="small"
          :placeholder="`Reference Link URL (${ref_lang})`"
          :disabled="true"
        />
      </template>
    </div>
  </a-form-model-item>
</template>

<script>
import cpnt_mixin from './cpnt_mixin.js'
const isEmptyLink = (v) => v === '[]()'
export default {
  name: 'StructLink',
  mixins: [cpnt_mixin],
  inject: ['ref_lang'],
  model: {
    prop: 'data_var_map',
    event: 'change'
  },
  data() {
    return {}
  },
  computed: {
    refValue() {
      return _.get(this.ref_data, 'values[0].value', '[]()')
    },
    currentValue: {
      get() {
        let result = _.get(this.data_var_map, 'values[0].value', '[]()')

        // put in getter so that this will trigger when init
        this.$emit('update:form', result)

        let display_html = isEmptyLink(result)
          ? ''
          : `<a class="variable is-link" href=${this.getUrl(result)} target='_blank'> ${this.getName(
              result
            )} </a>`

        this.$emit('update:display_html', display_html)

        return result
      },
      set(v) {
        let data = []
        if (_.get(this.data_var_map, 'values[0].id')) {
          data = [
            {
              ...this.data_var_map.values[0],
              create: false,
              value: v
            }
          ]
        } else {
          data = [
            {
              create: true,
              value: v
            }
          ]
        }

        this.$emit('change', { ...this.data_var_map, values: data })
      }
    },
    ref_linkName() {
      return this.getName(this.refValue)
    },

    ref_linkUrl() {
      return this.getUrl(this.refValue)
    },
    linkName: {
      get() {
        return this.getName(this.currentValue)
      },
      set(val) {
        if (!val && !this.linkUrl) {
          this.$emit('input', '')
          this.currentValue = ''
          return
        }
        this.currentValue = `[${val}](${this.linkUrl})`
      }
    },
    linkUrl: {
      get() {
        return this.getUrl(this.currentValue)
      },
      set(val) {
        if (!val && !this.linkName) {
          this.$emit('input', '')
          this.currentValue = ''
          return
        }
        this.currentValue = `[${this.linkName}](${val})`
      }
    },

    getRule() {
      let that = this
      return {
        // required: this.var_map.required,
        required: true,
        trigger: ['blur', 'change'],
        validator(rule, value, cb) {
          if (
            that.getUrl(value).match(/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?/) &&
            -1 === that.getUrl(value).indexOf(' ')
          ) {
            // validate url
            cb()
          }
          cb(new Error('Please input valid URL'))
        }
      }
    }
  },
  watch: {
    currentValue() {
      this.debounceValidate()
    }
  },
  methods: {
    debounceValidate: _.debounce(function() {
      if (_.get(this, '$parent.validateField', null) && _.get(this, 'var_map.key', null)) {
        // waiting for rendering to finished
        this.$nextTick(() => {
          this.$parent.validateField(this.var_map.key)
        })
      }
    }, 300),
    getName: (v) => v.replace(/^\[(.*)\]\(.*\)/, '$1'),
    getUrl: (v) => v.replace(/^\[.*\]\((.*)\)$/, '$1')
  }
}
</script>
