<template>
  <a-form-model-item
    class="multi-date-picker-container"
    :prop="row.key"
    :class="{ disabled: disabled }"
    :label="formItemLabel"
    :rules="getRule"
  >
    <div class="multi-date-picker-body">
      <component
        :is="pickerType[type] || 'a-date-picker'"
        style="display:inline-block"
        size="small"
        :show-time="type === 'datetime'"
        :disabled="disabled || $attrs.readonly"
        :type="type"
        placeholder="Please select date"
        @change="handleDateItem"
      >
        <div class="multi-date-picker-list">
          <template v-if="!dateList.length">
            Please select date
          </template>
          <a-tag
            v-else
            v-for="(date, index) of dateList"
            closable
            :key="index"
            @close="handleDeleteDate(index)"
          >
            {{ date }}
          </a-tag>
        </div>
        <template slot="dateRender" slot-scope="current">
          <div
            class="multi-date-picker-cell"
            :class="{
              'has-checked': hasCheckedStyle(current)
            }"
            @click.stop.prevent="handleDateItem(current)"
          >
            {{ current.date() }}
          </div>
        </template>
      </component>
      <a-icon type="calendar" style="color: rgba(0, 0, 0, 0.25)" />
    </div>
  </a-form-model-item>
</template>

<script>
import moment from 'moment'
import cpnt_mixin from './cpnt_mixin.js'

export default {
  name: 'MultiStructDatePicker',
  mixins: [cpnt_mixin],
  model: {
    prop: 'data_var_map',
    event: 'change'
  },
  data() {
    return {
      pickerType: {
        date: 'a-date-picker',
        month: 'a-month-picker',
        week: 'a-week-picker',
        datetime: 'a-date-picker',
        daterange: 'a-range-picker'
      }
    }
  },
  computed: {
    currentValue: {
      get() {
        let data = this.data_var_map
        let v = _.get(data, 'values[0].value', '')
        this.$emit('update:form', v)
        this.$emit('update:display_html', v)
        return v
      },
      set(v) {
        let data = []
        if (_.get(this.data_var_map, 'values[0].id')) {
          data = [
            {
              ...this.data_var_map.values[0],
              create: false,
              value: v
            }
          ]
        } else {
          data = v
            ? [
                {
                  create: true,
                  value: v
                }
              ]
            : []
        }
        this.$emit('change', { ...this.data_var_map, values: data })
      }
    },
    getRule() {
      let that = this
      // return { required: this.var_map.required };
      return {
        required: true,
        trigger: ['blur', 'change'],
        validator: (rule, value, callback) => {
          that.$nextTick(() => {
            if (that.currentValue) {
              callback()
            } else {
              callback(new Error(`${rule.field} is required`))
            }
          })
        }
      }
    },
    dateList() {
      return (this.currentValue && this.currentValue.split(',')) || []
    }
  },
  methods: {
    handleDeleteDate(index) {
      let dateList = this.dateList.slice()
      dateList.splice(index, 1)
      this.$set(this, 'currentValue', dateList.join(','))
    },
    handleDateItem(current) {
      if (!current) return

      let dateList = this.dateList.slice()
      let currentDate = moment(current).format('YYYY-MM-DD')
      if (dateList.includes(currentDate)) {
        dateList.splice(
          dateList.findIndex((item) => item === currentDate),
          1
        )
      } else {
        dateList.push(currentDate)
      }
      this.$set(this, 'currentValue', dateList.join(','))
    },
    hasCheckedStyle(current) {
      return this.dateList.includes(moment(+current).format('YYYY-MM-DD'))
    }
  }
}
</script>

<style lang="scss">
.multi-date-picker-container {
  .multi-date-picker-body {
    display: flex;
    align-items: center;
  }
  .multi-date-picker-list {
    padding: 0 3px;
    border: 1px solid #d8d8d8;
    border-radius: 4px;
    background: #fff;
    color: #d8d8d8;
    min-width: 172px;
    flex: 1;
    margin-right: 2px;
    cursor: pointer;
    .ant-tag {
      margin: 3px;
    }
  }
}
.multi-date-picker-cell {
  cursor: pointer;
  &:hover {
    color: #1790ff;
  }
  &.has-checked {
    width: 24px;
    height: 24px;
    line-height: 24px;
    border-radius: 24px;
    border: 1px solid #1790ff;
    margin: 0 auto;
    color: #1790ff;
  }
}
</style>
