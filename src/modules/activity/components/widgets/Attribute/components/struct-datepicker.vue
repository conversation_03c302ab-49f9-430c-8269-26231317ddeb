<template>
  <a-form-model-item :prop="row.key" :class="{ disabled: disabled }" :label="formItemLabel" :rules="getRule">
    <component
      :is="pickerType[type] || 'a-date-picker'"
      v-model="currentValue"
      style="display:inline-block"
      size="small"
      :show-time="type === 'datetime'"
      :disabled="disabled || $attrs.readonly"
      :type="type"
      placeholder="Please select date"
    />
  </a-form-model-item>
</template>

<script>
import moment from 'moment'
// let typeStruct = ADMIN_API.struct({
//   attr_item_id: "number",
//   key: "string",
//   values: [
//     {
//       is_free_text: "boolean?",
//       id: "number?",
//       create: "boolean",
//       value: "string",
//       order: "number?"
//     }
//   ]
// });
import cpnt_mixin from './cpnt_mixin.js'

const TIMEZONE_OFFSET = new Date().getTimezoneOffset() * 60 * 1000 // -480 * s * ms
export default {
  name: 'StructDatePicker',
  mixins: [cpnt_mixin],
  model: {
    prop: 'data_var_map',
    event: 'change'
  },
  data() {
    return {
      pickerType: {
        date: 'a-date-picker',
        month: 'a-month-picker',
        week: 'a-week-picker',
        datetime: 'a-date-picker',
        daterange: 'a-range-picker'
      }
    }
  },
  computed: {
    currentValue: {
      get() {
        let curValue = _.get(this.data_var_map, 'values[0].value', '')
        if (curValue) {
          // backend will translate timestamp to text using UTC timezone
          // add timezone offset to proper display on admin user browser
          curValue = new Date(+curValue + TIMEZONE_OFFSET)
        }
        let display_html = curValue
          ? this.type === 'date'
            ? moment(curValue).format('YYYY-MM-DD')
            : moment(curValue).format('YYYY-MM-DD HH:mm:ss')
          : ''

        this.$emit('update:form', curValue)
        this.$emit('update:display_html', display_html)
        ADMIN_API.struct.union(['date', ADMIN_API.struct.literal('')])(curValue)
        return display_html
      },
      set(v) {
        let data = []
        let value = (v && String(v.valueOf() - TIMEZONE_OFFSET)) || ''

        if (_.get(this.data_var_map, 'values[0].id')) {
          data = [
            {
              ...this.data_var_map.values[0],
              create: false,
              value
            }
          ]
        } else {
          data = [
            {
              create: true,
              value
            }
          ]
        }
        this.$emit(
          'change',
          _.isEmpty(this.data_var_map)
            ? {
                attr_item_id: this.var_map.id,
                key: this.var_map.key,
                values: data
              }
            : {
                ...this.data_var_map,
                values: data
              }
        )
      }
    },
    getRule() {
      let that = this
      // return { required: this.var_map.required };
      return {
        required: true,
        trigger: ['blur', 'change'],
        validator: (rule, value, callback) => {
          that.$nextTick(() => {
            if (that.currentValue) {
              callback()
            } else {
              callback(new Error(`${rule.field} is required`))
            }
          })
        }
      }
    }
  },
  methods: {}
}
</script>
