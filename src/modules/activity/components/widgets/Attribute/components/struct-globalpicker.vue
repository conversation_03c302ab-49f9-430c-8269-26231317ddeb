<template>
  <a-form-model-item :prop="row.key" :class="{ disabled: disabled }" :label="formItemLabel" :rules="getRule">
    <div class="globalpicker-content">
      <label class="__html">{{ display_html }}</label>
      <a-button size="small" :disabled="disabled || $attrs.readonly" @click="handleEdit">Edit</a-button>
    </div>
    <a-drawer
      :title="$t('global_button_handle')"
      :width="600"
      :visible.sync="visible"
      :body-style="{ paddingBottom: '80px' }"
      :mask-closable="false"
      @close="handleCancel"
    >
      <!-- lazy render global picker -->
      <taxonomy-picker
        v-model="tempValue"
        class="spin-content"
        :language="edit_lang"
        :type="type"
        :countryonly="type === 'country'"
      />

      <div class="drawer-footer">
        <a-button @click="handleCancel">{{ $t('global_cancel') }}</a-button>
        <a-button type="primary" @click="confirm">{{ $t('global_confirm') }}</a-button>
      </div>
    </a-drawer>
  </a-form-model-item>
</template>

<script>
import { TaxonomyPicker } from '../../../index'

// let typeStruct = ADMIN_API.struct({
//   attr_item_id: "number",
//   key: "string",
//   values: [
//     {
//       id: "number",
//       value: "string",
//       create: "boolean"
//     }
//   ]
// });
import cpnt_mixin from './cpnt_mixin.js'

export default {
  inject: ['edit_lang'],
  name: 'StructGlobalpicker',
  components: {
    TaxonomyPicker
  },
  mixins: [cpnt_mixin],
  model: {
    prop: 'data_var_map',
    event: 'change'
  },
  data() {
    return {
      visible: false
      // global_data: []
    }
  },
  computed: {
    // global_data() {
    //   return this.$store.state.global_data;
    // },
    tempValue: {
      get() {
        let data = this.data_var_map || {}
        let v

        v = !_.isEmpty(data.values) ? data.values : []

        let display_html = (data.values || []).map((v) => v.value).join('/')

        this.$emit('update:form', v)
        this.$emit('update:display_html', display_html)

        ADMIN_API.struct('array').assert(v)

        return v
      },
      set(v) {
        if (_.isEqual(v, this.tempValue)) {
          console.log('loop')
          return
        }
        let values
        if (this.type === 'country') {
          values = v.map((country) => ({
            id: country.country_id,
            value: country.country_name,
            create: false
          }))
        } else {
          values = v.reduce(
            (acc, cur) => [
              ...acc,
              ..._.map(cur.city_ids, (id) => ({
                id,
                value: cur.cities.find((city) => city.id === id).name,
                create: false
              }))
            ],
            []
          )
        }
        this.$emit(
          'change',
          _.isEmpty(this.data_var_map)
            ? {
                attr_item_id: this.var_map.id,
                key: this.var_map.key,
                values
              }
            : {
                ...this.data_var_map,
                values
              }
        )
        console.log(v)
      }
    },
    getRule() {
      return { required: true }
    }
  },
  watch: {
    display_html() {
      this.debounceValidate()
    }
  },
  methods: {
    debounceValidate: _.debounce(function () {
      if (_.get(this, '$parent.validateField', null) && _.get(this, 'var_map.key', null)) {
        // waiting for rendering to finished
        this.$nextTick(() => {
          this.$parent.validateField(this.var_map.key)
        })
      }
    }, 300),
    async confirm() {
      this.visible = false
      this.$emit('close', true)
    },
    handleCancel() {
      this.tempValue = _.cloneDeep(this.cacheValue)
      this.$nextTick(() => {
        this.visible = false
        this.$emit('close', false)
      })
    },
    handleEdit() {
      this.visible = true
      this.cacheValue = _.cloneDeep(
        this.tempValue.map((item) => ({
          country_id: item.id,
          country_name: item.value
        }))
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-footer {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid rgb(233, 233, 233);
  padding: 10px 16px;
  background: rgb(255, 255, 255);
  text-align: right;
  z-index: 1;
  button {
    margin-left: 12px;
  }
}

.globalpicker-content {
  display: flex;
  align-items: center;
  gap: 4px;

  .__html {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    line-height: 32px;
  }
}
</style>
