<template>
  <a-form-model-item
    :prop="rowKey"
    :class="{ disabled: calcDisabled }"
    :label="formItemLabel"
    :rules="getRule()"
  >
    <div style="display: inline-block; width: 100%">
      <a-input-number
        v-model="currentValue"
        style="width: 100%"
        :min="0"
        :max="9999"
        :disabled="calcDisabled"
        placeholder="Please input"
        class="common-suffix-count"
        @change="handleChange(row)"
      />
    </div>
  </a-form-model-item>
</template>

<script>
import cpnt_mixin from './cpnt_mixin.js'

export default {
  name: 'StructInputNumber',
  mixins: [cpnt_mixin],
  inject: {
    vDisabled: {
      default: () => ({})
    }
  },
  model: {
    prop: 'data_var_map',
    event: 'change'
  },
  data() {
    return {}
  },
  computed: {
    rowKey() {
      return this.row.key.replace(/:/g, '___')
    },
    calcDisabled() {
      const lock = this.vDisabled?.lock ?? false
      const readonly = this.$attrs.readonly
      return this.disabled || readonly || lock
    },
    currentValue: {
      get() {
        let data = this.data_var_map
        let v = _.get(data, 'values[0].value', '')
        // 转换 string 为 number，防止 a-input-number 报错
        let numValue = v
        if (typeof v === 'string' && v !== '') {
          const num = Number(v)
          numValue = isNaN(num) ? '' : num
        }
        // emit 还是输出 string 类型，兼容原有逻辑
        this.$emit('update:form', String(v))
        this.$emit('update:display_html', String(v))
        return numValue
      },
      set(v) {
        let data = this.getData(String(v))
        console.log('set currentValue', data)

        this.$emit('change', { ...this.data_var_map, values: data })
      }
    }
  },
  methods: {
    getRule() {
      return {
        required: true,
        trigger: ['blur', 'change'],
        type: 'number',
        validator(rule, value, callback) {
          console.log('validate rule', rule, value)
          if (value === '' || value === null || value === undefined) {
            callback(new Error(`${rule.field} is required`))
          } else {
            callback()
          }
        }
      }
    },
    getData(v) {
      let data = []
      if (_.get(this.data_var_map, 'values[0].id')) {
        data = [
          {
            ...this.data_var_map.values[0],
            create: false,
            value: v
          }
        ]
      } else {
        data =
          v !== '' && v !== null && v !== undefined
            ? [
                {
                  create: true,
                  value: v
                }
              ]
            : []
      }

      return data
    },
    handleChange({ field, ref_field_tag }) {
      console.log('handleChange', {
        value: this.getData(this.currentValue),
        field,
        ref_field_tag
      })
      this.$nextTick(() => {
        this.$emit('changeStructInput', {
          value: this.getData(this.currentValue),
          field,
          ref_field_tag
        })
      })
    }
  }
}
</script>
