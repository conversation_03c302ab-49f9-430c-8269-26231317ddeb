<template>
  <a-form-model-item
    :prop="rowKey"
    :class="{ disabled: calcDisabled }"
    :label="formItemLabel"
    :rules="getRule()"
  >
    <div style="display: inline-block; width: 100%">
      <a-input
        v-model="currentValue"
        size="small"
        :disabled="calcDisabled"
        placeholder="Please input"
        class="common-suffix-count"
        :max-length="maxLength"
        @blur="blurStructInput"
        @change="handleChange(row)"
      >
        <span v-if="showSuffix" slot="suffix">{{ suffix }}</span>
      </a-input>
      <a-input
        v-if="ref_lang && chosen"
        v-model="refValue"
        type="textarea"
        size="small"
        :class="injectComponentClassNameListByKey('input')"
        :placeholder="`Reference Input (${ref_lang})`"
        :disabled="true"
      />
    </div>
  </a-form-model-item>
</template>

<script>
const isDecimal = (val) =>
  val &&
  String(val).includes('.') &&
  isInteger(String(val).split('.')[0]) &&
  isInteger(String(val).split('.')[1])
// 整数
const isInteger = _.isInteger

import cpnt_mixin from './cpnt_mixin.js'
import { getWordCountLimit, getSuffixCount } from '@activity/utils'

export default {
  name: 'StructInput',
  mixins: [cpnt_mixin],
  inject: {
    ref_lang: {
      default: ''
    },
    injectComponentClassNameListByKey: {
      default: () => () => ''
    },
    vDisabled: {
      default: () => ({})
    }
  },
  //   event: "change"
  model: {
    prop: 'data_var_map',
    event: 'change'
  },
  props: {
    wordCountLimit: {
      type: Array,
      default: () => []
    },
    showSuffix: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {
    rowKey() {
      return this.row.key.replace(/:/g, '___')
    },
    calcDisabled() {
      const lock = this.vDisabled?.lock ?? false
      const readonly = this.$attrs.readonly
      return this.disabled || readonly || lock
    },
    suffix() {
      const { maxLength, currentValue } = this
      return getSuffixCount(maxLength, currentValue.trim().length)
    },
    maxLength() {
      return getWordCountLimit(this.wordCountLimit)
    },
    refValue() {
      let data = this.ref_data
      let v = _.get(data, 'values[0].value', '')
      return v
    },
    currentValue: {
      get() {
        let data = this.data_var_map
        let v = _.get(data, 'values[0].value', '')
        this.$emit('update:form', v)
        this.$emit('update:display_html', v)
        return v
      },
      set(v) {
        let data = this.getData(v)

        this.$emit('change', { ...this.data_var_map, values: data })
      }
    }
  },
  methods: {
    getRule() {
      const that = this
      let rules = [
        {
          required: true,
          validator(rule, value, callback) {
            if (that.currentValue === null || that.currentValue === undefined || that.currentValue === '') {
              return callback('Please input')
            }

            return callback()
          }
        }
      ]
      let obj = JSON.parse(this.data_var_map?.validation ?? '{}')

      let inputType = obj.input_type
      if (inputType === 'int') {
        rules.push({
          trigger: ['blur', 'change'],
          validator(rule, value, callback) {
            return isInteger(that.currentValue) ? callback() : callback('not integer')
          }
        })
      } else if (inputType === 'float') {
        rules.push({
          trigger: ['blur', 'change'],
          validator(rule, value, callback) {
            return isDecimal(that.currentValue) ? callback() : callback('not decimal')
          }
        })
      }
      return rules
    },
    getData(v) {
      let data = []
      if (_.get(this.data_var_map, 'values[0].id')) {
        data = [
          {
            ...this.data_var_map.values[0],
            create: false,
            value: v
          }
        ]
      } else {
        data = v
          ? [
              {
                create: true,
                value: v
              }
            ]
          : []
      }

      return data
    },
    blurStructInput() {
      this.currentValue = _.trim(this.currentValue)
    },
    handleChange({ field, ref_field_tag }) {
      this.$nextTick(() => {
        this.$emit('changeStructInput', {
          value: this.getData(_.trim(this.currentValue)),
          field,
          ref_field_tag
        })
      })
    }
  }
}
</script>
