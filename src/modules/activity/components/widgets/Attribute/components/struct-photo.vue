<template>
  <a-form-model-item :prop="row.key" :class="{ disabled: disabled }" :rules="getRule">
    <PhotoGallery
      ref="photo_gallery"
      :cur-lang="curLang"
      :value="list"
      :refLang="refLang"
      :activityId="activityId"
      :need-alt="needAlt"
      :need-desc="needDesc"
      :limit="limit"
      :need-display-as-banner="false"
      @current-list-change="handleCurrentListChange"
    />
  </a-form-model-item>
</template>

<script>
import PhotoGallery from '~src/modules/aidRevamp/components/photo-and-video/photo-gallery/index.vue'
import { getEditLang, getRefLang } from '@activity/utils'
import cpnt_mixin from './cpnt_mixin.js'
import {
  formatInputImageList,
  formatOutputImageList,
  formatImageDisplayHtml
} from '~src/modules/activity/components/widgets/Attribute/utils'

const IMAGE_COUNT_LIMIT = 10

export default {
  name: 'StructPhoto',
  components: { PhotoGallery },
  mixins: [cpnt_mixin],
  model: {
    prop: 'data_var_map',
    event: 'change'
  },
  props: {
    extraConfig: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      refLang: getRefLang(),
      curLang: getEditLang(),
      activityId: this.$route.params?.id,
      list: [],
      limit: IMAGE_COUNT_LIMIT
    }
  },
  computed: {
    getRule() {
      let that = this
      // return { required: this.var_map.required };
      return {
        required: true,
        trigger: ['blur', 'change'],
        validator: (rule, value, callback) => {
          that.$nextTick(() => {
            if (that.list?.length) {
              callback()
            } else {
              callback(new Error('image is required'))
            }
          })
        }
      }
    },
    needAlt() {
      return this.extraConfig.image_need_alt || false
    },
    needDesc() {
      return this.extraConfig.image_need_desc || false
    }
  },
  watch: {
    'data_var_map.values': {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        if (_.isEqual(newVal, oldVal)) {
          return
        }
        const _val = newVal?.[0]?.value || '[]'
        const val = JSON.parse(_val) || []
        const list = formatInputImageList(val, this.curLang) || []
        this.list = list
      }
    }
  },
  methods: {
    handleCurrentListChange(_newList = []) {
      // 数据变化后从 PhotoGallery 获取的最新数据处理逻辑
      const newList = formatOutputImageList(_newList, this.curLang) || []
      const final_html = formatImageDisplayHtml(newList)
      this.$emit('update:display_html', final_html)
      // 根据是否是编辑状态构造 payload

      const data = this.getData(JSON.stringify(newList))
      this.$emit(
        'change',
        _.isEmpty(this.data_var_map)
          ? {
              attr_item_id: this.var_map.id,
              key: this.var_map.key,
              values: data
            }
          : {
              ...this.data_var_map,
              values: data
            }
      )
    },
    getData(v) {
      let data = []
      if (_.get(this.data_var_map, 'values[0].id')) {
        data = [
          {
            ...this.data_var_map.values[0],
            create: false,
            value: v
          }
        ]
      } else {
        data = v?.length
          ? [
              {
                create: true,
                value: v
              }
            ]
          : []
      }

      return data
    }
  }
}
</script>
