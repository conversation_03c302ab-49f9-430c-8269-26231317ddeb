<template>
  <a-form-model-item :prop="row.key" :class="{ disabled: disabled }" :label="formItemLabel" :rules="getRule">
    <a-select
      v-model="currentValue"
      :disabled="
        disabled ||
        $attrs.readonly ||
        disabledVarOption.includes(`${option.ref_field_tag}-${row.ref_field_tag}`)
      "
      size="small"
      allow-clear
      placeholder="Please select"
      show-search
      collapse-tags
      :mode="multiple ? 'multiple' : 'default'"
      :filter-option="false"
      :not-found-content="fetching ? undefined : null"
      @change="(v) => handleChange(v, row)"
      @search="handleSearch"
      @dropdownVisibleChange="showOptions = true"
    >
      <!-- eslint-disable vue/no-use-v-if-with-v-for,vue/no-confusing-v-for-v-if -->
      <a-select-option v-for="item in currOptions" :key="item.id" :label="item.value" :value="item.id">
        {{ item.value }}
      </a-select-option>
    </a-select>
  </a-form-model-item>
</template>

<script>
// let typeStruct = ADMIN_API.struct({
//   attr_item_id: "number",
//   key: "string",
//   values: [
//     {
//       id: "number",
//       value: "string",
//       create: "boolean",
//     }
//   ]
// });
import cpnt_mixin from './cpnt_mixin.js'

export default {
  name: 'StructSelect',
  mixins: [cpnt_mixin],
  model: {
    prop: 'data_var_map',
    event: 'change'
  },
  props: {
    isActivateEditing: {
      type: Boolean,
      default: false
    },
    disabledVarOption: {
      type: Array,
      default: () => []
    },
    field: {
      type: String,
      default: ''
    },
    option: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      showOptions: false,
      options: [],
      fetching: false
    }
  },
  computed: {
    currency() {
      return this.$store.getters['attr/disabledVarOptionByFieldGetter']({
        field: 'cancellation_policy_currency'
      })
    },
    currOptions() {
      const widgetsID = 'cancellation_policy_fee_type'
      // 这里有一段逻辑，如果是取消政策，widgetsID是货币选择的slect只显示当前商户的币种
      if (
        this.isActivateEditing &&
        widgetsID === this?.row?.ref_field_tag &&
        this.field === 'cancellation_policy' &&
        this.currency?.length > 0
      ) {
        return (this.options || []).filter((option) => option.value === this.currency || option.value === '%')
      }

      return (this.options || []).filter(
        (option) => !this.disabledVarOption.includes(`${this.row.ref_field_tag}-${option.ref_field_tag}`)
      )
    },
    opted_less_options() {
      if (this.showOptions) {
        return this.var_map.values
      } else {
        // lazy load el-options
        // <a-select-option> list will cause great perf issue
        // https://github.com/ElemeFE/element/issues/6527
        if (this.multiple ? _.isEmpty(this.currentValue) : !this.currentValue) {
          return []
        } else {
          return this.var_map.values.filter((v) => _.map(this.data_var_map.values, 'id').includes(v.id))
        }
      }
    },
    // select & multiple select share the same type `1`. only judge multi select by using select_max in it's `validation` field
    multiple() {
      // return _.get(JSON.parse(this.var_map.validation), "select_max", 1) > 1;
      // multiple_selection_variables 包含标识是多选
      let multiple_selection_variables =
        _.get(JSON.parse(this.config || '{}'), 'multiple_selection_variables') || []
      return multiple_selection_variables.includes(this.var_map.id)
    },
    currentValue: {
      get() {
        let data = this.data_var_map || {}
        let v
        let display_html = ''
        if (_.isEmpty(data.values)) {
          v = this.multiple ? [] : ''
        } else {
          v = this.multiple ? data.values.map((v) => v.id) || [] : data.values[0].id || ''
        }
        if (!_.isEmpty(data.values)) {
          display_html = this.multiple
            ? this.var_map.values
                .filter((option) => v.includes(option.id))
                .map((v) => v.value)
                .join('/')
            : this.var_map.values.find((option) => v == option.id)?.value || ''
        }

        this.$emit('update:display_html', display_html)
        this.$emit('update:form', v)
        return this.multiple
          ? ADMIN_API.struct('array')(v)
          : ADMIN_API.struct.union(['number', ADMIN_API.struct.literal('')])(v)
      },
      set(v) {
        let data = []
        if (this.multiple) {
          data = this.var_map.values
            .filter((option) => v.includes(option.id))
            .map((option) => ({
              ...option,

              create: false
            }))
        } else {
          data = v
            ? [
                {
                  ...this.var_map.values.find((option) => v == option.id),

                  create: false,
                  id: v
                }
              ]
            : []
        }
        this.$emit(
          'change',
          _.isEmpty(this.data_var_map)
            ? {
                attr_item_id: this.var_map.id,
                key: this.var_map.key,
                values: data
              }
            : {
                ...this.data_var_map,
                values: data
              }
        )
      }
    },

    getRule() {
      return {
        required: true,
        trigger: ['blur', 'change'],
        validator(rule, value, callback) {
          if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
            callback(new Error(`${rule.field} is required`))
          } else {
            callback()
          }
        }
      }
    }
  },
  watch: {
    currOptions: {
      deep: true,
      handler(options) {
        if (!this.multiple && options.every((option) => option.id !== this.currentValue)) {
          this.currentValue = undefined
        }
      }
    },
    // FIXME have to use watch? ?
    data_var_map: {
      deep: true,
      handler(v) {
        // console.log('data_var_map', v)
      }
    },
    var_map: {
      deep: true,
      handler(v) {
        // console.log('var_map', v)
      }
    }
  },
  mounted() {
    this.options = this.var_map.values
  },
  methods: {
    handleChange(v, { field, ref_field_tag }) {
      this.$emit('changeStructSelect', {
        index: this.options.findIndex((item) => item.id === v),
        field,
        ref_field_tag
      })
    },
    handleSearch: _.debounce(function (query) {
      this.fetching = true
      if (query && query.trim().length) {
        this.options = this.opted_less_options.filter((option) =>
          option.value.toLowerCase().includes(query.toLowerCase())
        )
      } else {
        this.options = this.opted_less_options
      }
      this.fetching = false
    }, 100)
  }
}
</script>
