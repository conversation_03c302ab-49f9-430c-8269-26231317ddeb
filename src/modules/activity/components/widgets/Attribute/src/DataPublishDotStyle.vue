<template>
  <div
    v-tooltip="{
      visible: true,
      content: content
    }"
    class="data-publish-dot-style-container"
    :class="{
      'is-prod-data': published === 1,
      'is-draft-data': published === 0,
      'is-default-data': published === -1
    }"
  >
    <slot />
  </div>
</template>

<script>
export default {
  name: 'DataPublishDotStyle',
  props: {
    published: {
      type: Number,
      required: true
    }
  },
  computed: {
    content() {
      return this.published === 1 ? this.$t('global_published') : this.published === 0 ? this.$t('80677') : ''
    }
  }
}
</script>

<style lang="scss" scoped>
@import './mixin.scss';

.data-publish-dot-style-container {
  position: relative;
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 16px;
  background-color: #212121;
  cursor: pointer;

  @include dataStatusStyle;
}
</style>
