<template>
  <!-- 两种形式 : 1. 单选or多选选项(属性值),  2 freetext,不带勾选, 后续可能混杂 -->
  <!-- {id}_{order} 作为唯一 instance id，并不参与实际的排序（拖拽之后会更改）。顺序由前端控制的 ui_index 实现 -->
  <!-- option_list / check list 的实现待优化 -->
  <div v-if="someIsValidTag || leftTimes > 0" class="compound-select">
    <!-- 多选 -->
    <component
      :is="`${multi ? 'a-checkbox' : 'a-radio'}-group`"
      v-show="option_list.length || isAdding"
      v-model="_checkList"
      :max="title.select_limit"
    >
      <!-- <transition-group name="list"> -->
      <draggable
        v-model="option_list"
        v-bind="{
          animation: 300,
          group: `draggable-${title.id}`,
          scroll: true,
          scrollSpeed: 2000,
          scrollSensitivity: 10
        }"
        handle=".sort-btn"
        filter=".compose-li-disable"
        draggable=".compose-li-draggable"
        @end="handleDragEnd"
      >
        <!-- eslint-disable vue/no-use-v-if-with-v-for,vue/no-confusing-v-for-v-if -->
        <div
          v-for="(option, index) in option_list"
          v-if="isValidTag(option.ref_field_tag) && !option.hide"
          :key="genUUID(option) + index"
          class="compound-options"
          :class="option.ash ? 'compose-li-disable' : 'compose-li-draggable'"
        >
          <template v-if="!getOptionHideStateByOpt(option)">
            <CompoundOption
              ref="struct_option"
              class="struct_option"
              v-bind="$attrs"
              :draft-attr-values-deleted="draftAttrValuesDeleted"
              :multi="title.select_limit > 1"
              :label="genUUID(option)"
              :option="option"
              :index="index"
              :data="data"
              :total="option_list.length"
              :ref_data="getOptionRefData(option)"
              :option_data="getOptionData(option)"
              :copy-times="cpGetCopyDisable(option) ? 0 : 1"
              :type="getOptionType(option)"
              :disable-check-by-cancellation="cpGetCheckboxDisable(option)"
              :field="field"
              :group-id="groupId"
              :chosen="isChosen(option)"
              :allow-sort="displaySort && option.access_permission !== 1"
              :title-id="title.id"
              :has-draft-state="hasDraftState"
              :temp-close-expand-editing="shouldResetUiIndexAndOrder"
              @cancelCloseExpandEditing="cancelCloseExpandEditing"
              @change="(option) => updateOptionData(option)"
              @removeFreeText="removeFreeText"
              @copyItem="copyItem"
              @reset="resetItem"
              @confirm-add="confirmAdd"
              @confirm-edit="confirmEdit"
              @unselect="unselect"
              @unselectOption2Draft="unselectOption2Draft"
              @resetOption2Draft="resetOption2Draft"
              @saveOption2Draft="saveOption2Draft"
              @updateFreeText2Draft="updateFreeText2Draft"
              @changePublishedValue="changePublishedValue"
              @autoSelectOption="autoSelectOption"
              @removeFreeText2Draft="removeFreeText2Draft"
              @deleteInvalidOptionData="deleteInvalidOptionData"
            >
              <!-- eslint-disable vue/html-self-closing -->
              <template slot="operation"></template>
            </CompoundOption>
          </template>
        </div>
      </draggable>
      <!-- 添加 -->
      <CompoundOption
        v-if="isAdding"
        key="compound-select-add-new"
        ref="addFreeText"
        v-bind="$attrs"
        type="add"
        :field="field"
        :has-draft-state="hasDraftState"
        :group-id="groupId"
        :data="data"
        @confirm-add="confirmAdd"
        @cancel-add="isAdding = false"
        @addFreeText2Draft="addFreeText2Draft"
      />
      <!-- </transition-group> -->
    </component>
    <div v-show="leftTimes > 0" class="compound-extend" :data-position="`${field}_${groupId}_${title.id}`">
      <a-button
        type="link"
        size="small"
        class="compound-extend__btn"
        :disabled="!isPriorLang || $attrs.readonly || title.flag === 1"
        @click="addNew"
      >
        + {{ $t('global_button_add') }}
      </a-button>
    </div>
  </div>
</template>

<script>
import CompoundOption from './Option'
import draggable from 'vuedraggable'
import { genUUID } from '@activity/utils'
import draft2prod_mixin from '@activity/pages/activityManagement/detailV2/draft2prod_mixin'
import cancelllation_policy_mixin from '@activity/pages/package/basicInfo/mixins/cancellation/compoundGroup.js'

let dataStruct = ADMIN_API.struct([
  ADMIN_API.struct.interface({
    create: 'boolean?',
    ui_index: 'number?',
    order: 'number?', // TODO
    dup_limit: 'number?',
    id: 'number?',
    type: 'number?',
    is_free_text: 'boolean?',
    value: 'string',
    variable_map: 'any?',
    // 新增, 可否多选由该字段判断
    config: 'string?' //  "{ default_selected: false, multiple_selection_variables: [] }", // 配置信息 JSON 串,
  })
])

// 由于拖拽排序或者使用 op = replace 更新草稿数据, 此举会导致 option order 改变（后端逻辑导致）
// 此时需要重置 option order 及 ui_index, 进而会更新相应的 checkList 值。
// 此时会导致 option 进入勾选展开编辑态, 这里使用 shouldResetUiIndexAndOrder 来更新 order 的同时阻止展开编辑
const reset_order_when_replace_mixin = {
  data() {
    return {
      // 初始化需要重置为勾选的 order 值
      shouldResetUiIndexAndOrder: true
    }
  },
  methods: {
    cancelCloseExpandEditing: _.debounce(function () {
      this.shouldResetUiIndexAndOrder = false
    }, 100)
  }
}

const basic_info_attribute_mixin = {
  computed: {
    someIsValidTag() {
      return (
        (this.option_list.length &&
          this.option_list.some((option) => this.isValidTag(option.ref_field_tag))) ||
        true
      )
    }
  },
  methods: {
    isValidTag(ref_field_tag) {
      const invalid_attr_value_tags = _.get(this.$attrs, 'invalid_tag.invalid_attr_value_tags', [])

      return !invalid_attr_value_tags.includes(ref_field_tag)
    }
  }
}

// order 和 ui_index 的前端旧逻辑和后端草稿方案有冲突, 待简化这部分逻辑（todo: 取消原有逻辑的自动触发更新, 改为初始化 + 手动更新 order 和 ui_index 会更可控）
export default {
  name: 'CompoundGroup',
  components: { CompoundOption, draggable },
  mixins: [
    reset_order_when_replace_mixin,
    basic_info_attribute_mixin,
    draft2prod_mixin,
    cancelllation_policy_mixin
  ],
  model: {
    prop: 'data',
    event: 'change'
  },
  props: {
    ref_data: Array,
    data: { type: Array },
    title: { type: Object },
    // 选中值
    value: {
      type: [Array],
      default: () => {}
    },
    // // 必填
    // required: {
    //   type: Boolean,
    //   default: () => {}
    // },
    autoCheckedToFront: {
      type: Boolean,
      default: true
    },
    checkList: {
      type: [Array, String],
      default: () => {}
    },
    field: {
      type: String,
      default: ''
    },
    groupId: {
      type: Number,
      default: 0
    },
    displaySort: {
      type: Boolean,
      default: true
    },
    hasDraftState: {
      type: Boolean,
      default: false
    },
    draftAttrValuesDeleted: {
      type: Array,
      default: () => []
    },
    isCreate: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 最大勾选数量
      max: 1000,
      // list: [],
      optionTypeMap: {
        1: 'text',
        2: 'template',
        0: 'freetext'
      },
      // 剩余可添加次数
      // 允许添加次数, 0, 不允许添加
      // 正在添加
      isAdding: false,
      ui_index_list: []
    }
  },
  computed: {
    // 存在 pub 数据但在草稿下被删除了，需要标记删除线和草稿态 dot
    draftIsDeleted() {
      return (this.option && this.draftAttrValuesDeleted.includes(genUUID(this.option))) || false
    },
    _checkList: {
      get() {
        return this.checkList
      },
      set(v) {
        this.$emit('update:checkList', v)
      }
    },
    commonStoreParams() {
      return {
        field: this.field,
        groupId: this.groupId,
        titleId: this.title.id
      }
    },
    // 拉平 non-multi 数据为数组类型
    checkList2Arr() {
      if (!Array.isArray(this.checkList)) {
        return [this.checkList]
      }

      return this.checkList
    },
    isPriorLang() {
      return this.isOnlyLang || this.isEN
    },
    isOnlyLang() {
      const { react_data = [] } = this.locale_info || {}
      return (react_data || []).filter((v) => v.status).length == 1
    },
    isEN() {
      return this.edit_lang === 'en_US'
    },
    leftTimes() {
      return this.title.freetext_limit - this.data.filter((option) => option.is_free_text).length
    },
    // 选项列表
    option_list: {
      // data may duplicate or missing
      // we generation a unioned array
      // with property from both data and ui
      // and with duplicated data as standalone option
      // FIXME
      get() {
        return this.getOptionList()
      },
      set(new_list) {
        this.handleSort(new_list)
      }
    },
    multi() {
      return this.title.select_limit > 1
    }
  },
  watch: {
    isCreate: {
      immediate: true,
      handler(isCreate) {
        if (isCreate && this.hasDraftState) {
          this.$nextTick(async () => {
            for (const checked of this.checkList2Arr) {
              const [id, order] = checked.split('_')
              const index = this.option_list.findIndex((option) => option.id == id && option.order == order)
              const option = this.option_list[index]
              if (option?.type === 1) {
                // free text
                await this.saveOption2Draft({ option, index, published: 0 })
              }
            }
          })
        }
      }
    },
    checkList: {
      deep: true,
      handler(v) {
        console.log('watch check list', v)
        this.changeCheckList(v)
      }
    },
    checkList2Arr: {
      deep: true,
      async handler(v, oldV) {
        oldV = oldV.filter((item) => item)
        // 单选的时候会相等
        if (v.length - oldV.length >= 0) {
          await this.$nextTick()
          const diff = _.xorWith(v, oldV)

          ;(this.$refs.struct_option || []).forEach((item) => {
            if (diff.includes(item.label)) {
              item?.handleToEditing?.()
            }
          })
        }

        this.$emit(
          'update:draftAttrValuesDeleted',
          this.draftAttrValuesDeleted.filter((item) => !v.includes(item))
        )
      }
    }
  },
  methods: {
    genUUID,
    async initializedCallback() {
      await this.$nextTick()
      let value = this.multi ? [] : ''
      this.title.attr_value_list.forEach((attrValue) => {
        const hasInstanceData = this.data.find((item) => item.id === attrValue.id)

        if (!hasInstanceData && attrValue.auto_select) {
          if (this.multi) {
            value.push(genUUID(attrValue))
          } else {
            value = genUUID(attrValue)
          }
        }
      })

      if (value.length) {
        const value2list = Array.isArray(value) ? value : [value]
        const checkList = this.multi ? [...new Set([...this.checkList, ...value2list])] : value2list[0]
        this.$emit('update:checkList', checkList)
      }
    },
    getNewInsertUiIndex() {
      return this.option_list.length
    },
    getNewInsertOrder() {
      return Math.max(...this.option_list.map((item) => item.order || 0), 0) + 1
    },
    autoSelectOption({ value }) {
      const checkList = this.multi ? [...new Set([...this._checkList, value])] : value
      this.$emit('update:checkList', checkList)
    },
    // 继承原有的逻辑
    changeCheckList(v) {
      // 避免重复的计算 order / ui_index
      if (this.shouldResetUiIndexAndOrder) {
        this.cancelCloseExpandEditing()
        return
      }

      let freetext_items = _.cloneDeep(this.option_list).filter((op) => op.is_free_text)
      let options = _.cloneDeep(this.option_list).filter((option) =>
        this.multi ? v.includes(genUUID(option)) : v == genUUID(option)
      )
      // pick data default property from ui
      options.forEach(async (option) => {
        if (option.type == 2) {
          Object.entries(option.variable_map).forEach(([key, obj]) => {
            option.variable_map[key] = {
              attr_item_id: obj.id,
              key: obj.key
            }
          })
        }

        if (!hasOwnProperty.call(option, 'published')) {
          option.published = -1
        }
      })
      let chosen_old = _.filter(this.data, (option) =>
        this.multi ? v.includes(genUUID(option)) : v === genUUID(option)
      )
      let attrs = _.unionBy(freetext_items, chosen_old, options, genUUID)

      // make sure options are post in the order from uitree
      attrs = this.sortOptions(attrs)

      let result = ADMIN_API.struct([
        ADMIN_API.struct.interface({
          id: 'number?',
          value: 'string',
          variable_map: 'undefined|any'
        })
      ])(attrs)

      // 在切换选项前，重置当前的数据
      const checkList = result.filter((item) => !item.is_free_text).map((item) => genUUID(item))

      this.data.forEach((item) => {
        let uuid = genUUID(item)
        if (!checkList.includes(uuid)) {
          let var_map = item.variable_map
          if (var_map) {
            Object.keys(var_map).forEach((key) => {
              if (var_map[key].values) {
                delete var_map[key].values
              }
            })
          }
        }
      })

      // 排序时根据后端接口约定，需要前端刷新数据 published 为草稿态
      if (this.reset2Unpublished) {
        this.reset2Unpublished = false
        result = result.map((item) => ({ ...item, published: 0 }))
      }

      console.info('set group by checklist', result)
      this.$emit('change', _.cloneDeep(result))
    },
    getOptionList() {
      let result
      if (!this.title.attr_value_list.length && this.title.freetext_limit) {
        result = this.data
      } else {
        if (this.autoCheckedToFront) {
          // 根据接口返回的数据和 order, 选中的排在前, 没选中的排在后
          let selected_options = this.data.map((v, i) => {
            let order = v.order !== undefined ? v.order : i
            let attr_ui = this.title.attr_value_list.find((ui) => ui.id === v.id) || {}

            // replace(拖拽、单选)完成重新计算未勾选项的 order 和 ui_index
            if (this.shouldResetUiIndexAndOrder) {
              v.ui_index = undefined
              // 因为选项置顶, 所以重写 order 时需给数据注入 ui_index
              Object.assign(v, { ui_index: i })
              // 单选情况需要对 attr_value_list 做 ui_index 初始化写入
              if (!this.multi) {
                Object.assign(attr_ui, { ui_index: i })
              }
            }

            return {
              ...v,
              ...attr_ui,
              ui_index: v.ui_index !== undefined ? v.ui_index : i,
              order
            }
          })

          let selected_ids = this.data.map((v) => v.id)
          // 用于界面选项顺序
          let uiIndex = selected_options.length
          // 如果是空数据，minOrder 为 0, 反之需要 +1
          let minOrder = Math.max(...this.data.map((v) => v.order), 0)
          if (this.data.length) {
            minOrder += 1
          }
          // 没选中的排序在选中的之后
          let unselected_options = this.title.attr_value_list
            .map((item) => {
              if (selected_ids.includes(item.id)) {
                return item
              }

              // replace(拖拽、单选)完成重新计算未勾选项的 order 和 ui_index
              if (this.shouldResetUiIndexAndOrder) {
                item.order = undefined
                item.ui_index = undefined
              }
              Object.assign(item, {
                ...item,
                order: item.order === undefined ? minOrder : item.order,
                ui_index: item.ui_index === undefined ? uiIndex : item.ui_index
              })

              uiIndex += 1
              minOrder += 1

              return item
            })
            // 先将 ui_index 写入 attr_value_list, 防止单选项被置顶的交互问题
            // 再过滤已勾选项
            .filter((v) => !selected_ids.includes(v.id))

          let arr = [...selected_options, ...unselected_options]
          result = _.sortBy(arr, 'ui_index')
        } else {
          // 非展示属性页的逻辑照旧
          result = this.title.attr_value_list.reduce((acc, curr) => {
            const arr = this.data
              .filter((item) => item.id === curr.id)
              .map((item) => {
                return {
                  ...item,
                  ...curr,
                  ui_index: item.ui_index !== undefined ? item.ui_index : curr.ui_index
                }
              })
            if (arr.length) {
              return [...acc, ...arr]
            }

            return [...acc, curr]
          }, [])

          result = _.sortBy(result, 'ui_index')
        }
      }
      this.shouldResetUiIndexAndOrder = false

      // 初始化完 order 和 ui_index 之后需要处理自动勾选的问题
      if (!this.isInitialized) {
        this.isInitialized = true
        this.initializedCallback()
      }

      return dataStruct(result)
    },
    handleSort(new_list) {
      // 更新新的 ui_index
      new_list.forEach((v, i) => (v.ui_index = i))
      let indexMap = new_list.reduce((acc, cur, idx) => {
        acc[genUUID(cur)] = idx
        return acc
      }, {})
      // 记录位置
      this.title.attr_value_list.forEach((v) => {
        if (indexMap[genUUID(v)] !== undefined) {
          v.ui_index = indexMap[genUUID(v)]
        } else {
          let key = Object.keys(indexMap).find((idx) => idx.includes(v.id))
          v.ui_index = indexMap[key]
        }
      })
      // 找出已选中的项目
      let new_data = this.data.filter(
        (item) =>
          this.checkList2Arr.includes(genUUID(item)) ||
          item.is_free_text ||
          (!this.hasInitialSort && item.deleted) // 第一次初始化排序的时候，需要把草稿删除的 prod 数据给补上
      )
      // 更新实例数据顺序
      new_data.forEach((v) => {
        v.ui_index = indexMap[genUUID(v)]
      })
      // 重排
      new_data = _.sortBy(new_data, 'ui_index')
      this.$emit(
        'change',
        new_data.map((item, index) => ({ ...item, order: index }))
      ) // 后端统一使用 order 来排序

      this.hasInitialSort = true
    },
    getOptionType(option) {
      if (option.is_free_text && this.getOptionData(option).value === undefined) {
        return 'add'
      }
      return this.optionTypeMap[option.type]
    },
    // leftCopyTimes(option) {
    //   debugger
    //   return option.dup_limit - this.data.filter(v => v.id == option.id).length;
    // },
    sortOptions(options) {
      // by the order in ui tree
      // return _.sortBy(options, (v) => this.option_list.findIndex((option) => option.id == v.id))
      // 不需要对 id 进行收集排序了
      return options
    },
    updateOptionData: _.debounce(function updateOptionData(option) {
      // let options = _.unionBy([option], this.data, genUUID);
      let options = _.cloneDeep(this.data)
      options.some((v) => {
        if (genUUID(v) === genUUID(option)) {
          v = {
            ...v,
            ...option,
            published: this.hasDraftState ? 0 : option.published
          }
        }
      })
      if (this.multi) {
        options = options.filter((v) => this.checkList.includes(genUUID(v)) || v.is_free_text)
      } else {
        options = options.reduce((acc, curr) => {
          const res = options.find((v) => this.checkList === genUUID(v))
          if (curr.is_free_text || res) {
            acc.push(curr)
          }

          return acc
        }, [])
      }

      options = options.filter((item) => item)

      let result = dataStruct(this.sortOptions(options))
      console.info('set group by option data', result)

      this.$emit('change', result)
    }, 100),
    getOptionData(option) {
      return _.find(this.data, (item) => genUUID(item) === genUUID(option)) || {}
    },
    getOptionRefData(option) {
      return (
        _.find(
          this.ref_data,
          (item) =>
            (item.instance_id !== undefined && item.instance_id === option.instance_id) ||
            (item.is_free_text && item.id === option.id) ||
            genUUID(item) === genUUID(option)
        ) || {}
      )
    },
    initCheckList() {},
    // Template 是否允许编辑, 未勾选该选项, 则该template不用编辑
    isChosen(op) {
      return op.is_free_text || this.checkList2Arr.includes(genUUID(op))
    },
    // 展示属性填写了但没有保存为草稿
    tplDataNotSaved() {
      let refOptions = this.$refs.struct_option || []

      return refOptions.some((ref) => {
        if (!this.getOptionHideStateByOpt(ref.option)) {
          return ref.tplDataNotSaved()
        }

        return false
      })
    },
    async validate() {
      let valid = true

      if (
        this.title.required &&
        (!this.option_list.every((item) => item.hide) || !this.option_list.length) &&
        !this.data.length
      ) {
        return false
      }

      let refOptions = this.$refs.struct_option || []
      for (let ref of refOptions) {
        // 隐藏不显示的不需要校验
        if (!this.getOptionHideStateByOpt(ref.option)) {
          let option_valid = await ref.validate()
          valid = valid && option_valid
        }
      }

      return valid
    },
    // 获得展示属性项是否不可见
    getOptionHideStateByOpt(option) {
      return option.hide || option.access_permission === 0
    },
    removeFreeText2Draft({ option }) {
      const response = this.deleteDraftData({ option })

      if (response) {
        this.removeFreeText(option)
      }
    },
    // 删除
    removeFreeText(option) {
      const index = this.data.findIndex((item) => genUUID(item) === genUUID(option))

      if (~index) {
        let attrs = _.cloneDeep(this.data)
        attrs.splice(index, 1)

        this.$emit('change', attrs)
      }
    },
    resetItem({ option, displayMsg = true } = {}) {
      let index = _.findLastIndex(this.data, (v) => v.id == option.id)
      let attrs = _.cloneDeep(this.data)
      attrs[index] = _.cloneDeep(option)

      this.$emit('change', attrs)

      displayMsg && this.$message.success('Successfully reset.')
    },
    deleteInvalidOptionData({ option }) {
      const result = this.data.filter((item) => !(item.id === option.id && item.deleted))

      this.$emit('change', result)
    },
    // 复制属性值(选项)
    copyItem(option_data) {
      let attrs = _.cloneDeep(this.data)
      let new_option = _.cloneDeep(option_data)

      // let { order } = this.data[index]
      new_option.ui_index = this.getNewInsertUiIndex()
      new_option.order = this.getNewInsertOrder()

      attrs.push(new_option)
      const value = genUUID(new_option)
      this.$emit('update:checkList', [...new Set([...this.checkList, value])])
      this.$emit('change', attrs)

      this.$nextTick(() => {
        this.$refs.struct_option.forEach((ref) => {
          if (ref.label === value) {
            ref.editingTemplate = true
          }
        })
      })
    },
    // 编辑
    addNew() {
      this.isAdding = true
    },
    // 确认添加
    confirmAdd(option) {
      // 用于无草稿状态参与 autoCheckedToFront 计算时
      option.ui_index = this.getNewInsertUiIndex()
      option.order = this.getNewInsertOrder()

      let attrs = _.cloneDeep(this.data)

      if (option.create) {
        // 有草稿态需要手动置为 false, 避免更新时创建新的 id
        if (this.hasDraftState) {
          option.create = false
        }
        attrs.push(option)
      } else {
        let index = attrs.findIndex((v) => v.id == option.id)
        attrs.splice(index, 1, option)
      }

      this.$emit('change', attrs)
      this.isAdding = false
    },
    confirmEdit(option) {
      let attrs = _.cloneDeep(this.data)
      let index = attrs.findIndex((v) => v.id == option.id)
      attrs.splice(index, 1, option)

      this.$emit('change', attrs)
    },
    // 取消勾选
    unselect(value) {
      const checkList = this.multi ? this.checkList2Arr.filter((item) => item !== value) : ''
      this.$emit('update:checkList', checkList)
    },
    async saveDraftData({ option }) {
      this.rootChangeLoading(true)

      let op, attr_values
      const attrVal = {
        ...option,
        order: option.order === undefined ? option.ui_index : option.order
      }
      if (this.multi) {
        op = option.published === -1 ? 'insert' : 'update'
        attr_values = [attrVal]
      } else {
        // 单选的需要补充之前的数据给到后端
        op = 'replace'
        if (option.is_free_text) {
          attr_values = [...this.data.filter((item) => item.id !== attrVal.id), attrVal]
        } else {
          // 否则需要过滤掉非 free text 的数据
          attr_values = [...this.data.filter((item) => item.is_free_text), attrVal]
        }
      }
      // 置为草稿态
      option.published = 0

      const response = await ajax.postBody(ADMIN_API.act[this.saveDraftDataUrlKey], {
        data: {
          ...this.commonDraftParams,
          // 草稿状态是更新，其余状态是插入
          op,
          groups: [
            {
              ...this.commonGroupParams,
              widgets: [
                {
                  id: this.title.id,
                  order: this.$attrs.order || 0, // title order
                  attr_values,
                  extra_info: this.$attrs.extraInfo
                }
              ]
            }
          ]
        }
      })

      !option.is_free_text && this.updateOptionData(option)

      klook.bus.$emit('refreshPreview')
      this.rootChangeLoading(false)

      return response
    },
    async deleteDraftData({ option }) {
      this.rootChangeLoading(true)
      const response = await ajax.postBody(ADMIN_API.act[this.saveDraftDataUrlKey], {
        data: {
          ...this.commonDraftParams,
          op: 'delete',
          groups: [
            {
              ...this.commonGroupParams,
              widgets: [
                {
                  id: this.title.id,
                  order: this.title.order,
                  attr_values: [option]
                }
              ]
            }
          ]
        }
      })
      this.rootChangeLoading(false)

      return response
    },
    async unselectOption2Draft({ option, label }) {
      const response = await this.deleteDraftData({ option })
      if (response !== false) {
        this.unselect(label)
        klook.bus.$emit('refreshPreview')
      }
    },
    async resetOption2Draft({ option, index }) {
      const response = await ajax.postBody(ADMIN_API.act[this.resetDraftDataUrlKey], {
        data: {
          ...this.commonDraftParams,
          groups: [
            {
              ...this.commonGroupParams,
              widgets: [
                {
                  id: this.title.id,
                  order: this.title.order,
                  attr_values: [option]
                }
              ]
            }
          ]
        }
      })

      if (response !== false) {
        this.changePublishedValue({ index, published: response ? 1 : -1 })
        this.updateOptionData(option)
        klook.bus.$emit('refreshPreview')
        this.resetItem({ option })
      }
    },
    async saveOption2Draft({ option, index, published }) {
      const res = await this.saveDraftData({ option })

      if (res) {
        this.changePublishedValue({ index, published })
        klook.bus.$emit('refreshPreview')
      }
    },
    async updateFreeText2Draft(data) {
      const res = await this.saveDraftData(data)
      res && this.confirmEdit(data.option)
    },
    async addFreeText2Draft({ option }) {
      option.published = -1
      option.ui_index = this.getNewInsertUiIndex()
      option.order = this.getNewInsertOrder()
      const res = await this.saveDraftData({ option })

      if (res) {
        const currOpt = res.result[this.groupId].widgets.find((widget) => widget.id === this.title.id)
        const data = (currOpt?.draft_attr_values ?? []).reduce((acc, curr) => {
          if (this.data.find((av) => curr.deleted || av.id === curr.id)) return acc

          return curr
        }, {})

        this.confirmAdd({
          ...option,
          ...data,
          published: 0
        })
        klook.bus.$emit('refreshPreview')
        this.$message.success('Successfully add.')
      }
    },
    async handleDragEnd() {
      if (this.hasDraftState) {
        const response = await ajax.postBody(ADMIN_API.act[this.sortDraftDataUrlKey], {
          data: {
            ...this.commonDraftParams,
            groups: [
              {
                ...this.commonGroupParams,
                widgets: [
                  {
                    id: this.title.id,
                    order: this.title.order,
                    attr_values: this.data.map((item, index) => ({
                      ...item,
                      order: index,
                      published: 0
                    }))
                  }
                ]
              }
            ]
          }
        })

        if (response) {
          const widgets = response.result?.[this.groupId]?.widgets ?? []
          const index = widgets.findIndex((item) => genUUID(this.title) === genUUID(item))
          const currAttrValues = (widgets?.[index]?.draft_attr_values ?? []).filter((item) => !item.deleted)

          // 排序之后 order 会被修改, 所以需要重新计算 checklist
          // 同时 chosen 会触发编辑态，需要阻止
          this.shouldResetUiIndexAndOrder = true
          const checkList = currAttrValues.filter((item) => !item.is_free_text).map((item) => genUUID(item))
          this.reset2Unpublished = true // 用于执行 changeCheckList 的时候，都手动填入 published 0
          this.$emit('change', currAttrValues)
          this.$emit('update:checkList', this.multi ? checkList : checkList?.[0] ?? '')

          klook.bus.$emit('refreshPreview')
        } else {
          this.shouldResetUiIndexAndOrder = true
        }
      }
    },
    async changePublishedValue({ index, published }) {
      setTimeout(() => {
        const uuid = genUUID(this.option_list[index], 'ui_index')

        this.data.forEach((data) => {
          if (genUUID(data, 'ui_index') === uuid) {
            Object.assign(data, { published })
          }
        })
      }, 60)
    },
    async getRefByOption(option) {
      await this.$nextTick()
      const refs = this.$refs?.struct_option ?? []
      let refIndex
      let ref

      refs.forEach((item, index) => {
        if (
          (option.is_free_text && item.option.id === option.id) ||
          (item.option.id === option.id && item.option.ui_index === option.ui_index)
        ) {
          refIndex = index
          ref = item
        }
      })

      return {
        ref,
        refIndex
      }
    }
  }
}
</script>

<style lang="scss">
$borderColor: #d0e6fe;
// 整体group
.compound-select {
  .ant-radio-group,
  .ant-checkbox-group {
    &:first-of-type {
      width: 100%;
    }
  }
}
// animation
.list-enter-active {
  transition: all 0.3s;
}
/** 移除过程 **/
.list-leave-active {
  transition: all 0.3s;
}
/*** 开始插入、移除结束的位置变化 ***/
.list-enter,
.list-leave-to {
  max-height: 0;
  margin-bottom: 0;
  padding: 0 15px;
  // padding: 0;
  opacity: 0;
  transform: translateY(20px);
}
// 排序图标
.compound-option__sort {
  color: #8b8b8b;
  cursor: move;
  position: relative;
  &:hover {
    transition: all 0.2s;
    color: #1079fb;
  }
  i {
    position: relative;
    width: 30px;
    top: calc(50% - 11px);
    font-size: 18px;
  }
}

.compound-extend {
  margin-top: 12px;
  &--add-new {
    width: min-content;
  }
}

.compound-options {
  display: flex;
  margin-top: 8px;

  &:first-child {
    margin-top: 0;
  }
}

.compose-li-disable .compound-option__sort {
  visibility: hidden;
}

.draft-is-deleted .compound-option-text {
  text-decoration: line-through;
  color: #757575;
}
</style>
