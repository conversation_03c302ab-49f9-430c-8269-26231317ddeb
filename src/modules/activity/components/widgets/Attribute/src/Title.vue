<template>
  <div class="outer" :class="{ 'has-error': hasError }">
    <!--<p class="header">-->
    <!--<slot />-->
    <!--</p>-->
    <transition name="fade">
      <div v-if="showError" class="error-message">{{ validateMessage }}</div>
    </transition>
    <compound-group
      v-if="active || forceDisplay"
      ref="group"
      :key="refreshCount"
      v-model="currentData"
      :title="title"
      v-bind="$attrs"
      :ref_data="ref_group_data"
      v-on="$listeners"
      @forceUpdate="forceUpdate"
    />
  </div>
</template>

<script>
import CompoundGroup from './Group.vue'
// import { errorLogs } from '@activity/utils/logquery'

export default {
  name: 'AttributeTitle',
  components: {
    'compound-group': CompoundGroup
  },
  model: {
    prop: 'data',
    event: 'change'
  },
  props: {
    active: {
      type: Boolean,
      default: true
    },
    title: {
      type: Object,
      default: () => {}
    },
    data: {
      type: Object,
      default: () => {}
    },
    // eslint-disable-next-line vue/prop-name-casing
    ref_data: { type: Object, default: () => {} }
  },
  data() {
    return {
      forceDisplay: false, // used to lazy render detail content for better perf
      showError: false,
      validateMessage: '',
      hasError: false,
      refreshCount: 0
    }
  },
  computed: {
    ref_group_data() {
      return this.ref_data.attr_values || []
    },
    currentData: {
      get() {
        return this.data.attr_values || []
      },
      set(v) {
        let dataStruct = ADMIN_API.struct({
          id: 'number',
          order: 'number?',
          attr_values: ['any']
        })

        // 临时解决：
        // this.title, 也即是父组件的ui_data: { attr_value_list: [] }, 初始化的时候是没有 ID 的，
        // 渲染会触发 set，导致 struct 会报 ID, unfinished
        // 故注释了 errorLogs 解决 log 脏数据过多的问题
        try {
          let result = dataStruct(
            _.isEmpty(this.data)
              ? {
                  id: this.title.id,
                  order: this.title.display_order,
                  attr_values: v
                }
              : {
                  id: this.title.id,
                  order: this.title.display_order,
                  ...this.data,
                  attr_values: v
                }
          )

          this.$emit('change', result)
        } catch (e) {
          // errorLogs(e, JSON.stringify(this.data) + JSON.stringify(this.title))
        }
      }
    }
  },
  watch: {
    title: {
      deep: true,
      handler(v) {}
    },
    data: {
      deep: true,
      handler() {
        this.hasError = false
      }
    }
  },
  methods: {
    tplDataNotSaved() {
      return this.$refs.group?.tplDataNotSaved?.() ?? false
    },
    // 获取数据
    async validate() {
      // need to make sure every component get rendered before validate
      this.forceDisplay = true
      // await new Promise((resolve) => this.$nextTick(resolve))
      await this.$nextTick()

      const valid = await this.$refs.group.validate()
      this.hasError = !valid
      this.$emit('validate', valid)

      return valid
    },
    async getData() {
      await this.validate()
      return this.$refs.group.getData()
    },
    forceUpdate() {
      this.refreshCount += 1
    }
  }
}
</script>
<style lang="scss" scoped>
@use 'sass:color';
$headerHeight: 30px;
$copyBtnColor: #4d99f7;
.outer {
  position: relative;
  // width: 600px;
  width: calc(100% + 26px);
  padding: 4px 12px;
  border: 1px solid transparent;
  border-radius: 4px;
  left: -13px;

  &.has-error {
    border-color: #ff4a49;
  }

  /*padding-left: 15px;*/
  .error-message {
    top: 20px;
    position: absolute;
    width: 300px;
    line-height: 20px;
    // backdrop-filter: blur(5px);
    // text-align: center;
    // background-color: #ff4a49;
    // color: #fff;
    color: #ff4a49;
    font-size: 13px;
    font-weight: 400;
    border-radius: 3px;
  }
  .header {
    font-size: 15px;
    display: flex;
    margin-top: 10px;
    &__text {
      width: 100%;
      line-height: $headerHeight;
    }
    &__btn {
      cursor: pointer;
      line-height: $headerHeight;
      color: $copyBtnColor;
      &:hover {
        color: color.scale($copyBtnColor, $lightness: 10%);
      }
    }
  }
}
</style>
