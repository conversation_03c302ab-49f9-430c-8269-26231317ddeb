<template>
  <div class="attribute-widget" :data-title-id="refAttrId">
    <Title
      v-if="active && refresh"
      :key="`${ui.id}${ui.display_order}`"
      :ref="titleRefKey"
      v-bind="$attrs"
      :title.sync="ui_data"
      :data="current_data"
      :ref_data="current_ref_data"
      :field="field"
      :group-id="groupId"
      :check-list.sync="_checkList"
      v-on="$listeners"
      @validate="(valid) => $emit('validate', valid)"
      @change="(val) => handleChange(val)"
    />
  </div>
</template>
<script>
import { genUUID } from '@activity/utils'
import Title from './Title'
import { mapActions } from 'vuex'

// Attribute 组件
export default {
  name: 'AttributeWidget',
  components: { Title },
  model: {
    prop: 'data',
    event: 'change'
  },
  provide() {
    return {
      updateText: this.updateText,
      injectComponentClassNameListByKey: this.injectComponentClassNameListByKey
    }
  },
  props: {
    // 控制是否展示
    active: {
      type: Boolean,
      default: true
    },
    // widget ui 配置
    ui: {
      type: Object,
      default: () => {}
    },
    // 当前语言属性值数据
    data: {
      type: [Array, Object],
      default: () => []
    },
    // 参考语言的属性值数据
    refData: {
      type: [Array, Object],
      default: () => []
    },
    // v-disabled 需要排除的，需要注入对应的 className
    injectClassNameOnComponent: {
      type: Object,
      default: () => ({})
    },
    field: {
      type: String,
      default: ''
    },
    groupId: {
      type: Number,
      default: 0
    },
    // 当前的 title id
    refAttrId: {
      type: Number,
      default: 0
    },
    uuid: {
      type: String,
      required: true
    },
    checkList: {
      type: [Array, String],
      required: true
    },
    required: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      current_data: {}, // 选中的数据(.attr_values)
      current_ref_data: {},
      ui_data: { attr_value_list: [] }, // title 配置和其属性值列表
      allVariables: [],

      refresh: true
    }
  },
  computed: {
    _checkList: {
      get() {
        return this.checkList
      },
      set(data) {
        this.$emit('update:checkList', data)
      }
    },
    variable_map() {
      let obj = {}
      this.allVariables.forEach((v) => {
        obj[v.id] = v
      })
      return obj
    },
    groupBindingRel() {
      return this.$store.getters['attr/bindingRelOnGroupGetter']({
        field: this.field,
        groupId: this.groupId
      })
    },
    titlesBindingRel() {
      return this.groupBindingRel?.titles ?? {}
    },
    currTitleBindingRel() {
      return this.titlesBindingRel[this.ui.ref_attr_id]
    },
    isMulti() {
      return this.ui_data.config.multiple_selection
    },
    titleRefKey() {
      return `widget_${this.ui.id}_${this.ui.display_order}`
    }
  },
  watch: {
    // 当前语言数据
    data: {
      immediate: true,
      handler(next) {
        this.setCurrentData(next)
      }
    },
    // 参考语言数据
    refData: {
      immediate: true,
      handler(next) {
        if (next) {
          let is_arry = next instanceof Array
          let attr_values = _.cloneDeep(is_arry ? next : [next])
          this.current_ref_data = _.cloneDeep({
            attr_values
          })
        }
      }
    },
    ui: {
      immediate: true,
      handler: async function () {
        await this.refreshUIData()
      }
    },
    currTitleBindingRel: {
      deep: true,
      async handler(v) {
        if (!v) return
        await this.refreshUIData()

        // 保留有效值
        const data = this.data.filter((item) => {
          return item.is_free_text || this.ui_data.attr_value_list.find((attrVal) => attrVal.id === item.id)
        })

        this.$emit('change', data)
        // 重新赋值新的 check list

        let checkList = data.map((option) => genUUID(option))
        this.$emit('update:checkList', this.isMulti ? checkList : checkList[0] || '')
      }
    }
  },
  mounted() {
    this.$emit('widget-mounted', this)
  },
  methods: {
    ...mapActions(['getActVariables2actions']),
    async refreshUIData() {
      // 待确认 ui_data 更新后没有触发刷新
      this.refresh = false

      this.$set(this, 'ui_data', await this.formatUIData(this.ui))

      this.refresh = true
    },
    setCurrentData(next) {
      let is_arry = next instanceof Array
      let attr_values = _.cloneDeep(is_arry ? next : [next]).filter((item) => item)
      this.current_data = _.cloneDeep({
        attr_values: attr_values.map((item) => {
          Object.assign(item, {
            ...(item.extendConf || {})
          })
          delete item.extendConf

          return item
        })
      })
    },
    // 转换 ui 数据
    async formatUIData(ui) {
      try {
        this.allVariables = await this.getActVariables2actions()
      } catch (e) {
        console.log(e)
      }
      //! 没有变量信息? , 请求获取
      let ui_data = _.cloneDeep(ui)
      let config =
        '[object String]' === toString.call(ui_data.config) ? JSON.parse(ui_data.config) : ui_data.config
      ui_data.config = config
      // 现根据 allow_copy 判断能否复制
      ui_data.attr_dup_limit = _.get(config, 'allow_copy') ? 1000 : 0
      // 现根据 allow_free_text 判断能否增加 freetext
      ui_data.freetext_limit = _.get(config, 'allow_free_text') ? 1000 : 0
      // 现根据 multiple_selection 判断是否多选
      ui_data.select_limit = _.get(config, 'multiple_selection') ? 1000 : 0
      // 原为 Boolean 现为 1 / 0 , 转换为 boolean
      ui_data.required = this.required || !!ui.required
      // 匹配变量
      ui_data.attr_value_list.forEach((v) => {
        if (!v) return
        //! ui_tree 只返回了每个变量的 id, 这里做匹配, 拿到变量的详情
        for (let variable in v.var_map) {
          let obj = this.variable_map[v.var_map[variable]] || {}
          v.var_map[variable] = {
            ...obj,
            key: variable,
            values: obj.attr_value_list,
            validation: obj.validation || '{}'
          }
        }

        Object.assign(v, {
          ...(v.extendConf || {})
        })
        delete v.extendConf

        v.variable_map = v.var_map
      })

      ui_data.attr_value_list = ui_data.attr_value_list.filter((v) => v.access_permission !== 0)

      return ui_data
    },
    // 更改事件
    handleChange(val) {
      // 更新数据
      let is_arry = val.attr_values instanceof Array
      let attr_values = _.cloneDeep(Array.isArray(val) ? val : is_arry ? val.attr_values : [val.attr_values])
      // 更新数据
      this.current_data = val
      this.$emit('change', attr_values)
      this.setCurrentData(attr_values)
    },
    tplDataNotSaved() {
      return this.$refs[this.titleRefKey]?.tplDataNotSaved?.() ?? false
    },
    //! 校验事件 返回 promise
    validateForm() {
      return this.$refs[this.titleRefKey]?.validate?.() ?? true
    },
    // 记录指定的文案
    updateText(option, text) {
      // 记录指定的文案
      let index = this.current_data.attr_values.findIndex(
        (v) => v.id === option.id && option.order === v.order
      )
      if (index >= 0) {
        if (this.current_data.attr_values[index].showText === text) {
          return
        }
        this.current_data.attr_values[index] = {
          ...this.current_data.attr_values[index],
          showText: text
        }
        this.$emit('change', this.current_data.attr_values)
      }
    },
    injectComponentClassNameListByKey(componentNameKey) {
      let currentComponentState = _.get(this.injectClassNameOnComponent, componentNameKey, null)

      if (currentComponentState) {
        return Array.isArray(currentComponentState) ? currentComponentState : [currentComponentState]
      }

      return []
    }
  }
}
</script>
