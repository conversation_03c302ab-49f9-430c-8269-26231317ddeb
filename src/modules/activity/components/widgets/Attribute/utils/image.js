import { isDefaultItem } from '~src/modules/aidRevamp/components/photo-and-video/utils'
import { imageDisplayHtml } from '~src/modules/activity/components/widgets/Attribute/constant'

export const formatInputImageList = (photoList = [], curLang = '') => {
  return photoList
    ?.filter((ele) => !!ele?.image?.length)
    ?.map(({ image: _image = [], locale_map = {}, ...rest }) => {
      const ORIGIN = _image?.find((ele) => ele?.size_type === 'ORIGIN') || {}
      const currentLangData = locale_map?.[curLang] || {}
      const image = _image.map((img) => ({
        ...img,
        alt: currentLangData.alt || '',
        desc: currentLangData.desc || ''
      }))
      return {
        ...rest,
        image: {
          ...ORIGIN,
          image
        }
      }
    })
}
export const formatOutputImageList = (photoList, curLang = '') => {
  return photoList?.map((photo, photoIndex) => {
    const { languages = [], image: imageObj = {}, locale_map = {} } = photo || {}
    const isDefault = isDefaultItem(photo)
    const { alt = '', desc = '', ORIGIN = {}, DESKTOP = {}, MOBILE = {} } = imageObj
    const base = {
      alt,
      banner_display: 0,
      desc,
      display_order: photoIndex + 1,
      is_default: isDefault ? 1 : 0,
      is_card: 0
    }
    const image = [
      { ...ORIGIN, ...base },
      { ...DESKTOP, ...base },
      { ...MOBILE, ...base }
    ]
    const updatedLocaleMap = {
      ...(locale_map || {}),
      [curLang]: {
        alt: alt || '',
        desc: desc || ''
      }
    }
    return {
      display_order: photoIndex + 1,
      languages,
      image,
      locale_map: updatedLocaleMap
    }
  })
}
export const formatImageDisplayHtml = (newList = []) => {
  const imageUrlList = newList?.map((ele) => {
    const { image } = ele
    return image?.[0]?.url
  })

  const display_html = newList?.length
    ? imageUrlList?.reduce((acc, imageUrl) => {
        return (
          acc +
          `<img width="100" height="100" src="${imageUrl}" style="display: inline-block; margin-right: 8px;" />`
        )
      }, '')
    : imageDisplayHtml

  return display_html
}
