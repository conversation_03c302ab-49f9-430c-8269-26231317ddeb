<template>
  <a-form-model class="markdown-widget" ref="form" :model="form" label-width="0">
    <DisabledWidgetLayer :data="value" />
    <a-form-model-item prop="value" :rules="rules">
      <simple-mde v-model="form.value" :disabled="readonly" />
      <template v-if="refData">
        <simple-mde v-model="refData.value" style="margin-top: 12px;" disabled />
      </template>
    </a-form-model-item>
  </a-form-model>
</template>

<script>
import DisabledWidgetLayer from '@activity/components/DisabledWidgetLayer.vue'
import SimpleMde from '../simple-md/index.vue'

export default {
  name: 'MarkdownWidget',
  components: {
    DisabledWidgetLayer,
    SimpleMde
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    required: {
      type: Boolean,
      default: false
    },
    config: {
      type: Object,
      default: () => ({})
    },
    refData: {
      type: Object,
      default: null
    },
    readonly: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    }
  },
  computed: {
    rules() {
      return {
        required: this.required,
        message: 'Please fill in this field.'
      }
    },
    conf() {
      return {
        allow_copy: false,
        allow_free_text: false,
        multiple_selection: false,
        ...this.config
      }
    },
    form: {
      get() {
        return Object.assign(this.value, {
          value: '',
          ...this.value
        })
      },
      set() {}
    }
  },
  methods: {
    validateForm() {
      return new Promise((resolve) => this.$refs.form.validate((valid) => resolve(valid)))
    }
  }
}
</script>
<style lang="scss" scoped>
.markdown-widget {
  position: relative;
}
</style>
