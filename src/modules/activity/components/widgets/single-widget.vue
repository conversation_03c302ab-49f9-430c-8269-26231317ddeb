<template>
  <a-form-model class="single-widget" ref="form" :model="form" label-width="0">
    <DisabledWidgetLayer :data="value" />
    <a-form-model-item prop="value" :rules="rules">
      <a-input v-model="form.value" :disabled="readonly" />
      <template v-if="refData">
        <a-input v-model="refData.value" style="margin-top: 12px;" disabled />
      </template>
    </a-form-model-item>
  </a-form-model>
</template>

<script>
import DisabledWidgetLayer from '@activity/components/DisabledWidgetLayer.vue'
import AFormModelItem from 'ant-design-vue/es/form-model/FormItem'

export default {
  name: 'SingleWidget',
  components: {
    DisabledWidgetLayer,
    AFormModelItem
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    required: {
      type: <PERSON>olean,
      default: false
    },
    config: {
      type: Object,
      default: () => ({})
    },
    refData: {
      type: Object,
      default: null
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    rules() {
      return {
        required: this.required,
        message: 'Please fill in this field.'
      }
    },
    conf() {
      return {
        allow_copy: false,
        allow_free_text: false,
        multiple_selection: false,
        ...this.config
      }
    },
    form: {
      get() {
        return Object.assign(this.value, {
          value: '',
          ...this.value
        })
      },
      set() {}
    }
  },
  methods: {
    validateForm() {
      return new Promise((resolve) => this.$refs.form.validate((valid) => resolve(valid)))
    }
  }
}
</script>
<style lang="scss" scoped>
.single-widget {
  position: relative;
}
</style>
