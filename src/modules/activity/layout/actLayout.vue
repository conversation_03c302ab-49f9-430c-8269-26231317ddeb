<template>
  <div class="act-layout">
    <div v-if="alert2fixed.show && alert2fixed.html" class="common-alert-fixed">
      <a-alert type="warning" :showIcon="true">
        <div slot="message" style="display: flex; align-items: center">
          <span v-html="alert2fixed.html"></span>
          <a-icon type="close-circle" class="close-warning" @click="closeWarning" />
        </div>
      </a-alert>
    </div>

    <div v-if="showRatePlanEntry" class="rate-plan-entry" @click="handleGoRatePlanList">
      <span class="rate-plan-entry-text">{{ $t('76450') }}</span>
      <a-tooltip class="shim-antd-tooltip" placement="bottomLeft" overlayClassName="common-tooltip-style">
        <template slot="title">
          {{ $t('76557') }}
        </template>
        <a-icon type="info-circle" />
      </a-tooltip>
    </div>

    <a-alert v-if="gtips.msg" v-bind="gtips"></a-alert>

    <div class="act-layout-box">
      <div class="act-nav-wrap">
        <actNav
          v-if="templateId"
          v-bind="actGlobal"
          @change="
            (o) => {
              routeItem = o
            }
          "
        />
      </div>
      <div class="act-main-wrap">
        <div v-if="!$route.meta.hideTopInfo" class="main-top-box" :data-spm-page="getPageSpm">
          <actInfo @calcTitle="setTitle('actTitle', $event)" v-show="isActivity" v-bind="actGlobal" />
          <pkgInfo
            @calcTitle="setTitle('pkgTitle', $event)"
            v-show="!isActivity"
            :categoryInfo="actGlobal.pkgInfo"
          />
          <!-- 埋点 -->
          <div id="glb_preview_spm" :data-spm-module="getPreviewSpm" data-spm-virtual-item="__virtual"></div>
        </div>
        <div id="main-body-box" class="main-body-box">
          <div class="main-content-wrap">
            <div v-if="isReloadShow" v-disabled="calcPageDisabled">
              <keep-alive>
                <router-view v-if="$route.meta.keepAlive" :actGlobal="actGlobal" />
              </keep-alive>
              <router-view v-if="!$route.meta.keepAlive" :actGlobal="actGlobal" />
            </div>
          </div>
          <div v-if="!$route.meta.hideTimeline" class="floor-timeline-wrap">
            <floorTimeline />
          </div>
        </div>
      </div>
    </div>
    <div v-if="!$route.meta.hideSave" class="common-btns-wrap">
      <div class="common-btns-box">
        <a-button v-if="hasEventLog" type="link" style="margin: 0" @click="handleGoEventLog">
          {{ $t('activity_event_logs') }}
        </a-button>
        <a-button v-if="showPreview" @click="handlerPreview">
          {{ $t('global_preview') }}
        </a-button>
        <a-button
          v-if="!$root.nonEdit2act && !$root.nonEdit2status"
          type="primary"
          :disabled="$root.gsaveBtnDisabled"
          @click="saveThisSection('save')"
        >
          {{ $t('global_save') }}
        </a-button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions, mapMutations, mapGetters } from 'vuex'
import actNav from '@activity/components/actNav'
import actInfo from '@activity/components/actInfo'
import pkgInfo from '@activity/components/pkgInfo'
import floorTimeline from '@activity/components/floorTimeline'
import { checkAuth } from '@/plugins/authDirective'
import ReleaseLock from '@activity/utils/release-lock'
import { ROUTE_NAME_DICT, saveTrackEventCustom } from '@activity/utils/actGTM'
import { getPreviewValiditySync } from '@activity/components/preview-validity'

const EDIT_PROTECT_INTERVAL = 120 * 1000
// import { isAdmin } from '@/env'
const tipsStackKeys = ['target_chinese_tip', 'language_name_tip']
export default {
  components: { actNav, pkgInfo, actInfo, floorTimeline },
  provide() {
    return {
      getActGlobal2provide: () => this.actGlobal,
      reloadPage2provide: () => {
        this.reloadPage()
      },
      setAlertFixed2provide: (opt) => {
        // hack, 取消 在 computed 调用时导致的关闭失效问题
        this.$nextTick(() => {
          this.$emit('updateAlert2fixed', opt)
        })
      },
      handleGTMBtn: (data) => {
        this.handleGTMBtn(data)
      }
    }
  },
  computed: {
    ...mapGetters(['isPublishWithAI', 'activityIsUnablePreview']),
    showPreview() {
      if (this.activityIsUnablePreview) {
        return false
      }
      return this.$store.state.actStepStatus.activity_basic_info
    },
    getPageSpm() {
      const name = this.$route.name
      const pageObj = {
        basicInfo: 'ActivityBasicInfo',
        detail: 'ActivityDetail',
        packageBasicInfo: 'PackageInfo',
        packageItinerary: 'packageItinerary',
        packageDetail: 'PackageDetail',
        packageUnits: 'PriceInventory',
        packageExtra: 'OtherInfo',
        packageBulkEdit: 'BulkEdit',
        notes: 'OperationalInfo',
        seo: 'SEOInfo',
        packageSchedule: 'PriceInventory',
        financial_model: 'FinancialModel'
      }
      const page = pageObj[name]
      const oid = `activity_${this.actGlobal.activity_id || 0}`
      return `${page}?oid=${oid}&trg=manual`
    },
    getPreviewSpm() {
      const oid = `activity_${this.actGlobal.activity_id || 0}`
      return `PreviewActivity?oid=${oid}&trg=manual`
    },
    ...mapState({
      isMerchant: (state) => state.isMerchant,
      pkgStepStatus: 'pkgStepStatus'
    }),
    templateId() {
      return this.$store.state.categoryInfo?.template_id ?? 0
    },
    showRatePlanEntry() {
      return !this.isMerchant && [1, 2, 3, 5].includes(this.templateId)
    },
    pagePlatformRole() {
      return klook.getPlatformRoleKey()
    },
    calcPageDisabled() {
      if (!this.routeItem) return false
      let { pageEdit } = this.routeItem
      let res = false
      let obj = { admin: 'is_am', em: 'is_em' }
      let ret
      switch (typeof pageEdit) {
        case 'boolean':
          res = !pageEdit
          break
        case 'string':
          res = !this.$root.roles[obj[pageEdit]]
          break
        case 'function':
          ret = pageEdit.call(this, this.routeItem)
          res = typeof ret === 'boolean' ? !ret : ret
          break
        default:
        //
      }
      return res
    },
    packageId() {
      return klook.parse2id(this.$route.query.package_id)
    },
    isActivity() {
      return this.$route.path.includes('/act/activity/')
    },
    hasEventLog() {
      let { name } = this.$route

      return (
        !this.pagePlatformRole === 'merchant' &&
        ['basicInfo', 'detail', 'packageBasicInfo', 'packageDetail'].includes(name) &&
        checkAuth(['actEventLogEntry'])
      )
    },
    currentMerchantActApproveStatus() {
      let key = this.$route.path.includes('/act/package/') ? 'pkgInfo' : 'actInfo'

      return _.get(this.actGlobal, `${key}.approval_status`, -1)
    },
    merchantActHaveBeenApproved() {
      if (this.isMerchant) {
        return this.currentMerchantActApproveStatus === 3
      }

      return false
    },
    activityId() {
      return klook.parse2id(this.$route.params.id)
    }
  },
  data() {
    return {
      alert2fixed: {
        show: false,
        html: ''
      },
      gtips: {
        closable: true,
        banner: true,
        type: 'info',
        msg: '',
        message: (h) => {
          let node = <span domPropsInnerHTML={this.gtips.msg}></span>
          return node
        }
      },
      apiEditTimer: null,
      routeItem: {},
      actGlobal: {
        activity_id: +this.$route.params.id,
        categoryInfo: {
          // activity_id: 35704,
          // leaf_category_id: 338,
          // sub_category_id: 1,
          // template_id: 1
        },
        categoryArr: [],
        pkgInfo: {},
        actInfo: {},
        actTitle: '',
        pkgTitle: ''
      },
      isReloadShow: true,

      customGTM: false
    }
  },
  async created() {
    klook.bus.$off('addHandleSave2bus').$on('addHandleSave2bus', this.addHandleSave2bus)
    klook.bus.$off('initQueryData2bus').$on('initQueryData2bus', this.initQueryData)
    klook.bus.$off('updatePkgInfo2bus').$on('updatePkgInfo2bus', this.getPkgData)
    klook.bus.$off('saveTips2bus').$on('saveTips2bus', this.saveTips2bus)

    await this.initQueryData()
    await this.get_published_category_info()
  },
  mounted() {
    this.setPackageId(this.packageId)
    this.startEditProtect()
    this.$on('updateAlert2fixed', this.updateAlert2fixed)

    this.$nextTick(() => {
      this.releaseLockInstance = new ReleaseLock({
        activityId: this.activityId,
        onVisible: () => {
          this.dispatchEditProtect()
          this.startEditProtect()
        },
        onRelease: this.stopEditProtect
      })
    })
  },
  beforeDestroy() {
    this.releaseLockInstance?.destroy?.()
    klook.bus
      .$off('addHandleSave2bus', this.addHandleSave2bus)
      .$off('initQueryData2bus', this.initQueryData)
      .$off('updatePkgInfo2bus', this.getPkgData)
      .$off('saveTips2bus', this.saveTips2bus)
      .$off('updateAlert2fixed', this.updateAlert2fixed)
  },
  watch: {
    merchantActHaveBeenApproved(v) {
      this.$store.commit('setMerchantActHaveBeenApproved', v)
    },
    currentMerchantActApproveStatus(v) {
      this.$store.commit('setCurrentMerchantActApproveStatus', v)
      // merchant 端活动在审核中需要全部锁定不可编辑
      this.$store.commit('setLockMerchantActEditing', this.isMerchant && v === 1)
    },
    $route: {
      immediate: true,
      deep: true,
      handler(route, oldRoute) {
        // 修复 v-disabled 拿旧状态 computed 的问题
        if (
          oldRoute &&
          (route.name === oldRoute.name || route.query.package_id !== oldRoute.query.package_id)
        ) {
          this.$store.commit('setLockMerchantActEditing', false)
        }

        this.alert2fixed.show = false
        this.$oldRoute = oldRoute
        this.saveTrackEventCustom.bind(this)({
          previousPageUrl: this.getPreviousPageUrl(oldRoute),
          event: 'virtualPageView'
        })
        tipsStackKeys.forEach((key) => {
          this.$notification.close(key)
        })
        if (!oldRoute || route.name !== oldRoute.name) {
          this.saveTips2bus()
        }
      }
    },
    '$route.path'() {
      this.checkStatusLock()
    },
    packageId(packageId, oldPkg) {
      this.setPackageId(packageId)
      this.getPkgData()
      packageId !== oldPkg && this.reloadPage() // 为空时为新建套餐
    },
    'actGlobal.categoryInfo': {
      handler(obj) {
        this.checkStatusLock()
      },
      deep: true
    },
    'actGlobal.pkgInfo': {
      handler(obj) {
        this.setPkgStatus()
        this.checkStatusLock()
      },
      deep: true
    }
  },
  methods: {
    ...mapActions(['updateActItemData2actions', 'getActPublishedCategoryInfo2action']),
    ...mapMutations(['setPackageId', 'setCurrentPackageInfo']),
    saveTrackEventCustom: saveTrackEventCustom,
    updateAlert2fixed(opt) {
      _.merge(this.alert2fixed, opt)
    },
    async dispatchEditProtect() {
      if (this.activityId) {
        let lockStatus = await ajax.get({
          url: ADMIN_API.act.edit_protect,
          params: {
            activity_id: this.activityId
          }
        })
        // 检查 lockStatus 是否为对象且具有 need_redirect 属性，同时排除 null
        const need_redirect = lockStatus && typeof lockStatus === 'object' && lockStatus.need_redirect
        if (need_redirect) {
          this.$message.error(this.$t('193016', { user_name: lockStatus.kick_manager }))
        }

        setTimeout(() => {
          // 处理 lockStatus 为 false 或需要重定向的情况
          if (lockStatus === false || need_redirect) {
            if (this.isMerchant) {
              this.$router.push({
                name: 'activityManagement'
              })
            } else {
              location.href = `${location.origin}/act/activity/list`
            }
          }
        }, 2000)
      }
    },
    startEditProtect(interval = EDIT_PROTECT_INTERVAL) {
      clearInterval(this.apiEditTimer)
      this.apiEditTimer = setInterval(this.dispatchEditProtect, interval)
    },
    stopEditProtect() {
      clearInterval(this.apiEditTimer)
    },
    handleGoRatePlanList() {
      const route = this.$router.resolve({
        name: 'ratePlanList'
        // query: {
        //   keyword: this.$route.params.id
        // }
      })

      window.open(route.href, '_blank')
    },
    closeWarning() {
      this.alert2fixed.show = false
    },
    getPreviousPageUrl(oldRoute) {
      let previousPageUrl = document.referrer || 'none'

      if (oldRoute && oldRoute.path !== '/') {
        let oldInfo = _.find(ROUTE_NAME_DICT, {
          name: oldRoute.name
        })

        previousPageUrl = `${KLK_LANG}/act/${oldInfo ? oldInfo.path : 'other_' + oldRoute.name}`
      }

      return previousPageUrl
    },
    async handleGTMBtn({
      event = 'virtualPageView',
      eventAction = 'none',
      groupId = 'none',
      isBtnSave = true,
      customPath = undefined
    } = {}) {
      await this.saveTrackEventCustom({
        event,
        groupId,
        isBtnSave,
        customPath,
        eventAction,
        previousPageUrl: this.getPreviousPageUrl(this.$oldRoute)
      })
    },
    handleGoEventLog() {
      let url = `/${window.KLK_LANG_PATH}act/activity/event_logs/${this.actGlobal.activity_id}`
      let query = {
        edit_flag: '1,2,3'
      }
      let { package_id } = this.$route.query
      if (!isNaN(package_id)) {
        query.package_id = package_id
      }
      query = new URLSearchParams(query).toString()

      window.open(`${url}?${query}`)
    },
    setTitle(key, value) {
      document.title = this.isActivity ? this.actGlobal.actTitle : this.actGlobal.pkgTitle
      this.$set(this.actGlobal, key, value)
    },
    setPkgStatus() {
      const { pkgInfo } = this.actGlobal || {}
      const { approval_status } = pkgInfo || {}
      this.$store.commit('setPkgPendingApproval', approval_status === 1)
      this.$store.commit('setPkgApproved', approval_status === 3)
    },
    checkStatusLock() {
      // let obj = this.isActivity ? this.actGlobal.categoryInfo : this.actGlobal.pkgInfo
      // 目前兼容了活动和套餐状态
      if (
        this.pagePlatformRole === 'bd_audit'
        // (checkAuth('edit') && [1, 3].includes(obj.approval_status)) // 1:pendingApproval  3:approved
      ) {
        // 全局锁优先级大于状态锁
        this.$root.nonEdit2status = true
      } else {
        this.$root.nonEdit2status = !(checkAuth('edit') || checkAuth('draftMerchantActCreate'))
      }
    },
    reloadPage() {
      this.isReloadShow = false
      this.$nextTick(() => {
        this.isReloadShow = true
      })
    },
    addHandleSave2bus(callback = () => {}, { customGTM = false } = {}) {
      this.handleSave2bus = callback
      this.customGTM = customGTM
    },
    async initQueryData() {
      let { actGlobal } = this
      let obj =
        (await this.updateActItemData2actions({
          activity_id: actGlobal.activity_id,
          refresh: true
        })) || {}

      this.checkRoute(obj)
      this.$set(actGlobal, 'actInfo', _.merge(obj))
      this.$set(actGlobal, 'categoryInfo', _.merge(obj, obj.category))
      this.getPkgData()
      this.saveTips2bus()
    },
    async getPkgData() {
      let { actGlobal, packageId } = this
      if (packageId) {
        let result =
          (await ajax.get(ADMIN_API.act.get_floating_fields2pkg, {
            params: {
              package_id: this.$route.query.package_id,
              page_from: klook.getPlatformRoleKey(),
              language: klook.getEditLang()
            }
          })) || {}
        let obj = result || {}
        this.$store.commit('setCurrentPackageFloatingField', result)
        this.$store.commit('setPackageFloatingFieldMap', result)
        this.$set(actGlobal, 'pkgInfo', _.merge(obj, obj.category))
        this.setCurrentPackageInfo(obj)
      } else {
        this.$store.commit('setCurrentPackageFloatingField', {})
        this.$set(actGlobal, 'pkgInfo', {
          package_id: actGlobal.categoryInfo.package_id,
          package_title: actGlobal.categoryInfo.package_title,
          package_status: actGlobal.categoryInfo.package_status,
          approval_status: 0,
          ...actGlobal.categoryInfo.category
        })
      }
    },
    checkRoute(obj) {
      if (!this.$store.state.isMC2BD) return
      let { query, path } = this.$route
      let newQuery = _.merge({}, query)
      if (obj.language !== newQuery.lang) {
        newQuery.lang = obj.language || newQuery.lang
        this.$router.replace({
          path,
          query: newQuery
        })
      }
    },
    saveThisSection(...args) {
      let callback = this.handleSave2bus
      if (typeof callback === 'function') {
        callback(...args)

        !this.customGTM && this.handleGTMBtn()
      }
    },
    async get_published_category_info() {
      let { actGlobal } = this
      let result = await this.getActPublishedCategoryInfo2action()
      if (!result) return
      actGlobal.categoryArr = result
    },
    handlerPreview() {
      getPreviewValiditySync(this.$route.params.id, this.$i18n, (date) => {
        if (date) {
          let preview_url = klook.getUrlByEnv('')
          let activity_id = this.$route.params.id
          let language = this.$route.query.lang
          let url = `${preview_url}/en-US/preview/activity/${activity_id}/?lang=${language}&deadline_version=1&deadline=${encodeURIComponent(
            date
          )}`
          window.open(url)
          this.$tracker.track('action', '#glb_preview_spm')
        }
      })
    },

    async saveTips2bus() {
      let res = this.checkSaveTips({
        name: this.$route.name,
        params: {
          activity_id: klook.parse2id(this.$route.params.id),
          source_language: klook.getEditLang()
        }
      })
      if (!res) {
        this.gtips.msg = ''
        return false
      }
      this.$nextTick(() => {
        this.debouceShowTips(res || {})
      })
    },
    debouceShowTips: _.debounce(function ({ can_clone_chinese, domestic_chinese, auto_translate_message }) {
      let msg = ''
      if (can_clone_chinese && can_clone_chinese.length) {
        let str = `${can_clone_chinese.map((k) => klook.getLangTitle(k)).join(', ')}`
        str = klook.parseStr1(__('29502'), {
          target_Chinese: str // 测试问题target_Chinese有大写
        })
        msg += `<span style="color: #52c41a;">${str} </span>`
      }
      if (domestic_chinese && domestic_chinese.length) {
        // 防止页面首次加载位置重叠
        let str = `${domestic_chinese.map((k) => klook.getLangTitle(k)).join(', ')}`
        str = klook.parseStr1(__('29503'), {
          language_name: str
        })
        msg += `<span style="color: #faad14;">${str}</span>`
      }
      if (auto_translate_message) {
        msg = auto_translate_message
      }
      this.gtips.msg = msg
    }, 1000),
    checkSaveTips({ name, params }) {
      // let lang = params.source_language
      // if (
      //   (this.$store.state.isMC2BD || !['zh_CN', 'zh_TW', 'zh_HK'].includes(lang)) &&
      //   !this.isPublishWithAI
      // ) {
      //   return false
      // }
      //Activity Basic Info, Activity Detail, SEO, Package Info, Package Detail, Timeslot&Inventory（有个Unit name), Other Info
      let names = [
        'basicInfo',
        'detail',
        'seo',
        'packageBasicInfo',
        'packageDetail',
        'packageSchedule',
        'packageExtra',
        'packageItinerary'
      ]
      if (!names.includes(name)) return false
      if (this._apiData2tips) return this._apiData2tips
      this._apiData2tips = this.actGlobal.actInfo.auto_translate_info
      return this._apiData2tips
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../assets/css/lib/common.scss';

$padding: 8px;

.act-layout {
  .act-layout-box {
    position: relative;
    display: flex;
    background: #fafafa;
    padding: $padding;
  }
  .act-nav-wrap {
    display: inline-block;
    width: 280px;
    box-sizing: border-box;
    vertical-align: top;
  }
  .act-main-wrap {
    position: relative;
    display: inline-block;
    margin-left: 12px;
    width: calc(100vw - 312px - #{2 * $padding}); // padding
    box-sizing: border-box;
    vertical-align: top;
    padding-right: 4px;
    overflow: hidden;
  }
  .main-body-box {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
    margin: 12px 0 0 0;
  }
  .main-content-wrap {
    width: 100%;
    vertical-align: top;
    box-sizing: border-box;
    flex: 1 1 auto;
    z-index: 1;
  }
  .floor-timeline-wrap {
    position: relative;
    left: 10px;
    width: 294px;
    vertical-align: top;
    box-sizing: border-box;
  }
}
</style>

<style lang="scss">
.close-warning {
  margin-left: 12px;
  font-size: 13px;
  cursor: pointer;
}

.rate-plan-entry {
  // margin-bottom: 12px;
  padding: 4px 30px;
  color: #437dff;
  cursor: pointer;

  &-text {
    display: inline-block;
    margin-right: 6px;
  }
}
</style>
