export default {
  // old admin defined start
  act_photo_add_banner:
    'The amount of activity banner displayed on frontend has changed, suggest to re-crop images uploaded before. (Banner size on deskdop will change from 1160*460 to 960*460)',
  act_photo_less_banner:
    'The amount of activity banner displayed on frontend has changed, suggest to re-crop images uploaded before. (Banner size on deskdop will change from 960*460 to 1160*460)',

  js_confirm_delete: 'Confirm delete？',
  act_image_en_us_handle:
    'Image can be added, deleted at the only language or en_US.  Deleting/adding image applies to all languages',
  act_tips_saving_all_mandatory: 'You’ve completed saving all mandatory fields',
  act_fields_completed_saved: '{0} mandatory fields to be completed and saved',
  act_days: 'Day(s)',
  act_hours: 'Hour(s)',
  act_minutes: 'Minute(s)',
  global_images: '图片',
  global_show_msg_save_success: 'Save success!',
  global_location: 'Save coordinates',
  global_place_id: 'Save Google Place ID',
  global_tips: 'Tips',
  global_language: 'Language',
  global_success: 'Success',
  global_failure: 'Failure',
  global_dialog_title: 'Note',
  no_size_limit: 'Set up the banner without cutting the original size',
  display_on_banner: 'Add image to banner',
  act_photo_desc_hint: 'Enter Photo description',
  global_reupload: 'Reupload',

  taxonomy_filter_admin_region: 'Admin Region',
  taxonomy_filter_admin_region_placeholder:
    'Search with Admin Region or ID of key location',
  taxonomy_filter_parent_region: 'Parent Region',
  taxonomy_filter_parent: 'Parent',
  taxonomy_filter_city: 'City',
  taxonomy_filter_all: 'All',
  taxonomy_filter_city_yes: 'Yes',
  taxonomy_filter_city_no: 'No',
  taxonomy_filter_status: 'Status',
  taxonomy_filter_available: 'Available',
  taxonomy_filter_unavailable: 'Unavailable',
  taxonomy_filter: 'Filter',
  taxonomy_query: 'Query',
  taxonomy_reset: 'Reset',
  taxonomy_select: 'select',
  taxonomy_add: 'Select',
  taxonomy_create_admin_region: 'Create Admin Region',
  taxonomy_list_country_code_3: 'Country Code alpha-3',
  taxonomy_list_actions: 'Actions',
  taxonomy_alert_title: 'Caution!',
  taxonomy_alert_content:
    'EN-US is the default language for creating Admin Region. Rest of the languages could be edited afterward.',
  taxonomy_dialog_title:
    'EN-US is the default language for creating Admin Region.',
  taxonomy_dialog_name: 'Name',
  taxonomy_dialog_parent_region: 'Parent Region',
  taxonomy_dialog_parent_region_placeholder: 'Please input keyword and search',
  taxonomy_dialog_country_code_3: 'Country Code alpha-3',
  taxonomy_dialog_country_code_2: 'Country Code alpha-2',
  taxonomy_dialog_currency: 'Currency',
  taxonomy_dialog_city: 'City',
  taxonomy_dialog_status: 'Status',
  taxonomy_dialog_nicknames: 'Nicknames',
  taxonomy_dialog_nicknames_placeholder:
    'Keywords could be segmented by inputting commas.',
  taxonomy_dialog_coordinates: 'Coordinates',
  taxonomy_dialog_Search: 'Search',
  taxonomy_dialog_time_zone: 'Time Zone',
  taxonomy_dialog_longitude: 'Longitude',
  taxonomy_dialog_latitude: 'Latitude',
  taxonomy_root_menu: 'Root Menu',
  taxonomy_edit_dialog_title: 'Edit Admin Region',
  taxonomy_region_name: 'Region Name',
  taxonomy_region_type: 'Region Type',
  taxonomy_country_level: 'Country Level',
  taxonomy_city_level: 'City Level',
  taxonomy_region_id: 'Region ID',
  taxonomy_belong_county: 'Country',
  taxonomy_sub_regions: 'Sub-regions',
  taxonomy_primary_sub_regions: 'Primary Sub-region',
  taxonomy_multilingual_versions: 'Multilingual Versions',
  taxonomy_back_region_list: 'Back to Customized Region List',
  taxonomy_add_sub_regions: 'Select Sub-region',
  taxonomy_synonyms: 'Synonyms',
  taxonomy_admin_region_name: 'Admin Region Name',
  taxonomy_selected_sub_regions: 'Selected Sub-region',
  taxonomy_confirm_selected: 'Confirm Selected Items',
  taxonomy_manage_placeholder: 'Search with name or ID of customized region',
  taxonomy_create_region: 'Create Customized Region',
  taxonomy_status: 'Status',
  taxonomy_poi_name: 'Name',
  taxonomy_scenic_spot: 'Sights & Landmarks',
  taxonomy_airport: 'Airport',
  taxonomy_railway_station: 'Railway Station',
  taxonomy_bus_station: 'Bus Station',
  taxonomy_ferry: 'Ferry',
  taxonomy_mall: 'Shopping Mall',
  taxonomy_theme_park: 'Theme parks',
  taxonomy_nature: 'Nature & Parks',
  taxonomy_dining_room: 'Restaurants',
  taxonomy_hotel: 'Hotels',
  taxonomy_higher_level: 'Prior than city',
  taxonomy_belong_city: 'City',
  taxonomy_belong_country: 'Country',
  taxonomy_create_key_location: 'Create Key Location',
  taxonomy_create_location_name: 'Name',
  taxonomy_confirm_key_location:
    'The default language for key location is en-US.',
  taxonomy_full_address: 'Full Address',
  taxonomy_type: 'type',
  taxonomy_nicknames: 'Nicknames',
  taxonomy_created_by: 'Created by',
  taxonomy_poi_manage_placeholder: 'Search with name or ID of key location',
  taxonomy_dest_version_read: 'Read',
  taxonomy_dest_version_status_draft: 'Draft',
  taxonomy_dest_version_status_release: 'Scheduled',
  taxonomy_dest_version_status_publish: 'Published',
  taxonomy_dest_version_status_down: 'Unpublished',
  taxonomy_dest_version_title: 'Version Title',
  taxonomy_dest_version_status: 'Status',
  taxonomy_dest_edit_lang: 'Language',
  taxonomy_dest_version_search: 'Search',
  taxonomy_dest_version_create: 'Create Version',
  taxonomy_dest_version_create_time: 'Create Time',
  taxonomy_dest_version_creator: 'Creator',
  taxonomy_dest_version_last_modify_time: 'Last Edit Time',
  taxonomy_dest_version_last_modifier: 'Last Editor',
  taxonomy_dest_version_publish_time: 'Time of Publishment',
  taxonomy_dest_version_operation: 'Operation',
  taxonomy_dest_edit: 'Edit',
  taxonomy_dest_version_publish: 'Publish',
  taxonomy_dest_copy: 'Copy Version',
  taxonomy_dest_cancel_appoint: 'Cancel Schedule',
  taxonomy_dest_version_title_tip:
    'This title is only for searching, not for front-end display.',
  taxonomy_dest_select_placeholder: 'Select Language',
  taxonomy_dest_confirm: 'Confirm',
  taxonomy_dest_choose_lang_tip:
    'Language could not be changed after version saved, please confirm the language you selected before saving.',
  taxonomy_dest_add_items: 'Add Item',
  taxonomy_dest_item_type: 'Item Type',
  taxonomy_dest_type_dest: 'Destination Page',
  taxonomy_dest_type_define: 'Customized URL',
  taxonomy_dest_url_valid_f: 'Current edit language is ',
  taxonomy_dest_url_valid_s: '.URL should starts with https://www.klook.com',
  taxonomy_dest_url_valid_t: '/...',
  taxonomy_dest_item_content: 'Item Content',
  taxonomy_dest_item_title: 'Item Title',
  taxonomy_dest_item_image: 'Image',
  taxonomy_dest_delete: 'Delete',
  taxonomy_dest_version_appointment_confirm:
    'Are you sure to schedule the publishment of this version?',
  taxonomy_dest_version_publish_cfm:
    'Are you sure to publish this version immediately?',
  taxonomy_dest_version_publish_msg:
    'Version copied. Please review the content of the version to make sure every item is correct.',
  taxonomy_dest_version_cancel_publish_cfm:
    'Are you sure to cancel the schedule of this version?',
  taxonomy_dest_add_first_level: 'Create L1 Item',
  taxonomy_dest_order: 'Order',
  taxonomy_dest_first_menu_title: 'L1 Item Title',
  taxonomy_dest_pop_area: 'Popular Destination',
  taxonomy_dest_edit_submenu: 'Edit Sub-menu',
  taxonomy_dest_read_submenu: 'Read Sub-menu',
  taxonomy_dest_add_second_level: 'Create L2 Item',
  taxonomy_dest_area_type: 'Destination Page Type',
  taxonomy_dest_area_country: 'Country Page',
  taxonomy_dest_area_city: 'City Page',
  taxonomy_dest_add_third_level: 'Create L3 Item',
  taxonomy_dest_third_level_title: 'L3 Item Title',
  taxonomy_dest_delete_tips: 'Are you sure you want to delete this item？',
  taxonomy_dest_second_menu_title: 'L2 Item Title',
  taxonomy_destination_page_management: 'Destination Page Management',
  taxonomy_destination_page_template: 'Page Template',
  taxonomy_destination_type: 'Destination Type',
  taxonomy_destination_destination: 'Destination',
  taxonomy_destination_page_title: 'Page Title',
  taxonomy_destination_languages: 'Language',
  taxonomy_destination_page_id: 'Page ID',
  taxonomy_destination_time_of_generation: 'Time of Generation',
  taxonomy_destination_operation: 'Operation',
  taxonomy_destination_publish_act_num: '# of Activities',
  taxonomy_destination_publish_or_unpublish: 'Publish / Unpublish',
  taxonomy_destination_status: 'Status',
  taxonomy_destination_unpublished: 'Unpublished',
  taxonomy_destination_back: 'Back to Page List',
  taxonomy_destination_current_language: 'Current Language',
  taxonomy_destination_info: 'Destination Info',
  taxonomy_destination_basic_info: 'Destination Basic Info',
  taxonomy_destination_title: 'Destination Title',
  taxonomy_destination_coordinates: 'Coordinates',
  taxonomy_destination_time_zone: 'Time Zone',
  taxonomy_destination_currency: 'currency',
  taxonomy_destination_official_language: 'Official Language',
  taxonomy_destination_video: 'Video',
  taxonomy_destination_upload_video: 'Upload video',
  taxonomy_destination_images: 'Images',
  taxonomy_destination_upload_images: 'Upload Images',
  taxonomy_destination_description: 'Destination Description',
  taxonomy_destination_base_time_to_visit: 'Best Time to Visit',
  taxonomy_destination_seasonal_weather: 'Seasonal Weather',
  taxonomy_destination_single: 'Single',
  taxonomy_destination_range: 'Range',
  taxonomy_destination_edit_image: 'Edit Image',
  taxonomy_destination_image_size: 'Image Size',
  taxonomy_destination_applications: 'Applications',
  taxonomy_destination_ratio: 'Ratio',
  taxonomy_destination_pixel: 'size',
  taxonomy_destination_all_destination: 'All Destination',
  taxonomy_destination_tips:
    'Tips:<br>1.All destinations will be displayed in country page and sorted with descfending order by the number of published of activities by default.<br>2. If you want to pin some of the cities to the top, press the Pin button. The ranking of pinned cities could be adjusted by drag and drop.<br>3. If you want to adjust the ranking of all city, press pin all and edit the ranking of them by drag and drop.',
  taxonomy_destination_of_activities: '# Of Activities',
  taxonomy_destination_pin_all: 'Pin All',
  taxonomy_destination_cancel_pinning_all: 'Cancel Pinning All',
  taxonomy_destination_ranking: 'Ranking',
  taxonomy_destination_pinned_act: 'Pinned Activities',
  taxonomy_destination_pinned_category: 'Pinned Category',
  taxonomy_destination_unpinned_category: 'Unpinned Category',
  taxonomy_destination_pinned_destination: 'Pinned Destination',
  taxonomy_destination_unpinned_destination: 'Unpinned Destination',
  taxonomy_destination_click_to_edit: 'Click To Edit',
  taxonomy_destination_category_navigation: 'Category Navigation',
  taxonomy_destination_popular_activities: 'Popular Activities',
  taxonomy_destination_explore_city: 'Explore City',
  taxonomy_destination_keywords: 'Destination Keywords',
  taxonomy_destination_seo_url: 'SEO URL',
  taxonomy_destination_seo_title: 'SEO Title',
  taxonomy_destination_seo_keywords: 'SEO Keywords',
  taxonomy_destination_seo_meta: 'SEO Meta Description',
  taxonomy_destination_old_images_tips:
    'The current destination page is dopting the images uploaded before destination page mgmt module launch. Click Upload Image to upload, crop or refresh images.',
  taxonomy_destination_admin_region: 'Admin Region',
  taxonomy_destination_key_location: 'Key Location',
  taxonomy_destination_customized_region: 'Customized Region',
  // old admin defined end

  // global start
  router: {
    activityManager: 'Activity Manager'
  },
  common: {
    perPage: 'Per page',
    pieces: 'Pieces'
  },
  status: {
    unpublished: 'Unpublished',
    published: 'Published',
    pendingApproval: 'Pending Approval',
    archived: 'Archived',
    unarchived: 'Unarchived',
    rejected: 'Rejected',
    approved: 'Approved',
    edit: '--'
  },
  label: {
    from: 'From',
    to: 'To',
    noOnly: 'No. only',
    range: 'Range',
    addressDecription: 'Address Description',
    departureCountry: 'Departure Country Or Region',
    departureCity: 'Departure City',
    destinationCountry: 'Destination Country Or Region',
    destinationCity: 'Destination City',
    pkgKeyword: 'Package ID-Title',
    actKeyword: 'Activity ID-Title',
    keyword: 'Key Word',
    city: 'City',
    leafCategory: 'Leaf Category',
    activityStatus: 'Activity Status',
    archive: 'Archive',
    packageStatus: 'Package Status',
    approvalStatus: 'Approval Status',
    locationCoordinates: 'Location Coordinates',
    activityAddress: 'Activity Address',
    destination: 'Destination',
    countryOrRegion: 'Country or Region',
    cityName: 'City Name',
    publishConditions: 'Publish conditions',
    languageAvailability: 'Language availability',
    allLanguages: 'All languages',
    selectedLanguages: 'Selected languages',
    selectAll: 'Select All',
    clearAll: 'Clear All'
  },
  form: {
    keyword: 'Key Word',
    select: 'Please Select',
    input: 'Please Input',
    stkTextarea: 'Please input the modification history remarks',
    gpsLocation: 'Please insert name of the location to search on Google Map',
    typeToSearch: 'Type to search'
  },
  btn: {
    foldAll: 'Fold All',
    unfoldAll: 'Unfold All',
    copyPhotos: 'Copy from existing photos',
    addNew: 'Add New',
    search: 'Search',
    reset: 'Reset',
    createAct: 'Create New Activity',
    createPkg: 'Create New Package',
    recentUsed: 'Recent Used',
    cancel: 'Cancel',
    save: 'Save',
    preview: 'Preview',
    view: 'View',
    edit: 'Edit',
    more: '...',
    archive: 'Archive',
    unarchive: 'Unarchive',
    STK: 'Submit to Klook',
    VAP: 'View Activity Page',
    delete: 'Delete',
    confirm: 'Confirm',
    okay: 'Okay',
    submitPackage: 'Submit Package',
    groupSummary: 'Group Summary',
    saveThisSection: 'Save this Section',
    query: 'Query',
    submit: 'Submit'
  },
  // global end
  /* ------ pretty line ------ */
  /* eslint-disable quotes */
  // page start
  detail: {
    groupList: 'Group List',
    nextStep:
      'Save the edits to this group, complete saving all groups to proceed to next step.',
    groupSummary: 'Group Summary',
    imageLanguage:
      'Image can be added, deleted at the only language or en_US.  Deleting/adding image applies to all languages',
    saveEditsMade: 'Please save the edits that you made',
    viewWhatSelected: 'View What You’ve Selected',
    refLanguage: 'Ref. Language',
    completeAllGroups:
      'Please complete all groups to proceed, to complete all groups you need to complete all mandatory fields.'
  },
  basic: {
    image_change_dft_hint:
      "Confirm if you wanna change the setting? After change, can't upload the custom photos for this language",
    image_change_custom_hint:
      "Confirm if you wanna change the setting? After change, can upload the custom photos for this language and doesn't affect other language's photos",
    bannerTips: 'At least 4 photos are required to upload for this activity',
    languagesTip:
      'Languages that you would like/allow the activity to be translated into',
    basicInfo: 'Basic Info',
    bannerPhoto: 'Banner Photo',
    activityPhoto: 'Activity Photo',
    video: 'Video',
    notCity: "Can't find the city you need?",
    clickHere: 'Click here',
    toRequest: 'to submit request to administrator.',
    baseEnLang: 'Please select American English as base English language',
    cityRequest: 'City Request',
    blocklistNote:
      'Note: If the activity cannot be published in all languages, please select "Selected languages" and untick any unnecessary languages',
    photoForLang: 'Upload activity photo for this language only',
    photoAddBanner:
      'The amount of activity banner displayed on frontend has changed, suggest to re-crop images uploaded before. (Banner size on deskdop will change from 1160*460 to 960*460)'
  },
  actNav: {
    actTitle: 'Activity ID-Title',
    pkgTitle: 'Package Title',
    activity: 'Activity',
    package: 'Package',
    basicInfo: 'Basic Info',
    activityDetail: 'Activity Detail',
    packageInfo: 'Package Info',
    packageDetails: 'Package Details',
    sellingInfo: 'Selling Info ',
    extraInfomation: 'Extra Information',
    regText: 'Sky Terrace {{number}} Admission'
  },
  actManager: {
    tabActivity: 'Activity',
    tabPackage: 'Package',
    actColumns: {
      package_id: 'Package ID - Title',
      activity_id: 'Activity ID-Title',
      pkgCreated: 'Package(s) Created',
      city_name: 'Destination City',
      category_name: 'Category',
      sub_category_name: 'Sub category',
      leaf_category_name: 'Leaf Category',
      create_time_bj: 'Created time(UTC+8)',
      activity_status: 'Activity Status',
      package_status: 'Package Status',
      approval_status: 'Approval Status',
      package_archived: 'If Archived',
      manage: 'Manage'
    },
    eventLogTitle: 'Event Log',
    logColumns: {
      timestamp_utc: 'Timestamp',
      performed_by: 'Performed By',
      before_status: 'Before',
      after_status: 'After',
      note: 'Note'
    }
  },
  pkgExtra: {
    standardizedTipsActComboTemporarily:
      "Identified SKU under this package is the standalone SKU to combo package {0} of activity {1}, thus the following standardized extra info can't be configured at Combo package temporarily.",
    tabPackage: 'Package',
    tableActivity: {
      id: 'Activity ID-Title',
      pkgCreated: 'Package(s) Created',
      desCity: 'Destination City',
      category: 'Category',
      subCategory: 'Sub Category',
      leafCategory: 'Leaf Category',
      creteadTime: 'Created time',
      activityStatus: 'Activity Status',
      approvalStatus: 'Approval Status',
      manage: 'Manage'
    }
  },
  actCreate: {
    modalTitle: 'Choose Sub-category &  Leaf-category',
    category: 'Category',
    subLeafCategory: 'Sub-category &  Leaf-category',
    subCategory: 'Sub-category',
    leafCategory: 'Leaf-category',
    categoryFlow: 'Check the activities associated with the same category flow'
  },
  stkModal: {
    title: 'Please input the modification history remarks',
    content:
      'After Submit to Klook, it can be whithdraw if it has not been approved.'
  },
  // page end
  attribute: {
    add_free_text_hint: 'Welcome to ADMIN platform',
    add_free_text_disabled: 'Welcome to ADMIN platform'
  },
  blockList: {
    pleaseConfirm:
      'Please confirm that you want to publish in these languages:',
    systemWillNotify:
      'The system will notify localization teams to restart the onboarding process',
    notPublish:
      'Please confirm that you do not want to publish in these languages:',
    willNotPublish:
      'If the activity is already published in any of these languages, it will be automatically unpublished',
    atLeastOne:
      'Every activity needs publication in at least 1 language. If you want to remove the activity, change the activity status to "Unpublished"'
  },

  // other-info待补充的多语言
  other_info_submit_finished: 'The Package Extra is finished now !',
  other_info_copy_no_data: 'There is no package that has extra info to copy!',
  other_info_non_standardized_warning:
    'If the below selections is not enough to describe your products, please contact your klook point of contact and let them help you.',

  // other-info迁移多语言
  global_copy: 'Copy',
  global_yes: 'Yes',
  global_no: 'No',
  global_select: 'Please choose',
  global_bts_table_formatNoMatches: 'No match is found',
  global_copy_success: 'Copy Success',
  global_copy_error: 'Copy Error',
  global_delete_success: 'Delete Success',
  global_delete_error: 'Delete Error',
  global_create_success: 'Created Success',
  global_create_error: 'Created Error',
  global_modify_success: 'Modified Success',
  global_modify_error: 'Modified Error',
  global_button_ok: 'Ok',
  global_button_cancel: 'Cancel',
  copy_package: 'Copy contents to other packages',
  unfinish_field: 'Unfinish Field',
  main_package: 'Main Package',
  content_name: 'Name',
  act_save_success: 'Save Success',
  act_save_fail: 'Save Fail',
  js_post_success: 'Success',
  type_to_search: 'Type to search',
  package_price_data_error: 'Data error',
  can_not_be_empty: 'can not be empty',
  package_xinfo_finish_hint: "There's still unfinished main packages.",
  package_xinfo_hint:
    'If you cannot find the field you are looking for, scroll down to create a custom field',
  packge_xinfo_fields: 'Extra Info Fields',
  package_xinfo_custom_fields: 'Add customized user info (Non-standardized)',
  package_xinfo_preview_fields: 'Preview Fields',
  package_xinfo_modify: 'Modify',
  package_xinfo_dropdown_list: 'Dropdown list',
  package_xinfo_text_box: 'Text Box',
  package_xinfo_date: 'Date',
  package_xinfo_time: 'Time',
  package_xinfo_before_today: 'Any date before this day can be selected',
  package_xinfo_after_today:
    'Any date after and including this day can be selected',
  package_ask_once_per_booking: 'Ask Once Per Booking',
  package_ask_for_each_participants: 'Ask For Each Participant',
  package_xinfo_mandatory_hint: 'Is this Extra Info mandatory?',
  package_xinfo_another: 'Add Another Custom Field',
  package_xinfo_sort_hint:
    'The Extra Info You Choose / Create (can change display order)',
  package_another_main_package: 'Create Another Package',
  package_another_addon_package: 'Create Another Add-On Package',
  package_take_overview: 'Submit & Check Overview',
  pacage_another_main_hint: 'If you have more than one package option',
  package_another_addon_hint: 'If you want to create another add-on package',
  package_take_overview_hint:
    'If you have finished creating packages and have no add-on packages',
  package_info_location_hint:
    'This will be shown on the voucher to explain the location (png, jpg, jpeg; max. size 4MB)',
  package_info_name_placeholder: 'Enter package title',
  package_info_subtitle_placeholder: 'Enter package Subtitle',
  package_info_description_placeholder: 'Enter package description',
  package_xinfo_name_placeholder: 'Please enter extra info name',
  package_xinfo_hint_placeholder: 'Please enter extra info hint',
  package_xinfo_hint_input: 'Extra Info Hint',
  package_xinfo_dropdown_placeholder:
    'for lists, enter "option 1,option 2,option 3" as options (without the quote marks), the hint tells customers what to do e.g. please select',
  package_xinfo_checkbox_placeholder:
    'Please enter the Terms & Conditions (if applicable)',
  package_xinfo_copy: 'Copy from Existing Extra Info',
  package_xinfo_type: 'Extra Info Type',
  package_xinfo_name: 'Extra Info Name',
  package_xinfo_repeat_rules: 'Repeat Rules',
  package_xinfo_mandatory: 'Mandatory',
  package_xinfo_enter__name: 'Please enter any additional information ',
  package_xinfo_enter_hint: 'Please enter extra info hint ',
  package_xinfo_ask_once: 'Ask Once Per Booking',
  package_xinfo_ask_each: 'Ask For Each Participant',
  package_xinfo_checkbox: 'Tick Box',
  package_xinfo_choose_repeat_rule: 'Choose The Repeat Rules',
  package_xinfo_refresh_content: 'Refresh Content',
  package_info_add_package: 'Add Package',
  common_field_personal_information: 'Personal Information',
  common_field_pick_up_detail: 'Pick Up Details',
  common_field_car_transfer_detail: 'Car Transfer Details',
  common_field_hotel_information: 'Hotel Details',
  common_field_wifi_and_sim: 'WiFi & SIM Cards',
  common_field_language_and_others: 'Language & Other Preferences',
  common_field_diver_details: 'Diver Details',
  common_field_mailing_information: 'Mailing Information',
  common_field_personal_measurement: 'Personal Measurements',
  other_info_standardized: 'User Input info (Standardized)',
  other_info_contact: 'Contact info',
  other_info_following_info_collected:
    'The following info is collected automatically:',
  other_info_required_participants: 'Required Info from participants',
  other_info_required: 'Info required: ',
  other_info_less_likely_warn:
    "Users are less likely to book if they need to enter more info than is needed. Please only select what's necessary.",
  other_info_additional: 'General other info',
  other_info_less_likely_warn_2:
    "Users are less likely to book if they need to enter more info than is needed. Please only select what's necessary.",
  other_info_last_name: 'Last name',
  other_info_first_name: 'First name',
  other_info_country_region: 'Country/region',
  other_info_phone_number: 'Phone number',
  other_info_email: 'Email',
  other_info_no_required: 'No info required (Recommended)',
  other_info_1_participant: 'Info from 1 participant required',
  other_info_all_participant: 'Info from all participants required',
  other_info_validate_warn_duplicate: 'Error: Duplicate content.',
  other_info_non_standardized: 'Additional user info (Non-standardized)',
  other_info_all_selected_displayed:
    'All selected user info is displayed here:',
  other_info_user_info_non_standardized: 'Add user info (Non-standardized)',
  other_info_only_non_standardized_selected:
    'Only non-standardized user info can be reordered:',
  other_info_confirm_del: 'Are you sure you want to remove?',
  other_info_phased_out_eventually:
    'Try to use the above standardized fields as the non-standardized fields will be phased out eventually.',
  other_info_standardized_tips_act_combo_main:
    "Identified SKU under this package is combo SKU, thus the following standardized extra info can't be configured at Combo package temporarily.",
  other_info_standardized_tips_act_combo_temporarily:
    "Identified SKU under this package is the standalone SKU to combo package {0} of activity {1}, thus the following standardized extra info can't be configured at Combo package temporarily.",
  other_info_standardized_tips_combo:
    "Identified package {0} contains standardized extra info.  Standardized extra info can't be configured at combo package temporarily.",
  other_info_standardized_tips_act_api:
    'Below extra info are temporarily not applicable for API intergated activities until required adpation is in place.',
  other_info_standardized_tips_act_identity:
    'Below extra info are temporarily not applicable for activity with extra info: <br />140555,140556,140557,140558,140559,140560 as there are validation specifically set up for these certificate extra info, not until required adpation is in place.',
  other_info_standardized_tips_act_multi:
    'Below extra info are not applicable for following reason: ',

  // 价格日历迁移的多语言
  package_unit_type: 'Unit Type',
  package_unit_person: 'Person',
  package_unit_adult: 'Adult',
  package_unit_child: 'Child',
  package_unit_infant: 'Infant',
  package_unit_youth: 'Youth',
  package_unit_senior: 'Senior',
  package_unit_other: 'Other',
  package_unit_name: 'Unit Name',
  package_unit_tips:
    "Don't forget to translate the customized unit name if you add new units after activity published",
  package_unit_buy: 'Min. requirement unit',
  package_unit_tool:
    'If switched to ‘Yes’, the user must buy this unit; if switched to ‘No’, the user must book the max/min units only if this unit is selected.',
  package_price_age_range: 'Age Range',
  package_min_max_units: 'Min/Max No. of Units',
  package_unlimited: 'Unlimited',
  package_price_units: 'Units',
  package_price_cost: 'Cost Price',
  package_price_retail: 'Retail Price',
  package_price_selling: 'Selling Price',
  package_price_save_tips:
    'The activity that contains this current package has been published at least once. If the current package information are saved, costs and selling price could not be edited with the current interface. The only place for editing costs and prices is “package list > edit price”. Are you sure to save all information of current package?',
  package_price_regular_agent: 'Regular Agent Price',
  package_price_premier_agent: 'Premier Agent Price',
  package_price_china_agent_rate: 'China Agent Price Markup % Over Cost',
  pm_price_model: 'Price Model',
  pm_fixed_price: 'Fixed Price',
  pm_price_calendar: 'Price Calendar',
  pm_create_price_calendar: 'Generate Price Calendar',
  pm_default_setting: 'Default Setting',
  pm_update_default_setting: 'Edit Default Setting',
  pm_cost_price: 'Cost',
  pm_market_price: 'Retail Price',
  pm_selling_price: 'Selling Price',
  pm_special_selling_price: 'Special Selling Price',
  pm_price_type: 'Price Type',
  pm_modify_price: 'Edit Price',
  pm_batch_edit_calendar: 'Bulk Update Prices',
  pm_calendar_view: 'Calendar View',
  pm_list_view: 'List View',
  pm_edit_cost: 'Edit Cost or Selling Price',
  pm_cancel: 'Cancel',
  pm_submit: 'Submit',
  pm_change_merchant_warn:
    'Warning: you only set up default setting for the SKUs of current pacakge. It is likely that the amount of costs and prices are different from their actual number. Please confirm cost and selling price of each timeslot before you republish the package.',
  pm_timeslot_operator_warn:
    'If the created timeslot was the new one for this package, please be aware of setting the prices on the edit price page.\nIf any created timeslot is the same as any deleted timeslot of this package, the system will set the prices for those timeslots with historical prices. Please be aware of checking the price.',
  pm_change_pm: 'Switch Price Model',
  pm_pkg_id: 'Package ID',
  pm_pkg_name: 'Package Name',
  pm_pkg_status: 'Status',
  pm_reminder: 'Attention',
  pm_change_pm_warn:
    'Switching price model will lead to the removal of all existing price data points and these data points cannot be recovered. Are you sure to swtich price model of the current package?',
  pm_remark: 'Remark',
  pm_cost_history: 'Cost History',
  pm_effective_time: 'Effective Time',
  pm_pick_time_range: 'Duration',
  pm_parti_date: 'Participation Time',
  pm_pick_date: 'Select Participation Date',
  pm_search: 'Search',
  pm_reset: 'Reset',
  pm_cost: 'Cost',
  pm_operator: 'Operator',
  pm_reason: 'Remark',
  pm_selling_history: 'Selling Price History',
  pm_selling_price_type: 'Selling Price Type',
  pm_expire_time: 'Expire Time',
  pm_date_time: 'Participation Date or Time',
  package_schedule_rule: 'Schedule Rule Name',
  package_info_enter_desc: 'Enter package subtitle in ',
  package_tag_info: 'Package Info',
  package_tag_price: 'Price',
  package_tag_schedule: 'Create Timeslot',
  package_tag_extra_info: 'Extra Information',
  package_info_name: 'Package Title',
  package_info_desc: 'Package Description',
  package_info_max_min_participants: 'Min/Max Bookings',
  package_info_unlimited: 'Unlimited',
  package_info_limited: 'Maximum',
  package_info_participants: 'Purchase Quantity',
  package_info_reconfirmation: 'Reconfirmation',
  package_info_reconfirmation_hint:
    'Reconfirmation required from operator 24-48 hours before activity start time',
  package_merchant_currency: 'Merchant Currency',
  package_retail_selling_currency: 'Retail/Selling Currency',
  package_another_unit: 'Create Another Unit',
  package_create_timeslot_hint: 'You can create one or multiple timeslots',
  package_timeslot_open_ticket: 'Open Ticket',
  package_open_ticket_hint:
    'Customers can visit on any date, regardless of what day they select in the calendar ',
  package_start_date: 'Start Date',
  package_timeslot: 'Timeslot',
  package_timeslot_all_day: 'All day',
  package_timeslot_by_timeslot: 'By timeslot',
  package_cut_off_time: 'Cut-off Time',
  package_day_s: 'Day(s)',
  package_inventory_unlimited: 'Unlimited',
  package_inventory_limited_per_day: 'Limited per day',
  package_cut_off_time_hint:
    'Cut-off Time in local timezone; 00:00 is the first hour of the day; 24:00 is last hour of the day',
  package_inventory: 'Inventory',
  package_timeslot_repeat: 'Repeat',
  package_timeslot_repeat_daily: 'Repeat Daily',
  package_timeslot_repeat_weekly: 'Repeat Weekly',
  package_timeslot_repeat_none: 'Do not Repeat',
  package_until: 'Until',
  package_copy_schedule: 'Copy Package Schedule',
  package_copy_schedule_tips:
    'The copy schedule function is only work for the package without any available schedule. And if you have auto extend rule, please make sure block out the date in your auto extend rule.',
  package_copy_schedule_dialog_title: 'Select the package copy from',
  package_copy_schedule_dialog_tips:
    'Please select the package which need to copy the schedule',
  block_out_date: 'Block Out Date In Auto Extend',
  global_modify: 'Modify',
  global_valid: 'Valid',
  global_expired: 'Expired',
  global_usable: 'Usable',
  global_invalid: 'Invalid',
  global_read: 'view',
  package_schedule_autoextend: 'Auto Extend',
  package_schedule_autoextend_hint:
    'Everyday auto extend one-day timeslot from the last timeslot which is existing according to the above schedule rule. And the system will start auto extend today',
  package_schedule_close_autoextend: 'Delete Auto Extend',
  package_schedule_end_date: 'End Date',
  package_list_modify_pkg: 'Modify Package',
  package_list_modify_unit: 'Modify Unit',
  package_list_unit_published: 'Published',
  package_list_unit_unpublished: 'Unpublished',
  package_unit_unpublish: 'Unpublish',
  package_unit_publish: 'Publish',
  package_list_status: 'Status',

  // 价格日历需要补充的多语言
  package_unit_type_price_warning:
    'The default price will be used for auto extend. Please fill in carefully. You can rewrite the price by bulk modification after saved.',
  package_unit_name_tips:
    "Don't forget to translate the customized unit name if you add new units after activity published",
  package_unit_requirement_tips:
    'If switched to ‘Yes’, the user must buy this unit; if switched to ‘No’, the user must book the max/min units only if this unit is selected.',
  package_price_default_setting_warning:
    'The default price will be used for auto extend. Please fill in carefully.'
}
