/* eslint-disable quotes */
export default {
  // other-info待补充的多语言
  other_info_submit_finished: 'The Package Extra is finished now !',
  other_info_copy_no_data: 'There is no package that has extra info to copy!',
  other_info_non_standardized_warning:
    'If the below selections is not enough to describe your products, please contact your klook point of contact and let them help you.',

  // other-info迁移多语言
  global_copy: '复制',
  global_yes: '有',
  global_no: '无',
  global_select: '请选择',
  global_bts_table_formatNoMatches: '没有找到匹配的记录',
  global_copy_success: '复制成功',
  global_copy_error: '复制失败',
  global_delete_success: '删除成功',
  global_delete_error: '删除失败',
  global_create_success: '创建成功',
  global_create_error: '创建失败',
  global_modify_success: '修改成功',
  global_modify_error: '修改失败',
  global_button_ok: '确定',
  global_button_cancel: '取消',
  copy_package: '复制内容到其他套餐',
  content_name: '名称',
  main_package: '主套餐',
  js_post_success: '提交成功',
  act_save_success: '保存成功',
  act_save_fail: '保存失败',
  type_to_search: '请输入关键词',
  package_price_data_error: '数据错误',
  can_not_be_empty: '不能为空',
  package_xinfo_finish_hint: '本活动仍有其他主套餐未填写完毕',
  package_xinfo_hint: '如在此表格中無法找到所需項目，請下滑頁面至新建附加資訊',
  packge_xinfo_fields: '附加資訊區域',
  package_xinfo_custom_fields: '新增用戶資料（未結構化）',
  package_xinfo_preview_fields: '預覽',
  package_xinfo_modify: '更改',
  package_xinfo_dropdown_list: '下拉菜單',
  package_xinfo_text_box: '輸入框',
  package_xinfo_date: '日期',
  package_xinfo_time: '時間',
  package_xinfo_before_today: '能選到今天之前的日期',
  package_xinfo_after_today: '只能選到今天及之後的日期',
  package_ask_once_per_booking: '一個訂單只需填寫一次',
  package_ask_for_each_participants: '每位參與者均需填寫',
  package_xinfo_mandatory_hint: '這個附加信息是否為必填？',
  package_xinfo_another: '新增下一個附加資訊',
  package_xinfo_sort_hint: '所有你已選擇和創建的附加資訊（可拖動排序）',
  package_another_main_package: '创建新套餐',
  package_another_addon_package: '创建新的附加套餐',
  package_take_overview: '提交 & 综览所有内容',
  pacage_another_main_hint: '如果你有其他的套餐还需要创建',
  package_another_addon_hint: '如果你还有其他的附加套餐需要创建',
  package_take_overview_hint:
    '如果你已完成了所有套餐的创建，并且没有附加套餐需要创建',
  package_info_name_placeholder: '请填写套餐标题',
  package_info_subtitle_placeholder: '请填写套餐副标题',
  package_info_description_placeholder: '请填写套餐描述',
  package_xinfo_name_placeholder: '请填写附加信息名称',
  package_xinfo_hint_placeholder: '请填写提示信息',
  package_xinfo_hint_input: '附加信息提示',
  package_xinfo_dropdown_placeholder:
    '格式:"选项内容1,选项内容2,选项内容3"；"选项内容"作为下拉选框的选项内容，"附加信息提示"将成为框内提示内容',
  package_xinfo_checkbox_placeholder:
    '请填写具体条款内容（如果没有则不需要填写）',
  package_xinfo_copy: '从已有附加信息复制',
  package_xinfo_type: '类型',
  package_xinfo_name: '附加信息名称',
  package_xinfo_repeat_rules: '重复适用',
  package_xinfo_mandatory: '是否必填',
  package_xinfo_enter__name: '请填写附加信息名称',
  package_xinfo_enter_hint: '请输入提示信息',
  package_xinfo_ask_once: '一个订单只需填写一次',
  package_xinfo_ask_each: '每位参与者均需填写',
  package_xinfo_checkbox: '打勾确认框',
  package_xinfo_choose_repeat_rule: '选择重复规则',
  package_xinfo_refresh_content: '刷新内容',
  package_info_add_package: '添加套餐',
  common_field_personal_information: '个人信息',
  common_field_pick_up_detail: '接送信息',
  common_field_car_transfer_detail: '包车及接送信息',
  common_field_hotel_information: '酒店接送信息',
  common_field_wifi_and_sim: '移动WiFi & SIM卡',
  common_field_language_and_others: '语言选择&其他特殊备注',
  common_field_diver_details: '潜水信息',
  common_field_mailing_information: '邮寄信息',
  common_field_personal_measurement: '个人尺码',
  other_info_standardized: '用户信息（已结构化）',
  other_info_contact: '联系信息',
  other_info_following_info_collected: '以下为自动收集的联系人信息：',
  other_info_required_participants: '出行人必填信息设置',
  other_info_required: '是否需要出行人信息：',
  other_info_less_likely_warn:
    '用户下单时需要填写的信息越多，转换率越低，请仅勾选必要信息',
  other_info_additional: '附加信息',
  other_info_less_likely_warn_2:
    '用户下单时需要填写的信息越多，转换率越低，请仅勾选必要信息',
  other_info_last_name: '姓氏',
  other_info_first_name: '名字',
  other_info_country_region: '国家 / 地区',
  other_info_phone_number: '手机号码',
  other_info_email: '邮箱',
  other_info_no_required: '无需填写信息（推荐）',
  other_info_1_participant: '只需填写1名出行人信息',
  other_info_all_participant: '需填写所有出行人信息',
  other_info_validate_warn_duplicate: '错误： 重复的选项。',
  other_info_non_standardized: '其他用户信息（未结构化）',
  other_info_all_selected_displayed: '所有已选中和创建的信息将在此处显示：',
  other_info_user_info_non_standardized: '添加新的用户信息（未结构化）',
  other_info_only_non_standardized_selected: '仅可拖拽未结构化的用户信息：',
  other_info_confirm_del: '确认要删除此信息吗？',
  other_info_phased_out_eventually:
    '非结构化字段最终会被淘汰，请尽量使用上面的结构化字段。',
  other_info_standardized_tips_act_combo_main:
    '该套餐下的SKU为Combo SKU，故该套餐暂时不支持配置以下结构化的Extra Info。',
  other_info_standardized_tips_act_combo_temporarily:
    '该套餐下的SKU作为活动 {0} >> 套餐 {1}的Standalone SKU，故该套餐暂时不支持配置以下结构化的Extra Info。',
  other_info_standardized_tips_combo:
    '套餐 {0} 含有已结构化的Extra Info，暂不支持Combo配置的套餐含有已结构化的Extra Info.',
  other_info_standardized_tips_act_api:
    '下面的附加信息直到相关的适配完成之前暂时不适用于API活动。',
  other_info_standardized_tips_act_identity:
    '下面的附加信息暂时无法适用于适配了Extra Info ID为<br />140555,140556,140557,140558,140559,140560的套餐，直到相关的开发工作完成',
  other_info_standardized_tips_act_multi:
    '下面的附加信息暂时无法适用于该套餐，原因如下：',

  // 价格日历迁移的多语言
  package_unit_type: '价格单位类型',
  package_unit_person: '每人',
  package_unit_adult: '成人',
  package_unit_child: '儿童',
  package_unit_infant: '婴儿',
  package_unit_youth: '青年',
  package_unit_senior: '老年',
  package_unit_other: '其他',
  package_unit_name: '价格单位名称',
  package_unit_tips: '若在活动发布后新增unit, 请务必记得翻译创建的unit name。',
  package_unit_buy: '必须预订此项目',
  package_unit_tool:
    '如果打开开关，此价格单位将成为用户的必须购买项目；如果关闭开关，直到用户选择了此价格单位时，最多最少购买数量才会生效',
  package_price_age_range: '适用年龄范围',
  package_min_max_units: '单价中最少/最多购买数量',
  package_unlimited: '不限',
  package_price_units: '购买数量',
  package_price_cost: '成本价',
  package_price_retail: '市场价',
  package_price_selling: '售卖价',
  package_price_save_tips:
    '当前套餐所属的活动已被发布过，如果保存当前编辑，成本或价格将无法再通过本界面进行修改，仅可通过套餐列表 > 修改价格处进行管理，确定保存本套餐信息吗？',
  package_price_regular_agent: '普通代理商价格',
  package_price_premier_agent: '高级代理商价格',
  package_price_china_agent_rate: '中国代理商利率',
  pm_price_model: '价格模型',
  pm_fixed_price: '固定价格',
  pm_price_calendar: '按出行日期定价',
  pm_create_price_calendar: '生成价格日历',
  pm_default_setting: '基本设置',
  pm_update_default_setting: '更新基本设置',
  pm_cost_price: '成本价',
  pm_market_price: '市场价',
  pm_selling_price: '售卖价',
  pm_special_selling_price: '特殊售卖价',
  pm_price_type: '价格类型',
  pm_modify_price: '调整价格',
  pm_batch_edit_calendar: '批量调整价格日历',
  pm_calendar_view: '日历视图',
  pm_list_view: '列表视图',
  pm_edit_cost: '编辑成本或价格',
  pm_cancel: '取消',
  pm_submit: '提交',
  pm_change_merchant_warn:
    '当前该套餐下的所有SKU仅以基本设置配置了成本与售卖价，其金额很可能与实际情况不符，请在发布该套餐前确认每个出行日期或时间的成本价和售卖价金额是否正确！',
  pm_timeslot_operator_warn:
    '如添加的timeslot为新增，请到价格设置界面中设置价格\n如添加的time slot中有与该套餐以前已删除的time slot相同的，系统将会默认为这些timeslot使用历史价格，请注意确认价格。',
  pm_change_pm: '变更价格模型',
  pm_pkg_id: '套餐ID',
  pm_pkg_name: '套餐名称',
  pm_pkg_status: '状态',
  pm_reminder: '注意',
  pm_change_pm_warn:
    '更改价格模型会导致当前设置的价格全部丢失，且无法找回。请确认是否还要执行更改价格模型。',
  pm_remark: '备注',
  pm_cost_history: '成本历史',
  pm_effective_time: '生效时间',
  pm_pick_time_range: '选择时间范围',
  pm_parti_date: '出行时间',
  pm_pick_date: '选择出行日期',
  pm_search: '搜索',
  pm_reset: '重置',
  pm_cost: '成本价',
  pm_operator: '操作人',
  pm_reason: '原因',
  pm_selling_history: '售卖价历史',
  pm_selling_price_type: '价格类型',
  pm_expire_time: '失效时间',
  pm_date_time: '日期及时间',
  package_schedule_rule: '时间段规则名称',
  package_merchant_currency: '商户货币',
  package_retail_selling_currency: '市场价/售卖价货币',
  package_another_unit: '创建一个新价格',
  package_create_timeslot_hint: '可创建一个或多个时间段',
  package_timeslot_open_ticket: '无指定日期票券',
  package_open_ticket_hint: '用户在预订活动时无需选择日期',
  package_start_date: '活动开始日期',
  package_timeslot: '时间段',
  package_timeslot_all_day: '全天',
  package_timeslot_by_timeslot: '按时间段',
  package_cut_off_time: '截至预订时间',
  package_day_s: '天',
  package_inventory_unlimited: '无限',
  package_inventory_limited_per_day: '按每天限制数量',
  package_cut_off_time_hint:
    '以当地时间为准；00:00表示今天早上；24:00表示今天晚上',
  package_inventory: '库存',
  package_timeslot_repeat: '重复适用',
  package_timeslot_repeat_daily: '每天',
  package_timeslot_repeat_weekly: '一周中的某几天',
  package_timeslot_repeat_none: '不重复',
  package_until: '至',
  package_copy_schedule: '复制套餐的日期',
  package_copy_schedule_tips:
    '复制日期功能仅针对当前套餐没有任何可用日期。如果套餐有自动延续功能，请在自动延续功能的规则当中删除掉不需要的日期。',
  package_copy_schedule_dialog_title: '选择需要复制的套餐',
  package_copy_schedule_dialog_tips: '请选择需要复制到当前套餐的日期套餐来源',
  block_out_date: '设置无需添加的日期',
  global_modify: '修改',
  global_valid: '有效的',
  global_expired: '过期的',
  global_usable: '是否有效',
  global_invalid: '无效',
  global_read: '查看',
  package_schedule_autoextend: '自动延续',
  package_schedule_autoextend_hint:
    '自已有的最后一个时间段开始，每天自动根据以上时间段规则延续新一天的时间段；系统会从今天开始自动延续',
  package_schedule_close_autoextend: '停止自动延续',
  package_schedule_end_date: '结束日期',
  package_list_modify_pkg: '修改套餐',
  package_list_modify_unit: '修改单位',
  package_list_unit_published: '已发布',
  package_list_unit_unpublished: '未发布',
  package_unit_unpublish: '下架',
  package_unit_publish: '上架',
  package_list_status: '状态',

  // 价格日历需要补充的多语言
  package_unit_type_price_warning:
    'The default price will be used for auto extend. Please fill in carefully. You can rewrite the price by bulk modification after saved.',
  package_unit_unit_name_tips:
    "Don't forget to translate the customized unit name if you add new units after activity published",
  package_unit_requirement_tips:
    'If switched to ‘Yes’, the user must buy this unit; if switched to ‘No’, the user must book the max/min units only if this unit is selected.',
  package_unit_unit_number_tips:
    'If switched to ‘Limited per day’, the user must buy this unit; if switched to ‘Unlimited’, the user must book the max/min units only if this unit is selected.',
  package_price_default_setting_warning:
    'The default price will be used for auto extend. Please fill in carefully.'
}
