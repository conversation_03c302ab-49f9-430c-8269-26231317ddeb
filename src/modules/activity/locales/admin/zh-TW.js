/* eslint-disable quotes */
export default {
  // other-info待补充的多语言
  other_info_submit_finished: 'The Package Extra is finished now !',
  other_info_copy_no_data: 'There is no package that has extra info to copy!',
  other_info_non_standardized_warning:
    'If the below selections is not enough to describe your products, please contact your klook point of contact and let them help you.',

  // other-info迁移多语言
  global_copy: '複製',
  global_yes: '有',
  global_no: '無',
  global_select: '請選擇',
  global_bts_table_formatNoMatches: '沒有找到匹配的記錄',
  global_copy_success: '複製成功',
  global_copy_error: '複製失敗',
  global_delete_success: '刪除成功',
  global_delete_error: '刪除失敗',
  global_create_success: '創建成功',
  global_create_error: '創建失败',
  global_modify_success: '修改成功',
  global_modify_error: '修改失败',
  global_button_ok: '確定',
  global_button_cancel: '取消',
  copy_package: '复制内容到其他套餐',
  unfinish_field: '未填寫字段',
  main_package: '主套餐',
  content_name: '名稱',
  js_post_success: '提交成功',
  act_save_success: '保存成功',
  act_save_fail: '保存失敗',
  type_to_search: '請輸入關鍵詞',
  package_price_data_error: '數據錯誤',
  can_not_be_empty: '不能为空',
  package_xinfo_finish_hint: '本活動仍有其他主套餐未填寫完畢',
  package_xinfo_hint: '如在此表格中无法找到所需项目，请下滑页面至新建附加信息',
  packge_xinfo_fields: '附加信息区域',
  package_xinfo_custom_fields: '添加新的用户信息（未结构化）',
  package_xinfo_preview_fields: '预览',
  package_xinfo_modify: '修改',
  package_xinfo_dropdown_list: '下拉菜单',
  package_xinfo_text_box: '输入框',
  package_xinfo_date: '日期',
  package_xinfo_time: '时间',
  package_xinfo_before_today: '能选到今天之前的日期',
  package_xinfo_after_today: '只能选到今天及之后的日期',
  package_ask_once_per_booking: '一个订单只需填写一次',
  package_ask_for_each_participants: '每位参与者均需填写',
  package_xinfo_mandatory_hint: '这个附加信息是否为必填？',
  package_xinfo_another: '新增下一个附加信息',
  package_xinfo_sort_hint: '所有你已选择和创建的附加信息（可拖动排序）',
  package_another_main_package: '創建新套餐',
  package_another_addon_package: '創建新的附加套餐',
  package_take_overview: '提交＆總覽所有內容',
  pacage_another_main_hint: '如果你有其他的套餐還需要創建',
  package_another_addon_hint: '如果你還有其他的附加套餐需要創建',
  package_take_overview_hint:
    '如果你已完成了所有套餐的創建，並且沒有附加套餐需要創建',
  package_info_name_placeholder: '請填寫套餐標題',
  package_info_subtitle_placeholder: '請填寫套餐副標題',
  package_info_description_placeholder: '請填寫套餐描述',
  package_xinfo_name_placeholder: '請填寫附加資訊名稱',
  package_xinfo_hint_placeholder: '請填寫提示信息',
  package_xinfo_hint_input: '附加信息提示',
  package_xinfo_dropdown_placeholder:
    '格式： "選項內容1,選項內容2,選項內容3"；"選項內容"作為下拉選框的選項內容，"附加信息提示"將作為框內提示內容',
  package_xinfo_checkbox_placeholder:
    '請填寫具體條款內容（如果沒有則不需要填寫）',
  package_xinfo_copy: '從已有附加信息復制',
  package_xinfo_type: '類型',
  package_xinfo_name: '附加信息名稱',
  package_xinfo_repeat_rules: '重復規則',
  package_xinfo_mandatory: '是否必填',
  package_xinfo_enter__name: '請填寫附加信息名稱',
  package_xinfo_enter_hint: '請填寫提示信息',
  package_xinfo_ask_once: '一個訂單只需填寫一次',
  package_xinfo_ask_each: '每位參與者均需填寫',
  package_xinfo_checkbox: '打勾確認框',
  package_xinfo_choose_repeat_rule: '選擇重複規則',
  package_xinfo_refresh_content: '刷新內容',
  package_info_add_package: '添加套餐',
  common_field_personal_information: '個人資訊',
  common_field_pick_up_detail: '接送資訊',
  common_field_car_transfer_detail: '包車及機場接送資訊',
  common_field_hotel_information: '酒店接送資訊',
  common_field_wifi_and_sim: '移動WiFi & SIM卡',
  common_field_language_and_others: '語言選擇 & 其他特殊備註',
  common_field_diver_details: '潛水資訊',
  common_field_mailing_information: '郵寄資訊',
  common_field_personal_measurement: '個人尺碼',
  other_info_standardized: '用戶資料（已結構化）',
  other_info_contact: '聯絡資料',
  other_info_following_info_collected: '以下資料將自動收集：',
  other_info_required_participants: '必填資料設定',
  other_info_required: '必填資料：',
  other_info_less_likely_warn:
    '如果你們需要用戶輸入多於必須的資料，他們會較大可能放棄預訂，因此請只請選擇必要的選項。',
  other_info_additional: '附加資料',
  other_info_less_likely_warn_2:
    '如果你們需要用戶輸入多於必須的資料，他們會較大可能放棄預訂，因此請只請選擇必要的選項。',
  other_info_last_name: '姓氏',
  other_info_first_name: '名字',
  other_info_country_region: '國家 / 地區',
  other_info_phone_number: '電話號碼',
  other_info_email: '電郵地址',
  other_info_no_required: '無必填資料（建議選項）',
  other_info_1_participant: '必須填寫1名参与人的資料',
  other_info_all_participant: '必須填寫所有参与人的資料',
  other_info_validate_warn_duplicate: '錯誤： 重複的選項。',
  other_info_non_standardized: '額外用戶資料（未結構化）',
  other_info_all_selected_displayed: '所有已選的用戶資料均會在此顯示：',
  other_info_user_info_non_standardized: '新增用戶資料（未結構化）',
  other_info_only_non_standardized_selected: '只可選擇有未經結構化的用戶資料：',
  other_info_confirm_del: '確認要移除嗎？',
  other_info_phased_out_eventually:
    '非結構化字段最終會被淘汰，請盡量使用上面的結構化字段',
  other_info_standardized_tips_act_combo_main:
    '該套餐下的SKU為Combo SKU，故該套餐暫時不支持配置以下結構化的Extra Info。',
  other_info_standardized_tips_act_combo_temporarily:
    '該套餐下的SKU作為活動 {0} >> 套餐 {1}的Standalone SKU，故該套餐暫時不支持配置以下結構化的Extra Info。',
  other_info_standardized_tips_combo:
    '套餐 {0} 含有已結構化的Extra Info，暫不支持Combo配置的套餐含有已結構化的Extra Info.',
  other_info_standardized_tips_act_api:
    '下面的附加信息直到相關的適配完成之前暫時不適用於API活動。',
  other_info_standardized_tips_act_identity:
    '下面的附加信息暫時無法適用於適配了Extra Info ID為<br />140555,140556,140557,140558,140559,140560的套餐，直到相關的開發工作完成',
  other_info_standardized_tips_act_multi:
    '下面的附加信息暫時無法適用於該套餐，原因如下：',

  // 价格日历迁移的多语言
  package_unit_type: '價格單位類型',
  package_unit_person: '每人',
  package_unit_adult: '成人',
  package_unit_child: '兒童',
  package_unit_infant: '嬰兒',
  package_unit_youth: '青年',
  package_unit_senior: '長者',
  package_unit_other: '其他',
  package_unit_name: '價格單位名稱',
  package_unit_tips: '若在活動發佈後新增unit，請務必記得翻譯創建的unit name。',
  package_unit_buy: '必須預訂此項目',
  package_unit_tool:
    '如果打開開關，此價格單位將成為用戶的必須購買項目；如果關閉開關，直到用戶選擇了此價格單位時，最多最少購買數量才會生效',
  package_price_age_range: '適用年齡範圍',
  package_min_max_units: '單價中最少/最多購買數量',
  package_unlimited: '不限',
  package_price_units: '購買數量',
  package_price_cost: '成本價',
  package_price_retail: '市場價',
  package_price_selling: '售賣價',
  package_price_save_tips:
    '當前套餐所屬的活動已被發布過，如果保存當前編輯，成本或價格將無法再通過本界面進行修改，僅可通過套餐列表 > 修改價格處進行管理，確定保存本套餐信息嗎？',
  package_price_regular_agent: '普通代理商價格',
  package_price_premier_agent: '高級代理商價格',
  package_price_china_agent_rate: '中國代理商利率',
  pm_price_model: '價格模型',
  pm_fixed_price: '固定價格',
  pm_price_calendar: '按出行日期定價',
  pm_create_price_calendar: '生成價格日曆',
  pm_default_setting: '基本設置',
  pm_update_default_setting: '更新基本設置',
  pm_cost_price: '成本價',
  pm_market_price: '市場價',
  pm_price_type: '價格類型',
  pm_modify_price: '調整價格',
  pm_batch_edit_calendar: '批量調整價格日曆',
  pm_calendar_view: '日曆視圖',
  pm_list_view: '清單檢視',
  pm_edit_cost: '編輯成本或價格',
  pm_cancel: '取消',
  pm_submit: '提交',
  pm_change_merchant_warn:
    '當前該套餐下的所有SKU僅以基本設置配置了成本與售賣價,其金額很可能與實際情況不符,請在發佈該套餐前確認每個出行日期或時間的成本價和售賣價金額是否正確!',
  pm_timeslot_operator_warn:
    '如添加的timeslot為新增，請到價格設置界面中設置價格\n如添加的time slot中有與該套餐以前已刪除的time slot相同的，系統將會默認為這些timeslot使用歷史價格，請注意確認價格。',
  pm_change_pm: '變更價格模型',
  pm_pkg_id: '套餐ID',
  pm_pkg_name: '套餐名稱',
  pm_pkg_status: '狀態',
  pm_reminder: '注意',
  pm_change_pm_warn:
    '更改價格模型會導致當前設置的價格全部丟失,且無法找回。 請確認是否還要執行更改價格模型。',
  pm_remark: '備註',
  pm_cost_history: '成本歷史',
  pm_effective_time: '生效時間',
  pm_pick_time_range: '選擇時間範圍',
  pm_parti_date: '出行時間',
  pm_pick_date: '選擇出行日期',
  pm_search: '搜索',
  pm_reset: '重置',
  pm_cost: '成本價',
  pm_operator: '操作人',
  pm_reason: '原因',
  pm_selling_history: '售賣價歷史',
  pm_selling_price_type: '價格類型',
  pm_selling_price: '售賣價',
  pm_special_selling_price: '特殊售賣價',
  pm_expire_time: '失效時間',
  pm_date_time: '日期及時間',
  package_schedule_rule: '時間段規則名稱',
  package_merchant_currency: '商戶貨幣',
  package_retail_selling_currency: '市場價/售賣價貨幣',
  package_another_unit: '創建一個新價格',
  package_create_timeslot_hint: '可創建一個或多個時間段',
  package_timeslot_open_ticket: '無指定日期票券',
  package_open_ticket_hint: '用戶在預訂活動時無需選擇日期',
  package_start_date: '活動開始日期',
  package_timeslot: '時間段',
  package_timeslot_all_day: '全天',
  package_timeslot_by_timeslot: '按時間段',
  package_cut_off_time: '截止預訂時間',
  package_day_s: '天',
  package_inventory_unlimited: '無限',
  package_inventory_limited_per_day: '按每天限制數量',
  package_cut_off_time_hint:
    '以當地時間為準；00:00表示今天早上；24:00表示今天晚上',
  package_inventory: '庫存',
  package_timeslot_repeat: '重複適用',
  package_timeslot_repeat_daily: '每天',
  package_timeslot_repeat_weekly: '一週中的某幾天',
  package_timeslot_repeat_none: '不重複',
  package_until: '至',
  package_copy_schedule: '複製套餐的日期',
  package_copy_schedule_tips:
    '複製日期功能僅針對當前套餐沒有任何可用日期。如果套餐有自動延續功能，請在自動延續功能的規則當中删除掉不需要的日期。',
  package_copy_schedule_dialog_title: '選擇需要複製的套餐',
  block_out_date: '無需添加的日期',
  global_modify: '修改',
  global_valid: '有效的',
  global_expired: '過期的',
  global_usable: '是否有效',
  global_invalid: '無效',
  global_read: '查看',
  package_schedule_autoextend: '自動延續',
  package_schedule_autoextend_hint:
    '自已有的最後一個時間段開始，每天自動根據以上時間段規則延續新一天的時間段；系統會從今天開始自動延續',
  package_schedule_close_autoextend: '终止自動延續',
  package_schedule_end_date: '結束日期',
  package_list_modify_pkg: '更改套餐',
  package_list_modify_unit: '更改單位',
  package_list_unit_published: '已發布',
  package_list_unit_unpublished: '未發布',
  package_unit_unpublish: '下架',
  package_unit_publish: '上架',
  package_list_status: '狀態',

  // 价格日历需要补充的多语言
  package_unit_type_price_warning:
    'The default price will be used for auto extend. Please fill in carefully. You can rewrite the price by bulk modification after saved.',
  package_unit_unit_name_tips:
    "Don't forget to translate the customized unit name if you add new units after activity published",
  package_unit_requirement_tips:
    'If switched to ‘Yes’, the user must buy this unit; if switched to ‘No’, the user must book the max/min units only if this unit is selected.',
  package_unit_unit_number_tips:
    'If switched to ‘Limited per day’, the user must buy this unit; if switched to ‘Unlimited’, the user must book the max/min units only if this unit is selected.',
  package_price_default_setting_warning:
    'The default price will be used for auto extend. Please fill in carefully.'
}
