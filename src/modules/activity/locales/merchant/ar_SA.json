{"17049": "", "17057": "", "18718": "عر<PERSON> المزيد", "18719": "<PERSON>ر<PERSON> أقل", "21813": "", "21817": "", "21820": "", "21837": "", "21838": "", "21856": "", "21857": "", "21890": "", "22180": "", "22181": "", "22182": "", "23114": "Open Ticket with Calendar", "23115": "Open Ticket With Calendar Package", "23116": "Select this ticket type the activity page will display calendar selection and open ticket package icon", "23117": "", "23118": "Open Ticket Withour Calendar Package", "23119": "If all packages on the activity page are selected as open ticket without calendar type, the calendar selection will not be displayed. Only packages that do not rely on the participation date can be selected as an open ticket. The package settings for open ticket without calendar is as follow:<br/>- Confirmation policy: Instant confirmation<br/>- Inventory type: <br/>i) Under Klook code & API, package must be either free cancellation before redemption or no cancellation;<br/>ii) Under Inventory, package must be no cancellation<br/>- Merchant settlement policy: Either confirmation time or redemption time", "23120": "", "23121": "", "23122": "اسم الوحدة المخصص", "23123": "إذا تحولت إلى \\نعم\\\"، سيتم عرض النص على الواجهة الأمامية كاسم الوحدة. في حالة \\\" لا\\ \"، سيتم تطبيق اسم نوع الوحدة العادي.\"", "26083": "", "27109": "طالب", "27716": "", "27717": "", "27719": "", "27722": "إدارة الوحدات", "27723": "قاعدة المهلة الزمنية وإعدادات النموذج", "27724": "سيتم تطبيق الإعدادات أدناه على جميع الوحدات ضمن هذه الباقة. <br/><br/><span style=\\\"font-weight:bold;\\\">- ما هي الوحدة؟ </span>الوحدات تشكل مخزون النشاط وسعره، وعادة ما يتم تجميعها حسب نوع المشاركين (أي بالغ، طفل، شخص وما إلى ذلك) <br/><span style=\\\"font-weight:bold;\\\">- ما هي المهلة الزمنية؟ </span> هذا هو الوقت المحدد للجدول الزمني (أي 10:00, 12:00) <br/><span style=\\\"font-weight:bold;\\\">- ما هو الجدول الزمني؟ </span> مزيج من الفترة الزمنية والتاريخ (أي 2021-08-01 10:00, 2021-08-01 12:00, 2021-08-02 10:00)<br/><br/> ستعمل قاعدة المهلة الزمنية على أتمتة الجدول الزمني لأيام الأسبوع وعطلات نهاية الأسبوع المتكررة. على سبيل المثال، إذا تم تعيين القاعدة ليوم الاثنين 10:00، بعد ذلك سيتم إنشاء جدول 2021-08-02 10:00, 2021-08-09 10:00, 2021-08-16 10:00, إلخ لجميع الوحدات", "27725": "نوع الفترة الزمنية", "27726": "<PERSON><PERSON><PERSON> اليوم", "27727": "<PERSON><PERSON><PERSON> الوقت", "27728": "تكرار الفترات الزمنية والوقت المستقطع", "27729": "تكرير القاعدة", "27730": "ستعمل الفترة الزمنية الجديدة على أتمتة الجدول الزمني لأيام الأسبوع وعطلات نهاية الأسبوع المتكررة لجميع وحدات (SKUs) الموجودة ضمن هذه الباقة وأي وحدات (SKUs) مشتركة. هل أنت متأكد أنك تريد إضافتها؟", "27731": "ستتم مزامنة وقت التوقف المُعدّل تلقائيًا مع جميع الجداول الزمنية الفارغة لجميع وحدات (SKU) ضمن هذه الباقة وأي وحدات (SKU) مشتركة، ولكن لن تتم مزامنتها تلقائيًا مع الجداول الحالية. هل أنت متأكد أنك تريد تعديلها؟", "27732": "نموذج المخزون", "27733": "إجمالي المخزون", "27734": "الحد الأقصى المعين لعدد المخزون الذي يمكن للمستخدمين حجزه للجدول الزمني بأكمله (المخزون المدمج لجميع الجداول هو كمية محددة)", "27735": "تقويم المخزون", "27736": "", "27737": "مخزون غير محدود", "27738": "", "27739": "السعر ثابت لجميع الجداول الزمنية داخل الوحدة", "27740": "", "27741": "لن تتمكن من تغيير التاجر وعملات البيع بالتجزئة/البيع بعد الحفظ. هل أنت متأكد أنك تريد الحفظ؟", "27742": "لا يمكن تغيير نموذج السعر إلا في حالة عدم نشر الباقة ووحدات SKU الموجودة ضمن هذه الباقة", "27743": "يمكن تغيير نموذج المخزون فقط عندما:<br/>- لا توجد وحدة لها علاقة مشتركة للجدول الزمني والمخزون مع الوحدات الأخرى من باقة أخرى:<br/>- تكون الباقة وأي وحدات ضمن الباقة غير منشورة", "27744": "سيؤدي تبديل نماذج المخزون إلى إزالة جميع بيانات المخزون الحالية، والتي لا يمكن استعادتها. هل أنت متأكد أنك تريد تبديل نموذج المخزون لهذه الباقة؟", "27745": "تم التحويل بنجاح. يرجى إدخال بيانات المخزون الجديدة في \\إعدادات التقويم\\\" لكل وحدة في هذه الباقة.\"", "27746": "سيؤدي تبديل نماذج الأسعار إلى إزالة جميع بيانات الأسعار الحالية، والتي لا يمكن استعادتها. هل أنت متأكد أنك تريد تبديل نموذج الأسعار لهذه الباقة؟", "27747": "تم التحويل بنجاح. يرجى إدخال بيانات الأسعار الجديدة في \\إعدادات التقويم\\\" لكل وحدة في هذه الباقة.\"", "27748": "حدد نوع الوحدة المراد إنشاؤها", "27749": "أنواع الوحدات الشائعة", "27750": "أنواع الوحدات المخصصة (للنشاطات المحددة فقط)", "27751": "تعديل", "27752": "إنشاء وحدة جديدة", "27753": "علاقة مشتركة", "27754": "وحدة رئيسية", "27755": "ستقوم هذه الوحدة بمشاركة وتحديث الجدول الزمني والمخزون للوحدات الأخرى", "27756": "وحدة فرعية", "27757": "تتم مشاركة الجدول الزمني ومخزون هذه الوحدة وتحديثهما من وحدة أخرى", "27758": "وحدة منفردة", "27759": "تحتوي هذه الوحدة على جدول زمني ومخزون مستقل عن الوحدات الأخرى", "27760": "الخطوة 1: إعدادات العلاقة المشتركة (بين الوحدات)", "27761": "الخطوة 2: إعد<PERSON><PERSON><PERSON> التقويم (الجدول الزمني / المخزون / السعر)", "27762": "نسخ معلومات الوحدة / العلاقة المشتركة / الجدول الزمني / السعر / المخزون من وحدة أخرى", "27763": "اختيار وحدة للنسخ منها", "27764": "اختيار الباقة", "27765": "اختيار الوحدة", "27766": "هل الجدول الزمني ومخزون هذه الوحدة مشترك وتم تحديثه من وحدة أخرى؟", "27767": "\\\" نعم\\\" تعني أنه سيتم مشاركة الجدول الزمني والمخزون الخاص بهذه الوحدة (الوحدة الفرعية) مع الوحدة الرئيسية المحددة أدناه. يمكنك فقط تعديل الجدول الزمني والمخزون من الوحدة الرئيسية وسيتم تطبيق التغيير تلقائيًا على الوحدة الفرعية.<br/><br/>\\\" لا\\\" يعني أن الجدول الزمني والمخزون لا تتم مشاركتهما مع الوحدات الأخرى ويمكن تعديلهما مباشرة.<br/><br/>يمكنك فقط تحديد وحدة ضمن نفس النشاط لتكون الوحدة الرئيسية. إذا كان لديك حاليًا وحدة واحدة فقط في إطار هذا النشاط، فيرجى إنشاء وحدة أخرى تشارك جدولها ومخزونها", "27768": "حدد الوحدة الرئيسية التي ستشارك جدولها ومخزونها", "27769": "عرض العلاقة المشتركة", "27770": "الوحدة الحالية", "27771": "", "27772": "إزالة كافة العلاقات المشتركة", "27773": "هل أنت متأكد أنك تريد إزالة العلاقة المشتركة من كافة الوحدات؟", "27774": "هل أنت متأكد أنك تريد تغيير العلاقة؟", "27775": "سيؤدي إنشاء علاقات مشتركة جديدة إلى إزالة جميع بيانات الجدول الزمني والمخزون والسعر الحالية، والتي لا يمكن استردادها. هل أنت متأكد أنك تريد المواصلة؟", "27776": "", "27777": "", "27778": "", "27779": "", "27780": "", "27781": "", "27782": "", "27783": "إجمالي المخزون", "27784": "إجمالي المخزون و إعدادات الأسعار الثابتة", "27785": "إعدادات إجمالي المخزون", "27786": "إعدادات الأسعار الثابتة", "27787": "إضافة فترة زمنية", "27788": "التمديد التلقائي", "27789": "إضافة/تعديل بالجملة", "27790": "اضغط لإضافة الفترة الزمنية في قاعدة الباقة، والتي سيتم تطبيقها على جميع الوحدات في هذه الباقة", "27791": "بدءًا من اليوم الأخير، سيتم تمديد الجدول الزمني تلقائيًا يوميًا وفقًا للإعدادات أدناه. سيتم تمديد النظام تلقائيًا اعتبارًا من اليوم.", "27792": "تاريخ الانتهاء (تمديد تلقائي لغاية yyyy-mm-dd)", "27793": "<PERSON>ير محدود", "27794": "م<PERSON><PERSON><PERSON><PERSON>", "27795": "حدد تاريخًا أو نطاقًا زمنيًا", "27796": "", "27797": "", "27798": "رؤية الواجهة الأمامية", "27799": "جدول زمني معطل", "27800": "اضغط هنا لتحديد تاريخ البدء لإعداد المخزون والسعر", "27801": "اضغط على تاريخ البدء", "27802": "اض<PERSON>ط على تاريخ الانتهاء", "27803": "هل تريد تعديل المخزون والأسعار لجميع التواريخ المحددة؟", "27804": "نعم", "27805": "لا يمكن تعديل الجدول الزمني والمخزون لهذه الوحدة الفرعية بسبب مزامنتها تلقائيًا بواسطة الوحدة الرئيسية. يرجى الانتقال إلى الوحدة الرئيسية للتعديل.", "27806": "أنت بحاجة للانتقال إلى {Total Inventory & Fixed Price Setting} للتعديل", "27807": "سيؤدي تعديل الجدول الزمني / مخزون هذه الوحدة الرئيسية أيضًا إلى تغيير الجدول الزمني/ المخزون لجميع الوحدات الفرعية المرتبطة. هل تريد المواصلة؟", "27808": "هل أنت متأكد أنك تريد حذف الجداول المختارة؟ سيؤدي هذا فقط إلى إزالة المخزون والسعر.", "27809": "حالة الجدول الزمني", "27810": "إذا كانت فارغة", "27811": "جدول فارغ", "27812": "لم يتم العثور على بيانات المخزون والسعر لهذا الجدول الزمني", "27813": "جدول غير فارغ", "27814": "تم حفظ بيانات المخزون والسعر لهذا الجدول الزمني", "27815": "لا يمكن تعديل الجدول الزمني / مخزون هذه الوحدة الفرعية لأنها تمت مزامنتها تلقائيًا مع الوحدة الرئيسية. يمكنك تعديل الوحدة الرئيسية.", "27816": "", "27817": "سيؤدي تعديل الجدول الزمني أو المخزون الخاص بهذه الوحدة الرئيسية إلى المزامنة التلقائية مع جميع الوحدات الفرعية المرفقة. هل تريد المواصلة؟", "27818": "هل تريد التعديل؟", "27819": "", "27839": "", "27840": "", "27841": "الرجاء إزالة العلاقة المشتركة الحالية أولا", "27978": "الرجاء إلغاء نشر هذه الوحدة أولاً", "27979": "سيؤدي إيقاف تشغيل وظيفة التمديد التلقائي إلى إزالة أي بيانات تمديد تلقائي موجودة في هذه الصفحة. هل أنت متأكد أنك تريد إيقاف تشغيله؟", "27980": "رقم مرجع الحجز", "27982": "الوقت", "27983": "وقت الحجز", "27984": "تاريخ المشاركة", "27985": "تحديد التاريخ", "27986": "ير<PERSON><PERSON> التحديد", "27987": "ي<PERSON><PERSON><PERSON> إدخال", "27988": "الشخص الذي يمكن الاتصال به", "27989": "علامة اللون", "27994": "آخر 7 أيام", "27995": "<PERSON><PERSON><PERSON> 30 يومًا", "27996": "اليوم", "27997": "غداً", "27998": "حالة الحجز", "27999": "", "28000": "ملاحظة", "28001": "مبا<PERSON>ر", "28002": "الوقت الحالي أقل من 24 ساعة من وقت المشاركة", "28003": "متأخر", "28004": "لقد تجاوز وقت تأكيد الحجز وقت التأكيد المحدد في النظام", "28005": "معلومات النشاط", "28006": "معلومات المشاركة", "28007": "معلومات التأكيد", "28008": "معلومات الاتصال", "28009": "معلومات اخرى", "28010": "رقم النشاط", "28011": "اسم النشاط", "28012": "اسم الباقة", "28013": "مبلغ سعر التكلفة", "28014": "وقت المشاركة", "28015": "وحدة", "28016": "<PERSON><PERSON><PERSON> الوحدات", "28017": "نوع القسيمة", "28018": "وقت التأكيد", "28019": "الاسم الكامل", "28020": "الب<PERSON>يد الإلكتروني", "28021": "رقم الهاتف", "28022": "\"معلومات أخرى\" عامة", "28023": "\"معلومات أخرى\" غير معيارية", "28024": "تأكيد", "28025": "<PERSON><PERSON><PERSON>", "28026": "إعادة التأكيد", "28027": "تعديل", "28028": "أدخل معلومات القسيمة", "28030": "أدخل معلومات الملاحظات إذا لزم الأمر. ستظهر على قسيمة المستخدم.", "28031": "يرجى استخدام {language} (اللغة التي اختارها المستخدم). إذا لم تستطع ذلك، يرجى استخدام اللغة الإنجليزية.", "28032": "وقت التوصيل", "28033": "موقع التوصيل / الالتقاء (بالإنجليزية):", "28034": "موقع التوصيل / الالتقاء (باللغة المحلية)", "28035": "تأكيد", "28036": "إلغاء", "28037": "رقم القسيمة.", "28038": "مرفقات", "28047": "اللغة التي اختارها المستخدم", "28048": "معلومات القسيمة", "28084": "يُرجى تحديد العلاقة المشتركة لمنتجات Supply API التي تتم مزامنة مخزونها تلقائيًا بواسطة واجهة برمجة التطبيقات، لأن الاندماج لا يزال في مستوى الباقة وسيتم تحديث المخزون فقط إلى وحدة SKU الرئيسية (وليس جميع وحدات SKU) في هذه الباقة على المدى القصير", "28154": "", "28155": "", "28156": "", "28157": "", "28158": "المخزون المحجوز / المتبقي", "28232": "هل تحتاج إلى مساعدة في إدارة الجدول الزمني أو المخزون أو السعر؟ <a href=\\MULTILANG_URL_PLACEHOLDER\\\">Download the Guidebook</a>.\"", "28235": "اللغة المنشورة", "28236": "التبديل لللغة القابلة للتحرير لهذا النشاط", "28237": "", "28238": "", "28239": "", "28240": "", "28241": "", "28242": "", "28243": "تنبيه: لا يمكن تغيير اللغة القابلة للتعديل إلا مرة واحدة في الأسبوع", "28244": "", "28245": "", "28246": "منشئ النشاط", "28247": "منشئ الباقة", "28248": "المخزون المحجوز / المتبقي", "28249": "", "28250": "", "28251": "", "28252": "", "28253": "", "28254": "", "28255": "", "28256": "", "28257": "", "28259": "", "28260": "Can only open to merchant when activity proceed to published/unpublished status", "28261": "لغة قابلة للتعديل", "28298": "", "28309": "تم التأكيد خلال {hours} ساعات", "28311": "أ<PERSON><PERSON>ض", "28312": "بنفسجي", "28313": "أصفر", "28314": "أخضر", "28444": "", "28445": "", "28632": "", "28633": "", "28635": "", "28638": "", "28640": "", "28679": "", "28681": "نسخ الوحدة", "28682": "", "28686": "", "28690": "", "28691": "This step is completed on the old admin page", "28692": "You can open the old admin page to edit", "28693": "Open old admin", "28694": "This step is not completed on the old admin page", "28695": "Opening the old admin page", "28696": "The content is currently being edited on the old admin page", "28697": "This step is completed on the old admin page", "28698": "The old admin page is closed", "28699": "Something isn't right with the URL. Please check the URL again.", "28700": "The old admin page is closed. Please make sure all the required fields are completed in the old admin.", "28701": "", "28792": "تفعيل؟", "28793": "مفعّل", "28794": "معطل", "28795": "المخزون المحجوز هو عدد المخزون الذي تم حجزه بواسطة المستخدم (بما في ذلك أي حجوزات غير مؤكدة)", "28799": "مساعدة", "28800": "", "28810": "تنازل", "28851": "Only editable with AM access", "28852": "", "28910": "لا يمكن تعديل هذه الخانة لأنها مرتبطة بواجهة برمجة التطبيقات المدمجة وسيتم تحديثها تلقائيًا", "28917": "", "28958": "\"Other info\" hint", "28959": "", "28973": "", "28984": "رسوم التوصيل", "28985": "", "28986": "", "28987": "", "28988": "", "28989": "", "28990": "", "28991": "", "28992": "", "28993": "لا يمكن إضافة أو نشر وحدة SKU جديدة (يمكن نشر 8 وحدات SKU فقط على الأكثر)", "28994": "{num} يوم (أيام) مقدمًا", "29043": "", "29045": "", "29046": "", "29052": "لا يمكنك إضافة وحدات جديدة بينما الوحدات في هذه الصفحة مفتوحة. اضغط في أي مكان على بطاقة الوحدة لإغلاقها.", "29093": "", "29094": "", "29095": "إذا كان الجدول مرئيًا في الواجهة الأمامية ، فسيكون الجدول مرئيًا ويمكن للمستخدمين حجزه عند نشر الوحدة", "29147": "", "29148": "", "29149": "", "29155": "", "29156": "", "29157": "", "29158": "", "29159": "", "29160": "", "29161": "", "29162": "", "29163": "", "29164": "", "29165": "", "29166": "إلغاء", "29167": "", "29168": "", "29169": "الباقة", "29170": "", "29171": "", "29172": "", "29173": "", "29174": "", "29175": "", "29176": "", "29177": "", "29178": "", "29179": "", "29180": "", "29181": "", "29182": "", "29183": "", "29184": "", "29185": "", "29186": "", "29187": "", "29194": "تراجع", "29195": "", "29201": "أنت بحاجة للانتقال إلى \"إجمالي المخزون وإعدادات السعر الثابت\" للقيام بالتحديث", "29202": "يرجى الذهاب إلى \\إعدادات إجمالي المخزون \\\" للتعديل \"", "29203": "يرجى الذهاب إلى \\إعدادات المخزون الثابت \\\" للتعديل \"", "29251": "عذرًا، هناك عدد كبير جدًا من النتائج (⁦{num1}⁩ جداول) لعرضها. يمكن عرض ما يصل إلى ⁦{num2}⁩. يرجى تغيير الفلتر الخاص ك لإظهار نتائج أقل, مثل تحديد مدة أقصر، وحدات أقل ، إلخ.", "29252": "الرزمة الحالية", "29254": "تم التحديث بنجاح!", "29257": "فلترة الأعمدة", "29258": "نوصي باستخدام \\إضافة/التعديل بالجملة\\\" لإضافة أو تغيير الجداول والأسعار والمخزون\"", "29260": "", "29261": "", "29262": "", "29263": "", "29264": "", "29265": "", "29266": "", "29267": "", "29268": "لا يمكن نشر هذه الوحدة دون جدول متوفر / قابل للحجز", "29269": "", "29270": "", "29271": "", "29280": "الحفظ كمسودة", "29417": "", "29434": "يمكنك تطبيق الجدول الزمني حسب يوم الأسبوع دون وقت مشاركة / مغادرة محدد", "29435": "يمكنك تطبيق الجدول الزمني حسب يوم الأسبوع مع وقت مشاركة / مغادرة محدد. يمكن أن يختلف الوقت في اليوم.", "29438": "هل هناك حد لعدد الوحدات التي يمكن للمستخدم حجزها؟ إذا كان الأمر كذلك ، اختر \\محدود\\\" وأدخل الحد الأدنى / الحد الأقصى للوحدات.\"", "29439": "العملة المستخدمة في تحديد سعر التجزئة", "29440": "هذا هو المخزون المشترك لجميع الجداول داخل هذه الوحدة", "29441": "يرجى إدخال المخزون والسعر الذي سينعكس عندما يتم تمديد الجدول تلقائيًا (لن يتم تطبيق هذا المخزون والسعر على الجداول الحالية). <br/> <br/> عند استخدام التسعير الثابت ونموذج المخزون غير المحدود / الإجمالي، سيتم إدخال القيم تلقائيًا ولا يمكن تغييرها.", "29502": "", "29503": "", "29516": "", "29549": "", "29550": "", "29551": "", "29552": "", "29553": "", "29554": "", "29555": "", "29557": "", "29559": "", "29561": "", "29562": "", "29597": "", "29598": "", "29599": "", "29600": "", "29601": "", "29602": "", "29603": "", "29604": "", "29605": "", "29606": "", "29607": "", "29608": "", "29626": "عرض التفاصيل", "29632": "", "29633": "", "29634": "", "29635": "", "29636": "", "30114": "", "30127": "", "30128": "", "30131": "", "30132": "", "30133": "", "30134": "", "30135": "", "30154": "", "30162": "أقترح تغيير تاريخ المشاركة", "30163": "تاريخ المشاركة (بالتوقيت المحلي)", "30164": "التاجر يقترح تغيير تاريخ المشاركة إلى {date} (بالتوقيت المحلي)", "30165": "يرجى تحديد تاريخ المشاركة المقترح", "30225": "Please publish the units first", "30285": "الجدول الزمني الذي تم إنشاؤه مع المخزون", "30286": "الجدول الزمني الذي تم إنشاؤه مع المخزون", "30287": "كامل الشاشة", "30288": "الخروج من وضع الشاشة الكاملة", "30289": "حد<PERSON> الجدول (الجداو<PERSON>) لإجراء تعديلات في هذه الخانة. سيتم تطبيق التغييرات على جميع الجداول المحددة.", "30290": "تم تعديله ولكن لم يتم حفظه", "30291": "تم حذفه ولكن لم يتم حفظه", "30292": "نسخ قاعدة المهلة الزمنية والنموذج", "30293": "اختيار باقة للنسخ منها", "30294": "حد<PERSON> يومًا (أيام) للنسخ إليها", "30295": "فشل الحفظ", "30296": "/{Monday_12_00}⁦{Monday_14_00}⁩ تُستخدم حاليًا في الوحدات المذكورة أدناه. يرجى حذف هذه العناصر قبل إجراء أي تغييرات.", "30297": "", "30298": "هل أنت متأكد أنك تريد التبديل من \\باليوم\\\" إلى \\\"بالوقت-00:00\\\"؟\"", "30299": "", "30300": "اضغط على\\إضافة\\\" لإنشاء وحدة جديدة\"", "30301": "اض<PERSON>ط على خانة لتعديلها", "30409": "", "30432": "", "30433": "", "30434": "", "30435": "", "30436": "", "30437": "", "30438": "", "30439": "", "30440": "", "30441": "", "30442": "", "30443": "", "30444": "", "30526": "", "30527": "تذكرة بتاريخ ثابت", "30528": "", "30529": "", "30530": "", "30531": "", "30532": "", "30533": "", "30534": "", "30535": "", "30536": "", "30537": "", "30538": "", "30539": "", "30540": "", "30541": "", "30542": "", "30543": "", "30544": "", "30545": "", "30546": "", "30547": "", "30548": "", "30549": "", "30550": "", "30551": "", "30552": "", "30553": "", "30554": "", "30555": "", "30556": "", "30557": "", "30558": "", "30559": "", "30560": "", "30561": "", "30562": "", "30607": "", "30608": "", "30609": "", "30717": "", "30718": "If switched on, packages will appear as package cards on activity pages. Only activities with a \"no attributes matching\" status can use this function. We recommend using this function only if the activity has a low number of packages.", "30720": "", "30722": "", "30924": "", "31139": "", "42681": "أرقام SKU المطبقة للحجز", "42682": "كم عدد المنتجات التي يمكن حجزها؟", "42683": "", "42684": "", "42685": "", "42686": "", "42687": "", "42688": "", "42690": "", "44199": "", "44200": "", "44201": "", "44202": "", "44203": "", "44206": "", "44207": "", "44208": "", "44233": "", "44291": "يرجى إدخا<PERSON> عدد المنتجات التي يمكن حجزها لوحدة SKU الجديد", "44333": "تحميل", "44907": "", "44908": "", "44909": "", "44910": "", "44911": "", "44912": "", "44913": "", "44914": "", "44915": "", "44916": "", "44919": "", "44920": "", "44921": "", "44922": "", "44923": "", "44924": "", "44925": "", "44926": "", "44927": "", "44928": "", "44929": "", "44930": "", "44931": "", "44932": "", "44950": "البحث حسب رقم النشاط (يمكنك فقط اختيار 1 رقم نشاط)", "45672": "", "45673": "", "45674": "", "45675": "", "45676": "", "45677": "", "45678": "", "45679": "", "45680": "", "45681": "", "45682": "", "45683": "", "45684": "", "45688": "", "45689": "", "45737": "عملة التجزئة", "45738": "Retail currency", "47523": "", "47524": "", "47525": "", "47526": "", "47569": "يرجى استخدام نوع ملفات صحيح", "47784": "العربية", "48066": "إصدار (مع \"معلومات إضافية\")", "48068": "حالة الموافقة على التكلفة", "48069": "مرفو<PERSON>", "48070": "سيتم ارساله", "48071": "تمت الموافقة عليه", "48072": "قيد الانتظار", "48073": "الوحدة لديها مسودة أسعار التكلفة. يمكنك مسح المسودات أو إرسال مسودة التكاليف للموافقة عليها.", "48074": "إرسال التكلفة", "48075": "م<PERSON><PERSON> المسودات", "48076": "الاطلاع على حالة الموافقة", "48077": "التكلفة التي تم إدخالها مسبقًا لا تزال في انتظار التأكيد ويمكن سحبها", "48078": "س<PERSON><PERSON>", "48079": "الاطلاع على حالة الموافقة", "48080": "تم رفض التكلفة وما زالت موجودة كمسودة. راجع حالة الموافقة لمعرفة سبب الرفض. يمكنك مسح المسودات لعرض التكاليف المنشورة أو إعادة إرسال مسودة التكاليف للموافقة عليها.", "48081": "إعادة إرسال التكلفة", "48082": "م<PERSON><PERSON> المسودات", "48083": "الاطلاع على حالة الموافقة", "48084": "هل أنت متأكد أنك تريد مسح المسودات؟ سيتم إغلاق تذكرة الموافقة ولا يمكن استعادتها.", "48085": "م<PERSON><PERSON> المسودات", "48086": "العودة", "48087": "سيؤدي تحديد سعر التكلفة المنشور إلى عرض سعر التكلفة المستخدم حاليًا", "48088": "مسودة أسعار التكلفة", "48089": "أسعار التكلفة المنشورة", "48090": "إرسال التكلفة", "48091": "عرض التفاصيل", "48092": "إرسال التكلفة", "48093": "إلغاء", "48094": "<PERSON>س<PERSON> المواسم", "48095": "عرض ترويجي", "48096": "تغيير التكلفة/الاستراتيجية التشغيلية", "48097": "رقم التذكرة:", "48098": "النوع:", "48099": "تغيير السعر", "48100": "اللغة", "48101": "الصينية", "48102": "تم إرساله:", "48103": "رقم/اسم التاجر:", "48104": "مرفو<PERSON>", "48105": "سيتم ارساله", "48106": "تمت الموافقة عليه", "48107": "قيد الانتظار", "48108": "س<PERSON><PERSON>", "48109": "<PERSON><PERSON><PERSON>", "48110": "الموافقة", "48111": "اسم النشاط", "48112": "اسم الباقة", "48113": "وحدة:", "48114": "رقم الوحدة:", "48115": "نوع الوحدة:", "48116": "الحالة:", "48117": "الصلة:", "48118": "تمديد تلقائي", "48119": "الفترة الزمنية", "48120": "مسودة أسعار التكلفة", "48121": "سعر التكلفة المنشور", "48122": "سعر البيع الجديد", "48123": "مسودة العمولة", "48124": "SSP", "48125": "سعر البيع المنشور", "48126": "سعر العمولة المنشور", "48127": "التكلفة", "48128": "التاريخ", "48129": "الفترة الزمنية", "48130": "أسبوعياً", "48131": "الكل", "48132": "كل أحد", "48133": "كل اثنين", "48134": "كل ثلاثاء", "48135": "كل أربعاء", "48136": "كل خميس", "48137": "كل جمعة", "48138": "كل سبت", "48139": "ب<PERSON><PERSON>", "48140": "إعادة الضبط", "48141": "عرض التقويم", "48142": "عرض القائمة", "48143": "الفلترة حسب الجدول الزمني", "48144": "مسودة أسعار التكلفة", "48145": "سعر التكلفة المنشور", "48146": "اختر الكل", "48147": "مسودة سعر البيع", "48148": "مسودة العمولة", "48149": "SSP", "48150": "سعر البيع المنشور", "48151": "سعر العمولة المنشور", "48152": "سوف تحتاج إلى الموافقة على / رفض أي تذاكر معلقة متعلقة بهذه الوحدة أولاً", "48153": "تفاصيل الموافقة", "48154": "تم العثور على تذاكر قيد الانتظار", "48155": "تحتوي الباقة على تذاكر قيد الانتظار مقدمة من التجار حول تغيير السعر. سيؤدي تغيير نموذج السعر إلى إغلاق جميع التذاكر تلقائيًا. متابعة؟", "48156": "تغيير", "48157": "إلغاء", "48158": "تم العثور على تذاكر قيد الانتظار", "48159": "تحتوي الباقة على تذاكر قيد الانتظار مقدمة من التجار حول تغيير السعر. سيؤدي تغيير نموذج السعر إلى إغلاق جميع التذاكر تلقائيًا. متابعة؟", "48160": "تغيير", "48161": "إلغاء", "48173": "ير<PERSON>ى تحديد نشاط قبل الإصدار", "48218": "مغلق", "48219": "حالة الموافقة على النشاط", "48220": "حالة الموافقة على الباقة", "48221": "كمية الوحدة", "48222": "رقم - اسم الوحدة", "48223": "حالة الوحدة", "48224": "قائمة الموافقة", "48225": "السعر القائم على التقويم", "48226": "رقم التذكرة", "48227": "نوع التذكرة", "48228": "نشاط جديد", "48229": "", "48230": "حالة التذكرة", "48231": "", "48246": "", "48286": "", "48290": "", "48291": "", "48292": "", "48293": "", "48294": "", "48295": "", "48296": "", "48297": "", "48298": "", "48468": "The inventory type of this package has been chosen as \"Inventory\". Therefore the schedule and inventory won't be editable and can only be updated by the data from the PIC system", "48475": "The merchants of the units are different. Please make sure the unit has the same merchant ID as the main unit has before creating sharing relationship", "49362": "سعر التكلفة هو المبلغ الذي تدفعه كلوك للتجار", "49363": "يظهر المخزون عدد التذاكر/المنتجات المتاحة للبيع", "49364": "", "49365": "عند اختياره، سيعرض التقويم/القائمة التكاليف التي يجب تقديمها للموافقة عليها", "49366": "تحتوي الوحدات على تذاكر معلقة مقدمة من التجار حول تغيير التكلفة. ستغلق وحدات المشاركة تلقائيًا جميع تذاكر هذه الوحدة الفرعية. هل تود المواصلة؟", "49403": "", "49588": "", "49651": "", "49826": "", "54043": "", "54044": "", "54045": "", "54054": "", "70252": "تفاصيل التذكرة", "72048": "يقدم التاجر سعر التجزئة كسعر البيع المقترح أو السعر الرسمي للمنتجات في السوق", "72049": "سعر التكلفة هو المبلغ الذي تدفعه كلوك للتجار", "72169": "يناير", "72170": "فبراير", "72171": "مارس", "72172": "أب<PERSON>يل", "72173": "مايو", "72174": "يونيو", "72175": "يوليو", "72176": "أغسطس", "72177": "سبتمبر", "72178": "أكتوبر", "72179": "نوفمبر", "72180": "ديسمبر", "72518": "لم يتم العثور على أي بيانات بناء على الكلمات التي استخدمتها للبحث", "72753": "أنشطة التجارب", "72856": "هذا النوع من التذاكر غير مدعوم في التطبيق. يمكنك التحقق من مزيد من التفاصيل على منصة الويب", "72857": "See Approval detail on web", "73053": "", "73054": "", "73055": "", "73056": "", "73057": "", "73058": "", "73059": "", "73060": "", "73061": "", "73062": "", "73063": "", "73064": "", "73065": "", "73066": "", "73067": "", "73068": "", "73069": "", "73070": "", "73071": "", "73072": "", "73073": "", "73074": "", "73075": "", "73076": "", "73077": "", "73078": "", "73079": "", "73080": "", "73081": "", "73082": "", "73083": "", "73084": "", "73085": "", "73086": "", "73087": "", "73088": "", "73089": "", "73090": "", "73091": "", "73092": "", "73093": "", "73094": "", "73095": "", "73096": "", "73097": "", "73098": "", "73099": "", "73100": "", "73101": "", "73102": "", "73103": "", "73104": "", "73105": "", "73106": "", "73107": "", "73108": "", "73109": "", "73110": "", "73111": "", "73112": "", "73113": "", "73114": "", "73115": "", "73116": "", "73172": "", "73174": "", "73175": "", "73176": "", "73177": "", "73178": "", "73179": "", "73180": "", "73181": "", "73182": "", "73183": "", "73184": "", "73185": "", "73186": "", "73188": "", "73189": "", "73190": "", "73192": "", "73193": "", "73228": "حدد التاريخ لتمكين التعديل بالجملة", "73229": "لديك المخزون والسعر", "73231": "غير مفعل", "73246": "", "73247": "", "73248": "", "73249": "Please unpublish this combo unit first before making changes on the standalone units", "73250": "", "73518": "", "73519": "", "73520": "", "73521": "", "73522": "", "73523": "", "73565": "الباقات", "73566": "سجل العرض", "73567": "تفاصيل التذكرة", "73568": "عرض تفاصيل التذكرة", "73569": "تذاكر", "73570": "الموسمية", "73571": "ير<PERSON>ى إدخال ملاحظة", "73572": "<PERSON><PERSON><PERSON> الملاحظات لإرسال التكلفة", "73573": "قائمة الباقات", "73574": "توضيح", "73575": "رابط خاطئ", "73899": "", "73900": "", "73901": "", "73904": "", "73965": "", "73966": "", "73967": "", "73968": "", "73969": "", "73970": "", "73971": "", "73972": "", "73973": "", "73974": "", "73975": "", "73976": "", "73977": "", "73978": "", "73979": "", "73980": "", "73981": "", "74024": "", "74026": "", "74037": "لا يوجد مخزون أو قاعدة سعر محددة", "74248": "", "74249": "", "74250": "", "74289": "قائمة الباقة (في التطبيق فقط)", "74290": "قائمة الموافقة (في التطبيق فقط)", "74291": "سجل الأحداث (في التطبيق فقط)", "74292": "معاينة (في التطبيق فقط)", "74294": "", "74487": "", "74529": "", "75063": "", "75064": "", "75065": "", "75803": "", "76166": "", "76449": "", "76450": "", "76451": "", "76452": "", "76453": "", "76454": "", "76455": "", "76456": "", "76457": "", "76458": "", "76459": "", "76460": "", "76461": "", "76462": "", "76463": "", "76464": "", "76465": "", "76466": "", "76467": "", "76468": "", "76469": "", "76470": "", "76471": "", "76472": "", "76473": "", "76474": "", "76475": "", "76476": "", "76477": "", "76478": "", "76479": "", "76480": "", "76481": "", "76482": "", "76483": "", "76484": "", "76485": "", "76486": "", "76487": "", "76488": "", "76489": "", "76490": "", "76491": "", "76492": "", "76493": "", "76494": "", "76495": "", "76496": "", "76497": "", "76498": "", "76499": "", "76500": "", "76501": "", "76502": "", "76503": "", "76504": "", "76505": "", "76506": "", "76507": "", "76508": "", "76509": "", "76510": "", "76511": "", "76512": "", "76513": "", "76514": "", "76515": "", "76516": "", "76517": "", "76518": "", "76519": "", "76520": "", "76521": "", "76522": "", "76523": "", "76524": "", "76525": "", "76526": "", "76527": "", "76528": "", "76529": "", "76530": "", "76531": "", "76532": "", "76533": "", "76534": "", "76535": "", "76536": "", "76537": "", "76538": "", "76539": "", "76540": "", "76541": "", "76542": "", "76543": "", "76544": "", "76545": "", "76546": "", "76547": "", "76548": "", "76549": "", "76550": "", "76551": "", "76552": "", "76553": "", "76554": "", "76555": "", "76556": "", "76557": "", "76558": "", "76559": "", "76560": "", "76561": "", "76562": "", "76563": "", "76564": "", "76570": "", "76683": "", "76684": "", "76685": "", "76686": "", "76687": "", "76994": "", "76995": "", "76996": "", "77127": "", "77128": "", "77129": "", "77130": "", "77131": "", "77132": "", "77133": "", "77416": "", "77529": "", "77574": "Unavailable channels", "77684": "", "77685": "", "77688": "Are you sure to end it? By clicking it, you'll change the \"end time\" of this rate to now", "78220": "", "78221": "", "78223": "", "78232": "", "78243": "<span style=\"font-weight:bold;\">Heads up</span>: From end Sep 2022 (after Klook Rewards project launched), Gold members can get 3x credits (normal members get 1x credits). Besides, the credits that we give for bookings will be revised from 1% to 0.6%, so system will auto x 0.6 based on your original settings.", "78257": "قسيمة المسافر للأنشطة + كود المسافر للأنشطة", "78258": "قسيمة المسافر للأنشطة + كود التاجر", "78259": "هذا هو الوقت الذي ستتوقف المسافر للأنشطة عن قبول الحجوزات من المستخدمين (حسب التوقيت المحلي). أقرب وقت توقف يمكنك تعيينه هو \"اليوم على 00:00\".<br/>نصيحة: وجود مهلة أقصر يمكن بها قبول الحجوزات حتى اللحظة الأخيرة لزيادة مبيعاتك بأكبر قدر ممكن.", "78260": "Experiment and group name", "78261": "Group name", "78264": "e.g. abname1:group1,abname1:group2", "78267": "use \",\" to separate multiple names", "78302": "", "78303": "", "78304": "", "78325": "", "78326": "", "78327": "", "78328": "", "78329": "", "78330": "", "78331": "", "78332": "", "78333": "", "78334": "", "78335": "", "78342": "", "78344": "", "78345": "", "78346": "", "78347": "", "78437": "", "78463": "", "78464": "", "78465": "", "78466": "", "78467": "", "78468": "", "78469": "", "78470": "", "78477": "", "78482": "", "78492": "", "78493": "", "78494": "", "78606": "", "78607": "", "78608": "", "78659": "Unit info", "78835": "Click to review ticket", "78836": "", "78837": "", "78838": "", "78839": "", "78840": "", "78841": "", "78842": "", "78843": "", "78844": "", "78845": "", "78846": "", "78847": "", "78848": "", "78849": "", "78850": "", "78851": "", "78852": "", "78853": "", "78854": "", "78855": "", "78856": "", "78857": "", "78858": "", "78859": "", "78860": "", "78861": "", "78862": "", "78863": "", "78864": "", "78865": "", "78866": "", "78867": "", "78868": "", "78869": "", "78870": "", "78871": "", "78872": "", "78873": "", "78874": "", "78875": "", "78876": "", "78877": "", "78878": "", "78879": "", "78880": "", "78881": "", "78882": "", "78883": "", "78884": "", "78885": "", "78886": "", "78887": "", "78888": "", "78889": "", "78890": "", "78891": "", "78892": "", "78893": "", "78894": "", "78895": "", "78896": "", "78897": "", "78898": "", "78899": "", "78900": "", "78901": "", "78902": "", "78903": "", "78904": "", "78905": "", "78906": "", "78907": "", "78908": "", "78909": "", "78910": "", "78911": "", "78912": "", "78913": "", "78914": "", "78915": "", "78916": "", "78917": "", "78918": "", "78919": "", "78920": "", "78921": "", "78922": "", "78923": "", "78924": "", "78925": "", "78926": "", "78927": "", "78928": "", "78929": "", "78930": "", "78931": "", "78932": "", "78933": "", "78934": "", "78935": "", "78936": "", "78937": "", "78938": "", "78939": "", "78940": "", "78941": "", "78942": "", "78943": "", "78944": "", "78945": "", "78946": "", "78947": "", "78948": "", "78949": "", "78950": "", "78951": "", "78952": "", "78953": "", "78957": "Headinfo", "78958": "Footinfo", "78959": "Itinerary reminder", "79049": "", "79326": "Create New Redirection", "79327": "Bulk Upload", "79328": "AID to AID", "79329": "AID to URL", "79330": "From AID", "79331": "To AID", "79332": "Reviews/Participants migrated?", "79333": "Applicable Language", "79338": "Applicable Version", "79345": "Redirection Type", "79346": "Advance Setting", "79394": "", "79395": "", "79396": "", "79397": "", "79416": "URL rules as follows：<br/>1.URL pathname should begin with https://<br/>2.If multi language mapping is not required for this redirection, just copy&paste the url in the input field<br/>3. If multi language mapping is required for this redirection, follow the format below when input URL<br/>{host}:https://klook.com<br/>{language}:klook language", "79417": "Ex: {host}{language}catering/product/77754-seamart", "79419": "Version format as follows:<br/>1. Version is only required for APP. EX:6.27.0<br/>2. The change will apply to all versions filled in or above", "79420": "Invalid URL format", "79422": "Invalid Version format", "79423": "Invalid AID{AID}", "79429": "\"From AID{AID1}\" and \"To AID{AID2}\" conflicted", "79430": "Duplicated redirection for AID{from_AID}", "79433": "Migrate Reviews", "79434": "Download Template", "79445": "Reviews and participants migration failed because AID{to_AID} has migrated reviews before", "79446": "No Access for reviews/participants migration. Please contact tech.", "79448": "Reviews migrated exceed total participants", "79449": "Reviews/participants migration timeout. Please try again later.", "79450": "System error. Please try again later.", "79452": "Redirection Tool", "79453": "To URL", "79606": "", "79625": "", "79627": "", "79628": "", "79629": "", "79630": "", "79631": "", "79632": "", "79633": "", "79634": "", "79635": "", "79636": "", "79637": "", "79638": "", "79639": "", "79640": "", "79641": "", "79642": "", "79643": "", "79644": "", "79645": "", "79646": "", "79647": "", "79648": "", "79649": "", "79650": "", "79651": "", "79652": "", "79653": "", "79656": "", "79657": "", "79658": "", "79659": "", "79660": "", "79661": "", "79662": "", "79663": "", "79664": "", "79667": "", "79668": "", "79669": "", "79670": "", "79671": "", "79672": "", "79673": "", "79674": "", "79675": "", "79676": "", "79677": "", "79678": "", "79679": "", "79680": "", "79681": "", "79682": "", "79683": "", "79684": "", "79685": "", "79686": "", "79687": "", "79688": "", "79690": "", "79692": "", "79699": "", "79700": "", "79793": "", "79794": "", "79795": "", "79928": "Please make sure the activity you want to do redirection had been unlisted.", "79938": "Applicable Conditions", "79939": "Success", "79940": "Failed", "79941": "In process", "79942": "Not executed", "79943": "Unknown", "79959": "Applicable Platform", "79960": "Please make sure the URL starts with https://, http:// or {host}{language}", "80073": "Cannot turn on the schedule-publish function when package is already published.", "80074": "Cannot turn on the schedule-unpublish function when package is already unpublished.", "80075": "Cannot use the schedule-publish & schedule-unpublish function when the activity/package is under \"Sales Suspended\" status", "80076": "Session Expiring Soon!", "80077": "{text} is editing this package, you can't make any change right now.", "80120": "You can select up to {num} photo(s) to bulk upload", "80126": "Do remember to set warm up time on \"Package info\" page - \"Auto publish/unpublish\" field to make this status consistent with package display on frontend", "80127": "The activity is set \"Sales suspended\" status - you'll need to change activity to \"Unpublished\" status first to enable package publish.", "80128": "Max. no. cannot set 0", "80201": "Unknown error, please check with the platform for error reason", "80270": "Warm up time cannot be changed 30mins before the set up time", "80277": "", "80278": "", "80294": "", "80295": "", "80297": "The activity is set \"Sales suspended\" status - you'll need to change activity to \"Unpublished\" status and publish at least 1 package first before publishing the activity", "80333": "", "80438": "", "80439": "", "80442": "", "80446": "", "80496": "", "80673": "", "80674": "", "80675": "", "80676": "", "80677": "", "80678": "", "80679": "", "80680": "", "80681": "", "80682": "", "80683": "", "80684": "", "80685": "", "80686": "", "80687": "", "80688": "", "80689": "", "80690": "", "80691": "", "80692": "", "80693": "", "80726": "", "80727": "", "80728": "", "80729": "", "80730": "", "80731": "", "80732": "", "80733": "", "80734": "", "80857": "", "80858": "", "80859": "", "80861": "", "80892": "", "80893": "", "80894": "", "80895": "", "80896": "", "80897": "", "80898": "", "80899": "", "80900": "", "80901": "", "80902": "", "80903": "", "80904": "", "80905": "", "80906": "", "80907": "", "80908": "", "80909": "", "80910": "", "80911": "", "80912": "", "80913": "", "80914": "", "80915": "", "80916": "", "80917": "", "80918": "", "80919": "", "80920": "", "80921": "", "80922": "", "80923": "", "80924": "", "80925": "", "80926": "", "80927": "", "80928": "", "80929": "", "80930": "", "80931": "", "81796": "", "81801": "", "81802": "", "81803": "", "81804": "", "81810": "", "81811": "", "81812": "", "81813": "", "81816": "", "81818": "", "81819": "", "81820": "", "81821": "", "81822": "", "81823": "", "81824": "", "81825": "", "81903": "", "81930": "", "81936": "", "82050": "", "82051": "", "82053": "", "82054": "", "82173": "", "82174": "", "82181": "", "82183": "", "82184": "", "82192": "", "82193": "", "82222": "", "82552": "", "82553": "", "82667": "", "82668": "", "82669": "", "82670": "", "82671": "", "82672": "", "82673": "", "82674": "", "82675": "", "82676": "", "82721": "", "82776": "", "82777": "", "82783": "", "82784": "", "82910": "", "82942": "", "82959": "", "82960": "", "83010": "", "83068": "", "83078": "", "83098": "", "83099": "", "83100": "", "83108": "", "83140": "", "83297": "", "83300": "", "83301": "", "83308": "", "83309": "", "83349": "", "83350": "", "83610": "", "83634": "", "83790": "", "83791": "", "83792": "", "83793": "", "83794": "", "83795": "", "83796": "", "83797": "", "83798": "", "83799": "", "83800": "", "83801": "", "83802": "", "83803": "", "83804": "", "83805": "", "83806": "", "83807": "", "83808": "", "83809": "", "83810": "", "83811": "", "83812": "", "83813": "", "83814": "", "83815": "", "83816": "", "83817": "", "83818": "", "83819": "", "83820": "", "83821": "", "83822": "", "83823": "", "83824": "", "83825": "", "83826": "", "83827": "", "83828": "", "83829": "", "83830": "", "83831": "", "83832": "", "83833": "", "83834": "", "83835": "", "83836": "", "83837": "", "83838": "", "83839": "", "83840": "", "83841": "", "83842": "", "83843": "", "83844": "", "83845": "", "83846": "", "83847": "", "83848": "", "83849": "", "83850": "", "83851": "", "83853": "", "83860": "", "83865": "", "83866": "", "83867": "", "83890": "", "83891": "", "83892": "", "83893": "", "83894": "", "83895": "", "83896": "", "83897": "", "83898": "", "83899": "", "83900": "", "83901": "", "83902": "", "83903": "", "83939": "", "83949": "", "83950": "", "83951": "", "83952": "", "83953": "", "83954": "", "83955": "", "83959": "", "83964": "", "83965": "", "84084": "", "84085": "", "84086": "", "84088": "", "84089": "", "84090": "", "84091": "", "84092": "", "84093": "", "84250": "", "84276": "", "84277": "", "84278": "", "84279": "", "84347": "", "84418": "", "85274": "", "85275": "", "85276": "", "85321": "", "85325": "", "85326": "", "85327": "", "85441": "", "85535": "", "85884": "", "85994": "", "87282": "", "87522": "", "88450": "", "88478": "", "88481": "", "88482": "", "88555": "", "88556": "", "88636": "", "88678": "", "88688": "", "88691": "", "88694": "", "88695": "", "88696": "", "88697": "", "88698": "", "88699": "", "88700": "", "88701": "", "88702": "", "88703": "", "88705": "", "88706": "", "88709": "", "88823": "", "88912": "", "88913": "", "89084": "", "89085": "", "89086": "", "89087": "", "89088": "", "89110": "", "89111": "", "89142": "", "89143": "", "89144": "", "89145": "", "89149": "", "89270": "", "89271": "", "89272": "", "91921": "", "91922": "", "92240": "", "92241": "", "92242": "", "92243": "", "92244": "", "92245": "", "92294": "", "92426": "", "93284": "", "93285": "", "93289": "", "95932": "", "95933": "", "96206": "", "96207": "", "96208": "", "96209": "", "98280": "", "98284": "", "98413": "", "98415": "", "98416": "", "98417": "", "98418": "", "98419": "", "98420": "", "98421": "", "98422": "", "98423": "", "98424": "", "98425": "", "98426": "", "98427": "", "98428": "", "98431": "", "98475": "", "98476": "", "98478": "", "100809": "", "100810": "", "101026": "", "101027": "", "101028": "", "101029": "", "101030": "", "101042": "", "101173": "", "101174": "", "101175": "", "101547": "", "101664": "", "102199": "", "102202": "", "102256": "", "102257": "", "102258": "", "102259": "", "102260": "", "102261": "", "102262": "", "102263": "", "102264": "", "102265": "", "102266": "", "102267": "", "102268": "", "102269": "", "102270": "", "102271": "", "102272": "", "102273": "", "102274": "", "102275": "", "102276": "", "102277": "", "102278": "", "102279": "", "102280": "", "102281": "", "102282": "", "102283": "", "102284": "", "102285": "", "102286": "", "102287": "", "102288": "", "102289": "", "102290": "", "102291": "", "102292": "", "102293": "", "102294": "", "102295": "", "102416": "", "102471": "", "102518": "", "103570": "", "103751": "", "103752": "", "103753": "", "103754": "", "103755": "", "103756": "", "103757": "", "103758": "", "103759": "", "103760": "", "103761": "", "103901": "", "103902": "", "103903": "", "104017": "", "104018": "", "104019": "", "104020": "", "104021": "", "104022": "", "104023": "", "104033": "", "104034": "", "104035": "", "104036": "", "104037": "", "104039": "", "104212": "", "104213": "", "104214": "", "104215": "", "104216": "", "104217": "", "104218": "", "104219": "", "104220": "", "104221": "", "104222": "", "104223": "", "104224": "", "104225": "", "104293": "", "104294": "", "104295": "", "104296": "", "104297": "", "104300": "", "104301": "", "105993": "", "106049": "", "106050": "", "106051": "", "106266": "", "106267": "", "106536": "", "106537": "", "106653": "", "106655": "", "107096": "", "107100": "", "107873": "", "107874": "", "107875": "", "107876": "", "107877": "", "107878": "", "107879": "", "107880": "", "107881": "", "107882": "", "107883": "", "107884": "", "107885": "", "107886": "", "107887": "", "107888": "", "107889": "", "107890": "", "107891": "", "107892": "", "107893": "", "107894": "", "107895": "", "107896": "", "107897": "", "107898": "", "107899": "", "107900": "", "107901": "", "107902": "", "107903": "", "107904": "", "107905": "", "107906": "", "107907": "", "107908": "", "107909": "", "107910": "", "107911": "", "107912": "", "107913": "", "107914": "", "107915": "", "107916": "", "107917": "", "107918": "", "107919": "", "107920": "", "107921": "", "107922": "", "107923": "", "107924": "", "107925": "", "107926": "", "107927": "", "107928": "", "107929": "", "107930": "", "107931": "", "107932": "", "107933": "", "107934": "", "107935": "", "107936": "", "107937": "", "107938": "", "107939": "", "107940": "", "107941": "", "107942": "", "107943": "", "107944": "", "107945": "", "107946": "", "107947": "", "107948": "", "108020": "", "108064": "", "108218": "", "108219": "", "108220": "", "108221": "", "108222": "", "108223": "", "108224": "", "108225": "", "108236": "", "108762": "", "108765": "", "108766": "", "108769": "", "108782": "", "108783": "", "108784": "", "108785": "", "108786": "", "108787": "", "108788": "", "108789": "", "108790": "", "108791": "", "108792": "", "108793": "", "108844": "", "110086": "", "110087": "", "110088": "", "110089": "", "110090": "", "110091": "", "110092": "", "110093": "", "110094": "", "110095": "", "110096": "", "110178": "", "110179": "", "110420": "", "110653": "", "110654": "", "111885": "", "111886": "", "111887": "", "112107": "", "112108": "", "112109": "", "112744": "", "112745": "", "112910": "", "114386": "", "114446": "", "114447": "", "114448": "", "114449": "", "115450": "", "115832": "", "115833": "", "117148": "", "118632": "", "118633": "", "118634": "", "118635": "", "118636": "", "118637": "", "118707": "", "118708": "", "118709": "", "118710": "", "119180": "", "119181": "", "119369": "", "119617": "", "119618": "", "119826": "", "119827": "", "119831": "", "119832": "", "119833": "", "119834": "", "119835": "", "119836": "", "119837": "", "119838": "", "119839": "", "119840": "", "119841": "", "120316": "", "120317": "", "120318": "", "120319": "", "120320": "", "120321": "", "120322": "", "120323": "", "120372": "", "120373": "", "120374": "", "120375": "", "120376": "", "120377": "", "120378": "", "120381": "", "120383": "", "120394": "", "120460": "", "120636": "", "120803": "", "121699": "", "121700": "", "121701": "", "121702": "", "121703": "", "121704": "", "121705": "", "121706": "", "121707": "", "121708": "", "121709": "", "121710": "", "121711": "", "121712": "", "121713": "", "121714": "", "121715": "", "121716": "", "121717": "", "121718": "", "121719": "", "121720": "", "121721": "", "121722": "", "121723": "", "121724": "", "121725": "", "121726": "", "121727": "", "121728": "", "121729": "", "121730": "", "121731": "", "121732": "", "121733": "", "121734": "", "121735": "", "121736": "", "121737": "", "121738": "", "121739": "", "121740": "", "121741": "", "121742": "", "121743": "", "121744": "", "121745": "", "121746": "", "121747": "", "121748": "", "121749": "", "121750": "", "121751": "", "121752": "", "121753": "", "121754": "", "121755": "", "121756": "", "121757": "", "121758": "", "121759": "", "121760": "", "121761": "", "121762": "", "121763": "", "121764": "", "121765": "", "121766": "", "121767": "", "121768": "", "121769": "", "121770": "", "121771": "", "121772": "", "121773": "", "121774": "", "121775": "", "121776": "", "121777": "", "121778": "", "121779": "", "121780": "", "121781": "", "121782": "", "121783": "", "121830": "", "122696": "", "122697": "", "122698": "", "122699": "", "122700": "", "122701": "", "123206": "", "123402": "", "123699": "", "123796": "", "123840": "", "124008": "", "124959": "", "124964": "", "124967": "", "125033": "", "125034": "", "125035": "", "125036": "", "125037": "", "125038": "", "125039": "", "125040": "", "125041": "", "125042": "", "125043": "", "125044": "", "125045": "", "125046": "", "125047": "", "125048": "", "125049": "", "125050": "", "125143": "", "125179": "", "125180": "", "125181": "", "125182": "", "125183": "", "125184": "", "125185": "", "125186": "", "125187": "", "125188": "", "125189": "", "125190": "", "125191": "", "125194": "", "125195": "", "125196": "", "125197": "", "125198": "", "125199": "", "125200": "", "125201": "", "125248": "", "125249": "", "125250": "", "130943": "", "130945": "", "130946": "", "130947": "", "130948": "", "130952": "", "130953": "", "130955": "", "130956": "", "130957": "", "135578": "", "141865": "", "142595": "", "142731": "", "142732": "", "142779": "", "143327": "", "159932": "", "159933": "", "160687": "", "160688": "", "161206": "", "161207": "", "161208": "", "161209": "", "161210": "", "161211": "", "161212": "", "161213": "", "161214": "", "161215": "", "161216": "", "161217": "", "161218": "", "161219": "", "161220": "", "161221": "", "161222": "", "161223": "", "161224": "", "161225": "", "161226": "", "161227": "", "161228": "", "161229": "", "161230": "", "161231": "", "166250": "", "166254": "", "166255": "", "166256": "", "166257": "", "166258": "", "166259": "", "166260": "", "166261": "", "166262": "", "166263": "", "166264": "", "166265": "", "166266": "", "166267": "", "166268": "", "166269": "", "166270": "", "166271": "", "166272": "", "166273": "", "166274": "", "166275": "", "166276": "", "166277": "", "166278": "", "166279": "", "166280": "", "166281": "", "166287": "", "166288": "", "166334": "", "166894": "", "167230": "", "167231": "", "167232": "", "167233": "", "167234": "", "167238": "", "167239": "", "167240": "", "167271": "", "167272": "", "167273": "", "167274": "", "167715": "", "167716": "", "168075": "", "168076": "", "168874": "", "170359": "", "170360": "", "170361": "", "170362": "", "170364": "", "170365": "", "170366": "", "170367": "", "170368": "", "170369": "", "170370": "", "170371": "", "170372": "", "170377": "", "170378": "", "170746": "", "170770": "", "171250": "", "171251": "", "171252": "", "171253": "", "171254": "", "172380": "", "172381": "", "172382": "", "172383": "", "172384": "", "172672": "", "172673": "", "172674": "", "172675": "", "172676": "", "172677": "", "172678": "", "172680": "", "172681": "", "172682": "", "172683": "", "172684": "", "172685": "", "172686": "", "172687": "", "172688": "", "172689": "", "173156": "", "173707": "", "173708": "", "174481": "", "174482": "", "174915": "", "174916": "", "174917": "", "174918": "", "174919": "", "174920": "", "174921": "", "174922": "", "174923": "", "174933": "", "174934": "", "174936": "", "174937": "", "174938": "", "174939": "", "174940": "", "174941": "", "174942": "", "174943": "", "174944": "", "174945": "", "174946": "", "174948": "", "174949": "", "174950": "", "174951": "", "174952": "", "174953": "", "174964": "", "174965": "", "174966": "", "174967": "", "174968": "", "174969": "", "174970": "", "174971": "", "174972": "", "174973": "", "174976": "", "174977": "", "174978": "", "174979": "", "174983": "", "174994": "", "174995": "", "174996": "", "174997": "", "174998": "", "175006": "", "175007": "", "175008": "", "175009": "", "175010": "", "175129": "", "176818": "", "176829": "", "176830": "", "176831": "", "176832": "", "177147": "", "177148": "", "177150": "", "177151": "", "177152": "", "177153": "", "177154": "", "177155": "", "177156": "", "192408": "", "192409": "", "192410": "", "192412": "", "192413": "", "192414": "", "192415": "", "192416": "", "192417": "", "192418": "", "192743": "", "193011": "", "193012": "", "193013": "", "193014": "", "193015": "", "193016": "", "193216": "", "193217": "", "193218": "", "193304": "", "193305": "", "193435": "", "193813": "", "193817": "", "193818": "", "193819": "", "193820": "", "194907": "", "196161": "", "196162": "", "196163": "", "196164": "", "196165": "", "196167": "", "196169": "", "196170": "", "196171": "", "196172": "", "196177": "", "196628": "", "196629": "", "196630": "", "196631": "", "196632": "", "196633": "", "196634": "", "196635": "", "196636": "", "196637": "", "196638": "", "196639": "", "196640": "", "196641": "", "196642": "", "196643": "", "196644": "", "196645": "", "196646": "", "196647": "", "196648": "", "196649": "", "196650": "", "196651": "", "196652": "", "196653": "", "196654": "", "196655": "", "196656": "", "196657": "", "196658": "", "196659": "", "196660": "", "196661": "", "196662": "", "196663": "", "196664": "", "196665": "", "196666": "", "196667": "", "196668": "", "196669": "", "196670": "", "196671": "", "196672": "", "196673": "", "196674": "", "196675": "", "196676": "", "196677": "", "196678": "", "196679": "", "196680": "", "196681": "", "196682": "", "196683": "", "196684": "", "196685": "", "196686": "", "196687": "", "196688": "", "196689": "", "196690": "", "196691": "", "196692": "", "196693": "", "196694": "", "196695": "", "196696": "", "196697": "", "196698": "", "196699": "", "196700": "", "196701": "", "196702": "", "196703": "", "196704": "", "196705": "", "196706": "", "196707": "", "196708": "", "196709": "", "196710": "", "196711": "", "196712": "", "196713": "", "196714": "", "196715": "", "196716": "", "196717": "", "196718": "", "196719": "", "196720": "", "196721": "", "196722": "", "196723": "", "196724": "", "196725": "", "196726": "", "196727": "", "196728": "", "196729": "", "196730": "", "196731": "", "196732": "", "196733": "", "196734": "", "196735": "", "196736": "", "196737": "", "196738": "", "196739": "", "196740": "", "196741": "", "196742": "", "196743": "", "196744": "", "196745": "", "196746": "", "196747": "", "196748": "", "196749": "", "196750": "", "196751": "", "196752": "", "196753": "", "196754": "", "196755": "", "196756": "", "196757": "", "196758": "", "196759": "", "196760": "", "196761": "", "196762": "", "196763": "", "196764": "", "196765": "", "196766": "", "196767": "", "196768": "", "196769": "", "196770": "", "196771": "", "196772": "", "196773": "", "196774": "", "196775": "", "196776": "", "196777": "", "196778": "", "196779": "", "196780": "", "196781": "", "196782": "", "196783": "", "196784": "", "196785": "", "196786": "", "196787": "", "196788": "", "196789": "", "196790": "", "196791": "", "196792": "", "196793": "", "196794": "", "196795": "", "196796": "", "196797": "", "196798": "", "196799": "", "196800": "", "196801": "", "196802": "", "196803": "", "196804": "", "196805": "", "196806": "", "196807": "", "196808": "", "196809": "", "196810": "", "196811": "", "196812": "", "196813": "", "196814": "", "196815": "", "196816": "", "196817": "", "196818": "", "196819": "", "196820": "", "196821": "", "196822": "", "196823": "", "196824": "", "196825": "", "196826": "", "196827": "", "196828": "", "196829": "", "196830": "", "196999": "", "197000": "", "197001": "", "197002": "", "197003": "", "197004": "", "197005": "", "197006": "", "197007": "", "197008": "", "197013": "", "197014": "", "197015": "", "197016": "", "197017": "", "197018": "", "197019": "", "197020": "", "197021": "", "197500": "", "197501": "", "197669": "", "197675": "", "197695": "", "197731": "", "197733": "", "197734": "", "197735": "", "197737": "", "197739": "", "197740": "", "197761": "", "197762": "", "198001": "", "198002": "", "198003": "", "198208": "", "198209": "", "198460": "", "198462": "", "198463": "", "198464": "", "198513": "", "198514": "", "198515": "", "198516": "", "198517": "", "198518": "", "198519": "", "198520": "", "198521": "", "198522": "", "198523": "", "198526": "", "198527": "", "198528": "", "198531": "", "198532": "", "198533": "", "198534": "", "198535": "", "198536": "", "198537": "", "198538": "", "198539": "", "198540": "", "198541": "", "198542": "", "198543": "", "198544": "", "198545": "", "198546": "", "198547": "", "198548": "", "198549": "", "198550": "", "198551": "", "198552": "", "198553": "", "198554": "", "198559": "", "198560": "", "198561": "", "198562": "", "198563": "", "198564": "", "198565": "", "198566": "", "198567": "", "198568": "", "198569": "", "198570": "", "198571": "", "198572": "", "198573": "", "198574": "", "198575": "", "198576": "", "198577": "", "198578": "", "198579": "", "198580": "", "198581": "", "198582": "", "198583": "", "198584": "", "198585": "", "198586": "", "198587": "", "198588": "", "198589": "", "198590": "", "198592": "", "198593": "", "198594": "", "198595": "", "198596": "", "198597": "", "198598": "", "198599": "", "198600": "", "198601": "", "198602": "", "198603": "", "198605": "", "198638": "", "198639": "", "198640": "", "198641": "", "198642": "", "198643": "", "198644": "", "198645": "", "198646": "", "198647": "", "198648": "", "198649": "", "198650": "", "198651": "", "198652": "", "198653": "", "198654": "", "198655": "", "198656": "", "198657": "", "198658": "", "198659": "", "198660": "", "198661": "", "198662": "", "198663": "", "198664": "", "198665": "", "198666": "", "198667": "", "198668": "", "198669": "", "198670": "", "198671": "", "198672": "", "198673": "", "198674": "", "198675": "", "198676": "", "198677": "", "198678": "", "198679": "", "198680": "", "198681": "", "198682": "", "198683": "", "198684": "", "198685": "", "198686": "", "198687": "", "198688": "", "198689": "", "198690": "", "198691": "", "198692": "", "198693": "", "198694": "", "198695": "", "198696": "", "198697": "", "198698": "", "198699": "", "198700": "", "198701": "", "198702": "", "198703": "", "198704": "", "198705": "", "198706": "", "198707": "", "198708": "", "198709": "", "198710": "", "198711": "", "198712": "", "198713": "", "198714": "", "198715": "", "198716": "", "198717": "", "198718": "", "198719": "", "198720": "", "198721": "", "198722": "", "198723": "", "198724": "", "198725": "", "198726": "", "198727": "", "198728": "", "198729": "", "198730": "", "198731": "", "198732": "", "198733": "", "198734": "", "198735": "", "198736": "", "198737": "", "198738": "", "198739": "", "198740": "", "198741": "", "198742": "", "198743": "", "198744": "", "198745": "", "198746": "", "198747": "", "198748": "", "198749": "", "198750": "", "198751": "", "198752": "", "198753": "", "198754": "", "198755": "", "198756": "", "198757": "", "198758": "", "198759": "", "198760": "", "198761": "", "198762": "", "198763": "", "198764": "", "198765": "", "198766": "", "198767": "", "198768": "", "198769": "", "198770": "", "198771": "", "198772": "", "198773": "", "198774": "", "198775": "", "198776": "", "198777": "", "198778": "", "198779": "", "198780": "", "198781": "", "198782": "", "198783": "", "198784": "", "198785": "", "198786": "", "198787": "", "198788": "", "198789": "", "198790": "", "198791": "", "198792": "", "198793": "", "198794": "", "198795": "", "198798": "", "198799": "", "198800": "", "198801": "", "198802": "", "198803": "", "198804": "", "198805": "", "198806": "", "198807": "", "198808": "", "198809": "", "198810": "", "198811": "", "198812": "", "198813": "", "198814": "", "198815": "", "198819": "", "198822": "", "198823": "", "198824": "", "198825": "", "198826": "", "198827": "", "198828": "", "198829": "", "198830": "", "198831": "", "198832": "", "198833": "", "198834": "", "198835": "", "198836": "", "198837": "", "198838": "", "198839": "", "198840": "", "198841": "", "198842": "", "198843": "", "198844": "", "198845": "", "198846": "", "198847": "", "198848": "", "198849": "", "198850": "", "198851": "", "198852": "", "198853": "", "198854": "", "198855": "", "198856": "", "198857": "", "198858": "", "198859": "", "198860": "", "198861": "", "198862": "", "198863": "", "198864": "", "198865": "", "198866": "", "198867": "", "198868": "", "198869": "", "198870": "", "198871": "", "198872": "", "198873": "", "198874": "", "198875": "", "198876": "", "198877": "", "198878": "", "199044": "", "199484": "", "199485": "", "199486": "", "199487": "", "199488": "", "199489": "", "199490": "", "199491": "", "199901": "", "199902": "", "199904": "", "199905": "", "199906": "", "199907": "", "199928": "", "200047": "", "200048": "", "200078": "", "200107": "", "200121": "", "200122": "", "200123": "", "200124": "", "200165": "", "200166": "", "200167": "", "200168": "", "200169": "", "200170": "", "200171": "", "200172": "", "200173": "", "200174": "", "200334": "", "200335": "", "200449": "", "200450": "", "200451": "", "200486": "", "200487": "", "200489": "", "200564": "", "200565": "", "200566": "", "200567": "", "200568": "", "200569": "", "200570": "", "200571": "", "200572": "", "200573": "", "200574": "", "200575": "", "200576": "", "200577": "", "200578": "", "200579": "", "200580": "", "200581": "", "200590": "", "200591": "", "200592": "", "200593": "", "200594": "", "200595": "", "200596": "", "200597": "", "200598": "", "200599": "", "200600": "", "200601": "", "200602": "", "200603": "", "200604": "", "200605": "", "200606": "", "200607": "", "200608": "", "200609": "", "200610": "", "200611": "", "200643": "", "200750": "", "200766": "", "200772": "", "200783": "", "200784": "", "200785": "", "200786": "", "200787": "", "200788": "", "200789": "", "200790": "", "200791": "", "200792": "", "200793": "", "200794": "", "200795": "", "200796": "", "200797": "", "200798": "", "200812": "", "200813": "", "200828": "", "200835": "", "200954": "", "201039": "", "201137": "", "201138": "", "201670": "", "201671": "", "201672": "", "201673": "", "201674": "", "201675": "", "204211": "", "204212": "", "204213": "", "204214": "", "204215": "", "204216": "", "204217": "", "204218": "", "204219": "", "204220": "", "204221": "", "204222": "", "204223": "", "204224": "", "204225": "", "204226": "", "204227": "", "204228": "", "204229": "", "204230": "", "204231": "", "204232": "", "204233": "", "204234": "", "204235": "", "204236": "", "204237": "", "204238": "", "204239": "", "204240": "", "204241": "", "204242": "", "204243": "", "204244": "", "204245": "", "204246": "", "204247": "", "204248": "", "204249": "", "204250": "", "204251": "", "204252": "", "204253": "", "204254": "", "204255": "", "204256": "", "204257": "", "204258": "", "204259": "", "204260": "", "204261": "", "204571": "", "204572": "", "204573": "", "204574": "", "204575": "", "205215": "", "205220": "", "205242": "", "205933": "", "205935": "", "205936": "", "206119": "", "206120": "", "206948": "", "206951": "", "206952": "", "206953": "", "206954": "", "206955": "", "206956": "", "206957": "", "206958": "", "206959": "", "207083": "", "207265": "", "207285": "", "208151": "", "208152": "", "208153": "", "208154": "", "208397": "", "208398": "", "208632": "", "208639": "", "208641": "", "208924": "", "208925": "", "208926": "", "208927": "", "208928": "", "208929": "", "208930": "", "208931": "", "208932": "", "208933": "", "208934": "", "208935": "", "208936": "", "208937": "", "208938": "", "208939": "", "208940": "", "208941": "", "208942": "", "208943": "", "208944": "", "208945": "", "208946": "", "208947": "", "208948": "", "208949": "", "208950": "", "208951": "", "208952": "", "208953": "", "208954": "", "208955": "", "208956": "", "208957": "", "208958": "", "208959": "", "208960": "", "208961": "", "208962": "", "208963": "", "208964": "", "208965": "", "208966": "", "209034": "", "209483": "", "209484": "", "209485": "", "209486": "", "209487": "", "209488": "", "209489": "", "209490": "", "209491": "", "209492": "", "209493": "", "209494": "", "209495": "", "209496": "", "209497": "", "209498": "", "209499": "", "209500": "", "209501": "", "209502": "", "209503": "", "209504": "", "209505": "", "209506": "", "209507": "", "209508": "", "209509": "", "209510": "", "209511": "", "209512": "", "209513": "", "209514": "", "209515": "", "209516": "", "209517": "", "209518": "", "209519": "", "209520": "", "209521": "", "209522": "", "209523": "", "209524": "", "209525": "", "209526": "", "209527": "", "209528": "", "209529": "", "209530": "", "209531": "", "209532": "", "209533": "", "209534": "", "209535": "", "209536": "", "209537": "", "209538": "", "209539": "", "209540": "", "209541": "", "209542": "", "209543": "", "209544": "", "209545": "", "209546": "", "209547": "", "209548": "", "209549": "", "209550": "", "209551": "", "209552": "", "209553": "", "209729": "", "209730": "", "209850": "", "210024": "", "210025": "", "210026": "", "210027": "", "210028": "", "210029": "", "210030": "", "210031": "", "210032": "", "210033": "", "210034": "", "210035": "", "210036": "", "210037": "", "210038": "", "210039": "", "210040": "", "210041": "", "210042": "", "210043": "", "210044": "", "210045": "", "210046": "", "210047": "", "210048": "", "210049": "", "210050": "", "210051": "", "210052": "", "210053": "", "210087": "", "210304": "", "210305": "", "210306": "", "210307": "", "210308": "", "210309": "", "210310": "", "210311": "", "210312": "", "210328": "", "210377": "", "210378": "", "210379": "", "210380": "", "210381": "", "210382": "", "210383": "", "210384": "", "210385": "", "210386": "", "210387": "", "210388": "", "210389": "", "210390": "", "210391": "", "210392": "", "210393": "", "210394": "", "210395": "", "210396": "", "210397": "", "210398": "", "210399": "", "210400": "", "210401": "", "210471": "", "210472": "", "210473": "", "210474": "", "210475": "", "210476": "", "210477": "", "210478": "", "210479": "", "210480": "", "210481": "", "210521": "", "210522": "", "210666": "", "210667": "", "210670": "", "210671": "", "210672": "", "210769": "", "210796": "", "210797": "", "210893": "", "210894": "", "211313": "", "211314": "", "211319": "", "211475": "", "211858": "", "211859": "", "211860": "", "211861": "", "211862": "", "211863": "", "211864": "", "211865": "", "211866": "", "211867": "", "212410": "", "213324": "", "214288": "", "214579": "", "214580": "", "214581": "", "214582": "", "214583": "", "214584": "", "214585": "", "214586": "", "214587": "", "214588": "", "214589": "", "214590": "", "214591": "", "214592": "", "214593": "", "214594": "", "214597": "", "214598": "", "214599": "", "214600": "", "214601": "", "214602": "", "214603": "", "214604": "", "214605": "", "214606": "", "214607": "", "214609": "", "214610": "", "214613": "", "214614": "", "214627": "", "214628": "", "214629": "", "214630": "", "214631": "", "214632": "", "214633": "", "214635": "", "214637": "", "214640": "", "214641": "", "214642": "", "214643": "", "214648": "", "214649": "", "214650": "", "214651": "", "214654": "", "214655": "", "214656": "", "214657": "", "214658": "", "214659": "", "214660": "", "214661": "", "214662": "", "214665": "", "214679": "", "214680": "", "214681": "", "214682": "", "214686": "", "214687": "", "214688": "", "214689": "", "214690": "", "214691": "", "214692": "", "214693": "", "214695": "", "214697": "", "214698": "", "214699": "", "214700": "", "214701": "", "214702": "", "214703": "", "214704": "", "214705": "", "214706": "", "214707": "", "214708": "", "214709": "", "214710": "", "214711": "", "214712": "", "214713": "", "214714": "", "214715": "", "214716": "", "214717": "", "214718": "", "214719": "", "214720": "", "214721": "", "214722": "", "214723": "", "214724": "", "214725": "", "214726": "", "214727": "", "214728": "", "214730": "", "214731": "", "214732": "", "214733": "", "214734": "", "214735": "", "214736": "", "214737": "", "214738": "", "214739": "", "214740": "", "214741": "", "214742": "", "214743": "", "214744": "", "214745": "", "214746": "", "214747": "", "214748": "", "214824": "", "214837": "", "214838": "", "214839": "", "214840": "", "214842": "", "214845": "", "214846": "", "214847": "", "214848": "", "214849": "", "214850": "", "214851": "", "214852": "", "214853": "", "214854": "", "214855": "", "214856": "", "214857": "", "214915": "", "214916": "", "215250": "", "filter_btn_text_apply": "تأكيد", "booking_report.details": "تفاصيل", "cancel": "إلغاء", "alter_page_traveler_inforamtion": "معلومات المسافر", "global.selected_date": "التواريخ المحددة:", "ob_instant": "تأكيد فوري", "package_unit_buy": "مطلوب للحجز", "global_week_abbr_tue": "الثلاثاء", "package_bulk_modification_default_price_setting": "الإعدادات الافتراضية", "global_failure": "إخفاق", "package_unit_name": "اسم الوحدة", "global_confirm": "تأكيد", "package_timeslot_by_daily": "يوميًا", "package_timeslot_title": "إضافة الفترة الزمنية والمخزون", "global_button_search": "ب<PERSON><PERSON>", "package_unit_publish": "النشر", "global_valid": "صالح", "taxonomy_dialog_currency": "العملة", "global_button_all": "الجميع", "package_schedule_rule": "اسم قاعدة الجدول الزمني", "package_timeslot_by_timeslot": "حسب الفترة الزمنية", "package_unit_type_price_warning": "يمكنك فقط تعديل السعر في صفحة \"التعديل بالجملة\" بعد حفظه.", "act_list_take_rate": "مب<PERSON>غ العمولة", "global_button_ok": "موافق", "global_week_fri": "الجمعة", "package_unit_limited_when_required": "يرجى اختيار \"محدود\" وتحديد الحد الأدنى إذا كانت الوحدة مطلوبة", "taxonomy_filter_city_no": "لا", "package_bulk_modification_inventory_tips": "ستتم مشاركة كمية المخزون لجميع وحدات SKU ضمن هذه الباقة", "reason_for_price_changing": "سبب تغير السعر", "global_selected": "تم اختياره", "package_unlimited": "<PERSON>ير محدود", "package_bulk_modification_title": "تعديل بالجملة", "SKU_ID": "معرف SKU", "global_copy_error": "خطأ في النسخ", "global_success": "تمت العملية بنجاح", "act_blocklist_clear_all": "<PERSON><PERSON><PERSON> ال<PERSON>", "package_price_modify_search": "ب<PERSON><PERSON>", "package_bulk_modification_delete_warning": "هل تريد حذف العناصر المختارة؟", "package_price_modify_reset": "إعادة تعيين", "package_timeslot_copy": "نسخ الجدول الزمني للباقة", "global_invalid": "غير صالح", "global_read": "<PERSON><PERSON><PERSON>", "pm_edit_cost": "تعديل السعر", "package_timeslot_by_day": "في اليوم", "taxonomy_dest_confirm": "تأكيد", "package_price_default_price_tips_2": "و", "package_unit_unpublish": "إلغاء النشر", "global_create_success": "تم الإنشاء بنجاح", "package_bulk_modification_disable_tips": "يرجى إضافة المخزون والسعر أولًا", "global_filter": "فلتر", "global_week_tue": "الثلاثاء", "act_save_fail": "فشل الحفظ", "package_timeslot": "المهلة الزمنية", "global_week_sun": "ال<PERSON><PERSON>د", "package_cut_off_time": "آخر موعد لاستلام الطلبات", "package_min_max_age": "الح<PERSON> الأدنى / الأقصى للسن", "package_price_units": "الوحدات", "package_unit_type": "نوع الوحدة", "global_cancel": "إلغاء", "package_inventory_unlimited": "<PERSON>ير محدود", "package_unit_age_number": "قيود السن", "global_button_save": "<PERSON><PERSON><PERSON>", "act_lan_all": "الجميع", "package_bulk_modification_cutoff_tips": "ستتم مشاركة آخر موعد لاستلام الطلبات لجميع وحدات SKU ضمن هذه الباقة", "package_schedule_autoextend_hint": "يتم التمديد التلقائي للفترة الزمنية اليومية ليوم واحد من آخر فترة زمنية موجودة وفقًا لقاعدة الجدول الزمني أعلاه. وسيبدأ النظام بالتمديد التلقائي اليوم", "quantity_special_inventory": "كمية المخزون الخاص", "package_unit_requirement_tips": "في حالة التشغيل، يتعين على المستخدم حجز هذا النوع من الوحدات", "global_create_error": "خطأ في الإنشاء", "global_dialog_title": "ملاحظة", "package_timeslot_date_range": "نطاق التاريخ", "SKU_Name": "اسم وحدة SKU", "package_list_unit_published": "تم نشره", "package_bulk_modification_cost_price_tips": "يمكن تعديله فقط بنفس سعر التكلفة", "global_sold_out": "نفدت الكمية", "global_week_abbr_sat": "السبت", "activity_special_inventory": "المخزون الخاص", "act_blocklist_select_all": "اختيار الكل", "timeslotlist_weekly": "أسبوعيًا", "package_timeslot_repeat": "التكرار", "package_list_unit_unpublished": "لم يتم نشره", "package_unit_min_num_when_required": "الرقم الأدنى للوحدة المطلوبة لا يمكن أن يكون 0", "package_copy_schedule_dialog_tips": "يرجى تحديد الباقة التي تريد نسخ الجدول الزمني", "global_delete_success": "تم الحذف", "global_week_abbr_fri": "الجمعة", "global_week_wed": "الأربعاء", "act_lan_both": "الجميع", "package_bulk_modification_edit_warning": "لا يمكن تعديله إلا لوحدة SKU الرئيسية: {SKU name}", "global_please_select": "ير<PERSON><PERSON> التحديد", "global_week_abbr_thu": "الخميس", "package_cut_off_time_hint": "هذا هو الوقت الذي ستتوقف <اسم الشركة> عن قبول الحجوزات من المستخدمين (محدد بالتوقيت المحلي). أقرب آخر موعد لاستلام الطلبات يمكنك تحديده هو \\\"اليوم 00:00\\\". \n\n نصيحة: وجود آخر موعد لاستلام الطلبات أقصر يعني أنه يمكنك قبول الحجوزات حتى اللحظة الأخيرة لزيادة مبيعاتك إلى الحد الأقصى", "package_timeslot_subtitle": "الفترة الزمنية والمخزون", "package_copy_schedule_dialog_title": "نسخ التفاصيل من أي باقة؟", "global_copy_success": "تم النسخ بنجاح", "special_inventory_tips": "إذا تم إنشاء فترات زمنية ولكن تم بيع الباقة، يرجى مراجعة المخزون الخاص. سيعمل كل من المخزون الخاص ومخزون الفترة الزمنية على هذه الحزمة.", "package_unit_age_integer": "العدد الصحيح مطلوب", "package_bulk_modification_fixed_price_tips": "هذا النموذج بسعر ثابت، وسينطبق التعديل على جميع الفترات الزمنية تحت وحدة SKU هذه", "pm_price_calendar": "تقويم الأسعار", "package_unit_num_integer": "العدد الصحيح مطلوب", "package_timeslot_all_day": "طوال اليوم", "global_operation": "عملية", "package_unit_limited": "م<PERSON><PERSON><PERSON><PERSON>", "package_price_retail": "سعر التجزئة", "package_unit_main_sku_tips": "سيتم تعيين الوحدة الأولى التي تم نشرهاعلى أنها وحدة SKU الرئيسية بشكل افتراضي. لا يمكن تعديل المواعيد الزمنية والمخزون إلا لوحدة SKU الرئيسية ومشاركتها بواسطة وحدات SKU الأخرى ضمن هذه الباقة.", "package_retail_selling_currency": "عملة البيع بالتجزئة / البيع", "package_timeslot_selector": "الفترة الزمنية:", "package_please_choose": "يرجى اختيار باقة", "global_modify_error": "خطأ في التغيير", "global_yes": "نعم", "package_price_default_setting_warning": "يرجى ملاحظة أنه سيتم استخدام السعر الافتراضي للفترات الزمنية التي يتم تمديدها تلقائيًا.", "package_bulk_modification_edit_price": "تعديل السعر", "package_bulk_modification_edit_inventory": "تعديل المخزون", "global_week_sat": "السبت", "package_unit_age_comparison": "الحد الأقصى للعمر يجب أن يكون أكبر من الحد الأدنى للعمر", "package_xinfo_date": "التاريخ", "package_unit_main": "وحدة SKU الرئيسية", "global_package": "الباقة", "package_timeslot_start_time": "وقت البدء", "package_price_default_price_tips_3": "كاملة.", "package_unit_subtitle": "الوحدة والسعر", "package_timeslot_by_weekly": "أسبوعيًا", "new_take_rate": "مبلغ العمولة الجديد", "global_week_abbr_mon": "الإثنين", "redemptions_filter_text": "تصنيف", "global_modify_success": "تم التعديل بنجاح", "package_price_schedule_delete_tips": "هل تريد حذف قاعدة الجدول الزمني؟", "package_price_default_price_tips_1": "يكون التقويم ظاهرًا فقط عندما يكون", "package_error_mandatory": "يرجى تعبئة جميع الحقول المطلوبة", "js_confirm_delete": "تأكيد الحذف؟", "block_out_date": "حجب التاريخ في التمديد التلقائي", "global_status": "الحالة", "package_unit_num_comparison": "الرقم الأقصى يجب أن يكون أكبر من الرقم الأدنى", "global_day": "يوم", "package_day_s": "أيام", "global_week_abbr_sun": "ال<PERSON><PERSON>د", "package_timeslot_by_weekdays": "أيام الأسبوع", "act_save_success": "تم الحفظ بنجاح", "package_unit_selector": "الوحدة:", "package_merchant_currency": "عملة التاجر", "global_week_thu": "الخميس", "global_week_abbr_wed": "الأربعاء", "act_leave_tips": "إذا غادرت الآن، فستفقد أي تغييرات غير محفوظة", "package_min_max_units": "الحد الأدنى / الأقصى للوحدات", "package_unit_number": "قيود كمية الحجز", "package_unit_title": "إضافة الوحدة والسعر", "global_no": "لا", "package_price_cost": "سعر التكلفة", "pm_price_model": "نموذج السعر", "package_bulk_modification_save_tips": "ير<PERSON>ى تحديد العناصر أولا", "global_week_mon": "الإثنين", "package_inventory_limited": "المخزون محدود", "package_inventory_limited_per_day": "محدودة في اليوم", "package_list_status": "الحالة", "package_schedule_today": "اليوم", "package_bulk_modification_edit_cutoff": "تعديل آخر موعد لاستلام الطلبات", "package_timeslotlist_extend": "قائمة التمديد التلقائي", "package_unit_drag_main_sku_warning": "يجب نشر الوحدة الأولى", "global_total": "المجموع", "global_delete_error": "خطأ في الحذف", "package_customized_unit_name": "اسم الوحدة المخصص", "package_inventory": "المخزون", "global_please_input": "ي<PERSON><PERSON><PERSON> إدخال", "global_delete": "<PERSON><PERSON><PERSON>", "global_save": "<PERSON><PERSON><PERSON>", "package_unit_child": "طفل", "package_unit_infant": "رضيع", "package_unit_senior": "كبير", "status.unarchived": "غير مؤرشف", "merchant_withdraw_action": "طلب الموافقة على السحب", "package_schedule_autoextend": "تمديد تلقائي", "global_city": "مدينة", "merchant_archive_package": "هل تريد أرشفة هذه الباقة؟", "activity_id_title": "رقم وعنوان النشاط", "merchant_withdraw_package": "هل تريد سحب طلب الموافقة على هذه الباقة {PID-Name}؟", "merchant_submit_activity_action": "إرسال النشاط", "global_add": "إضافة", "sub_category": "فئة فرعية", "package_archived": "مؤرشف؟", "package_list_modify": "تعديل", "package_start_date": "تاريخ البدء", "price_inventory_page": "سعر والمخزون", "package_list_archive": "حالة الأرشيف", "performed_by": "تم من قبل", "global_activity": "نشاط", "before_status": "قبل", "merchant_approved": "تمت الموافقة عليه", "package_id_title": "رقم وعنوان الباقة", "create_time_bj": "وقت الإنشاء (UTC+8)", "package_schedule_1_advance": "يوم واحد مقدمًا", "package_count": "كمية الباقة", "status.archived": "مؤرشف", "global_preview": "معاينة", "merchant_none": "--", "global_button_cancel": "إلغاء", "merchant_rejected": "مرفو<PERSON>", "package_schedule_end_date": "تاريخ الانتهاء", "activity_event_logs": "سجلات الأحداث", "global_activity_status": "حالة النشاط", "btn.more": "...", "package_block_out_date": "تاريخ الحجب", "actCreate.leafCategory": "الفئة النهائية", "manage": "إدارة", "global_unpublished": "غير منشور", "global_submit": "إرسال", "package_unit_person": "شخص", "merchant_submit_package_action": "إرسال الباقة", "merchant_approve_status": "حالة الموافقة", "package_unit_adult": "بالغ", "modify_time_bj": "الطابع الزمني", "global_published": "تم نشره", "package_list_package_status": "حالة الباقة", "act_binding_destination_city": "المدينة الوجهة", "global_view": "<PERSON><PERSON><PERSON>", "merchant_self_onboard_entry": "إدارة النشاط", "package_info_name": "عنوان الباقة", "leaf_category": "الفئة النهائية", "merchant_withdraw_activity": "هل تريد سحب طلب الموافقة على هذا النشاط {AID-Name}؟", "merchant_unarchive_package": "هل تريد إلغاء أرشفة هذه الباقة؟", "package_unit_other": "أ<PERSON><PERSON><PERSON>", "merchant_pending": "قيد الانتظار", "pm_fixed_price": "سعر ثابت", "price_engine_sub-category": "الفئة الفرعية", "after_status": "بعد", "global_reset": "إعادة الضبط", "common.perPage": "لكل صفحة", "common.pieces": "قِطَع", "instant_confirm": "تأكيد فوري", "activity.detail.location.map": "Location", "app3.activityDetail.recommendActivities": "You might also like...", "speact_rl4_tv0": "Reviews", "relevant_activity": "Often booked together", "act_change_template_hint": "Once you saved the template can't be changed, please select carefully", "common.leave_title": "", "submit_time_bj": "وقت الإرسال(UTC+8)", "note_required_optional.name": "", "voucher.delete_note": "", "voucher.copy": "", "group_per_unit": "", "global.user_guide_next": "التالي", "global.user_guide_got_it": "واضح", "global.more": "", "act_enter_duration_range": "النطاق", "act_hours": "ساعة (ساعات)", "act_days": "يوم (أيام)", "act_minutes": "دقيقة (دقائق)", "global_fixed_time": "ثابت", "package_type": "", "ob_ventory": "", "stock_out_type": "", "actNav.sellingInfo": "", "activity_special_inventory_edit_tips": "", "inv_pkg_hint": "", "act_skip_displaying_pkg_price": "", "orderdetail_text_reject_reason": "سبب الرفض", "manage_language_manage_language": "", "schedule_publish": "", "cancel_before_start": "", "act_create_new_activity": "", "title_clear": "م<PERSON><PERSON>", "act_responsible_BD": "", "link_bulk_update_responsible_BDs": "", "package_info_desc": "", "global_select": "", "activity.detail.about.activity": "", "upload_4_photos_at_least": "", "package_required_fields": "", "contact_way.hover": "", "save_and_next": "", "confirmation_time": "", "cancellation_policy": "", "no_size_limit": "", "global.contact.us": "", "act_tips_diff_sub_categories": "", "other_info_only_non_standardized_selected": "", "act_binding_destination_poi": "", "package_manage_unit": "", "taxonomy_manage_placeholder": "", "activity_search": "", "package_info_subtitle": "", "act_save_edit_mode": "", "package_xinfo_after_booking": "", "taxonomy_destination_languages": "", "taxonomy_dialog_longitude": "", "ob_vouc_usage1": "", "package_timeslot_list": "", "package_price_premier_agent": "", "taxonomy_dest_add_second_level": "", "taxonomy_full_address": "", "ob_cancel_choice1": "", "package_inventory_limited_number": "", "pm_selling_price_type": "", "act_lan_account_both": "", "act_paixu_diff": "", "taxonomy_back_region_list": "", "act_tips_saving_to_next": "", "other_info_email": "", "cancel_within_7d": "", "link_manage_timeslot": "", "form.gpsLocation": "", "ref_lang_none": "", "act_activity_main_photos": "", "act_list_pub_ref_tips": "", "ob_voucher_choice2": "", "act_lan_zh_CN": "", "taxonomy_destination_ratio": "", "taxonomy_dialog_time_zone": "", "taxonomy_filter_status": "", "ob_cancel_choice9": "", "package_timeslot_repeat_none": "", "taxonomy_nicknames": "", "act_be_standardized": "", "taxonomy_alert_content": "", "merchant_package_contact_type_whatsapp": "", "other_info_additional": "", "act_lan_en_GB": "", "act_if_meet_criterias": "", "taxonomy_dialog_country_code_2": "", "package_timeslot_open_ticket": "", "auto_extend_hint": "", "act_select_to_from": "", "activity_basic_page_banner_photo": "", "pm_remark": "", "package_info_add_package": "", "package_copy_schedule": "", "taxonomy_destination_pin_all": "", "fnb_use_template": "", "taxonomy_dest_version_status": "", "act_gps_location": "", "act_lan_en_AU": "", "act_list_view_online_only": "", "taxonomy_dest_copy": "", "act_publish_to_user_account": "", "create_photo_copy": "", "act_detail_picture_tip": "", "package_xinfo_choose_repeat_rule": "", "pm_submit": "", "taxonomy_confirm_selected": "", "merchant_id": "", "act_auto_filled_original": "", "taxonomy_belong_country": "", "ob_48hrs": "", "act_fold_all": "", "act_lan_en_CA": "", "elevy_none": "", "taxonomy_destination_tips": "", "group_per_booking_tip": "", "taxonomy_dest_select_placeholder": "", "act_binding_destination_not_find_poi": "", "other_info_validate_warn_duplicate": "", "act_blocklist_will_not_publish": "", "package_tag_price": "", "taxonomy_admin_region_name": "", "act_activity_unpublish_package_error1": "", "reason_seasonal_mkt_team": "", "ob_cancel_choice7": "", "timeslotlist_add": "", "taxonomy_dest_item_type": "", "act_fields_completed_saved": "", "act_binding_destination_country": "", "package_xinfo_modify": "", "taxonomy_dest_type_define": "", "merchant_package_contact_type_email": "", "act_binding_destination_departure_info": "", "act_lan_en_HK": "", "manage_language_reference_lang": "", "package_list_archived_packages": "", "taxonomy_dest_first_menu_title": "", "act_group_summary": "", "taxonomy_dialog_coordinates": "", "act_binding_destination_create": "", "publish_lang_manage": "", "package_price_age_range": "", "taxonomy_scenic_spot": "", "act_blocklist_system_will_notify": "", "act_blocklist_at_least_one": "", "packge_xinfo_fields": "", "act_binding_destination_scope": "", "xinfo_restrict_hint": "", "taxonomy_destination_page_template": "", "act_binding_destination_city_request": "", "global_location": "", "activity_approve": "", "package_published_overflow": "", "package_schedule_3_advance": "", "cancel_within_48h": "", "taxonomy_add_sub_regions": "", "taxonomy_dest_item_title": "", "merchant_editing_try_later": "", "category": "", "act_upload_arrange": "", "taxonomy_belong_county": "", "package_xinfo_hint_placeholder": "", "reason_others": "", "lang_hint.name": "", "lang_hint.hover": "", "accept_country_num.name": "", "participant_hint.hover": "", "participant_write_type_number.name": "", "lang_select.name": "", "contact_way.name": "", "identification_info_date_issued.name": "", "accept_country.hover": "", "participant_number_range.name": "", "accept_country.name": "", "lang_items.name": "", "accept_country_num.hover": "", "identification_info.name": "", "identification_type.name": "", "note_hint.name": "", "note_write_type.name": "", "lang_select_multi.name": "", "note_required.name": "", "identification_info_id.name": "", "participant_write_type.name": "", "participant_type.name": "", "note_type.name": "", "note_hint.hover": "", "CODE_AD_applicable_platform": "", "tips": "", "note_write_type_text.name": "", "participant_hint.name": "", "name_group.name": "", "name": "", "is_hidden.name": "", "lang_write_type.name": "", "is_hidden_check.name": "", "identification_info_expiration_date.name": "", "identification_info_issuing_place.name": "", "select_items.name": "", "identification_type.hover": "", "name_lang.hover": "", "name_lang.name": "", "form.stkTextarea": "", "package_special_selling_price": "", "taxonomy_destination_seo_meta": "", "taxonomy_destination_type": "", "act_binding_destination_cancel": "", "act_activity_unpublish_sku_error1": "", "reason_seasonal_merchant": "", "previous_step": "", "taxonomy_destination_page_title": "", "first_sku_id_no_delete": "", "act_menchant_voucher": "", "pm_cost": "", "package_tag_fnb_content": "", "global_expired": "", "act_goto_price_page": "", "act_lan_gb": "", "package_click_here_to_change": "", "taxonomy_dest_version_search": "", "package_price_new_unit": "", "timeslotlist_date_range": "", "pm_timeslot_operator_warn": "", "act_binding_destination_click_me": "", "package_price_add_unit_error": "", "act_binding_destination_city1": "", "act_activity_detail_change_tip": "", "other_info_last_name": "", "taxonomy_dest_type_dest": "", "act_blocklist_all_languages": "", "package_info_reconfirmation": "", "taxonomy_destination_explore_city": "", "taxonomy_poi_name": "", "revise_price_published_merchant": "", "taxonomy_filter_all": "", "auto_publish_unpublish_tips_3": "", "package_timeslot_repeat_daily": "", "act_binding_destination_to_request": "", "submitted_language": "", "taxonomy_destination_cancel_pinning_all": "", "taxonomy_dest_choose_lang_tip": "", "no_cancel": "", "act_activity_unpublish_sku_error3": "", "taxonomy_dest_area_country": "", "package_timeslot_available": "", "package_this_is_open_ticket": "", "act_lan_cn": "", "pm_effective_time": "", "pm_pkg_id": "", "common_field_personal_measurement": "", "taxonomy_destination_unpinned_category": "", "create_act_photo_for_lang": "", "package_another_addon_hint": "", "act_pkg_copy_info": "", "taxonomy_type": "", "klk_voucher_klk_code": "", "package_schedule_30_advance": "", "act_lan_th_TH": "", "taxonomy_destination_publish_or_unpublish": "", "ob_no_voucher": "", "package_timeslot_another": "", "pm_pkg_status": "", "act_lan_id": "", "approval_activity": "", "btn.okay": "", "pkg.followingInfomation": "", "taxonomy_filter_admin_region": "", "choose_city": "", "taxonomy_root_menu": "", "Go_To_AM_Activity_List": "", "package_open_ticket_hint": "", "pricelist_activity_status": "", "act_current_language_detail_no_data": "", "act_binding_destination_destination_country_or_region": "", "act_tips_saving_all_mandatory": "", "act_binding_destination_departure_city": "", "approve_events_log": "", "act_view_what_selected": "", "publish_title": "", "stkModal.sureActivityWithPackage": "", "package_price_confirm_hint": "", "pm_change_merchant_warn": "", "taxonomy_destination_category_navigation": "", "pm_default_setting": "", "act_copy_from_another_pkg": "", "unpublish_title": "", "act_binding_destination_destination_info": "", "pm_reminder": "", "taxonomy_filter_parent_region": "", "package_displayed_month_tips": "", "delete_free_text_hint": "", "act_image_en_us_handle": "", "activity_basic_page_main_photo": "", "taxonomy_sub_regions": "", "act_activity_mark_on_tip": "", "package_xinfo_refresh_content": "", "taxonomy_dest_version_create_time": "", "taxonomy_edit_dialog_title": "", "Merchant": "", "package_no_activity": "", "act_guarantee_for_package_1": "", "taxonomy_dialog_title": "", "manage_timeslot_update": "", "package_xinfo_name": "", "manage_language": "", "package_list_modify_pkg": "", "taxonomy_destination_seo_url": "", "taxonomy_destination_seo_title": "", "taxonomy_destination_pinned_act": "", "activity_required_fields": "", "taxonomy_destination_ranking": "", "package_info_location_instructions": "", "cancel_with_conditions": "", "global_unfilled": "", "keywords_search": "", "package_tag_schedule": "", "reset_search": "", "act_lan_nz": "", "package_info_reconfirmation_hint": "", "package_tag_package_details": "", "create_banner_photo_for_lang": "", "upload_images": "", "ob_cancel_choice6": "", "act_lan_user_only": "", "taxonomy_dest_version_cancel_publish_cfm": "", "i18n_custom_photo_hint": "", "taxonomy_poi_manage_placeholder": "", "package_list_modify_unit": "", "voucher_usage": "", "taxonomy_list_actions": "", "package_default_price_retail": "", "common_field_mailing_information": "", "package_unit_tool": "", "act_copy_package": "", "act_lan_en_PH": "", "other_info_standardized_tips_act_multi": "", "package_xinfo_enter__name": "", "add_free_text_disabled": "", "taxonomy_filter_unavailable": "", "package_xinfo_another": "", "act_activity_unpublish_package_error3": "", "package_price_unit_name": "", "act_tips_check_same_category": "", "taxonomy_city_level": "", "fnb_fixed_date": "", "package_info_limited": "", "cancel_within_24h": "", "taxonomy_add": "", "act_activity_details": "", "taxonomy_dest_area_type": "", "other_info_less_likely_warn_2": "", "pm_search": "", "act_blocklist_publish_conditions": "", "taxonomy_synonyms": "", "duplicate_attr_hint": "", "package_displayed_month_number": "", "other_info_country_region": "", "pm_modify_price": "", "act_copy_from_another": "", "other_info_standardized_tips_act_combo_main": "", "ob_others": "", "package_xinfo_dropdown_placeholder": "", "activity_reject": "", "package_another_addon_package": "", "global_copy": "", "taxonomy_dest_add_first_level": "", "remove_attr_hint": "", "taxonomy_multilingual_versions": "", "act_detail_subtitle_placeholder": "", "package_xinfo_before_today": "", "taxonomy_belong_city": "", "taxonomy_select": "", "pm_pick_date": "", "taxonomy_destination_seasonal_weather": "", "act_binding_destination_submit": "", "taxonomy_dest_url_valid_t": "", "ob_voucher_choice1": "", "act_list_view_range_all": "", "act_group_list": "", "taxonomy_dest_version_title": "", "ob_vouc_usage3": "", "taxonomy_filter_city_yes": "", "schedule_unpublish": "", "act_product_notes": "", "package_price_valid_time": "", "act_seo_title_hint": "", "unpublish_package": "", "financial_model_skip": "", "act_seo_meta_description": "", "email_to_pic": "", "send_email_success": "", "no_permission": "", "publish_activity": "", "financial_model_dialog_save": "", "act_seo_fields": "", "package_original_cost": "", "financial_model_status": "", "financial_model_dialog_back_to": "", "no_auth_edit": "", "target_take_rate": "", "financial_model_approved": "", "status_tip": "", "financial_model_reset": "", "fnb_open_ticket_with_content": "", "act_seo_title": "", "act_financial_model_send_btn": "", "financial_model_financial_model": "", "global_remarks": "", "info_notes": "", "financial_model_pending_approve": "", "fnb_single_dish": "", "act_financial_model_send_email": "", "goto_archived_packages": "", "ttr_email": "", "fnb_open_ticket_without_content": "", "unpublish_activity": "", "publish_package": "", "act_for_internal_analysis_1": "", "package_price_status": "", "financial_model_not_set": "", "act_seo_top_keywords_hint": "", "act_seo_top_keywords": "", "questionnaire": "", "package_price_modify_update_cost": "", "act_seo_url": "", "act_activity_title": "", "act_notes": "", "fnb_buffet": "", "fnb_open_ticket_with_title": "", "fnb_cash_voucher": "", "global_button_handle": "", "content_act_seo_url": "", "edit_activity_content": "", "fnb_open_ticket_without_title": "", "cms_datetime_picker_ph": "", "fnb_meal_kit": "", "package_price_agent_type": "", "act_financial_model_pkg_warn": "", "package_price_amount_type": "", "act_tags": "", "last_take_rate": "", "act_for_internal_analysis_2": "", "act_short_description": "", "package_price_invalid_time": "", "global_button_del": "", "package_price_type": "", "merchant_id_tier": "", "act_seo_keywords_hint": "", "package_price_selling_amount_number": "", "ttr_confirm_change": "", "package_price_effect": "", "package_price_operator": "", "seo_url_format_tip": "", "act_seo_keywords": "", "msp_amount_plh": "", "ttr_compare_tr": "", "msp_currency_plh": "", "package_base_selling": "", "submit_to_preview": "", "fnb_set_menu": "", "package_price_modify_cost_price": "", "fnb_premium_service": "", "submit_to_em": "", "package_current_cost": "", "ttr_new_tr": "", "override_take_rate": "", "financial_model_rejected": "", "act_activity_subtitle": "", "financial_model_confirm_tips": "", "package_current_cost_col": "", "taxonomy_dialog_name": "", "taxonomy_dialog_latitude": "", "global_validate_text_required": "", "taxonomy_destination_upload_video": "", "taxonomy_dest_add_third_level": "", "common_field_diver_details": "", "package_reject": "", "package_xinfo_repeat_rules": "", "activity_basic_page_basic_info": "", "traditional_chinese": "", "taxonomy_dialog_parent_region_placeholder": "", "act_cannot_copy_package": "", "package_ask_once_per_booking": "", "act_submit_to_content": "", "no_edit_en": "", "other_info_non_standardized": "", "other_info_standardized_tips_combo": "", "pm_price_type": "", "publish_unpublish_reason": "", "voucher_get_method": "", "package_schedule_close_autoextend": "", "timeslotlist_delete_selected": "", "taxonomy_dest_version_publish_msg": "", "other_info_no_required": "", "pkg.checkItems": "", "act_package_unit_name_hint": "", "taxonomy_destination_basic_info": "", "global_tips": "", "act_lan_ko_KR": "", "chart_export_as_image": "", "act_lan_agent_only": "", "act_choose_activity_category": "", "package_list_manage": "", "package_another_main_package": "", "elevy_stay_outside_hk": "", "taxonomy_dest_delete_tips": "", "taxonomy_destination_pixel": "", "pm_pick_time_range": "", "package_input_error": "", "package_info_unlimited": "", "evoucher_only": "", "act_choose_view_lan": "", "taxonomy_destination_keywords": "", "taxonomy_destination_single": "", "cancel_within_72h": "", "package_xinfo_checkbox_placeholder": "", "taxonomy_dest_url_valid_f": "", "js_confirm_not_verify": "", "pm_calendar_view": "", "taxonomy_destination_operation": "", "taxonomy_dest_version_appointment_confirm": "", "merchant_voucher": "", "taxonomy_destination_base_time_to_visit": "", "taxonomy_destination_seo_keywords": "", "taxonomy_filter_parent": "", "pm_change_pm_warn": "", "package_price_china_agent_rate": "", "other_info_confirm_del": "", "other_info_1_participant": "", "package_xinfo_ismadatory": "", "taxonomy_destination_coordinates": "", "act_unfold_all": "", "package_xinfo_common_field": "", "act_binding_destination_of_act": "", "package_schedule_4_advance": "", "timslotlist_timeslot_chosen": "", "pkg_restrict_hint": "", "package_hour_s": "", "act_upload_a_lots": "", "package_xinfo_after_today": "", "package_schedule_7_advance": "", "taxonomy_hotel": "", "taxonomy_filter_available": "", "taxonomy_dest_version_read": "", "other_info_submit_finished": "", "taxonomy_dialog_Search": "", "taxonomy_reset": "", "common_field_personal_information": "", "taxonomy_destination_customized_region": "", "first_publish_date": "", "package_info_enter_desc": "", "api_pkg_hint": "", "package_info_subtitle_placeholder": "", "revise_price_hint": "", "taxonomy_theme_park": "", "act_binding_destination_not_find": "", "other_info_following_info_collected": "", "taxonomy_dining_room": "", "package_timeslot_repeat_weekly": "", "voucher_level": "", "taxonomy_dialog_country_code_3": "", "taxonomy_created_by": "", "unfinish_field": "", "act_activity_unpublish_sku_error2": "", "pm_cost_price": "", "taxonomy_dialog_city": "", "act_groups_to_copy": "", "merchant_package_contact_type_phone": "", "package_xinfo_enter_name": "", "package_xinfo_copy": "", "taxonomy_destination_info": "", "ob_vouc_usage2": "", "Go_To_Content_Package_List": "", "basic_information": "", "merchant_submit_activity_required": "", "other_info_copy_no_data": "", "package_xinfo_dropdown_list": "", "approve": "", "taxonomy_primary_sub_regions": "", "taxonomy_status": "", "package_approve": "", "package_info_description_desc": "", "taxonomy_dest_add_items": "", "act_binding_destination_continent": "", "delete_timeslot_error": "", "taxonomy_dest_edit_lang": "", "act_create_en_hint": "", "taxonomy_destination_time_zone": "", "taxonomy_dest_version_status_draft": "", "act_list_content_to_edit": "", "act_if_bookable": "", "mandatory_lang_hint": "", "content_name": "", "ob_m_voucher": "", "package_list_unarchive": "", "global_field_need_compress": "", "taxonomy_dest_third_level_title": "", "act_lan_de_DE": "", "basic.activityPhoto": "", "global_button_add": "", "act_photo_less_banner": "", "act_lan_hk": "", "common_field_hotel_information": "", "auto_publish_unpublish_tips_2": "", "taxonomy_destination_unpublished": "", "act_recommended_ratio": "", "taxonomy_nature": "", "package_schedule_2_advance": "", "ob_cancel_choice4": "", "pm_reset": "", "pm_market_price": "", "other_info_less_likely_warn": "", "act_activity_unpublish_activity_error3": "", "language_publish_condition": "", "act_guarantee_for_package_2": "", "package_copy_schedule_tips": "", "act_activity_unpublish_activity_error2": "", "act_blocklist_selected_languages": "", "act_list_view_range_only": "", "act_lan_en_MY": "", "merchant_package_contact_type_wechat": "", "package_bulk_modification_edit_selling_price": "", "package_minimum_take_rate": "", "package_min_s": "", "taxonomy_destination_currency": "", "taxonomy_dest_version_operation": "", "taxonomy_filter_city": "", "other_info_standardized_tips_act_api": "", "pm_list_view": "", "act_blocklist_please_confirm": "", "common_field_language_and_others": "", "package_price_data_error": "", "global_button_detail": "", "manage_language_modify_detail": "", "type_to_search": "", "global_reupload": "", "taxonomy_dialog_nicknames_placeholder": "", "package_info_location_hint": "", "package_default_price_cost": "", "ticket_type": "", "taxonomy_destination_edit_image": "", "print_mobile_voucher": "", "package_schedule_6_advance": "", "merchant_submit_activity": "", "act_lan_zh_HK": "", "act_copy_groups": "", "pm_cancel": "", "timeslotlist_extend": "", "print_mobile_offline_voucher": "", "act_photo_add_banner": "", "taxonomy_destination_time_of_generation": "", "package_overview_hint": "", "custom_xinfo": "", "taxonomy_dest_version_title_tip": "", "package_edit_price": "", "taxonomy_destination_back": "", "act_lan_en_SG": "", "package_info_location_size": "", "act_transferable_tip2": "", "delete_auto_extend": "", "other_info_required": "", "act_detail_picture": "", "taxonomy_country_level": "", "package_open_ticket": "", "ob_cancel_choice3": "", "package_price_save_tips": "", "keyword": "", "group_per_unit_tip": "", "act_edit_photo": "", "taxonomy_dialog_parent_region": "", "pm_expire_time": "", "other_info_phased_out_eventually": "", "other_info_all_participant": "", "ob_cancel_choice10": "", "taxonomy_dest_version_create": "", "taxonomy_mall": "", "package_language_none": "", "elevy_act_outside_hk": "", "package_enter_unit_name": "", "act_lan_kr": "", "global_usable": "", "can_not_be_empty": "", "act_list_cancel_edit": "", "act_copy_to_other_pkg": "", "ob_voucher_retr_method": "", "stkModal.choosePackagesWithActivity": "", "revise_price_new_cur": "", "taxonomy_destination_official_language": "", "navname_content_cnrail_crosssell": "", "act_photo_download_original_image": "", "taxonomy_dest_version_publish_cfm": "", "taxonomy_bus_station": "", "taxonomy_airport": "", "taxonomy_alert_title": "", "act_activity_unpublish_package_error2": "", "act_package_package_name_hint": "", "act_copy_direction": "", "pm_date_time": "", "other_info_contact": "", "klk_voucher_merchant_code": "", "act_activity_mark_can_confirm": "", "other_info_all_selected_displayed": "", "package_tag_extra_info": "", "ob_vouc_usage5": "", "act_lan_es_ES": "", "package_take_overview_hint": "", "simplified_chinese": "", "act_create_activity": "", "actCreate.modalTitle": "", "taxonomy_destination_pinned_destination": "", "act_map": "", "taxonomy_create_location_name": "", "ob_api": "", "back_to_timeslotlist": "", "timeslotlist_all_timeslots": "", "taxonomy_dialog_nicknames": "", "global_completed": "", "global_images": "", "package_xinfo_sort_hint": "", "ob_vouc_usage4": "", "act_pkg_copy_error": "", "package_xinfo_mandatory_hint": "", "english": "", "error_repeat_timeslot": "", "48_confirm": "", "package_xinfo_ask_once": "", "act_binding_destination_geographic_level": "", "act_lan_vn": "", "taxonomy_dest_version_status_down": "", "pacage_another_main_hint": "", "act_lan_en_NZ": "", "package_schedule_14_advance": "", "package_list_delete_package_tips": "", "taxonomy_filter": "", "auto_publish_unpublish_tips": "", "act_detail_desc_placeholder": "", "cancel_within_14d": "", "taxonomy_dest_version_last_modify_time": "", "package_price_regular_agent": "", "pm_special_selling_price": "", "taxonomy_destination_destination": "", "taxonomy_dialog_status": "", "package_take_overview": "", "act_activity_mark_confirm_type": "", "taxonomy_destination_pinned_category": "", "taxonomy_dest_version_status_publish": "", "next_step": "", "act_lan_vi_VN": "", "spu_xinfo_hint": "", "btn.createPkg": "", "package_unit_tips": "", "act_content_icons": "", "fnb_open_ticket": "", "taxonomy_create_key_location": "", "taxonomy_create_admin_region": "", "package_xinfo_checkbox": "", "pm_update_default_setting": "", "package_until": "", "reason_violation_of_selling_price": "", "merchant_package_contact": "", "main_package": "", "merchant_submit_package": "", "act_package_package_subtitle_hint": "", "act_if_transferable": "", "publish_unpublish_other_reason": "", "other_info_standardized": "", "cancel_before_redeem": "", "package_xinfo_time": "", "taxonomy_destination_key_location": "", "act_lan_en_BS": "", "taxonomy_destination_of_activities": "", "pm_selling_price": "", "js_post_success": "", "act_address": "", "act_lan_ja_JP": "", "act_binding_destination_departure_country_or_region": "", "act_to": "", "act_lan_th": "", "taxonomy_create_region": "", "taxonomy_destination_all_destination": "", "other_info_user_info_non_standardized": "", "fnb_package_management_tip3": "", "package_unit_delete_warning": "", "fnb_package_management_tip2": "", "act_list_view_range_responsible": "", "act_english_content": "", "act_binding_destination_city_name": "", "taxonomy_railway_station": "", "pkg.specialSettings": "", "pm_parti_date": "", "package_xinfo_text_box": "", "act_enter_no_only": "", "package_unit_youth": "", "delete_free_text_disabled": "", "fnb_pacakge_dependent": "", "act_enter_to_location": "", "manage_timeslot_placeholder": "", "common_field_pick_up_detail": "", "act_lan_jp": "", "package_unit_publish_warning": "", "global_show_msg_save_success": "", "package_default_price_selling": "", "taxonomy_dest_cancel_appoint": "", "fnb_package_management_tip": "", "package_xinfo_enter_hint": "", "taxonomy_dest_edit": "", "act_lan_au": "", "package_info_max_min_participants": "", "note": "", "change_price_incorrect": "", "taxonomy_destination_unpinned_destination": "", "act_list_in_preview": "", "taxonomy_dest_version_publish_time": "", "other_info_standardized_tips_act_identity": "", "display_on_banner": "", "act_lan_zh_TW": "", "manage_language_status": "", "taxonomy_higher_level": "", "mail_voucher": "", "global_updated": "", "taxonomy_dest_url_valid_s": "", "cancel_within_30d": "", "copy_to_city": "", "global_please_choose": "", "package_xinfo_ask_each": "", "act_search_keywords": "", "other_info_phone_number": "", "act_transferable_no": "", "package_xinfo_mandatory": "", "other_info_standardized_tips_act_combo_temporarily": "", "act_tips_complete_all_groups": "", "package_tag_info": "", "merchant_setting_language": "", "taxonomy_dest_pop_area": "", "taxonomy_region_name": "", "taxonomy_destination_applications": "", "taxonomy_query": "", "image_change_dft_hint": "", "act_lan_it_IT": "", "act_list_is_on_blocklist": "", "package_calendar_take_rate_tips": "", "edit_pacakge_name": "", "taxonomy_destination_image_size": "", "change_price_system_err": "", "pm_operator": "", "taxonomy_dest_item_content": "", "taxonomy_dest_version_publish": "", "language_selection_onboard": "", "global_draft": "", "taxonomy_dest_order": "", "package_existing_take_rate": "", "package_list_archiv_package_tips": "", "loading_text": "", "group_per_booking": "", "merchant_self_confirm": "", "merchant_confirm_type": "", "act_modify_restrict_hint": "", "pm_change_pm": "", "edit_package_name": "", "taxonomy_region_id": "", "merchant_package_contact_type_line": "", "package_another_unit": "", "taxonomy_dest_version_creator": "", "taxonomy_destination_click_to_edit": "", "taxonomy_destination_status": "", "package_xinfo_hint": "", "package_create_timeslot_hint": "", "taxonomy_destination_range": "", "taxonomy_dest_version_status_release": "", "act_pkg_copy_price": "", "package_unit_name_tips": "", "ob_cancel_choice8": "", "act_min_size": "", "Go_To_Content_Activity_List": "", "global_bts_table_formatNoMatches": "", "act_lan_en_IN": "", "ob_cancel_choice2": "", "act_list_search_placeholder": "", "taxonomy_destination_admin_region": "", "act_lan_id_ID": "", "delete_timeslot_msg": "", "taxonomy_destination_video": "", "elevy_from_hk": "", "timeslotlist_back_to_packagelist": "", "global_place_id": "", "act_activity_mark_no_confirm": "", "taxonomy_list_country_code_3": "", "24_confirm": "", "act_lan_sg": "", "act_lan_en_US": "", "package_info_name_placeholder": "", "taxonomy_confirm_key_location": "", "taxonomy_dest_second_menu_title": "", "taxonomy_destination_upload_images": "", "taxonomy_destination_old_images_tips": "", "package_xinfo_before_booking": "", "act_merchant": "", "act_list_permission_denied": "", "no_voucher": "", "common_field_wifi_and_sim": "", "copy_to_country": "", "taxonomy_dest_item_image": "", "pkg.policiesRules": "", "merchant_self_confirm_turn_on_btn_tips": "", "other_info_non_standardized_warning": "", "act_list_cancel_add": "", "act_blocklist_not_publish": "", "package_schedule_5_advance": "", "pm_selling_history": "", "ob_e_voucher": "", "group_per_unit_type": "", "btn.withdrawSubmit": "", "ob_24hrs": "", "act_transferable_tip1": "", "create_photo_for_lang": "", "ob_voucher_choice3": "", "taxonomy_dest_area_city": "", "manage_publish_language": "", "reason_msp_regulation": "", "act_transferable_yes": "", "act_voucher_type": "", "act_ref_language": "", "ob_voucher_code_level": "", "act.is.editing": "", "status.pendingApproval": "", "other_info_required_participants": "", "timeslotlist_edit_selected": "", "taxonomy_dest_version_last_modifier": "", "package_schedule_32_advance": "", "act_detail_title_placeholder": "", "act_recent_used": "", "act_from": "", "taxonomy_dest_read_submenu": "", "e_voucher": "", "evoucher_offline_voucher": "", "taxonomy_destination_title": "", "act_lan_in": "", "act_blocklist_language_availability": "", "print_voucher": "", "Go_To_AM_Package_List": "", "act_pkg_copy_info_dialog_title": "", "taxonomy_destination_description": "", "global_modify": "", "reason_selling_price_above_retail_price": "", "pm_create_price_calendar": "", "act_lan_ru_RU": "", "pm_batch_edit_calendar": "", "package_xinfo_name_placeholder": "", "act_blocklist_note": "", "act_binding_destination_country_or_region": "", "remove_attr_disabled": "", "global_export": "", "act_photo_desc_hint": "", "copy_standardize_pkg_hint": "", "act_lan_us": "", "other_info_first_name": "", "ob_cancel_choice5": "", "taxonomy_destination_page_management": "", "merchant_withdraw_try_later": "", "package_xinfo_preview_fields": "", "js_confirm_verify": "", "act_pkg_info_copy_tips": "", "pm_reason": "", "dupliate_attr_disabled": "", "package_schedule_10_advance": "", "pm_cost_history": "", "add_free_text_hint": "", "timeslotlist_delete_all": "", "auto_publish_unpublish": "", "package_xinfo_custom_fields": "", "taxonomy_destination_current_language": "", "act_excluding_copied_fields": "", "package_xinfo_type": "", "image_change_custom_hint": "", "taxonomy_dest_edit_submenu": "", "taxonomy_dest_delete": "", "actCreate.subLeafCategory": "", "act_klook_voucher": "", "act_basic_change_merchant": "", "taxonomy_region_type": "", "no_lang_act_publish_tips": "", "taxonomy_selected_sub_regions": "", "fnb_package_management_save": "", "fnb_use_template_disabled": "", "package_price_selling": "", "taxonomy_destination_page_id": "", "package_xinfo_finish_hint": "", "act_lan_tw": "", "reason_merchant_changed_net_rate": "", "global_language": "", "package_info_description_placeholder": "", "common_field_car_transfer_detail": "", "taxonomy_destination_publish_act_num": "", "act_activity_unpublish_activity_error1": "", "merchant_package_contact_type_kakaotalk": "", "package_ask_for_each_participants": "", "reject": "", "copy_package": "", "taxonomy_filter_admin_region_placeholder": "", "package_schedule_16_advance": "", "merchant_package_contact_type_facebook": "", "group_per_unit_type_tip": "", "taxonomy_destination_popular_activities": "", "pm_pkg_name": "", "global_error": "", "act_activity_mark_off_tip": "", "act_lan_fr_FR": "", "ob_act_addr": "", "taxonomy_ferry": "", "package_custom_selling": "", "theme_park_assistant": "", "taxonomy_destination_images": "الصور", "global_confirm_delete": "هل أنت متأكد أنك تريد الحذف؟", "MULTIPLE_aid": "رقم النشاط", "common_back": "العودة", "product_list_page_destination": "", "booking_view_application": "مشاهدة الطلب", "booking_traveler_info": "معلومات المسافر", "traveler_name": "اسم المسافر", "booking_extra_info": "معلومات إضافية", "participant_name": "اسم المشارك", "booking_processing": "قيد المعالجة", "booking_confirm": "إعادة التأكيد", "booking_reconfirm_title": "تأ<PERSON>يد الطلب", "booking_reconfirm_content": "سيتم إبلاغ العميل بمجرد تأكيد طلبه. هل أنت متأكد أنك تريد تأكيد الطلب؟", "booking_updated": "تم التأكيد", "booking_withdrawn_tips": "تم سحب طلب تحديث معلومات الحجز. لا توجد أي إجراءات أخرى مطلوبة", "booking_info_changed": "تم تحديث معلومات الحجز وتحتاج إلى إعادة تأكيد", "booking_application_confirmed": "تم تعديل الحجز.", "booking.pending": "قيد الانتظار", "booking.canceled": "ملغي", "booking.expired": "منتهي الصلاحية", "booking_application": "ط<PERSON><PERSON>", "booking.confirmed": "تم التأكيد", "to_be_canceled": "سيتم إلغاؤها", "reason_to_decline": "سبب رفض الحجز:", "processing_voucher": "معالجة القسيمة ...", "processing_voucher_tip": "القسيمة لا تزال قيد المعالجة. لا يلزم اتخاذ أي إجراء آخر حتى يتم تأكيد القسيمة.", "inquire_message": "رسالة", "inquire_message_holder": "ير<PERSON>ى إدخال أسئلتك (بحد أقصى 500 كلمة)", "info.document_tips": "نوع الملف: png ، jpg ، jpeg ، pdf. الحجم الأقصى: 4 ميغابايت.", "global.empty_tips": "لا يمكن أن تكون فارغة", "booking_confirmed_click": "اضغط لإظهار ملاحظة التأكيد", "booking_confirm_title": "ت<PERSON><PERSON>ي<PERSON> الحجز", "booking_confirm_tips": "هل أنت متأكد من تغيير الطلب لتأكيد الحالة؟", "merge_and_check": "الدمج والتحقق", "booking_confirm_instant": "بمجرد تأكيد هذا الحجز، سيستلم المستخدم قسيمة النشاط الخاصة به على الفور", "voucher_number_placeholder": "يدعم فقط الأحرف والأرقام والموصلات (-)", "pickup_location_english_placeholder": "ير<PERSON>ى إدخال تفاصيل موقع التوصيل/ الالتقاء باللغة الإنجليزية", "ppickup_location_local_placeholder": "ير<PERSON>ى إدخال تفاصيل موقع التوصيل/ الالتقاء بلغتك الرسمية المحلية", "whether_set_up_reconfirmation": "إعداد إشعار التأكيد الثاني لهذا النشاط", "set_up_reconfirmation_label": "كم يوما قبل تاريخ المشاركة؟", "booking.unit.day": "يوم (أيام)", "voucher_number_error": "ير<PERSON>ى إدخال الكود الخاص بك فقط باستخدام الأحرف والأرقام وعلامات الوصل (-)", "booking.user_order_lang_tip": "إذا واجهت أي صعوبات في التواصل باللغة المفضلة للمستخدم، فيرجى اختيار اللغة الإنجليزية بدلاً من ذلك.", "booking.note.confirm.placeholder": "", "global.upload": "تحميل", "reject_dialog_title": "", "reject_select_reason": "<PERSON><PERSON><PERSON> سبب الرفض", "reject_note_placeholder": "سرد الفترات الزمنية المتاحة أو المعلومات الأخرى ذات الصلة (بحد أقصى 500 حرف، اختياري)", "reject_auto_reject": "إذا اخترت هذا السبب، فسيرفض النظام تلقائيًا جميع الحجوزات قيد الانتظار لهذه الفترة الزمنية", "reject_total": "إجمالي قيد الانتظار", "reject_view_booking": "عرض الحجوزات", "reject_timeslot": "الفترة الزمنية", "reject_auto_reopen": "إعادة فتح هذه الفترة الزمنية تلقائيًا إذا تم إصدار استرداد لأي حجوزات سابقة", "reject_delete_timeslot": "سيتم إغلاق الفترة الزمنية بشكل دائم ولن يتم إعادة فتحها", "reject_note": "ملاحظات:", "activity.published": "تم نشره", "activity.notpublished": "لم يتم نشره", "overdue_in_2h": "وقت التأكيد المتأخر بعد ساعتين", "pending_10h": "قيد الانتظار لأكثر من 10 ساعات", "redeem_unredeemable": "وحدة (وحدات) غير قابلة للاسترداد", "global.file_size_limit": "{limit} ميغابايت كحد أقصى للتحميل", "global.file_number_limit": "{limit} مرات تحميل كحد أقصى.", "booking_view_confirm_time": "يرجى تأكيد طلب تعديل الحجز قبل {time} (التوقيت المحلي)", "global.delete": "", "global.delete_file_tips": "هل أنت متأكد أنك تريد حذف ؟", "app3.activityDetail.menu.FAQ": "", "trp_2022031770": "", "global.password_placeholder": "أد<PERSON>ل كلمة المرور", "signup.send_again": "إعادة الإرسال خلال {second} ثانية", "toast_account_added_success": "تم الإضافة بنجاح!", "activities.add_new": "", "global.confirm": "", "activity.v2.label.package_options": "", "activity.v2.label.most_recent_reviews": "", "activities.wednsday": "الأربعاء", "activities.thursday": "", "activities.friday": "", "activities.sunday": "", "activities.saturday": "", "activities.tuesday": "", "content": "المحتوى", "booking_updated_tips": "<PERSON><PERSON><PERSON><PERSON>", "timestamp": ****************}