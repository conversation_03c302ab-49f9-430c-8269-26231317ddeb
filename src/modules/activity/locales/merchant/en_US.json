{"17049": "Show less", "17057": "Cancel", "18718": "View more", "18719": "View less", "21813": "Terms & conditions", "21817": "No location", "21820": "Because this voucher will be redeemed offline, the voucher code level must be per unit", "21837": "These fields can't be modified while the activity is pending approval. You can withdraw the approval request if you need to make changes.", "21838": "Does your activity feature any of the following?", "21856": "No photo uploaded", "21857": "Copy existing package details", "21890": "Mon", "22180": "Start date", "22181": "End date", "22182": "No data found", "23114": "Open Ticket with Calendar", "23115": "Open Ticket With Calendar Package", "23116": "Select this ticket type the activity page will display calendar selection and open ticket package icon", "23117": "Open ticket without calendar", "23118": "Open Ticket Withour Calendar Package", "23119": "If all packages on the activity page are selected as open ticket without calendar type, the calendar selection will not be displayed. Only packages that do not rely on the participation date can be selected as an open ticket. The package settings for open ticket without calendar is as follow:<br/>- Confirmation policy: Instant confirmation<br/>- Inventory type: <br/>i) Under Klook code & API, package must be either free cancellation before redemption or no cancellation;<br/>ii) Under Inventory, package must be no cancellation<br/>- Merchant settlement policy: Either confirmation time or redemption time", "23120": "Please enter a valid URL with \"https://\" or \"http://\"", "23121": "Customize the unit name?", "23122": "Customized unit name", "23123": "If switched to \"Yes\", free text will display on the frontend as the unit name. If \"No\", the standardized unit type name will apply.", "26083": "Can't edit this attribute because it is deleted/unpublished. Please unselect this attribute and select other versions if needed.", "27109": "Student", "27716": "I/We hereby declare that I do not reside in/we are not registered in any sanctioned countries such as <span style=\"color:text-color-warning;font-weight:bold;\">Cuba, North Korea, Myanmar, Sudan, Syria, Crimea, Iran and any other sanctioned countries</span> as updated from time to time by relevant authorities (collectively \"Sanctioned Countries\") and that I/we do not and will not have any products, services, dealings or transactions with any Sanctioned Countries.", "27717": "Cuba, North Korea, Myanmar, Sudan, Syria, Crimea, Iran and any other sanctioned countries", "27719": "I have read and agree to the terms above", "27722": "Manage units", "27723": "Timeslot rule & model settings", "27724": "Settings below will apply to all units under this package.<br/><br/><span style=\"font-weight:bold;\">- What is a unit? </span>Units make up the stock and price of an activity, and are usually grouped by participant type (i.e. Adult, child, person, etc.)<br/><span style=\"font-weight:bold;\">- What is a timeslot?</span> This is the time set for the schedule (i.e. 10:00, 12:00)<br/><span style=\"font-weight:bold;\">- What is a schedule?</span> The combination of a timeslot and date (i.e. 2021-08-01 10:00, 2021-08-01 12:00, 2021-08-02 10:00)<br/><br/>The timeslot rule will automate the schedule for repeated weekdays and weekends. For example, if the rule is set for Monday 10:00, then it will generate a schedule of 2021-08-02 10:00, 2021-08-09 10:00, 2021-08-16 10:00, etc. for all units.", "27725": "Timeslot type", "27726": "By day", "27727": "By time", "27728": "Repeat timeslot & cut-off time", "27729": "Repeat rule", "27730": "The new timeslot will automate the schedule for repeated weekdays and weekends for all SKUs under this package and any shared SKUs. Are you sure you want to add it?", "27731": "The modified cut-off time will auto-sync to all empty schedules for all SKUs under this package and any shared SKUs, but will not auto-sync to existing schedules. Are you sure you want to modify it?", "27732": "Inventory model", "27733": "Total inventory", "27734": "The set maximum number of inventory that users can book for the entire schedule (combined inventory for all schedules is a set quantity)", "27735": "Inventory limited by day", "27736": "The amount of inventory is independent for each schedule per unit", "27737": "Unlimited inventory", "27738": "There is no limit on the inventory amount for each schedule per unit", "27739": "The price is fixed for all schedules within the unit", "27740": "Price can vary for each schedule per unit", "27741": "You won't be able to change the merchant and retail/selling currencies after saving. Are you sure you want to save?", "27742": "The price model can only be changed when the package and SKUs under this package are unpublished", "27743": "The inventory model can only be changed when:<br/>- No unit has a shared relationship for schedule and inventory with other units from another package<br/>- The package and any units under the package are unpublished", "27744": "Switching inventory models will remove all existing inventory data, which cannot be recovered. Are you sure you want to switch the inventory model of this package?", "27745": "Switched successfully. Please enter new inventory data at the \"Calendar settings\" for each unit in this package.", "27746": "Switching price models will remove all existing price data, which cannot be recovered. Are you sure you want to switch the price model of this package?", "27747": "Switched successfully. Please enter new price data at the \"Calendar settings\" for each unit in this package.", "27748": "Select a unit type to create", "27749": "Common unit types", "27750": "Customized unit types (for the selected activity only)", "27751": "Customize", "27752": "Create new unit", "27753": "Shared relationship", "27754": "Main unit", "27755": "This unit will share and update the schedule and inventory to other units", "27756": "Sub-unit", "27757": "This unit's schedule and inventory is shared and updated from another unit", "27758": "Individual unit", "27759": "This unit has a schedule and inventory independent of other units", "27760": "Step 1: Shared relationship settings (between units)", "27761": "Step 2: Calendar settings (schedule/inventory/price)", "27762": "Copy unit info/Shared relationship/Schedule/Price/Inventory from another unit", "27763": "Select a unit to copy from", "27764": "Select package", "27765": "Select unit", "27766": "Is the schedule and inventory of this unit shared and updated from another unit?", "27767": "\"Yes\" means that the schedule and inventory of this unit (sub-unit) will be shared with the main unit selected below. You can only edit the schedule and inventory from the main unit and the change will be automatically applied to the sub-unit.<br/><br/>\"No\" means that the schedule and inventory is not shared with other units and they can be edited directly.<br/><br/>You can only select a unit within the same activity to be the main unit. If you currently have only one unit under this activity, please create another unit which will share its schedule and inventory.", "27768": "Select the main unit which will share its schedule and inventory", "27769": "View shared relationship", "27770": "Current unit", "27771": "Are you sure you want to remove the shared relationship between this unit and the main unit?", "27772": "Unlink items", "27773": "Are you sure you want to remove the shared relationship from all units?", "27774": "Are you sure you want to change the relationship?", "27775": "Creating new shared relationships will remove all existing schedule, inventory and price data, which cannot be recovered. Are you sure you want to continue?", "27776": "Can't switch to \"yes\" when the SKU status is \"published\"", "27777": "Can't set this unit as the main unit because it's a sub-unit in another shared group", "27778": "Can't set this SKU as the main SKU because its schedule and inventory are incomplete", "27779": "Can't set this unit as the main unit because the inventory type doesn't match", "27780": "Please publish the main unit first", "27781": "Please publish the main unit linked to this unit first", "27782": "Please unpublish all sub-units linked with the main unit first", "27783": "Total inventory", "27784": "Total inventory & fixed price settings", "27785": "Total inventory setting", "27786": "Fixed price setting", "27787": "Add timeslot", "27788": "Auto-extend", "27789": "Bulk add/edit", "27790": "Click to add timeslot in package rule, which will be applied to all units in this package", "27791": "Starting from the last day, the schedule will be auto-extended daily according to the settings below. The system will auto-extend from today.", "27792": "End date (Auto-extend until yyyy-mm-dd)", "27793": "Unlimited", "27794": "Limited", "27795": "Select a date or date range", "27796": "Empty schedule", "27797": "Inventory and price data not found for this date", "27798": "Frontend visibility", "27799": "Deactivated schedule", "27800": "Click here to select a start date to set up inventory and price", "27801": "Click start date", "27802": "Click end date", "27803": "Do you want to edit the inventory and prices for all selected dates?", "27804": "Yes", "27805": "Can't edit the schedule and inventory of this sub-unit because its auto-synced by the main unit. Please go to the main unit to edit.", "27806": "You need to go to {Total Inventory & Fixed Price Setting} to edit", "27807": "Editing the schedule/inventory of this main unit will also change the schedule/inventory of all linked sub-units. Do you want to continue?", "27808": "Are you sure you want to delete the selected schedules? This will only remove the inventory and price.", "27809": "Schedule status", "27810": "If empty", "27811": "Empty schedule", "27812": "Inventory and price data not found for this schedule", "27813": "Non-empty schedule", "27814": "Inventory and price data have been saved for this schedule", "27815": "Can't modify the schedule/inventory of this sub-unit because it's auto-synced with the main unit. You can modify the main unit.", "27816": "Please go to {Total Inventory & Fixed Price Setting} to update", "27817": "Editing the schedule or inventory of this main unit will auto-sync with all attached sub-units. Do you want to continue?", "27818": "Do you want to edit?", "27819": "Are you sure to you want to delete the selected schedules? This will only remove the inventory and price.", "27839": "Can't be set as the main SKU because there is an additional timeslot that can't be found", "27840": "Can't set this SKU as the main SKU because the inventory type doesn't match (PIC/Supply API/Others)", "27841": "Please remove the current shared relationship first", "27978": "Please unpublish this unit first", "27979": "Turning off the auto-extend function will remove any existing auto-extend data on this page. Are you sure you want to turn it off?", "27980": "Booking reference ID", "27982": "Time", "27983": "Booking time", "27984": "Participation date", "27985": "Select date", "27986": "Please select", "27987": "Please enter", "27988": "Contact person", "27989": "Color tag", "27994": "Last 7 days", "27995": "Last 30 days", "27996": "Today", "27997": "Tomorrow", "27998": "Booking status", "27999": "Message Klook", "28000": "Note", "28001": "Impending", "28002": "There are less than 24 hours from the participation time for you to confirm", "28003": "Overdue", "28004": "It's already past the confirmation time set in the system", "28005": "Activity info", "28006": "Participation info", "28007": "Confirmation info", "28008": "Contact info", "28009": "Other info", "28010": "Activity ID", "28011": "Activity name", "28012": "Package name", "28013": "Cost price amount", "28014": "Participation time", "28015": "Unit", "28016": "No. of units", "28017": "Voucher type", "28018": "Confirmation time", "28019": "Full name", "28020": "Email", "28021": "Phone number", "28022": "General \"Other info\"", "28023": "Non-standardized \"Other info\"", "28024": "Confirm", "28025": "Reject", "28026": "Re-confirm", "28027": "Edit", "28028": "Enter voucher info", "28030": "Enter the remark info if necessary. It will display on the user's voucher.", "28031": "Please use the user's selected language ({language}). If you cannot, please use English.", "28032": "Pick-up time:", "28033": "Pick-up/meet-up location (English):", "28034": "Pick-up/meet-up location (Local language):", "28035": "Confirm", "28036": "Cancel", "28037": "Voucher no.", "28038": "Attachment", "28047": "User selected language", "28048": "Voucher info", "28084": "Please set the shared relationship for Supply API products whose Inventory is auto-synced by API, because the integration is still in the package level and the Inventory would only update to the Main SKU (not all SKUs) in this package in short-term", "28154": "Cannot copy from this unit because they have a different inventory model", "28155": "Cannot copy from this unit because it has a different price model", "28156": "Cannot copy from this unit because there is an additional timeslot that can't be found", "28157": "Cannot copy from this unit because the currencies do not match", "28158": "Booked/remaining inventory", "28232": "Need help managing the schedule, inventory or price? <a href=\"MULTILANG_URL_PLACEHOLDER\">Download the Guidebook</a>.", "28235": "Published language", "28236": "Switch editable language for this activity", "28237": "Change merchant's activity management language", "28238": "Published with AI translation", "28239": "Any free text will auto-sync with American English, and an AI translation button will be provided. Only the title and any template-generated content will be published using a human translation.", "28240": "Published with American English", "28241": "Any free text will auto-sync with American English", "28242": "Merchant's activity management language:", "28243": "Heads up: The editable language can only be changed once a week", "28244": "Can't switch the editable language for this activity because it’s already marked as your activity management language", "28245": "Can't select the language published with AI Translation or American English as this activity’s editable language", "28246": "Activity creator", "28247": "Package creator", "28248": "Booked/remaining inventory", "28249": "Please go to the <a href=\"MULTILANG_URL_PLACEHOLDER\">Activity Management Backend</a> to manage the timeslot and inventory", "28250": "Can merchants edit?", "28251": "Published!", "28252": "Do you want to open this activity to the merchant for self-management?", "28253": "If yes, you cannot add packages with other Merchant ID anymore", "28254": "Which published language do you want to open for editing?", "28255": "You can go to the <a href=\"MULTILANG_URL_PLACEHOLDER\">Merchant list</a> to change the activity management language", "28256": "<PERSON>ide later", "28257": "Can't add other merchant IDs to the activities that are self-managed by merchants", "28259": "Can't be edited if the activity's status is in draft or if it has multiple merchant IDs", "28260": "Can only open to merchant when activity proceed to published/unpublished status", "28261": "Editable language", "28298": "Timeslot and inventory management for activities", "28309": "Confirm within {hours} hours", "28311": "White", "28312": "Purple", "28313": "Yellow", "28314": "Green", "28444": "Best Price Guarantee", "28445": "Remove Klook watermark", "28632": "Delivery info needed?", "28633": "No", "28635": "Yes", "28638": "Required info when making a booking", "28640": "Delivery details", "28679": "Terms & Conditions", "28681": "Copy unit", "28682": "SSP", "28686": "T&Cs confirmation needed?", "28690": "Add", "28691": "This step is completed on the old admin page", "28692": "You can open the old admin page to edit", "28693": "Open old admin", "28694": "This step is not completed on the old admin page", "28695": "Opening the old admin page", "28696": "The content is currently being edited on the old admin page", "28697": "This step is completed on the old admin page", "28698": "The old admin page is closed", "28699": "Something isn't right with the URL. Please check the URL again.", "28700": "The old admin page is closed. Please make sure all the required fields are completed in the old admin.", "28701": "Couldn't open. Please try again.", "28792": "Activate?", "28793": "Active", "28794": "Deactivated", "28795": "Booked inventory is the number of inventory that has been booked by the user (includes any non-confirmed bookings)", "28799": "Help", "28800": "Enter photo alt text (for SEO)", "28810": "Concession", "28851": "Only editable with AM access", "28852": "Only editable with EM access", "28910": "Can't edit this field because it is API integrated and will be auto-updated", "28917": "Pick up and return time", "28958": "\"Other info\" hint", "28959": "\"Other info\" dropdown option(s)", "28973": "Please make sure the dropdown options have the same order for each language", "28984": "Delivery Charge", "28985": "<PERSON><PERSON>", "28986": "SIM card", "28987": "No. of YSIM (with Data Package)", "28988": "Data Package", "28989": "Voucher", "28990": "Room", "28991": "Night", "28992": "You can select up to {20} photos to bulk upload", "28993": "Cannot add or publish new SKU (Only 8 published SKUs at most)", "28994": "{num} days in advance", "29043": "This extra info cannot be edited due to Merchant-API restrictions. Please contact tech team for assistance", "29045": "This can only be changed when the package and units under this package are unpublished", "29046": "Switching merchants will remove all existing price data, which cannot be recovered. Are you sure you want to switch the merchant of this package?", "29052": "You cannot add new units while units on this page are open. Click anywhere on the unit card to close it.", "29093": "Characters: {num}", "29094": "Selling price cannot be set lower than the cost price", "29095": "If visible on the frontend, the schedule is visible and bookable by users when the unit is published", "29147": "Title", "29148": "First & last name (English)", "29149": "First & last name (Local language)", "29155": "Create new", "29156": "Search", "29157": "AID/Package ID/Unit ID", "29158": "No data found", "29159": "Apply price on the selected unit", "29160": "Upload", "29161": "Upload expected prices", "29162": "Upload", "29163": "Download template", "29164": "Start date for editing", "29165": "Submit", "29166": "Cancel", "29167": "Today", "29168": "Activity", "29169": "Package", "29170": "Unit name", "29171": "Unit ID", "29172": "Type", "29173": "Currencies", "29174": "Apply", "29175": "Updated", "29176": "Current price", "29177": "Reason for price edit", "29178": "Select reason", "29179": "Please select reason", "29180": "To price match with competitors", "29181": "Price experiment", "29182": "Price adjustment for promotion", "29183": "New MSP regulation", "29184": "Others", "29185": "Please enter other reason", "29186": "Submit", "29187": "Cancel", "29194": "Undo", "29195": "Please set the shared relationship for all units under this package first. Go to \"Price & inventory\", set a Main unit and then attach other units as sub-units.", "29201": "You need to go to “Total Inventory & Fixed Price Setting” to update", "29202": "Please go to \"Total inventory settings\" to edit", "29203": "Please go to \"Fixed price settings\" to edit", "29251": "Sorry, there's too many results ({num1} schedules) to display. Up to {num2} can be displayed. Please change your filters to show less results e.g. Select a shorter duration, less units, etc.", "29252": "Current package", "29254": "Updated successfully!", "29257": "Filter columns", "29258": "We recommend using \"Bulk add/edit\" to add or change schedules, prices and inventories", "29260": "This timeslot rule and inventory/price model already exists", "29261": "Please select a valid inventory model", "29262": "Please select a valid price model", "29263": "Please select a valid selling currency", "29264": "Please enter a repeat rule", "29265": "Invalid timeslot type", "29266": "Sorry, the timeslot rule and model do not exist", "29267": "Please unpublish the package and all sub-units under this unit first", "29268": "This unit can't be published without an available/bookable schedule", "29269": "Please remove the shared relationship under this unit first", "29270": "Please enter a valid inventory number", "29271": "Please enter a price", "29280": "Save as draft", "29417": "Cannot submit because either the selling price (for auto-extend timeslot) is lower than the MSP (Minimum Selling Price) or the cost price is higher than the MSP.", "29434": "You can apply the schedule by day of the week without a specific participation/departure time", "29435": "You can apply the schedule by day of the week with a specific participation/departure time. The time can vary per day.", "29438": "Is there a limit on the amount of units that a user can book? If so, select \"Limited\" and enter the min./max. units.", "29439": "The currency for setting the retail price", "29440": "This is combined inventory for all schedules within this unit", "29441": "Please enter the inventory and price that will be reflected when the schedule is auto-extended (This inventory and price will not be applicable to existing schedules).<br/><br/>When using the fixed pricing and unlimited/total inventory model, the values will be entered automatically and cannot be changed.", "29502": "<span style=\"font-weight:bold;\">{target_Chinese} </span>will be synced automatically", "29503": "<span style=\"font-weight:bold;\">/ {language_name} will NOT be synced automatically</span>  (Tip: use the clone Chinese tool to force it, if needed)", "29516": "<span style=\"font-weight:bold;\">Heads up: </span>We're upgrading the system on {2021-08-18}, {6:00-10:00am HKT}. During this time, onboarding/activity management won't be accessible.", "29549": "You'll need to select a language to download the translation status and publish status", "29550": "Language", "29551": "Download", "29552": "Full search result: {number} IDs", "29553": "Current page result: {number} IDs", "29554": "Download", "29555": "Cancel", "29557": "Notice:  Please wait 2 minutes before downloading again to avoid the system being overloaded.", "29559": "Search result found: {number} AIDs", "29561": "Publish Language", "29562": "Package ID {Package_ID}: please select the finance model first", "29597": "Exploitation of animals (e.g. panda hugging, elephant riding, safari)", "29598": "Biases/prejudices (e.g. sexism, racism)", "29599": "Drugs (e.g. Amsterdam coffee shops, ganja walking tours)", "29600": "Children (e.g. orphanage tours, activities involving charitable organizations)", "29601": "Exploitation of socio-economic situation (e.g. slum tours)", "29602": "LGBT (e.g. gay-friendly tours, pride parades/festivals)", "29603": "Post-crisis tours (e.g. earthquakes, nuclear disasters, super typhoons)", "29604": "Sex (e.g. adult toys museum, red light district tours)", "29605": "Sustainability issues (activities that may lead to environmental damage/pollution)", "29606": "Medical treatments (e.g. vaccinations, DNA tests, plastic surgery)", "29607": "Refugees (e.g. refugees working illegally in foreign countries)", "29608": "None of the above", "29626": "View details", "29632": "Original image", "29633": "Desktop", "29634": "Mobile", "29635": "Videos", "29636": "Note that videos may need more time to load", "30114": "Bulk edit", "30127": "{SKUID_name}", "30128": "{skuid_name}: Please enter the completed inventory and price for the schedule", "30131": "Set fixed SSP", "30132": "SSP = Selling price x % entered x exchange rate", "30133": "Add fixed SSP", "30134": "Add elastic SSP", "30135": "Expiry time", "30154": "Example: To increase the SSP by 10% compared with the selling price, enter 110%", "30162": "Suggest to change participation date", "30163": "Participation date (Local time)", "30164": "The merchant suggests to change the participation date to {date} (local time)", "30165": "Please select a suggested participation date", "30225": "Please publish the units first", "30285": "Schedule created with inventory", "30286": "Schedule created with inventory", "30287": "Fullscreen", "30288": "Exit fullscreen", "30289": "Select schedule(s) to make edits in this field. Changes will apply to all selected schedules.", "30290": "Edited but not saved", "30291": "Deleted but not saved", "30292": "Copy timeslot rule & model", "30293": "Select a package to copy from", "30294": "Select day(s) to copy to", "30295": "Failed to save", "30296": "{Monday_12_00}/{Monday_14_00} are currently being used in the units listed below. Please delete these items before making any changes.", "30297": "This list only displays up to 50 schedules", "30298": "Are you sure you want to switch from \"by day\" to \"by time-00:00\"?", "30299": "Select day(s) and timeslot(s) to copy to", "30300": "Click \"Add\" to create a new unit", "30301": "Click on a cell to edit", "30409": "Activate", "30432": "Destination details are automatically entered based on the coordinates provided above", "30433": "Sorry, we can't seem to find a city based on the coordinates entered above. {click_here} to the geo manager (<EMAIL>) to add the city in our database. Please wait for feedback before you continue filling in the fields below.", "30434": "Send a creation request", "30435": "The city has been changed from “{previous_country_previous_city}” to “{new_country_new_city}”", "30436": "Submitted successfully", "30437": "The geo manager (<EMAIL>) will get in touch by email within 1 working day", "30438": "This activity will be displayed on the following destination page(s): {Page_ID_Page_Name_publish_status}.<br/><NAME_EMAIL> or <EMAIL> for any questions.", "30439": "The city above is not linked to any destination page. <NAME_EMAIL> or <EMAIL> to resolve this issue.", "30440": "Click \"Save\" at the bottom of the page to apply changes to destination details based on coordinates entered (previous city: {city})", "30441": "The current city displayed needs to be updated, but we can't seem to find the new city based on the coordinates entered above. Please {create_a_new_city} or {modify_the_location_coordinates}.", "30442": "Sorry, you won't be able to make changes to this page until the city is updated based on the coordinates entered. Please {create_a_new_city} or {modify_the_location_coordinates}.", "30443": "send a city creation request", "30444": "change the location coordinates", "30526": "Ticket type", "30527": "Fixed date ticket", "30528": "Open date ticket", "30529": "Does this activity need a calendar displayed to customers for selecting a participation date?", "30530": "Display calendar", "30531": "No calendar", "30532": "Open date ticket type", "30533": "Valid from {booking_confirmation_date} for {num} day(s)", "30534": "Expires on {the_last_day}, {hh_mm}", "30535": "Expires on {the_same_redemption_time_of_the_last_day}", "30536": "Valid from {date_range}", "30537": "Valid until {fixed_date}", "30538": "Valid before the selected date", "30539": "Available days", "30540": "Unavailable dates", "30541": "None", "30542": "Select date(s)", "30543": "Additional notes", "30544": "Validity preview", "30545": "Validity", "30546": "The voucher is valid only on the specified date (and time if applicable)", "30547": "The voucher is valid for {num} day(s) from {booking_confirmation_date}. It expires at {hh_mm} on the last day", "30548": "The voucher is valid for {num} day(s) from {booking_confirmation_date}. It expires at {the_same_redemption_time}. (I.e. If {booking_confirmation_participation_redemption_time} is at 13:00, it will expire at 13:00 {num} day(s) later)", "30549": "The voucher is valid for {num} day(s) from {booking_confirmation_date}. It expires at {hh_mm} the day after the last day", "30550": "The voucher is valid from {fixed_date_1} to {fixed_date_2}", "30551": "The voucher is valid until {fixed_date}", "30552": "The voucher is valid before the selected date", "30553": "The voucher is valid for {weekday1_weekday2_weekday3}", "30554": "The voucher is not valid for {date1_date2_date3_date4_date5}", "30555": "the booking confirmation date", "30556": "the participation date", "30557": "the redemption/activation date", "30558": "the last day", "30559": "the day after", "30560": "the same redemption/activation time on the last day", "30561": "the same participation time on the last day", "30562": "the same booking confirmation time on the last day", "30607": "the booking confirmation time", "30608": "the redemption/activation time", "30609": "the participation time", "30717": "Show package options as package cards on mweb & app", "30718": "If switched on, packages will appear as package cards on activity pages. Only activities with a \"no attributes matching\" status can use this function. We recommend using this function only if the activity has a low number of packages.", "30720": "About price fluctuation: The AID is under a price engine rule. Admin tool selling price and frontend price might not match. For more information, please reach out to the Pricing Team at<span style=\"font-weight:bold;\"> <EMAIL></span>.", "30722": "About price fluctuation: Some units under this AID are part of a promotion: <span style=\"font-weight:bold;\">{event_name}</span>. For more information, please reach out to the POC in your region (See <a href=\"MULTILANG_URL_PLACEHOLDER\">POC list</a>).", "30924": "Customized other info", "31139": "The fixed special selling price has been edited or deleted. Click 'Cancel' to revert the changes.", "42681": "Unit IDs applicable for reservation", "42682": "How many products can be reserved?", "42683": "You can only publish 1 unit per reservation package", "42684": "Choose \"Usage Validity\" to set 1 validity period (applicable if the customer has to activate the pass and participate in an activity within a specific time after their booking date). Choose \"Activation validity and usage validity\" to set 2 validity periods (applicable if the customer has to activate the pass within a specific time after their booking date, but can participate within a different period).", "42685": "Usage validity only", "42686": "Activation validity and usage validity", "42687": "Activation validity", "42688": "Usage validity", "42690": "Valid from {booking_confirmation_date_participant_date} for {num} days", "44199": "Bulk input", "44200": "Bulk select", "44201": "Fixed price models are non-editable", "44202": "Total inventory models are non-editable", "44203": "Sub units are non-editable", "44206": "<span style=\"font-weight:bold;\">Heads up: </span>We're upgrading the system on Nov 2, 9:00-14:00 (HKT). During this time, price submission won't be accessible.", "44207": "Pending confirmation from <PERSON><PERSON>", "44208": "This booking needs a second confirmation from Klook (The booking status will then change to \"Confirmed\")", "44233": "Sub-category", "44291": "Please enter the number of products that can be reserved for the new unit", "44333": "Upload", "44907": "<span style=\"color:text-color-normal;\">Recommends that merchants set their price based on this amount</span>", "44908": "<span style=\"color:text-color-normal;\">Gives merchants a reference to the selling price in the wider market</span>", "44909": "MSP setting", "44910": "Minimum Selling Price settings", "44911": "MSP", "44912": "Enter an amount", "44913": "Select currency", "44914": "Add", "44915": "Confirm", "44916": "Cancel", "44919": "Invalid Minimum Selling Price found", "44920": "The MSP entered is lower than the cost of the current SKU. Please review the active or pending costs in the list below and the corresponding MSPs before resubmitting.", "44921": "Effective", "44922": "Amount", "44923": "Created", "44924": "Created by", "44925": "OK", "44926": "Back", "44927": "The MSP entered is higher than the selling price or special selling price of the current SKU. Please review the active or pending prices in the list below and the corresponding MSPs before resubmitting.", "44928": "Type", "44929": "<PERSON><PERSON><PERSON><PERSON>", "44930": "The MSP entered is lower than the cost of the current SKU. Please review the active or pending costs in the list below and the corresponding MSPs before resubmitting.", "44931": "Schedule", "44932": "The MSP entered is higher than the selling price or special selling price of the current SKU. Please review the active or pending prices in the list below and the corresponding MSPs before resubmitting.", "44950": "Search by Activity ID (you can only select 1 AID)", "45672": "QR code", "45673": "Back to activity list", "45674": "Add express check-out activity", "45675": "Select activity & landing page", "45676": "Download QR code", "45677": "Default language", "45678": "Default currency", "45679": "You can copy UTM information from the link generated by the Campaign Tracking Link Generator tool.<br/><br/>Only letters, numbers, hyphens (-) and underlines (_) can be used in the fields below.", "45680": "UTM campaign", "45681": "UTM medium", "45682": "UTM source", "45683": "UTM term", "45684": "Save & add", "45688": "Enter info from the link generated by Campaign Tracking Link Generator tool", "45689": "For configuring express check-out: <br/>- Activity status must be published <br/>- Activity must be listed under the \"Unpublish\" for the country name<br/>- Activity must be in TTD template, i.e. frontend activity page using TTD template<br/>- Activity must have a new AID separate from the regular AID<br/>- Voucher code type must be a QR code<br/>- All packages are recommended to have instant confirmation with KLK voucher, to ensure the voucher fulfilment efficiency", "45737": "Retail currency", "45738": "Retail currency", "47523": "Copy from existing (new)", "47524": "Copy from existing (old)", "47525": "You've already selected {common_other_info_name_1_common_other_info_name_2} for this package. Are you sure you want to overwrite it with the copied version?", "47526": "Yes", "47569": "Please use a valid format", "47784": "Arabic", "48066": "Export (with \"Extra info\")", "48068": "Cost approval status", "48069": "Rejected", "48070": "To be submitted", "48071": "Approved", "48072": "Pending", "48073": "The unit has draft cost prices. You can clear the drafts or submit the draft costs for approval.", "48074": "Submit cost", "48075": "Clear drafts", "48076": "See approval status", "48077": "The cost previously entered is still pending and can be withdrawn", "48078": "Withdraw", "48079": "See approval status", "48080": "The cost was rejected and is still a draft. See the approval status to find out the rejection reason. You can clear the drafts to view the published costs or re-submit the draft costs for approval.", "48081": "Re-submit cost", "48082": "Clear draft", "48083": "See approval status", "48084": "Are you sure you want to clear the drafts? The approval ticket will be closed it can't be restored.", "48085": "Clear drafts", "48086": "Go back", "48087": "Selecting published cost price will display the cost price that is currently used", "48088": "Draft cost prices", "48089": "Published cost prices", "48090": "Submit cost", "48091": "See details", "48092": "Submit cost", "48093": "Cancel", "48094": "By seasons", "48095": "Promotion", "48096": "Change in operational cost/strategy", "48097": "Ticket ID:", "48098": "Type:", "48099": "Price change", "48100": "Language:", "48101": "Chinese", "48102": "Submitted:", "48103": "Merchant ID/name:", "48104": "Rejected", "48105": "To be submitted", "48106": "Approved", "48107": "Pending", "48108": "Withdraw", "48109": "Reject", "48110": "Approve", "48111": "Activity name:", "48112": "Package name:", "48113": "Unit:", "48114": "Unit ID:", "48115": "Unit Type:", "48116": "Status:", "48117": "Relationship:", "48118": "Auto extended", "48119": "Timeslot", "48120": "Draft cost price", "48121": "Published cost price", "48122": "New selling price", "48123": "Draft take rate", "48124": "SSP", "48125": "Published selling price", "48126": "Published take rate", "48127": "Cost", "48128": "Date", "48129": "Timeslot", "48130": "Weekly", "48131": "All", "48132": "Each Sun", "48133": "Each Mon", "48134": "Each Tue", "48135": "Each Wed", "48136": "Each Thu", "48137": "Each Fri", "48138": "Each Sat", "48139": "Search", "48140": "Reset", "48141": "Calendar view", "48142": "List view", "48143": "Filter by schedule", "48144": "Draft cost price", "48145": "Published cost price", "48146": "Select all", "48147": "Draft selling price", "48148": "Draft take rate", "48149": "SSP", "48150": "Published selling price", "48151": "Published take rate", "48152": "You'll need to approve/reject any pending tickets related to this unit first", "48153": "Approval details", "48154": "Pending tickets found", "48155": "The package has pending tickets submitted by merchants about price change. Changing the price model will automatically close all tickets. Continue?", "48156": "Change", "48157": "Cancel", "48158": "Pending tickets found", "48159": "The package has pending tickets submitted by merchants about price change. Changing merchants will automatically close all tickets. Continue?", "48160": "Change", "48161": "Cancel", "48173": "Please select an activity before exporting", "48218": "Closed", "48219": "Activity approval status", "48220": "Package approval status", "48221": "Unit quantity", "48222": "Unit ID - Name", "48223": "Unit status", "48224": "Approval list", "48225": "Calendar-based price", "48226": "Ticket ID", "48227": "Ticket type", "48228": "New activity", "48229": "New package", "48230": "Ticket status", "48231": "Please contact your account manager to edit any free text", "48246": "Please publish the activity first", "48286": "About this activity (Shown as \"What to expect\" to customers)", "48290": "The system can't associating any newly created units with the inventory in PIC. You'll need to first finish the fixed price setting. The system will then generate schedules and inventory after the unit is associated with inventory info in the PIC.", "48291": "The system can't associating any newly created units with the inventory in PIC. The system will then generate schedules and inventory after the unit is associated with inventory info in the PIC. After association, the system will send out an email reminder to set price on the newly created schedule.", "48292": "The inventory type of this package has been selected as \"Inventory\" and as a result the schedule and inventory can't be edited. It can only be updated by the data from PIC system.", "48293": "Item ID:", "48294": "The item ID is used in the PIC system for associating the inventory with the unit", "48295": "Heads up: Changing inventory type from Inventory to API/Others will reset the inventory data", "48296": "Heads up: Changing inventory type from API/Others to Inventory will reset all of the schedules. The schedule and inventory will only rely on the automation with the PIC.", "48297": "Change", "48298": "Cancel", "48468": "The inventory type of this package has been chosen as \"Inventory\". Therefore the schedule and inventory won't be editable and can only be updated by the data from the PIC system", "48475": "The merchants of the units are different. Please make sure the unit has the same merchant ID as the main unit has before creating sharing relationship", "49362": "The cost price is the amount that Klook pays to merchants", "49363": "The inventory shows the number of tickets/products available to sell", "49364": "The retail price is provided by merchant as the suggested selling price or the official price of the products in the market. It could be listed on the activity page for customers' reference but not necessarily at the price Klook will use.", "49365": "When selected, the calendar/list will show those costs that need to be submitted for approval", "49366": "The units have pending tickets submitted by merchants about cost change. Sharing units will automatically close all tickets of this sub-unit. Continue?", "49403": "Searchable/viewable on Klook?", "49588": "Gallery", "49651": "The link is invalid, please fill it in the correct template. For example: http(s)://www.example.com", "49826": "Confirm", "54043": "Check box content", "54044": "Please confirm that you acknowledge the following:", "54045": "Please enter content for the check box", "54054": "Up to 50 characters", "70252": "Ticket details", "72048": "The retail price is provided by merchant as the suggested selling price or the official price of the products in the market.", "72049": "The cost price is the amount that Almosafer Activities pays to merchants", "72169": "Jan", "72170": "Feb", "72171": "Mar", "72172": "Apr", "72173": "May", "72174": "Jun", "72175": "Jul", "72176": "Aug", "72177": "Sep", "72178": "Oct", "72179": "Nov", "72180": "Dec", "72518": "No data found based on your search terms", "72753": "Experience activities", "72856": "This ticket type can't be viewed in the Klook Merchant app. You'll need to use the desktop web platform.", "72857": "See Approval detail on web", "73053": "This is a combo unit - the schedule, inventory and prices can't be edited here. You'll need to edit them at the standalone unit.", "73054": "{otherinfo_name} (from package {package_id_1}, {package_id_2})", "73055": "{otherinfo_name} (sourced from package {package_id})", "73056": "View & edit", "73057": "Merge \"Other info\"", "73058": "Details", "73059": "Selected \"Other info\"", "73060": "<span style=\"font-weight:bold;\">Why merge \"Other info\"?</span><br/>Merging allows users to enter their info once for both activities - and speed up the booking process.<br/><br/><span style=\"font-weight:bold;\">How to merge?</span><br/>1. You'll need to merge the \"Other info\" from the standalone packages into the combo package. <br/>2. Make sure you selected the correct \"Other info\" to merge. You can double-check these under 'Selected \"Other info\"'.<br/>3. Merge the 'Other info'.<br/><br/><span style=\"font-weight:bold;\">Tips</span><br/>- In the free text box, enter a new \"Other info\" that fits the original \"Other info\"<br/>- For dropdown options, merge the original options to a new one to be displayed in the Combo package (Any options not merged will not be displayed)", "73061": "Merge the options shown below first", "73062": "Please enter content for combo package", "73063": "<PERSON><PERSON>", "73064": "Split", "73065": "{x}/{y} items", "73066": "{z} item(s)", "73067": "Standalone packages", "73068": "Combo package", "73069": "Save", "73070": "Cancel", "73071": "Heads up", "73072": "If you leave this page, you'll lose any unsaved information", "73073": "Don't remind me again", "73074": "Leave", "73075": "Stay", "73076": "Are you sure you want to merge the \"Other info\"?", "73077": "<PERSON><PERSON>", "73078": "Are you sure to split the selected \"Other info\"? Once split, all settings will be removed.", "73079": "Are you sure you want to change the package? Once changed, the price and inventory will reset.", "73080": "Cancel", "73081": "Package name (optional)", "73082": "Optional package names are used for standalone packages to be distinguished from the regular package. This name will be shown on the checkout page, booking details page, vouchers and confirmation emails - <span style=\"font-weight:bold;\">please keep it concise</span>.", "73083": "Standalone units", "73084": "The following packages don't meet the criteria because there's no applicable timeslot:  {pkg1_id}, {pkg2_id}", "73085": "The following packages don't meet the criteria because the timeslot rule must be by day: {pkg1_id}, {pkg2_id}", "73086": "The following packages don't meet the criteria because there's no applicable min./max. bookings quantity: {pkg1_id}, {pkg2_id}", "73087": "The following standalone packages aren‘t published: {pkg1_id}, {pkg2_id}", "73088": "The \"Other info\" can't merged because of conflicting settings. You'll need to adjust the \"Other info\" of standalone packages or select different \"Other info\".", "73089": "Changes to the following standalone packages don't meet the criteria for combo creation: {pkg1_id}, {pkg2_id}", "73090": "The following standalone units aren't published: {sku1_id}, {sku2_id}", "73091": "The following units don‘t meet the criteria because there's no applicable min./max. bookings quantity: {sku1_id}, {sku2_id}", "73092": "The following units don't meet the criteria because the combo price is higher than the sum of standalone units: {sku1_id}, {sku2_id}", "73093": "Product type", "73094": "Regular package", "73095": "Combo package", "73096": "Combo details", "73097": "Standalone packages", "73098": "Select a package", "73099": "No results found", "73100": "Heads up", "73101": "Are you sure you want to save these standalone packages together? Once saved, the combination can't be changed.", "73102": "I'm sure", "73103": "Go back", "73104": "Add", "73105": "Step 1: Combo setting", "73106": "Combo selling price setting", "73107": "Amount", "73108": "Percentage", "73109": "Manual setup", "73110": "The manual setup has been removed in Sep 2023. Accordingly, all related combo packages changed into Percentage mode set with 100% of the original price.<br/><br/><span style=\"font-weight:bold;\">1. Amount</span><br/>- For deducting an amount off the original selling price when the packages are in a combo. The price change for the standalone packages will be synchronized to the combo unit. E.g. If Package A is USD 100 and Package B is USD 200, you can set the selling price for the combo to 270 to deduct USD 10 from A and 20 from B.<br/><br/><span style=\"font-weight:bold;\">2. Percentage </span><br/>- For deducting a percentage off the original selling price when the packages are in a combo. The price change for the standalone package will synchronize to the combo package. E.g. If Package A is USD 100 set at 90% of the original price and Package B is USD 200 set at 80% of the original price, the combo package's selling price is USD 250.", "73111": "Combo tag", "73112": "Save {x}% with this combo", "73113": "Buy {y} get {z} free", "73114": "The tag will be shown on the package card for combo packages", "73115": "Merge \"Other info\"", "73116": "{xx} x \"Other info\" selected", "73172": "Package publishing restriction settings", "73174": "Use this page to set packages' publishing restrictions by language and IP location. I.e. If the package is set as available in Singapore only, then only customers with a Singapore IP can see it. <br/><br/><span style=\"font-weight:bold;\">Heads up:</span> <br/>- IP location accuracy is around 87% on Klook. If you need stricter requirements (e.g. proof of ID needed), you can set them from the \"Other info\" in the admin onboarding backend.<br/>- The language fencing function doesn’t work well if this language is published with en_US, or if published with AI.<br/>- For reservation packages included in the Klook Pass that can't be sold separately, remember to set them as \"For Klook pass\" for the \"Available only\" under the Package restriction section", "73175": "Language", "73176": "IP location", "73177": "Set conditions", "73178": "A) Available to all languages", "73179": "B) Available to selected languages", "73180": "C) Unavailable to selected languages", "73181": "D) Unavailable to all languages", "73182": "A) Available to all IP locations", "73183": "B) Available to selected IP locations", "73184": "C) Unavailable to selected IP locations", "73185": "D) Unavailable to all IP locations", "73186": "Select a condition", "73188": "Select languages", "73189": "Select countries", "73190": "Heads up", "73192": "If you select all languages, then the condition will automatically change to {option}", "73193": "If you select all IP locations, then the condition will automatically change to {option}. Continue?", "73228": "Select dates to allow for bulk editing", "73229": "Inventory and price added", "73231": "Deactivated", "73246": "The \"Other info\" can't be merged because they are from the same package", "73247": "The \"Other info\" can't be merged because they are from different sub-categories", "73248": "The \"Other info\" can't be merged because they are under different \"Other info\" types", "73249": "Please unpublish this combo unit first before making changes on the standalone units", "73250": "Please unpublish the combo first before making changes to any standalone units", "73518": "Sync API availability", "73519": "Auto-sync the schedule, inventory and price (if available) from the API to the selected units. You'll get informed on Lark when the sync is complete.", "73520": "Only units connected to the API mapping list and with an inventory on the API side can be synced", "73521": "Select a date range to sync from the API", "73522": "Only the price of sub-units can be auto-synced from the API (schedule and inventory excluded)", "73523": "Heads up: Once confirmed, the API-integrated and auto-updated fields (i.e. price/inventory model, schedule, etc.) can't be manually modified. Continue?", "73565": "package(s)", "73566": "See log", "73567": "Ticket details", "73568": "See ticket details", "73569": "tickets", "73570": "Seasonality", "73571": "Enter any additional notes", "73572": "Select a reason for cost submission", "73573": "Package list", "73574": "Tips", "73575": "Invalid URL:", "73899": "Suggested format: (PartnerNameHere).partner.flickket.com", "73900": "Merchant name", "73901": "Domain", "73904": "Add new Flickket white-label merchant", "73965": "Common settings", "73966": "Main color", "73967": "Main font", "73968": "Shortcut icon", "73969": "Merchant logo", "73970": "Merchant customized header (web)", "73971": "Merchant customized header (mweb)", "73972": "Additional tech development is required for customized headers and footers. You'll need to first request this to the product manager. Later they will share a JavaScript link with you that you can enter into this text box.", "73973": "Merchant customized footer (Web)", "73974": "Merchant customized footer (Mweb)", "73975": "Image must be square (1:1 ratio) and in png, ico or gif format.", "73976": "Email settings", "73977": "Email logo", "73978": "Image must be sized 600 x 100 pixels", "73979": "Voucher header logo", "73980": "Voucher header background color", "73981": "Image height must be 24 pixels", "74024": "Flickket white-label ID", "74026": "Voucher settings", "74037": "No inventory or price rule set", "74248": "Please upload a Merchant voucher with proof of Taiwan Escrow", "74249": "- Upload up to 3 images<br/>- Max size: 4MB<br/>- Accepted formats: JPG, JPEG, PNG", "74250": "- The Taiwan Ops Team will review the uploaded proof (it will not affect the activity's onboarding process)<br/>- The proof will not be displayed to customers", "74289": "Package list (App only)", "74290": "Approval list (App only)", "74291": "Event log (App only)", "74292": "Preview (App only)", "74294": "No. of calendar days displayed", "74487": "Want to learn about combos? Find out how they can benefit your business and how to set one up in this <a href=\"MULTILANG_URL_PLACEHOLDER\">handy guide</a>.", "74529": "You can search for any packages that meet the following conditions:<br/>1. Sub category ID: 1, 2, 8, 15, 16, 17, 18, 20, 22, 171, 177, 179, 194, 206, 311, 317, 413, 415, 523, 525, 551, 552, 558, 561, 563, 638<br/>2. For sub category IDs 2 and 15: Confirmation time must not be 48 hours/72 hours before departure<br/>3. Package status: Published, Unpublished, In preview<br/>4. Bookable to agent/user: User only, Agent & user<br/>5. Is not a combo package created in the old combo tool", "75063": "Set as activity card photo", "75064": "The image will display in 16:9. Please wait 24 hours to see any changes on the frontend.", "75065": "By unticking this checkbox, any images uploaded in this language will be deleted and replaced with other images at the activity level. Continue?", "75803": "Please select a photo as the activity card photo", "76166": "Because this voucher will be redeemed offline, the voucher code level must be per booking", "76449": "Activity rate plans", "76450": "Boost your business with simple pricing and discount management", "76451": "Create a new rate plan", "76452": "Select a rate type", "76453": "Choose a rate that caters to your business growth and needs", "76454": "Direct discount", "76455": "E.g. Get 20% off with no minimum spend from 1 August - 31 August", "76456": "Book X get Y", "76457": "E.g. Book 2 adult tickets and get 10% off", "76458": "See how it's displayed", "76459": "How rate plans are displayed", "76460": "Activity list", "76461": "Activity page", "76462": "Select", "76463": "Select which level", "76464": "Choose a product level that is applicable to the rate plan", "76465": "Package level", "76466": "All units (adults, children or senior citizens) under the same package are applicable to the rate plan", "76467": "E.g. Every booking with 2 adults and 1 child gets 20% off", "76468": "Unit level", "76469": "Only one unit (e.g. adult, child or senior) is applicable to the rate plan", "76470": "E.g. Book 3 adult tickets and enjoy 30% off", "76471": "Show all rate plans", "76472": "Select applicable products", "76473": "Selection", "76474": "{num} package(s) selected", "76475": "{num} unit(s) selected", "76476": "Separate activity IDs using \",\"", "76477": "Search activity ID or name", "76478": "Heads up", "76479": "Do you want to update the rate plan level? Any selected products will be lost.", "76480": "Discount", "76481": "Set the units and the discounts for the deal. For example, if using \"Book 2, get 10% off\" and \"Book 5, get 20% off\", the customer can get 10% off for booking 2-4 units or 20% off for booking 5 or more units.", "76482": "Book", "76483": "Get", "76484": "Price preview after discount", "76485": "Original cost", "76486": "Original selling price", "76487": "Discounted cost", "76488": "Discounted selling price", "76489": "(Book {number} unit(s))", "76490": "Heads up", "76491": "Do you want to update the rate type? Any discounts entered will be lost.", "76492": "Discount period", "76493": "Choose the booking period you want to schedule for the rate plan. After saving the information, it'll switch to its destination time zone.", "76494": "Time zone", "76495": "Starts", "76496": "Ends", "76497": "No end", "76498": "Ends on", "76499": "Targeted customers", "76500": "Select which customer segment can enjoy the discount", "76501": "Origin", "76502": "Set the rate plan for inbound traveler or resident segments", "76503": "All customers", "76504": "Inbound customers", "76505": "The customer's IP country/region differs from that of the activity", "76506": "All inbound customers", "76507": "Selected country/region", "76508": "Residents", "76509": "Only available for residents of a country/region. We recommend that merchants check for proof of residency upon entry (The system can't detect the customer's place of residency).", "76510": "Platform types", "76511": "Site option", "76512": "The rate plan will only be available on klook.com (not klook.cn)", "76513": "The rate plan is available on klook.com and klook.cn. In the China market, price discrimination might occur.", "76514": "Rate plans are stackable with other discounts on the platform such as promo codes. Make sure to consult with the marketing team before creating rate plans.", "76515": "The pricing team can adjust the product price separately from your rate plan settings. Make sure to align with the pricing team when you set rate plans so that your take rate won't be affected.", "76516": "Usually, once you save the rate plan settings, you can't change them. Ensure all the settings are correct before saving.", "76517": "Unsupported products: Klook pass products and combo products. API & PIC products are not supported if the budget source type is Merchant fund - Settlement deduction", "76518": "Saved successfully", "76519": "The rate plan you edited will go live according to the scheduled date and time. If you want to stop the rate plan, please click \"Suspend\" on this rate plan list page.", "76520": "{num} results", "76521": "Use the checkboxes to bulk edit rate plans with the same status", "76522": "Edit", "76523": "Suspend", "76524": "Recover", "76525": "Delete", "76526": "Product ID-name", "76527": "Rate plan ID", "76528": "Status", "76529": "Draft", "76530": "Scheduled", "76531": "Live", "76532": "End", "76533": "Suspended", "76534": "Eligibility", "76535": "All customers", "76536": "Pricing by origin", "76537": "Pricing by platform", "76538": "Rate type", "76539": "Duration", "76540": "Creator", "76541": "Created", "76542": "Action", "76543": "Edit", "76544": "Duplicate", "76545": "Delete", "76546": "Suspend", "76547": "Recover", "76548": "View log", "76549": "Do you want to recover the rate plan?", "76550": "Do you want to suspend the rate plan?", "76551": "Modification type", "76552": "Status update", "76553": "Rule update", "76554": "About price fluctuation: This unit is under a rate plan rule funded by merchants (Rate plan ID: {ID}). As a result, the price set in this onboarding system might not match with the what the customer books on the frontend and what is agreed with the merchant.", "76555": "The items selected have been removed due to the changes in the rate plan level. Please select again.", "76556": "Search for AID-name or PID-name", "76557": "We've created this system to help you boost your conversion rates and get more attention on your activities. Now you can:<br/>- Set up deals like direct discounts, group rates and origin rates<br/>- Get your offers featured on the platform<br/>- Use eye-catching discount tags", "76558": "Required info not complete", "76559": "You can save as draft or go back to continue completing the required info", "76560": "Reset info?", "76561": "This action cannot be undone", "76562": "The end time cannot be earlier than the start time", "76563": "This rate plan has the following issue with rate plan {ID}: One unit can only start one rate plan at a time for the same rate plan type.", "76564": "There is a rule conflict with rate plan {ID}", "76570": "Edit required", "76683": "About price fluctuation: This unit is under a rate plan funded by merchants. As a result, the price set in this onboarding system might not match with the what the customer books on the frontend and what is agreed with the merchant.", "76684": "Available countries", "76685": "Available platforms", "76686": "Available sites", "76687": "Funded by merchant", "76994": "Product level", "76995": "More filters", "76996": "Please make sure that the discounts are compatible and set again", "77127": "Sales channel", "77128": "Available channels", "77129": "Express Go", "77130": "For setting up the front-end display. This can only be applied if the AID is available in the Express Go channel. Separate configuration of the Express Go is still needed.", "77131": "White Label", "77132": "For setting up the front-end display. This can only be applied if the AID is available in the White Label channel. Separate configuration of the White Label is still needed.", "77133": "B2C", "77416": "Save rate plan", "77529": "Save as draft", "77574": "Unavailable channels", "77684": "Search reservation units by activity ID", "77685": "Search standalone units by activity ID", "77688": "Are you sure to end it? By clicking it, you'll change the \"end time\" of this rate to now", "78220": "Copy existing \"Other info\"", "78221": "We recommend saving any changes made to \"Customized other info\" before copying existing \"Other info\"", "78223": "Go back & save", "78232": "Copy & don't save", "78243": "<span style=\"font-weight:bold;\">Heads up</span>: From end Sep 2022 (after Klook Rewards project launched), Gold members can get 3x credits (normal members get 1x credits). Besides, the credits that we give for bookings will be revised from 1% to 0.6%, so system will auto x 0.6 based on your original settings.", "78257": "ALMACT voucher + ALMACT code", "78258": "ALMACT voucher + merchant code", "78259": "This is the time at which Almosafer Activities will stop accepting bookings from users (set in local time). The earliest cut-off time you can set is \"Today 00:00\". <br/><br/>Tip: Having a shorter cut-off time means you can accept bookings until the last minute to maximize your sales.", "78260": "Experiment and group name", "78261": "Group name", "78264": "e.g. abname1:group1,abname1:group2", "78267": "use \",\" to separate multiple names", "78302": "Activity tiers", "78303": "1 default tier", "78304": "Multiple tiers", "78325": "Select at least 2 tiers", "78326": "Premium", "78327": "Standard", "78328": "Super-saver", "78329": "Extra", "78330": "Units for reservation", "78331": "{num} tiers", "78332": "Drag & drop into tiers", "78333": "Search unit ID (e.g. ************)", "78334": "Items", "78335": "How many units can be reserved in each tier?", "78342": "Only supports activities within these subcategories: 1,2,15,22,171,413,415,523,551,552", "78344": "This unit can't be unpublished because it's mapped with the following published pass units: {AID}-{PID}-{SKUid}<br/><br/><br/>You can set it as temporarily unavailable if necessary.", "78345": "Participation validity", "78346": "Valid for {{num}} day(s) from the first participation date", "78347": "{num} selected", "78437": "How to use your pass", "78463": "Klook vouchers can't be redeemed if expired", "78464": "The travel date of the Klook voucher doesn't match the actual participation date and can't be redeemed", "78465": "The voucher participation date {date} doesn't meet the conditions for redemption", "78466": "Close", "78467": "The voucher has expired", "78468": "The voucher doesn't meet the conditions for redemption", "78469": "The voucher has expired. Do you want to continue?", "78470": "The voucher participation date doesn't meet the redemption conditions. Do you want to continue?", "78477": "Select all", "78482": "Remaining: {num}", "78492": "Can redeem the expired voucher", "78493": "Can redeem the voucher during a non-participation date", "78494": "Confirmation needed after redemption", "78606": "Please add all units to the tiers", "78607": "Make sure all selected tiers have at least 1 unit added", "78608": "Please select at least 2 tiers if \"Multiple tiers\" is selected", "78659": "Unit info", "78835": "Click to review ticket", "78836": "Duration", "78837": "{num} day(s)", "78838": "Departure time", "78839": "hours", "78840": "mins", "78841": "Recommended arrival time", "78842": "Departure type", "78843": "Meet-up point", "78844": "Upload photo", "78845": "Upload new photo", "78846": "Select from activity gallery", "78847": "Link to Google Maps", "78848": "Edit Google Maps link", "78849": "Enter details (Optional)", "78850": "Time/duration", "78851": "Time", "78852": "Duration", "78853": "Tour type", "78854": "Attraction/experience name", "78855": "See POI details", "78856": "No results. You can {add_new_one}. (Only freetext without map link is allowed)", "78857": "add a new one", "78858": "Admission included?", "78859": "Add another attraction/experience", "78860": "Restaurant name", "78861": "<PERSON><PERSON>", "78862": "Expenses included?", "78863": "Transport type", "78864": "Accommodation name", "78865": "Property type", "78866": "Property rating", "78867": "Bed type", "78868": "Bathroom facilities", "78869": "No. of people per room", "78870": "Show detailed rules", "78871": "Show less", "78872": "Add an alternative", "78873": "Return time", "78874": "Return type", "78875": "Return point", "78876": "Copy existing itinerary", "78877": "Select a package to copy from", "78878": "Delete this setting?", "78879": "If you want to change the duration of this itinerary, you'll need to add details for any extra days. Continue?", "78880": "If you want to change the duration of this itinerary, any details of extra days will be lost. Continue?", "78881": "Please enter at least 1 group (e.g. Attraction/Experience, Accommodation, etc.) for day #{num}", "78882": "The time must be later than the previous itinerary", "78883": "Start", "78884": "{num} hour(s)", "78885": "{num} min(s)", "78886": "{num1} hour(s) {num2} min(s)", "78887": "Please arrive at the location {num} mins before the departure time", "78888": "Pick up", "78889": "Meet up", "78890": "Attractions / Experiences", "78891": "Guided tour", "78892": "Self-guided", "78893": "Admission not needed", "78894": "Admission included", "78895": "Admission not included", "78896": "<PERSON><PERSON>", "78897": "Breakfast", "78898": "Lunch", "78899": "Afternoon tea", "78900": "Dinner", "78901": "Snack", "78902": "Expenses included", "78903": "Expenses partially included", "78904": "Expenses not included", "78905": "Transport", "78906": "By bus", "78907": "By car", "78908": "By boat", "78909": "By ferry", "78910": "By subway", "78911": "By bike", "78912": "By walking", "78913": "Accommodation", "78914": "Hotel", "78915": "Guesthouse", "78916": "Apartment", "78917": "Hostel", "78918": "Resort", "78919": "Capsule", "78920": "2 stars or below (Economy)", "78921": "3 stars (Standard)", "78922": "4 stars (Premium)", "78923": "5 stars (<PERSON><PERSON><PERSON>)", "78924": "Queen-size bed (for 2 people)", "78925": "2 single beds", "78926": "1 single bed", "78927": "3 single beds", "78928": "Shared bathroom", "78929": "Private bathroom", "78930": "person(s) per room", "78931": "You may need to share rooms with other customers if your booking quantity doesn't meet the number of guests needed for the room", "78932": "End", "78933": "To hotel/personal address", "78934": "To drop-off point", "78935": "The menu might change depending on the season and availability of ingredients", "78936": "Day {num}", "78937": "Itinerary", "78938": "Day {num}", "78939": "Map", "78940": "Your accommodation will be arranged by your guide based on the hotel's capacity", "78941": "We hope you had a blast! See you for the next one.", "78942": "[Departure] {time} meet up at [{address}]({url}). {early_arrival_time} {details}", "78943": "[Departure] {time} pick up. {Early_arrival_time} {Details}", "78944": "[Attraction/experience] {time} {duration} {visiting_type} {name} {expense_included} {Details}", "78945": "[Dining] {time} {duration} {dining_type} {restaurant_name} {expense_included} {Details}", "78946": "[Dining] {time} {duration} {dining_type} {expense_included} {Details}", "78947": "[Accommodation] {time} {hotel_name} {property_type} {property_rating} {bed_type} {bathroom_facility} {people_num} {Details}", "78948": "[Accommodation] {time} {property_type} {property_rating} {bed_type} {bathroom_facility} {people_num} {Details}", "78949": "[Transportation] {time} {transport_type}", "78950": "[Return] {time} at [{address}]({url}) {Details}", "78951": "[Return] {time} Shuttle included", "78952": "Old itinerary", "78953": "Please migrate the details to the new itinerary module", "78957": "Headinfo", "78958": "Footinfo", "78959": "Itinerary reminder", "79049": "Please DO NOT upload images with text as this will cause language inconsistencies. If necessary, you'll need to follow the \"image-with-text\" translation process.", "79326": "Create New Redirection", "79327": "Bulk Upload", "79328": "AID to AID", "79329": "AID to URL", "79330": "From AID", "79331": "To AID", "79332": "Reviews/Participants migrated?", "79333": "Applicable Language", "79338": "Applicable Version", "79345": "Redirection Type", "79346": "Advance Setting", "79394": "Advanced settings", "79395": "Enter name with city (e.g. Sentosa Singapore)", "79396": "Enter name with city (e.g. Sentosa Singapore)", "79397": "Enter name with city (e.g. Royal Garden Hong Kong)", "79416": "URL rules as follows：<br/>1.URL pathname should begin with https://<br/>2.If multi language mapping is not required for this redirection, just copy&paste the url in the input field<br/>3. If multi language mapping is required for this redirection, follow the format below when input URL<br/>{host}:https://klook.com<br/>{language}:klook language", "79417": "Ex: {host}{language}catering/product/77754-seamart", "79419": "Version format as follows:<br/>1. Version is only required for APP. EX:6.27.0<br/>2. The change will apply to all versions filled in or above", "79420": "Invalid URL format", "79422": "Invalid Version format", "79423": "Invalid AID{AID}", "79429": "\"From AID{AID1}\" and \"To AID{AID2}\" conflicted", "79430": "Duplicated redirection for AID{from_AID}", "79433": "Migrate Reviews", "79434": "Download Template", "79445": "Reviews and participants migration failed because AID{to_AID} has migrated reviews before", "79446": "No Access for reviews/participants migration. Please contact tech.", "79448": "Reviews migrated exceed total participants", "79449": "Reviews/participants migration timeout. Please try again later.", "79450": "System error. Please try again later.", "79452": "Redirection Tool", "79453": "To URL", "79606": "person(s) per room", "79625": "{num} max.", "79627": "Choose one of the meet-up points and arrive on time", "79628": "Choose one of the return points", "79629": "Sales suspended", "79630": "Suspend sales", "79631": "Suspend activity sales", "79632": "Suspend package sales", "79633": "Suspend an activity's sales to make it un-bookable but still searchable for customers (The booking button will be grayed out with \"Unavailable\", for example)", "79634": "Suspend a package's sales to make it un-bookable but still visible for customers (The booking button will be grayed out with \"Unavailable\", for example)", "79635": "Suspended packages are still visible to customers but can't be booked (The booking button will be grayed out with \"Unavailable\", for example)", "79636": "Suspended activities are still searchable to customers but can't be booked (The booking button will be grayed out with \"Unavailable\", for example)", "79637": "After submitting, the activity/package will become visible but not bookable to customers", "79638": "Suspend sales end date (Once the suspension ends, it'll revert to unpublished)", "79639": "Select reason (Determines text customers will see on grayed-out button)", "79640": "Activity closed", "79641": "Sold out", "79642": "Other", "79643": "Enter your reason for sales suspension here (The activity/package will say \"Unavailable\")", "79644": "Set warm up time", "79645": "Make the package visible to customers but not yet bookable with a grayed-out \"On sale soon\" button and a countdown", "79646": "The warm-up period must be 7 days or less", "79647": "The package is set to warm up and will auto-publish - you'll need to remove this setting to publish/unpublish it now", "79648": "In warm up", "79649": "The package is set to warm up and will auto-publish. It is not bookable now but will become bookable once auto-published.", "79650": "Activity closed", "79651": "Sold out", "79652": "Unavailable", "79653": "On sale soon", "79656": "Add on settings", "79657": "Other tools", "79658": "Search by main product or add on name/ID", "79659": "Main product", "79660": "Show published items only", "79661": "Package", "79662": "Unit", "79663": "Add ons", "79664": "Add on's package & unit", "79667": "Price strategy", "79668": "Actions", "79669": "Order", "79670": "Show add ons only", "79671": "Attach add on", "79672": "{num} unit(s) selected", "79673": "You'll need to first select a unit to attach add ons to", "79674": "No results found - try updating your filters", "79675": "Save", "79676": "Are you sure you want to remove all {num} add ons under this main product?", "79677": "Are you sure you want to remove filtered {num} add ons under this main product?", "79678": "Remove", "79679": "Go back", "79680": "Clear filters", "79681": "Selected main products", "79682": "Select add ons", "79683": "Please attach new add ons or select from the existing list", "79684": "E.g. 80", "79685": "For setting the percentage of the original selling price when the unit is set as an add on. For example, entering 80% here means a 20% discount off the original selling price.", "79686": "Add/edit", "79687": "Search packages & units", "79688": "Any price strategy changes will not affect other add ons", "79690": "Create at least 1 package and 1 unit first", "79692": "You can attach up to 8 add ons to each main product unit", "79699": "Units under this package are set as add ons - \"Other info\" can't be added", "79700": "Remove the \"Other info\" for any units set as add ons", "79793": "Your guide will choose one of the following restaurants based on capacity", "79794": "Pick up: The account manager/merchant provides details about the general pick-up area. The customer will provide a specific location. <br/><br/>Meet up: The merchant provides the specific location for the customers to go to", "79795": "The itinerary plan is for reference only - make sure to participate based on the time you selected when booking", "79928": "Please make sure the activity you want to do redirection had been unlisted.", "79938": "Applicable Conditions", "79939": "Success", "79940": "Failed", "79941": "In process", "79942": "Not executed", "79943": "Unknown", "79959": "Applicable Platform", "79960": "Please make sure the URL starts with https://, http:// or {host}{language}", "80073": "Cannot turn on the schedule-publish function when package is already published.", "80074": "Cannot turn on the schedule-unpublish function when package is already unpublished.", "80075": "Cannot use the schedule-publish & schedule-unpublish function when the activity/package is under \"Sales Suspended\" status", "80076": "Session Expiring Soon!", "80077": "{text} is editing this package, you can't make any change right now.", "80120": "You can select up to {num} photo(s) to bulk upload", "80126": "Do remember to set warm up time on \"Package info\" page - \"Auto publish/unpublish\" field to make this status consistent with package display on frontend", "80127": "The activity is set \"Sales suspended\" status - you'll need to change activity to \"Unpublished\" status first to enable package publish.", "80128": "Max. no. cannot set 0", "80201": "Unknown error, please check with the platform for error reason", "80270": "Warm up time cannot be changed 30mins before the set up time", "80277": "Enter a price strategy", "80278": "This add on is already attached to at least 1 of the main products selected", "80294": "App e-voucher + Merchant offline redemption", "80295": "The merchant/operator will manually redeem the voucher from the customer's phone", "80297": "The activity is set \"Sales suspended\" status - you'll need to change activity to \"Unpublished\" status and publish at least 1 package first before publishing the activity", "80333": "E-voucher + Merchant offline redemption", "80438": "Please complete the previous step first.", "80439": "Bahasa Melayu", "80442": "Free Grab voucher", "80446": "Limited offer for free Grab vouchers", "80496": "Merchant offline redemption needed？", "80673": "Add content field", "80674": "Suggested to add", "80675": "Enter details (Required)", "80676": "Enter details (Optional)", "80677": "In draft", "80678": "In draft: {num}", "80679": "Clear drafts", "80680": "Frontend preview", "80681": "See the current frontend", "80682": "The preview below is for reference only", "80683": "Turn on all editing functions", "80684": "Choose content to display for this package", "80685": "Content field list", "80686": "Content to display", "80687": "Click the pencil icon to start choosing content fields to display", "80688": "Choose content field to display to customers", "80689": "Enter any variables if needed", "80690": "Add more content fields", "80691": "Choose content fields to display to customers", "80692": "Next step", "80693": "Got it!", "80726": "The following languages will auto-sync with the selected source language ({source_lang}) using the AI translation tool: {target_language_list}", "80727": "The selected language {target_lang} is auto-synced with the the source language ({source_lang}) using the AI translation tool. Any free text can't be edited (except the Activity title).", "80728": "Source language: {language}", "80729": "The system will auto-publish en_US content for all en and European languages. APAC languages will be published with AI. Any free text will auto-sync with the en_US content.", "80730": "The system will auto-publish with AI for en_US and APAC languages. Any free text will auto-sync with this language's content.", "80731": "Even if unpublished, en_US is still the source language for languages published with AI and other en locales. Any free text will auto-sync from en_US.", "80732": "Even if unpublished, this language is still the source language for languages published with AI. Any free text will auto-sync from this language.", "80733": "\"Publish with AI translation\" means that any free text will auto-sync with the source language, and an AI translation button will be provided. Only the title and any template-generated content will be published using human translation.", "80734": "English characters, numbers and symbols only in the activity title", "80857": "Available only", "80858": "Unavailable", "80859": "For setting up add ons display. Once set up, this package will only be available under the add ons section. A separate configuration of the add on is still needed.", "80861": "Changes made are still in draft and aren't published on the frontend. Are you sure you want to leave now?", "80892": "For any information not covered in other categories. You can use free text here.", "80893": "Create a Package", "80894": "Add package & unit", "80895": "Package settings", "80896": "Age group", "80897": "Mandatory unit for booking", "80898": "Schedule type provided", "80899": "Sold by date", "80900": "Sold by time", "80901": "Package availability", "80902": "Repeat Datetime", "80903": "Accept bookings until", "80904": "Inventory set up", "80905": "Limit inventory by day", "80906": "Unlimited inventory", "80907": "Price set up", "80908": "Fixed price", "80909": "Different price by day", "80910": "Copy schedule & inventory", "80911": "If you change any rule settings, the changes will apply to all units under this package", "80912": "If you update the datetime setting, it will apply to all repeats days under this unit.", "80913": "The changes made will apply to all selected units", "80914": "The timeslot & inventory exist, while the customer can not view & purchase.", "80915": "The timeslot & inventory does not exist anymore. If you would like to republish this date, then you will need to fill in the inventory accordingly.", "80916": "Please kindly fill in the period when you would like to delete or deactive", "80917": "Enter the schedule you want to deactivate/delete, and then select the relevant units", "80918": "Deactivate/delete a schedule", "80919": "Bulk deactivate/delete schedules", "80920": "Suggest to use \"Fixed price\" if you are selling the experiences at the same price each date.", "80921": "Suggest to use \"unlimited inventory\" if you have no limitation on the inventory.", "80922": "If you change any rule settings, the changes will apply to all sub-units under this package", "80923": "The rule settings for this sub-unit can't be changed", "80924": "The change will only apply to the schedules selected", "80925": "Heads up: If you update any date/time settings, it will apply to all repeat days selected", "80926": "If you reset the timeslot, all relevant schedules will also be removed", "80927": "To copy from one unit to another, the two units need to have the same price setting", "80928": "To copy from one unit to another, the two units need to have the same inventory setting", "80929": "To copy from one unit to another, the two units need to have the same timeslot - it is missing from the unit you want to copy to", "80930": "Enter details for your package here", "80931": "You can also enter details at the unit level", "81796": "Packages under any of the following conditions can't be set up as add ons:<br/>1. With sub category ID: 360, 527, 535, 552, 558 <br/>2. With \"Participant info\", \"Delivery info\", \"Other info\" <br/>3. Combo packages", "81801": "Unpublished units: {num}", "81802": "Edit price and inventory rules", "81803": "Set unit rules", "81804": "Add/edit schedule", "81810": "The quantity setting is applied to packages. If the total quantity sold within this package meets the minimum group size requirement, the \"Departure confirmed\" tag will be shown on the live page.", "81811": "Minimum group size", "81812": "Maximum group size", "81813": "Departure confirmation setting", "81816": "The maximum group size must be greater than the minimum group size", "81818": "Tag Setting", "81819": "Time", "81820": "Date & time", "81821": "Sold / Remaining", "81822": "Schedule:", "81823": "Cost price", "81824": "Retail price", "81825": "Price & inventory", "81903": "Delete unit", "81930": "You'll need to complete the {text} section before submitting the package", "81936": "OK", "82050": "Cross-border transport<span style=\"font-weight:bold;\"> from Hong Kong</span> (e.g. ferry, train, bus, flight, private transfer, etc.)", "82051": "Accommodation <span style=\"font-weight:bold;\">outside Hongkong</span> (e.g. hotel, hostel, B&B, etc.)", "82053": "Activity info", "82054": "Booking policies & voucher settings", "82173": "Apply default selected package to activity page?", "82174": "When switched on, a package will be selected by default for customers on the activity page (Only applies to the attribute/package list layout). When switched off, no package will be selected by default.", "82181": "Add unit", "82183": "Create package", "82184": "Customized unit type", "82192": "Drag to sort", "82193": "Cutoff Time", "82222": "Cost & retail price", "82552": "Please save as draft first", "82553": "No content added. Please click \"{add}\".", "82667": "Add schedule", "82668": "Edit schedule", "82669": "Package & units", "82670": "Deactivate", "82671": "No info added yet. You can add new schedule/inventory info or copy from existing units.", "82672": "For the fixed price model, please go to edit rule setting to modify the price.", "82673": "Go to \"Edit price and inventory rules\" page to change the inventory", "82674": "Notes", "82675": "For the total inventory model, please go to the <span style=\"font-weight:bold;\">edit rule setting </span>page to modify the inventory.", "82676": "For the fixed price model, go to \"Edit price and inventory rules\" page to change the price", "82721": "Deactivating will close the schedule but save the data so you can activate it again. Deleting will permanently remove any data for this schedule.", "82776": "Enter verification code", "82777": "Get verification code", "82783": "Want to add more content? The more details, the better customers can understand.", "82784": "Add more content", "82910": "In the merchant's default currency", "82942": "Time/Duration", "82959": "Activity title can't include the following: (, ), [, ], % off", "82960": "Up to 65 characters", "83010": "Only lower-case English characters, numbers and hyphens allow (Can't start or end with a hyphen)", "83068": "The schedule/inventory of sub-units can't be changed", "83078": "The schedule of sub-units can't be deactivated/deleted", "83098": "Add package", "83099": "Select unit to copy from", "83100": "The package title should clearly tell customers what they are booking (e.g. duration, etc.). Make sure the package title doesn't overlap with the activity title. Up to 44 characters (including spaces).", "83108": "Schedule complete", "83140": "Add units", "83297": "Before you unpublish", "83300": "This activity/package/unit is included under a Klook Pass, and unpublishing it might hurt the customer experience for buying a pass. <a href=\"MULTILANG_URL_PLACEHOLDER\">Learn more about Klook Passes</a>.", "83301": "Reason for unpublishing", "83308": "Describe your reason in detail", "83309": "Unpublish", "83349": "No. of calendar months displayed", "83350": "Last available date: {DD}/{MM}/{YYYY}", "83610": "Before changing the min. group size from {num1} to {num2}, make sure to double-check any dates within the \"Departure confirmed\" tag. Continue?", "83634": "The last available date is set to CST (UTC +8)", "83790": "Which language(s) will you offer your activities in?", "83791": "Let's give your activity a name", "83792": "Provide a name for your package", "83793": "Which kind of unit do you provide?", "83794": "What is the min/max age limitation for this unit?", "83795": "Where does your experience take place?", "83796": "Which country does this activity depart from?", "83797": "Which city does this experience depart from?", "83798": "Which country do you want to host your activity in?", "83799": "Which city do you want to host your activity in?", "83800": "Provide a Cover Photo for your activity<br/>Add 3 more photos to describe your activity", "83801": "Provide a vivid video for your activity", "83802": "Any highlights of your activity?", "83803": "Let's describe your activity with more details", "83804": "Package Availability", "83805": "Schedule Type Provided", "83806": "DOs <br/>Keep your title simple and to be within 65 characters including spaces<br/><br/>Follow the title case capitalization rule (e.g. Swiss Open Air Museum Ballenberg Admission), except preposition (in, on, over, with), conjunction (and, or, for) and article (the, a, an).<br/><br/>DON'Ts <br/>Not to include \"exclusive\", \"promotion\", \"discount\" etc.<br/><br/>Do not use subjective term, such as \"Hot item\" or \"Best seller\"<br/><br/><br/>For more format example, please kindly check the guideline [link]https://docs.google.com/presentation/d/1GCfcqm6MSfqS7PvhT1puXvkjk9Sj-05FtpDMb1h1JZg/edit#slide=id.g11bff86b949_4_4197", "83807": "Section 5 : <br/>Please share with the travelers more about this activity. (Min 100 words, Max 150 wordsE.g. At Wuling Farm, with its 700 hectares of land, you can relax and enjoy a day surrounded by beautiful nature. Start off your tour by meeting up with your tour guide at Luodong Train Station in Yilan. You will board a shuttle bus that departs for Wuling Farm. On the way, the bus will stop at a quaint tea village before heading to the farm where you can find breathtaking views. If you bring mats and snacks, you can enjoy a picnic under the shadows of the trees. In the late afternoon, you will board your shuttle bus and be taken back to Luodong Train Station.", "83808": "Please refer to this link to more sample photos <br/>DOs<br/><br/>Showing energetic, fun and engaging moments<br/><br/>Expressing joy and enjoying the activity<br/><br/>People immersing in cultural experience<br/><br/>Always include joyful faces in theme parks.<br/><br/>Don'ts<br/><br/>Participants not showing engagement in an activity/ experience<br/><br/>Avoid using still objects for any activity/ experiences<br/><br/>Avoid moody shots or heavy filters<br/><br/>No signs of participation<br/><br/><br/><br/>Photo Captions<br/><br/>Offers more insight into the service<br/><br/>Gives customer a look inside the actual ferry<br/><br/>Simple and straightforward; within the photo caption limits<br/><br/>Sample Captions<br/>You can get from Hong Kong to Macau on this TurboJet in just 50 minutes<br/><br/>Comfortable seating and spacious legroom await you aboard the TurboJet Ferry", "83809": "Photo Instruction<br/><br/>Image Resolution: image should be above 4.0 megapixels or 72 pixels per inch (e.g. 2500×1600 is 4.0 megapixels)<br/><br/>Image Size: Up to 4MB & Banner: 3:2 / 1612 x 1074<br/><br/>File Format: JPG or PNG<br/><br/>Photo Ratio/Orientation: width:height = 3:2<br/><br/>Image Quantity: At least 4 image with different scenes of each activity to provide a better illustration<br/>Note<br/>Please upload the photo to which you own the rights only. In the case of third-party infringement claims, please note that Klook will collect the damage from the operator.", "83810": "Video Instruction<br/><br/>Video Size: Less than 45 mb<br/><br/>File Format: MOV, MP4, AVI, or WMV<br/><br/>Photo Ratio/Orientation: width:height = 16:9 / 1080P (1920x1080) or 720P (1280x720)", "83811": "Section 4<br/>Please share with the travelers what the unique selling points of this activity.<br/>Reminder<br/><br/>Use bullet points! 4-6 bullet points only<br/><br/>20 words max per highlight<br/><br/>Keep your highlights to the length of one line of text on the page<br/><br/>Give a brief summary of the reasons why the customer should book this product<br/><br/>Write as if you are talking to the customer and telling them what they will be experiencing<br/><br/><br/><br/>Emples for Activities & Experiences product<br/><br/>Record at a studio where stars such as VIXX, <PERSON>, and more create their hit songs<br/><br/>Take a look into the offices of Ceci, one of South Korea's most prominent magazines<br/><br/>Use the most advanced audio equipment in a comfortable and elegantly designed studio<br/><br/>Personally choose and record your preferred songs for your own K-pop album!<br/><br/><br/>Examples for Tours product<br/><br/>Malaysia’s Cameron Highlands is a getaway filled with sprawling plantations for those who long to escape city life<br/><br/>This day tour allows you to trek through mountains and mossy forests with an expert guide<br/><br/>Other places such as the Time Tunnel Museum, Sam Poh Buddhist Temple, and a butterfly farm are part of the tour<br/><br/>At the end of the day, relax by picking plump fruits within the area’s popular strawberry farm<br/><br/><br/><br/><br/><br/><br/><br/>[Body text]It helps to answer the following questions when writing highlights（這項可以直接放在頁面中間嗎？或是當成free text欄位中的default文字）<br/>[Bulletpoint]<br/>What is this product about? Why is it special? What will they experience?<br/><br/>Who would you recommend it for? Families, couples, solo travelers, groups? <br/><br/>Are there any special features or inclusions along with the ticket? (e.g., transfers, meals, coupons, skip the line, fast track, etc)", "83812": "How long does it take to process an order once it is received?<br/>Instant confirmation<br/><br/>24-hours confirmation", "83813": "How many days before the event can the travelers cancel the order?<br/>Free cancellation before activity time<br/><br/>Free cancellation before redemption<br/><br/>Free cancellation 24-hrs before activity time<br/><br/>Free cancellation 48-hrs before activity time<br/><br/>Free cancellation 72-hrs before activity time", "83814": "Per booking : The customer will receive one QR code per booking<br/><br/>Per unit type : The customer will receive a QR code per each unit type. For example, 2 adults will share one QR code and 2 children will share another QR code<br/><br/>Per unit : The customer will receive one QR code per unit. For example, 2 adults and 2 children will get 4 unique QR codes", "83815": "Please make sure you fill in the \"Destination City& Detination Country\"", "83816": "{num}section(s) to submit", "83817": "{num}section(s) done", "83818": "{num}package(s) to submit", "83819": "{num}package(s) done", "83820": "Quit from New Process", "83821": "Which language do you want to onboard the activity in?", "83822": "Let's give your activity a name", "83823": "What's this package's name?", "83824": "What unit type? (E.g. Adult, Child, etc.)", "83825": "Any min./max. age limitations for the unit?", "83826": "Where will your activity happen?", "83827": "Which country/region does your activity depart from?", "83828": "Which city does your activity depart from?", "83829": "Which country/region is your activity based in?", "83830": "Which city is your activity based in?", "83831": "Add an eye-catching cover photo", "83832": "Upload an engaging video", "83833": "What are the highlights (key selling points)?", "83834": "Describe your activity in more details", "83835": "Package availability", "83836": "Schedule type provided", "83837": "### DOs <br/>* Keep your title simple and within 65 characters (including spaces)<br/>* Follow the title case capitalization rule (e.g. Swiss Open Air Museum Ballenberg Admission), except prepositions (in, on, over, with), conjunctions (and, or, for) and articles (the, a, an).<br/>### DON'Ts<br/>* Don't include \"exclusive\", \"promotion\", \"discount\" etc.<br/>* Don't use subjective terms, such as \"Hot item\" or \"Best seller\"<br/>* Avoid decorative characters, such as \"~ ! * $ ? _ ~ { } # < > * ; ^ ¬ ¦ \"<br/>### For more format examples, check the [guidelines](https://docs.google.com/presentation/d/1GCfcqm6MSfqS7PvhT1puXvkjk9Sj-05FtpDMb1h1JZg/edit#slide=id.g11bff86b949_4_4197)", "83838": "* Keep the description within 100-150 words<br/>* Give the customer more insight on the activity<br/>* Provide customers with a detailed description of the product/activity they are about to book<br/>* Be concise, enriching and engaging with details that can sell the product <br/>* Write in a straightforward manner and add interesting facts that will help contextualize the activity", "83839": "![9fdd4692-7c25-4a96-8ef3-3c786d39051b](https://res.klook.com/image/upload/v1678177091/admin-markdown/eo7v1pl8gw1ejqsvx9bq.jpg)<br/>![output](https://res.klook.com/image/upload/v1678177182/admin-markdown/jxr0siwapovob8i1kink.jpg)<br/><br/>[See more sample photos](https://docs.google.com/presentation/d/1GCfcqm6MSfqS7PvhT1puXvkjk9Sj-05FtpDMb1h1JZg/edit#slide=id.gfe76aa36e4_0_3203)<br/><br/>### DOs<br/>* Show energetic, fun, and joyful moments<br/>* Show people engaging in the activity<br/>* Show people immersed in cultural experiences<br/>* Always include joyful faces in theme parks<br/>* Avoid using still objects or props<br/>* Avoid leveraging moody shots or heavy filters", "83840": "Photo upload tips", "83841": "### Video instructions<br/>* Video size: Max. 45MB<br/>* File format: MOV, MP4, AVI or WMV<br/>* Photo ratio/orientation: 16:9 (width:height) / 1080 P (1920x1080) or 720 P (1280x720)", "83842": "Share the key selling points of this activity with customers.<br/><br/>* Use bullet points! 4-6 bullet points only<br/>* 20 words max per highlight<br/>* Keep your highlights to the length of one line of text on the page<br/>* Give a brief summary of the reasons why the customer should book this product<br/>* Write as if you are talking to the customer and you are telling them what they will experience", "83843": "**The confirmation time is how long will it take to process an order after its received** <br/> e.g. Instant confirmation, 24-hour confirmation, etc.", "83844": "**The cancellation policy determines how many days before the activity takes place when a customer can cancel for free.**", "83845": "**Per booking**: The customer will receive 1 QR code per booking<br/>**Per unit type**: The customer will receive 1 QR code by unit type. For example, 2 adults will share 1 QR code and 2 children will share another QR code<br/>**Per unit**: The customer will receive 1 QR code per unit. For example, 2 adults and 2 children will get 4 unique QR codes", "83846": "You'll need to enter the country and city to continue", "83847": "{num} section(s) to submit", "83848": "{num} section(s) done", "83849": "{num} package(s) to submit", "83850": "{num} package(s) done", "83851": "Exit creation process", "83853": "Up to {num} characters only", "83860": "Add 3 more photos that will help people visualize your activity", "83865": "Continue to packages", "83866": "{num} completed", "83867": "{num1}/{num2} packages complete", "83890": "You'll need to enter the country and city in the Package's main info section to continue", "83891": "Upload photo", "83892": "Upload video", "83893": "Re-upload", "83894": "You can edit the info after completing all the steps", "83895": "Submit all packages", "83896": "Next package", "83897": "Heads up before you exit", "83898": "If you exit, you can't come back to this new activity creation process. The information you've entered so far will be saved but you can only continue from the old activity creation pages.", "83899": "Exit", "83900": "Photos & videos", "83901": "Packages & units to sell", "83902": "What do you need from the customer?", "83903": "Search or select", "83939": "Highlights are key selling points. It helps to answer the following questions when writing highlights:<br/>- What is this activity about?<br/>- Why is it special?<br/>- What will customers experience?<br/>- Who do you recommend it for? (e.g. Families, solo travelers, couples)<br/>- Are there any special experiences or inclusions that come along with the activity?", "83949": "Resolution: Images should be above 4.0 megapixels or 72 pixels per inch (e.g. 2500×1600 is equal to 4.0 megapixels)", "83950": "Banner size: Up to 4 MB, 3:2 (width:height) / 1612 x 1074", "83951": "File format: JPG or PNG", "83952": "Photo ratio/orientation: 3:2 (width:height)", "83953": "Quantity: At least 4 images that will represent different characteristics and highlights of the activity each", "83954": "Heads up: Please ensure you have full rights to any images you upload. In the event of third-party infringement disputes, Klook will hold the merchant/supplier responsible for any associated losses.", "83955": "Photo captions", "83959": "Photo #{num}", "83964": "Upload photos & video", "83965": "Activity details", "84084": "Package's main info", "84085": "Package details", "84086": "Set unit schedule / price / inventory", "84088": "Customer info needed", "84089": "Provide itinerary", "84090": "Required", "84091": "To do", "84092": "To do list", "84093": "Task completion rate:", "84250": "Once reviews are migrated, this action can't be undone. Continue?", "84276": "Sorry, you don't have access to edit the free text field. Reach out to your account manager if you need access.", "84277": "The free text of the activity has been translated. To make changes, you'll need to reach out to your account manager so that translations can be adjusted and synced.", "84278": "This activity title has been translated. To make changes, you'll need to contact your account manager.", "84279": "This content needs to be reviewed by a Klook employee before saving. Please reach out to your account manager.", "84347": "Customized unit type", "84418": "Copied the settings from the previous package for this one", "85274": "Rejection reason (Main category):", "85275": "Rejection reason (Sub category):", "85276": "Rejection reason in detail:", "85321": "Rejection reason", "85325": "E.g. \"Book 2 adult tickets & get 10% off\" or \"Book 2 adult tickets & get 1 free\"", "85326": "E.g. \"Book 2 adult tickets & get 10% off\" or \"Book 2 adult tickets & get 1 free\"", "85327": "Here you can set the units and discounts. For example, if using \"Book 2 & get 10% off\" and \"Book 5 & get 20% off\", the customer can get 10% off for booking 2-4 units or 20% off for booking 5 or more units. Alternatively, if you use \"Book 2 & get 1 free\" and \"Book 4 & get 2 free\", the customer will get 1 free unit for booking 2-3 units or get 2 units free for booking 4 or more units.", "85441": "The minimum group size is required", "85535": "free", "85884": "E.g. \"Start a day in Hawaii with an exhilarating hike up Oahu Island’s famous Diamond Head to watch the sunrise. See the city of Honolulu in all its morning glory at the summit and enjoy spectacular 360-degree views of the metropolis, surrounding landscapes, and the sea as they are slowly illuminated by sunlight. Then cap off this short adventure and commemorate it by snapping panoramic photos and get yourself pumped for your sightseeing adventure for the day with a delicious Marasada from Leonard’s Bakery.\"", "85994": "### Photo captions <br/>* Offer more insight into the service <br/>* Be concise and straightforward <br/><br/>### Caption examples <br/>* \"You can get from Hong Kong to Macau on this TurboJet in just 50 minutes\"<br/>* \"Comfortable seating and spacious legroom await you aboard the TurboJet Ferry\"", "87282": "Complete activity info", "87522": "![9fdd4692-7c25-4a96-8ef3-3c786d39051b](https://res.klook.com/image/upload/v1678177091/admin-markdown/eo7v1pl8gw1ejqsvx9bq.jpg)<br/><br/>![output](https://res.klook.com/image/upload/v1678177182/admin-markdown/jxr0siwapovob8i1kink.jpg)<br/><br/><br/>[See more sample photos](https://docs.google.com/presentation/d/1GCfcqm6MSfqS7PvhT1puXvkjk9Sj-05FtpDMb1h1JZg/edit#slide=id.gfe76aa36e4_0_3203)<br/><br/><br/>### DOs<br/><br/>* Show energetic, fun and engaging moments<br/>* Show people expressing joy and enjoying the activity<br/>* Show people immersing in cultural experience<br/>* Always include joyful faces in theme parks<br/><br/>### DON'Ts<br/><br/>* Don't show participants not engaging in an activity<br/>* Don't use still objects for any activity<br/>* Avoid moody shots or heavy filters", "88450": "Instructions for GPS coordinate search", "88478": "Tasks to do", "88481": "Customers cannot get a rate plan discount if their participation date is on the block-out date", "88482": "Block-out date", "88555": "Date", "88556": "This package or SKU is linked to a rate plan. If the ticket type is changed, the rate plan connected to the package or SKU will be unlinked.<br/><br/>Do you want to continue?", "88636": "Guidelines", "88678": "Product details", "88688": "Includes walking, biking, bus, car & cruise tours", "88691": "The suggested commission rate is based on your selected destination or activities you offer", "88694": "Product/service description", "88695": "Booking confirmation policy", "88696": "Allow customers to cancel their booking before the participation time without any charges", "88697": "Allow customers to cancel their booking without any charges as long as the voucher hasn't been redeemed", "88698": "Free cancellation 24 hr before activity starts", "88699": "Allow customers to cancel their booking 24 hours before the participation time without any charges", "88700": "Free cancellation 48 hr before activity starts", "88701": "Allow customers to cancel their booking 48 hours before the participation time without any charges", "88702": "The customer's booking will be confirmed in minutes after they've paid (The merchant doesn't need to manually confirm the booking)", "88703": "The merchant will need to confirm the booking within 24 hours on the Merchant Portal after the customer has paid", "88705": "We'll collect the customer's contact info. You'll be able to see this info after confirming the customer's booking.", "88706": "Choose what required info (if any) you need from the customer for them to take part in your activity. You can require info from just one participant if you don't need info from all participants.", "88709": "Congrats, you've completed the main details! There's just a few final touches after this step and you're ready to sell.", "88823": "You can enter the price for each date later when editing", "88912": "Event name", "88913": "Ticket selection", "89084": "Are you sure you want to delete the following items?", "89085": "Manage schedule", "89086": "Deactivating will close the schedule but save the data so you can activate it again", "89087": "Deleting will permanently remove all data under the schedule", "89088": "Activating will open the schedule", "89110": "Product name:", "89111": "This will remove the schedule, price and inventory data, and cannot be undone", "89142": "Delete", "89143": "Delete", "89144": "Deactivate", "89145": "Activate", "89149": "Sub-SKU", "89270": "Save & exit", "89271": "Exit without saving", "89272": "You have unsaved changes", "91921": "Initiative", "91922": "You can choose from the options to filter campaign deals and types of rate plans.<br/>(1) If your rate plan has campaign deals, choose \"mega campaign\" or \"local campaign\" based on the campaign you are signed up for. <br/>(2) \"Origin rate\" is for rates that only apply to specific origins. This is automatically chosen by the system based on your setting.<br/>(3) \"Evergreen\" applies Always-on rate plans. This is automatically chosen by the system based on your setting.", "92240": "Driver's contact information", "92241": "Vehicle license number", "92242": "Note", "92243": "Pending pick-up info", "92244": "You need to update the pick-up info", "92245": "Please enter the pick-up info ASAP", "92294": "Region", "92426": "No history", "93284": "Are you sure?", "93285": "If you toggle this off, customers will not be able to search this activity.", "93289": "Customers can not search or see this activity now. Toggle this on to make the activity available.", "95932": "Excluded dates", "95933": "Select days to include", "96206": "Available for all countries", "96207": "Available for residents of selected countries", "96208": "Unavailable for residents of selected countries", "96209": "Unavailable for any countries", "98280": "Residency", "98284": "Available on activity page and add-ons", "98413": "Sort", "98415": "Activity ranking", "98416": "<PERSON><PERSON><PERSON>", "98417": "Manually sorted", "98418": "Activity ranking status: {status}", "98419": "Event log", "98420": "Activity ID", "98421": "Activity name", "98422": "Sales volume ranking", "98423": "The default logic of activity ranking is based on the sales volume of each activity that can be reserved with the attraction pass. The ranking is for your reference if you have manually adjusted it.", "98424": "Timestamp (UTC+8)", "98425": "Performed by", "98426": "Ranking before sorting", "98427": "Ranking after sorting", "98428": "You added a new activity to the pass. Remember to sort the activity ranking again after saving.", "98431": "When adjusting the ranking here, the following sections will be affected. If you are not sure, please consult Attraction planning team for more info.", "98475": "\"Attraction included\" entrance section of the pass activtiy page", "98476": "\"Attraction included\" intro section of the pass activity page", "98478": "Attraction reservation page", "100809": "Click here to create your first activity", "100810": "Click here to submit your first activity", "101026": "Important announcement", "101027": "Preview link is for internal purposes only. Please refrain from sharing the link via public channels (e.g. blog, social media). <br/><br/>From {yyyy_mm_dd}, preview links will be automatically expired (in 7 days by default and up to 30 days if extended). Once the link expires, you can't open the page anymore and will have to generate a new preview link.", "101028": "Generate preview link", "101029": "Valid for:", "101030": "{num} day(s)", "101042": "You've selected too many days or time slots", "101173": "48 hours before departure", "101174": "72 hours before departure", "101175": "Departure confirmed", "101547": "More", "101664": "Due to technical reason, you cannot modify the data below. If revision is needed, create new data to replace the current one.", "102199": "Package restriction", "102202": "For all products", "102256": "T&A POI data management", "102257": "Search with POI ID or name", "102258": "Filter", "102259": "Main POI", "102260": "Sub POI", "102261": "Independent POI", "102262": "Verified POI", "102263": "Unverified POI", "102264": "Filter by POI name", "102265": "{num} results matched", "102266": "No result", "102267": "{num} similar POIs", "102268": "{num} POIs", "102269": "Linked to {num} published activities", "102270": "<PERSON>rged", "102271": "<PERSON> as \"Verified\"", "102272": "Merging...", "102273": "Merge into \"{to_id}-{to_poi}\"", "102274": "Are you sure you want to merge \"{from_id_name1}\" into \"{to_id_name2}\"?", "102275": "Once merged, \"{from_id_name1}\" will disappear from this tool and all activities linked to it will be linked to \"{to_id_name2}\"", "102276": "Merge failed. You need to remove the sub POIs from the main POI first.", "102277": "You can't merge POIs into POIs unrecognized by Klook", "102278": "Add as a sub POI under a main POI", "102279": "Add as a sub POI under \"{id}-{main_poi}\"", "102280": "Manage main & sub POIs", "102281": "Remove all", "102282": "Remove", "102283": "Are you sure?", "102284": "Once removed, you can't restore the previous setting", "102285": "You can't add a POI under POIs unrecognized by Klook", "102286": "You can't add a POI under sub POIs", "102287": "Export data", "102288": "Main & sub POIs", "102289": "Merged POIs", "102290": "Verified POIs", "102291": "Most recommended", "102292": "POI ID: {ID}", "102293": "Others", "102294": "Merged!", "102295": "Added as a sub POI!", "102416": "You can't choose price calendar for Klook Pass packages", "102471": "Merge into another POI", "102518": "There are {num} similar POIs:", "103570": "This POI is not within the visible range of the map", "103751": "Pick-up location & map", "103752": "You can specify pick-up locations or indicate a region where pick up is available. If you don't select either of the options, customers will be able to choose anywhere on the map as their pick-up location.", "103753": "Pick-up type", "103754": "At specified regions", "103755": "At specified locations", "103756": "Restrictions", "103757": "Customers cannot book if pick up is not available at the location", "103758": "Customers can book even if pick up is not available at the location, but there may be an additional fee", "103759": "Anywhere", "103760": "Pick-up locations", "103761": "Areas", "103901": "Sold by date: Departure is made only once a day. You need to provide the exact time of departure and return for the customer's better understanding.", "103902": "Sold by time: Customers can take part at different times (e.g. 10:00, 11:00, 12:00). In this case, we suggest that you should provide relative timings (e.g. duration) rather than providing the exact time of arrival for each stop.", "103903": "You can choose to depart at the following times: {time_select_compoment}. Make sure you take part at the time you selected at booking", "104017": "See pick-up location", "104018": "Set pick-up location", "104019": "You have view-only access from here. Go to \"Other info\" page to edit pick-up locations.", "104020": "Location", "104021": "Address", "104022": "City", "104023": "District", "104033": "Copy from/to other packages", "104034": "Copied to {number} packages", "104035": "Copied only to some of the packages. You need to manually update packages with the following PIDs: {package_id}.", "104036": "Failed to copy. Try again later.", "104037": "Content copied from package with the following PID: {package_id}.", "104039": "Standardized other info", "104212": "<PERSON><PERSON>", "104213": "Add as a Sub POI", "104214": "Bulk edit", "104215": "Bulk set as sub POI", "104216": "Merge in bulk", "104217": "1. Select one main POI for sub POIs to be added to", "104218": "2. Select POIs to add as sub POI", "104219": "{num} selected to be added", "104220": "1. Select which POI to merge to", "104221": "2. Select POIs that will be merged", "104222": "{num} selected to be merged", "104223": "Non-attraction POI", "104224": "Set as non-attraction POI", "104225": "Sorry, this can't be done for non-attraction POIs", "104293": "Districts selected", "104294": "Manage", "104295": "Can't find anything.", "104296": "{Add} a pick-up location?", "104297": "Please select on the map", "104300": "Set as main POI", "104301": "Merge to", "105993": "You've zoomed out to too much of a large scope. POI data can't be shown in this view.", "106049": "Place ID: {id}", "106050": "Publish activity", "106051": "Publish package", "106266": "No matching result in Google Map, you may use this address and confirm with local operator after the payment", "106267": "Use what I inputted as pick up location", "106536": "Return time options", "106537": "You can choose to return at the following times: {time_select_component}. Options available can vary based on the departure time you selected at booking and the duration of the itinerary.", "106653": "I don't have insurance policy", "106655": "You can upload a valid insurance policy between you and the customer. If you don't have one, you can tick the checkbox.", "107096": "Overview", "107100": "Restricted content", "107873": "T&A POI page management", "107874": "Filters", "107875": "POI ID/name", "107876": "Select or enter", "107877": "Country/city", "107878": "Page status", "107879": "No. of linked activities", "107880": "Condition", "107881": "More than", "107882": "Less than", "107883": "Equal to", "107884": "{num} results", "107885": "Page ID", "107886": "POI ID", "107887": "POI name", "107888": "Country", "107889": "City", "107890": "No. of published activities", "107891": "L30D bookings", "107892": "Created date", "107893": "Publish date", "107894": "Last modified by", "107895": "Page status", "107896": "Operation", "107897": "Edit", "107898": "Start now", "107899": "Publish/unpublish", "107900": "Unpublish", "107901": "Published", "107902": "Unpublished", "107903": "Draft", "107904": "Language publish status", "107905": "Language", "107906": "Status", "107907": "No. of published activities", "107908": "Edit POI", "107909": "Basic info", "107910": "Linked activities ({num})", "107911": "SEO setting", "107912": "Linked sub POIs", "107913": "Page title", "107914": "POI description", "107915": "Content has been generated with ChatGPT and you can modify it directly", "107916": "Generate again", "107917": "ChatGPT helps you to generate contents", "107918": "Select 1 option you prefer", "107919": "Option {num}", "107920": "Use the content", "107921": "Photos", "107922": "Upload at least {num1} and up to {num2} photos", "107923": "Existing photos", "107924": "Upload", "107925": "Editing language", "107926": "Reference language", "107927": "Order", "107928": "Activity", "107929": "Included POI(s)", "107930": "Remove", "107931": "Rest<PERSON>", "107932": "Reviewed", "107933": "English content of {field_name} was modified. Which language's translation do you want to update accordingly with AI?", "107934": "Meta title", "107935": "Meta description", "107936": "Customize", "107937": "On", "107938": "Off", "107939": "POI description for SEO", "107940": "FAQ", "107941": "Prompt", "107942": "Question", "107943": "Answer", "107944": "Add new FAQ", "107945": "Don't use ChatGPT", "107946": "The POI doesn't have enough activities linked to be published", "107947": "Sub POI can't be published", "107948": "Unverified POI can't be published", "108020": "Prompt {number}", "108064": "You can use functions to add or delete FAQs or to generate description texts for POIs only for the EN_US content, and other languages will be updated in accordance with changes made in English", "108218": "Rate plan budget", "108219": "You can allocate budget for a rate plan. Once the budget runs out, this rate plan will end automatically.", "108220": "Limit by amount", "108221": "Limit by units", "108222": "No limitation needed", "108223": "You need to allocate budget to the rate plan", "108224": "You need to fill in budget amount", "108225": "You need to specify limitations for the units", "108236": "You can only enter integers", "108762": "This information overwrite the merchant's default contact information on the customer's voucher", "108765": "Store name", "108766": "Address", "108769": "Other", "108782": "Exploitation of animals & wildlife (e.g. panda hugging, elephant riding, safari)", "108783": "Bias/prejudice (e.g. sexism, racism)", "108784": "Cannabis/drugs (e.g. Amsterdam coffee shops, ganja walking tours)", "108785": "Exploitation of children (e.g. orphanage tours, activities involving charitable organizations)", "108786": "Exploitation of socio-economic situation (E.g. slum tours)", "108787": "LGBT (e.g. gay-friendly tours, pride parades/festivals)", "108788": "Post-crisis (earthquake, nuclear, typhoon) tours (e.g. Fukushima, Chernobyl, Haiti)", "108789": "Sex (e.g. adult toys museum, red light district tours)", "108790": "Harming sustainability (e.g. activities that may lead to environmental damage/pollution)", "108791": "Medicine (e.g. vaccination, DNA test, plastic surgery)", "108792": "Refugees (e.g. refugees working illegally in foreign countries)", "108793": "None of the above", "108844": "You need to provide the insurance policy to go to next step", "110086": "Exporting", "110087": "You can download the file after it's exported", "110088": "Cancel export", "110089": "Export cancelled", "110090": "File exported", "110091": "The file is now ready to be downloaded", "110092": "Download", "110093": "Export failed", "110094": "Something went wrong. Please try again.", "110095": "Export again", "110096": "The file exceeds {num} rows and is too large for export. Please try again with a smaller file.", "110178": "Heads up", "110179": "Please double-check if these restrictions have been clearly notified to the merchants:<br/>- The package will be automatically unpublished when the validity period passes<br/>- The default participation date will be the last day of the validity period<br/>- Inventory model can only be either Unlimited or Total<br/>- Price model can only be Fixed Price", "110420": "We suggest hiding the retail price on the activity page for Tours", "110653": "You can't use this formatting style", "110654": "The content has some formatting styles that can't be used", "111885": "For the fixed price model, go to \"{variable}\" page to change the price", "111886": "Edit price and inventory rules", "111887": "For the total inventory model, go to \"{variable}\" page to change the inventory", "112107": "This is the price you'll provide the product to customers without discounts applied. Klook has the right to decide the final price that customers have to pay, which may be higher or lower than the retail price you entered.", "112108": "This is the amount you will get paid", "112109": "Commission rate", "112744": "Ticket quantity: {num}", "112745": "Responsible BD", "112910": "English characters, numbers and symbols only in the package title", "114386": "Package Photo", "114446": "Pass discount", "114447": "Select discount rate (e.g. \"20\" for 20% off)", "114448": "The info will be shown on the activity banner and USP", "114449": "Preview", "115450": "Use {{poi}} for POI names", "115832": "Upload banner image and select discount rate for your pass activity", "115833": "Up to {discount}% off", "117148": "Guidebook", "118632": "Update all bookings later", "118633": "Suggest the same date for all bookings", "118634": "Reject all bookings", "118635": "Go back", "118636": "Other bookings with the same participation date", "118637": "Notes about all updated bookings", "118707": "Booked participation date", "118708": "Bookings awaiting confirmation", "118709": "Suggested date", "118710": "There's not enough capacity left on the new date. Please suggest different dates for the other bookings separately.", "119180": "Suspend sales automatically when the stock runs out", "119181": "If you toggle this on, sales of the package will be automatically suspended when all the units are out of stock. Once inventory is updated through API sync, the sales will be automatically resumed. We will highly recommend toggling this on if your package gets sold out frequently.", "119369": "Can’t find your report? Click here to see all the past booking reports.", "119617": "Verification", "119618": "Please verify yourself to continue", "119826": "Inquiry type", "119827": "I want to report an error", "119831": "Please select", "119832": "Please enter", "119833": "Inquiry description", "119834": "Please describe the details of your request or summarize your issues. Let us know what we can do to assist you.", "119835": "Relevant activity/package", "119836": "Please enter activity ID or package ID", "119837": "Screen shot", "119838": "Relevant info (Automatically filled in)", "119839": "Report an error", "119840": "The region I want is unavailable", "119841": "Report the error", "120316": "Create my first activity", "120317": "My activities", "120318": "Create activity", "120319": "Create your first activity and bring unforgettable experiences to millions of travelers around the world!", "120320": "You can create up to 5 activities in total", "120321": "Your registration has been rejected", "120322": "The merchant is still waiting for approval of their registration. Please review their application and make an approval first.", "120323": "You must review the merchant's application and make an approval first", "120372": "Finish creation", "120373": "Edit", "120374": "Created on {time_dMMMYYYY}", "120375": "Review now", "120376": "Review later", "120377": "Choice Pass", "120378": "Day Pass", "120381": "pending planning", "120383": "You have no access to manage Day Pass", "120394": "Confirm", "120460": "Activity management during registration", "120636": "<PERSON><PERSON><PERSON>", "120803": "Tips:<br/>This action will reset the price settings for your activity. You will need to reconfigure the price and resubmit the activity.<br/>Are you certain you want to proceed with this action?<br/>[No, cancel] [Yes, proceed]", "121699": "Reservation policy", "121700": "Do customers need to make reservations in advance?", "121701": "Yes", "121702": "No", "121703": "Reservation method", "121704": "Via Klook app", "121705": "Others", "121706": "Please select", "121707": "Add", "121708": "Write details about the reservation methods (optional)", "121709": "Refund policy for no-shows", "121710": "If no-shows are non-refundable according to your policy, customers won't be allowed to request refunds if they don't show up for their appointment", "121711": "Non-refundable", "121712": "Refundable", "121713": "Reservation cancellation & amendment policy", "121714": "Customers can cancel or amend their reservation anytime before the appointment confirmation. After appointment confirmation, they can cancel or amend their reservation at least {num} {hours_days} before the confirmed appointment time.", "121715": "Available appointment times", "121716": "Select all available dates & times", "121717": "Day of the week", "121718": "Start time", "121719": "End time", "121720": "Select unavailable dates (optional)", "121721": "Start date", "121722": "End date", "121723": "Reservation deadline", "121724": "Customers need to make a reservation at least {num} {hours_days} before the preferred appointment time", "121725": "Select sections to copy:", "121726": "{package_id_name}: field of \"{filed_name}\" is not eligible. Please modify it first before copy", "121727": "Ticket Type", "121728": "Voucher issuance", "121729": "Copied! Due to system limitations, {package_id_name} will automatically become non-bookable to agents accordingly.", "121730": "Merchant offline redemption", "121731": "Voucher issuance must be \"per booking\" or \"per unit\" if the reservation method is via Klook app", "121732": "Merchant offline redemption should not be allowed if the reservation method is via Klook app", "121733": "Packages should not be bookable to agents if the reservation method is via Klook app", "121734": "Spa bookings", "121735": "Reservation status:", "121736": "Awaiting requests", "121737": "Partially reserved", "121738": "Requested", "121739": "See all reservation requests", "121740": "Reservation list", "121741": "Filters", "121742": "Reservation ID", "121743": "Booking reference ID", "121744": "Confirmed appointment time", "121745": "From {hh_mm}", "121746": "To {hh_mm}", "121747": "Preferred appointment time", "121748": "Activity/Package ID or name", "121749": "Contact person", "121750": "All", "121751": "Urgent: Preferred time is impending", "121752": "Overdue: 24+ hours passed since request", "121753": "<PERSON><PERSON>", "121754": "Overdue", "121755": "Activity ID - activity name", "121756": "Package ID - package name", "121757": "Reserved participants", "121758": "Preferred appointment time", "121759": "Confirmed appointment time", "121760": "Cancellation & amendment policy: Free to cancel or amend at least {num} {hours_days} before the confirmed appointment time", "121761": "Refund policy for no-shows: {refundable_nonrefundable}", "121762": "Message to the customer", "121763": "Confirm reservation", "121764": "Suggest different time", "121765": "Reject reservation", "121766": "Reservation confirmation", "121767": "Please select one from the customer's chosen dates and times to make confirmation accordingly", "121768": "Confirmed appointment time", "121769": "Please select", "121770": "Appointment time selection", "121771": "Message to the customer (optional)", "121772": "The content will be machine-translated into the customer's preferred language", "121773": "When you reject this reservation request, you can suggest alternative appointment times for customers to make reservations again accordingly", "121774": "Alternative suggestions", "121775": "Make suggestions", "121776": "You can select suggested dates within the upcoming 180 days", "121777": "Select all", "121778": "After the rejection, the customers will need to reserve again or cancel their bookings depending on their needs", "121779": "Are you sure you want to reject this request?", "121780": "The code matches with a different reservation (reservation ID: {id}). Please redeem the correct QR code.", "121781": "The code does not match with this reservation. Please make sure the QR code is redeemed from the corresponding reservation detail page.", "121782": "Redeem anyways", "121783": "Go back", "121830": "Select all available times", "122696": "Photo alt text for SEO (Optional)", "122697": "This field will be automatically filled with the same content as photo descriptions or the activity title if it's left empty", "122698": "This field was automatically filled with the same content as the English activity title", "122699": "Keywords automatically generated by ChatGPT has been provided", "122700": "This field was automatically filled with the same content as the activity title", "122701": "This can't be done because either the required fields are not filled in or your account doesn't have the permission", "123206": "The ticket type must be \"Open date\" if the reservation method is via Klook app", "123402": "Selected dates", "123699": "See/edit all suggestions", "123796": "Merchant's suggestions", "123840": "Search activity/package", "124008": "No-show", "124959": "Create groups", "124964": "Draft", "124967": "Edit groups", "125033": "Published", "125034": "Group info", "125035": "Add new", "125036": "Group name on US English page", "125037": "Please enter", "125038": "Group description on US English page", "125039": "Promoted group", "125040": "Set as promoted group", "125041": "Only one group can be set as a promoted group for each activity. On the live page, promoted groups are displayed on the top of other groups with a highlighted design.", "125042": "Apply US English", "125043": "AI-translate US English", "125044": "Group name on other language pages", "125045": "Cancel", "125046": "Confirm", "125047": "Saved!", "125048": "Group {order_number}", "125049": "Edit", "125050": "Total number of packages in this group", "125143": "Leaving already? Any unsaved info will be lost.", "125179": "Save sequence", "125180": "Package grouping", "125181": "Any ungrouped packages will automatically belong to this group. If there are any published packages in this group, they will appear in the \"Others\" group on the live page.", "125182": "All available packages", "125183": "package(s)", "125184": "Search by package ID/name", "125185": "Packages in this group", "125186": "As there are no packages in this group, it won't be displayed on the live page", "125187": "Preview", "125188": "Save as draft", "125189": "Publish", "125190": "Are you sure you want to publish? Packages will be immediately displayed in groups on the live page. Non-English live pages may display outdated English content or AI-translated content until translations are complete.", "125191": "No package found", "125194": "Are you sure you want to delete this group?", "125195": "You need to remove the packages included in this group first", "125196": "OK", "125197": "Others", "125198": "Package unique selling point", "125199": "This is displayed only on the grouped packages", "125200": "Describe what's specially/exclusively offered by this package", "125201": "Combo package titles can be edited even if the attribute mapping was done. However, if a combo package is blacklisted, its package title will be automatically generated by attribute mapping and such a package will be displayed under the \"Standard\" tab of the \"Package options\" section on the live page.", "125248": "Unpublish", "125249": "Are you sure you want to unpublish the package groups? Packages won't be displayed in groups on the live page.", "125250": "There already is a promoted group. Are you sure you want to change the promoted group to this one?", "130943": "Deactivate Pass asset voucher", "130945": "Voucher unit", "130946": "Deactivate Pass asset voucher", "130947": "Unavailable", "130948": "Available", "130952": "Please follow IWP to apply for the compensation accordingly", "130953": "Start compensation", "130955": "Please double-check if the info is correct and confirm with the user. Once deactivation is complete, it can't be undone.", "130956": "The total amount to be compensated is {amount}", "130957": "The following asset voucher(s) will be deactivated:", "135578": "Deactivation result", "141865": "Some asset vouchers couldn't be deactivated", "142595": "Complete", "142731": "Incomplete", "142732": "OK", "142779": "The following asset voucher(s) couldn't be deactivated:", "143327": "Reason", "159932": "Amount to be compensated", "159933": "Calculation logic", "160687": "Double confirm compensation amount", "160688": "The total amount is {amount}. Please double confirm the compensation units and amounts.", "161206": "The information entered here will automatically appear on the \"Other info\" section of the customer's checkout page. Customers will select their preferred pick-up/meet-up location accordingly.", "161207": "I can meet the customers only at a specific location", "161208": "I can pick up the customers from their location within the covered region", "161209": "Pick-up coverage", "161210": "By administrative districts", "161211": "By customized area", "161212": "District selection", "161213": "Area selection", "161214": "Pick-up area", "161215": "Extra fee", "161216": "Action", "161217": "Edit", "161218": "Delete", "161219": "Add customized area", "161220": "<PERSON><PERSON><PERSON><PERSON>", "161221": "Confirm", "161222": "Cancel", "161223": "Heads up!", "161224": "The selected area can't be added as it overlaps with the existing area", "161225": "Got it", "161226": "Customized area setting", "161227": "Select a district to choose from", "161228": "Area name", "161229": "Name this area", "161230": "Once you make the edits, the same coverage will be applied to all the itineraries in the same region. Are you sure you want to proceed?", "161231": "Required information types were automatically selected based on the itinerary", "166250": "Batch update", "166254": "Batch update reservation activity (Refer to the {instructions})", "166255": "Reservation activity to update", "166256": "Select activities you want to update", "166257": "Search by English activity name (e.g. Universal Studios)", "166258": "Pass activity to update", "166259": "Select the activity you want to edit", "166260": "Search by English activity name (e.g. Klook Pass Singapore)", "166261": "Filter by English package/unit name", "166262": "See published packages & units only", "166263": "Select all", "166264": "Unselect all", "166265": "Reset", "166266": "Published", "166267": "Unpublished", "166268": "Klook Pass operation", "166269": "Result", "166270": "Here are the batch update results. Please take the necessary actions for incomplete updates.", "166271": "Update complete: {num} item(s)", "166272": "Update incomplete: {num} item(s)", "166273": "Package and unit name", "166274": "Update action", "166275": "Incomplete reason", "166276": "Update confirmation", "166277": "Units may be automatically unpublished while the batch update is in progress and then republished once it's done. Please be careful not to manually publish or unpublish any units during the update.", "166278": "Please ensure that the batch update will be done according to the following details:", "166279": "The number of reservation activities is smaller than the reservable amount", "166280": "Republish incomplete", "166281": "Unpublish incomplete. Please try again.", "166287": "Add", "166288": "Remove", "166334": "instructions", "166894": "Please select the tiers first", "167230": "Departure location photo", "167231": "Attraction/experience photo", "167232": "Restaurant/food photo", "167233": "Location name", "167234": "Drag your cursor to draw a customized area", "167238": "Fill in this info into “{Itinerary_module}”. What you fill in there will automatically appear on the checkout page as well.", "167239": "itinerary", "167240": "You can update this info in “{Itinerary_module}”", "167271": "Add", "167272": "Cut out", "167273": "Undo", "167274": "Redo", "167715": "Selected areas", "167716": "Districts/locations", "168075": "Time range", "168076": "Time/Time range", "168874": "Reservation deadline", "170359": "Create POI page(s)", "170360": "Please enter the POI ID(s) that you want to create the POI pages for", "170361": "Separate IDs with a comma", "170362": "{num} POI page(s) created!", "170364": "{num} POI page(s) weren't created due to the following reasons:", "170365": "Number", "170366": "Reason", "170367": "POI ID invalid", "170368": "POI unverified", "170369": "Only 1 activity published (min. requirement: 2)", "170370": "Duplicated POI", "170371": "Sub-POI", "170372": "Export result", "170377": "Creating", "170378": "Created", "170746": "The time zone above is only for the reference. Please note that the time you choose here will be applied to all the selected rate plans regardless of time zones. For example, if you choose 14:00, the time base for all rate plans will be 14:00 in each of their time zone.", "170770": "Terms & Conditions", "171250": "How is the itinerary provided?", "171251": "Specified time-based", "171252": "The itinerary departs and returns to the same meet-up/pick-up location at a specified time", "171253": "Multiple time option-based", "171254": "Customers can choose from multiple departure and return time options to and from the meet-up/pick-up location", "172380": "Additional data purchase settings", "172381": "Allow buying additional data for this package?", "172382": "Applicable additional data package ID", "172383": "Cancel", "172384": "Edit", "172672": "Inventory management method", "172673": "Via API integration", "172674": "(Seamlessly synchronize the inventory and pricing with your reservation system)", "172675": "Manually on Klook", "172676": "The confirmation policy of the products connected to the API must be set as \"Instant Confirmation\"", "172677": "API mapping", "172678": "Select your API partner", "172680": "Self-mapping is supported for {partner_name}. To connect your inventory with API, please fill in the mapping template provided below. For detailed instructions, please refer to the {Guidebook}", "172681": "guidebook", "172682": "API mapping template", "172683": "(Applied to all units)", "172684": "Other info mapping attributes from API partners", "172685": "Self-mapping is not supported for {partner_name}. Reach out to your Klook Account Manager or submit this {form} to get help.", "172686": "form", "172687": "Connected", "172688": "Please go to the \"Supply API backend\" to manage API settings", "172689": "To edit the package, you'll need to unpublish it first", "173156": "Please select at least 1 package", "173707": "Please go to the \"{Supply_API_backend}\" to manage API settings", "173708": "Supply API backend", "174481": "Please reserve as much in advance to participation as possible", "174482": "Please reserve at least {num} {time_unit} before participation", "174915": "Nothing to see here yet. Start your journey by creating a new activity!", "174916": "Create your activity", "174917": "Click \"Create new activity\" to start creating your activity. Ensure all fields are filled out.", "174918": "Submit for approval", "174919": "Click \"Submit activity\" and \"Submit package\" buttons to submit your activity for review.", "174920": "Wait for your approval", "174921": "Klook will complete the review within 7 days.", "174922": "Publish your activity", "174923": "Once your activity and package content are approved, Klook will publish your activity.", "174933": "See creation process", "174934": "Activity creation process", "174936": "All set? Click the button to submit your activity & package for review", "174937": "Submit", "174938": "Requirements", "174939": "Edit", "174940": "Suggestions", "174941": "Click here to submit for review!", "174942": "Requirements for submission are not met for this package", "174943": "Reminder", "174944": "It seems you are ready to submit the activity & package. Are you sure you want to leave without submitting?", "174945": "Leave", "174946": "Submit", "174948": "Submission result", "174949": "Result", "174950": "Succeded", "174951": "Failed", "174952": "Reason", "174953": "Please check the result above. Please take the necessary actions for failed submissions. If all submissions were successful, please wait 7 days for Klook to review.", "174964": "Location", "174965": "Departure city", "174966": "Enter city name to search", "174967": "You can only select cities within the same country", "174968": "Matching city", "174969": "Activity location", "174970": "A location that customers will visit (Only one location allowed)", "174971": "Activity location(s)", "174972": "List of all locations that customers will visit", "174973": "Address", "174976": "Destination that the location belongs to:", "174977": "Other destinations that the location belongs to:", "174978": "Have questions/requests about the destination?", "174979": "Set as primary location", "174983": "The primary location will be displayed on the activity's live page as part of the activity address and will be used for systemic processes such as data analytics", "174994": "Add/edit activity location(s)", "174995": "Select activity location", "174996": "Enter an address to find location", "174997": "List all locations that customers will visit", "174998": "Single location allowed", "175006": "Nothing to see here yet", "175007": "Search a location to add", "175008": "Just a moment!", "175009": "You have unsaved changes. Are you sure you want to leave?", "175010": "Leave", "175129": "Please submit the modified cost before you leave", "176818": "History", "176829": "Details", "176830": "Category", "176831": "Updated by", "176832": "Updated on", "177147": "Categories can't be changed. Please choose carefully.", "177148": "Choose a category for your product", "177150": "Usually comes with an itinerary in which customers will be visiting multiple spots guided by a tour guide or staff, regardless of durations, modes of transportation or themes.", "177151": "Activities focused on a single location or theme, helping users experience the cultural and geographical beauty of the destination, possibly involving instructors, teachers, or staff members.", "177152": "Is it a spa, massage, hot springs, or beauty-related activity?", "177153": "Examples: 1-day tours, multi-day tours, cruise tours, bicycle tours, and walking tours", "177154": "Examples: kimono rental, ski classes, diving, cooking classes, hot springs, and massages", "177155": "Examples: massages, hot springs, mud baths, and salons", "177156": "Please choose a sub-category that best matches your activity:", "192408": "Audio guide", "192409": "Upload", "192410": "BDs can upload Klook-generated audio guides for selected merchants. Please reach out to @Jenny Li from the T&A Planning team for details.", "192412": "Audio guide for {pkg_name}", "192413": "Attraction/experience", "192414": "File requirements", "192415": "File format: MP3<br/>File length: Up to 30 mins<br/>File size: Up to 50 MB", "192416": "File must be in MP3 format", "192417": "File must be 50 MB or less", "192418": "File upload error. Please try again.", "192743": "Calendar-based or API-synced", "193011": "Take over the edits", "193012": "If you take over the edits, {Editing_user_name} will be removed and their edits won't be saved.", "193013": "Before continuing, remember to let {Editing_user_name} know and remind them to save their changes.", "193014": "Confirm", "193015": "Take over the edits to this activity?", "193016": "You've been removed as {user_name} has taken over the edits. You'll redirected to the activity list.", "193216": "Required section", "193217": "This section must be filled in", "193218": "Please fill in at least one section", "193304": "Don't show me this again", "193305": "Please submit your package and apply for API connection first", "193435": "The image size exceeds the limit, please re-upload. Banner image size requirements: no more than  {file_size_limit}  Mb, width not exceeding  {image_width_limit}  pixels, and height not exceeding  {image_height_limit}  pixels.", "193813": "Ensure that the combo selling price is higher than the cost price", "193817": "Low quality photo! Please submit another one.<br/>Current photo: {filesize}, ({width}x{height}), Laplacian: {variance}<br/>Minimum requirement: {minfilesize} ({minwidth}x{minheight})", "193818": "Photo is too large! Please submit another one. <br/>Current photo: {filesize}, ({width}x{height}), Laplacian: {variance}<br/>Max size: {maxfilesize} ({maxwidth}x{maxheight})", "193819": "Passed! Current photo: {filesize}, ({width}x{height}), Laplacian: {variance}<br/>Minimum: {minfilesize} ({minwidth}x{minheight})", "193820": "Please upload a clearer photo! Minimum requirement: {minfilesize} {minsize} ({minwidth}x{minheight})", "194907": "If you have any questions, please contact your responsible BD or send an email to: <EMAIL>.", "196161": "Pre-sale settings", "196162": "When will the voucher be issued?", "196163": "Voucher will be issued {x}-{y} days before participation date", "196164": "Voucher will be issued at least {x} days before the participation date", "196165": "Pre-sale", "196167": "Pre-sale time settings", "196169": "2C Display Start Time(Local time)", "196170": "2C Display End Time(Local time)", "196171": "Current time", "196172": "No end time", "196177": "Is pre-sale allowed?", "196628": "See", "196629": "All unit types", "196630": "Unit types created by me", "196631": "Unit type ID", "196632": "Unit category", "196633": "Unit type name", "196634": "Last updated (GMT+8)", "196635": "Last updated by", "196636": "Language status", "196637": "Unit type setting", "196638": "Please choose a category for this unit", "196639": "Unit type name (en_US)", "196640": "Please choose sub-categories for this unit", "196641": "Please make sure localization process is finished before you publish.", "196642": "This unit type is the same as an existing unit type. Please make sure they're different before trying again.", "196643": "- Only English letters, numbers, commas, hyphens, and parentheses \",( )\" are allowed<br/>- Avoid special characters, such as \"~ ! * $ ? _ ~ [ ] # < > * ; ^ ¬ ¦ \"<br/>- Character limit: 30", "196644": "All services", "196645": "Services created by me", "196646": "Service ID", "196647": "Service name", "196648": "Service option", "196649": "Service settings", "196650": "Which sub-categories can this service be applied to?", "196651": "Add service option", "196652": "Service option ID", "196653": "Service option name", "196654": "Operations", "196655": "Allow users to add custom service options", "196656": "Ensure localization is complete before publishing", "196657": "This service is the same as an existing service. Please make sure they're different and try again.", "196658": "Unable to publish as localization is not complete. Please complete it before publishing.", "196659": "Create using previous admin process", "196660": "Do you want to migrate an existing activity or create a new product?", "196661": "Migrate existing activity", "196662": "Create new product", "196663": "AID name", "196664": "Use this AID to create my new activity", "196665": "AIDs can only be used once and cannot be changed after this", "196666": "Packages (You can choose more than 1)", "196667": "<PERSON><PERSON>", "196668": "Basic info", "196669": "Start & end", "196670": "Photos / videos", "196671": "Policies / redemption", "196672": "Included / Not included", "196673": "Restrictions and other details", "196674": "Participant info", "196675": "Operation info", "196676": "Rules settings", "196677": "Inventory schedule", "196678": "Basic info", "196679": "We suggest using the language you are most comfortable with", "196680": "Which languages to display the activity in?", "196681": "Unselect languages you don't need", "196682": "Clear all", "196683": "Which city does your tour depart from?", "196684": "How many group types will you offer?", "196685": "1 group type", "196686": "E.g. Private tour only, Join-in group tour only", "196687": "More than 1", "196688": "Can set different prices/inventory for each option later.", "196689": "Select a group type", "196690": "Private", "196691": "Join-in (small group)", "196692": "Join-in (big group)", "196693": "Group size limit:", "196694": "How long is this tour?", "196695": "Half-day", "196696": "Less than 7 hours", "196697": "One-day", "196698": "7 hours or more within the same day", "196699": "Multi-day", "196700": "More than 1 day", "196701": "What are the main attractions or experiences?", "196702": "Select up to 2 attractions or experiences that are most attractive to customers. They'll be used to generate your tour title.", "196703": "What are some selling points of this tour?", "196704": "Select up to 2 keywords to help customers understand the tour highlights. They'll be used to generate your tour title.", "196705": "Merchant settings", "196706": "Start & end", "196707": "What time does the tour start?", "196708": "Add a start time", "196709": "Meet-up only", "196710": "Customers need to go to the meet-up point on their own", "196711": "Pick-up only", "196712": "Customers will be picked up from their accomodation", "196713": "Meet-up and pick-up both available", "196714": "Customer can choose either one", "196715": "Meet-up info", "196716": "Meet-up point address", "196717": "Meet-up time", "196718": "A tour \"ends\" when customers leave the tour. End time must be later than the start time.", "196719": "Flexible start time", "196720": "Needs to be later than meet-up time for meet-up point {num}", "196721": "Pictures of meet-up point", "196722": "Recommended resolution", "196723": "Provide at least 1 photo, ideally including actual photos of the meet-up point", "196724": "Additional description", "196725": "Add a description to make the meet-up point easier to find. E.g. \"A 5-minute walk from Exit 8, West Exit of Kyoto Station, Kyoto Line\"", "196726": "Add another meet-up point", "196727": "Meet-up time cannot be earlier than the start time", "196728": "Supply API mapping key", "196729": "Pick-up info", "196730": "How do you want to choose the pick-up area?", "196731": "Use a standard district map", "196732": "Most merchants choose this option", "196733": "Draw a custom area", "196734": "Draw my own pick-up area on the map", "196735": "Pick-up areas", "196736": "Change currency", "196737": "Extra pick-up fees", "196738": "Pick-up time", "196739": "Meet-up time cannot be earlier than the tour start time", "196740": "Pictures of pick-up area", "196741": "We recommend including pictures of the location, screenshot of a map, signs or location markers.", "196742": "Add a description to let customers know where to wait. E.g. \"Please wait for your driver at the gate of the selected pick-up point\"", "196743": "Select the districts where pick-up is available", "196744": "Select pick-up areas", "196745": "Add new custom area", "196746": "Custom area name", "196747": "What time does this tour end?", "196748": "Each timeslot needs its own end time", "196749": "Session {num}", "196750": "Is transport provided to the end point?", "196751": "Transport provided to a drop-off point decided by the merchant", "196752": "Transport provided to customers' hotel, airbnb etc", "196753": "No, tour ends at the last stop", "196754": "Customers will leave directly after the last stop", "196755": "Drop-off point", "196756": "Add another option", "196757": "Reminders for customers", "196758": "How long is this tour?", "196759": "What’s next?", "196760": "For example, in the next tour segment, which attractions will be visited, what mode of transportation will be taken, and what kind of cuisine will guests be able to enjoy?", "196761": "The tour passes by this attraction without entering", "196762": "Attraction / experience name", "196763": "Photos", "196764": "Include at least {num} photos of the attraction / experience. This will give customers a good idea of what to expect.", "196765": "What are some highlights and unique features?", "196766": "Sub-attraction / experience", "196767": "This tour segment includes multiple attractions or experiences.", "196768": "Yes, add 1", "196769": "Is this a guided tour?", "196770": "No, it's self-guided", "196771": "Yes, there will be a guide", "196772": "Is the entrance ticket required and included?", "196773": "Required and included", "196774": "Ticket price is included in the tour", "196775": "Required, but not included", "196776": "Customers must buy it either online or on site", "196777": "Free entry", "196778": "No ticket is required at all", "196779": "Required, and can be included or not included.", "196780": "You'll be able to set different prices and inventory later on", "196781": "What transport options do customers have?", "196782": "Only 1 option", "196783": "Recommended option - used by most tours", "196784": "More than 1 option", "196785": "Transport types", "196786": "Photos of transport types", "196787": "Add photos to help customers understand their transport options", "196788": "What meal is this?", "196789": "What cuisine is it?", "196790": "Restaurant name and city", "196791": "Photos of restaurant or food", "196792": "Is this meal included in the tour?", "196793": "Yes, included", "196794": "This meal is included in the tour price", "196795": "Customers can decide", "196796": "Accomodation type", "196797": "Accomodation name", "196798": "Photos of the accomodation", "196799": "Add photos to help customers understand what to expect", "196800": "Are there alternative accomodation options?", "196801": "Add more options if there alternatives, in the event that the accomodation above is unavailale.", "196802": "Yes, add 1", "196803": "Add a custom location name", "196804": "Add custom location", "196805": "Location name", "196806": "Select a point closest to this location", "196807": "Search for an address or drag the red pin to set the correct location for customers", "196808": "Cover photo", "196809": "- Image size: min 300KB (1280x720); max 4MB (1920x1080)<br/>- File format: JPG or PNG", "196810": "Set a different photo / description for each language?", "196811": "Set by language", "196812": "De<PERSON>ult cover photo", "196813": "Gallery", "196814": "Filter photos by", "196815": "Show it on", "196816": "Set different videos for each language?", "196817": "Default video", "196818": "Show videos settings for other languages", "196819": "Photos for each tour segment will be displayed automatically. There's no need to re-upload them.", "196820": "{num} selected", "196821": "Redemption options", "196822": "Merchant contact info", "196823": "What unit types are available for this tour?", "196824": "Current unit types", "196825": "<PERSON><PERSON>", "196826": "Age requirements", "196827": "Customers don't need to enter additional participant info for this unit", "196828": "Default unit name:", "196829": "Enter a custom name", "196830": "Enter a custom name (in en_US)", "196999": "Attraction info", "197000": "Name & location", "197001": "Photos & videos", "197002": "Announcements", "197003": "Guidebook", "197004": "Highlights", "197005": "Unique selling points", "197006": "SEO info", "197007": "SPU shelf", "197008": "Grouping", "197013": "SPU info", "197014": "Basic info", "197015": "SPU policies", "197016": "SPU details", "197017": "Checkout info", "197018": "Operational info", "197019": "SKU management", "197020": "Unit type", "197021": "Price & inventory", "197500": "{POIs} {tour_duration} {USPs} Tour", "197501": "Your tour title is:", "197669": "A service cannot have more than 10 service options", "197675": "You can currently only select 1 AID. Unfamiliar with the product creation process? Please refer to this <a target=\"_blank\" href=\"https://docs.google.com/document/d/18vUgDWmtkz0vRAn02IU9ozWCYbTX4fT_1Lj6KXnkSJg/edit?tab=t.0#heading=h.fi4342jep14z\">user manual</a>.", "197695": "How do customers join the tour?", "197731": "Please fill in all required info to continue", "197733": "Select what customers will experience in the next segment of their trip. For example, attractions they will visit, transport options, or meals they will enjoy.", "197734": "Yes, see more", "197735": "Pictures can help customers better understand what to expect from the accomodation", "197737": "Pictures can help customers better understand the meal", "197739": "You can set prices separately", "197740": "Choose this if your tour provides more than 1 transport option", "197761": "Based on your previous settings, the duration has been automatically selected as:", "197762": "One day or less", "198001": "years old", "198002": "Max", "198003": "Min", "198208": "Yes, to drop-off point", "198209": "Yes, to customers' accomodation", "198460": "Please add at least one attraction/experience", "198462": "Cancellation policy", "198463": "Display settings", "198464": "Other tour info", "198513": "Attraction list", "198514": "Range:", "198515": "See all attractions", "198516": "See attractions I'm responsible for", "198517": "City:", "198518": "Attraction status:", "198519": "Click to select or search", "198520": "Click to select", "198521": "Keywords", "198522": "Enter keywords", "198523": "Attraction ID & name", "198526": "Responsible BD", "198527": "Last updated (GMT+8)", "198528": "Manage languages", "198531": "SPU management", "198532": "Edit", "198533": "Edit log", "198534": "Preview", "198535": "Unpublish", "198536": "Publish", "198537": "Create new attraction", "198538": "Previous page", "198539": "Next page", "198540": "Search", "198541": "Reset all", "198542": "Published!", "198543": "How satisfied are you with the onboarding?", "198544": "Very dissatisfied", "198545": "Very satisfied", "198546": "How long did it take you to onboard this product or attraction?", "198547": "Enter a number", "198548": "min", "198549": "Other feedback", "198550": "Share your feedback and help improve the admin experience", "198551": "Submit feedback", "198552": "Duplicate attractions", "198553": "We've found similar attractions on the admin system", "198554": "Check and confirm that there are no duplicates. If the attraction you want to create already exists, please use it instead.", "198559": "Editing in {language}", "198560": "Referencing {language}", "198561": "Preview current attraction", "198562": "Preview opening hours", "198563": "Opening hours will be applied to every calendar year.", "198564": "Opening dates (Local time)", "198565": "Opening time (Local time)", "198566": "Last entry time", "198567": "Date range", "198568": "All year", "198569": "Fixed dates", "198570": "Select", "198571": "Closed all day", "198572": "Time", "198573": "Opening hours details", "198574": "Please enter any additional details", "198575": "Add opening hours", "198576": "Required field", "198577": "Start date", "198578": "End date", "198579": "Please select", "198580": "Select time", "198581": "All", "198582": "Editing language", "198583": "Reference language", "198584": "Language settings", "198585": "Confirm", "198586": "Cancel", "198587": "Change language?", "198588": "Changes made in {language} are still in draft and haven't been saved.", "198589": "Edited:", "198590": "Save & change to {language}", "198592": "Change to {language} without saving", "198593": "Cover photo", "198594": "Set different images for different languages", "198595": "Go to set", "198596": "Default image (all)", "198597": "Gallery", "198598": "Show it on", "198599": "All languages", "198600": "Video", "198601": "Set different videos for different languages", "198602": "Default video (all)", "198603": "Show all (including other languages)", "198605": "Attraction announcements", "198638": "<PERSON><PERSON> Insider {numbers}", "198639": "Enter your announcement", "198640": "A tour \"ends\" when customers leave the tour", "198641": "Included & Not included", "198642": "Some inclusions have been auto-applied based on your previous selections", "198643": "Guide", "198644": "Guide type", "198645": "Auto-applied", "198646": "Applied based on your itinerary settings", "198647": "Edit", "198648": "Add new", "198649": "Add meal", "198650": "We recommend adding descriptions about this service into your itinerary. This will help guests better understand.", "198651": "Add other items", "198652": "Ensure there are no duplicates", "198653": "Yes, add new item", "198654": "BD setting", "198655": "Inventory management method", "198656": "Based on your previous settings, we have automatically selected:", "198657": "Can be changed based on the date", "198658": "No upper limit", "198659": "All dates share the same inventory", "198660": "Same price for all dates", "198661": "Prices may change with date or will be synced via API", "198662": "Which currency do you want to sell the activity in?", "198663": "Available days & cut-off time", "198664": "Available everyday", "198665": "How far in advance do customers need to book?", "198666": "Days", "198667": "Select days", "198668": "<PERSON>s", "198669": "Select mins", "198670": "Add different availabilities for specific dates", "198671": "See less", "198672": "See all", "198673": "Create item", "198674": "1. Create items based on your previous settings<br/>2. Set price and inventory for these items<br/>3. Check the price and inventory calendar<br/>4. Submit item for review", "198675": "Service", "198676": "You can only add up to 5 services at the moment", "198677": "Current services", "198678": "We recommend adding as few services as possible. This will keep costs down, and make it easier for customers to choose.", "198679": "New service", "198680": "Create an item", "198681": "See difference", "198682": "Bulk create automatically", "198683": "System will automatically generate all possible combinations based on your selected serivces", "198684": "Create manually", "198685": "Manually add items", "198686": "Customers must purchase this unit", "198687": "Duplicated item, please select another one.", "198688": "Current items ({num})", "198689": "Select available items", "198690": "Selected items ({num})", "198691": "Create item", "198692": "Select all", "198693": "Item list ({num})", "198694": "Delete", "198695": "Status:", "198696": "ID:", "198697": "Required to book", "198698": "Best price setting", "198699": "Choose which item price to display in the activity. If unselected, the 1st item's price will be displayed.", "198700": "Best price", "198701": "Select which item to show as the best price", "198702": "Schedule", "198703": "Bulk edit", "198704": "Item to view", "198705": "Price / Inventory Settings", "198706": "More", "198707": "Shared inventory settings", "198708": "Main item", "198709": "Select an item", "198710": "item details are shared with:", "198711": "Selected item", "198712": "Sub-item", "198713": "Copy settings from another item", "198714": "This item will be deleted and cannot be recovered. All price and inventory settings for this item will be cleared.", "198715": "Edit service", "198716": "How do I edit existing services?<br/>To keep information consistent<br/>- First go to {variant_info_page} module to edit {variant_info_module} <br/>- Return here to review items and their prices and inventory", "198717": "See example", "198718": "The service options you've edited ({module}) are related to a item. After this, please go to the {inventory_page} to confirm if your item needs to be updated.", "198719": "Confirm edits?", "198720": "The service options you've edited are related to a item. After this, please go to the {inventory_page} to confirm if your item needs to be updated.", "198721": "The service options you've edited are related to a item. After this, please go to the {inventory_page} to confirm if your item needs to be updated.", "198722": "Select up to 10", "198723": "Add custom service option", "198724": "After editing any services or service options, please confirm the items and their corresponding prices and inventory.", "198725": "Save edits?", "198726": "Save", "198727": "Cancel", "198728": "Items linked to this service will be unpublished. Continue?", "198729": "To delete this option, please uncheck it and delete the corresponding products first.", "198730": "This operation is only available if the activity's status is \"Unpublished\"", "198731": "Got it", "198732": "Option name:", "198733": "Use English", "198734": "Add service option", "198735": "Add service?", "198736": "You've edited a service. Please check your items and their corresponding prices and inventory.", "198737": "Check my items", "198738": "Review current items", "198739": "Please add services for your items. Prices and inventory will not be changed.", "198740": "Select", "198741": "Deleting this item will also delete its prices and inventory.", "198742": "Delete service?", "198743": "Deleting the service will reset the prices and inventory for all items.", "198744": "Delete", "198745": "Are you sure to reset all product & inventory?", "198746": "What are services?", "198747": "Services are attributes of your activity that requires different prices and inventory. Customers will have to select services before placing an order.", "198748": "If an attribute doesn't affect price or inventory, we don't recommend setting it as a service.", "198749": "What's the difference between \"Bulk create\" and \"Create manually\"?", "198750": "Use bulk creation if you can offer most of the service combinations. If not, it's best to create services manually.", "198751": "Example", "198752": "Display Start time - End Time (Local time)", "198753": "Start time", "198754": "End time", "198755": "Add announcement", "198756": "Add up to 3 announcements for this attraction", "198757": "Ticket info", "198758": "Using your ticket", "198759": "Things to note", "198760": "Other info", "198761": "Enter details", "198762": "Highlight title", "198763": "Highlight description", "198764": "Add up to 3 USPs for this attraction", "198765": "USP title", "198766": "USP description", "198767": "Unique selling point", "198768": "Group setting", "198769": "Enable", "198770": "SPUs will be grouped when displayed to customers", "198771": "Disable", "198772": "SPUs will be shown without grouping", "198773": "SPU list", "198774": "{x} SPUs found in this attraction", "198775": "Attraction variants", "198776": "Range:", "198777": "See all variants", "198778": "See variants I created", "198779": "Attraction:", "198780": "Keywords:", "198781": "Select or search for attraction", "198782": "Enter keywords", "198783": "Search", "198784": "Reset", "198785": "Attraction", "198786": "Variant name", "198787": "Variant options", "198788": "Created by", "198789": "Last updated (GMT+8)", "198790": "Language status", "198791": "Variant status", "198792": "Operation", "198793": "Published", "198794": "Unpublished", "198795": "Create variant", "198798": "Variant settings", "198799": "Variant name", "198800": "Enter variant name", "198801": "Attraction", "198802": "Select an attraction", "198803": "Attraction details", "198804": "Variant option list", "198805": "Variant option ID", "198806": "Variant option name", "198807": "Text IDs in the L10N CMS", "198808": "Operation", "198809": "Add variant option", "198810": "Variant info", "198811": "Describe this variant", "198812": "New variant option", "198813": "<PERSON><PERSON><PERSON>", "198814": "Variant option name (en_US)", "198815": "Enter a name for this variant option", "198819": "See schedule", "198822": "Attraction SPU list", "198823": "Attraction management", "198824": "See all SPUs", "198825": "See SPUs I'm responsible for:", "198826": "Category:", "198827": "Keywords:", "198828": "Enter keywords", "198829": "All", "198830": "Published", "198831": "Unpublished", "198832": "SPU ID and title", "198833": "Attraction", "198834": "SPU status", "198835": "Responsible BD", "198836": "Category", "198837": "Create new SPU", "198838": "Batch publish", "198839": "Batch unpublish", "198840": "Previous", "198841": "Preview language", "198842": "Preview on app (Scan QR code)", "198843": "Deep link:", "198844": "SPU name", "198845": "Search by SPU ID or name", "198846": "Variant & SKU list", "198847": "<PERSON><PERSON><PERSON>", "198848": "Variants not needed", "198849": "For smaller, simpler SKUs", "198850": "Needs variants", "198851": "For more complex SKUs", "198852": "The system will create all possible SKUs based on the unit type you choose. Publish the ones you need.", "198853": "Batch publish", "198854": "Batch unpublish", "198855": "SKU status", "198856": "Select variants for your SKU", "198857": "See more", "198858": "See less", "198859": "Need a new variant?", "198860": "Enable sorting", "198861": "Set price and inventory", "198862": "Save SKU list", "198863": "Please complete SKU settings first", "198864": "Apply for new variant option", "198865": "Apply for new variant type", "198866": "New variant option", "198867": "New variant options for an existing variant", "198868": "New variant", "198869": "New variant and variant options", "198870": "Select a variant for this variant option", "198871": "Variant option name (en_US) (Separate the options with commas)", "198872": "Enter variant option name (en_US)", "198873": "Reason for new variant option (en_US)", "198874": "Enter reason", "198875": "Add new", "198876": "Variant name (en_US)", "198877": "Enter variant name", "198878": "No variants yet. Create a variant to get started.", "199044": "Voucher", "199484": "The service options you've chosen create more than 300 possible sale combinations!", "199485": "Too many options affect customers' willingness to buy and your sales. Consider reducing the number of services or service options, or add items manually.", "199486": "Current item", "199487": "Delete service option?", "199488": "Add new service", "199489": "Use US English and keep it under 30 characters. Must not be the same as another service option.", "199490": "Use previous admin system", "199491": "Untitled", "199901": "Select up to 2", "199902": "Klook insider", "199904": "Edit map link", "199905": "Add pick-up area", "199906": "Included", "199907": "Not included", "199928": "Location name visible to customers", "200047": "Product operation setting", "200048": "Financial model", "200078": "Please select at least two:", "200107": "Use English letters, numbers, and symbols", "200121": "This service doesn't contain any service options. Please add a service option to select it.", "200122": "After adding the start time, remember to confirm the booking cut-off time in the {Rules_settings}", "200123": "This start time is currently in use. To edit it, please remove the corresponding item and timeslot.", "200124": "The new start time is related to an item. To make sure everything is in order, first go to the {rules_setting} to confirm if the cut-off time needed to be updated. <br/><br/><br/>After that, go to {inventory_page} to confirm if the item needs to be updated.", "200165": "Included by default:", "200166": "Customer can decide:", "200167": "Accommodation name and duration:", "200168": "Accomodation name:", "200169": "night(s) at", "200170": "Select number of nights and enter accomodation name", "200171": "Enter accomodation name", "200172": "Select number", "200173": "Enter accomodation name", "200174": "Enter accomodation name", "200334": "You're currently using fixed price/total inventory for your rule settings. Please choose the price and inventory before bulk editing.", "200335": "- Image size: 4MB max<br/>- Upload limit: {count_limit} images", "200449": "Private group ({min} - {max})", "200450": "Small group ({min} - {max})", "200451": "Big group ({min} - {max})", "200486": "Unpublish SKUs before changing variants", "200487": "Changing variants will delete all existing SKUs. Please unpublish before changing your variants.", "200489": "This item does not support purchase attributes. Please delete all configured attributes and try again.", "200564": "Please use `Publish with American English` for all other English languages, unpublish first if that English is already published", "200565": "Unpublish", "200566": "Publish with en_US", "200567": "Cancel en_US publish", "200568": "Translation Status", "200569": "Language publish status", "200570": "Publish with AI translation", "200571": "Cancel AI translation publish (Source language={lang})", "200572": "\"Publish with AI translation\" means that any free text will auto-sync with the source language, and an AI translation button will be provided. Only the title and any template-generated content will be published using a human translation.", "200573": "Operation Time Range", "200574": "Select Event Type", "200575": "Operator", "200576": "Select Level", "200577": "Activity", "200578": "Reset", "200579": "Apply", "200580": "Download Timeslot Changelog", "200581": "Content edits log before 2020-12-09 16:30:00 (GMT+8) isn't being incorporated, go find the tech team if you want to trace any log before that.", "200590": "Which language do you want to onboard the attraction in？", "200591": "Attraction name", "200592": "Attraction Location", "200593": "Select location", "200594": "Keywords (for searching on Klook)", "200595": "Which language do you want to onboard the product in？", "200596": "Contact method", "200597": "This can only be changed when the product and units under this product are unpublished", "200598": "The language you are using to onboard a product", "200599": "Make sure to apply for API mapping before you publish the product", "200600": "Voucher", "200601": "Reservation required", "200602": "Strongly recommend to select \"Instant Confirm\" as the policy, otherwise it would affect your product's conversion rate", "200603": "Skip displaying the price of the SPU on attraction card/page", "200604": "Skip displaying the price of the product on attraction card/page", "200605": "Are vouchers for this product transferable to another person?", "200606": "Does the product include the following services", "200607": "Does your product feature any of the following?", "200608": "Are you sure to change the product selling structure?", "200609": "Changing the product structure will rebuild the product, all SKUs will be deleted including all price&inventory of the product.", "200610": "Please unpublish all SKUs first!", "200611": "Changing product structure will delete all existing SKUs. Please unpublish all the SKUs first.", "200643": "A maximum of {max_SKU_num} items can be published. Uunpublish other items first and try again.", "200750": "Rule type:", "200766": "Voucher will be issued on {YYYY-MM-DD}", "200772": "Validity type", "200783": "Submit to EM", "200784": "Preview", "200785": "Publish", "200786": "Unpublish", "200787": "Publish product", "200788": "Unpublish product", "200789": "You are about to unpublish product {SPU_ID}. Please provide a reason:", "200790": "Suspend sale", "200791": "If sale is suspensed, customers will be able to see the product, but will not be able to book it. Example: \"Book now\" button will be disabled.", "200792": "Min must be less than max", "200793": "Expand", "200794": "Collapse", "200795": "Please input valid min group size", "200796": "Max cannot exceed 100", "200797": "Please input valid max group size", "200798": "Max must be greater than min", "200812": "Flexible end time", "200813": "End time must be later than the start time", "200828": "Itinerary photos", "200835": "Unpublish this item before deleting it", "200954": "Only English letters, numbers, commas, hyphens, and parentheses \",( )\" are allowed", "201039": "The product cannot be published due to certain restrictions", "201137": "Disable pre-sale?", "201138": "Disabling pre-sale will delete all price, inventory and date settings", "201670": "{num} meet-up points in {city}", "201671": "{pick_num} pick-up areas in {pick_city} and {meet_num} meet-up points in {meet_city}", "201672": "{pick_num} pick-up and {meet_num} meet-up points in {city}", "201673": "{time_list} & more", "201674": "Departure info", "201675": "Return info", "204211": "Which item(s) to edit？", "204212": "What dates to edit?", "204213": "Availability", "204214": "Manage schedule status", "204215": "To hide certain dates from sale, select \"Deactivate\" to turn off sale without deleting your data.", "204216": "Select days", "204217": "Set status", "204218": "Sort by", "204219": "Service preview on front-end", "204220": "Users will see the following selections when booking and can choose a combination that fits them. This is just an example.", "204221": "*This is just an example", "204222": "Sort service types", "204223": "Services will be presented to users in this order.", "204224": "SEO content display", "204225": "All content in this section will be displayed to users on the AID page.", "204226": "Overview", "204227": "Displayed as \"More about this product\"", "204228": "SEO photos", "204229": "Technical SEO content", "204230": "Content in this section is for technical SEO only. It's not visible to users on the front-end.", "204231": "Heads up! Videos may take longer to load.", "204232": "Both horizontal and vertical videos can be uploaded", "204233": "Upload vertical video", "204234": "Video ratio : 9:16", "204235": "Upload horizontal video", "204236": "Video ratio : 16:9", "204237": "Making the most of your videos", "204238": "Video size: Less than 45 mb <br/> File format: MOV, MP4, AVI, or WMV <br/><br/>Video ratio/Orientation: <br/>- Landscape video = 16:9 / 1080P (1920x1080) or 720P (1280x720) <br/>- Vertical video = 9:16 / 1080P (1080x1920) or 720P (720x1280)", "204239": "Reminders", "204240": "If you upload both types, horizontal videos will be shown on desktop, while vertical videos will be shown on mobile web and app.", "204241": "Horizontal video", "204242": "Base", "204243": "Download original video", "204244": "Select video cover", "204245": "Vertical video", "204246": "Reminders about vertical videos", "204247": "The top and bottom of the video will be cut off. If your video has text, please make sure it's in the middle.", "204248": "Custom cover", "204249": "Edit custom cover", "204250": "If you do not select a cover, the first frame of the video will be used as a default.", "204251": "Preview video and select cover frame.", "204252": "Set as cover", "204253": "Drag the slide above to select the cover frame, or upload a custom cover image.", "204254": "(Photo ratio should be 16:9)", "204255": "(Photo ratio should be 9:16)", "204256": "Pause", "204257": "If you're not happy with the current cover, you can upload a new image at any time.", "204258": "Select another cover", "204259": "Upload", "204260": "Default video", "204261": "De<PERSON>ult cover photo", "204571": "Voucher display mode", "204572": "Static voucher", "204573": "Dynamic voucher", "204574": "Dynamic vouchers have QR codes that refresh periodically.", "204575": "By selecting \"dynamic voucher\", PDF voucher attachments will not be sent to customers.", "205215": "Are you issuing a pre-sale voucher?", "205220": "If yes, please confirm that it's on sale and stock is available. Otherwise, the pre-sale booking will not be completed.", "205242": "Sale is not available for the days you've selected. Please select again.", "205933": "Migrated to SPU?", "205935": "Bulk editing is not supported for \"Unlimited inventory\" type. You can modify it at [<span style=\"font-weight:bold;\">Rules settings</span>] module.", "205936": "Please note:<br/>Fixed-price items cannot be modified prices for a specified time range. Once modified, the price will apply to all dates.", "206119": "Create using previous process", "206120": "Use previous system", "206948": "Customer type", "206951": "Per person pricing", "206952": "Per group pricing", "206953": "Vehicle type", "206954": "Accommodation units", "206955": "SIM / Wi-Fi", "206956": "Others", "206957": "Add unit type", "206958": "Select unit types you want to add for this tour", "206959": "More unit types", "207083": "Are you sure you want to withdraw the approval request for the product {PID_Name}?", "207265": "Group", "207285": "Product:", "208151": "Only videos in {video_ratio} ratio are allowed", "208152": "HTML tag is not allowed", "208153": "Someone else is editing", "208154": "Read only", "208397": "Choose from itinerary", "208398": "This is the language you'll manage your activity in. To change it, please go to your account settings.", "208632": "Some required steps are missing. Please complete them before publishing the product.", "208639": "The maximum allowed length is {num} characters", "208641": "The product can't be published until the merchant account is approved", "208924": "A tour \"starts\" when <b>the first customer</b> joins at the first meet-up or pick-up location.<br>All times use the <b>24-hour</b> clock.", "208925": "How do you want to set up your meet-up or pick-up location?", "208926": "Set up manually", "208927": "Enter your meet-up or pick-up info manually", "208928": "Set up automatically via API", "208929": "Automatically sync your meet-up or pick-up info", "208930": "Select your API partner", "208931": "Your API partner {API_supplier} supports the syncing of <b>meet-up</b> info. <br>Please note that a maximum of 90 pick-up points can be added at this time.", "208932": "You don't need to manually enter this info.<br>Your meet-up info will be <b>automatically synced</b> once API mapping is done. Please check again later. Don't forget to set your inventory type to sync via API as well.", "208933": "API sync in progress. Please check again later. Note that you can't edit info while using API sync. <br/>If it's taking too long, try resyncing or reach out to your Klook Account Manager for help.", "208934": "Sync completed! Please check your meet-up info to ensure its accuracy. If there are any updates, you may resync your info.", "208935": "Resync info", "208936": "Enter info manually", "208937": "Copy to others", "208938": "Sync unsuccessful. Please try syncing your info again. <br/> Contact your Klook Account Manager if the issue continues.", "208939": "API sync in progress", "208940": "You are now switching modes to enter info manually. <br/> Do you want to keep what was previously synced?", "208941": "Yes, keep info", "208942": "No, clear all info", "208943": "You are now switching modes to sync info automatically via API. <br/>All previous info will be cleared. Please make sure your inventory type has been set to sync via API as well. Confirm?", "208944": "You have to unpublish your product before you can switch modes", "208945": "You can't edit info while using API sync", "208946": "Bulk upload meet-up points", "208947": "Allowed formats: xlsx, .xls", "208948": "(Download template)", "208949": "Upload successful! Please double check your info.", "208950": "Upload unsuccessful. See what went wrong and try again.", "208951": "View details", "208952": "Bulk upload unsuccessful", "208953": "Please check the following lines in your file and try again:", "208954": "Row", "208955": "Column", "208956": "Row  {row_num}", "208957": "Column {column_num}", "208958": "The upload was successful but you'll need to double check your <b>meet-up time(s)</b>", "208959": "Duplicates were found in your file. Please remove them and try again.", "208960": "Upload took too long. Please try again.", "208961": "The language in the file must be the same as the onboarding language selected for the product", "208962": "Upload in progress", "208963": "Bulk uploading all meet-up points. Please continue only after it's done.", "208964": "Uploading", "208965": "Please wait...", "208966": "Cancel upload", "209034": "This meet-up time will be copied to all other meet-up points. Confirm?", "209483": "Bulk actions", "209484": "Issue refund", "209485": "Action history", "209486": "Issue refund", "209487": "Select cancellation reason", "209488": "This reason will be shown to the customer to explain the cancellation and refund", "209489": "Uncontrollable incidents", "209490": "Booking or redemption issues", "209491": "Requested by user", "209492": "Disease outbreak or illness", "209493": "Bad weather or natural disaster", "209494": "Protests or social unrest", "209495": "Other unexpected events", "209496": "No operation or unexpected closure", "209497": "Fully booked or out of stock", "209498": "Low participation", "209499": "Other booking or redemption issues", "209500": "Customer requested the cancellation", "209501": "Please make sure you've communicated and agreed on the cancellation with the customer", "209502": "Provide evidence for cancellation", "209503": "Click to upload", "209504": "Supported formats: png, jpg, and jpeg. Up to 5 files are allowed.", "209505": "Cancellation penalty", "209506": "For cancellation due to booking or redemption issues: <br/>- If you cancel {number_of_days_1} days before the participation date, you will be penalized {num_1}% of the booking amount <br/>- If you cancel {number_of_days_2} before the participation date, you will be penalized {num_2}% of the booking amount", "209507": "Select booking", "209508": "Search bookings", "209509": "By participation date", "209510": "By activity", "209511": "By participation date and activity", "209512": "By booking reference ID", "209513": "Activity", "209514": "Search by activity name or ID", "209515": "Participation date", "209516": "Participation time", "209517": "All days", "209518": "Search bookings", "209519": "Reset", "209520": "Booking ID", "209521": "Search by booking ID", "209522": "Bulk upload booking IDs", "209523": "Supported format: .xlsx", "209524": "Download template", "209525": "Re-upload", "209526": "Switching filters will clear your current results. Continue?", "209527": "Affected bookings", "209528": "You'll be issuing a refund request for the following bookings", "209529": "Also cancel timeslot for the selected date(s), and automatically reject new bookings", "209530": "Booking reference ID", "209531": "Participation date and time", "209532": "Activity name", "209533": "Product name", "209534": "No. of participants", "209535": "Price", "209536": "Select booking", "209537": "Select cancellation reason", "209538": "Confirm cancellation", "209539": "Confirm cancellation and refund request", "209540": "{num} bookings will be canceled.", "209541": "This includes: {date}, {timeslot}. Customers will no longer be able to book these timeslots.", "209542": "Customers will subsequently receive a notification about this cancellation request", "209543": "Confirm cancellation?", "209544": "Confirm", "209545": "Back", "209546": "Action history", "209547": "Cancellation reason", "209548": "Action type", "209549": "Affected bookings", "209550": "Successfully canceled bookings", "209551": "Merchant account", "209552": "Merchant issued refund for {refund_reason}", "209553": "Merchant issued refund", "209729": "A maximum of {num} sessions is allowed", "209730": "A maximum of {num} meet-up points is allowed", "209850": "Your refund request is being processed. Please check your action history later for more details.", "210024": "Merchant name", "210025": "Merchant language", "210026": "Rule", "210027": "Country/region", "210028": "Category", "210029": "Assigned date", "210030": "Reset", "210031": "Search", "210032": "Task list", "210033": "Import", "210034": "Export", "210035": "Create tasks", "210036": "Assign tasks", "210037": "Task title", "210038": "Business impact", "210039": "Tips", "210040": "Status", "210041": "Country/region", "210042": "Task ID", "210043": "Assigned date", "210044": "You can import info and edit the \"Task title\" and \"Business impact\" fields. Only unassigned tasks can be edited.", "210045": "Unassigned", "210046": "Canceled", "210047": "Assigned", "210048": "Pending", "210049": "Completed", "210050": "Rejected", "210051": "Expired", "210052": "Archived", "210053": "Excluded", "210087": "Please add a meet-up point address or set a location name that will be visible to customers", "210304": "Please fill in the required fields", "210305": "Please select a booking", "210306": "Please provide evidence for your cancellation", "210307": "Please search for a package name or ID", "210308": "Please select an activity", "210309": "Please select a package", "210310": "Please select a booking ID", "210311": "Please select a participation date", "210312": "Export", "210328": "Last rejection reason", "210377": "Importing info", "210378": "Hold on - we're importing your file", "210379": "Imported successfully", "210380": "The \"Task title\" and \"Business impact\" fields have been updated. Please refresh this page.", "210381": "Exporting info", "210382": "You can download the file after it's been exported", "210383": "Exported successfully", "210384": "The file is now ready to be downloaded", "210385": "Cancel export", "210386": "Download", "210387": "Close", "210388": "Creating tasks", "210389": "Task creation in progress", "210390": "Successfully created", "210391": "New tasks have been successfully created and added to your task list. Please refresh this page.", "210392": "Task list updated", "210393": "Assigning tasks", "210394": "Task assignment in progress", "210395": "Tasks assigned", "210396": "Tasks assigned successfully. Please refresh this page.", "210397": "Task list updated", "210398": "Something went wrong", "210399": "The action couldn't be completed. Please try again.", "210400": "Close", "210401": "Refresh", "210471": "Create tasks", "210472": "Select rule", "210473": "Add filters", "210474": "Confirm creation of the selected tasks?", "210475": "Back", "210476": "Create", "210477": "Select all", "210478": "Assign tasks", "210479": "{X} task(s) will be assigned", "210480": "You are about to assign tasks to the merchant and notify them through the merchant portal. <br/>Confirm assignment of the selected tasks?", "210481": "Create", "210521": "Activity info", "210522": "Package", "210666": "Choose this if you already have an existing product on Klook", "210667": "Create a new product from scratch", "210670": "Unfamiliar with the product creation process? Please refer to this <a target=\"_blank\" href=\"https://docs.google.com/document/d/18vUgDWmtkz0vRAn02IU9ozWCYbTX4fT_1Lj6KXnkSJg/edit?tab=t.0#heading=h.fi4342jep14z\">user manual</a>.", "210671": "Set your tour's start time(s) and meet-up or pick-up time(s) here:<br/>- Add multiple start times if you'll have different groups starting at different times <br/>- The start times you set will be automatically copied and displayed as sessions under inventory settings  <br/>- If you use API to manage your products, please set the start time according to the API supplier's requirements", "210672": "Select if there's a special experience provided", "210769": "Tips", "210796": "Remember to click <span style=\"font-weight:bold;\">submit</span> after your adjustment", "210797": "Enter a custom name if the location name differs from the map address", "210893": "I need more help", "210894": "Got it", "211313": "Please enter an ID or name", "211314": "Please select", "211319": "Task status", "211475": "Assign all task", "211858": "Join <PERSON> as a merchant | Grow your travel business worldwide", "211859": "Join <PERSON> as a merchant and promote your tours, activities, hotels, private transfers, and more to millions of travelers across 2,700+ destinations", "211860": "\"Take rate\" refers to the markup percentage Klook applies to calculate the final selling price shown to customers. <br/>This mechanism ensures that the merchant receives the full cost price per booking.<br/>Formula: Selling Price = Cost Price ÷ (1 - Take Rate%)", "211861": "I acknowledge and agree that Klook will apply a {take_rate} markup to the cost price I provided, as the final selling price shown to customers", "211862": "Application under review", "211863": "Thanks for applying to become a Klook merchant! You'll hear from us within 7 days. <br/>In the meantime, why not start creating your first product to get ahead of the competition?", "211864": "Create now", "211865": "Why create your product now?", "211866": "Just a few simple steps to start earning", "211867": "The sooner your product goes live, the sooner you can start attracting bookings", "212410": "Get help", "213324": "Some content was auto-filled. Please double check for accuracy.", "214288": "Only part of the data was synchronized successfully. <br/>The number of collections exceeds the system's maximum limit 500, and the excess will be discarded.", "214579": "Select the languages you want to auto-publish with Loc AI", "214580": "Source language:", "214581": "Target languages:", "214582": "The selected languages will be translated with Loc AI and then auto-published once translation is done", "214583": "Manage language status", "214584": "{numbers} selected", "214585": "Unpublish", "214586": "Publish with EN_US", "214587": "Publish with Loc AI", "214588": "Language", "214589": "Translation status", "214590": "Publish status", "214591": "Source language", "214592": "Last edited", "214593": "Action", "214594": "None", "214597": "Draft", "214598": "In progress", "214599": "Unpublished", "214600": "Published", "214601": "Published with Loc AI", "214602": "Published with EN_US", "214603": "None", "214604": "Translating", "214605": "Partially human reviewed", "214606": "Maintained by BD", "214607": "Translated by AI", "214609": "Publish with Loc AI", "214610": "Publish with EN_US", "214613": "Published by loc", "214614": "Unpublish", "214627": "Unpublish with Loc AI", "214628": "\"Publish with Loc AI\" means that this language will be translated by the Translation Request Portal (TRP) (including the activity title).  <br/>\"Publish with EN_US\" can only be used for other EN locales (e.g. EN_HK). Unpublish first if the EN is already published.", "214629": "Edit in {language}", "214630": "Change source language", "214631": "Please note that {target_language} will be unpublished once the source language has been changed. Other languages will be auto-translated after {target_language} is auto-published.", "214632": "Please note that other languages that are published with Loc AI will be translated immediately based on {target_language}", "214633": "Change 3CN source language", "214635": "Click to set a title for internal use", "214637": "Max cannot exceed {num}", "214640": "Product internal reference name", "214641": "Tips: For internal reference only. The title you set here will not be shown to customers.", "214642": "The original title:", "214643": "Please input title for reference", "214648": "Internal ref name:", "214649": "What is session?", "214650": "A Session is an <span style=\"font-weight:bold;\">independent tour time slot</span> for the same itinerary on a single day, defining:<br/>✅ Start time (first passenger joining)<br/>✅ End time (last passenger departing)", "214651": "⚠️ Passenger isolation: Guests in different sessions<span style=\"font-weight:bold;\"> will not interact</span>", "214654": "Scenarios", "214655": "Same session?", "214656": "Reason", "214657": "Multiple pickup points (e.g. Point A 7:00, Point B 7:30)", "214658": "Yes", "214659": "All passengers join the same tour group", "214660": "Same itinerary with different departure times (e.g. 9:00 group vs 14:00 group)", "214661": "No", "214662": "Separate groups with no interaction", "214665": "Meet-up point list", "214679": "Add a meet-up point", "214680": "Edit meet-up point", "214681": "Flexible meet-up time", "214682": "Remove session?", "214686": "Remove session", "214687": "After a certain session is closed, the customers of that session will no longer be able to choose the corresponding departure type.", "214688": "Session", "214689": "Start & end time", "214690": "Available for meet-up", "214691": "Available for pick-up", "214692": "On", "214693": "Off", "214695": "Are you sure to delete this meet-up point?", "214697": "Flexible pick-up time", "214698": "Keep same as meet-up point(s)", "214699": "Add another drop-off point", "214700": "To customers' accomodation", "214701": "To drop-off point", "214702": "Only available for customers who choose pick-up", "214703": "If you offer both meet-up and pick-up options, we will inform customers that only who choose pick-up can be sent back to their accomodation.", "214704": "Tour ends at the last stop", "214705": "Add drop-off point", "214706": "Drop-off time", "214707": "Flexible drop-off time", "214708": "After checking this, the existing data will be cleared. Are you sure?", "214709": "Advanced settings", "214710": "This is an optional attraction with extra fee that customers can choose to visit or not.", "214711": "What’s an optional attraction?", "214712": "This is a seasonal attraction and has other alternatives based on participant dates.", "214713": "What’s a seasonal attraction?", "214714": "This attraction has sub-attraction/experiences", "214715": "What’s a sub-attraction?", "214716": "Optional attraction can not be set with other special settings (e.g. seasonal attraction, sub-attraction).", "214717": "Seasonal attraction can not be set with other special settings (e.g. optional attraction, sub-attraction).", "214718": "Sub-attraction can not be set with other special settings (e.g. seasonal attraction, optional attraction).", "214719": "No more than 2 optional attractions can be set in the same itinerary.", "214720": "Seasonal attraction cannot select this option", "214721": "Optional attraction cannot select this option", "214722": "We don't support custom name for optional attraction at this time", "214723": "You already selected 5 services. Please delete at least one to add an optional attraction.<br/>Note: Too many services to choose from may reduce customers' willingness to buy.", "214724": "Please set the dates customers will visit this attraction and the alternative sites for the rest of the time", "214725": "Will be skipped during the rest time", "214726": "The rest of time", "214727": "Add an alternative", "214728": "An optional attraction is a non-compulsory experience within a regular itinerary that requires:<br/>- <span style=\"font-weight:bold;\">Customer's active choice</span> to participate<br/>- <span style=\"font-weight:bold;\">Additional fee</span> beyond base tour price", "214730": "A seasonal attraction is an attraction designed around different seasons or time periods. It has to meet the following requirements:<br/>- <span style=\"font-weight:bold;\">Exact active dates required</span> (e.g. \"Maple Corridor: Oct 1-Nov 30\")<br/>- <span style=\"font-weight:bold;\">Must set substitute</span> for non-year-round attractions", "214731": "<span style=\"font-weight:bold;\">Important Notes:</span>", "214732": "Seasonal & substitute attractions <span style=\"font-weight:bold;\">must share base price</span>", "214733": "A sub-attraction refers to multiple relatively independent and visitable <span style=\"font-weight:bold;\">smaller attractions within a larger main attraction or scenic area</span>.<br/>This concept also applies to <span style=\"font-weight:bold;\">attractions in close proximity</span> that allow visitors to choose flexibly within a limited time frame.", "214734": "Attractions requiring transit should not be set as sub-attraction", "214735": "Type", "214736": "Key feature", "214737": "Guest behavior", "214738": "Example", "214739": "Contained sub-attraction", "214740": "Physically within main site", "214741": "Accessible only via main gate", "214742": "Yellowstone national park → Grand prismatic spring", "214743": "Clustered sub-attraction", "214744": "Separate but adjacent sites", "214745": "Free selection + shared duration", "214746": "Nami Island → Petite France/Italian Village", "214747": "Some of the attractions in your itinerary are located in different countries. Please confirm that the settings are correct.", "214748": "For reference only. The itinerary may subject to adjust due to weather, traffic, temporary closures, etc.", "214824": "Full meal", "214837": "Based on participant date:", "214838": "Included if customer choose to visit:", "214839": "Please specify the inclusion.", "214840": "Height limitation", "214842": "cm (included)", "214845": "Set which days/sessions your product can be booked.<br/>Sessions are selected and tied with what you set in <span style=\"font-weight:bold;\">Start & end</span> section.", "214846": "Add service", "214847": "Set reference name", "214848": "Add new item", "214849": "Enter a custom service option name (in en_US)", "214850": "Enter a custom service option name (in your editing language)", "214851": "Internal refer item name", "214852": "Tips: For internal reference only. The name you set here will not be shown to customers.", "214853": "The original name:", "214854": "Please input reference name", "214855": "Bulk publish/ unpublish", "214856": "Bulk publish", "214857": "Bulk unpublish", "214915": "The session you turned off is already being used. Please delete all related prices and inventories first.", "214916": "You already selected 5 services. Please delete at least one to add another departure type.", "215250": "Pick-up time cannot be earlier than the start time", "merchant_editing_try_later": "{user} is editing the activity right now. Please try editing again later.", "act_save_fail": "Failed to save", "package_min_max_age": "Min./max. age", "act_upload_arrange": "Please upload at least 1 photo (5 max.)", "taxonomy_belong_county": "Country", "taxonomy_add_sub_regions": "Select Sub-region", "global_delete": "Delete", "package_xinfo_hint_placeholder": "Please enter \"Other info\" hint", "package_unit_senior": "Senior", "package_published_overflow": "Published package number reach maximum", "activity_approve": "You will approve content of activity onboarded by merchants {AID-Name}", "global_location": "Save coordinates", "package_price_default_price_tips_3": " are complete.", "package_bulk_modification_fixed_price_tips": "This is a fixed price model, and the modification will apply to all timeslots under this SKU", "xinfo_restrict_hint": "This \"Other info\" cannot be edited due to Merchant-API restriction. Please contact the relevant team for assistance.", "act_binding_destination_scope": "Application scope", "cancel_within_48h": "48 hours", "category": "Category", "act_blocklist_system_will_notify": "Localization teams will be automatically notified to restart the onboarding process", "act_blocklist_at_least_one": "Activities must be published in at least 1 language. If you want to remove the activity, you can unpublish it.", "packge_xinfo_fields": "\"Other info\" fields", "act_binding_destination_city_request": "City request", "package_price_age_range": "Age range", "publish_lang_manage": "Edit", "act_binding_destination_create": "Create it!", "taxonomy_dialog_coordinates": "Coordinates", "package_list_archived_packages": "Archived packages", "taxonomy_dest_first_menu_title": "L1 Item Title", "act_lan_en_HK": "Hong Kong English", "manage_language_reference_lang": "Reference language", "act_binding_destination_departure_info": "Departure information", "merchant_package_contact_type_email": "Email", "taxonomy_dest_type_define": "Customized URL", "reason_for_price_changing": "Reason for price change", "package_xinfo_modify": "Modify", "act_binding_destination_country": "Country", "inv_pkg_hint": "Note: Make sure to connect to the inventory system before you publish the package/activity", "act_fields_completed_saved": "{0} required fields to be completed and saved", "timeslotlist_add": "Add new timeslots", "package_timeslot_copy": "Copy package schdedule", "reason_others": "Others", "taxonomy_dest_item_type": "Item Type", "reason_seasonal_mkt_team": "Seasonal promo-initiated by and set price agreed with MKT team", "act_activity_unpublish_package_error1": "Are you sure you want to unpublish the activity (ID: {0})? This activity is related to {1} redemption links. If you want to unpublish it, please first contact Asiamiles: <EMAIL>, <EMAIL>, Airasia Big: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>", "ob_cancel_choice7": "30 days", "cancellation_policy": "Cancellation policy", "taxonomy_dest_version_status": "Status", "act_blocklist_will_not_publish": "The system will automatically unpublish the activity in these languages (if already published)", "pm_price_calendar": "Price calendar", "package_tag_price": "Price", "package_info_name": "Package title", "group_per_booking_tip": "The customer will receive one QR code per booking", "global_selected": "Selected", "act_binding_destination_not_find_poi": "Can't find the POI you need?", "ob_48hrs": "2-days confirmation", "package_list_package_status": "Package status", "act_fold_all": "Collapse all", "taxonomy_belong_country": "Country", "other_info_validate_warn_duplicate": "Duplicate content", "package_copy_schedule_dialog_tips": "Please select the package which need to copy the schedule", "act_lan_en_CA": "Canadian English", "elevy_none": "None of the above", "taxonomy_destination_tips": "Tips:<br>1.All destinations will be displayed in country page and sorted with descfending order by the number of published of activities by default.<br>2. If you want to pin some of the cities to the top, press the Pin button. The ranking of pinned cities could be adjusted by drag and drop.<br>3. If you want to adjust the ranking of all city, press pin all and edit the ranking of them by drag and drop.", "pm_submit": "Submit", "act_lan_en_AU": "Australia English", "act_publish_to_user_account": "Display to the following account types:", "act_gps_location": "Please enter location name to search using Google Maps", "create_photo_copy": "Copy from existing photos", "act_detail_picture_tip": "To be displayed on \"How to use\" on activity page and voucher", "package_day_s": "Days", "act_list_view_online_only": "View published activities only", "fnb_use_template": "Use template to input Package Details", "package_info_add_package": "Add package", "package_timeslotlist_extend": "Auto Extend List", "act_select_to_from": "Select package(s) to copy", "auto_extend_hint": "No match found, if you want to auto extend timeslots, please go to \"Create timeslots\"", "price_engine_sub-category": "Sub-category", "global_button_ok": "Ok", "package_copy_schedule": "Copy package schedule", "pm_remark": "Remark", "taxonomy_filter_status": "Status", "package_timeslot_open_ticket": "Open ticket", "package_bulk_modification_cost_price_tips": "Only editable for the same cost price ", "act_lan_en_GB": "British English", "taxonomy_alert_content": "EN-US is the default language for creating Admin Region. Rest of the languages could be edited afterward.", "taxonomy_dialog_country_code_2": "Country Code alpha-2", "package_unit_unpublish": "Unpublish", "act_be_standardized": "Not standardized", "global_reset": "Reset", "global_yes": "Yes", "package_timeslot_repeat_none": "Do not repeat", "ob_cancel_choice9": "Free cancellation before the activity starts", "merchant_package_contact_type_whatsapp": "Whatsapp", "taxonomy_nicknames": "Nicknames", "merchant_id": "Merchant ID-Name", "global_day": "Day", "package_bulk_modification_cutoff_tips": "The cut-off time will be shared for all SKUs under this package", "act_list_pub_ref_tips": "The selected reference language is published in the activity content on the US English page. The reference content will be displayed in US English when editing.", "taxonomy_destination_ratio": "<PERSON><PERSON>", "act_tips_saving_to_next": "Save the edits to this group, complete saving all groups to proceed to next step.", "act_lan_zh_CN": "Simplified Chinese", "act_paixu_diff": "Duplicated photo sequence. Please rearrange.", "ref_lang_none": "No reference language available", "act_binding_destination_poi": "POI", "ob_voucher_choice2": "Klook voucher + Merchant code", "cancel_within_7d": "7 days", "package_price_default_price_tips_2": " and ", "link_manage_timeslot": "Bulk manage timeslot of multiple activities/packages", "package_timeslot_by_weekdays": "Weekdays", "global_week_thu": "Thursday", "package_unit_title": "Add unit & price", "ob_cancel_choice1": "No cancellation", "act_lan_account_both": "Agent & user", "other_info_email": "Email", "taxonomy_dest_add_second_level": "Create L2 Item", "package_timeslot_list": "Timeslot list", "act_save_edit_mode": "Please save your edits before continuing", "package_inventory_limited_number": "Limited number", "taxonomy_dialog_longitude": "Longitude", "package_xinfo_after_booking": "The booking date and any date after can be selected", "taxonomy_manage_placeholder": "Search with name or ID of customized region", "taxonomy_destination_languages": "Language", "package_cut_off_time": "Cut-off time", "package_info_subtitle": "Package subtitle", "other_info_only_non_standardized_selected": "Only non-standardized user info can be set in a new order:", "package_list_modify": "Edit", "activity_search": "Search name/ID/city", "taxonomy_filter_parent_region": "Parent Region", "taxonomy_filter_city_yes": "Yes", "ob_vouc_usage3": "App e-voucher", "taxonomy_dest_version_title": "Version Title", "act_group_list": "Group list", "act_list_view_range_all": "View all activities", "ob_voucher_choice1": "Klook voucher + Klook no.", "taxonomy_dest_url_valid_t": "/...", "global_view": "View", "global_week_abbr_thu": "<PERSON>hu", "package_timeslot_date_range": "Date range", "act_binding_destination_submit": "Submit", "merchant_unarchive_package": "Do you want to unarchive this package?", "pm_pick_date": "Select participation date", "taxonomy_select": "select", "taxonomy_belong_city": "City", "global_invalid": "Invalid", "global_total": "Total", "package_xinfo_before_today": "Any date before this day can be selected", "act_detail_subtitle_placeholder": "Please enter activity subtitle (45-48 characters suggested)", "remove_attr_hint": "Heads up: Deleting this attribute will delete text in other languages on the frontend", "taxonomy_dest_add_first_level": "Create L1 Item", "package_xinfo_dropdown_placeholder": "for lists, enter \"option 1,option 2,option 3\" as options (without the quote marks), the hint tells customers what to do e.g. please select", "ob_others": "Others", "activity_reject": "You will reject content of an activity onboarded by merchants. Please give a reason:", "package_archived": "Archived?", "other_info_standardized_tips_act_combo_main": "The SKU under this package is marked as \"combo\", and cannot use the standardized \"Other info\"", "package_start_date": "Start date", "act_copy_from_another": "Copy this whole page from another package", "other_info_country_region": "Country/region", "pm_modify_price": "Edit price", "pm_edit_cost": "Edit cost or selling prices", "act_lan_it_IT": "Italian", "edit_pacakge_name": "Edit package name", "taxonomy_query": "Query", "act_tips_complete_all_groups": "Please complete all the required fields to continue", "copy_to_city": "Copy to city", "language_selection_onboard": "The language you are using to onboard an activity to Klook", "mail_voucher": "Mailed voucher", "other_info_phone_number": "Phone number", "global_fixed_time": "Fixed", "taxonomy_higher_level": "Prior than city", "other_info_standardized_tips_act_combo_temporarily": "The SKU under this package is the standalone SKU to combo package {0} of activity {1}, and cannot use the standardized \"Other info\"", "package_existing_take_rate": "Existing TR：", "manage_language_status": "Manage language status", "act_list_is_on_blocklist": "This language is on the blocklist. Go to the onboarding system to edit settings and re-trigger the onboarding workflow.", "merchant_setting_language": "Language settings", "global_published": "Published", "package_xinfo_date": "Date", "act_search_keywords": "Search keywords", "package_calendar_take_rate_tips": "The original selling price was calculated automatically with a {0} Minimum Expected Take Rate after the merchant's cost submission. Please review and maintain the selling price.", "act_transferable_no": "No", "global_unpublished": "Unpublished", "package_bulk_modification_save_tips": "Please select items first", "fnb_package_management_tip": "Please follow the current package creation process to create a F&B package", "package_xinfo_mandatory": "Mandatory", "other_info_standardized_tips_act_identity": "The \"Other info\" is not applicable for activity with \"Other info\": <br />140555,140556,140557,140558,140559,140560 as there are validation specifically set up for these certificate \"Other info\", not until required adaptation is in place.", "change_price_system_err": "System error, please try again", "note": "Note", "display_on_banner": "Add image to banner", "pm_operator": "Operator", "merchant_withdraw_package": "Do you want to withdraw of approval request for this package {PID-Name}?", "cancel_within_30d": "30 days", "taxonomy_dest_edit": "Edit", "global_please_choose": "Please choose", "global_week_abbr_tue": "<PERSON><PERSON>", "act_english_content": "English content", "manage_timeslot_placeholder": "Select the time range to delete", "act_lan_jp": "Japanese", "delete_free_text_disabled": "Content only can be deleted from en_US, but you can edit content in other languages", "ob_ventory": "Inventory", "act_binding_destination_city_name": "City name", "global_select": "Please choose", "package_bulk_modification_edit_cutoff": "Edit cut off time", "package_unit_publish_warning": "Do you want to {0} this unit?", "act_binding_destination_departure_country_or_region": "Departure country/region", "package_take_overview_hint": "If you have finished creating packages and have no add-on packages", "package_xinfo_enter_hint": "Please enter \"Other info\" hint", "common_field_pick_up_detail": "Pick-up details", "package_unit_drag_main_sku_warning": "The first unit must be published", "taxonomy_create_location_name": "Name", "status.unarchived": "Unarchived", "global_button_cancel": "Cancel", "act_lan_all": "All", "package_unit_youth": "Youth", "act_lan_ja_JP": "Japanese", "taxonomy_dest_version_publish": "Publish", "other_info_user_info_non_standardized": "Add user info (Non-standardized)", "global_updated": "Updated", "change_price_incorrect": "The price value is incorrect, please re-enter", "other_info_standardized": "User input info (Standardized)", "package_tag_info": "Package info", "merchant_none": "--", "taxonomy_destination_applications": "Applications", "act_lan_th": "Thai", "package_bulk_modification_delete_warning": "Do you want to delete the selected items?", "taxonomy_filter": "Filter", "taxonomy_dialog_status": "Status", "act_to": "To", "act_enter_no_only": "No. only", "merchant_approved": "Approved", "merchant_self_onboard_entry": "Activity management", "package_unit_type": "Unit type", "ob_instant": "Instant confirmation", "merchant_submit_package": "Please make sure the activity will be submitted together with the package:", "pm_selling_price": "Selling price", "package_inventory_unlimited": "Unlimited", "act_change_template_hint": "Once you saved the template can't be changed, please select carefully", "fnb_package_management_tip2": "You cannot revisit this page after you save. Are you sure you want to save?", "act_enter_to_location": "Press enter to generate", "cancel_before_redeem": "Free cancellation before redemption", "js_post_success": "Success", "package_unit_delete_warning": "Do you want to delete this unit?", "act_lan_vi_VN": "Vietnamese", "act_lan_en_BS": "International English", "act_leave_tips": "If you leave now, you'll lose any unsaved changes", "spu_xinfo_hint": "Notice: It’s only for WiFi activity, and can only create for once. You can create the return location as the \"Other info\". Once you select , the user will select the return location on the activity page instead of payment page.", "package_until": "Until", "package_bulk_modification_title": "Bulk modification", "pm_parti_date": "Participation time", "act_lan_vn": "Viet", "act_content_icons": "Package details", "package_unit_tips": "Make sure to translate any customized unit names if you add new units after the activity is published", "pm_update_default_setting": "Edit default settings", "fnb_package_management_tip3": "The template is only for newly created package and the activity must finish Activity Standardization first.", "package_xinfo_time": "Time", "act_activity_mark_confirm_type": "Confirmation type", "publish_unpublish_other_reason": "Other reasons", "taxonomy_dest_version_status_publish": "Published", "pm_fixed_price": "Fixed price", "ob_api": "API", "taxonomy_destination_destination": "Destination", "global_draft": "Draft", "package_xinfo_checkbox": "Tick Box", "act_package_package_subtitle_hint": "Enter package subtitle", "taxonomy_dest_order": "Order", "pm_special_selling_price": "Special selling price", "error_repeat_timeslot": "Timeslots / Auto extend schedule rule conflicts with existing time slots, please create again / Reset", "merchant_package_contact": "Contact method for package", "global_no": "No", "pacage_another_main_hint": "If you have more than 1 package option", "act_list_view_range_responsible": "View activities I'm responsible for", "act_detail_desc_placeholder": "Please enter short description (1-2 sentences suggested)", "act_binding_destination_geographic_level": "Application level", "auto_publish_unpublish_tips": "Auto publish and unpublish can be turned on independently or consistently. Auto publish cannot be cut off 30 minutes before the set-up time.", "package_xinfo_sort_hint": "\"Other info\" selected/created (You can change the displayed order)", "reason_violation_of_selling_price": "Violation of fixed selling price agreed with merchant", "next_step": "Continue", "package_list_delete_package_tips": "Are you sure to delete the package? This action can't be undone.", "cancel_within_14d": "14 days", "global_week_abbr_fri": "<PERSON><PERSON>", "package_schedule_14_advance": "14 days in advance", "act_pkg_copy_error": "Copy command fails. The currency of two merchants does not match.", "global_completed": "Completed", "btn.createPkg": "Create new package", "package_xinfo_mandatory_hint": "Is this \"Other info\" mandatory?", "global_status": "Status", "common.pieces": "Pieces", "timeslotlist_all_timeslots": "All timeslots ({0})", "ob_vouc_usage4": "App e-voucher + offline voucher", "actCreate.modalTitle": "Choose category", "package_unit_child": "Child", "back_to_timeslotlist": "Go back to timeslot list", "global_confirm_delete": "Are you sure you want to delete?", "ob_vouc_usage5": "Printed voucher + mobile voucher + offline voucher", "act_lan_both": "All", "taxonomy_dialog_nicknames": "Nicknames", "taxonomy_dest_version_status_down": "Unpublished", "english": "English", "act_map": "Map", "act_create_activity": "Create activity", "simplified_chinese": "Simplified Chinese", "global_dialog_title": "Note", "global_save": "Save", "global_week_sun": "Sunday", "other_info_contact": "Contact info", "other_info_all_selected_displayed": "All selected user info:", "act_lan_en_NZ": "New Zealand English", "act_lan_es_ES": "Spanish", "act_activity_mark_can_confirm": "Activity self-confirmation status", "klk_voucher_merchant_code": "Klook voucher + merchant code", "package_unit_age_comparison": "Max. age should be greater than min. age", "link_bulk_update_responsible_BDs": "Bulk update responsible BDs", "global_images": "Images", "package_tag_extra_info": "Other info", "duplicate_attr_hint": "Heads up: The copy function applies to all languages. Please send a CER if there is any free text.", "package_displayed_month_number": "No. of months displayed in the calendar", "taxonomy_synonyms": "Synonyms", "act_blocklist_publish_conditions": "Publish conditions", "pm_search": "Search", "package_info_limited": "<PERSON>.", "cancel_within_24h": "24 hours", "taxonomy_add": "Select", "other_info_less_likely_warn_2": "Users are less likely to book if they need to enter more info than is needed. Please only select what's necessary.", "global_button_save": "Save", "act_tips_check_same_category": "Check the activities associated with the same category flow", "package_price_unit_name": "Unit name", "taxonomy_filter_unavailable": "Unavailable", "act_activity_unpublish_package_error3": "Are you sure you want to unpublish the activity (ID: {0})? This activity is related to {1} redemption links and connected with API. If you want to unpublish it, please first contact Asiamiles & API: <EMAIL>, <EMAIL>, Airasia Big: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>", "add_free_text_disabled": "New content only can be added from the source language (en_US)", "package_xinfo_enter__name": "Please enter any additional information ", "other_info_standardized_tips_act_multi": "The \"Other info\" is not applicable because:", "package_unit_main": "Main SKU", "act_lan_en_PH": "Philippine English", "package_error_mandatory": "Please complete all required fields", "act_copy_package": "Heads up: When the package is copied, the freetext standardized fields in other languages will be deleted", "common_field_mailing_information": "Mailing info", "package_default_price_retail": "Default Retail Price", "merchant_archive_package": "Do you want to archive this package?", "voucher_usage": "Voucher usage", "taxonomy_list_actions": "Actions", "taxonomy_poi_manage_placeholder": "Search with name or ID of key location", "package_list_modify_unit": "Edit unit", "i18n_custom_photo_hint": "This activity includes language-specific photos and cannot be translated. The activity status won't be set as “preview” after submission. Please contact EM to coordinate.", "act_lan_user_only": "User only", "taxonomy_dest_version_cancel_publish_cfm": "Are you sure to cancel the schedule of this version?", "upload_images": "Please upload image", "package_bulk_modification_inventory_tips": "The inventory quantity will be shared for all SKUs under this package", "create_banner_photo_for_lang": "Upload banner photo for this language only", "ob_cancel_choice6": "14 days", "package_tag_package_details": "Package details", "package_info_reconfirmation_hint": "Reconfirmation is required from the operator 24-48 hours before the activity starts", "package_schedule_end_date": "End date", "act_lan_nz": "New Zealand English", "keywords_search": "Keywords search", "reset_search": "Reset", "global_unfilled": "Unfilled", "cancel_with_conditions": "Conditional*", "package_info_location_instructions": "Upload package location instructions", "taxonomy_destination_ranking": "Ranking", "package_tag_schedule": "Create timeslot", "activity_required_fields": "Please complete all the required fields for the activity", "taxonomy_destination_seo_url": "SEO URL", "package_unit_other": "Other", "package_list_modify_pkg": "Edit package", "package_schedule_today": "Today", "manage_language": "Manage languages", "global_week_tue": "Tuesday", "package_timeslot": "Timeslot", "package_timeslot_repeat": "Repeat", "manage_timeslot_update": "Update", "taxonomy_dialog_title": "EN-US is the default language for creating Admin Region.", "package_timeslot_by_day": "By day", "package_no_activity": "Error: No Activity ID", "Merchant": "Merchant", "taxonomy_sub_regions": "Sub-regions", "package_unit_selector": "Unit:", "package_unit_age_integer": "Whole number required", "package_schedule_1_advance": "1 day in advance", "act_activity_mark_on_tip": "Are you sure you want to turn on?", "activity_special_inventory_edit_tips": "You may check {0} if Special Inventory is set but activity is sold out", "delete_free_text_hint": "Heads up: Deleting content from the source language (en_US) will delete content in other languages on the front end", "global_week_abbr_mon": "Mon", "package_price_units": "Units", "package_displayed_month_tips": "Each package on the activity page displays a 6-month calendar by default for the user to select dates. If the package has special requirements, the calendar can be extended by up to 12 months.", "act_image_en_us_handle": "You can add/delete images in en_US or the sole language published.\n\nAdding/deleting images applies to all published languages.", "form.stkTextarea": "Feel free to leave any special notes for Klook BDs:", "pm_reminder": "Attention", "act_binding_destination_destination_info": "Destination information", "unpublish_title": "You are about to unpublish activity {0}, please give a reason:", "pm_default_setting": "Default settings", "package_list_unit_unpublished": "Unpublished", "act_copy_from_another_pkg": "Copy from another package", "pm_change_merchant_warn": "Heads up: All SKUs under this package are only configured with cost and selling prices with default settings. The amount may not match the actual prices. Please confirm whether the cost price and selling price amount of each date or time are correct before publishing the package.", "package_price_confirm_hint": "Please enter your the unit name carefully. Once confirmed it cannot be changed.", "publish_title": "You are about to re-publish activity {0}, please give a reason:", "act_blocklist_clear_all": "Clear all", "stkModal.sureActivityWithPackage": "Please make sure the activity will be submitted together with the package{PID-Name}:", "act_view_what_selected": "View selected", "act_tips_saving_all_mandatory": "You've completed and saved all required fields", "act_binding_destination_departure_city": "Departure city", "act_binding_destination_destination_country_or_region": "Destination country/region", "package_unit_num_comparison": "Max. no. should be greater than min no.", "act_current_language_detail_no_data": "Ref. language has no data yet", "pricelist_activity_status": "Activity status", "package_open_ticket_hint": "Users can redeem on any date, regardless of the date selected at booking", "approve_events_log": "Events log", "btn.okay": "OK", "pm_pkg_status": "Status", "Go_To_AM_Activity_List": "Go to AM activity list", "choose_city": "Select city", "taxonomy_filter_admin_region": "Admin Region", "pkg.followingInfomation": "If the Activity contains following information", "approval_activity": "Approve activity", "act_lan_id": "Bahasa Indonesia", "package_timeslot_another": "Create other timeslots", "after_status": "After", "ob_no_voucher": "No voucher", "taxonomy_destination_publish_or_unpublish": "Publish / Unpublish", "act_lan_th_TH": "Thai", "package_schedule_30_advance": "30 days in advance", "klk_voucher_klk_code": "Klook voucher + Klook no.", "package_another_addon_hint": "If you want to create another add-on package", "taxonomy_type": "type", "global_add": "Add", "common_field_personal_measurement": "Personal measurements", "pm_pkg_id": "Package ID", "pm_effective_time": "Effective time", "act_lan_cn": "Simplified Chinese", "package_this_is_open_ticket": "This is an open ticket", "confirmation_time": "Confirmation time", "package_timeslot_available": "Created timeslots", "taxonomy_dest_area_country": "Country Page", "act_activity_unpublish_sku_error3": "Are you sure you want to unpublish the unit (ID: {0})? This unit is related to {1} redemption codes and it is connected with API. If you want to unpublish it, please first contact Asiamiles & API: <EMAIL> & <EMAIL>, Airasia Big: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>", "no_cancel": "No cancellation", "taxonomy_dialog_currency": "<PERSON><PERSON><PERSON><PERSON>", "taxonomy_dest_choose_lang_tip": "Language could not be changed after version saved, please confirm the language you selected before saving.", "act_binding_destination_to_request": "to submit a request to admin", "package_timeslot_repeat_daily": "Repeat daily", "package_timeslot_start_time": "Start time", "auto_publish_unpublish_tips_3": "Auto publish needs to be set up at least 30 minutes before the set-up time", "global_success": "Success", "taxonomy_filter_all": "All", "revise_price_published_merchant": "If you wanna changed the merchant, please unpublished the package first.", "taxonomy_poi_name": "Name", "package_info_reconfirmation": "Reconfirmation", "package_schedule_autoextend_hint": "Daily auto extend one-day timeslot from the last timeslot which is existing according to the above schedule rule. And the system will start auto extend today", "package_bulk_modification_edit_warning": "Can only be edited for the main SKU: {SKU name}", "other_info_last_name": "Last name", "block_out_date": "Block out date in auto extend", "act_activity_detail_change_tip": "Are you sure you want to make this change?", "act_binding_destination_city1": "City", "package_price_add_unit_error": "You must finish editing and save the original unit before you can create a new one", "act_binding_destination_click_me": "Click here", "pm_timeslot_operator_warn": "If the timeslot is new to this package, please set the price. \n\nIf the new time period matches a previously deleted one from the package, the system will use the historic price. Make sure to confirm the price before continuing.", "timeslotlist_date_range": "Date range", "package_price_new_unit": "New unit", "taxonomy_dest_version_search": "Search", "package_inventory": "Inventory", "act_lan_gb": "British English", "package_click_here_to_change": "Click here to edit the timeslots that you have created", "global_expired": "Expired", "package_please_choose": "Please select a package", "act_goto_price_page": "Go to package - price page", "package_tag_fnb_content": "Package content", "pm_cost": "Cost price", "act_menchant_voucher": "Merchant voucher", "first_sku_id_no_delete": "The first unit can't be unpublished. Please change the sequence first.", "merchant_withdraw_action": "Withdraw approval request", "global_sold_out": "Sold out", "package_list_status": "Status", "previous_step": "Go back", "reason_seasonal_merchant": "Seasonal promo-funded by merchant", "act_activity_unpublish_sku_error1": "Are you sure you want to unpublish the activity (ID: {0})? This unit is related to {1} redemption codes. If you want to unpublish it, please first contact Asiamiles: <EMAIL>, <EMAIL>, Airasia Big: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>", "act_binding_destination_cancel": "Cancel", "package_special_selling_price": "Special selling price per currency", "merchant_package_contact_type_facebook": "Facebook", "taxonomy_ferry": "Ferry", "act_lan_fr_FR": "French", "global_error": "Something isn't right", "group_per_unit_type_tip": "The customer will receive a QR code per each unit type. For example, 2 adults will share one QR code and 2 children will share another QR code", "act_activity_mark_off_tip": "Are you sure you want to turn off?", "pm_pkg_name": "Package name", "package_schedule_16_advance": "16 days in advance", "taxonomy_filter_admin_region_placeholder": "Search with Admin Region or ID of key location", "reject": "Reject", "copy_package": "Copy content to other packages", "package_ask_for_each_participants": "Ask for every participant", "act_activity_unpublish_activity_error1": "Are you sure you want to unpublish the activity (ID: {0})? This activity is related to {1} redemption links. If you want to unpublish it, please first contact Asiamiles: <EMAIL>, <EMAIL>, Airasia Big: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>", "taxonomy_destination_publish_act_num": "# of Activities", "package_info_description_placeholder": "Enter package description", "common_field_car_transfer_detail": "Car transfer details", "reason_merchant_changed_net_rate": "Merchant changed net rate", "global_language": "Languages", "act_skip_displaying_pkg_price": "Skip displaying the price of the package on activity card/page", "taxonomy_destination_page_id": "Page ID", "package_xinfo_finish_hint": "There's still unfinished main packages.", "package_bulk_modification_edit_price": "Edit price", "fnb_use_template_disabled": "The template is only for activities that has finished Activity Standardization. Please finish Activity Standardization first.", "global_week_mon": "Monday", "no_lang_act_publish_tips": "Please publish activity in at least 1 language", "package_unit_age_number": "Age limitations", "act_basic_change_merchant": "Please go to the Price page change the cost price of all units in all packages, otherwise will influence calculate of price and settlement with merchant.", "taxonomy_dest_delete": "Delete", "global_week_wed": "Wednesday", "act_klook_voucher": "Klook voucher", "act_excluding_copied_fields": "Exclude copied fields", "stock_out_type": "Inventory type", "status.archived": "Archived", "actCreate.subLeafCategory": "Sub-category & Leaf-category", "package_price_default_price_tips_1": "The calendar is visible only when the ", "auto_publish_unpublish": "Auto publish/unpublish", "package_xinfo_custom_fields": "Add customized user info (Non-standardized)", "package_price_retail": "Retail Price", "pm_cost_history": "Cost price history", "pm_reason": "Remark", "package_schedule_10_advance": "10 days in advance", "act_pkg_info_copy_tips": "Published packages selected, only content will be applied only when copying to a published package. If you need to copy all settings and content, please unpublish the package first.", "timeslotlist_delete_all": "Delete all search results", "js_confirm_verify": "Verification required?", "dupliate_attr_disabled": "Attributes only can be copied from en_US", "merchant_withdraw_try_later": "{user} is editing the activity right now. Please withdraw the approval request later.", "add_free_text_hint": "To add free text to the activity, please submit a request to edit content to the Product Content team", "ob_cancel_choice5": "7 days", "other_info_first_name": "First name", "act_lan_us": "English", "act_lan_in": "Indian  English", "taxonomy_confirm_key_location": "The default language for key location is en-US.", "act_photo_desc_hint": "Enter photo description", "global_export": "Export", "copy_standardize_pkg_hint": "Heads up: Copy function can only be used in the source language (en_US). Once used, all the standardized data of this package will be updated and any existing freetext will be deleted in other languages.", "remove_attr_disabled": "Attributes only can be deleted from en_US", "act_binding_destination_country_or_region": "Country/region", "package_price_default_setting_warning": "Please be noted that the default price will be used for auto-extend timeslots.", "package_xinfo_name_placeholder": "Please enter \"Other info\" name", "pm_price_model": "Price model", "pm_create_price_calendar": "Generate price calendar", "pm_batch_edit_calendar": "Bulk update prices", "package_price_modify_search": "Search", "reason_selling_price_above_retail_price": "Selling price above retail price", "global_modify": "Edit", "global_week_abbr_sun": "Sun", "act_lan_ru_RU": "Russian", "act_pkg_copy_info_dialog_title": "Which package do you want to copy information from?", "Go_To_AM_Package_List": "Go to AM package list", "act_blocklist_language_availability": "Language availability", "act_recent_used": "Recently used", "evoucher_offline_voucher": "App e-voucher + offline voucher", "act_from": "From", "package_unit_adult": "Adult", "act_enter_duration_range": "Range", "act_blocklist_select_all": "Select all", "package_inventory_limited_per_day": "Limited per day", "package_schedule_32_advance": "32 days in advance", "timeslotlist_edit_selected": "Edit selected", "act_detail_title_placeholder": "Please enter activity title (30-33 characters suggested)", "act.is.editing": "This activity was recently edited. Please try again later.", "package_timeslot_by_weekly": "Weekly", "reason_msp_regulation": "New MSP regulation", "act_ref_language": "Ref. language", "manage_publish_language": "Manage languages", "manage": "Manage", "act_voucher_type": "Voucher type", "act_transferable_yes": "Yes", "act_transferable_tip1": "Please specify whether the activity's voucher is transferable to another user", "btn.withdrawSubmit": "Withdraw approval request", "act_list_cancel_add": "Don't add", "ob_24hrs": "1-day confirmation", "act_blocklist_not_publish": "Please confirm that you do not want the activity published in these languages:", "global_week_sat": "Saturday", "merchant_self_confirm_turn_on_btn_tips": "To turn on, the Merchant self-confirmation needs to be turned on first", "global_package": "Package", "package_schedule_5_advance": "5 days in advance", "ob_e_voucher": "App e-voucher", "create_photo_for_lang": "Upload photos for this language only", "no_voucher": "No voucher", "act_merchant": "Merchant", "taxonomy_dest_item_image": "Image", "common_field_wifi_and_sim": "WiFi & SIM Cards", "package_list_unit_published": "Published", "copy_to_country": "Copy to country/region", "package_xinfo_before_booking": "Any date before the booking date can be selected", "act_list_permission_denied": "Permission denied", "merchant_rejected": "Rejected", "taxonomy_destination_video": "Video", "taxonomy_destination_old_images_tips": "The current destination page is dopting the images uploaded before destination page mgmt module launch. Click Upload Image to upload, crop or refresh images.", "package_info_name_placeholder": "Enter package title", "act_lan_sg": "Singapore English", "taxonomy_dest_second_menu_title": "L2 Item Title", "global_cancel": "Cancel", "act_lan_en_US": "*American English", "act_activity_mark_no_confirm": "No confirmation types selected", "activity_special_inventory": "Special inventory", "modify_time_bj": "Timestamp", "global_week_abbr_wed": "Wed", "global_place_id": "Save Google Place ID", "taxonomy_destination_status": "Status", "timeslotlist_back_to_packagelist": "Go back to package list", "package_timeslot_subtitle": "Timeslot & Inventory", "act_lan_id_ID": "Indonesian", "act_lan_en_IN": "Indian  English", "act_min_size": "Min. size", "ob_cancel_choice8": "Conditional*", "taxonomy_destination_range": "Range", "package_xinfo_hint": "If you cannot find the field you are looking for, scroll down to create a custom field", "package_create_timeslot_hint": "You can create one or multiple timeslots", "package_unit_name_tips": "If you've added new units after the activity is published, make sure to send a Content Edit Request ticket to translate the customized unit name", "Go_To_Content_Activity_List": "Go to EM activity list", "taxonomy_dest_version_status_release": "Scheduled", "package_another_unit": "Add new unit", "package_bulk_modification_disable_tips": "Please add Inventory&Price first", "delete_timeslot_msg": "Bulk delete successful. Processed package ID:", "btn.more": "...", "act_list_search_placeholder": "Activity title/ID/keywords", "taxonomy_filter_city_no": "No", "taxonomy_region_id": "Region ID", "create_time_bj": "Created time (UTC+8)", "edit_package_name": "Edit package name", "ob_cancel_choice2": "24 hours", "merchant_withdraw_activity": "Do you want to withdraw of approval request for this activity {AID-Name}?", "global_bts_table_formatNoMatches": "No match is found", "pm_change_pm": "Switch price model", "act_pkg_copy_price": "Copy package price", "act_modify_restrict_hint": "Some \"Other info\" may be locked because of Agent API mapping. Please contact GDS and apply to unlock: <EMAIL>", "merchant_self_confirm": "Merchant self-confirmation", "merchant_package_contact_type_line": "Line", "merchant_confirm_type": "Voucher type", "actCreate.leafCategory": "Leaf-category", "act_copy_direction": "Copy to/from", "act_package_package_name_hint": "Enter package title", "act_activity_unpublish_package_error2": "Are you sure you want to unpublish the activity (ID: {0})? This activity is connected to API. If you want to unpublish it, please first contact <NAME_EMAIL>, <EMAIL>", "act_days": "Day(s)", "taxonomy_airport": "Airport", "taxonomy_bus_station": "Bus Station", "taxonomy_dest_version_publish_cfm": "Are you sure to publish this version immediately?", "act_create_en_hint": "Please select American English as base English language", "act_photo_download_original_image": "Download original image", "navname_content_cnrail_crosssell": "China trains cross sell", "act_minutes": "Minute(s)", "taxonomy_dest_confirm": "Confirm", "stkModal.choosePackagesWithActivity": "Select a package to submit together with activity {AID-Name}: ", "revise_price_new_cur": "New merchant currency", "ob_voucher_retr_method": "Voucher retrieval method", "actNav.sellingInfo": "Price & inventory", "act_copy_to_other_pkg": "Copy to other packages", "act_list_cancel_edit": "Cancel edit", "common.perPage": "Per page", "global_usable": "Usable", "other_info_required": "Info required:", "package_unit_limited": "Limited", "act_lan_kr": "Korean", "package_enter_unit_name": "Please enter unit name", "package_language_none": "None", "ob_cancel_choice10": "Free cancellation before redemption", "other_info_all_participant": "Info from all participants required", "other_info_phased_out_eventually": "Please try to use the above standardized fields as the non-standardized fields will be phased out", "package_timeslot_selector": "Timeslot:", "act_edit_photo": "Edit photos", "group_per_unit_tip": "The customer will receive one QR code per unit. For example, 2 adults and 2 children will get 4 unique QR codes", "taxonomy_dialog_parent_region": "Parent Region", "keyword": "Key Word", "package_price_save_tips": "The activity that contains this current package has been published at least once. If the current package information are saved, costs and selling price could not be edited with the current interface. The only place for editing costs and prices is “package list > edit price”. Are you sure to save all information of current package?", "package_open_ticket": "Open ticket", "ob_cancel_choice3": "48 hours", "act_detail_picture": "Picture", "delete_auto_extend": "The system will no longer auto extend timeslots", "package_info_location_size": "Size: 1920 * 720", "package_edit_price": "Edit price", "custom_xinfo": "\"Other info\" field", "taxonomy_dest_version_title_tip": "This title is only for searching, not for front-end display.", "act_transferable_tip2": "The voucher cannot be transferred if it includes the user's name and/or requires ID verification to redeem", "act_photo_add_banner": "The amount of activity banner displayed on frontend has changed and you might want to recrop (Banner size on desktop will change from 1160*460 to 960*460)", "package_bulk_modification_edit_inventory": "Edit inventory", "package_overview_hint": "Please check the information below", "pm_cancel": "Cancel", "timeslotlist_extend": "Auto extend list", "merchant_submit_activity": "Select a package to submit together with activity:", "global_valid": "<PERSON><PERSON>", "package_schedule_6_advance": "6 days in advance", "taxonomy_destination_images": "Images", "global_button_detail": "Detail", "package_info_location_hint": "This will be displayed on the voucher to show the location (png, jpg, jpeg; max. size 4MB)", "package_unlimited": "Unlimited", "global_reupload": "Reupload", "global_submit": "Submit", "package_unit_main_sku_tips": "The 1st published unit will be set as main SKU by default. Timeslots and inventory can only be modified for Main SKU and shared by other SKUs under this package.", "manage_language_modify_detail": "Last edited:", "taxonomy_dialog_nicknames_placeholder": "Keywords could be segmented by inputting commas.", "act_save_success": "Successfully saved", "type_to_search": "Type to search", "package_price_data_error": "Data error", "common_field_language_and_others": "Language & other preferences", "package_timeslot_all_day": "All day", "act_blocklist_please_confirm": "Please confirm that you want the activity published in these languages:", "other_info_standardized_tips_act_api": "The \"Other info\" listed below are currently not applicable for API integrated activities until required adaptation is completed", "pm_list_view": "List view", "taxonomy_filter_city": "City", "taxonomy_dest_version_operation": "Operation", "taxonomy_destination_currency": "currency", "package_count": "Package quantity", "package_bulk_modification_edit_selling_price": "Edit selling price", "package_min_s": "Minute(s)", "global_confirm": "Confirm", "package_minimum_take_rate": "Minimum TR among all Future Timeslots：", "act_lan_en_MY": "Malaysian English", "act_activity_unpublish_activity_error2": "Are you sure you want to unpublish the activity (ID: {0})? This activity is connected to API. If you want to unpublish it, please first contact <NAME_EMAIL>, <EMAIL>", "package_copy_schedule_tips": "The copy schedule function is for packages without an available schedule. If you are using auto extend, please make sure block out the date.", "act_guarantee_for_package_2": "Please confirm whether the merchant has provided the escrow/bank guarantee for any Taiwan local packages", "act_list_view_range_only": "View activities I created", "act_activity_unpublish_activity_error3": "Are you sure you want to unpublish the activity (ID: {0})? This activity is related to {1} redemption links and connected with API. If you want to unpublish it, please first contact Asiamiles & API: <EMAIL>, <EMAIL>, Airasia Big: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>", "pm_market_price": "Retail price", "pm_reset": "Reset", "ob_cancel_choice4": "72 hours", "other_info_less_likely_warn": "Users are less likely to book if they need to enter more info than is needed. Please only select what's necessary.", "package_schedule_2_advance": "2 days in advance", "taxonomy_nature": "Nature & Parks", "package_unit_name": "Unit name", "act_recommended_ratio": "Recommended ratio", "auto_publish_unpublish_tips_2": "Auto publish cannot be cut off 30 minutes before to the set-up time", "taxonomy_destination_unpublished": "Unpublished", "package_unit_publish": "Publish", "act_lan_hk": "Traditional Chinese(HK)", "act_photo_less_banner": "The amount of activity banner displayed on frontend has changed and you might want to recrop. (Banner size on desktop will change from 960*460 to 1160*460)", "basic.activityPhoto": "Activity main photo", "package_timeslot_by_timeslot": "By timeslot", "act_lan_de_DE": "German", "global_field_need_compress": "Compress?", "taxonomy_dest_third_level_title": "L3 Item Title", "package_list_unarchive": "Unarchive", "content_name": "Name", "manage_language_manage_language": "Edit languages", "act_if_bookable": "Bookable to agent/user?", "ob_m_voucher": "Mailed voucher", "taxonomy_dest_version_status_draft": "Draft", "package_unit_person": "Person", "delete_timeslot_error": "No eligible packages found", "pkg_restrict_hint": "You'll need to unpublish this package before editing. Do you want to unpublish?", "taxonomy_status": "Status", "taxonomy_dest_edit_lang": "Language", "global_week_fri": "Friday", "act_binding_destination_continent": "Continent", "package_approve": "You will approve content and cost of package onboarded by merchants {PID-Name}", "package_info_description_desc": "Package description is in testing phase and only visible to some of the CN and TW attractions activities pages. Word limitation is 50 including punctuation marks and space.", "taxonomy_primary_sub_regions": "Primary Sub-region", "approve": "Approve", "package_xinfo_dropdown_list": "Dropdown list", "Go_To_Content_Package_List": "Go to EM package list", "basic_information": "Basic info", "ob_vouc_usage2": "Printed voucher only", "taxonomy_destination_info": "Destination Info", "global_read": "view", "common_field_hotel_information": "Hotel details", "merchant_submit_activity_required": "Select at least 1 package to submit together with activity", "global_failure": "Failure", "package_price_schedule_delete_tips": "Do you want to delete this schedule rule?", "package_xinfo_enter_name": "Please enter \"Other info\" name", "merchant_package_contact_type_phone": "Phone", "taxonomy_dialog_city": "City", "pm_cost_price": "Cost price", "taxonomy_dining_room": "Restaurants", "global_button_search": "Search", "act_activity_unpublish_sku_error2": "Are you sure you want to unpublish the unit (ID: {0})? The unit is connected with API. If you want to unpublish it, please first contact GDS Team: <EMAIL>, <EMAIL>)", "voucher_level": "Voucher level", "taxonomy_dialog_country_code_3": "Country Code alpha-3", "taxonomy_created_by": "Created by", "unfinish_field": "Unfinish Field", "package_timeslot_repeat_weekly": "Repeat weekly", "taxonomy_theme_park": "Theme parks", "package_info_subtitle_placeholder": "Enter package subtitle", "revise_price_hint": "Please make sure that the merchant currency and the cost currency are the same and re-input the cost price", "api_pkg_hint": "Note: Make sure to apply for API mapping before you publish the package/activity", "act_binding_destination_not_find": "Can't find the city you need?", "global_button_all": "All", "package_info_enter_desc": "Enter package subtitle in ", "first_publish_date": "1st publish date", "common_field_personal_information": "Personal info", "taxonomy_reset": "Reset", "taxonomy_dialog_Search": "Search", "taxonomy_hotel": "Hotels", "taxonomy_dest_version_read": "Read", "package_schedule_7_advance": "7 days in advance", "taxonomy_filter_available": "Available", "package_unit_infant": "Infant", "global_create_success": "Created Success", "package_xinfo_after_today": "Any date after and including this day can be selected", "package_price_modify_reset": "Reset", "act_upload_a_lots": "You can upload up to 5 photos", "package_hour_s": "Hour(s)", "timslotlist_timeslot_chosen": "{0} timeslots selected", "instant_confirm": "Instant confirmation", "package_schedule_autoextend": "Auto extend", "package_schedule_4_advance": "4 days in advance", "act_unfold_all": "Show all", "package_xinfo_common_field": "Other user info (Non-standardized)", "act_binding_destination_of_act": "Number of activities", "taxonomy_destination_coordinates": "Coordinates", "taxonomy_destination_single": "Single", "global_operation": "Operation", "other_info_1_participant": "Info from 1 participant required", "package_xinfo_ismadatory": "\"Other info\" is required", "taxonomy_filter_parent": "Parent", "taxonomy_destination_base_time_to_visit": "Best Time to Visit", "taxonomy_destination_seo_keywords": "SEO Keywords", "other_info_confirm_del": "Are you sure you want to remove?", "package_price_china_agent_rate": "China Agent Price Markup % Over Cost", "pm_change_pm_warn": "Switching price model will remove all existing price data points and these data points cannot be recovered. Are you sure to continue?", "merchant_pending": "Pending", "taxonomy_dest_version_appointment_confirm": "Are you sure to schedule the publishment of this version?", "merchant_voucher": "Merchant voucher", "act_hours": "Hour(s)", "taxonomy_destination_operation": "Operation", "global_preview": "Preview", "pm_calendar_view": "Calendar view", "js_confirm_not_verify": "Verification not required?", "taxonomy_dest_url_valid_f": "Current edit language is ", "global_activity": "Activity", "evoucher_only": "App e-voucher", "cancel_within_72h": "72 hours", "global_filter": "Filter", "package_info_unlimited": "Unlimited", "act_lan_agent_only": "Agent only", "taxonomy_destination_pixel": "size", "package_another_main_package": "Add new package", "package_list_manage": "Manage", "before_status": "Before", "taxonomy_dest_delete_tips": "Are you sure you want to delete this item？", "taxonomy_destination_keywords": "Destination Keywords", "package_unit_num_integer": "Whole number required", "act_choose_activity_category": "Select activity category", "act_choose_view_lan": "Select language view", "act_lan_ko_KR": "Korean", "global_tips": "Notes", "taxonomy_destination_basic_info": "Destination Basic Info", "package_input_error": "Invalid input", "chart_export_as_image": "Export as image", "pkg.checkItems": "Check Items", "pm_pick_time_range": "Duration", "timeslotlist_delete_selected": "Delete selected", "taxonomy_dest_version_publish_msg": "Version copied. Please review the content of the version to make sure every item is correct.", "other_info_no_required": "No info required (Recommended)", "other_info_standardized_tips_combo": "Identified package {0} contains standardized \"Other info\".  Standardized \"Other info\" can't be configured at combo package temporarily.", "pm_price_type": "Price type", "package_schedule_close_autoextend": "Stop auto extend", "voucher_get_method": "Voucher retrieval method", "package_unit_type_price_warning": "You can only edit the price in 'Bulk modification' page after saved.", "package_timeslot_title": "Add timeslot & inventory", "act_package_unit_name_hint": "Please enter unit name", "no_edit_en": "Sorry, you can only edit en_US. Please edit and publish this activity using en_US. Please contact Product Content team member if you need localized English content added to other English locales.", "publish_unpublish_reason": "Reason", "taxonomy_dialog_latitude": "Latitude", "other_info_non_standardized": "Additional user info (Non-standardized)", "package_timeslot_by_daily": "Daily", "price_inventory_page": "Price & inventory", "global_week_abbr_sat": "Sat", "package_ask_once_per_booking": "Ask once per booking", "taxonomy_dialog_name": "Name", "act_cannot_copy_package": "After the event is published, only users with AM permission can use the copy function", "package_list_archiv_package_tips": "Only unpublished packages can be archived. Unpublished packages will be archived automatically after 12 months.", "taxonomy_dialog_parent_region_placeholder": "Please input keyword and search", "package_reject": "You will reject content and cost of package onboarded by merchant, please justify this action with a valid reason.", "traditional_chinese": "Traditional Chinese", "activity_basic_page_basic_info": "Basic Info", "taxonomy_dest_add_third_level": "Create L3 Item", "taxonomy_destination_upload_video": "Upload video", "common_field_diver_details": "Diver details", "act_submit_to_content": "Submit for edits", "timeslotlist_weekly": "Weekly", "global_validate_text_required": "This field is required", "package_unit_requirement_tips": "If switched on, the user is required to book this unit type", "package_unit_buy": "Required for booking", "package_schedule_3_advance": "3 days in advance", "act_if_meet_criterias": "Does the activity include the following services?", "act_if_transferable": "Are vouchers for this activity transferable to another person?", "e_voucher": "Electronic voucher", "elevy_from_hk": "Transport from Hong Kong to outside of Hong Kong", "elevy_act_outside_hk": "Arrangements for an activity outside Hong Kong", "elevy_stay_outside_hk": "Accommodation outside Hong Kong", "activity_basic_page_banner_photo": "Banner photo", "leaf_category": "Leaf category", "global_activity_status": "Activity status", "ob_act_addr": "Activity address", "status.pendingApproval": "Pending approval", "ob_voucher_code_level": "Voucher issuance", "merchant_approve_status": "Approval status", "ticket_type": "Ticket type", "package_merchant_currency": "Merchant currency", "package_id_title": "Package ID & title", "act_group_summary": "Quick view", "act_activity_main_photos": "Activity photos", "global_copy": "Copy details from which info type?", "activity_id_title": "Activity ID & title", "activity_basic_page_main_photo": "Activity photos", "act_pkg_copy_info": "Copy from another package", "package_list_archive": "Archive status", "global_please_select": "Please select", "global_please_input": "Please enter", "act_copy_groups": "Copy sections across packages", "other_info_copy_no_data": "No packages to copy from yet", "act_groups_to_copy": "Select sections to copy", "other_info_submit_finished": "You have completed creating a package. Do you want to add more?", "package_copy_schedule_dialog_title": "Copy details from which package?", "package_xinfo_choose_repeat_rule": "Choose the repeat rules", "package_xinfo_repeat_rules": "Repeat rules", "taxonomy_dest_add_items": "Add item", "taxonomy_destination_customized_region": "Customized region", "package_bulk_modification_default_price_setting": "Default settings", "taxonomy_destination_upload_images": "Upload images", "24_confirm": "24-hour confirmation", "taxonomy_destination_click_to_edit": "Click to edit", "pm_date_time": "Participation date/time", "taxonomy_destination_official_language": "Official language", "taxonomy_dest_version_create": "Create version", "pm_expire_time": "Expire time", "taxonomy_country_level": "Country level", "taxonomy_destination_back": "Return to page list", "act_lan_en_SG": "Singaporean English", "taxonomy_destination_time_of_generation": "Generation time", "act_lan_zh_HK": "Traditional Chinese (HK)", "taxonomy_destination_edit_image": "Edit image", "package_default_price_cost": "Default cost price", "merchant_package_contact_type_wechat": "WeChat", "global_button_add": "Add new", "act_list_content_to_edit": "EM to edit", "taxonomy_destination_time_zone": "Time zone", "taxonomy_dest_item_title": "Item title", "taxonomy_destination_page_template": "Page template", "global_delete_success": "Deleted", "taxonomy_scenic_spot": "Sights & landmarks", "schedule_unpublish": "Schedule unpublish", "taxonomy_admin_region_name": "Admin region name", "taxonomy_dest_select_placeholder": "Select language", "group_per_unit": "Per unit", "act_auto_filled_original": "Auto-filled original package name", "taxonomy_confirm_selected": "Confirm selected items", "package_unit_subtitle": "Unit & price", "taxonomy_dest_copy": "Copy version", "taxonomy_destination_pin_all": "Pin all", "act_list_take_rate": "Take rate", "taxonomy_dialog_time_zone": "Time zone", "taxonomy_back_region_list": "Back to customized region list", "taxonomy_full_address": "Full address", "package_schedule_rule": "Schedule rule name", "pm_selling_price_type": "Selling price type", "package_price_premier_agent": "Premier agent price", "package_manage_unit": "Manage unit", "ob_vouc_usage1": "Printed + mobile voucher", "taxonomy_destination_seasonal_weather": "Seasonal weather", "taxonomy_multilingual_versions": "Multilingual versions", "package_another_addon_package": "Create another add-on package", "merchant_submit_activity_action": "Submit activity", "global_modify_error": "Modified error", "taxonomy_region_name": "Region name", "merchant_submit_package_action": "Submit package", "act_lan_zh_TW": "Traditional Chinese (TW)", "taxonomy_destination_unpinned_destination": "Unpinned destination", "taxonomy_dest_item_content": "Item content", "act_lan_au": "Australian English", "package_xinfo_text_box": "Text box", "taxonomy_dest_cancel_appoint": "Cancel schedule", "schedule_publish": "Schedule publish", "global_show_msg_save_success": "Saved!", "taxonomy_destination_image_size": "Image size", "package_default_price_selling": "Default selling price", "taxonomy_dest_pop_area": "Popular destination", "taxonomy_destination_all_destination": "All destinations", "act_list_in_preview": "In preview", "package_xinfo_ask_each": "Ask each participant", "taxonomy_railway_station": "Train station", "package_info_max_min_participants": "Min./max. bookings", "fnb_pacakge_dependent": "Package dependent", "taxonomy_dest_version_last_modifier": "Last editor", "package_inventory_limited": "Inventory limited", "pm_selling_history": "Selling price history", "group_per_unit_type": "Per unit type", "pkg.policiesRules": "Policies & rules", "taxonomy_dest_version_publish_time": "Published time", "taxonomy_destination_key_location": "Key location", "main_package": "Main package", "performed_by": "Performed by", "package_take_overview": "Submit & see overview", "pkg.specialSettings": "Special settings", "taxonomy_create_admin_region": "Create admin region", "taxonomy_destination_of_activities": "No. of activities", "taxonomy_create_region": "Create customized region", "taxonomy_create_key_location": "Create key location", "fnb_open_ticket": "Open ticket", "package_price_regular_agent": "Regular agent price", "taxonomy_dest_version_last_modify_time": "Last edit time", "taxonomy_destination_pinned_category": "Pinned category", "package_xinfo_ask_once": "Ask once per booking", "global_copy_error": "Copy error", "48_confirm": "48-hour confirmation", "taxonomy_destination_pinned_destination": "Pinned destination", "global_delete_error": "Delete error", "taxonomy_dest_area_type": "Destination page type", "act_activity_details": "Activity details", "package_block_out_date": "Block out date", "fnb_fixed_date": "Fixed date", "taxonomy_city_level": "City level", "package_xinfo_another": "Add another custom field", "global_modify_success": "Successfully modified", "activity_event_logs": "Event logs", "taxonomy_destination_pinned_act": "Pinned activities", "taxonomy_destination_seo_title": "SEO title", "act_create_new_activity": "Create new activity", "package_xinfo_name": "\"Other info\" name", "taxonomy_root_menu": "Root menu", "taxonomy_edit_dialog_title": "Edit admin region", "taxonomy_dest_version_create_time": "Time created", "package_xinfo_refresh_content": "Refresh content", "taxonomy_destination_category_navigation": "Category navigation", "taxonomy_destination_unpinned_category": "Unpinned category", "taxonomy_destination_cancel_pinning_all": "Don't pin all", "submitted_language": "Submitted language", "taxonomy_destination_explore_city": "Explore city", "taxonomy_dest_type_dest": "Destination page", "global_copy_success": "Copy success", "taxonomy_destination_page_title": "Page title", "taxonomy_destination_type": "Destination type", "taxonomy_destination_seo_meta": "SEO meta description", "package_price_cost": "Cost price", "new_take_rate": "New take rate", "package_custom_selling": "Special selling price", "global_create_error": "Created error", "taxonomy_destination_popular_activities": "Popular activities", "merchant_package_contact_type_kakaotalk": "KakaoTalk", "act_lan_tw": "Traditional Chinese (TW)", "package_price_selling": "Selling price", "fnb_package_management_save": "Save & continue editing", "taxonomy_selected_sub_regions": "Selected sub-region", "taxonomy_region_type": "Region type", "taxonomy_dest_edit_submenu": "Edit sub-menu", "package_xinfo_type": "\"Other info\" type", "taxonomy_destination_current_language": "Current language", "package_xinfo_preview_fields": "Preview fields", "taxonomy_destination_page_management": "Destination page management", "group_per_booking": "Per booking", "taxonomy_destination_description": "Destination description", "taxonomy_dest_read_submenu": "Read sub-menu", "taxonomy_destination_title": "Destination title", "ob_voucher_choice3": "Merchant voucher", "taxonomy_dest_area_city": "City page", "other_info_non_standardized_warning": "The \"Other info\" boxes appear during the customer’s checkout that gives Klook additional information on the customer’s details and requirements during the experience. If these selections are not satisfactory in describing your product, please contact your Klook POC.", "taxonomy_list_country_code_3": "Country code alpha-3", "taxonomy_destination_admin_region": "Admin region", "taxonomy_alert_title": "Heads up", "can_not_be_empty": "Can't be empty", "taxonomy_mall": "Shopping mall", "mandatory_lang_hint": "Make sure that merchant received and merchant confirmed email can send complete, please enter the custom field \"Other info\"", "package_xinfo_copy": "Copy from existing \"Other info\"", "taxonomy_dest_url_valid_s": ". URL should start with https://www.klook.com", "ttr_new_tr": "You're about to set the new take rate at:", "act_activity_subtitle": "Activity subtitle", "override_take_rate": "Minimum take rate in future timeslots", "submit_to_em": "Submit activity to EM", "financial_model_rejected": "Rejected", "package_current_cost": "Edited cost", "package_current_cost_col": "Current cost", "fnb_premium_service": "Premium service", "financial_model_confirm_tips": "Bulk purchase products can only have “Inventory” as Inventory type. Please go back to the \"Package details\" page to check the Inventory type setting or contact the PIC to get advice.", "package_price_modify_cost_price": "Edit cost", "package_base_selling": "Selling price", "special_inventory_tips": "If timeslots are created but the package is sold out, please check the Special inventory. The Special inventory and Timeslot inventory will both work on this package.", "ttr_compare_tr": "Heads up: The new take rate is lower than the SKU's minimum take rate in future timeslots", "fnb_set_menu": "Set menu", "msp_currency_plh": "<PERSON><PERSON><PERSON><PERSON>", "submit_to_preview": "Submit activity to preview", "msp_amount_plh": "Amount", "seo_url_format_tip": "Enter using lowercase letters, numbers and hyphens (-)", "package_price_operator": "Operator", "package_price_effect": "Active", "package_price_selling_amount_number": "Selling price amount", "ttr_confirm_change": "Are you sure you want to override?", "package_price_type": "Type", "global_button_del": "Delete", "act_short_description": "Short description", "package_price_invalid_time": "Expired date", "merchant_id_tier": "Merchant ID-Name-Tier", "act_for_internal_analysis_2": "(for internal analysis)", "package_price_amount_type": "Selling price type", "last_take_rate": "Existing take rate", "act_tags": "Tags", "SKU_Name": "SKU name", "fnb_buffet": "Buffet", "act_financial_model_pkg_warn": "This package is published. Please unpublish before responding to the question.", "cms_datetime_picker_ph": "Please select a date and time", "package_price_status": "Status", "fnb_meal_kit": "Meal kit", "package_price_agent_type": "Agent type", "global_button_handle": "Edit", "content_act_seo_url": "activity-name-city-name", "edit_activity_content": "Edit SEO content", "fnb_open_ticket_without_title": "Open ticket (without calendar) package", "fnb_cash_voucher": "Cash voucher", "fnb_open_ticket_with_title": "Open ticket (with calendar)", "package_customized_unit_name": "Customized unit name", "fnb_single_dish": "À la carte", "package_price_modify_update_cost": "Update cost", "act_activity_title": "Activity title", "act_seo_url": "SEO URL", "questionnaire": "Questionnaire", "act_notes": "Operational info", "financial_model_not_set": "Not set", "package_type": "Package type", "act_for_internal_analysis_1": "Product vertical level", "act_seo_top_keywords": "SEO top keywords", "quantity_special_inventory": "Special inventory quantity", "unpublish_activity": "Unpublish activity", "ttr_email": "By clicking “Confirm”, an alert and edit history will be sent to the revenue team", "financial_model_status": "Status", "publish_package": "Publish package", "act_seo_top_keywords_hint": "3 keywords max., separated with a comma (,)", "act_financial_model_send_email": "For maximum efficiency, all packages pending review will be sent together. Please try to create all of your packages before sending the review request to the PIC.", "goto_archived_packages": "View archived packages", "act_product_notes": "Product notes", "financial_model_pending_approve": "Pending approval", "financial_model_financial_model": "Financial model", "info_notes": "Operational info", "act_seo_title": "SEO title", "act_financial_model_send_btn": "Send now", "financial_model_reset": "Reset", "financial_model_dialog_back_to": "Back to package settings", "global_remarks": "Remark", "target_take_rate": "Minimum expected take rate", "financial_model_approved": "Approved", "package_original_cost": "Origin cost", "publish_activity": "Publish activity", "financial_model_dialog_save": "Continue & save", "act_seo_fields": "SEO info", "send_email_success": "Email sent", "financial_model_skip": "<PERSON><PERSON>", "no_permission": "Sorry, your account doesn't have permission", "email_to_pic": "Request PIC review", "act_seo_meta_description": "SEO meta description", "unpublish_package": "Unpublish package", "act_seo_title_hint": "Please enter the SEO title", "package_price_valid_time": "Effective time", "no_auth_edit": "This can only be edited when in \"EM to edit\" status (with EM permission)", "status_tip": "Can't continue because either the required fields are not complete (i.e. <span style=\"font-weight:bold;\">photo alt text</span> under Basic info) or your account doesn't have permission", "name_lang.name": "Enter language", "name_lang.hover": "When booking your activity, users must enter their name using a language you've selected", "identification_type.hover": "If you select multiple ID types, users only need to enter info for 1", "select_items.name": "Choose ticket type", "identification_info_expiration_date.name": "Expiry date", "identification_info_issuing_place.name": "Place of issue", "note_write_type_text.name": "Text", "is_hidden.name": "Only visible on app (before version 5.56)", "is_hidden_check.name": "Yes", "name_group.name": "Name requirement", "name": "Certificate", "note_required_optional.name": "Optional", "participant_hint.name": "Entering instructions", "tips": "Configuration of country code requires development on each Frontend platform (e.g. app, 2B web)", "note_type.name": "Select special note type", "note_hint.hover": "Brief instructions to help users enter information when booking", "participant_type.name": "Participant type", "participant_write_type.name": "User input type", "identification_info_id.name": "ID number", "note_write_type.name": "User input type", "note_hint.name": "Input instructions", "note_required.name": "Optional?", "accept_country.name": "Accepted countries/regions", "accept_country_num.hover": "When entering their phone number, users must use a country/region code you've selected", "identification_type.name": "ID type", "identification_info.name": "ID info", "contact_way.name": "Messaging apps", "lang_items.name": "Options for users", "participant_number_range.name": "Number range", "accept_country.hover": "When booking your activity, users must be a resident of a country you've selected", "identification_info_date_issued.name": "Issue date", "participant_write_type_number.name": "Number", "participant_hint.hover": "Brief instructions to help users enter information when booking", "accept_country_num.name": "Accepted country/region codes", "contact_way.hover": "If you select multiple messaging apps, users only need to enter info for 1", "lang_hint.name": "Input instructions", "lang_hint.hover": "Brief instructions to help users enter information when booking", "act_guarantee_for_package_1": "Escrow provided by merchant?", "package_cut_off_time_hint": "This is the time at which Klook will stop accepting bookings from users (set in local time). The earliest cut-off time you can set is \"Today 00:00\". \n\nTip: Having a shorter cut-off time means you can accept bookings until the last minute to maximize your sales.", "package_unit_tool": "Is the user required to book this unit? If so, select \"Yes\" and set the Booking quantity limitations as \"Limited\" with the min. unit to 1.", "form.gpsLocation": "Please enter an address to find the coordinates", "act_address": "Coordinate search", "global.selected_date": "Selected dates:", "act_seo_keywords": "Activity keywords (for searching on Klook)", "act_seo_keywords_hint": "Separate keywords with a comma (e.g. Staycation, theme park)", "redemptions_filter_text": "Filter", "voucher.copy": "Copy", "alter_page_traveler_inforamtion": "Traveler's Information", "act_blocklist_all_languages": "All languages (default)", "language_publish_condition": "Untick any languages you don't need", "package_sku_id_desc": "", "activity.v2.label.package_options": "Package options", "activity.detail.about.activity": "What to expect", "app3.activityDetail.menu.FAQ": "FAQs", "theme_park_assistant": "Theme Park Planner", "activity.detail.location.map": "Location", "global.contact.us": "Contact us", "activity.v2.label.most_recent_reviews": "Most recent review", "speact_rl4_tv0": "Reviews", "relevant_activity": "Often booked together", "app3.activityDetail.recommendActivities": "You might also like...", "image_change_custom_hint": "By ticking this box, any images will be uploaded in the selected language only. Continue?", "image_change_dft_hint": "By unticking this box, any images will be uploaded in all<span style=\"font-weight:bold;\"> </span>languages. Continue?", "create_act_photo_for_lang": "Upload activity photo for this language only (to upload the image in all languages, do not check this box)", "package_xinfo_checkbox_placeholder": "Please enter the Terms & Conditions", "print_voucher": "Printed voucher only", "print_mobile_offline_voucher": "E-voucher + offline voucher supported", "print_mobile_voucher": "E-voucher supported", "booking_report.details": "Details", "MULTIPLE_aid": "AID", "filter_btn_text_apply": "Confirm", "cancel": "Cancel", "fnb_open_ticket_with_content": "Packages with this ticket type selected will display both a calendar to select dates and an \"Open date\" package icon to users. For the best user experience, we strongly recommend using \"Open date without calendar\" instead. Find out more in this <a href=\"MULTILANG_URL_PLACEHOLDER\">handy guide</a>.", "SKU_ID": "Unit ID", "CODE_AD_applicable_platform": "Applicable Platform", "voucher.delete_note": "Are you sure to want to delete?", "loading_text": "Processing – Please wait for the result.", "common.leave_title": "Leave this page", "global.user_guide_got_it": "Got it", "global.user_guide_next": "Next", "global.more": "Show more", "js_confirm_delete": "Are you sure you want to deactivate/delete？", "orderdetail_text_reject_reason": "Reason for Rejection", "cancel_before_start": "Free cancellation before the activity starts", "title_clear": "Clear", "act_responsible_BD": "Responsible BD", "package_info_desc": "About this package", "package_unit_limited_when_required": "You can't choose this for mandatory units. Please select \"Limited\" and set a min. quantity.", "package_unit_min_num_when_required": "Min. quantity of required units should be at least 1", "package_min_max_units": "Min./max. quantity", "upload_4_photos_at_least": "You need to upload at least 4 photos for this activity", "package_required_fields": "You need to fill in all the required fields for the package first", "no_size_limit": "Set as banner without cropping (on activity page only)", "act_tips_diff_sub_categories": "Please note that once you confirm, you can only switch to a different leaf category (not to a different sub-category). These categories help users find your activity on vertical pages.\n\nPlease confirm that you are creating an activity tagged with the following category flow: [{0}] > [{1}] > [{2}]", "global_city": "Destination", "act_binding_destination_city": "Destination", "submit_time_bj": "Submitted time (UTC+8)", "common_back": "Back", "product_list_page_destination": "Destination City", "fnb_open_ticket_without_content": "Packages with this ticket type selected will NOT display a calendar on activity page. The package setting conditions are:\n- Subcategory ids: 1,2,4,8,15,17,18,20,21,22,171,177,179,194,206,220,223,228,230,232,311,317,413,415,523,525,527,529,551,552,558,561,563\n- Confirmation policy: All supported (Instant confirmation, 24 hours confirmation, 48 hours confirmation)\n- Cancellation policy: All supported except for \"Free cancellation before activity starts\"\n- Merchant settlement policy: All supported (Pay by redemption/confirmation/participation)\n- Inventory type: Others, API\n\nHeads up:\n- The package will be automatically unpublished when the validity period has passed\n- The default participation date will be the last day of the validity period\n- For more details, check this <a href=\"MULTILANG_URL_PLACEHOLDER\">handy guide</a> for more details.", "other_info_required_participants": "Additional info required:", "other_info_additional": "Other required info", "other_info_following_info_collected": "Basic info required:", "lang_write_type.name": "Selections allowed", "lang_select_multi.name": "Multiple selections", "lang_select.name": "Single selection", "booking_view_application": "View Request", "booking_traveler_info": "Traveler's Information", "traveler_name": "Traveler Name", "booking_extra_info": "Extra Information", "participant_name": "Participant Name", "booking_processing": "Processing", "booking_confirm": "Re-confirm", "booking_reconfirm_title": "Confirm Request", "booking_reconfirm_content": "The customer will be informed once their request has been confirmed. Are you sure you want to confirm the request?", "booking_updated": "Confirmed", "booking_withdrawn_tips": "The request to update booking information has been withdrawn. No further action required", "booking_info_changed": "The booking information has been updated and needs to be re-confirmed", "booking_application_confirmed": "The booking has been amended.", "booking.pending": "Pending", "booking.canceled": "Canceled", "booking.expired": "Expired", "booking_application": "Request", "booking.confirmed": "Confirmed", "to_be_canceled": "To Be Canceled", "reason_to_decline": "Reason of rejection:", "processing_voucher": "Processing voucher...", "processing_voucher_tip": "Voucher is still processing. No further action is needed until the voucher has been confirmed.", "inquire_message": "Message", "inquire_message_holder": "Please enter your questions (limited within 500 words)", "info.document_tips": "Formats: png, jpg, jpeg, pdf. Max size: 4MB.", "global.empty_tips": "Can't be empty", "booking_confirmed_click": "Click to show Confirm note", "booking_confirm_title": "Confirm Booking", "booking_confirm_tips": "Are you sure to change the order to confirm status?", "merge_and_check": "Merge and Check", "booking_confirm_instant": "Once you confirm this booking, the user will receive their activity voucher right away", "voucher_number_placeholder": "Only supports letters, numbers, and hyphens (-)", "pickup_location_english_placeholder": "Please enter Pick up / Meet up location details in English", "ppickup_location_local_placeholder": "Please enter Pick up / Meet up location details in your local official language", "whether_set_up_reconfirmation": "Get a reminder to reconfirm pre-participation info with customers", "set_up_reconfirmation_label": "How many days before the pariticipation date?", "booking.unit.day": "Day(s)", "voucher_number_error": "Please enter your code using only letters, numbers and hyphens (-)", "booking.user_order_lang_tip": "If you have any difficulties communicating in the user's preferred language, please select English instead.", "booking.note.confirm.placeholder": "Leave a message for <PERSON><PERSON> if applicable. (optional)", "global.upload": "Upload", "reject_dialog_title": "Reason for Rejection (for Klook reference only)", "reject_select_reason": "Select reason for rejection", "reject_note_placeholder": "List available time slots or other relevant information (max. 500 characters, optional)", "reject_auto_reject": "If you select this reason, the system will automatically reject all pending bookings for this time slot", "reject_total": "Total pending", "reject_view_booking": "See all", "reject_timeslot": "Time slot", "reject_auto_reopen": "Automatically re-open this time slot if any previous bookings are issued a refund", "reject_delete_timeslot": "The date will be permanently closed and will not be re-opened", "reject_note": "Notes:", "activity.published": "Published", "activity.notpublished": "Not Published", "overdue_in_2h": "Becomes overdue in 2 hours", "pending_10h": "Pending for over 10 hours", "redeem_unredeemable": "Unredeemable Unit(s)", "global.file_size_limit": "{limit} MB max. for upload", "global.file_number_limit": "{limit} uploads max.", "booking_view_confirm_time": "Please confirm the booking amendment request before {time} (Local Time)", "global.delete": "Delete", "global.delete_file_tips": "Are you sure you want to delete?", "trp_2022031770": "Create translation request", "global.password_placeholder": "Enter password", "signup.send_again": "Send again in {second} s", "toast_account_added_success": "Added successfully!", "package_unit_number": "Booking quantity limit", "save_and_next": "Save and continue", "act_blocklist_note": "Select \"I want to choose\" to exclude certain languages", "taxonomy_dest_version_creator": "Created by", "act_blocklist_selected_languages": "I want to choose", "sub_category": "Sub-category", "package_retail_selling_currency": "Selling currency", "activities.add_new": "Add new", "global.confirm": "Confirm", "activities.wednsday": "Wed", "activities.thursday": "<PERSON>hu", "activities.tuesday": "<PERSON><PERSON>", "activities.friday": "<PERSON><PERSON>", "activities.sunday": "Sun", "activities.saturday": "Sat", "content": "Content", "booking_updated_tips": "Updated", "timestamp": ****************}