import { mapActions } from 'vuex'
const sspPriceMixin = {
  data() {
    return {
      sspPriceMixin: {
        sellingCurrency: '',
        price: 0,
        defKeys: {
          type: 'pricing_rule_type',
          currency: 'currency',
          percent: 'percent',
          price: 'price'
        },
        rates: {},
        fn: {
          getPriceRates: async (currency) => {
            if (currency) {
              let { rates } = this.sspPriceMixin
              //成本汇率转售价汇率
              let result = await this.getExchangeRate2actions({
                currency
              })
              if (result && result.length) {
                this.$set(rates, currency, result)
              }
            }
          },
          getRate: (sellingCurrency, currency) => {
            let { rates } = this.sspPriceMixin
            let arr = rates[sellingCurrency] || []
            let obj = arr.find((o) => o.to_currency === currency) || {}
            return obj.to_value || 0
          },
          bindPrice: (item, mk) => {
            let { fn, price, sellingCurrency } = this.sspPriceMixin
            if (item[mk.type] !== 1) return
            if (!item[mk.currency] || !item[mk.percent]) return
            let rate = fn.getRate(sellingCurrency, item.currency)
            let calcPrice = ((price * item.percent) / 100) * rate
            this.$set(item, mk.price, parseFloat(calcPrice.toFixed(2)))
          },
          bindList: async (list, options) => {
            let { mKeys = {}, price, sellingCurrency } = options || {}
            let { defKeys, fn, rates } = this.sspPriceMixin
            this.sspPriceMixin.sellingCurrency = sellingCurrency
            if (sellingCurrency && !rates[sellingCurrency]) {
              await fn.getPriceRates(sellingCurrency)
            }
            this.sspPriceMixin.price = parseFloat(price) || 0
            let mk = _.merge({}, defKeys, mKeys)
            list.forEach((item) => {
              fn.bindPrice(item, mk)
            })
          }
        }
      }
    }
  },
  methods: {
    ...mapActions(['getExchangeRate2actions']),
    async bindSspList2mixin(...args) {
      await this.sspPriceMixin.fn.bindList(...args)
    },
    async getPriceRates2mixin(...args) {
      await this.sspPriceMixin.fn.getPriceRates(...args)
    }
  }
}

export default {
  sspPriceMixin
}
