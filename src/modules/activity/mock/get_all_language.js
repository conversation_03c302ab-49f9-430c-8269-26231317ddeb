/* eslint-disable quotes */
export default {
  success: true,
  error: {
    code: '',
    message: ''
  },
  result: [
    {
      id: 3,
      code: 'af',
      language: 'af_ZA',
      content: '南非语',
      content_en: 'Afrikaans'
    },
    {
      id: 100,
      code: 'am',
      language: 'am_AM',
      content: '阿姆哈拉语',
      content_en: 'Amharic'
    },
    {
      id: 5,
      code: 'ar',
      language: 'ar_SA',
      content: '阿拉伯语',
      content_en: 'Arabic'
    },
    {
      id: 7,
      code: 'az',
      language: 'az_AZ',
      content: '阿塞拜疆语',
      content_en: 'Azerbaijani'
    },
    {
      id: 9,
      code: 'be',
      language: 'be_BY',
      content: '比利时语',
      content_en: 'Belarusian'
    },
    {
      id: 12,
      code: 'bg',
      language: 'bg_BG',
      content: '保加利亚语',
      content_en: 'Bulgarian'
    },
    {
      id: 10,
      code: 'bn',
      language: 'bn_BN',
      content: '孟加拉语',
      content_en: 'Bengali'
    },
    {
      id: 11,
      code: 'bs',
      language: 'bs_BS',
      content: '波斯尼亚语',
      content_en: 'Bosnian'
    },
    {
      id: 13,
      code: 'ca',
      language: 'ca_ES',
      content: '加泰隆语',
      content_en: 'Catalan'
    },
    {
      id: 15,
      code: 'co',
      language: 'co_CO',
      content: '科西嘉语',
      content_en: 'Corsican'
    },
    {
      id: 17,
      code: 'cs',
      language: 'cs_CZ',
      content: '捷克语',
      content_en: 'Czech'
    },
    {
      id: 94,
      code: 'cy',
      language: 'cy_GB',
      content: '威尔士语',
      content_en: 'Welsh'
    },
    {
      id: 18,
      code: 'da',
      language: 'da_DK',
      content: '丹麦语',
      content_en: 'Danish'
    },
    {
      id: 27,
      code: 'de',
      language: 'de_DE',
      content: '德语(德国)',
      content_en: 'German'
    },
    {
      id: 99,
      code: 'dv',
      language: 'dv_DV',
      content: '迪维希语',
      content_en: 'Maldivian'
    },
    {
      id: 98,
      code: 'dz',
      language: 'dz_DZ',
      content: '不丹语',
      content_en: 'Bhutanese'
    },
    {
      id: 28,
      code: 'el',
      language: 'el_GR',
      content: '希腊语',
      content_en: 'Greek'
    },
    {
      id: 2,
      code: 'en',
      language: 'en_US',
      content: '英语',
      content_en: 'English'
    },
    {
      id: 20,
      code: 'eo',
      language: 'eo_EO',
      content: '世界语',
      content_en: 'Esperanto'
    },
    {
      id: 81,
      code: 'es',
      language: 'es_ES',
      content: '西班牙语',
      content_en: 'Spanish'
    },
    {
      id: 21,
      code: 'et',
      language: 'et_EE',
      content: '爱沙尼亚语',
      content_en: 'Estonian'
    },
    {
      id: 8,
      code: 'eu',
      language: 'eu_ES',
      content: '巴士克语',
      content_en: 'Basque'
    },
    {
      id: 65,
      code: 'fa',
      language: 'fa_IR',
      content: '法斯语',
      content_en: 'Persian'
    },
    {
      id: 22,
      code: 'fi',
      language: 'fi_FI',
      content: '芬兰语',
      content_en: 'Finnish'
    },
    {
      id: 23,
      code: 'fr',
      language: 'fr_FR',
      content: '法语(法国)',
      content_en: 'French'
    },
    {
      id: 24,
      code: 'fy',
      language: 'fy_FY',
      content: '弗里斯兰语',
      content_en: 'Frisian'
    },
    {
      id: 38,
      code: 'ga',
      language: 'ga_GA',
      content: '爱尔兰语',
      content_en: 'Irish'
    },
    {
      id: 72,
      code: 'gd',
      language: 'gd_GD',
      content: '苏格兰盖尔语',
      content_en: 'Scots Gaelic'
    },
    {
      id: 25,
      code: 'gl',
      language: 'gl_ES',
      content: '加里西亚语',
      content_en: 'Galician'
    },
    {
      id: 29,
      code: 'gu',
      language: 'gu_IN',
      content: '古吉拉特语',
      content_en: 'Gujarati'
    },
    {
      id: 31,
      code: 'ha',
      language: 'ha_HA',
      content: '豪萨语',
      content_en: 'Hausa'
    },
    {
      id: 32,
      code: 'he',
      language: 'he_IL',
      content: '希伯来语',
      content_en: 'Hebrew'
    },
    {
      id: 33,
      code: 'hi',
      language: 'hi_IN',
      content: '印地语',
      content_en: 'Hindi'
    },
    {
      id: 16,
      code: 'hr',
      language: 'hr_HR',
      content: '克罗地亚语',
      content_en: 'Croatian'
    },
    {
      id: 30,
      code: 'ht',
      language: 'ht_HT',
      content: '海地克里奥尔语',
      content_en: 'Haitian Creole'
    },
    {
      id: 34,
      code: 'hu',
      language: 'hu_HU',
      content: '匈牙利语',
      content_en: 'Hungarian'
    },
    {
      id: 6,
      code: 'hy',
      language: 'hy_AM',
      content: '亚美尼亚语',
      content_en: 'Armenian'
    },
    {
      id: 37,
      code: 'id',
      language: 'id_ID',
      content: '印度尼西亚语',
      content_en: 'Indonesian'
    },
    {
      id: 36,
      code: 'ig',
      language: 'ig_IG',
      content: '伊博语',
      content_en: 'Igbo'
    },
    {
      id: 35,
      code: 'is',
      language: 'is_IS',
      content: '冰岛语',
      content_en: 'Icelandic'
    },
    {
      id: 39,
      code: 'it',
      language: 'it_IT',
      content: '意大利语',
      content_en: 'Italian'
    },
    {
      id: 40,
      code: 'ja',
      language: 'ja_JP',
      content: '日语',
      content_en: 'Japanese'
    },
    {
      id: 41,
      code: 'jv',
      language: 'jv_JV',
      content: '爪哇语',
      content_en: 'Javanese'
    },
    {
      id: 26,
      code: 'ka',
      language: 'ka_GE',
      content: '格鲁吉亚语',
      content_en: 'Georgian'
    },
    {
      id: 43,
      code: 'kk',
      language: 'kk_KZ',
      content: ' 哈萨克语',
      content_en: 'Kazakh'
    },
    {
      id: 44,
      code: 'km',
      language: 'km_KM',
      content: '高棉语',
      content_en: 'Khmer'
    },
    {
      id: 42,
      code: 'kn',
      language: 'kn_IN',
      content: '卡纳拉语',
      content_en: 'Kannada'
    },
    {
      id: 45,
      code: 'ko',
      language: 'ko_KR',
      content: '朝鲜语',
      content_en: 'Korean'
    },
    {
      id: 46,
      code: 'ku',
      language: 'ku_KU',
      content: '库尔德语',
      content_en: 'Kurdish '
    },
    {
      id: 47,
      code: 'ky',
      language: 'ky_KG',
      content: '吉尔吉斯语',
      content_en: 'Kyrgyz'
    },
    {
      id: 49,
      code: 'la',
      language: 'la_LA',
      content: '拉丁语',
      content_en: 'Latin'
    },
    {
      id: 52,
      code: 'lb',
      language: 'lb_LB',
      content: '卢森堡语',
      content_en: 'Luxembourgish'
    },
    {
      id: 48,
      code: 'lo',
      language: 'lo_LO',
      content: '老挝语',
      content_en: 'Lao'
    },
    {
      id: 51,
      code: 'lt',
      language: 'lt_LT',
      content: '立陶宛语',
      content_en: 'Lithuanian'
    },
    {
      id: 50,
      code: 'lv',
      language: 'lv_LV',
      content: '拉脱维亚语',
      content_en: 'Latvian'
    },
    {
      id: 54,
      code: 'mg',
      language: 'mg_MG',
      content: '马尔加什语',
      content_en: 'Malagasy'
    },
    {
      id: 58,
      code: 'mi',
      language: 'mi_NZ',
      content: '毛利语',
      content_en: 'Maori'
    },
    {
      id: 53,
      code: 'mk',
      language: 'mk_MK',
      content: '马其顿语',
      content_en: 'Macedonian'
    },
    {
      id: 56,
      code: 'ml',
      language: 'ml_ML',
      content: '马拉雅拉姆语',
      content_en: 'Malayalam'
    },
    {
      id: 60,
      code: 'mn',
      language: 'mn_MN',
      content: '蒙古语',
      content_en: 'Mongolian'
    },
    {
      id: 59,
      code: 'mr',
      language: 'mr_MR',
      content: '马拉地语',
      content_en: 'Marathi'
    },
    {
      id: 55,
      code: 'ms',
      language: 'ms_MY',
      content: '马来语',
      content_en: 'Malay'
    },
    {
      id: 57,
      code: 'mt',
      language: 'mt_MT',
      content: '马耳他语',
      content_en: 'Maltese'
    },
    {
      id: 61,
      code: 'my',
      language: 'my_MY',
      content: '缅甸语',
      content_en: 'Myanmar '
    },
    {
      id: 62,
      code: 'ne',
      language: 'ne_NE',
      content: '尼泊尔语',
      content_en: 'Nepali'
    },
    {
      id: 19,
      code: 'nl',
      language: 'nl_NL',
      content: '荷兰语(荷兰)',
      content_en: 'Dutch'
    },
    {
      id: 63,
      code: 'no',
      language: 'no_NO',
      content: '挪威语',
      content_en: 'Norwegian'
    },
    {
      id: 14,
      code: 'ny',
      language: 'ny_NY',
      content: '齐切瓦语',
      content_en: 'Chichewa'
    },
    {
      id: 68,
      code: 'pa',
      language: 'pa_IN',
      content: '旁遮普语',
      content_en: 'Punjabi'
    },
    {
      id: 66,
      code: 'pl',
      language: 'pl_PL',
      content: '波兰语',
      content_en: 'Polish'
    },
    {
      id: 64,
      code: 'ps',
      language: 'ps_PS',
      content: '普什图语',
      content_en: 'Pashto'
    },
    {
      id: 67,
      code: 'pt',
      language: 'pt_PT',
      content: '葡萄牙语',
      content_en: 'Portuguese'
    },
    {
      id: 69,
      code: 'ro',
      language: 'ro_RO',
      content: '罗马尼亚语',
      content_en: 'Romanian'
    },
    {
      id: 70,
      code: 'ru',
      language: 'ru_RU',
      content: '俄语',
      content_en: 'Russian'
    },
    {
      id: 76,
      code: 'sd',
      language: 'sd_SD',
      content: '信地语',
      content_en: 'Sindhi'
    },
    {
      id: 77,
      code: 'si',
      language: 'si_SI',
      content: '僧伽罗语',
      content_en: 'Sinhala'
    },
    {
      id: 78,
      code: 'sk',
      language: 'sk_SK',
      content: '斯洛伐克语',
      content_en: 'Slovak'
    },
    {
      id: 79,
      code: 'sl',
      language: 'sl_SI',
      content: '斯洛文尼亚语',
      content_en: 'Slovenian'
    },
    {
      id: 71,
      code: 'sm',
      language: 'sm_SM',
      content: '萨摩亚语',
      content_en: 'Samoan'
    },
    {
      id: 75,
      code: 'sn',
      language: 'sn_SN',
      content: '修纳语',
      content_en: 'Shona'
    },
    {
      id: 80,
      code: 'so',
      language: 'so_SO',
      content: '索马里语',
      content_en: 'Somali'
    },
    {
      id: 4,
      code: 'sq',
      language: 'sq_AL',
      content: '阿尔巴尼亚语',
      content_en: 'Albanian'
    },
    {
      id: 73,
      code: 'sr',
      language: 'sr_SP',
      content: '塞尔维亚语',
      content_en: 'Serbian'
    },
    {
      id: 74,
      code: 'st',
      language: 'st_ST',
      content: '塞索托语',
      content_en: 'Sesotho'
    },
    {
      id: 82,
      code: 'su',
      language: 'su_SU',
      content: '巽他语',
      content_en: 'Sundanese'
    },
    {
      id: 84,
      code: 'sv',
      language: 'sv_SE',
      content: '瑞典语',
      content_en: 'Swedish'
    },
    {
      id: 83,
      code: 'sw',
      language: 'sw_KE',
      content: '斯瓦希里语',
      content_en: 'Swahili'
    },
    {
      id: 86,
      code: 'ta',
      language: 'ta_IN',
      content: '泰米尔语',
      content_en: 'Tamil'
    },
    {
      id: 87,
      code: 'te',
      language: 'te_IN',
      content: '泰卢固语',
      content_en: 'Telugu'
    },
    {
      id: 85,
      code: 'tg',
      language: 'tg_TG',
      content: '塔吉克语',
      content_en: 'Tajik'
    },
    {
      id: 88,
      code: 'th',
      language: 'th_TH',
      content: '泰语',
      content_en: 'Thai'
    },
    {
      id: 97,
      code: 'tk',
      language: 'tk_TK',
      content: '土库曼语',
      content_en: 'Turkmen'
    },
    {
      id: 89,
      code: 'tr',
      language: 'tr_TR',
      content: '土耳其语',
      content_en: 'Turkish'
    },
    {
      id: 90,
      code: 'uk',
      language: 'uk_UA',
      content: '乌克兰语',
      content_en: 'Ukrainian'
    },
    {
      id: 91,
      code: 'ur',
      language: 'ur_PK',
      content: '乌都语',
      content_en: 'Urdu'
    },
    {
      id: 92,
      code: 'uz',
      language: 'uz_UZ ',
      content: '乌兹别克语',
      content_en: 'Uzebek'
    },
    {
      id: 93,
      code: 'vi',
      language: 'vi_VN',
      content: '越南语',
      content_en: 'Vietnamese'
    },
    {
      id: 95,
      code: 'xh',
      language: 'xh_ZA',
      content: '班图语',
      content_en: 'Xhosa'
    },
    {
      id: 1,
      code: 'zh',
      language: 'zh_CN',
      content: '中文',
      content_en: 'Chinese'
    },
    {
      id: 96,
      code: 'zu',
      language: 'zu_ZA',
      content: '祖鲁语',
      content_en: 'Zulu'
    }
  ]
}
