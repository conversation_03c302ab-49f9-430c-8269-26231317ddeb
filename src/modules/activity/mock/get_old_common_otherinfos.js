/* eslint-disable quotes */
export default {
  success: true,
  error: {
    code: '',
    message: ''
  },
  result: {
    info: [
      {
        category_id: '1',
        content: [
          {
            extra_info_id: 140558,
            type_flag: 3,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '证件类型',
                hint:
                  '请选择证件类型,护照,中国内地居民身份证,香港/澳门旅客回乡证,台湾旅客台胞证',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 4,
            type_flag: 1,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '护照号码',
                hint: '填写护照号码',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 11,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '护照号码',
                hint: '填写预订人护照号码',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 111,
            type_flag: 2,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '填写出生日期',
                hint: '年/月/日',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 112,
            type_flag: 2,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '填写出生日期',
                hint: '年/月/日',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 113,
            type_flag: 3,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '性别',
                hint: '请选择,男士,女士',
                version: 1
              }
            ]
          },
          {
            extra_info_id: 141,
            type_flag: 3,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '称谓',
                hint: '请选择,女士,先生',
                version: 1
              }
            ]
          },
          {
            extra_info_id: 242,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '联系电话',
                hint: '填写联系电话',
                version: 1
              }
            ]
          },
          {
            extra_info_id: 328,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '儿童年龄',
                hint: '比如, 5,8,10',
                version: 1
              }
            ]
          },
          {
            extra_info_id: 362,
            type_flag: 1,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '护照 - 姓',
                hint: '请填写英文, 如: LIN',
                version: 1
              }
            ]
          },
          {
            extra_info_id: 363,
            type_flag: 1,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '护照 - 名',
                hint: '请填写英文, 如: XIAOMING',
                version: 1
              }
            ]
          },
          {
            extra_info_id: 2047,
            type_flag: 3,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '国籍',
                hint:
                  '请选择,Afghanistan,Albania,Algeria,American Samoa,Andorra,Angola,Anguilla,Antarctica,Antigua and Barbuda,Argentina,Armenia,Aruba,Australia,Austria,Azerbaijan,Bahamas,Bahrain,Bangladesh,Barbados,Belarus,Belgium,Belize,Benin,Bermuda,Bhutan,Bolivia,Bosnia and Herzegovina,Botswana,Bouvet Island,Brazil,British Indian Ocean Territory,Brunei Darussalam,Bulgaria,Burkina Faso,Burundi,Cambodia,Cameroon,Canada,Cape Verde,Cayman Islands,Central African Republic,Chad,Chile,China,Christmas Island,Cocos (Keeling) Islands,Colombia,Comoros,Congo-the Democratic Republic of the CD,Cook Islands,Costa Rica,Cote d‘Ivoire,Croatia,Cuba,Cyprus,Czech Republic,Denmark,Djibouti,Dominica,Dominican Republic,Ecuador,Egypt,El Salvador,Equatorial Guinea,Estonia,Falkland Islands (Malvinas),Fiji,Finland,France,French Guiana,French Southern Territories,Gabon,Gambia,Georgia,Germany,Ghana,Gibraltar,Greece,Greenland,Grenada,Guadeloupe,Guam,Guatemala,Guinea,Guinea-Bissau,Guyana,Haiti,Heard Island and McDonald Islands,Holy See (Vatican City State),Honduras,Hong Kong,Hungary,Iceland,India,Indonesia,Iran-Islamic Republic of IR,Iraq,Ireland,Israel,Italy,Jamaica,Japan,Jordan,Kazakhstan,Kenya,Kiribati,Korea-Democratic People’s Republic of KP,Korea-Republic of KR,Kuwait,Kyrgyzstan,Lao People’s Democratic Republic,Latvia,Lebanon,Leichenstein,Lesotho,Liberia,Libya,Liechtenstein,Lithuania,Luxembourg,Macao,Macedonia,Madagascar,Malawi,Malaysia,Maldives,Mali,Malta,Marshall Islands,Martinique,Mauritania,Mauritius,Mayotte,Mexico,Micronesia-Federated States of FM,Moldova-Republic of MD,Monaco,Mongolia,Montserrat,Morocco,Morovia,Mozambique,Myanmar,Namibia,Nauru,Nepal,Netherlands,New Caledonia,New Zealand,Nicaragua,Niger,Nigeria,Niue,Norfolk Island,Northern Ireland,Northern Mariana Islands,Norway,Oman,Pakistan,Palau,Panama,Papua New Guinea,Paraguay,Peru,Philippines,Pitcairn Islands,Poland,Portugal,Puerto Rico,Qatar,Reunion,Romania,Russia Federation,Rwanda,Saint Kitts and Nevis,Saint Lucia,Saint Pierre and Miquelon,Saint Vincent and the Grenadines,Samoa,Sao Tome and Principe,Saudi Arabia,Senegal,Seychelles,Sierra Leone,Singapore,Slovakia,Slovenia,Solomon Islands,Somalia,South Africa,South Georgia and the South Sandwich Islands,Spain,Sri Lanka,St Helena,Sudan,Suriname,Svalbard and Jan Mayen,Swaziland,Sweden,Switzerland,Syrian Arab Republic,Taiwan,Tajikistan,Tanzania-United Republic of TZ,Thailand,Timor-Leste,Togo,Tokelau,Tonga,Trinidad and Tobago,Tunisia,Turkey,Turkmenistan,Turks and Caicos Islands,Tuvalu,Uganda,Ukraine,United Arab Emirates,United Kingdom,United States,United States Minor Outlying Islands,Uruguay,Uzbekistan,Vanuatu,Venezuela,Vietnam,Virgin Islands British,Virgin Islands U.S.,Wallis & Futuna,Western Sahara,Yemen,Zambia,Zimbabwe',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 114,
            type_flag: 1,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '国籍',
                hint: '填写您的国籍',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 140559,
            type_flag: 1,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '证件号码',
                hint: '请填写证件号码',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 140560,
            type_flag: 1,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '中国内地居民身份证',
                hint: '请填写中国内地居民身份证',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 140555,
            type_flag: 3,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '证件类型',
                hint:
                  '请选择证件类型,港澳居民：香港/澳门旅客回乡证,台湾居民：台湾旅客台胞证,中国内地居民：身份证,其他出行人：护照',
                version: 26
              }
            ]
          },
          {
            extra_info_id: 140556,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '证件号码',
                hint: '请填写证件号码',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 140557,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '中国内地居民身份证',
                hint: '请填写中国内地居民身份证',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 142913,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: 'From',
                hint: 'From',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 142914,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: 'To',
                hint: 'To',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 142915,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: 'Message',
                hint: 'Message',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 142916,
            type_flag: 2,
            ask_option: 1,
            date_option: 2,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: 'Delivery date',
                hint: 'Delivery date,',
                version: 1
              }
            ]
          },
          {
            extra_info_id: 142344,
            type_flag: 3,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: []
          }
        ]
      },
      {
        category_id: '4',
        content: [
          {
            extra_info_id: 37,
            type_flag: 3,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '选择时段',
                hint: '选择喜好时段,早上,中午,下午,黄昏',
                version: 1
              }
            ]
          },
          {
            extra_info_id: 1,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '入住酒店及地址',
                hint: '请填写入住酒店名称及地址',
                version: 0
              }
            ]
          }
        ]
      },
      {
        category_id: '6',
        content: [
          {
            extra_info_id: 333,
            type_flag: 1,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '过敏信息',
                hint: '请输入敏感物质或无',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 385,
            type_flag: 3,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '语言选择',
                hint: '请选择,英文,日文',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 276,
            type_flag: 3,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '参与人数',
                hint: '请选择,1,2,3,4,5,6,7,8,9,10+',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 269,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '原始活动凭证号',
                hint: '填写如KLK01234567',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 166,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '备注',
                hint: "请填写特殊要求，或'无'",
                version: 0
              }
            ]
          },
          {
            extra_info_id: 8,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '喜好时间',
                hint: '注明喜好时间 (如：上午8点)',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 14,
            type_flag: 1,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '个别饮食需求',
                hint: '如素食或忌口',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 25,
            type_flag: 3,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '喜好时段',
                hint: '选择时段,上午,下午',
                version: 1
              }
            ]
          },
          {
            extra_info_id: 127,
            type_flag: 3,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '语言选择',
                hint: '请选择, 英文, 中文',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 130,
            type_flag: 3,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '语言选择',
                hint: '请选择, 英文,中文,日文',
                version: 0
              }
            ]
          }
        ]
      },
      {
        category_id: '2',
        content: [
          {
            extra_info_id: 30,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '接载地点',
                hint: '填写接载地点',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 29,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '接载时间',
                hint: '请注明接载时间 (例：08:00AM)',
                version: 1
              }
            ]
          },
          {
            extra_info_id: 27,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '接载时间',
                hint: '填写酒店/机场接载时间',
                version: 0
              }
            ]
          }
        ]
      },
      {
        category_id: '3',
        content: [
          {
            extra_info_id: 6,
            type_flag: 2,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '抵境日期',
                hint: '选择抵境日期',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 151,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '预订儿童座椅',
                hint: '填写所需数量，第一个免费',
                version: 1
              }
            ]
          },
          {
            extra_info_id: 32,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '航班编号 zhcn',
                hint: '填写航班编号,',
                version: 4
              }
            ]
          },
          {
            extra_info_id: 31,
            type_flag: 3,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '接送方向',
                hint: '选择所需接送方向, 抵境, 离境',
                version: 1
              }
            ]
          },
          {
            extra_info_id: 18,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '离境航班号码',
                hint: '填写离境航班号码',
                version: 1
              }
            ]
          },
          {
            extra_info_id: 7,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '抵境航班号码',
                hint: '填写抵境航班号码',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 5,
            type_flag: 2,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '离境日期',
                hint: '选择离境日期',
                version: 0
              }
            ]
          }
        ]
      },
      {
        category_id: '7',
        content: [
          {
            extra_info_id: 83,
            type_flag: 1,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '填写最近潜水日期',
                hint: '最近一次潜水的日期',
                version: 1
              }
            ]
          },
          {
            extra_info_id: 96,
            type_flag: 3,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '潜水证书程度',
                hint:
                  '请选择,浅水域 - Scuba Diver,开放水域 - Open Water Scuba Diver,资深开放水域 - Advanced Open Water Diver,拯救员 - Rescue Diver, 导师级 - Dive Master,专业指导员Instructor',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 105,
            type_flag: 1,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '体重 (磅/ 公斤)',
                hint: '填写您的体重',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 320,
            type_flag: 1,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '鞋码',
                hint: '填写鞋码(欧洲码，如：37码)',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 104,
            type_flag: 1,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '身高 (吋/厘米)',
                hint: '填写您的身高',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 93,
            type_flag: 1,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '鞋码 (cm)',
                hint: '填写鞋码以预备装备',
                version: 0
              }
            ]
          }
        ]
      },
      {
        category_id: '8',
        content: [
          {
            extra_info_id: 255,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '邮寄地址',
                hint: '住宅地址/酒店地址',
                version: 2
              }
            ]
          },
          {
            extra_info_id: 326,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '邮递区号',
                hint: '请输入邮递区号',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 315,
            type_flag: 1,
            ask_option: 1,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '收件人姓名',
                hint: '请填写,如陈红',
                version: 1
              }
            ]
          }
        ]
      },
      {
        category_id: '9',
        content: [
          {
            extra_info_id: 320,
            type_flag: 1,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '鞋码',
                hint: '填写鞋码(欧洲码，如：37码)',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 105,
            type_flag: 1,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '体重 (磅/ 公斤)',
                hint: '填写您的体重',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 104,
            type_flag: 1,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '身高 (吋/厘米)',
                hint: '填写您的身高',
                version: 0
              }
            ]
          },
          {
            extra_info_id: 93,
            type_flag: 1,
            ask_option: 2,
            date_option: 0,
            required: 1,
            version: 0,
            details: [
              {
                language: 'zh_CN',
                name: '鞋码 (cm)',
                hint: '填写鞋码以预备装备',
                version: 0
              }
            ]
          }
        ]
      }
    ]
  }
}
