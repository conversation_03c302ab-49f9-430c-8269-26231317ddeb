/* eslint-disable quotes */
export default {
  success: true,
  error: {
    code: '',
    message: ''
  },
  result: {
    package_id: 64080,
    old_styles: [
      {
        extra_info_id: 144322,
        category_id: '0',
        type_flag: 1,
        package_id: 64080,
        ask_option: 1,
        date_option: 0,
        required: 1,
        version: 1,
        position: 0,
        info: [
          {
            language: 'zh_CN',
            name: 'text-cn',
            hint: 'text-cn',
            version: 1
          },
          {
            language: 'en_US',
            name: 'text',
            hint: 'text',
            version: 1
          }
        ],
        is_from_new_style: false
      },
      {
        extra_info_id: 320,
        category_id: 'Foot Size(EU Repeat)',
        type_flag: 1,
        package_id: 0,
        ask_option: 2,
        date_option: 0,
        required: 1,
        version: 2,
        position: 0,
        info: [
          {
            language: 'zh_CN',
            name: '鞋码',
            hint: '填写鞋码(欧洲码，如：37码)',
            version: 0
          },
          {
            language: 'en_US',
            name: '<PERSON><PERSON> Si<PERSON> (EU)',
            hint: 'Enter European shoe size',
            version: 2
          }
        ],
        is_from_new_style: false
      },
      {
        extra_info_id: 105,
        category_id: 'Input weight (Repeat)',
        type_flag: 1,
        package_id: 0,
        ask_option: 2,
        date_option: 0,
        required: 1,
        version: 22,
        position: 0,
        info: [
          {
            language: 'zh_CN',
            name: '体重 (磅/ 公斤)',
            hint: '填写您的体重',
            version: 0
          },
          {
            language: 'en_US',
            name: 'Weight (kg)',
            hint: 'Enter weight',
            version: 4
          }
        ],
        is_from_new_style: false
      },
      {
        extra_info_id: 144595,
        category_id: '0',
        type_flag: 3,
        package_id: 64080,
        ask_option: 1,
        date_option: 0,
        required: 1,
        version: 0,
        position: 0,
        info: [
          {
            language: 'zh_CN',
            name: '下拉',
            hint: '下拉,下拉1,下拉2,下拉3',
            version: 0
          },
          {
            language: 'en_US',
            name: '下拉',
            hint: '下拉,下拉1,下拉2,下拉3',
            version: 0
          }
        ],
        is_from_new_style: false
      },
      {
        extra_info_id: 144696,
        category_id: '0',
        type_flag: 2,
        package_id: 64080,
        ask_option: 2,
        date_option: 4,
        required: 0,
        version: 4,
        position: 0,
        info: [
          {
            language: 'zh_CN',
            name: 'aaa',
            hint: 'aaa',
            version: 4
          },
          {
            language: 'en_US',
            name: 'a',
            hint: 'a',
            version: 4
          }
        ],
        is_from_new_style: false
      }
    ],
    new_styles: [
      {
        package_id: 64080,
        instance_datas: {
          groups: [
            {
              group_id: 3301,
              group_name: '名称',
              tips: '',
              info_type: 1,
              display_order: 0,
              is_multi: 0,
              write_level: 0,
              style: {
                fields: [
                  {
                    key: 'name_lang',
                    name: '指定书写语言',
                    hover: '预订时用户须使用所选语言填写姓名',
                    style: {
                      is_unique: 0,
                      required: 1,
                      type: 1,
                      multiple_selection: 0
                    },
                    options: {
                      type: 1,
                      all_value: {
                        type: 1,
                        value: -1
                      },
                      default: -1
                    },
                    value: -1
                  },
                  {
                    key: 'name_group',
                    name: '书写规范',
                    hover: '',
                    style: {
                      is_unique: 0,
                      required: 1,
                      type: 1,
                      multiple_selection: 0
                    },
                    options: {
                      type: 3,
                      all_value: {
                        type: 0,
                        value: null
                      },
                      default: 3303
                    },
                    value: 3303
                  }
                ]
              }
            },
            {
              group_id: 3304,
              group_name: '称谓',
              tips: '',
              info_type: 1,
              display_order: 3,
              is_multi: 0,
              write_level: 0,
              style: {
                fields: []
              }
            },
            {
              group_id: 3305,
              group_name: '国家/地区',
              tips: '',
              info_type: 1,
              display_order: 4,
              is_multi: 0,
              write_level: 0,
              style: {
                fields: [
                  {
                    key: 'accept_country',
                    name: '可选国家 / 地区',
                    hover: '活动的出行人籍贯须为所选的国家 / 地区',
                    style: {
                      is_unique: 0,
                      required: 1,
                      type: 1,
                      multiple_selection: 1
                    },
                    options: {
                      type: 2,
                      all_value: {
                        type: 1,
                        value: -1
                      },
                      default: -1
                    },
                    value: null
                  }
                ]
              }
            },
            {
              group_id: 3306,
              group_name: '出生日期',
              tips: '',
              info_type: 1,
              display_order: 5,
              is_multi: 0,
              write_level: 0,
              style: {
                fields: []
              }
            },
            {
              group_id: 3307,
              group_name: '年龄',
              tips: '',
              info_type: 1,
              display_order: 6,
              is_multi: 0,
              write_level: 0,
              style: {
                fields: []
              }
            },
            {
              group_id: 3308,
              group_name: '手机号码',
              tips: '接口产品使用配置区号的新功能需要用户端开发支持新样式',
              info_type: 1,
              display_order: 7,
              is_multi: 0,
              write_level: 0,
              style: {
                fields: [
                  {
                    key: 'accept_country_num',
                    name: '可选国家 / 地区区号',
                    hover: '用户填写的联系电话代码须为所选的国家 / 地区',
                    style: {
                      is_unique: 0,
                      required: 1,
                      type: 1,
                      multiple_selection: 1
                    },
                    options: {
                      type: 2,
                      all_value: {
                        type: 1,
                        value: -1
                      },
                      default: -1
                    },
                    value: null
                  }
                ]
              }
            },
            {
              group_id: 3309,
              group_name: '邮箱',
              tips: '',
              info_type: 1,
              display_order: 8,
              is_multi: 0,
              write_level: 0,
              style: {
                fields: []
              }
            },
            {
              group_id: 3310,
              group_name: '证件',
              tips: '',
              info_type: 1,
              display_order: 9,
              is_multi: 0,
              write_level: 0,
              style: {
                fields: [
                  {
                    key: 'identification_type',
                    name: '证件类型',
                    hover: '如勾选多个证件类型, 则用户仅需选一个类型填写信息',
                    style: {
                      is_unique: 0,
                      required: 1,
                      type: 1,
                      multiple_selection: 1
                    },
                    options: {
                      type: 3,
                      all_value: {
                        type: 1,
                        value: -1
                      },
                      default: 3311
                    },
                    value: [3311]
                  },
                  {
                    key: 'identification_info',
                    name: '证件信息',
                    hover: '',
                    style: {
                      is_unique: 0,
                      required: 1,
                      type: 1,
                      multiple_selection: 1
                    },
                    options: {
                      type: 5,
                      all_value: {
                        type: 0,
                        value: null
                      },
                      items: [
                        {
                          key: 'identification_info_id',
                          name: '证件号码'
                        },
                        {
                          key: 'identification_info_date_issued',
                          name: '签发日期'
                        },
                        {
                          key: 'identification_info_expiration_date',
                          name: '有效期至'
                        },
                        {
                          key: 'identification_info_issuing_place',
                          name: '签发地点'
                        }
                      ],
                      default: 'identification_info_id'
                    },
                    value: [
                      'identification_info_id',
                      'identification_info_date_issued',
                      'identification_info_expiration_date',
                      'identification_info_issuing_place'
                    ]
                  }
                ]
              }
            },
            {
              group_id: 3325,
              group_name: '通讯应用',
              tips: '',
              info_type: 1,
              display_order: 24,
              is_multi: 0,
              write_level: 0,
              style: {
                fields: [
                  {
                    key: 'contact_way',
                    name: '通讯应用',
                    hover: '如勾选多个通讯应用, 则用户仅需选一个类型填写帐号',
                    style: {
                      is_unique: 0,
                      required: 1,
                      type: 1,
                      multiple_selection: 1
                    },
                    options: {
                      type: 4,
                      all_value: {
                        type: 1,
                        value: -1
                      },
                      default: -1
                    },
                    value: null
                  }
                ]
              }
            },
            {
              group_id: 3326,
              group_name: '偏好语言',
              tips: '',
              info_type: 0,
              display_order: 25,
              is_multi: 0,
              write_level: 0,
              style: {
                fields: [
                  {
                    key: 'lang_write_type',
                    name: '用户填写形式',
                    hover: '',
                    style: {
                      is_unique: 0,
                      required: 1,
                      type: 14,
                      multiple_selection: 0
                    },
                    options: {
                      type: 5,
                      all_value: {
                        type: 0,
                        value: null
                      },
                      items: [
                        {
                          key: 'lang_select_multi',
                          name: '多选'
                        },
                        {
                          key: 'lang_select',
                          name: '单选'
                        }
                      ],
                      default: 'lang_select'
                    },
                    value: null
                  },
                  {
                    key: 'lang_items',
                    name: '用户选项',
                    hover: '',
                    style: {
                      is_unique: 0,
                      required: 1,
                      type: 1,
                      multiple_selection: 1
                    },
                    options: {
                      type: 1,
                      all_value: {
                        type: 1,
                        value: -1
                      },
                      default: -1
                    },
                    value: null
                  },
                  {
                    key: 'lang_hint',
                    name: '用户填写指引',
                    hover: '提供简短指引, 帮助用户预订时填写所需信息',
                    style: {
                      is_unique: 0,
                      required: 0,
                      type: 2,
                      multiple_selection: 0
                    },
                    options: {
                      type: 6,
                      all_value: {
                        type: 0,
                        value: null
                      }
                    },
                    value: null
                  }
                ]
              }
            },
            {
              group_id: 3327,
              group_name: '特殊备注',
              tips: '',
              info_type: 0,
              display_order: 26,
              is_multi: 1,
              write_level: 0,
              style: {
                fields: [
                  {
                    key: 'note_type',
                    name: '选择备注类型',
                    hover: '',
                    style: {
                      is_unique: 1,
                      required: 1,
                      type: 1,
                      multiple_selection: 0
                    },
                    options: {
                      type: 4,
                      all_value: {
                        type: 0,
                        value: null
                      },
                      default: 10010078
                    },
                    value: null
                  },
                  {
                    key: 'note_write_type',
                    name: '用户填写形式',
                    hover: '',
                    style: {
                      is_unique: 0,
                      required: 1,
                      type: 14,
                      multiple_selection: 0
                    },
                    options: {
                      type: 5,
                      all_value: {
                        type: 0,
                        value: null
                      },
                      items: [
                        {
                          key: 'note_write_type_text',
                          name: '文本'
                        }
                      ],
                      default: 'note_write_type_text'
                    },
                    value: null
                  },
                  {
                    key: 'note_hint',
                    name: '用户填写指引',
                    hover: '提供简短指引, 帮助用户预订时填写所需信息',
                    style: {
                      is_unique: 0,
                      required: 0,
                      type: 2,
                      multiple_selection: 0
                    },
                    options: {
                      type: 6,
                      all_value: {
                        type: 0,
                        value: null
                      }
                    },
                    value: null
                  },
                  {
                    key: 'note_required',
                    name: '是否为选填',
                    hover: '',
                    style: {
                      is_unique: 0,
                      required: 0,
                      type: 14,
                      multiple_selection: 1
                    },
                    options: {
                      type: 5,
                      all_value: {
                        type: 0,
                        value: null
                      },
                      items: [
                        {
                          key: 'note_required_optional',
                          name: '选填'
                        }
                      ],
                      default: 'note_required_optional'
                    },
                    value: null
                  }
                ]
              }
            },
            {
              group_id: 3328,
              group_name: '参与人数',
              tips: '',
              info_type: 0,
              display_order: 27,
              is_multi: 1,
              write_level: 0,
              style: {
                fields: [
                  {
                    key: 'participant_type',
                    name: '参与者类型',
                    hover: '',
                    style: {
                      is_unique: 1,
                      required: 1,
                      type: 1,
                      multiple_selection: 0
                    },
                    options: {
                      type: 4,
                      all_value: {
                        type: 0,
                        value: null
                      },
                      default: 10010081
                    },
                    value: null
                  },
                  {
                    key: 'participant_write_type',
                    name: '用户填写形式',
                    hover: '',
                    style: {
                      is_unique: 0,
                      required: 1,
                      type: 14,
                      multiple_selection: 0
                    },
                    options: {
                      type: 5,
                      all_value: {
                        type: 0,
                        value: null
                      },
                      items: [
                        {
                          key: 'participant_write_type_number',
                          name: '数字'
                        }
                      ],
                      default: 'participant_write_type_number'
                    },
                    value: null
                  },
                  {
                    key: 'participant_number_range',
                    name: '数字范围',
                    hover: '',
                    style: {
                      is_unique: 0,
                      required: 1,
                      type: 1,
                      multiple_selection: 0
                    },
                    options: {
                      type: 7,
                      all_value: {
                        type: 0,
                        value: null
                      },
                      items: [
                        {
                          key: 'min_num',
                          name: '',
                          hint: 'Min.',
                          options: {
                            min: 0,
                            max: 100
                          },
                          default: 1
                        },
                        {
                          key: 'max_num',
                          name: '',
                          hint: 'Max.',
                          options: {
                            min: 1,
                            max: 100
                          },
                          default: 100
                        }
                      ]
                    },
                    value: null
                  },
                  {
                    key: 'participant_hint',
                    name: '用户填写指引',
                    hover: '提供简短指引, 帮助用户预订时填写所需信息',
                    style: {
                      is_unique: 0,
                      required: 0,
                      type: 2,
                      multiple_selection: 0
                    },
                    options: {
                      type: 6,
                      all_value: {
                        type: 0,
                        value: null
                      }
                    },
                    value: null
                  }
                ]
              }
            }
          ],
          group_id_list: [3301, 3304, 3306, 3310]
        }
      }
    ],
    support_new_otherinfo: {
      package_id: 64080,
      has_new_otherinfo: true,
      support_new_otherinfo: true,
      package_status: {
        is_in_white_list: true,
        is_in_black_list: false,
        is_combo_package: false,
        is_combo_alone_package: false,
        combo_info: {
          combo_activity_id: 0,
          combo_package_id: 0,
          alone_activity_id: 0,
          alone_package_id: 0
        },
        is_api: false,
        stock_out_type: 'KLOOKCODE',
        api_detail: '',
        identity_types: []
      }
    }
  }
}
