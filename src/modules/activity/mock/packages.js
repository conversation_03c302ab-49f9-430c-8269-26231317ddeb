/* eslint-disable quotes */
export default {
  error: {
    code: '',
    message: ''
  },
  result: {
    voucher_status: 0,
    package_info: [
      {
        package_id: 73041,
        max_participants: 9999,
        min_participants: 1,
        location_info: '',
        reconfirmation: false,
        type: 1,
        published: false,
        inv_type: 0,
        priority: 0,
        min_trip: 1,
        max_trip: 255,
        data_status: 0,
        price_status: 0,
        tips: '111111',
        e_levy: 0,
        escrow_guarantee: false,
        package_details: [
          {
            language_type: 'en_US',
            name: '111111',
            subname: '',
            desc: '',
            policy: '',
            guideline: {
              confirmation_details: ''
            },
            usage: {
              voucher_validity: '',
              voucher_type_desc: '',
              redemption_process: '',
              voucher_identity: '',
              open_hours: '',
              pick_up_procedure: '',
              transportation: '',
              device_usage: '',
              meet_up_location: '',
              pick_up_location: '',
              surcharge: '',
              location: ''
            }
          },
          {
            language_type: 'zh_CN',
            name: '111111',
            subname: '',
            desc: '',
            policy: '',
            guideline: null,
            usage: null
          }
        ],
        auto_published: 0,
        auto_publish_time: {
          id: 13996,
          package_id: 73041,
          published: 0,
          unpublished: 0,
          published_time: '0001-01-01 00:00:00',
          unpublished_time: '0001-01-01 00:00:00'
        },
        spec_status: 0,
        voucher_type: 1,
        voucher_get_method: 1,
        merchant_confirm_type: 0,
        merchant_id: 3660,
        stock_out_type: 'INVENTORY',
        transferable: 0,
        sales_channels: 0,
        redemption_type: -1,
        ticket_type: 0,
        voucher_usage: -1,
        voucher_level: 'UniqueVoucher',
        cancelation_type: 0,
        confirmation_type: 1,
        merchant_self_confirm: 0,
        merchant_confirm_col: 1,
        usage_images_all: [
          {
            language_type: 'zh_HK',
            image: []
          },
          {
            language_type: 'en_PH',
            image: []
          },
          {
            language_type: 'en_HK',
            image: []
          },
          {
            language_type: 'en_CA',
            image: []
          },
          {
            language_type: 'id_ID',
            image: []
          },
          {
            language_type: 'th_TH',
            image: []
          },
          {
            language_type: 'en_GB',
            image: []
          },
          {
            language_type: 'es_ES',
            image: []
          },
          {
            language_type: 'it_IT',
            image: []
          },
          {
            language_type: 'ru_RU',
            image: []
          },
          {
            language_type: 'de_DE',
            image: []
          },
          {
            language_type: 'en_BS',
            image: []
          },
          {
            language_type: 'zh_TW',
            image: []
          },
          {
            language_type: 'en_NZ',
            image: []
          },
          {
            language_type: 'en_AU',
            image: []
          },
          {
            language_type: 'ja_JP',
            image: []
          },
          {
            language_type: 'fr_FR',
            image: []
          },
          {
            language_type: 'en_MY',
            image: []
          },
          {
            language_type: 'en_IN',
            image: []
          },
          {
            language_type: 'ALL',
            image: []
          },
          {
            language_type: 'en_US',
            image: []
          },
          {
            language_type: 'zh_CN',
            image: []
          },
          {
            language_type: 'ko_KR',
            image: []
          },
          {
            language_type: 'vi_VN',
            image: []
          },
          {
            language_type: 'en_SG',
            image: []
          }
        ],
        history_published: false,
        merchant_contact: null,
        sensitive_info: ''
      },
      {
        package_id: 72672,
        max_participants: 9999,
        min_participants: 1,
        location_info: '',
        reconfirmation: false,
        type: 1,
        published: true,
        inv_type: 0,
        priority: 0,
        min_trip: 1,
        max_trip: 255,
        data_status: 0,
        price_status: 0,
        tips: '主套餐',
        e_levy: 0,
        escrow_guarantee: false,
        package_details: [
          {
            language_type: 'en_US',
            name: '主套餐',
            subname: '',
            desc: '',
            policy: '',
            guideline: {
              confirmation_details: ''
            },
            usage: {
              voucher_validity: '',
              voucher_type_desc: '',
              redemption_process: '',
              voucher_identity: '',
              open_hours: '',
              pick_up_procedure: '',
              transportation: '',
              device_usage: '',
              meet_up_location: '',
              pick_up_location: '',
              surcharge: '',
              location: ''
            }
          },
          {
            language_type: 'zh_CN',
            name: '固定价格模型',
            subname: '',
            desc: '',
            policy: '',
            guideline: {
              confirmation_details: ''
            },
            usage: {
              voucher_validity: '',
              voucher_type_desc: '',
              redemption_process: '',
              voucher_identity: '',
              open_hours: '',
              pick_up_procedure: '',
              transportation: '',
              device_usage: '',
              meet_up_location: '',
              pick_up_location: '',
              surcharge: '',
              location: ''
            }
          }
        ],
        auto_published: 0,
        auto_publish_time: {
          id: 13636,
          package_id: 72672,
          published: 0,
          unpublished: 0,
          published_time: '0001-01-01 00:00:00',
          unpublished_time: '0001-01-01 00:00:00'
        },
        spec_status: 0,
        voucher_type: 0,
        voucher_get_method: 1,
        merchant_confirm_type: 0,
        merchant_id: 1268,
        stock_out_type: 'KLOOKCODE',
        transferable: 0,
        sales_channels: 0,
        redemption_type: -1,
        ticket_type: 0,
        voucher_usage: -1,
        voucher_level: 'UniqueVoucher',
        cancelation_type: 0,
        confirmation_type: 1,
        merchant_self_confirm: 0,
        merchant_confirm_col: 1,
        usage_images_all: [
          {
            language_type: 'vi_VN',
            image: []
          },
          {
            language_type: 'en_SG',
            image: []
          },
          {
            language_type: 'it_IT',
            image: []
          },
          {
            language_type: 'ru_RU',
            image: []
          },
          {
            language_type: 'en_AU',
            image: []
          },
          {
            language_type: 'en_NZ',
            image: []
          },
          {
            language_type: 'es_ES',
            image: []
          },
          {
            language_type: 'id_ID',
            image: []
          },
          {
            language_type: 'en_MY',
            image: []
          },
          {
            language_type: 'zh_CN',
            image: []
          },
          {
            language_type: 'zh_TW',
            image: []
          },
          {
            language_type: 'zh_HK',
            image: []
          },
          {
            language_type: 'en_PH',
            image: []
          },
          {
            language_type: 'en_CA',
            image: []
          },
          {
            language_type: 'ja_JP',
            image: []
          },
          {
            language_type: 'en_HK',
            image: []
          },
          {
            language_type: 'en_IN',
            image: []
          },
          {
            language_type: 'ko_KR',
            image: []
          },
          {
            language_type: 'th_TH',
            image: []
          },
          {
            language_type: 'en_GB',
            image: []
          },
          {
            language_type: 'fr_FR',
            image: []
          },
          {
            language_type: 'en_BS',
            image: []
          },
          {
            language_type: 'ALL',
            image: []
          },
          {
            language_type: 'en_US',
            image: []
          },
          {
            language_type: 'de_DE',
            image: []
          }
        ],
        history_published: true,
        merchant_contact: null,
        sensitive_info: ''
      },
      {
        package_id: 72673,
        max_participants: 9999,
        min_participants: 1,
        location_info: '',
        reconfirmation: false,
        type: 1,
        published: true,
        inv_type: 0,
        priority: 0,
        min_trip: 1,
        max_trip: 255,
        data_status: 0,
        price_status: 0,
        tips: '子套餐1',
        e_levy: 0,
        escrow_guarantee: false,
        package_details: [
          {
            language_type: 'en_US',
            name: '主套餐',
            subname: '',
            desc: '',
            policy: '',
            guideline: {
              confirmation_details: ''
            },
            usage: {
              voucher_validity: '',
              voucher_type_desc: '',
              redemption_process: '',
              voucher_identity: '',
              open_hours: '',
              pick_up_procedure: '',
              transportation: '',
              device_usage: '',
              meet_up_location: '',
              pick_up_location: '',
              surcharge: '',
              location: ''
            }
          },
          {
            language_type: 'zh_CN',
            name: '固定价格模型',
            subname: '',
            desc: '',
            policy: '',
            guideline: {
              confirmation_details: ''
            },
            usage: {
              voucher_validity: '',
              voucher_type_desc: '',
              redemption_process: '',
              voucher_identity: '',
              open_hours: '',
              pick_up_procedure: '',
              transportation: '',
              device_usage: '',
              meet_up_location: '',
              pick_up_location: '',
              surcharge: '',
              location: ''
            }
          }
        ],
        auto_published: 0,
        auto_publish_time: {
          id: 13637,
          package_id: 72673,
          published: 0,
          unpublished: 0,
          published_time: '0001-01-01 00:00:00',
          unpublished_time: '0001-01-01 00:00:00'
        },
        spec_status: 0,
        voucher_type: 0,
        voucher_get_method: 1,
        merchant_confirm_type: 0,
        merchant_id: 1268,
        stock_out_type: 'KLOOKCODE',
        transferable: 0,
        sales_channels: 0,
        redemption_type: -1,
        ticket_type: 0,
        voucher_usage: -1,
        voucher_level: 'UniqueVoucher',
        cancelation_type: 0,
        confirmation_type: 1,
        merchant_self_confirm: 0,
        merchant_confirm_col: 1,
        usage_images_all: [
          {
            language_type: 'ko_KR',
            image: []
          },
          {
            language_type: 'en_PH',
            image: []
          },
          {
            language_type: 'it_IT',
            image: []
          },
          {
            language_type: 'vi_VN',
            image: []
          },
          {
            language_type: 'zh_HK',
            image: []
          },
          {
            language_type: 'en_GB',
            image: []
          },
          {
            language_type: 'en_AU',
            image: []
          },
          {
            language_type: 'en_CA',
            image: []
          },
          {
            language_type: 'ja_JP',
            image: []
          },
          {
            language_type: 'en_HK',
            image: []
          },
          {
            language_type: 'en_MY',
            image: []
          },
          {
            language_type: 'ALL',
            image: []
          },
          {
            language_type: 'en_US',
            image: []
          },
          {
            language_type: 'zh_TW',
            image: []
          },
          {
            language_type: 'en_NZ',
            image: []
          },
          {
            language_type: 'es_ES',
            image: []
          },
          {
            language_type: 'de_DE',
            image: []
          },
          {
            language_type: 'fr_FR',
            image: []
          },
          {
            language_type: 'en_IN',
            image: []
          },
          {
            language_type: 'zh_CN',
            image: []
          },
          {
            language_type: 'th_TH',
            image: []
          },
          {
            language_type: 'ru_RU',
            image: []
          },
          {
            language_type: 'id_ID',
            image: []
          },
          {
            language_type: 'en_SG',
            image: []
          },
          {
            language_type: 'en_BS',
            image: []
          }
        ],
        history_published: true,
        merchant_contact: null,
        sensitive_info: ''
      },
      {
        package_id: 72674,
        max_participants: 9999,
        min_participants: 1,
        location_info: '',
        reconfirmation: false,
        type: 1,
        published: true,
        inv_type: 0,
        priority: 0,
        min_trip: 1,
        max_trip: 255,
        data_status: 0,
        price_status: 0,
        tips: '子套餐2',
        e_levy: 0,
        escrow_guarantee: false,
        package_details: [
          {
            language_type: 'en_US',
            name: '主套餐',
            subname: '',
            desc: '',
            policy: '',
            guideline: {
              confirmation_details: ''
            },
            usage: {
              voucher_validity: '',
              voucher_type_desc: '',
              redemption_process: '',
              voucher_identity: '',
              open_hours: '',
              pick_up_procedure: '',
              transportation: '',
              device_usage: '',
              meet_up_location: '',
              pick_up_location: '',
              surcharge: '',
              location: ''
            }
          },
          {
            language_type: 'zh_CN',
            name: '固定价格模型',
            subname: '',
            desc: '',
            policy: '',
            guideline: {
              confirmation_details: ''
            },
            usage: {
              voucher_validity: '',
              voucher_type_desc: '',
              redemption_process: '',
              voucher_identity: '',
              open_hours: '',
              pick_up_procedure: '',
              transportation: '',
              device_usage: '',
              meet_up_location: '',
              pick_up_location: '',
              surcharge: '',
              location: ''
            }
          }
        ],
        auto_published: 1,
        auto_publish_time: {
          id: 13638,
          package_id: 72674,
          published: 0,
          unpublished: 1,
          published_time: '2021-01-20 17:00:00',
          unpublished_time: '2021-01-20 17:59:16'
        },
        spec_status: 0,
        voucher_type: 0,
        voucher_get_method: 1,
        merchant_confirm_type: 0,
        merchant_id: 1268,
        stock_out_type: 'KLOOKCODE',
        transferable: 0,
        sales_channels: 0,
        redemption_type: -1,
        ticket_type: 0,
        voucher_usage: -1,
        voucher_level: 'UniqueVoucher',
        cancelation_type: 0,
        confirmation_type: 1,
        merchant_self_confirm: 0,
        merchant_confirm_col: 1,
        usage_images_all: [
          {
            language_type: 'zh_HK',
            image: []
          },
          {
            language_type: 'en_NZ',
            image: []
          },
          {
            language_type: 'es_ES',
            image: []
          },
          {
            language_type: 'ru_RU',
            image: []
          },
          {
            language_type: 'de_DE',
            image: []
          },
          {
            language_type: 'id_ID',
            image: []
          },
          {
            language_type: 'en_HK',
            image: []
          },
          {
            language_type: 'th_TH',
            image: []
          },
          {
            language_type: 'en_BS',
            image: []
          },
          {
            language_type: 'en_MY',
            image: []
          },
          {
            language_type: 'en_GB',
            image: []
          },
          {
            language_type: 'en_PH',
            image: []
          },
          {
            language_type: 'en_CA',
            image: []
          },
          {
            language_type: 'fr_FR',
            image: []
          },
          {
            language_type: 'zh_TW',
            image: []
          },
          {
            language_type: 'ko_KR',
            image: []
          },
          {
            language_type: 'en_AU',
            image: []
          },
          {
            language_type: 'en_SG',
            image: []
          },
          {
            language_type: 'en_IN',
            image: []
          },
          {
            language_type: 'ALL',
            image: []
          },
          {
            language_type: 'zh_CN',
            image: []
          },
          {
            language_type: 'it_IT',
            image: []
          },
          {
            language_type: 'ja_JP',
            image: []
          },
          {
            language_type: 'vi_VN',
            image: []
          },
          {
            language_type: 'en_US',
            image: []
          }
        ],
        history_published: true,
        merchant_contact: null,
        sensitive_info: ''
      },
      {
        package_id: 72675,
        max_participants: 9999,
        min_participants: 1,
        location_info: '',
        reconfirmation: false,
        type: 1,
        published: false,
        inv_type: 0,
        priority: 0,
        min_trip: 1,
        max_trip: 255,
        data_status: 0,
        price_status: 1,
        tips: '主套餐2',
        e_levy: 0,
        escrow_guarantee: false,
        package_details: [
          {
            language_type: 'en_US',
            name: '主套餐2',
            subname: '',
            desc: '',
            policy: '',
            guideline: {
              confirmation_details: ''
            },
            usage: {
              voucher_validity: '',
              voucher_type_desc: '',
              redemption_process: '',
              voucher_identity: '',
              open_hours: '',
              pick_up_procedure: '',
              transportation: '',
              device_usage: '',
              meet_up_location: '',
              pick_up_location: '',
              surcharge: '',
              location: ''
            }
          },
          {
            language_type: 'zh_CN',
            name: '主套餐2',
            subname: '',
            desc: '',
            policy: '',
            guideline: {
              confirmation_details: ''
            },
            usage: {
              voucher_validity: '',
              voucher_type_desc: '',
              redemption_process: '',
              voucher_identity: '',
              open_hours: '',
              pick_up_procedure: '',
              transportation: '',
              device_usage: '',
              meet_up_location: '',
              pick_up_location: '',
              surcharge: '',
              location: ''
            }
          }
        ],
        auto_published: 0,
        auto_publish_time: {
          id: 13639,
          package_id: 72675,
          published: 0,
          unpublished: 0,
          published_time: '0001-01-01 00:00:00',
          unpublished_time: '2021-01-20 16:47:00'
        },
        spec_status: 0,
        voucher_type: 0,
        voucher_get_method: 1,
        merchant_confirm_type: 0,
        merchant_id: 1268,
        stock_out_type: 'KLOOKCODE',
        transferable: 0,
        sales_channels: 0,
        redemption_type: -1,
        ticket_type: 0,
        voucher_usage: -1,
        voucher_level: 'UniqueVoucher',
        cancelation_type: 0,
        confirmation_type: 1,
        merchant_self_confirm: 0,
        merchant_confirm_col: 1,
        usage_images_all: [
          {
            language_type: 'it_IT',
            image: []
          },
          {
            language_type: 'vi_VN',
            image: []
          },
          {
            language_type: 'en_NZ',
            image: []
          },
          {
            language_type: 'es_ES',
            image: []
          },
          {
            language_type: 'en_GB',
            image: []
          },
          {
            language_type: 'en_PH',
            image: []
          },
          {
            language_type: 'ru_RU',
            image: []
          },
          {
            language_type: 'de_DE',
            image: []
          },
          {
            language_type: 'en_CA',
            image: []
          },
          {
            language_type: 'fr_FR',
            image: []
          },
          {
            language_type: 'zh_TW',
            image: []
          },
          {
            language_type: 'th_TH',
            image: []
          },
          {
            language_type: 'en_IN',
            image: []
          },
          {
            language_type: 'en_BS',
            image: []
          },
          {
            language_type: 'zh_HK',
            image: []
          },
          {
            language_type: 'ja_JP',
            image: []
          },
          {
            language_type: 'en_HK',
            image: []
          },
          {
            language_type: 'en_MY',
            image: []
          },
          {
            language_type: 'ALL',
            image: []
          },
          {
            language_type: 'en_US',
            image: []
          },
          {
            language_type: 'en_AU',
            image: []
          },
          {
            language_type: 'id_ID',
            image: []
          },
          {
            language_type: 'en_SG',
            image: []
          },
          {
            language_type: 'zh_CN',
            image: []
          },
          {
            language_type: 'ko_KR',
            image: []
          }
        ],
        history_published: true,
        merchant_contact: null,
        sensitive_info: ''
      },
      {
        package_id: 72676,
        max_participants: 9999,
        min_participants: 1,
        location_info: '',
        reconfirmation: false,
        type: 1,
        published: false,
        inv_type: 0,
        priority: 0,
        min_trip: 1,
        max_trip: 255,
        data_status: 0,
        price_status: 0,
        tips: '价格日历1',
        e_levy: 0,
        escrow_guarantee: false,
        package_details: [
          {
            language_type: 'en_US',
            name: '价格日历1',
            subname: '',
            desc: '',
            policy: '',
            guideline: {
              confirmation_details: ''
            },
            usage: {
              voucher_validity: '',
              voucher_type_desc: '',
              redemption_process: '',
              voucher_identity: '',
              open_hours: '',
              pick_up_procedure: '',
              transportation: '',
              device_usage: '',
              meet_up_location: '',
              pick_up_location: '',
              surcharge: '',
              location: ''
            }
          },
          {
            language_type: 'zh_CN',
            name: '价格日历1',
            subname: '',
            desc: '',
            policy: '',
            guideline: null,
            usage: null
          }
        ],
        auto_published: 0,
        auto_publish_time: {
          id: 13640,
          package_id: 72676,
          published: 0,
          unpublished: 0,
          published_time: '0001-01-01 00:00:00',
          unpublished_time: '0001-01-01 00:00:00'
        },
        spec_status: 0,
        voucher_type: 0,
        voucher_get_method: 1,
        merchant_confirm_type: 0,
        merchant_id: 1268,
        stock_out_type: 'KLOOKCODE',
        transferable: 0,
        sales_channels: 0,
        redemption_type: -1,
        ticket_type: 0,
        voucher_usage: -1,
        voucher_level: 'UniqueVoucher',
        cancelation_type: 0,
        confirmation_type: 1,
        merchant_self_confirm: 0,
        merchant_confirm_col: 1,
        usage_images_all: [
          {
            language_type: 'ja_JP',
            image: []
          },
          {
            language_type: 'zh_TW',
            image: []
          },
          {
            language_type: 'zh_HK',
            image: []
          },
          {
            language_type: 'es_ES',
            image: []
          },
          {
            language_type: 'it_IT',
            image: []
          },
          {
            language_type: 'ru_RU',
            image: []
          },
          {
            language_type: 'de_DE',
            image: []
          },
          {
            language_type: 'en_AU',
            image: []
          },
          {
            language_type: 'id_ID',
            image: []
          },
          {
            language_type: 'vi_VN',
            image: []
          },
          {
            language_type: 'en_BS',
            image: []
          },
          {
            language_type: 'ALL',
            image: []
          },
          {
            language_type: 'zh_CN',
            image: []
          },
          {
            language_type: 'ko_KR',
            image: []
          },
          {
            language_type: 'th_TH',
            image: []
          },
          {
            language_type: 'en_PH',
            image: []
          },
          {
            language_type: 'fr_FR',
            image: []
          },
          {
            language_type: 'en_IN',
            image: []
          },
          {
            language_type: 'en_US',
            image: []
          },
          {
            language_type: 'en_NZ',
            image: []
          },
          {
            language_type: 'en_GB',
            image: []
          },
          {
            language_type: 'en_CA',
            image: []
          },
          {
            language_type: 'en_SG',
            image: []
          },
          {
            language_type: 'en_HK',
            image: []
          },
          {
            language_type: 'en_MY',
            image: []
          }
        ],
        history_published: true,
        merchant_contact: null,
        sensitive_info: ''
      },
      {
        package_id: 72677,
        max_participants: 9999,
        min_participants: 1,
        location_info: '',
        reconfirmation: false,
        type: 1,
        published: true,
        inv_type: 0,
        priority: 0,
        min_trip: 1,
        max_trip: 255,
        data_status: 0,
        price_status: 1,
        tips: '价格日历2',
        e_levy: 0,
        escrow_guarantee: false,
        package_details: [
          {
            language_type: 'en_US',
            name: '价格日历2',
            subname: '',
            desc: '',
            policy: '',
            guideline: {
              confirmation_details: ''
            },
            usage: {
              voucher_validity: '',
              voucher_type_desc: '',
              redemption_process: '',
              voucher_identity: '',
              open_hours: '',
              pick_up_procedure: '',
              transportation: '',
              device_usage: '',
              meet_up_location: '',
              pick_up_location: '',
              surcharge: '',
              location: ''
            }
          },
          {
            language_type: 'zh_CN',
            name: '价格日历2',
            subname: '',
            desc: '',
            policy: '',
            guideline: {
              confirmation_details: ''
            },
            usage: {
              voucher_validity: '',
              voucher_type_desc: '',
              redemption_process: '',
              voucher_identity: '',
              open_hours: '',
              pick_up_procedure: '',
              transportation: '',
              device_usage: '',
              meet_up_location: '',
              pick_up_location: '',
              surcharge: '',
              location: ''
            }
          }
        ],
        auto_published: 0,
        auto_publish_time: {
          id: 13641,
          package_id: 72677,
          published: 0,
          unpublished: 0,
          published_time: '0001-01-01 00:00:00',
          unpublished_time: '0001-01-01 00:00:00'
        },
        spec_status: 0,
        voucher_type: 0,
        voucher_get_method: 1,
        merchant_confirm_type: 0,
        merchant_id: 1268,
        stock_out_type: 'KLOOKCODE',
        transferable: 0,
        sales_channels: 0,
        redemption_type: -1,
        ticket_type: 2,
        voucher_usage: -1,
        voucher_level: 'UniqueVoucher',
        cancelation_type: 0,
        confirmation_type: 1,
        merchant_self_confirm: 0,
        merchant_confirm_col: 1,
        usage_images_all: [
          {
            language_type: 'vi_VN',
            image: []
          },
          {
            language_type: 'fr_FR',
            image: []
          },
          {
            language_type: 'zh_CN',
            image: []
          },
          {
            language_type: 'ko_KR',
            image: []
          },
          {
            language_type: 'en_NZ',
            image: []
          },
          {
            language_type: 'es_ES',
            image: []
          },
          {
            language_type: 'ja_JP',
            image: []
          },
          {
            language_type: 'id_ID',
            image: []
          },
          {
            language_type: 'en_IN',
            image: []
          },
          {
            language_type: 'en_BS',
            image: []
          },
          {
            language_type: 'ALL',
            image: []
          },
          {
            language_type: 'en_US',
            image: []
          },
          {
            language_type: 'it_IT',
            image: []
          },
          {
            language_type: 'de_DE',
            image: []
          },
          {
            language_type: 'en_CA',
            image: []
          },
          {
            language_type: 'en_HK',
            image: []
          },
          {
            language_type: 'en_PH',
            image: []
          },
          {
            language_type: 'ru_RU',
            image: []
          },
          {
            language_type: 'en_AU',
            image: []
          },
          {
            language_type: 'en_MY',
            image: []
          },
          {
            language_type: 'zh_TW',
            image: []
          },
          {
            language_type: 'th_TH',
            image: []
          },
          {
            language_type: 'zh_HK',
            image: []
          },
          {
            language_type: 'en_GB',
            image: []
          },
          {
            language_type: 'en_SG',
            image: []
          }
        ],
        history_published: true,
        merchant_contact: null,
        sensitive_info: ''
      }
    ],
    server_time: '2021-01-25 10:12:41'
  },
  success: true
}
