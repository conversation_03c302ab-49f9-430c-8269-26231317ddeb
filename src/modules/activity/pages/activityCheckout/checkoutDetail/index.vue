<template>
  <div>
    <a-button type="link" @click="goBack"> <a-icon type="left" />{{ $t('45673') }} </a-button>
    <div class="ck-title">{{ $t('45674') }}</div>
    <a-steps :current="steps" size="default" class="steps">
      <a-step :title="$t('45675')" />
      <a-step :title="$t('45676')" />
    </a-steps>

    <!-- search form -->
    <a-form-model v-show="steps == 0" class="form" :rules="searchFormRules" :model="searchForm">
      <a-form-model-item :label="$t('28010')" prop="activity_id">
        <a-popover placement="rightTop" trigger="hover" :auto-adjust-overflow="false">
          <template slot="content">
            <div class="pop-tips" v-html="$t('45689')"></div>
          </template>
          <a-input
            v-model="searchForm.activity_id"
            class="search-ipt"
            size="large"
            :placeholder="$t('global_please_input')"
            :disabled="isEdit"
            @pressEnter="queryActivity"
          />
        </a-popover>

        <a-button
          class="search-btn"
          type="primary"
          size="large"
          :disabled="!searchAbled || isEdit"
          :loading="searchLoading"
          @click="queryActivity"
        >
          {{ $t('taxonomy_dest_confirm') }}
        </a-button>
      </a-form-model-item>
    </a-form-model>

    <!-- steps 1 form -->
    <a-form-model
      v-show="activity_base.activity_id && steps == 0"
      ref="detailForm"
      layout="vertical"
      :rules="detailFormRules"
      :model="detailForm"
      class="detail-form"
    >
      <a-form-model-item class="form-item" :label="$t('28011')">
        <div class="act-name">{{ activity_base.activity_name }}</div>
      </a-form-model-item>

      <a-form-model-item class="form-item" :label="$t('45677')" prop="language">
        <a-select v-model="detailForm.language" show-search :placeholder="$t('21315')">
          <a-select-option v-for="item in publish_language_list" :key="item" :value="item">
            {{ item }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item class="form-item" :label="$t('45678')" prop="default_currency">
        <a-select v-model="detailForm.default_currency" show-search :placeholder="$t('21315')">
          <a-select-option v-for="item in currency" :key="item.currency" :value="item.currency">
            {{ item.currency }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item class="form-item" label="">
        <a-alert type="warning">
          <div v-html="$t('45679')" slot="message"></div>
        </a-alert>
      </a-form-model-item>
      <a-form-model-item class="form-item" :label="$t('45680')" prop="utm_campaign">
        <a-input v-model="detailForm.utm_campaign" :placeholder="$t('45688')" />
      </a-form-model-item>
      <a-form-model-item class="form-item" :label="$t('45681')" prop="utm_medium">
        <a-input v-model="detailForm.utm_medium" :placeholder="$t('45688')" />
      </a-form-model-item>
      <a-form-model-item class="form-item" :label="$t('45682')" prop="utm_source">
        <a-input v-model="detailForm.utm_source" :placeholder="$t('45688')" />
      </a-form-model-item>
      <a-form-model-item class="form-item" :label="$t('45683')" prop="utm_term">
        <a-input v-model="detailForm.utm_term" :placeholder="$t('45688')" />
      </a-form-model-item>
      <a-form-model-item class="form-item">
        <a-button class="sv-btn" type="primary" size="large" @click="saveActivity">
          {{ $t('45684') }}
        </a-button>
      </a-form-model-item>
    </a-form-model>

    <!-- steps 2 qrcode -->
    <div v-show="steps == 1" class="qr-wrap"><QrcodeDownload :qr-data="qrData" /></div>

    <!-- 无数据 -->
    <div v-show="!activity_base.activity_id" class="empty-wrap"><a-empty :description="$t('48246')" /></div>
  </div>
</template>

<script>
import QrcodeDownload from '../components/qrcodeDownload.vue'
export default {
  components: {
    QrcodeDownload
  },
  data() {
    const validateUTM = (rule, value, callback) => {
      value = value.trim()
      let validate = /^[A-Za-z0-9_-]*$/.test(value)
      if (validate) {
        callback()
      } else {
        callback(new Error(this.$t('package_input_error')))
      }
    }
    return {
      searchFormRules: {
        activity_id: [
          { required: true, message: 'Please input Activity ID' },
          { pattern: /^[0-9]\d*$/, message: 'Please input a number' }
        ]
      },
      steps: 0,
      searchForm: {
        activity_id: ''
      },
      searchLoading: false,
      detailForm: {
        activity_id: '',
        default_currency: '',
        utm_campaign: '',
        utm_medium: '',
        utm_source: '',
        utm_term: '',
        language: ''
      },
      detailFormRules: {
        language: [{ required: true, message: 'Language is required' }],
        default_currency: [{ required: true, message: 'Currency is required' }],
        utm_campaign: [{ validator: validateUTM, trigger: ['change'] }],
        utm_medium: [{ validator: validateUTM, trigger: ['change'] }],
        utm_source: [{ validator: validateUTM, trigger: ['change'] }],
        utm_term: [{ validator: validateUTM, trigger: ['change'] }]
      },
      publish_language_list: [],
      currency: [],
      qrData: {},
      activity_base: {},
      // 编辑状态
      isEdit: false
    }
  },
  computed: {
    searchAbled() {
      return /^[0-9]\d*$/.test(this.searchForm.activity_id)
    }
  },
  mounted() {
    let { aid = '' } = this.$route.query
    this.searchForm.activity_id = aid
    // query携带activity参数时直接查询数据
    if (this.searchAbled) {
      this.queryActivity()
      this.isEdit = true
    }
  },
  methods: {
    async queryActivity() {
      let {
        searchForm: { activity_id }
      } = this
      this.searchLoading = true
      // 检测活动是否可以设置checkout
      const result = await ajax.get(ADMIN_API.act.allow_create_express_checkout, {
        params: {
          activity_id: activity_id
        }
      })
      if (result) {
        // 检测成功，获取checkout详情
        let { activity_base = {}, currency = [], publish_language_list } = result
        // 语言
        this.publish_language_list = publish_language_list
        // 默认货币
        this.currency = currency
        // 活动基本信息，通过activity_base.activity_id搜索
        this.activity_base = activity_base
        const detail = await ajax.post(ADMIN_API.act.query_activity_config_list, {
          data: {
            page: 0,
            limit: 10,
            key_word: activity_base.activity_id + '',
            create_by: ''
          }
        })
        this.searchLoading = false
        // 设置默认语言language,使用publish_language_list列表中的第一个
        let language = publish_language_list[0] || ''
        // 设置默认货币default_currency，使用currency列表中的第一个
        let default_currency = currency[0] ? currency[0].currency : ''
        // 活动基本信息为空为新增，给定默认值，使用activity_base.activity_id为活动ID
        let defaultForm = {
          activity_id: activity_base.activity_id,
          default_currency: default_currency,
          utm_campaign: '',
          utm_medium: '',
          utm_source: '',
          utm_term: '',
          language: language
        }
        if (detail) {
          const data = (detail.data && detail.data[0]) || defaultForm
          // 接口返回language则使用，否者使用publish_language_list的第一项
          language = data.language ? data.language : language
          // 设置默认货币，default_currency有则使用，无则使用currency列表中的第一个
          default_currency = data.default_currency ? data.default_currency : default_currency
          this.detailForm = {
            activity_id: data.activity_id,
            default_currency: default_currency,
            utm_campaign: data.utm_campaign,
            utm_medium: data.utm_medium,
            utm_source: data.utm_source,
            utm_term: data.utm_term,
            language: language
          }
        } else {
          this.detailForm = defaultForm
        }
      } else {
        this.activity_base = {}
        // 如果活动不允许编辑，则取消编辑状态
        this.isEdit = false
        this.searchLoading = false
      }
    },
    goBack() {
      let { back = '0' } = this.$route.query
      // 从非列表进入编辑页面或直接打开链接的情况下，点击替换到列表
      if (window.history.length == 1 || back != '1') {
        this.$router.replace('/act/activity_checkout')
        return
      }
      this.$router.go(-1)
    },
    saveActivity() {
      this.$refs.detailForm.validate(async (validate) => {
        if (!validate) {
          return
        }
        let {
          detailForm: {
            activity_id,
            default_currency,
            utm_campaign,
            utm_medium,
            utm_source,
            utm_term,
            language
          }
        } = this
        const data = {
          activity_id: +activity_id,
          default_currency: default_currency,
          utm_campaign: utm_campaign.trim(),
          utm_medium: utm_medium.trim(),
          utm_source: utm_source.trim(),
          utm_term: utm_term.trim(),
          language
        }
        this.saveLoading = true
        const result = await ajax.postBody(ADMIN_API.act.save_activity_config, {
          data
        })
        this.saveLoading = false
        if (result.success) {
          this.qrData = {
            url: result.result.qr_code
          }
          this.steps = 1
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.empty-wrap {
  padding-top: 60px;
  width: 630px;
}
.pop-tips {
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
}
.qr-wrap {
  display: flex;
  justify-content: center;
  padding-top: 30px;
}
.detail-form {
  width: 630px;
  .form-item {
    padding-bottom: 20px;
  }
  .act-name {
    color: #000;
    font-size: 14px;
    line-height: 22px;
    font-weight: 500;
  }
  .sv-btn {
    margin-top: 28px;
  }
}
.form {
  padding: 30px 0;
  .search-btn {
    vertical-align: top;
  }
}
.ck-title {
  color: rgba(0, 0, 0, 0.85);
  font-size: 24px;
  font-weight: 500;
  padding: 30px 0 20px 0;
  margin: 0;
}
.steps {
  width: 800px;
}
.search-ipt {
  width: 530px;
  margin-right: 10px;
}
</style>
