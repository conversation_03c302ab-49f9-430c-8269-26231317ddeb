<template>
  <div>
    <a-form-model layout="vertical" class="form-wrapper">
      <a-form-model-item :label="$t('28010')" class="form-item">
        <a-input v-model="form.key_word" :placeholder="$t('global_please_input')" />
      </a-form-model-item>
      <a-form-model-item :label="$t('taxonomy_created_by')" class="form-item">
        <a-input v-model="form.create_by" :placeholder="$t('global_please_input')" />
      </a-form-model-item>
      <a-form-model-item class="form-item-last">
        <a-button type="primary" :loading="loading" @click="search">
          {{ $t('global_filter') }}
        </a-button>
        <a-button type="default" class="clear-btn" @click="clear">
          {{ $t('global_reset') }}
        </a-button>
      </a-form-model-item>
    </a-form-model>
    <a-divider />
    <a-row align="middle" type="flex" justify="space-between" style="padding-bottom: 10px;">
      <a-col> {{ $t('29559', { number: pagination.total }) }} </a-col>
      <a-col>
        <a-button type="primary" @click="editClick('')">
          <a-icon type="plus" />
          {{ $t('45674') }}
        </a-button>
      </a-col>
    </a-row>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :scroll="{ y: 1000 }"
      bordered
      :row-key="(_, index) => index"
      :pagination="pagination"
      :hide-on-single-page="true"
      :loading="loading"
      @change="changeTable"
    >
    </a-table>

    <!-- 二维码弹窗 -->
    <a-modal
      v-model="showLayer"
      title=""
      width="600px"
      centered
      class="model-radius__model_radius"
      :footer="null"
      :body-style="bodyStyle"
      @ok="() => (modal2Visible = false)"
    >
      <QrcodeDownload :qr-data="qrData" />
    </a-modal>
  </div>
</template>

<script>
import QrcodeDownload from '../components/qrcodeDownload.vue'

export default {
  components: {
    QrcodeDownload
  },
  data() {
    return {
      bodyStyle: {
        height: '412px',
        borderRadius: '12px',
        paddingTop: '40px'
      },
      form: {
        key_word: '',
        create_by: ''
      },
      columns: [
        { title: this.$t('28010'), dataIndex: 'activity_id', key: 'activity_id', width: 120 },
        { title: this.$t('28011'), dataIndex: 'activity_title', key: 'activity_title', ellipsis: true },
        {
          title: this.$t('global_activity_status'),
          dataIndex: 'publish_status',
          key: 'publish_status',
          width: 120,
          customRender: (txt, record, index) => {
            const publish_status = record.publish_status
            const color = publish_status === 1 ? '#52C41A' : '#D9D9D9'
            const text = publish_status === 1 ? 'Published' : 'Unpublished'
            return { children: <a-badge color={color} text={text} /> }
          }
        },
        {
          title: this.$t('taxonomy_created_by'),
          dataIndex: 'create_by',
          key: 'create_by',
          width: 200,
          ellipsis: true
        },
        {
          title: this.$t('taxonomy_list_actions'),
          key: 'action',
          fixed: 'right',
          width: 80,
          align: 'center',
          scopedSlots: { customRender: 'action' },
          customRender: (text, record, index) => {
            const id = record.activity_id
            const edit = this.$t('publish_lang_manage')
            return {
              children: (
                <a
                  onClick={() => {
                    this.editClick(id)
                  }}
                >
                  {edit}
                </a>
              )
            }
          }
        },
        {
          title: this.$t('45672'),
          key: 'qrcode',
          fixed: 'right',
          width: 80,
          align: 'center',
          scopedSlots: { customRender: 'qrcode' },
          customRender: (text, record) => {
            const url = record.url || ''
            return {
              children: <a-icon onClick={() => this.showQrcode({ url })} type="qrcode" class="qrcode" />
            }
          }
        }
      ],
      dataSource: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      showLayer: false,
      loading: false,
      qrData: null
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    search() {
      this.pagination = {
        current: 1,
        pageSize: 10,
        total: 0
      }
      this.getData()
    },
    clear() {
      this.form = {
        key_word: '',
        create_by: ''
      }
      this.search()
    },
    changeTable(pagination) {
      this.pagination = { ...this.pagination, ...pagination }
      this.getData()
    },
    async getData() {
      let {
        pagination,
        form: { create_by = '', key_word = '' }
      } = this
      this.loading = true
      const result = await ajax.post(ADMIN_API.act.query_activity_config_list, {
        data: {
          page: pagination.current,
          limit: pagination.pageSize,
          key_word: key_word.trim(),
          create_by: create_by.trim()
        }
      })
      this.loading = false
      if (result) {
        this.pagination.total = result.total || 0
        this.dataSource = result.data || []
      } else {
        this.dataSource = []
      }
    },
    showQrcode(qr) {
      this.qrData = qr
      this.showLayer = true
    },
    editClick(aid = '') {
      this.$router.push('/act/checkout_edit?back=1&aid=' + aid)
    }
  }
}
</script>
<style lang="scss">
.model-radius__model_radius {
  .ant-modal-content {
    border-radius: 12px;
  }
}
</style>
<style lang="scss" scoped>
.qrcode {
  cursor: pointer;
  color: #000;
  font-size: 18px;
  opacity: 0.85;
}
.form-wrapper {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  .form-item {
    width: 485px;
    margin-right: 24px;
    &-last {
      width: 100%;
    }
  }
  .clear-btn {
    margin-left: 8px;
  }
}
</style>
