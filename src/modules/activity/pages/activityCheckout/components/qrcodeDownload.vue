<template>
  <div class="flex-wrap">
    <div class="qr-wrap">
      <img class="qrcode" :src="qrLink" alt="error" />
    </div>
    <a-button type="primary" size="large" class="down-btn" @click="download">
      <a-icon type="download" />{{ $t('29554') }}
    </a-button>
    <a-input-search
      class="copy-input"
      placeholder="input search text"
      :value="qrData.url"
      :read-only="true"
      @focus="copyLink"
    >
      <a-button slot="enterButton">
        <a-icon type="copy" :style="{ color: 'rgba(0,0,0,.6)' }" theme="filled" />
      </a-button>
    </a-input-search>

    <!-- 下载二维码标签 -->
    <a ref="download" :href="downLink" :download="downloadName"></a>
  </div>
</template>

<script>
import QRCode from 'qrcode'
export default {
  props: {
    qrData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      downloadName: 'qrcode',
      downLink: ''
    }
  },
  computed: {
    qrLink() {
      return 'https://www.klook.com/api/qrcode/?code=' + this.qrData.url
    }
  },
  methods: {
    download() {
      // 生成二维码，触发a标签点击事件，达到下载的目的
      QRCode.toDataURL(this.qrData.url)
        .then((url) => {
          this.downloadName = url.slice(-20)
          this.downLink = url
          this.$nextTick(() => {
            this.$refs.download.click()
          })
        })
        .catch((err) => {
          console.error(err)
        })
    },
    copyLink() {
      navigator.clipboard && navigator.clipboard.writeText(this.qrData.url)
      this.$message.success('Replicated Successfully')
    }
  }
}
</script>
<style lang="scss" scoped>
.flex-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  .copy-input {
    width: 500px;
    margin-top: 60px;
  }
  .down-btn {
    width: 128px;
  }
  .qr-wrap {
    width: 200px;
    height: 200px;
    padding: 16px;
  }
  .qrcode {
    width: 100%;
    height: 100%;
    display: block;
  }
}
</style>
