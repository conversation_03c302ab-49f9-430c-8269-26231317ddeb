const COMMON_PREFIX = '/v1/experiencesadminnodesrv'

const API_MAPS = {
  GET_ACTIVITY_PACKAGES: `${COMMON_PREFIX}/activity/package_service/get_package_option_sources`,
  GET_PACKAGE_DETAILS: `${COMMON_PREFIX}/package/component_service/get_package_details`,
  GET_LLM_CHAT: `${COMMON_PREFIX}/llm/chat`,
  GET_LLM_PREDICT: `${COMMON_PREFIX}/llm/predict`,
  GET_REVIEWS: `${COMMON_PREFIX}/activity/component_service/get_activity_review_list`,
  SET_REDIS: `${COMMON_PREFIX}/redis/set`,
  GET_REDIS: `${COMMON_PREFIX}/redis/get`
}

export async function getPackageListByAid(activity_id) {
  const res = await ajax.get(API_MAPS['GET_ACTIVITY_PACKAGES'], {
    headers: {
      withCredentials: true
    },
    params: {
      activity_id
    }
  })
  return res
}

export async function getPackageDetails(params) {
  const res = await ajax.get(API_MAPS['GET_PACKAGE_DETAILS'], {
    headers: {
      withCredentials: true
    },
    params
  })
  return res
}

export async function getLLMPredict(data) {
  return ajax.postBody({
    url: API_MAPS['GET_LLM_PREDICT'],
    headers: {
      withCredentials: true
    },
    data,
    timeout: 0 // 设置请求不超时
  })
}

export async function getLLMChat(data) {
  return ajax.postBody({
    url: API_MAPS['GET_LLM_CHAT'],
    headers: {
      withCredentials: true
    },
    data,
    timeout: 0 // 设置请求不超时
  })
}

export async function setRedis(data) {
  try {
    const { value = '' } = data || {}
    const response = await ajax.postBody({
      url: API_MAPS['SET_REDIS'],
      headers: {
        withCredentials: true
      },
      data: {
        ...data,
        value: JSON.stringify(value)
      }
    })
    return response
  } catch (error) {
    console.error('Error setting Redis value:', error)
    throw error
  }
}

export async function getRedis(key) {
  try {
    const response = await ajax.get({
      url: API_MAPS['GET_REDIS'],
      headers: {
        withCredentials: true
      },
      params: {
        key
      }
    })

    const { result } = response
    return JSON.parse(result)
  } catch (error) {
    console.error('Error getting Redis value:', error)
    throw error
  }
}

