<template>
  <div>
    <h3>{{ fieldName }}</h3>
    <div v-if="!fieldValueList.length">None</div>
    <ul v-else class="item-block-contenter flex-colomn gap-8">
      <li v-for="(value, index) in fieldValueList" :key="index">
        <text-highlight :queries="highLightWords">
          {{ value }}
        </text-highlight>
      </li>
    </ul>
  </div>
</template>

<script>
import TextHighlight from 'vue-text-highlight'
export default {
  name: 'FieldData',
  components: {
    TextHighlight
  },
  inject: ['getHighLightWords'],
  props: {
    fieldName: {
      type: String,
      default: ''
    },
    fieldValue: {
      type: [Array, String],
      default: () => []
    }
  },
  computed: {
    fieldValueList() {
      const { fieldValue: _fieldValue } = this
      return (Array.isArray(_fieldValue) ? _fieldValue : [_fieldValue])?.filter((ele) => !!ele)
    },
    highLightWords() {
      return this.getHighLightWords?.()
    }
  }
}
</script>

<style lang="scss" scoped>
ul,
ol {
  padding-left: 18px;
  list-style-type: disc;
}
</style>
