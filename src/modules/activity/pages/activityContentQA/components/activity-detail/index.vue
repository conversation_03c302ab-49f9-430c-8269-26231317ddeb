<template>
  <div class="activity-detail">
    <h3>Activity</h3>
    <a-card :title="activityName" style="max-width: '50%'">
      <div class="card-main-body">
        <field-data
          v-for="(fieldName, fieldIndex) in Object.keys(activityDatas)"
          :key="fieldIndex"
          :field-name="fieldName"
          :field-value="activityDatas[fieldName]"
        />
      </div>
    </a-card>
  </div>
</template>

<script>
import FieldData from './field-data.vue'
export default {
  name: 'ActivityDetail',
  components: { FieldData },
  props: {
    activityDetail: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    activityName() {
      const { activity_name } = this.activityDetail
      return activity_name
    },
    activityDatas() {
      // eslint-disable-next-line no-unused-vars
      const { activity_name, ...other } = this.activityDetail
      return {
        ...other
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.flex-colomn {
  display: flex;
  flex-direction: column;
}
.gap-8 {
  gap: 8px;
}
ul,
ol {
  padding-left: 18px;
  list-style-type: disc;
}
</style>
