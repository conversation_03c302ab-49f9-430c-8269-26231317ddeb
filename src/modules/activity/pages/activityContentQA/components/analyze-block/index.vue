<template>
  <div class="analyze-block">
    <!-- 处理 analyze 为对象的情况 -->
    <template v-if="isObject">
      <div class="analyze-list flex-column">
        <div v-for="(key, index) in Object.keys(analyze)" :key="index">
          <div v-if="Array.isArray(analyze[key]) && analyze[key].length" class="mb-20">
            <div class="analyze-key">{{ key }}</div>
            <div class="error-block flex-column gap-30">
              <div v-for="(errorItem, errorItemIndex) in analyze[key]" :key="`${index}-${errorItemIndex}`">
                <ul>
                  <li
                    v-for="(errorStr, errorStrIndex) in formatErrorItem(errorItem)"
                    :key="errorStrIndex"
                    v-html="errorStr"
                  ></li>
                </ul>
              </div>
            </div>
          </div>
          <div v-else-if="typeof analyze[key] === 'string' && analyze[key].length" class="mb-20">
            <div class="analyze-key">{{ key }}</div>
            <div class="error-block flex-column gap-30">
              <ul>
                <li v-html="analyze[key]"></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </template>
    <!-- 处理 analyze 为数组的情况 -->
    <template v-else-if="isArray">
      <div class="analyze-list flex-column">
        <div v-for="(item, index) in analyze" :key="index" class="mb-20">
          <div class="analyze-key">{{ item.checklistItem }}</div>
          <div class="error-block flex-column gap-30">
            <ul>
              <li><strong>错误类型:</strong> {{ item.errorType }}</li>
              <li><strong>分析:</strong> {{ item.analysis }}</li>
              <li><strong>评分:</strong> {{ item.score }}</li>
            </ul>
          </div>
        </div>
      </div>
    </template>
    <!-- 处理 analyze 为其他类型的情况 -->
    <template v-else>
      <div v-html="analyze"></div>
    </template>
  </div>
</template>

<script>
export default {
  name: 'AnalyzeBlock',
  components: {},
  props: {
    analyze: {
      type: [Object, String, Array], // 修改类型以允许接收多种类型的 analyze
      default: () => {}
    }
  },
  computed: {
    isObject() {
      return typeof this.analyze === 'object' && !Array.isArray(this.analyze)
    },
    isArray() {
      return Array.isArray(this.analyze)
    }
  },
  methods: {
    formatErrorItem(item) {
      if (typeof item === 'string') {
        return [item]
      } else if (Array.isArray(item)) {
        return item
      } else if (typeof item === 'object' && item !== null) {
        return Object.keys(item).map((key) => `${key}: \n${item[key]}`) || []
      } else {
        return []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.analyze-key {
  color: #212121;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 600;
  margin-bottom: 10px;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.gap-30 {
  gap: 30px;
}
.mb-20 {
  margin-bottom: 20px;
}
ul,
ol {
  padding-left: 18px;
  list-style-type: disc;
}
</style>
