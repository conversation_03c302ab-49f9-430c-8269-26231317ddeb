<template>
  <div>
    <a-drawer
      v-if="visible"
      title="Checklist Config"
      :visible.sync="visible"
      width="500"
      class="content-qa-drawer"
      :header-style="{
        position: 'relative'
      }"
      @close="handleClose"
    >
      <a-form-model ref="form" :model="form" layout="vertical">
        <a-form-model-item label="Use Checklist">
          <a-radio-group v-model="form.use_config.checklist" :disabled="true">
            <a-radio :value="true">Yes</a-radio>
            <a-radio :value="false">No</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <div class="checklist-list" v-if="form.use_config.checklist">
          <div v-for="(_, index) in form.config.checklist" :key="index" class="checklist-item">
            <div class="title-block">
              <h3>Checklist {{ index + 1 }}</h3>
              <a-button @click="handleClear(index)">Clear</a-button>
            </div>
            <a-form-model-item label="Section Position">
              <a-select
                v-model="form.config.checklist[index].sections"
                :options="PACKAGE_SECTIONS"
                mode="multiple"
              ></a-select>
            </a-form-model-item>

            <a-form-model-item label="Analyze Method">
              <a-textarea
                v-model="form.config.checklist[index].analysisMethod"
                placeholder="Please input analysis method"
                :allow-clear="true"
                auto-size
              />
            </a-form-model-item>
          </div>

          <div class="add-item-box" @click="handleAddChecklist">
            <a-icon type="plus"></a-icon>
            <h5>Add Checklist</h5>
          </div>
        </div>
      </a-form-model>
      <div
        :style="{
          position: 'absolute',
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e8e8e8',
          padding: '10px 16px',
          textAlign: 'right',
          left: 0,
          background: '#fff',
          borderRadius: '0 0 4px 4px'
        }"
      >
        <a-button type="primary" @click="handleSave">Save changes online</a-button>
      </div>
    </a-drawer>
    <a-button @click="handleOpen">Checklist Config</a-button>
  </div>
</template>

<script>
import { PACKAGE_SECTIONS } from '../../constant/mainSections.ts'
export default {
  name: 'ExamplesConfigV2',
  components: {},
  props: {
    form: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      visible: false,
      PACKAGE_SECTIONS
    }
  },
  methods: {
    handleOpen() {
      this.visible = true
    },
    handleClose() {
      this.visible = false
    },
    handleAddChecklist() {
      this.form.config.checklist.push({
        sections: [],
        analysisMethod: ''
      })
    },
    handleSave() {
      this.$emit('saveChanges')
    },
    handleClear(index) {
      this.form.config.checklist.splice(index, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.add-item-box {
  background-color: #f9f9f9;
  text-align: center;
  cursor: pointer;
  border: 1px solid #d9d9d9;
  border-style: dashed;
  padding: 8px;
}
.title-block {
  display: flex;
  justify-content: space-between;
}
.content-qa-drawer {
  ::v-deep .ant-drawer-body {
    padding-bottom: 60px;
  }
}
</style>
