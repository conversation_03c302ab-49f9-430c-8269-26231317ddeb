<template>
  <div class="common-search-block">
    <div class="package-info-input-block">
      <a-form-model ref="form" :model="form" layout="inline">
        <a-form-model-item label="activity_id" prop="activity_id" required>
          <a-input
            v-model="form.activity_id"
            placeholder="Please input aid first so you can select the packageId instead of entering it."
            :allow-clear="true"
            style="width: 300px"
            @change="handleSearchPackages"
          />
        </a-form-model-item>
        <a-form-model-item label="package_id" prop="package_id" required>
          <a-select
            v-model="form.package_id"
            :disabled="!form.activity_id"
            show-search
            style="width: 600px"
            :options="packageOptions"
            placeholder="Please select package"
            :loading="loading"
          ></a-select>
        </a-form-model-item>
        <a-form-model-item label="gpt model" prop="gpt_model">
          <a-select
            v-model="form.gpt_model"
            :disabled="!form.package_id"
            style="width: 200px"
            :options="modelOptions"
            placeholder="Please select Gpt Model"
          ></a-select>
        </a-form-model-item>
        <a-form-model-item label="language" prop="k_lang">
          <a-select v-model="form.k_lang" show-search style="width: 200px" :options="languageOptions" />
        </a-form-model-item>
      </a-form-model>
    </div>
    <div class="button-opeation-block">
      <ExamplesConfig :form="form" @saveChanges="handleSaveChanges('examples')" />
      <ChecklistConfig :form="form" @saveChanges="handleSaveChanges('checklist')" />
      <ErrorTypeConfig :form="form" @saveChanges="handleSaveChanges('error_types')" />
      <WorkflowConfig :form="form" @saveChanges="handleSaveChanges('workflows')" />
      <a-button type="primary" @click="getPkgDetails">Get Pkg Detail</a-button>
      <a-button @click="clear">Clear</a-button>
    </div>
  </div>
</template>

<script>
import { getPackageListByAid } from '../../apis'
import ExamplesConfig from '../examples-config/index.vue'
import ChecklistConfig from '../checklist/index.vue'
import ErrorTypeConfig from '../error-type-config/index.vue'
import WorkflowConfig from '../workflow-config/index.vue'
import { lang_array } from '../../constant/language.ts'
import { modelOptions } from '../../constant/model.ts'
export default {
  name: 'CommonSearch',
  components: {
    ExamplesConfig,
    ErrorTypeConfig,
    WorkflowConfig,
    ChecklistConfig
  },
  props: {
    form: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      activityId: '',
      currAidPackages: [],
      loading: false,
      languageOptions: lang_array,
      modelOptions
    }
  },
  computed: {
    packageOptions() {
      const { currAidPackages } = this
      return currAidPackages?.map((ele) => {
        const { package_id, package_name } = ele
        return {
          label: `${package_id}-${package_name}`,
          value: package_id,
          key: package_id
        }
      })
    }
  },
  methods: {
    handleSearchPackages: _.debounce(async function () {
      const { activity_id: aid = '' } = this.form
      const activityId = aid?.trim()
      if (!activityId) {
        this.currAidPackages = []
        return
      }
      this.loading = true
      const res = await getPackageListByAid(activityId)
      this.loading = false
      if (!res) {
        this.$message.error('Search Packages failed, please try again')
        return
      }
      const { packages: _packages, combo_info = {} } = res
      const packages = [..._packages, ...combo_info?.packages]
      this.currAidPackages = packages
      this.form.package_id = ''
    }, 500),
    async submit() {
      this.$refs.form?.validate(async (isValid) => {
        if (isValid) {
          this.$emit('submit', this.form)
        } else {
          this.$message.error('Something Invalid, Please Check Again')
        }
      })
    },
    async getPkgDetails() {
      this.$refs.form?.validate(async (isValid) => {
        if (isValid) {
          this.$emit('getPkgDetails', this.form)
        } else {
          this.$message.error('Something Invalid, Please Check Again')
        }
      })
    },
    handleSaveChanges(key) {
      this.$emit('saveChanges', key)
    },
    clear() {
      this.form?.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-input {
  display: flex;
  align-items: center;
  gap: 10px;
}

.button-opeation-block {
  display: flex;
  gap: 4px;
  justify-content: end;
}
</style>
