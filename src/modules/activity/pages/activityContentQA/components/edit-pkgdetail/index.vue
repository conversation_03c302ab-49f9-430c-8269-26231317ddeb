<template>
  <div>
    <a-drawer
      v-if="visible"
      title="Edit Pkg Detail"
      :visible.sync="visible"
      width="500"
      :header-style="{
        position: 'relative'
      }"
      @close="handleClose"
    >
      <a-form-model ref="form" layout="vertical">
        <div class="pkgInfo">
          <div v-for="(key, index) in Object.keys(pkgInfo)" :key="index">
            <a-form-model-item :label="key">
              <a-textarea v-model="pkgInfo[key]" placeholder="Please input" :allow-clear="true" auto-size />
            </a-form-model-item>
          </div>
        </div>
      </a-form-model>
      <div class="btn-group flex-end">
        <a-button style="margin-right: 8px" @click="handleClose"> cancle </a-button>
        <a-button type="primary" @click="handleSubmit">Confirm</a-button>
      </div>
    </a-drawer>
    <a-button @click="handleOpen">Edit Pkg Detail</a-button>
  </div>
</template>

<script>
export default {
  name: 'EditPkgDetail',
  components: {},
  props: {
    oldPkgInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      visible: false,
      pkgInfo: { ...this.oldPkgInfo } // 确保初始化 pkgInfo 为父组件的 oldPkgInfo 的副本
    }
  },
  watch: {
    oldPkgInfo(newValue) {
      this.pkgInfo = { ...newValue } // 监听父组件的值变化并同步
    }
  },
  methods: {
    handleOpen() {
      this.visible = true
    },
    handleClose() {
      this.visible = false
    },
    handleSubmit() {
      this.$emit('change', { ...this.pkgInfo }) // emit 当前的 pkgInfo 给父组件
      this.handleClose()
    }
  }
}
</script>

<style lang="scss" scoped></style>
