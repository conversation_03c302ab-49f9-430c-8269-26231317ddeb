<template>
  <div>
    <a-drawer
      v-if="visible"
      title="Error Type Config"
      :visible.sync="visible"
      width="500"
      class="content-qa-drawer"
      :header-style="{
        position: 'relative'
      }"
      @close="handleClose"
    >
      <a-form-model ref="form" :model="form" layout="vertical">
        <a-form-model-item label="Use Error Types">
          <a-radio-group v-model="form.use_config.error_types">
            <a-radio :value="true">Yes</a-radio>
            <a-radio :value="false">No</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <div class="error-list" v-if="form.use_config.error_types">
          <div v-for="(_, index) in form.config.error_types" :key="index">
            <div class="title-block">
              <h3>Error Type {{ index + 1 }}</h3>
              <a-button @click="handleClear(index)">Clear</a-button>
            </div>
            <a-form-model-item label="error type name">
              <a-input
                v-model="form.config.error_types[index].type"
                placeholder="Please input error type name"
                :allow-clear="true"
              />
            </a-form-model-item>
            <a-form-model-item label="error type description">
              <a-textarea
                v-model="form.config.error_types[index].desc"
                placeholder="Please input description of this error"
                :allow-clear="true"
                auto-size
              />
            </a-form-model-item>
          </div>
          <div class="add-item-box" @click="handleAddErrorTypes">
            <a-icon type="plus"></a-icon>
            <h5>Add ErrorTypes</h5>
          </div>
        </div>
      </a-form-model>
      <div
        :style="{
          position: 'absolute',
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e8e8e8',
          padding: '10px 16px',
          textAlign: 'right',
          left: 0,
          background: '#fff',
          borderRadius: '0 0 4px 4px'
        }"
      >
        <a-button type="primary" @click="handleSave">Save changes online</a-button>
      </div>
    </a-drawer>
    <a-button @click="handleOpen">Error Type Config</a-button>
  </div>
</template>

<script>
export default {
  name: 'ErrorTypeConfig',
  components: {},
  props: {
    form: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      visible: false
    }
  },
  methods: {
    handleOpen() {
      this.visible = true
    },
    handleClose() {
      this.visible = false
    },
    handleAddErrorTypes() {
      this.form.config.error_types.push({
        type: '',
        desc: ''
      })
    },
    handleSave() {
      this.$emit('saveChanges')
    },
    handleClear(index) {
      this.form.config.error_types.splice(index, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.add-item-box {
  background-color: #f9f9f9;
  text-align: center;
  cursor: pointer;
  border: 1px solid #d9d9d9;
  border-style: dashed;
  padding: 8px;
}

.title-block {
  display: flex;
  justify-content: space-between;
}
.content-qa-drawer {
  ::v-deep .ant-drawer-body {
    padding-bottom: 60px;
  }
}
</style>
