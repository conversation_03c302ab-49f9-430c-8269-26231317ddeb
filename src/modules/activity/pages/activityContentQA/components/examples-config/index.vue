<template>
  <div>
    <a-drawer
      v-if="visible"
      title="Examples Config"
      :visible.sync="visible"
      width="500"
      class="content-qa-drawer"
      :header-style="{
        position: 'relative'
      }"
      @close="handleClose"
    >
      <a-form-model ref="form" :model="form" layout="vertical">
        <a-form-model-item label="Use Examples">
          <a-radio-group v-model="form.use_config.examples">
            <a-radio :value="true">Yes</a-radio>
            <a-radio :value="false">No</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <div class="example-list" v-if="form.use_config.examples">
          <div v-for="(_, index) in form.config.examples" :key="index" class="example-item">
            <div class="title-block">
              <h3>Example {{ index + 1 }}</h3>
              <a-button @click="handleClear(index)">Clear</a-button>
            </div>
            <div
              v-for="(_, currExampleSectionIndex) in form.config.examples[index].sections"
              :key="currExampleSectionIndex"
            >
              <a-form-model-item label="Section Position">
                <a-select
                  v-model="form.config.examples[index].sections[currExampleSectionIndex].postion"
                  :options="PACKAGE_SECTIONS"
                ></a-select>
              </a-form-model-item>

              <a-form-model-item label="Section OriginText">
                <a-textarea
                  v-model="form.config.examples[index].sections[currExampleSectionIndex].content"
                  placeholder="Please input originText"
                  :allow-clear="true"
                  auto-size
                />
              </a-form-model-item>
            </div>
            <div class="add-item-box" @click="handleAddCurrExampleSections(index, currExampleSectionIndex)">
              <a-icon type="plus"></a-icon>
              <h5>Add Sections</h5>
            </div>

            <a-form-model-item label="Error Type">
              <a-select v-model="form.config.examples[index].error_type" :options="erroTypeOptions" />
            </a-form-model-item>
            <a-form-model-item label="Analysis">
              <a-textarea
                v-model="form.config.examples[index].analysis"
                placeholder="Please input analysis"
                :allow-clear="true"
                auto-size
              />
            </a-form-model-item>
            <a-form-model-item label="Severity">
              <a-select v-model="form.config.examples[index].severity" :options="SEVERITY_OPTIONS"></a-select>
            </a-form-model-item>
            <a-form-model-item label="Error Score">
              <a-input-number
                id="inputNumber"
                v-model="form.config.examples[index].score"
                :min="0"
                :max="25"
              />
            </a-form-model-item>
          </div>

          <div class="add-item-box" @click="handleAddExamples">
            <a-icon type="plus"></a-icon>
            <h5>Add Example</h5>
          </div>
        </div>
      </a-form-model>
      <div
        :style="{
          position: 'absolute',
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e8e8e8',
          padding: '10px 16px',
          textAlign: 'right',
          left: 0,
          background: '#fff',
          borderRadius: '0 0 4px 4px'
        }"
      >
        <a-button type="primary" @click="handleSave">Save changes online</a-button>
      </div>
    </a-drawer>
    <a-button @click="handleOpen">Example Config</a-button>
  </div>
</template>

<script>
import { EMPTY_EXAMPLE_V2 } from '../../constant/examples.ts'
import { PACKAGE_SECTIONS } from '../../constant/mainSections.ts'
import { SEVERITY_OPTIONS } from '../../constant/severity.ts'
export default {
  name: 'ExamplesConfigV2',
  components: {},
  props: {
    form: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      visible: false,
      EMPTY_EXAMPLE_V2,
      PACKAGE_SECTIONS,
      SEVERITY_OPTIONS
    }
  },
  computed: {
    erroTypeOptions() {
      return this.form.config.error_types?.map((ele) => ({
        key: ele?.type,
        label: ele?.type,
        value: ele?.type
      }))
    }
  },
  methods: {
    handleOpen() {
      this.visible = true
    },
    handleClose() {
      this.visible = false
    },
    handleAddExamples() {
      this.form.config.examples.push(EMPTY_EXAMPLE_V2)
    },
    handleAddCurrExampleSections(index, currExampleSectionIndex) {
      this.form.config.examples[index].sections.push({
        postion: '',
        content: ''
      })
    },
    handleSave() {
      this.$emit('saveChanges')
    },
    handleClear(index) {
      this.form.config.examples.splice(index, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.add-item-box {
  background-color: #f9f9f9;
  text-align: center;
  cursor: pointer;
  border: 1px solid #d9d9d9;
  border-style: dashed;
  padding: 8px;
}
.title-block {
  display: flex;
  justify-content: space-between;
}
.content-qa-drawer {
  ::v-deep .ant-drawer-body {
    padding-bottom: 60px;
  }
}
</style>
