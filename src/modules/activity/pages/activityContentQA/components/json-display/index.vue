<template>
  <div>
    <pre>{{ formattedJson }}</pre>
  </div>
</template>

<script>
export default {
  name: 'JSONDisplay',
  props: {
    json: {
      required: true
    }
  },
  computed: {
    formattedJson() {
      // 格式化 JSON 数据
      return JSON.stringify(this.json, null, 2)
    }
  }
}
</script>

<style scoped>
pre {
  background-color: #f8f9fa;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  white-space: pre-wrap; /* 保证长文本换行 */
  word-break: break-all; /* 保证长单词换行 */
  font-family: monospace; /* 使用等宽字体 */
}
</style>
