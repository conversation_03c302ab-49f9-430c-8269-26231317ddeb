<template>
  <div class="package-detail">
    <h3>Package</h3>
    <div class="card-list">
      <a-card :title="packageName" style="max-width: '50%'">
        <div class="card-main-body">
          <Units v-if="pkgUnits.length" :units="pkgUnits" />
          <TimeSlots v-if="pkgTimeSlots.length" :time-slots="pkgTimeSlots" />
          <TagList v-if="tagList.length" :tag-list="tagList" />
          <PackageDetailSection :sections="pkgDetailSections" />
        </div>
      </a-card>
      <a-card v-if="showItineraryObj" title="ItinerayDetail" style="max-width: '50%'">
        <ItinerayDetail :itineraryObj="itineraryObj" />
      </a-card>
    </div>
  </div>
</template>

<script>
import PackageDetailSection from './sections.vue'
import TagList from './tag-list.vue'
import Units from './units.vue'
import TimeSlots from './time-slots.vue'
import ItinerayDetail from './itineray-detail.vue'
export default {
  name: 'PackageDetail',
  components: {
    PackageDetailSection,
    TagList,
    Units,
    TimeSlots,
    ItinerayDetail
  },
  props: {
    packageData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    packageId() {
      return this.packageData.package_id || ''
    },
    packageName() {
      return this.packageData.package_name || ''
    },
    tagList() {
      return this.packageData.tagList || []
    },
    pkgDetailSections() {
      return this.packageData.pkgDetailSections || []
    },
    pkgUnits() {
      return this.packageData.pkgUnits || []
    },
    pkgTimeSlots() {
      return this.packageData.pkgTimeSlots || []
    },
    itineraryObj() {
      return this.packageData.itineraryObj || {}
    },
    showItineraryObj() {
      return Object.keys(this.itineraryObj).length > 0
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.icon-title {
  color: #212121;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 400;
}

.icon-description-block {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.icon-description {
  color: #757575;
  font-size: 14px;
  line-height: 1.5;
  font-weight: 400;
}

.package-tagList-block {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-main-body {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.card-list {
  display: flex;
  flex-direction: column;
  gap: 40px;
}
</style>
