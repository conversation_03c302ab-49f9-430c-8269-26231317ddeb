<template>
  <div class="itinerary-container">
    <!-- 行程提醒 -->
    <section v-if="itineraryReminder.length" class="reminder-section">
      <h2>Itinerary Reminder</h2>
      <ul>
        <li v-for="(reminder, index) in itineraryReminder" :key="index">{{ reminder }}</li>
      </ul>
    </section>

    <!-- 每日行程 -->
    <div v-if="itinerary.length">
      <section class="day-itinerary-section" v-for="(day, index) in itinerary" :key="index">
        <h2>{{ day.day_name }}</h2>
        <p>itinerary departure: {{ day.itinerary_departure }}</p>
        <p>itinerary_return: {{ day.itinerary_return }}</p>

        <div v-for="(attraction, idx) in day.itinerary_attraction" :key="idx" class="attraction-card">
          <h3>{{ attraction.time }} - {{ attraction.name || '-' }}</h3>
          <ul>
            <li v-for="(tag, i) in attraction.tags" :key="i">{{ tag }}</li>
          </ul>
          <p>{{ attraction.text }}</p>
          <ul>
            <li v-for="(poi, j) in attraction.pois" :key="j">
              <strong>{{ poi.title }}</strong
              >: {{ poi.address }} ({{ poi.location }})
            </li>
          </ul>
        </div>
      </section>
    </div>

    <!-- 集合信息 -->
    <section v-if="pickUpMeetUp.length" class="meetup-section">
      <h2>pick up meet up</h2>
      <div v-for="(meetup, index) in pickUpMeetUp" :key="index">
        <h3>itinerary departure</h3>
        <ul>
          <li v-for="(departure, idx) in meetup.itinerary_departure.data" :key="idx">
            {{ departure.detail }}
          </li>
        </ul>
        <h3>itinerary return</h3>
        <ul>
          <li v-for="(returnInfo, idx) in meetup.itinerary_return.data" :key="idx">
            {{ returnInfo.name }} - {{ returnInfo.location }}
          </li>
        </ul>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'ItinerayDetail',
  components: {},
  props: {
    itineraryObj: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    itineraryReminder() {
      return this.itineraryObj?.itinerary_reminder || []
    },
    itinerary() {
      return this.itineraryObj?.itinerary || []
    },
    pickUpMeetUp() {
      return this.itineraryObj?.pick_up_meet_up_v2 || []
    }
  }
}
</script>

<style scoped>
.reminder-section,
.day-itinerary-section,
.meetup-section {
  margin-bottom: 20px;
}
.attraction-card {
  border: 1px solid #ddd;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
}
</style>
