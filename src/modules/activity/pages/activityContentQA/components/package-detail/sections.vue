<template>
  <div class="sections flex-colomn gap-20">
    <div v-for="(block, blockIndex) in sections" :key="blockIndex">
      <div class="block-title">{{ block.blockTitle }}</div>
      <div class="sub-block-contenter flex-colomn gap-10">
        <div
          v-for="(subBlock, subBlockIndex) in block.blockContent"
          :key="`${blockIndex} - ${subBlockIndex}`"
        >
          <div class="subblock-title">{{ subBlock.subBlockTitle }}</div>
          <ul class="item-block-contenter flex-colomn gap-8">
            <li
              v-for="(item, itemIndex) in subBlock.subBlockContent"
              :key="`${blockIndex} - ${subBlockIndex} - ${itemIndex}`"
            >
              <text-highlight :queries="highLightWords">
                {{ item }}
              </text-highlight>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TextHighlight from 'vue-text-highlight'
export default {
  name: 'Sections',
  components: {
    TextHighlight
  },
  inject: ['getHighLightWords'],
  props: {
    sections: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  computed: {
    highLightWords() {
      return this.getHighLightWords?.()
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.flex-colomn {
  display: flex;
  flex-direction: column;
}

.gap-20 {
  gap: 20px;
}

.gap-10 {
  gap: 10px;
}

.gap-8 {
  gap: 8px;
}

.block-title {
  font-size: 20px;
  line-height: 1.32;
  font-weight: 600;
  color: #212121;
  margin-bottom: 10px;
}

.subblock-title {
  color: #212121;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 600;
}

ul,
ol {
  padding-left: 18px;
  list-style-type: disc;
}
.item-block-contenter {
  span {
    word-break: break-all;
  }
}
</style>
