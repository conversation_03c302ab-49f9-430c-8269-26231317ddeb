<template>
  <div class="taglist-block">
    <div v-for="(tag, index) in tagList" :key="index">
      <div class="icon-title">{{ tag.title || '' }}</div>
      <div class="icon-description-block">
        <div class="icon-description">{{ tag.description.join('; ') }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TagList',
  props: {
    tagList: {
      type: Array,
      default: () => []
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.icon-title {
  color: #212121;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 400;
}

.icon-description-block {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.icon-description {
  color: #757575;
  font-size: 14px;
  line-height: 1.5;
  font-weight: 400;
}

.taglist-block {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
