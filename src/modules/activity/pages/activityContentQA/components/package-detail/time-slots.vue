<template>
  <div class="timeslots">
    <h3>TimeSlots</h3>
    <ul>
      <li v-for="(timeSlot, index) in timeSlots" :key="index">{{ timeSlot }}</li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'TimeSlots',
  props: {
    timeSlots: {
      type: Array,
      default: () => []
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
ul,
ol {
  padding-left: 18px;
  list-style-type: disc;
}
</style>
