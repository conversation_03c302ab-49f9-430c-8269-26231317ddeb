<template>
  <div class="units">
    <h3>Units</h3>
    <ul>
      <li v-for="(unit, index) in units" :key="index">{{ unit }}</li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'Units',
  props: {
    units: {
      type: Array,
      default: () => []
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
ul,
ol {
  padding-left: 18px;
  list-style-type: disc;
}
</style>
