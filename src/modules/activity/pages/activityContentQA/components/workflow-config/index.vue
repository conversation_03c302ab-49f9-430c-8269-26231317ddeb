<template>
  <div>
    <a-drawer
      v-if="visible"
      title="WorkFlow Config"
      :visible.sync="visible"
      width="500"
      class="content-qa-drawer"
      :header-style="{
        position: 'relative'
      }"
      @close="handleClose"
    >
      <a-form-model ref="form" :model="form" layout="vertical">
        <a-form-model-item label="Use Workflows">
          <a-radio-group v-model="form.use_config.workflows">
            <a-radio :value="true">Yes</a-radio>
            <a-radio :value="false">No</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <div v-if="form.use_config.workflows" class="workflow">
          <div v-for="(_, index) in form.config.workflows" :key="index">
            <div class="title-block">
              <h3>Step {{ index + 1 }}</h3>
              <a-button @click="handleClear(index)">Clear</a-button>
            </div>
            <a-form-model-item>
              <a-textarea
                v-model="form.config.workflows[index]"
                placeholder="Please input workflow steps"
                :allow-clear="true"
                auto-size
              />
            </a-form-model-item>
          </div>
          <div class="add-item-box" @click="handleAddWorkflows">
            <a-icon type="plus"></a-icon>
            <h5>Add Workflow</h5>
          </div>
        </div>
      </a-form-model>
      <div
        :style="{
          position: 'absolute',
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e8e8e8',
          padding: '10px 16px',
          textAlign: 'right',
          left: 0,
          background: '#fff',
          borderRadius: '0 0 4px 4px'
        }"
      >
        <a-button type="primary" @click="handleSave">Save changes online</a-button>
      </div>
    </a-drawer>
    <a-button @click="handleOpen">WorkFlow Config</a-button>
  </div>
</template>

<script>
export default {
  name: 'WorkflowConfig',
  components: {},
  props: {
    form: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      visible: false
    }
  },
  methods: {
    handleOpen() {
      this.visible = true
    },
    handleClose() {
      this.visible = false
    },
    handleAddWorkflows() {
      this.form.config.workflows.push('')
    },
    handleClear(index) {
      this.form.config.workflows.splice(index, 1)
    },
    handleSave() {
      this.$emit('saveChanges')
    }
  }
}
</script>

<style lang="scss" scoped>
.add-item-box {
  background-color: #f9f9f9;
  text-align: center;
  cursor: pointer;
  border: 1px solid #d9d9d9;
  border-style: dashed;
  padding: 8px;
}

.title-block {
  display: flex;
  justify-content: space-between;
}

.content-qa-drawer {
  ::v-deep .ant-drawer-body {
    padding-bottom: 60px;
  }
}
</style>
