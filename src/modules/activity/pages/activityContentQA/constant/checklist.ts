export const DEFAULT_PACKAGE_CHECKLIST = [
  "Check for contradictions between inclusive_of and not_inclusive_of:\n Analysis Method: If the content in inclusive_of includes something, but the content in not_inclusive_of also includes something or a variant of it, this creates a contradiction.",
  "Check for inconsistencies between eligibility and unit: \nAnalysis Method: If the eligibility mentions a specific group (e.g., children or seniors) but the unit is per group without specifying this group, this can create confusion and inconsistency.",
  "Check for outdated information in validity and open_hours:\nAnalysis Method: If the validity date mentioned is in the past or the open_hours specify dates that have already passed, this information is outdated and needs to be updated.",
  "Check for unclear information between how_to_redeem and voucher_type_desc:\nAnalysis Method: If the how_to_redeem section describes a process that is vague or incomplete, while the voucher_type_desc gives conflicting or insufficient details, this can confuse the customer.",
  "Check for irrelevant information in additional_information:\nAnalysis Method: If additional_information includes details that are not directly related to the activity or package (e.g., historical facts about the venue), this information is irrelevant and should be removed or relocated.",
  "Check for contradictory information between package_name and eligibility:\nAnalysis Method: If the package_name states that it is for a specific group (e.g., Malaysians) but the eligibility section says it is not available for that group, this is a direct contradiction.",
  "Check for contradictions between eligibility and unit regarding group size:\nAnalysis Method: If the eligibility requires a minimum number of participants, but the unit is per group without specifying this requirement, this creates a conflict. For example, eligibility requires a minimum of 4 participants, but the unit is per group of 8.",
  "Check for contradictions between additional_information and how_to_redeem regarding arrival time:\nAnalysis Method: If additional_information and how_to_redeem provide different instructions for arrival times, this creates a contradiction. For example, one says to arrive 10 minutes early, while the other says 20 minutes early.",
  "Check for contradictions within open_hours:\nAnalysis Method: If open_hours provide conflicting information about the days and times the attraction is open, this creates a contradiction. For example, one part says closed on Tuesdays and Wednesdays, while another lists hours for every day of the week.",
  "Check for contradictions within tags regarding cancellation and refund policies:\nAnalysis Method: If tags provide conflicting information about cancellation and refund policies, this creates a contradiction. For example, one tag says no cancellations or refunds, while another tag allows for 80% refunds.",
  "Check for contradictions within how_to_redeem regarding redemption times:\nAnalysis Method: If how_to_redeem provides different redemption times, this creates a contradiction. For example, one part says redemption starts at 08:30, while another lists operational hours as starting from 09:30.",
  'Check for unclear information within inclusive_of regarding currency:\nAnalysis Method: If inclusive_of mentions a monetary value without specifying the currency, this creates ambiguity. For example, "$10 beverage discount voucher" could be confusing if the currency is not specified.',
  "Check for unclear information within open_hours regarding specific days and exceptions:\nAnalysis Method: If open_hours do not specify whether they apply daily, or include exceptions like holidays or seasonal variations, this creates ambiguity. For example, listing hours without indicating the days they apply to or mentioning holidays.",
];

export const DEFAULT_PACKAGE_CHECKLIST_V2 = [
  {
    sections: ["eligibility", "units"],
    analysisMethod:
      "If the content in eligibility or unit section includes something, but the content in vice versa also includes something or a variant of it, which is different age range for the same group, this creates a contradiction.\nIf the content in eligibility section only writes the certain groups, but the age range is not specified, it is unclear.\nIf the content in eligibility writes something about the date, but compared to today's date, it is already expired, it is outdated.",
  },
  {
    sections: ["inclusive_of", "not_inclusive_of"],
    analysisMethod:
      "If the content in inclusive_of section includes something, but the content in not_inclusive_of section also includes something or a variant of it, this creates a contradiction.\nIf the content in inclusive_of or not_inclusive_of section only writes the currency symbol, but the specific currency is not specified, it is unclear.\nIf the content in inclusive_of or not_inclusive_of section writes something about the date, but compared to today's date, it is already expired, it is outdated.",
  },
  {
    sections: ["inclusive_of", "package_name"],
    analysisMethod:
      "If the content in package_name or inclusive_of section includes something, but the content in vice versa also includes something or a variant of it, this creates a contradiction.\nIf the content in inclusive_of or package_name section writes something about the date, but compared to today's date, it is already expired, it is outdated.",
  },
  {
    sections: ["additional_information", "eligibility"],
    analysisMethod:
      "If the content in additional_information or eligibility section includes something, but the content in vice versa also includes something or a variant of it, this creates a contradiction.\nIf the content in additional_information or eligibility section writes something about the date, but compared to today's date, it is already expired, it is outdated.",
  },
  {
    sections: ["open_hours", "how_to_redeem"],
    analysisMethod:
      "If the content in open_hours or how_to_redeem section includes something, but the content in vice versa also includes something or a variant of it, this creates a contradiction.\nIf the content in open_hours or how_to_redeem section only writes the time, but the day of week is not specified, it is unclear.\nIf the content in open_hours or how_to_redeem section writes something about the date, but compared to today's date, it is already expired, it is outdated.",
  },
  {
    sections: ["cancellation_policy", "amendment_policy"],
    analysisMethod:
      "If the content in cancellation_policy section includes something, but the content in amendment_policy section also includes something or a variant of it, this creates a contradiction.\nIf the content in cancellation_policy or amendment_policy section only writes the certain number of days, but the day range is not related on the selected date, it is unclear.\nIf the content in cancellation_policy or amendment_policy section writes something about the date, but compared to today's date, it is already expired, it is outdated.",
  },
  {
    sections: ["cancellation_policy"],
    analysisMethod:
      "If the content in cancellation_policy section includes something, but the content in cancellation_policy section includes something or a variant of it, this creates a contradiction.\nIf the content in cancellation_policy section only writes the certain number of days, but the day range is not related on the selected date, it is unclear.\nIf the content in cancellation_policy writes something about the date, but compared to today's date, it is already expired, it is outdated.",
  },
  {
    sections: ["how_to_redeem", "voucher_type_desc"],
    analysisMethod:
      "If the content in how_to_redeem section includes something, but the content in voucher_type section includes something or a variant of it, this creates a contradiction.\nIf the content in how_to_redeem section only writes the time, but the day of week is not specified, it is unclear.\nIf the content in how_to_redeem or voucher_type section writes something about the date, but compared to today's date, it is already expired, it is outdated.",
  },
  {
    sections: ["validity", "additional_information"],
    analysisMethod:
      "If the content in validity or additional_information includes something, but the validity provided is different, this creates a contradiction.\nIf the content in validity or additional_information section writes something about the date, but compared to today's date, it is already expired, it is outdated.",
  },
  {
    sections: ["validity", "package_name"],
    analysisMethod:
      "If the content in validity or package_name includes something, but the content in vice versa also includes something or a variant of it, this creates a contradiction.\nIf the content in validity or package_name section writes something about the date, but compared to today's date, it is already expired, it is outdated.",
  },
];

export const DEFAULT_PACKAGE_CHECKLIST_V3 = [
  {
    sections: ["eligibility", "units"],
    analysisMethod:
      "Field Definitions: 'eligibility' describes the groups eligible for benefits (e.g., free entry), while 'units' defines the applicable age ranges for pricing categories. Analysis Steps: 1. Consistency Check: Compare age ranges in 'eligibility' and 'units' to ensure they align. If the age range for a group is missing or inconsistent, such as an undefined gap for children aged 3-4, it creates confusion and should be flagged. 2. Completeness Check: Ensure all groups in 'eligibility' are clearly defined in 'units' and vice versa. Missing age ranges or categories can lead to unclear information. 3. Clarity Check: If an age range or group definition is unclear, particularly around boundary ages, this needs to be highlighted as a minor issue. 4. Outdated Info Check: Verify if any dates in 'eligibility' are expired and update as necessary. 5. Logical Check: Ensure all descriptions are clear and logical, with no ambiguity in age range or category definitions. 6. Output Results: Identify contradictions, unclear, or outdated info, and suggest improvements. Minor issues like missing age ranges should result in a small deduction (e.g., 5 points).",
  },
  {
    sections: ["inclusive_of", "not_inclusive_of"],
    analysisMethod:
      "Field Definitions: 'inclusive_of' specifies all items and services that are part of the package, while 'not_inclusive_of' lists items or services that are excluded or require additional payment by the user. Analysis Steps: 1. Consistency Check: Carefully compare the content of 'inclusive_of' and 'not_inclusive_of' to ensure there are no contradictions. If an item listed in 'inclusive_of' appears in 'not_inclusive_of' with a conflicting definition or exclusion, this would be a contradiction. However, items like 'Other personal expenses,' which refer to costs outside the scope of the package, do not constitute a contradiction and should be treated separately. 2. Categorization and Explanation: Distinguish between core services and equipment necessary for the activity, which should be included in 'inclusive_of,' and personal expenses or optional items, which should be listed in 'not_inclusive_of.' Personal expenses are not considered a contradiction if they do not overlap with the included items. 3. Completeness Check: Verify that any symbols or currency mentioned in either section are fully specified and clear. If only the currency symbol is provided without specifying the exact currency, the information is unclear and incomplete. 4. Outdated Information Check: Review any dates mentioned in both sections to ensure they are current. If the dates are expired, the information is outdated and needs to be updated. 5. Logical and Semantic Check: Ensure that all descriptions are clear, logical, and unambiguous, particularly in distinguishing between what is included and what is excluded. 6. Output Results: Based on the analysis, identify any contradictions, unclear information, or outdated content, and provide suggestions for improvement. In cases where personal expenses are the only items excluded, no points should be deducted as this does not constitute a conflict.",
  },
  {
    sections: ["inclusive_of", "package_name"],
    analysisMethod:
      "Field Definitions: 'inclusive_of' lists the specific services and items included in the package, while 'package_name' is a brief, descriptive name of the package meant to give users a quick overview. Analysis Steps: 1. Consistency Check: Ensure there is no direct contradiction between the 'package_name' and the detailed content in 'inclusive_of'. The title can be a general description, with specifics provided in 'inclusive_of'. 2. Completeness Check: Verify that the detailed services in 'inclusive_of' align with the general concept of the 'package_name'. A title like 'Admission Ticket' is acceptable as a broad overview if detailed services are appropriately listed in 'inclusive_of'. 3. Relevance Check: Assess whether the items listed in 'inclusive_of' logically relate to the 'package_name'. If the title is meant to be brief and the services are fully described in 'inclusive_of', this does not constitute irrelevant information and should not be marked as such. 4. Outdated Info Check: Verify that any dates mentioned in 'inclusive_of' are current. If outdated, flag the content for updates. 5. Logical and Semantic Check: Ensure the descriptions in both sections are clear, logical, and provide a cohesive understanding of what the package offers. 6. Output Results: Identify any contradictions, unclear, or outdated information, and suggest improvements. If the 'package_name' serves as a general descriptor with details in 'inclusive_of', this should not be considered irrelevant and no points should be deducted.",
  },
  {
    sections: ["additional_information", "eligibility"],
    analysisMethod:
      "Field Definitions: 'additional_information' provides extra details or requirements for participation, such as health conditions or behavioral expectations, while 'eligibility' defines who qualifies to participate, typically focusing on age and ticket requirements. Analysis Steps: 1. Consistency Check: Review both sections to ensure there is no direct conflict. Recognize that 'eligibility' typically addresses age and ticketing, while 'additional_information' may include broader participation conditions. 2. Categorization: Differentiate between the types of requirements listed in each section. For example, 'eligibility' may focus on age, while 'additional_information' includes health advisories or behavioral restrictions. These differences are not contradictions but complementary details. 3. Completeness Check: Ensure the information in 'eligibility' and 'additional_information' is complementary and not redundant. If specific participation conditions are listed in 'additional_information' and not in 'eligibility', this usually does not imply incompleteness or unclear information. 4. Relevance Check: Assess whether the details in 'additional_information' are logically related to the content in 'eligibility'. Even if 'additional_information' includes more specific requirements or advisories, they should be seen as relevant and not irrelevant. 5. Logical and Semantic Check: Ensure that both sections are clear and logically aligned, especially when describing participation requirements for different age groups or specific health conditions. 6. Output Results: Identify any contradictions, unclear, or outdated information, and provide suggestions for improvement. If 'additional_information' supplements 'eligibility' without conflict, no points should be deducted.",
  },
  {
    sections: ["open_hours", "how_to_redeem"],
    analysisMethod:
      "Field Definitions: 'open_hours' details the specific times and dates when a service or activity is available, while 'how_to_redeem' explains the steps and requirements for redeeming a service or product, including potential redemption times. Analysis Steps: 1. Consistency Check: Compare 'open_hours' and 'how_to_redeem' to ensure there are no contradictions between the available times and redemption requirements. If there is a conflict between opening and redemption times, it should be flagged. 2. Completeness Check: Verify that both sections specify times and the corresponding days of the week. If either only provides times without specifying days, this may result in unclear information. Ensure that redemption instructions align with the service's available hours. 3. Outdated Info Check: Check for any dates mentioned in either section that may have already passed. If dates are outdated, the information should be updated. 4. Logical and Semantic Check: Ensure that the descriptions in both sections are clear and logical, with a consistent relationship between opening hours and redemption times. 5. Output Results: Identify any contradictions, unclear, or outdated information, and provide suggestions for improvement, especially in cases where time or date information may be incomplete or inconsistent.",
  },
  {
    sections: ["cancellation_policy", "amendment_policy"],
    analysisMethod:
      "Field Definitions: 'cancellation_policy' outlines the rules for booking cancellations, including deadlines and refund conditions, while 'amendment_policy' describes the rules for modifying bookings, such as allowed changes, deadlines, and potential fees. Analysis Steps: 1. Consistency Check: Compare the content of 'cancellation_policy' and 'amendment_policy' to ensure there are no contradictions. If a condition allowed in one section conflicts with the other, it should be flagged. 2. Completeness Check: Verify that any specified number of days or date ranges are clearly related to the booking date. If only days are mentioned without linking them to specific dates, this may lead to unclear information. Ensure that the time frames for cancellations and amendments are aligned and logically consistent. 3. Outdated Info Check: Check for any dates mentioned in either section that may have already passed. If dates are outdated, the information should be updated. 4. Logical and Semantic Check: Ensure that the descriptions in both sections are clear and logically connected, especially regarding the deadlines for cancellations and amendments. Any exceptions or special conditions should be consistently integrated into the overall policy. 5. Output Results: Identify any contradictions, unclear, or outdated information, and provide suggestions for improvement. Pay special attention to cases where time frames or date information may be incomplete or inconsistent, and propose solutions.",
  },
  {
    sections: ["cancellation_policy"],
    analysisMethod:
      "Field Definition: 'cancellation_policy' outlines the rules for booking cancellations, including deadlines, refund conditions, and associated fees. Analysis Steps: 1. Internal Consistency Check: Review the content within the 'cancellation_policy' to ensure there are no self-contradictory statements. If the policy includes conflicting conditions for the same situation, it should be flagged for revision. 2. Time Frame and Date Completeness Check: Ensure that any specified number of days or date ranges are clearly related to the booking date and are easy to understand. For example, if the policy mentions 'cancel within 7 days for a full refund,' it should clearly indicate this means '7 days before the event start date.' 3. Outdated Info Check: Verify that all dates mentioned in the 'cancellation_policy' are current. If any dates are expired, the policy should be updated to reflect the most recent information. 4. Logical and Semantic Check: Ensure that all clauses within the 'cancellation_policy' are clear, logical, and consistent, allowing users to easily understand the cancellation conditions and processes. Any exceptions or special conditions should be seamlessly integrated into the overall policy. 5. Output Results: Identify any internal contradictions, unclear information, or outdated content, and suggest improvements. Pay special attention to inconsistencies in time frames or the logical flow of the policy, and provide recommendations for clarification.",
  },
  {
    sections: ["how_to_redeem", "voucher_type_desc"],
    analysisMethod:
      "Field Definitions: 'how_to_redeem' explains the specific steps and requirements for redeeming a service or product, including times and locations, while 'voucher_type_desc' describes the type of voucher, its conditions, and restrictions. Analysis Steps: 1. Consistency Check: Compare the content in 'how_to_redeem' and 'voucher_type_desc' to ensure there are no contradictions between the redemption method and the voucher's usage conditions. If the methods differ between the sections, this should be flagged. 2. Completeness Check: Verify that 'how_to_redeem' specifies both the time and the day of the week for redemption. If only the time is mentioned without specifying the date or day, this may lead to unclear information. Ensure that the voucher's validity period in 'voucher_type_desc' aligns with the redemption times in 'how_to_redeem'. 3. Outdated Info Check: Check for any expired dates in 'how_to_redeem' or 'voucher_type_desc'. If any dates are outdated, the information should be updated to reflect the current validity and redemption period. 4. Logical and Semantic Check: Ensure that all descriptions in both sections are clear, logical, and consistent, particularly in the relationship between the voucher's conditions and the redemption process. Any exceptions or special conditions should be aligned between both sections to avoid confusion. 5. Output Results: Identify any contradictions, unclear information, or outdated content, and suggest improvements. Special attention should be given to cases where redemption methods and voucher conditions may be inconsistent, and recommendations for clarification should be provided.",
  },
  {
    sections: ["validity", "additional_information"],
    analysisMethod:
      "Field Definitions: 'validity' outlines the product or service's validity period, including start and end dates, while 'additional_information' provides extra details such as usage restrictions or special conditions. Analysis Steps: 1. Validity Consistency Check: Compare the validity periods in 'validity' and 'additional_information' to ensure there are no contradictions. If the validity provided in these sections differs, it should be flagged. 2. Date Information Completeness Check: Verify that both sections provide complete date information. If only partial details are provided or key dates are missing, this may result in unclear information. Ensure all dates and time frames clearly indicate the product or service's availability. 3. Outdated Info Check: Check for any expired dates in 'validity' or 'additional_information'. If any dates are outdated, the information should be updated. 4. Logical and Semantic Check: Ensure that the descriptions in both sections are clear, logical, and consistent, particularly regarding the relationship between validity and any additional conditions. Any exceptions or special conditions in 'additional_information' should align with the validity period. 5. Output Results: Identify any contradictions, unclear information, or outdated content, and suggest improvements. Pay special attention to inconsistencies between validity and additional conditions, and provide recommendations for clarification.",
  },
  {
    sections: ["validity", "package_name"],
    analysisMethod:
      "Field Definitions: 'validity' outlines the product or service's validity period, including start and end dates, while 'package_name' is a brief description or name of the package, often implying certain time-related aspects. Analysis Steps: 1. Consistency Check: Compare the content in 'validity' and 'package_name' to ensure there are no contradictions. If 'package_name' implies a specific time frame that differs from the 'validity' period, it should be flagged as a contradiction. 2. Date Information Completeness Check: Verify that both sections provide clear and consistent date information. If 'package_name' hints at a time period without explicitly stating it, 'validity' should provide the necessary details. Ensure that all dates and time frames clearly indicate the package's availability. 3. Outdated Info Check: Check for any expired dates in 'validity' or 'package_name'. If any dates are outdated, the information should be updated to reflect the current validity and relevance. 4. Logical and Semantic Check: Ensure that the descriptions in both sections are clear, logical, and consistent. 'package_name' should succinctly convey the content of the package, while 'validity' should clearly define the usage period, with both supporting each other. 5. Output Results: Identify any contradictions, unclear information, or outdated content, and suggest improvements. Pay special attention to cases where the title implies a time frame that must align with the validity period, and provide recommendations for clarification.",
  },
  {
    sections: ["tags", "eligibility"],
    analysisMethod:
      "Field Definitions: 'tags' provide brief descriptions of key characteristics or conditions of the activity, while 'eligibility' defines specific participation conditions, including age, participant numbers, and special requirements. Analysis Steps: 1. Consistency Check: Compare the content in 'tags' and 'eligibility' to ensure there are no contradictions. If conditions described in 'tags' differ from the requirements in 'eligibility', this indicates a contradiction. 2. Accuracy Check: Ensure that the descriptions in 'tags' and 'eligibility' are accurate and mutually supportive. Any discrepancies in participant numbers, age limits, or other conditions should be addressed. 3. Completeness Check: Verify that both sections provide complete information covering all necessary participation conditions. 'tags' should briefly mention key conditions that are fully explained in 'eligibility'. 4. Logical and Semantic Check: Ensure that descriptions in both sections are logically consistent and clearly expressed. The information in 'tags' should align with the detailed conditions in 'eligibility'. 5. Output Results: Identify any contradictions, unclear, or inaccurate information, and suggest improvements. Special attention should be given to ensuring consistency between participant requirements and other key conditions in both sections.",
  },
  {
    sections: ["open_hours"],
    analysisMethod:
      "Field Definition: 'open_hours' describes the operating times of an activity or venue, including specific dates, exceptions (such as holidays or special events), and any closures. Analysis Steps: 1. Date Accuracy Check: Verify that all dates mentioned in 'open_hours' accurately reflect the current operational schedule. Outdated dates, such as past holidays or private events, should be flagged and updated as necessary. 2. Exception Handling: Ensure that exceptions (e.g., public holidays or school holidays) are clearly described and understandable. Users should be able to determine how these exceptions affect regular opening hours. 3. Logical Consistency Check: Review the logical flow of the opening hours. If there are multiple conditions for a single date (e.g., a public holiday exception and a private event closure), ensure that the information is consistent and not contradictory. 4. Completeness Check: Confirm that all necessary dates and times are provided, without omissions. This includes ensuring that exceptions or special events are fully integrated into the overall schedule. 5. Output Results: Identify any outdated, unclear, or inconsistent information, and suggest improvements. Ensure that all dates are current and that exceptions are clearly explained. Outdated information should be updated promptly to maintain accuracy.",
  },
];
