export const DEFAULT_ERROR_TYPE = [
  {
    type: "Contradictory or inconsistent info",
    desc: "The content is inconsistent and contradictory, for example, the content of what's included states that tour guides are included, while what's not included states that tour guides are not included, and the age of children in eligibility and unit are inconsistent.",
  },
  {
    type: "Irrelevant info",
    desc: "Irrelevant info about the activity, E.g., The activity does not include a river cruise, but additional information has the relevant description",
  },
  {
    type: "Outdated info",
    desc: "The date described in the content is outdated",
  },
  {
    type: "Unclear info",
    desc: "Vague content, unable to understand what activity is offered from the content, for example, opening hours are provided but the specific opening dates are not specified from which day of the week to which day of the week",
  },
];

export const errorTypes = [
  "Contradictory Info",
  "Irrelevant info",
  "Unclear info",
  "Inconsistent info",
];

export const DEFAULT_ERROR_TYPE_OPTIONS = errorTypes?.map((ele) => ({
  label: ele,
  key: ele,
  value: ele,
}));
