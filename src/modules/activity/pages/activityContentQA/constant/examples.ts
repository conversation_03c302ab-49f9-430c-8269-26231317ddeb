export const DEFAULT_ACT_HIGHLIGHT_ANALYST_EXAMPLES = [
  {
    originTexts: [
      "To ensure a pleasant experience for all visitors, a timed entry ticket is required to enter SkyPark Observation Deck after 16:00’",
      "Entry before 16:00, timeslot based",
    ],
    problematicContent:
      "a timed entry ticket is required to enter SkyPark Observation Deck after 16:00’....’Entry before 16:00’",
    explanation:
      "This suggests two different times as the entry rules, which is contradictory",
  },
];
export const DEFAULT_PACKAGE_DETAIL_ANALYST_EXAMPLES = [
  {
    originTexts: [
      "Children aged 12 and above are charged the same as adults.",
      "According to restaurant policy, children must order from the menu, with childrens meal reservations limited to those under 10 years old, and those aged 10 and above must order an adult meal.",
    ],
    problematicContent:
      "Children aged 12 and above are charged the same as adults. ... those aged 10 and above must order an adult meal.",
    explanation:
      "This suggests two different age thresholds for adult pricing, which is contradictory.",
    suggestedRevision:
      "For childrens meal pricing, the cut-off age is 10. All individuals aged 10 and above will be charged adult prices.",
  },
  {
    originTexts: [
      "What’s included: Lunch",
      "All other sections: Lunch is not included/Lunch is your own expense",
    ],
    problematicContent: "included......not included/is your own expenses",
    explanation:
      "Differing arrangements regarding lunch provision across different sections, which are contradictory",
  },
  {
    originTexts: [
      "Voucher type: ‘Enter the venue directly with this voucher",
      "How to redeem: ‘Present your voucher and exchange it for a physical ticket’",
    ],
    problematicContent:
      "directly with this voucher......exchange it for a physical ticket",
  },
];

export const EMPTY_EXAMPLE = {
  originTexts: ["", ""],
  problematicContent: "",
  explanation: "",
  suggestedRevision: "",
};

export const DEFAULT_PACKAGE_DETAIL_ANALYST_EXAMPLES_V2 = [
  {
    sections: [
      {
        postion: "eligbility",
        content:
          "Child ticket: Aged 4-11; toodlers or elementary school students",
      },
      {
        postion: "units",
        content: "Per group (Group of 8)",
      },
    ],
    error_type: "Contradictory info",
    analysis:
      "The eligibility says there is a price for children, while the units is per group, which is a conflict of content due to no child price. This type of error will cause the customer to be unable to identify whether there is a child price or whether the whether the child will be the same price as adults, thus affecting the customer's booking from Klook. This is a major error, so 15 points will be deducted.",
    severity: "Major",
    score: 15,
  },
  {
    sections: [
      {
        postion: "eligbility",
        content: "This offer is not available for Malaysian passport holders",
      },
      {
        postion: "package_title",
        content: "Malaysian private group ticket (Valid until 31 July 2024)",
      },
    ],
    error_type: "Contradictory info",
    analysis:
      "The eligibility says it is not available for Malaysians, while the package title says it is only available for Malaysians, which is a conflict of content. This type of error will cause the customer to be unable to identify the applicability of this package, thus affecting the customer's booking. This is a major error, so 15 points will be deducted.",
    severity: "Major",
    score: 15,
  },
  {
    sections: [
      {
        postion: "eligbility",
        content:
          "This package requires a minimum of 4 participant(s). If the minimum is not met, you can book an alternative date or request a full refund",
      },
      {
        postion: "units",
        content: "Per group (Group of 8)",
      },
    ],
    error_type: "Irrelevant info",
    analysis:
      "The eligibility says require a minimum of 4 participants, while the units is per group price, which shows an irrelevant content with per group. This type of error will cause the customer receiving the information which is not related to the information but not affecting the customer's booking. This is a minor error, so 5 points will be deducted.",
    severity: "Minor",
    score: 5,
  },
  {
    sections: [
      {
        postion: "what's_included",
        content: "Dinner",
      },
      {
        postion: "not_included",
        content: "Meals and beverages",
      },
    ],
    error_type: "Contradictory info",
    analysis:
      "The what's included says it includes a dinner, while the not included says not including meals and beverages, which is a conflict of content. This type of error will cause the customer to be unable to identify whether dinner is included, thus affecting the customer's booking. This is a major error, so 15 points will be deducted.",
    severity: "Major",
    score: 15,
  },
  {
    sections: [
      {
        postion: "additional_information",
        content:
          "Guests are required to come 10 minutes before their scheduled slot for processing of tickets",
      },
      {
        postion: "how_to_redeem",
        content:
          "Please arrive 20 minutes before your booked time slot to avoid any unnecessary delays",
      },
    ],
    error_type: "Contradictory info",
    analysis:
      "The additional information says guests are required to come 10 minutes in advance, while how to redeem says 20 minutes, which is a conflict of content. This type of error will potentially cause confusion about how early they need to arrive. This is a major error, so 15 points will be deducted.",
    severity: "Major",
    score: 15,
  },
  {
    sections: [
      {
        postion: "opening_hours",
        content:
          "The attraction is open six days a week, with closures on Tuesdays and Wednesdays, respectively, until further notice",
      },
      {
        postion: "opening_hours",
        content: "Monday-Sunday: 10:00-20:00",
      },
    ],
    error_type: "Contradictory info",
    analysis:
      "The opening hours say open six days a week, while the park is closed on both Tuesdays and Wednesdays, which is a conflict of content due to the closure for two days and the open days being five days a week and also conflict with Monday-Sunday. This type of error will cause the customer to be unable to identify the true closure days, thus negatively impacting the overall customer experience and causing a refund. This is a major error, so 15 points will be deducted.",
    severity: "Major",
    score: 15,
  },
  {
    sections: [
      {
        postion: "tags",
        content:
          "The voucher is valid for 30 day(s) from the booking confirmation date. It expires at 22:00 on the last day",
      },
      {
        postion: "package_title",
        content: "Malaysian private group ticket (Valid until 31 July 2024)",
      },
    ],
    error_type: "Contradictory info",
    analysis:
      "The voucher states it's valid for 30 days from booking confirmation until 22:00 on the last day. However, the package title indicates the admission ticket is valid until July 31, creating contradictory information about the ticket's expiry. This discrepancy could confuse customers and impact their satisfaction. This is a critical error, so 25 points will be deducted.",
    severity: "Critical",
    score: 25,
  },
  {
    sections: [
      {
        postion: "tags",
        content: "No cancellations, refunds, or changes can be made",
      },
      {
        postion: "tags",
        content:
          "80% refunds will be issued for cancellations made before a voucher is redeemed. Contact Klook customer service for any refunds",
      },
    ],
    error_type: "Contradictory info",
    analysis:
      "The policy states that no cancellations or refunds are allowed, but it also mentions an 80% refund for cancellations made before the activity. This contradictory information could confuse customers and impact their satisfaction. This is a critical error, so 25 points will be deducted.",
    severity: "Critical",
    score: 25,
  },
  {
    sections: [
      {
        postion: "how_to_redeem",
        content: "09:30-19:00",
      },
      {
        postion: "how_to_redeem",
        content: "Redemption time starts from 08:30",
      },
    ],
    error_type: "Contradictory info",
    analysis:
      "The how to redeem says that redemption time starts at 08:30, but the operational hours for redemption location are listed as 10:00-19:00. This creates contradictory information regarding when you can redeem the voucher versus when the service is actually available and there is a 5 hours gap. This discrepancy could lead to confusion for customers about when they can access the service. This is a major error, so 15 points will be deducted.",
    severity: "Major",
    score: 15,
  },
  {
    sections: [
      {
        postion: "how_to_redeem",
        content: "Present your voucher and exchange it for a physical ticket",
      },
      {
        postion: "voucher_type_desc",
        content: "Enter the venue directly with this voucher",
      },
    ],
    error_type: "Contradictory info",
    analysis:
      "The redemption instructions state that you can enter the venue directly with your voucher, but additional information specifies that you must redeem a physical ticket. This conflicting information may confuse customers about the correct redemption process. This is a major error, so 15 points will be deducted.",
    severity: "Major",
    score: 15,
  },
  {
    sections: [
      {
        postion: "opening_hours",
        content: "Telescope area 11:00-19:00",
      },
    ],
    error_type: "Unclear info",
    analysis:
      "The opening hours for the Telescope area are listed as 11:00-19:00. However, the information lacks clarity on whether these hours apply daily, specific days of the week, or include exceptions like holidays or seasonal variations. This ambiguity could make it difficult for customers to plan their visit accurately, potentially affecting their experience and satisfaction. This is a minor error, so 5 points will be deducted.",
    severity: "Minor",
    score: 5,
  },
  {
    sections: [
      {
        postion: "how_to_redeem",
        content:
          "Please arrive at the appointed location to meet the staff after redeeming your physical ticket",
      },
    ],
    error_type: "Unclear info",
    analysis:
      "The redemption instructions require meeting staff at a specific location, but the package details do not provide this location. This lack of information can confuse customers. This is a minor error, so 5 points will be deducted.",
    severity: "Minor",
    score: 5,
  },
  {
    sections: [
      {
        postion: "inclusive_of",
        content: "Free $10 beverage discount voucher",
      },
    ],
    error_type: "Unclear info",
    analysis:
      "This what's included says $10 beverage discount voucher, while there are many currencies using the same currency sign, which is unclear to the customer. This type of error will cause the customer to be confused about the currency for the dining voucher amount, thus affecting the customer's action to check the CEG team. This is a minor error, so 5 points will be deducted.",
    severity: "Minor",
    score: 5,
  },
  {
    sections: [
      {
        position: "additional_information",
        content: "Guests may change the reservations with valid tickets...",
      },
      {
        position: "inclusive_of",
        content: "1 Hong Kong Disneyland 1-Day ticket(s)...",
      },
    ],
    error_type: "No",
    analysis: "additional_information和inclusive_of之间没有明显的矛盾。",
    severity: "None",
    score: 0,
  },
];

export const EMPTY_EXAMPLE_V2 = {
  sections: [
    {
      postion: "",
      content: "",
    },
  ],
  error_type: "",
  analysis: "",
  severity: "",
  score: 0,
};
