import { DEFAULT_PACKAGE_CHECKLIST_V3 } from "./checklist.ts";
import { DEFAULT_ERROR_TYPE } from "./errorTypes.ts";
import { DEFAULT_PACKAGE_DETAIL_ANALYST_EXAMPLES_V2 } from "./examples.ts";
import { DEFAULT_PACKAGE_WORKFLOWS } from "./workflow.ts";

export const PKG_REDIS_KEY = "content_qa_package";

export const PKG_REDIS_KEY_LIST = [
  "examples",
  "checklist",
  "workflows",
  "error_types",
];

export const DEFAULT_CONFIG = {
  examples: DEFAULT_PACKAGE_DETAIL_ANALYST_EXAMPLES_V2,
  error_types: DEFAULT_ERROR_TYPE,
  workflows: DEFAULT_PACKAGE_WORKFLOWS,
  checklist: DEFAULT_PACKAGE_CHECKLIST_V3,
};
