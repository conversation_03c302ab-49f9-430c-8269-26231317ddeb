export const DEFAULT_SEVERITY = [
  {
    label: "Red Flag",
    desc: "Error causes revenue loss (ie. wrong price/timeslot setting, etc.)",
  },
  {
    label: "Critival",
    desc: "Error leads to lawsuits from either customer or other parties (ie.plagiarism, misleading or wrong information)",
  },
  {
    label: "Major",
    desc: "Error leads to customer not booking the activity at all or results to refunds of the activity due to it affecting their experience onsite",
  },
  {
    label: "Minor",
    desc: "Error does not affect customer conversion but may affect theeir user experience (ie. spelling, wrong localized text, unprofessional/LQ photos, etc.)",
  },
  {
    label: "Trivial",
    desc: "Error is not noticed by customer at all but does not follow some Klook standards of quality (ie. wrong grammar, not following standardized wording, wrong formatting, etc.)",
  },
  {
    label: "Reminder",
    desc: "Errors do not affect customer conversion or the user experieence",
  },
  {
    label: "Score Revised",
    desc: "Score revised after discussion on second opinion from BD team",
  },
];

export const severitys = [
  "Red Flag",
  "Critival",
  "Major",
  "Minor",
  "Trivial",
  "Reminder",
  "Score Revised",
];

export const SEVERITY_OPTIONS = severitys?.map((ele) => ({
  label: ele,
  key: ele,
  value: ele,
}));
