import { errorTypes } from "./errorTypes.ts";
import { severitys } from "./severity.ts";
export const DEFAULT_PACKAGE_WORKFLOWS = [
  `Initial Review:
  - Carefully read through the entire package details to get a comprehensive understanding of the content.
  - Take note of the main tags and sections to ensure alignment with the overall structure.`,
  `Checklist Analysis:
  - Systematically go through each item in the provided checklist.
  - For each checklist item, perform a detailed analysis to identify any errors, inconsistencies, or areas for improvement.`,
  `Error Identification:\n -Use the ${errorTypes} list to classify each identified issue.\n -Ensure that each error type is clearly marked and categorized.`,
  `Detailed Reporting:
  - For each identified issue, provide an analysis report consisting of the following parts: error type, analysis, score, and severity.
  - Error Type: Select an error type from contradictory info, contradictory policy, irrelevant info, outdated info and unclear info.
  - Analysis: Provide a detailed explanation of the issue, referencing specific parts of the package details. Use examples if necessary.
  - Score: Assign a score to each issue on a scale of 5 to 25 (5 for unclear, outdated and irrelevant content error, 15 for contradictory content error, 25 for contradictory policy content error). If there are no problems, return 0.-Severity: Choose a severity level from ${severitys}.`,
];

export const DEFAULT_ACTIVITY_WORKFLOWS = [
  "Read Package Details: Read the 「package details」 section to understand the specific content, rules, and guidelines of the package.",
  "Review Activity Details: Read the 「activity details」 part to understand the specific content, rules, and guidelines of the activity.",
  "Identify Contradictions and Issues: Identify any contradictions, inconsistencies, unclear,  outdated, or irrelevant expressions between the activity details and package details.",
  "Document Issues: Record all identified issues in detail, including the specific content and its location.",
  "Suggest Improvements: Provide specific revision suggestions for each issue, ensuring the content is clear, consistent, and accurate.",
  "Validate Improvements: Confirm that the suggested revisions are reasonable and consistent with the package details.",
  "Assign Score: Assign a score to each issue on a scale of 5 to 15 (5 for unclear, outdated, irrelevant content error, 15 for contradictory and inconsistent content error), where a higher score indicates a more severe issue.",
];
