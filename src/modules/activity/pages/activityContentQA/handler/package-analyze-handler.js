import { getLLMChat, getPackageDetails } from '../apis/index.js'
import { getOriginalText, format2Arr, formatAnalyze, camelCaseToWords } from '../utils.js'
import { lang_array } from '../constant/language.ts'
import { PRESET_ID_MAP } from '../constant/presetId.ts'

export async function getPkgDetails(payload) {
  const { package_id, k_lang } = payload
  const pkgDetail = await getPackageDetails({
    package_id,
    k_lang
  })
  const {
    pkgSectionObj = {},
    package_name = '',
    activity_name = '',
    pkgUnits = [],
    pkgTimeSlots = [],
    tags = [],
    itineraryObj = {}
  } = pkgDetail
  const pkgInfo = {
    ...pkgSectionObj,
    package_name,
    activity_name,
    units: pkgUnits?.join(','),
    tags: tags?.join(','),
    time_slots: pkgTimeSlots?.join(',')
  }
  return {
    pkgDetail,
    pkgInfo,
    itineraryObj
  }
}

const genCheckItem = ({ checklist, packageDetail, examples, language }) => {
  return checklist?.map((checkItem) => {
    const { sections = [], analysisMethod = '' } = checkItem
    const obj = _.pick(packageDetail, sections)
    const objStr = sections?.map((key) => `${key}: ${obj[key]}`)?.join('\n')
    const hasAllSection = Object.keys(obj)?.length === sections?.length
    return {
      sections: sections?.map((key) => ({
        position: key,
        content: obj[key] || '-'
      })),
      question: hasAllSection
        ? `The detail of this package is ${objStr}. Please help me
     to analyze it. The analyze method is ${analysisMethod}. If there is no problem, return none. Please output with ${language}`
        : ''
    }
  })
}

export async function getChecklistAnalyze({ packageDetail, configs }) {
  const { k_lang, config = {}, use_config = {}, gpt_model = '4o-mini' } = configs || {}
  const { label: language } = lang_array?.find((ele) => ele?.value === k_lang) || {}
  const { workflows = [], checklist = [], examples, error_types: _error_types = [] } = config || {}
  const error_types = _error_types?.map((ele) => ele?.type) || []
  const error_type_desc = _error_types?.reduce((obj, ele) => {
    const { type, desc } = ele
    obj[type] = desc
    return obj
  }, {})
  const list = genCheckItem({
    language,
    checklist,
    packageDetail
  })
  const { examples: useExamples, error_types: useErrorTypes, workflows: useWorkflows } = use_config || {}
  const inputs = {
    history: [],
    examples: useExamples ? examples : [],
    workflows: useWorkflows ? workflows : [],
    errorTypes: useErrorTypes ? error_types : [],
    errorTypeDesc: useErrorTypes ? error_type_desc : []
  }
  const genChecklistAnalyzePromise = ({ question, inputs, gpt_model }) => {
    return question
      ? getLLMChat({
          question,
          inputs,
          presetId: PRESET_ID_MAP[gpt_model]['CHECKLIST_ANALYZE'],
          userId: 'jywTest',
          userType: 'test'
        })
      : new Promise((resolve) => {
          resolve({
            success: true,
            result: ''
          })
        })
  }
  const promiseList = list?.map((ele) =>
    genChecklistAnalyzePromise({
      question: ele.question,
      inputs,
      gpt_model
    })
  )
  const allRes = await Promise.all(promiseList)
  const allResult = allRes?.map((ele) => {
    const { result, success } = ele
    return success ? result : ''
  })
  const checklistAnalyze = list.map((item, index) => {
    const { sections: _sections = [] } = item
    const analyze = format2Arr(allResult[index])?.filter((ele) => !!ele)
    const sections = _sections?.map((ele) => {
      const { position, content } = ele
      return {
        position,
        content: content?.length > 50 ? content.slice(0, 50) + '...' : content
      }
    })
    return {
      sections,
      analyze
    }
  })
  return checklistAnalyze
}

export async function getOverallAnalyze({ packageDetail, configs }) {
  const { k_lang, gpt_model = '4o-mini', use_config = {}, config = {} } = configs || {}
  const { examples } = config || {}
  const { label: language } = lang_array?.find((ele) => ele?.value === k_lang) || {}
  const { examples: useExamples } = use_config || {}
  const { result: overallAnalyze } = await getLLMChat({
    question: `Here is the detail of this package: ${JSON.stringify(
      packageDetail
    )}. Please output with ${language}`,
    inputs: {
      history: [],
      activityName: packageDetail?.activity_name,
      examples: useExamples ? examples : []
    },
    presetId: PRESET_ID_MAP[gpt_model]['OVERALL_ANALYZE'],
    userId: 'jywTest',
    userType: 'test'
  })
  return overallAnalyze
}

export async function getItinerayAnalyze({ itineraryObj, configs }) {
  const { k_lang, gpt_model = '4o-mini' } = configs || {}
  const { label: language } = lang_array?.find((ele) => ele?.value === k_lang) || {}
  const { result: itineraryAnalyze } = await getLLMChat({
    question: `Here is the itinerary of this package: ${JSON.stringify(
      itineraryObj
    )}. Please output with ${language}`,
    inputs: {
      history: []
    },
    presetId: PRESET_ID_MAP[gpt_model]['ITINERARY_ANALYZE'],
    userId: 'jywTest',
    userType: 'test'
  })
  return itineraryAnalyze
}

export function getChecklistExportData({ aid, pid, checklistAnalyze }) {
  const formatedChecklistAnalyze = checklistAnalyze.map((ele) => ({
    sections: ele?.sections,
    analyze: formatAnalyze(ele?.analyze)
  }))
  const maxSectionsLen = Math.max(...formatedChecklistAnalyze.map((ele) => ele?.sections?.length))
  const maxAnalyzeLen = Math.max(...formatedChecklistAnalyze.map((ele) => ele?.analyze?.length))
  return formatedChecklistAnalyze.map((item) => {
    const { sections = [], analyze = [] } = item
    const res = {
      AID: aid,
      'Pkg ID': pid
    }

    for (let i = 1; i <= maxSectionsLen; i++) {
      const sectionItem = sections?.[i - 1] || {}
      res[`Section ${i}`] = sectionItem?.position || '-'
      res[`Text ${i}`] = sectionItem?.content || '-'
    }

    for (let j = 1; j <= maxAnalyzeLen; j++) {
      const analyzeItem = analyze?.[j - 1] || {}
      res[`Error type ${j}`] = analyzeItem?.errorType || '-'
      res[`Checklist analysis ${j}`] = analyzeItem?.analysis || '-'
      res[`Deduction score ${j}`] = analyzeItem?.score || '-'
      res[`Severity ${j}`] = analyzeItem?.severity || '-'
    }

    return res
  })
}

export function getOverallExportData({ aid, pid, overallAnalyze }) {
  const res = []
  for (const key of Object.keys(overallAnalyze || {})) {
    ;(overallAnalyze[key] || []).forEach((item) => {
      const { explanation, suggestedRevision, score } = item
      res.push({
        AID: aid,
        'Pkg ID': pid,
        Text: getOriginalText(item) || '-',
        'Analysis category': camelCaseToWords(key),
        'Analysis text': `"explanation": "${explanation}"
"suggestedRevision": "${suggestedRevision}"`,
        Score: score || '-'
      })
    })
  }
  return res
}

// export function getItineraryExportData({ aid, pid, itineraryAnalyze }) {

// }
