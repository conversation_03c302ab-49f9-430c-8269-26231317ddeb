<template>
  <div class="content-qa-container">
    <a-tabs :active-key="activeTab" @tabClick="handleChangeTabs">
      <a-tab-pane key="packageAnalyze" tab="packageAnalyze">
        <PackageAnalyze class="bg-white pd-20" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import PackageAnalyze from './pages/package-analyze.vue'
export default {
  name: 'ActivityGrouping',
  components: {
    PackageAnalyze,
  },
  data() {
    return {
      activeTab: 'packageAnalyze'
    }
  },
  methods: {
    handleChangeTabs(targetTab) {
      this.activeTab = targetTab
    }
  }
}
</script>

<style lang="scss" scoped>
.bg-white {
  // background: #f5f5f5;
}
.pd-20 {
  padding: 20px;
}
.content-qa-container {
  padding: 10px;
}
</style>
