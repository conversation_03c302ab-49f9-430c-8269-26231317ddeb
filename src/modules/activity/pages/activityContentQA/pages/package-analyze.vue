<template>
  <a-spin :spinning="redisRequestLoading" :delay="500" tip="Loading...">
    <div class="content-qa">
      <common-search :form="form" @getPkgDetails="getPkgDetails" @saveChanges="handleSaveChanges" />
      <div v-if="pkgDetail.package_id" class="package-info-analyse-block">
        <div class="package-detail flex-column mw-40">
          <div class="button-group">
            <EditPkgDetail :oldPkgInfo="pkgInfo" @change="handleEditPkgInfo" />
            <a-button @click="handleAnalyze">Analyse</a-button>
          </div>
          <PackageDetail :package-data="pkgDetail" />
        </div>
        <a-spin :spinning="loading">
          <div class="analyse-block gap-20">
            <div
              v-for="currAnalyzeKey in Object.keys(analyze)"
              :key="currAnalyzeKey"
              class="flex-1 flex-column"
            >
              <div class="flex-between mb-8">
                <h3>{{ currAnalyzeKey }}</h3>
                <div>
                  <a-button
                    v-if="['checklistAnalyze', 'overallAnalyze'].includes(currAnalyzeKey)"
                    @click="handleExport(currAnalyzeKey)"
                  >
                    Export
                  </a-button>
                </div>
              </div>
              <JSONDisplay :json="analyze[currAnalyzeKey]" />
            </div>
          </div>
        </a-spin>
      </div></div
  ></a-spin>
</template>

<script>
import PackageDetail from '../components/package-detail/index.vue'
import CommonSearch from '../components/common-search/index.vue'
import JSONDisplay from '../components/json-display/index.vue'
import EditPkgDetail from '../components/edit-pkgdetail/index.vue'
import { lang_array } from '../constant/language.ts'
import { MODEL_MAP } from '../constant/model.ts'
import {
  getChecklistAnalyze,
  getOverallAnalyze,
  getItinerayAnalyze,
  getPkgDetails,
  getChecklistExportData,
  getOverallExportData
} from '../handler/package-analyze-handler.js'
import { getRedis, setRedis } from '../apis/index.js'
import { DEFAULT_CONFIG, PKG_REDIS_KEY, PKG_REDIS_KEY_LIST } from '../constant/redis.ts'
import { exportToXlsx } from '../utils.js'

export default {
  name: 'PackageAnalyze',
  components: {
    PackageDetail,
    CommonSearch,
    JSONDisplay,
    EditPkgDetail
  },
  data() {
    return {
      form: {
        package_id: '',
        k_lang: 'en_US',
        gpt_model: MODEL_MAP['4o-mini'],
        config: {},
        use_config: {
          examples: true,
          error_types: true,
          workflows: true,
          checklist: true
        }
      },
      pkgDetail: {
        package_id: '',
        package_name: '',
        tagList: [],
        pkgDetailSections: []
      },
      itineraryObj: {},
      pkgInfo: {},
      analyze: {},
      loading: false,
      redisRequestLoading: false,
      // highLightWords: [],
      lang_array
    }
  },
  provide() {
    return {
      getHighLightWords: () => {
        // return this.highLightWords
        return []
      }
    }
  },
  watch: {
    'pkgDetail.package_id': {
      immediate: true,
      handler(v) {
        // 清空分析面板
        this.analyze = {}
      }
    }
  },
  created() {
    this.getConfigs()
  },
  methods: {
    async getConfigs() {
      const promiseList = PKG_REDIS_KEY_LIST.map((key) => {
        return getRedis(`${PKG_REDIS_KEY}_${key}`)
      })
      this.redisRequestLoading = true
      Promise.all(promiseList)
        .then((res) => {
          const config = res.reduce((obj, currRes, index) => {
            const key = PKG_REDIS_KEY_LIST[index]
            obj[key] = currRes || DEFAULT_CONFIG[key]
            return obj
          }, {})
          this.$set(this.form, 'config', config)
        })
        .finally(() => {
          this.redisRequestLoading = false
        })
    },
    async getPkgDetails(formdata) {
      const { pkgDetail, pkgInfo, itineraryObj = {} } = await getPkgDetails(formdata)
      this.pkgDetail = pkgDetail
      this.pkgInfo = pkgInfo
      this.itineraryObj = itineraryObj
    },
    async handleAnalyze() {
      const { pkgInfo: packageDetail, form: configs, itineraryObj = {} } = this
      this.loading = true
      const checklistAnalyze = await getChecklistAnalyze({
        packageDetail,
        configs
      })
      const overallAnalyze = await getOverallAnalyze({
        packageDetail,
        configs
      })
      const itineraryAnalyze = Object.keys(itineraryObj).length
        ? await getItinerayAnalyze({
            itineraryObj,
            configs
          })
        : null
      const analyze = {
        checklistAnalyze,
        overallAnalyze
      }
      if (itineraryAnalyze) {
        analyze.itineraryAnalyze = itineraryAnalyze
      }
      this.analyze = analyze
      this.loading = false
    },
    async handleSaveChanges(key) {
      const value = (this.form || {})?.config?.[key]
      const redisKey = `${PKG_REDIS_KEY}_${key}`
      const res = await setRedis({
        key: redisKey,
        value
      })
      if (res) {
        this.$message.success('The changes have been saved!')
      } else {
        this.$message.error('The changes have not been saved, Please try again')
      }
    },
    handleEditPkgInfo(val) {
      this.pkgInfo = {
        ...val
      }
    },
    clear() {
      this.form?.resetFields()
    },
    handleExport(key) {
      switch (key) {
        case 'checklistAnalyze':
          exportToXlsx({
            filename: `ai_tool_checklist_${this.form.activity_id}_${this.form.package_id}.csv`,
            sheetList: [
              {
                name: 'checklist',
                data: getChecklistExportData({
                  aid: this.form.activity_id,
                  pid: this.form.package_id,
                  checklistAnalyze: this.analyze.checklistAnalyze
                })
              }
            ]
          })
          break
        case 'overallAnalyze':
          exportToXlsx({
            filename: `ai_tool_overall_${this.form.activity_id}_${this.form.package_id}.csv`,
            sheetList: [
              {
                name: 'overall',
                data: getOverallExportData({
                  aid: this.form.activity_id,
                  pid: this.form.package_id,
                  overallAnalyze: this.analyze.overallAnalyze
                })
              }
            ]
          })
          break
        default:
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-input {
  display: flex;
  align-items: center;
  gap: 10px;
}
.content-qa {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.flex-1 {
  display: flex;
  flex: 1;
}

.package-info-analyse-block {
  display: flex;
  gap: 40px;
}
.button-group {
  justify-content: flex-end;
  gap: 10px;
  display: flex;
}
.analyse-block {
  display: flex;
}
.gap-20 {
  gap: 20px;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex {
  display: flex;
}
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.mw-40 {
  max-width: 40%;
}
.mb-8 {
  margin-bottom: 8px;
}
</style>
