import XLSX from 'xlsx'

export const originTextReg = /^originText\d+$/

export const getHighLightWords = (analyze) => {
  const highLightWords = Object.keys(analyze)?.reduce((originTextList, errorType) => {
    const currErrorTypeContentList = analyze?.[errorType] || []
    currErrorTypeContentList?.forEach((ele) => {
      Object.keys(ele)?.forEach((key) => {
        if (originTextReg.test(key)) {
          originTextList.push(ele?.[key])
        }
      })
    })
    return originTextList
  }, [])
  return highLightWords
}

export const format2Arr = (res) => {
  if (Array.isArray(res)) {
    return res
  }
  return [res]
}

export const formatAnalyze = (res) => {
  const keyList = ['issues', 'errors']
  for (const key of keyList) {
    if (res?.[0]?.[key]) {
      return res?.[0]?.[key]
    }
  }
  return res
}

export function camelCaseToWords(input) {
  return input
    .replace(/([A-Z])/g, ' $1')
    .trim()
    .toLowerCase()
}

export function exportToXlsx({ filename = '', sheetList = [] }) {
  if (!filename || !sheetList.length) {
    console.error(`Invalid parameters for exportToXlsx: ${filename}, ${sheetList}`)
    return
  }

  const workbook = XLSX.utils.book_new()
  sheetList.forEach((sheet) => {
    const { name: sheetName, data } = sheet
    const worksheet = XLSX.utils.json_to_sheet(data)
    XLSX.utils.book_append_sheet(workbook, worksheet, camelCaseToWords(sheetName))
  })
  XLSX.writeFile(workbook, filename)
}

export function getOriginalText(item) {
  let originText = item?.originText
  if (originText) {
    return originText
  }

  for (const key of Object.keys(item)) {
    if (originTextReg.test(key)) {
      originText = item?.[key]
      break
    }
  }

  return originText
}
