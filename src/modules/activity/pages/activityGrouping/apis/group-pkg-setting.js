export async function getGroupingRels(activityId) {
  const res = await ajax.get(ADMIN_API.act.get_grouping_rels, {
    params: {
      activity_id: activityId,
      language: 'en_US'
    }
  })
  return res
}

export async function updateGroupingRels(activity_id = '', grouping_pkg_rel = []) {
  const res = await ajax.post(ADMIN_API.act.update_grouping_rels, {
    data: {
      activity_id,
      group_items: grouping_pkg_rel
    }
  })
  return res
}

export async function updateGroupingStatus(activity_id = '', status) {
  const res = await ajax.post(ADMIN_API.act.update_grouping_status, {
    data: {
      activity_id,
      status
    }
  })
  return res
}
