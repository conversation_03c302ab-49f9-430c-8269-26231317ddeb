export async function getGroupingList(activityId) {
  const res = await ajax.get(ADMIN_API.act.get_grouping_list, {
    params: {
      activity_id: activityId,
      language: 'en_US'
    }
  })
  return res || {}
}

export async function getGroupingInfo(activityId, groupId) {
  const res = await ajax.get(ADMIN_API.act.get_grouping_info, {
    params: {
      activity_id: activityId,
      group_ids: [groupId].join(',')
    }
  })
  return res
}

export async function createGrouping({ activity_id, group_info }) {
  const res = await ajax.post(ADMIN_API.act.create_or_update_groupping, {
    data: {
      activity_id,
      group_info
    }
  })
  return res
}

export async function updateGrouping({ activity_id, group_info }) {
  const res = await ajax.post(ADMIN_API.act.create_or_update_groupping, {
    data: {
      activity_id,
      group_info
    }
  })
  return res
}

export async function deleteGrouping(activity, groupIds) {
  const res = await ajax.post(ADMIN_API.act.delete_grouping, {
    data: {
      activity_id: activity,
      group_ids: groupIds
    }
  })
  return res === false ? false : true
}

export async function sortGrouping(activity, groupIds) {
  const res = await ajax.post(ADMIN_API.act.sort_grouping, {
    data: {
      activity_id: activity,
      group_ids: groupIds
    }
  })
  return res === false ? false : true
}

export async function sentToTranslate(data) {
  return await ajax.post(ADMIN_API.act.create_group_meegle_translate_task, {
    data
  })
}

export async function getMeegleTranslateTaskInfo({ activity_id, meegle_id }) {
  return await ajax.get(ADMIN_API.act.get_group_meegle_translate_task_info, {
    params: {
      activity_id,
      meegle_id
    }
  })
}
