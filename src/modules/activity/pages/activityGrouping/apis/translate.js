const googleTranslateApi = '/v1/translationtrpserv/translate/text'
const googleTranslateApiHeader = {
  'Content-Type': 'application/json'
}

function genTranslatePromise({ target_locale = 'en_US', contents = [] }) {
  return ajax.postBody({
    url: googleTranslateApi,
    headers: googleTranslateApiHeader,
    data: {
      client_id: 'attraction',
      target_locale,
      content_type: 'plain',
      no_need_cache: true,
      contents
    }
  })
}
export async function getTranslatedText(params) {
  const res = await genTranslatePromise(params)
  return res
}

export async function batchGetTranslatedText({ target_locales = ['en_US'], contents = [] }) {
  const promises = target_locales?.map((target_locale) => {
    return genTranslatePromise({ target_locale, contents: contents?.filter((ele) => !!ele) })
  })
  const resList = await Promise.all(promises)
  const translationMap = resList.reduce((currMap, res, promiseIndex) => {
    const { result = {} } = res
    const { translation = [] } = result
    const currLanguage = target_locales[promiseIndex]
    currMap[currLanguage] = translation?.map((ele) => ele?.translation_content) || []
    return currMap
  }, {})
  return translationMap
}
