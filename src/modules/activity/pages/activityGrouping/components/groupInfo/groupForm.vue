<template>
  <a-form-model ref="form" :model="form" :rules="formRules">
    <a-alert v-if="editDisabled" :message="EDIT_PROTECT_TEXT" type="info" />
    <a-form-model-item :label="$t(GROUPING_TEXT_ID_MAP['GROUP_NAME(EN-US)'])" prop="group_name" required>
      <a-input
        v-model="form.group_name"
        :placeholder="$t(GROUPING_TEXT_ID_MAP['PLEASE_ENTER'])"
        :allow-clear="true"
        :show-count="true"
        :disabled="editDisabled"
      />
    </a-form-model-item>
    <a-form-model-item :label="$t(GROUPING_TEXT_ID_MAP['GROUP_DESC(EN-US)'])" prop="group_desc">
      <a-input
        v-model="form.group_desc"
        :placeholder="$t(GROUPING_TEXT_ID_MAP['PLEASE_ENTER'])"
        :allow-clear="true"
        :show-count="true"
        :disabled="editDisabled"
      />
    </a-form-model-item>
    <a-form-model-item :label="$t(GROUPING_TEXT_ID_MAP['PROMOTION_GROUP'])" prop="is_opt">
      <a-checkbox v-model="form.is_opt" :disabled="editDisabled">
        <div style="display: inline">
          {{ $t(GROUPING_TEXT_ID_MAP['SET_AS_PROMO_GROUP']) }}
          <a-tooltip placement="top">
            <template #title>
              <span>{{ $t(GROUPING_TEXT_ID_MAP['SET_AS_PROMO_GROUP_ICON_TEXT']) }}</span>
            </template>
            <a-icon type="question-circle"></a-icon>
          </a-tooltip>
        </div>
      </a-checkbox>
    </a-form-model-item>
    <GroupI18n
      :form="form"
      :group_name="form.group_name"
      :group_desc="form.group_desc"
      :disabled="editDisabled"
    />
    <div class="btn-group flex-end">
      <a-button style="margin-right: 8px" @click="handleCancle">
        {{ $t(GROUPING_TEXT_ID_MAP['CANCEL']) }}
      </a-button>
      <a-button type="primary" :disabled="editDisabled" @click="handleSubmit">{{
        $t(GROUPING_TEXT_ID_MAP['CONFIRM'])
      }}</a-button>
    </div>
  </a-form-model>
</template>

<script>
import GroupI18n from './groupI18n.vue'
import { lang_array, isValidText } from '../../utils'
import { getGroupingInfo } from '../../apis'
import { EDIT_PROTECT_TEXT, GROUPING_TEXT_ID_MAP } from '../../constant'
export default {
  name: 'GroupForm',
  components: {
    GroupI18n
  },
  inject: ['activityId'],
  props: {
    group_id: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      form: {
        group_name: '',
        group_desc: '',
        is_opt: false,
        languageFormData: []
      },
      editDisabled: false,
      formRules: {
        group_name: [
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, cb) => {
              const maxLen = 50
              this.validatorInputValid(rule, value, cb, maxLen)
            }
          }
        ],
        group_desc: [
          {
            trigger: 'change',
            validator: (rule, value, cb) => {
              const maxLen = 65
              this.validatorInputValid(rule, value, cb, maxLen)
            }
          }
        ]
      },
      lang_array,
      GROUPING_TEXT_ID_MAP,
      EDIT_PROTECT_TEXT
    }
  },
  created() {
    this.getCurrGroupInfo()
  },
  methods: {
    formatLanguageData(languageData = []) {
      const languageFormData = lang_array?.map((ele) => {
        const { val: languageName = '' } = ele
        const { group_name = '', group_desc = '' } =
          languageData?.find((item) => item.language === languageName) || {}
        return {
          language: languageName,
          group_name,
          group_desc
        }
      })
      return languageFormData
    },
    validatorInputValid: (rule, value, callback, maxLen) => {
      const isValid = isValidText(value)
      if (!isValid) {
        callback(new Error('Please Input America English'))
      } else if (value.length > maxLen) {
        callback(new Error(`Please do not enter more than ${maxLen} characters`))
      } else {
        callback()
      }
    },
    async getCurrGroupInfo() {
      const { group_id } = this
      if (!group_id) {
        const i18ns = []
        const languageFormData = this.formatLanguageData(i18ns)
        this.form.languageFormData = languageFormData
        return
      }
      const { items = [] } = await getGroupingInfo(this.activityId, group_id)
      const groupInfo = items?.find((ele) => ele?.group_id === group_id) || {}
      const { i18ns = [], edit_protect = false, ...basicInfo } = groupInfo
      const languageFormData = this.formatLanguageData(i18ns)
      this.form = {
        ...basicInfo,
        languageFormData: languageFormData
      }
      this.editDisabled = edit_protect
    },
    handleSubmit: _.throttle(function () {
      this.handleSubmitFormData()
    }, 300),
    handleSubmitFormData() {
      this.$refs.form?.validate?.((isValid) => {
        if (isValid) {
          const {
            group_name = '',
            group_desc = '',
            is_opt = false,
            languageFormData = [],
            group_id = ''
          } = this.form
          const formData = {
            group_name,
            group_desc,
            is_opt,
            i18ns: languageFormData || []
          }
          if (group_id) {
            formData.group_id = group_id
          }
          this.$emit('submit', formData)
        } else {
          this.$message.error('Something Invalid, Please Check Again')
        }
      })
    },
    handleCancle() {
      this.$emit('cancle')
    }
  }
}
</script>

<style lang="scss" scoped>
.flex-end {
  display: flex;
  justify-content: flex-end;
}
</style>
