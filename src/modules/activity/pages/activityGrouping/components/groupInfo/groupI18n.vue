<template>
  <div>
    <div class="manage-language-header">
      <h3>{{ $t(GROUPING_TEXT_ID_MAP['MANAGE_LANGUAGES']) }}</h3>
      <div class="button-group">
        <a-button @click="handleApplyEnlish" :disabled="disabled">{{
          $t(GROUPING_TEXT_ID_MAP['COPY_FROM_EN-US'])
        }}</a-button>
        <a-button @click="handleApplyAi" type="primary" :disabled="disabled">{{
          $t(GROUPING_TEXT_ID_MAP['COPY_FROM_AI_TRANSLATE'])
        }}</a-button>
      </div>
    </div>
    <div class="manage-language-area">
      <div class="manage-language-area-header">Group name: {{ form.group_name }}</div>
      <div v-for="(item, index) in form.languageFormData" :key="`group-name-${index}`">
        <a-form-item :label="lang_obj[item.language]">
          <a-input
            v-model="form.languageFormData[index].group_name"
            :placeholder="$t(GROUPING_TEXT_ID_MAP['PLEASE_ENTER'])"
            :disabled="disabled"
          />
        </a-form-item>
      </div>
    </div>
    <div class="manage-language-area">
      <div class="manage-language-area-header">Group Desc: {{ form.group_desc || '' }}</div>
      <div v-for="(item, index) in form.languageFormData" :key="`group-desc-${index}`">
        <a-form-item :label="lang_obj[item.language]">
          <a-input
            v-model="form.languageFormData[index].group_desc"
            :placeholder="$t(GROUPING_TEXT_ID_MAP['PLEASE_ENTER'])"
            :disabled="disabled"
          />
        </a-form-item>
      </div>
    </div>
  </div>
</template>

<script>
import { batchGetTranslatedText } from '../../apis/index.js'
import { lang_array, lang_obj } from '../../utils'
import { GROUPING_TEXT_ID_MAP } from '../../constant'

export default {
  name: 'GrouoI18n',
  props: {
    form: {
      type: Object,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      lang_array,
      lang_obj,
      loadingKey: 'updatable',
      translatedContents: {},
      GROUPING_TEXT_ID_MAP
    }
  },
  methods: {
    formatLanguageData(languageData = []) {
      const languageFormData = lang_array?.map((ele) => {
        const { val: languageName = '' } = ele
        const { group_name = '', group_desc = '' } =
          languageData?.find((item) => item.language === languageName) || {}
        return {
          language: languageName,
          group_name,
          group_desc
        }
      })
      return languageFormData
    },
    handleApplyEnlish() {
      const { group_name = '', group_desc = '', languageFormData = [] } = this.form || {}
      const _languageFormData =
        languageFormData?.map(({ language }) => {
          return {
            language,
            group_name,
            group_desc
          }
        }) || []
      this.form.languageFormData = _languageFormData
    },
    async getTranslateContents({ group_name = '', group_desc = '' }) {
      const languages = lang_array?.map((ele) => ele?.val)
      const translationMap = await batchGetTranslatedText({
        target_locales: languages,
        contents: [group_name, group_desc]
      })
      const enTranslate = translationMap['en_HK'] || []
      // 不找index的话容易串
      const nameTranslateIndex = enTranslate?.findIndex((ele) => ele === group_name)
      const descTranslateIndex = enTranslate?.findIndex((ele) => ele === group_desc)
      const translatedContents = languages?.map((language) => {
        return {
          language,
          group_name: translationMap[language]?.[nameTranslateIndex] || '',
          group_desc: translationMap[language]?.[descTranslateIndex] || ''
        }
      })
      return translatedContents
    },
    async handleApplyAi() {
      const { translatedContents = {} } = this
      const { group_name = '', group_desc = '' } = this.form
      const cacheKey = `${group_name}-${group_desc}`
      if (!translatedContents[cacheKey]) {
        this.$message.loading({ content: 'Translating, please wait....', key: this.loadingKey })
        const translatedContents = await this.getTranslateContents({ group_name, group_desc })
        this.form.languageFormData = translatedContents
        this.translatedContents[cacheKey] = translatedContents
        this.$message.success({ content: 'Translated!', key: this.loadingKey, duration: 2 })
      } else {
        this.form.languageFormData = this.translatedContents[cacheKey]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.manage-language-area {
  margin-bottom: 10px;
  padding: 12px 20px;
  border: 1px solid #ccc;
  border-radius: 5px;
  &-header {
    border-bottom: 1px solid #0000000f;
    margin-bottom: 12px;
    padding-bottom: 10px;
  }
}
.manage-language-header {
  margin-bottom: 12px;
  .button-group {
    display: flex;
    gap: 20px;
  }
}
</style>
