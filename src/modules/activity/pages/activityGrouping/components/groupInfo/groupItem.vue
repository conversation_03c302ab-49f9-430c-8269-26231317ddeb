<template>
  <div class="group-item">
    <div class="group-item-header">
      <div class="title-block flex-block gap-4">
        <span class="title">{{ title }}</span>
        <a-tag v-if="group.is_opt" color="blue">{{ $t(GROUPING_TEXT_ID_MAP['PROMOTION_GROUP']) }}</a-tag>
      </div>
      <div class="operation-block flex-block gap-8">
        <div class="hover-icon" @click="editGroup">
          <a-icon type="edit" />
          {{ $t(GROUPING_TEXT_ID_MAP['EDIT']) }}
        </div>
        <div class="drag-handle hover-icon">☰</div>
        <div @click="removeGroup">
          <a-icon type="delete" class="hover-icon" style="color: red" />
        </div>
      </div>
    </div>
    <a-divider />
    <div class="group-item-body">
      <div class="group-item-field">
        <div class="group-item-field-key">Group name</div>
        <div class="group-item-field-value">{{ group.group_name }}</div>
      </div>
      <div class="group-item-field">
        <div class="group-item-field-key">Group Desc</div>
        <div class="group-item-field-value">{{ group.group_desc || '/' }}</div>
      </div>
      <div class="group-item-field">
        <div class="group-item-field-key">{{ $t(GROUPING_TEXT_ID_MAP['TOTAL_NUM_OF_THIS_GRPUP']) }}</div>
        <div class="group-item-field-value">{{ group.pkg_rel_numbers || 0 }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { GROUPING_TEXT_ID_MAP } from '../../constant'
export default {
  name: 'GroupItem',
  components: {},
  props: {
    title: {
      type: String,
      default: ''
    },
    group: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      GROUPING_TEXT_ID_MAP
    }
  },
  methods: {
    editGroup() {
      const { group_id } = this.group || {}
      this.$emit('editGroup', group_id)
    },
    removeGroup() {
      const { group_id } = this.group || {}
      this.$emit('removeGroup', group_id)
    }
  }
}
</script>

<style lang="scss" scoped>
.gap-4 {
  gap: 4px;
}
.gap-8 {
  gap: 8px;
}
.flex-block {
  display: flex;
  align-items: center;
}
.group-item {
  display: flex;
  flex-direction: column;

  ::v-deep .ant-divider-horizontal {
    margin: 12px 0;
  }
}
.group-item-header {
  display: flex;
  justify-content: space-between;
  .title {
    font-size: 16px;
    font-weight: 700;
  }
  .hover-icon {
    cursor: pointer;
  }
}
.group-item-body {
  display: flex;
  flex-wrap: wrap;
  .group-item-field {
    width: 50%;
    display: flex;
    flex-direction: column;
    font-size: 14px;
    font-weight: 400;
    margin-bottom: 10px;
    &-key {
      color: gray;
    }
    &-value {
      color: black;
    }
  }
}
</style>
