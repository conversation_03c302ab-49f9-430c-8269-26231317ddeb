<template>
  <div>
    <div class="btn-area mb-10">
      <meegle-task-info :groupList="groupList" />
    </div>
    <div class="group-info">
      <div class="group-list y-scroll">
        <draggable v-model="dragList" :options="{ handle: '.drag-handle' }" @change="handleChangeOrder">
          <div v-for="(group, index) in dragList" :key="index" class="group-box">
            <group-item
              :group="group"
              :title="`Group ${index + 1}`"
              @editGroup="handleEditGroup(group)"
              @removeGroup="handleRemoveGroup(group)"
            />
          </div>
        </draggable>
        <div v-if="dragList.length < maxNum" @click="handleCreateGroup" class="group-box add-item-box">
          <a-icon type="plus"></a-icon>
          <h5>{{ $t(GROUPING_TEXT_ID_MAP['ADD_NEW_GROUP']) }}</h5>
        </div>
      </div>
      <a-drawer
        v-if="opDrawerVisisble"
        :title="`${currOpName} group`"
        :visible.sync="opDrawerVisisble"
        width="500"
        :header-style="{
          position: 'relative'
        }"
        @close="handleCloseOpDrawer"
      >
        <GroupForm :group_id="currEditGroupId" @submit="handleSubmitForm" @cancle="handleCloseOpDrawer" />
      </a-drawer>
    </div>
    <div class="btn-area">
      <a-button :disabled="orderBtnDisabled" @click="handleSaveOrder">{{
        $t(GROUPING_TEXT_ID_MAP['SAVE_ORDER'])
      }}</a-button>
    </div>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import GroupItem from './groupItem.vue'
import GroupForm from './groupForm.vue'
import { getGroupingList, createGrouping, updateGrouping, deleteGrouping, sortGrouping } from '../../apis'
import { isArrayOrderEqual } from '../../utils'
import { GROUPING_TEXT_ID_MAP } from '../../constant'
import MeegleTaskInfo from './meegleTaskInfo/index.vue'

export default {
  name: 'GroupInfo',
  components: {
    draggable,
    GroupItem,
    GroupForm,
    MeegleTaskInfo
  },
  inject: ['activityId'],
  data() {
    return {
      groupList: [],
      dragList: [],
      maxNum: 8,
      orderBtnDisabled: true,

      currEditGroupId: '',
      currRemoveGroupId: '',

      opDrawerVisisble: false,

      removeLoadingKey: 'remove',
      GROUPING_TEXT_ID_MAP
    }
  },
  watch: {
    groupList: {
      immediate: true,
      handler(v) {
        this.dragList = v
      }
    }
  },
  computed: {
    currOpName() {
      const { currEditGroupId } = this
      return currEditGroupId ? 'edit' : 'create'
    }
  },
  created() {
    this.getGroupingList()
  },
  methods: {
    async getGroupingList() {
      const { items: groupList } = await getGroupingList(this.activityId)
      this.groupList = groupList || []
    },
    handleRefreshGroupList() {
      this.getGroupingList()
      this.orderBtnDisabled = true
    },
    handleEditGroup(group) {
      const { group_id } = group
      this.currEditGroupId = group_id
      this.opDrawerVisisble = true
    },
    async handleRemoveGroup(group) {
      const { group_id, pkg_rel_numbers = 0 } = group
      if (pkg_rel_numbers > 0) {
        this.$modal.confirm({
          title: this.$t(this.GROUPING_TEXT_ID_MAP['DELETE_GROUP_ABANDOM_WARNING']),
          onOk: () => {
            this.$emit('changeTabs', 'packageSetting')
          },
          onCancel() {
            console.log('Cancel')
          }
        })
      } else {
        this.$modal.confirm({
          title: this.$t(this.GROUPING_TEXT_ID_MAP['DELETE_GROUP_WARNING']),
          onOk: async () => {
            const removeRes = await deleteGrouping(this.activityId, [group_id])
            if (removeRes) {
              this.$message.success({ content: 'Success!', duration: 2 })
              this.handleRefreshGroupList()
            }
          },
          onCancel() {
            console.log('Cancel')
          }
        })
      }
    },
    handleCreateGroup() {
      this.opDrawerVisisble = true
    },
    handleCloseOpDrawer() {
      this.opDrawerVisisble = false
      this.currEditGroupId = ''
    },
    handleFindCurrOptGroup(currGroupId = '') {
      const restGroup = this.groupList?.filter((ele) => +ele?.group_id !== +currGroupId) || []
      return restGroup?.find((item) => item?.is_opt) || {}
    },
    handleChangeOrder() {
      const { groupList = [], dragList = [] } = this
      const notChanged = isArrayOrderEqual(
        groupList?.map((ele) => ele?.group_id) || [],
        dragList?.map((ele) => ele?.group_id) || []
      )
      this.orderBtnDisabled = notChanged
    },
    async handleOpGroup(type = 'create', params) {
      const fnHandle = {
        create: async function (params) {
          return await createGrouping(params)
        },
        edit: async function (params) {
          return await updateGrouping(params)
        }
      }
      const fn = fnHandle[type]
      return await fn(params)
    },
    async handleSubmitForm(group_info) {
      const { group_id = '', is_opt } = group_info
      const type = group_id ? 'edit' : 'create'
      const currOptGroup = this.handleFindCurrOptGroup(group_id)

      // 判断是否因为是运营组所以要进行运营组替换
      if (is_opt && currOptGroup?.group_id) {
        this.$modal.confirm({
          title: this.$t(this.GROUPING_TEXT_ID_MAP['UNIQUE_PROMO_GROUP_WARNING']),
          onOk: async () => {
            const opCurrGroupRes = await this.handleOpGroup(type, {
              activity_id: this.activityId,
              group_info
            })
            if (opCurrGroupRes) {
              this.$message.success({ content: 'Success!', duration: 2 })
              this.handleCloseOpDrawer()
              this.handleRefreshGroupList()
            } else {
              this.$message.error({
                content: opCurrGroupRes?.error?.message
              })
            }
          },
          onCancel() {
            console.log('Cancel')
          }
        })
      } else {
        // 正常创建/修改
        const opRes = await this.handleOpGroup(type, { activity_id: this.activityId, group_info })
        if (opRes) {
          this.$message.success({ content: 'Success!', duration: 2 })
          this.handleCloseOpDrawer()
          this.handleRefreshGroupList()
        } else {
          this.$message.error({ content: opRes?.error?.message || '', duration: 2 })
        }
      }
    },
    async handleSaveOrder() {
      const { dragList } = this
      const res = await sortGrouping(
        this.activityId,
        dragList?.map((ele) => ele?.group_id)
      )
      if (res) {
        this.$message.success({ content: 'Success!', duration: 2 })
      } else {
        this.$message.error({ content: res?.error?.message || '', duration: 2 })
      }
      this.handleRefreshGroupList()
    }
  }
}
</script>

<style lang="scss" scoped>
.group-box {
  margin-bottom: 10px;
  padding: 12px 20px;
  border: 1px solid #ccc;
  border-radius: 5px;
  background: #ffffff;
}

.y-scroll {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.add-item-box {
  background-color: #f9f9f9;
  text-align: center;
  cursor: pointer;
  border: 1px solid #d9d9d9;
  border-style: dashed;
}

.group-info-box {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #0000000f;
  margin-bottom: 12px;

  // cursor: grab;
  .group-info-box-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.btn-area {
  display: flex;
  justify-content: flex-end;
}

.mb-10 {
  margin-bottom: 20px;
}
</style>
