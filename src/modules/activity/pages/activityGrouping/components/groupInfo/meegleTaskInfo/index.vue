<template>
  <div class="meegle-info-area">
    <template v-if="isLoading">
      <a-icon type="loading" />
    </template>
    <template v-else>
      <div class="btn-area">
        <div @click="handleRreshMeegleTaskInfo">
          <svg-icon class="refresh-icon" icon-name="refresh" />
        </div>
        <SendToTranslateBtn
          :disabled="!canSendToTranslate || meegleTaskInfo.group_cms_btn_disabled"
          @onConfirm="handleSendToTranslate"
        />
      </div>
      <div class="text-area">
        <a v-if="meegleTaskInfo.grouping_cms_link" :href="meegleTaskInfo.grouping_cms_link" target="_blank">
          CMS Link
        </a>
        <a v-if="meegleTaskInfo.meegle_link" :href="meegleTaskInfo.meegle_link" target="_blank">
          Meegle Link
        </a>
        <span v-if="showRefreshDesc" style="color: gray">
          Meegle link is not ready, Please refresh and try again later
        </span>
      </div>
    </template>
  </div>
</template>

<script>
import { getMeegleTranslateTaskInfo, sentToTranslate } from '../../../apis'
import { GROUPING_TEXT_ID_MAP } from '../../../constant'
import SendToTranslateBtn from './sendToTranslateBtn.vue'

export default {
  name: 'MeegleTaskInfo',
  components: {
    SendToTranslateBtn
  },
  inject: ['activityId'],
  props: {
    groupList: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    groupList: {
      immediate: true,
      handler() {
        this.getMeegleTranslateTaskInfo()
      }
    }
  },
  data() {
    return {
      meegleTaskInfo: {
        group_cms_btn_disabled: false,
        grouping_cms_link: '',
        meegle_link: ''
      },
      isLoading: false,
      GROUPING_TEXT_ID_MAP,
      meegleId: '',
      confirmVisible: false
    }
  },
  created() {
    this.getMeegleTranslateTaskInfo()
  },
  computed: {
    canSendToTranslate() {
      const { groupList = [] } = this
      return groupList?.filter((ele) => !!ele?.group_name)?.length > 0
    },
    showRefreshDesc() {
      const { group_cms_btn_disabled, grouping_cms_link, meegle_link } = this.meegleTaskInfo || {}
      return !!group_cms_btn_disabled && !grouping_cms_link && !meegle_link
    }
  },
  methods: {
    async getMeegleTranslateTaskInfo() {
      this.isLoading = true
      const { activityId, meegleId } = this
      const meegleTaskInfo = await getMeegleTranslateTaskInfo({
        activity_id: activityId,
        meegle_id: meegleId
      })
      this.meegleTaskInfo = {
        ...meegleTaskInfo
      }
      this.isLoading = false
    },
    handleRreshMeegleTaskInfo() {
      this.getMeegleTranslateTaskInfo()
    },
    showConfirmModal() {
      this.confirmVisible = true
    },
    async handleSendToTranslate(taskDescription) {
      const { activityId, groupList } = this
      const res = await sentToTranslate({
        activity_id: activityId,
        group_ids: groupList?.filter((ele) => ele?.group_id)?.map((ele) => ele?.group_id),
        task_description: taskDescription
      })
      if (res) {
        const { meegle_id } = res || {}
        this.meegleId = meegle_id
        this.$message.success({ content: 'Success!', duration: 2 })
        this.handleRreshMeegleTaskInfo()
        this.confirmVisible = false
      } else {
        this.$message.error({ content: res?.error?.message || '', duration: 2 })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.btn-area {
  display: flex;
  gap: 8px;
  align-items: center;
}

.refresh-icon {
  color: #1890ff;
  cursor: pointer;
}

.text-area {
  display: flex;
  flex-direction: column;
  text-align: end;
  gap: 4px;
}

.meegle-info-area {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
