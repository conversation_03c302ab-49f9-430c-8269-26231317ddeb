<template>
  <div>
    <a-modal v-model="visible" title="Send to Translate" @ok="submit" @cancel="handleCancel">
      <p style="color: black">
        Can you confirm that the configuration of the group info has been completed? Once the translation task
        is submitted, it cannot be withdrawn or resubmitted until the translation is finished!
      </p>

      <a-form-model-item label="Task Description" required>
        <a-input
          v-model="taskDescription"
          placeholder="Please tell the translation colleagues what you have modified or added."
          :auto-focus="true"
        />
      </a-form-model-item>
    </a-modal>
    <a-button type="primary" :disabled="disabled" @click="showConfirmModal">
      {{ $t(GROUPING_TEXT_ID_MAP['SEND_TO_TRANSLATE']) }}
    </a-button>
  </div>
</template>

<script>
import { GROUPING_TEXT_ID_MAP } from '../../../constant'
export default {
  name: 'SendToTranslateBtn',
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      taskDescription: '',
      visible: false,
      GROUPING_TEXT_ID_MAP
    }
  },
  methods: {
    showConfirmModal() {
      this.visible = true
    },
    closeConfirmModal() {
      this.visible = false
    },
    submit() {
      if (!this.taskDescription) {
        this.$message.error('Task Description is required')
        return
      }
      this.$emit('onConfirm', this.taskDescription)
      this.closeConfirmModal()
    },
    handleCancel() {
      this.closeConfirmModal()
    }
  }
}
</script>
