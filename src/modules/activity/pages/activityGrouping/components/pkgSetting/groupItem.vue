<template>
  <div class="group-item" :class="{ active: isActive }">
    <div class="group-item-header">
      <div class="group-item-header-index">{{ title }}</div>
      <div v-if="defaultGroupTips" style="display: inline">
        <a-tooltip placement="top">
          <template #title>
            <span>{{ defaultGroupTips }}</span>
          </template>
          <a-icon type="question-circle"></a-icon>
        </a-tooltip>
      </div>
      <a-tag v-if="isOpt" color="blue">{{ $t(GROUPING_TEXT_ID_MAP['PROMOTION_GROUP']) }}</a-tag>
    </div>
    <div class="group-item-body">
      <div class="group-item-body-name" v-if="groupName">{{ groupName }}</div>
      <div class="group-item-body-numbers">Package Numbers: {{ pkgRelNumbers }}</div>
    </div>
  </div>
</template>

<script>
import { GROUPING_TEXT_ID_MAP } from '../../constant'
export default {
  name: 'GroupItem',
  components: {},
  props: {
    title: {
      type: String,
      default: ''
    },
    groupId: {
      type: [String, Number],
      default: ''
    },
    groupName: {
      type: String,
      default: ''
    },
    isOpt: {
      type: Boolean,
      default: false
    },
    isActive: {
      type: Boolean,
      default: false
    },
    pkgRelNumbers: {
      type: Number,
      default: 0
    },
    defaultGroupTips: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      GROUPING_TEXT_ID_MAP
    }
  }
}
</script>

<style lang="scss" scoped>
.group-item {
  padding: 10px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  cursor: pointer;

  &.active {
    border-color: #e8e8e8;
    border: 2px solid #0091ff;
    box-shadow: none;
    color: #0091ff;
  }
  .group-item-header {
    display: flex;
    justify-content: space-between;

    &-index {
      font-weight: 500;
      font-size: 16px;
    }
  }
  .group-item-body {
    margin-top: 10px;

    &-desc {
      color: rgba(0, 0, 0, 0.45);
    }
  }
}
</style>
