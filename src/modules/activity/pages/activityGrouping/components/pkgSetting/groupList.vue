<template>
  <div>
    <div v-for="(item, index) in allGroupList" :key="index" @click="handleSelectGroup(item)">
      <group-item
        class="bg-white"
        :title="
          `${!calcIsCurrGroupDefaultGroup(item.group_id) ? `Group ${index + 1}` : defaultGroup.group_name}`
        "
        :default-group-tips="calcIsCurrGroupDefaultGroup(item.group_id) ? defaultGroup.group_desc : ''"
        :group-id="item.group_id"
        :group-name="`${!calcIsCurrGroupDefaultGroup(item.group_id) ? item.group_name : ''}`"
        :pkg-rel-numbers="
          !calcIsCurrGroupDefaultGroup(item.group_id)
            ? groupPkgMap[item.group_id] && groupPkgMap[item.group_id].length
            : unMatchedPkg.length
        "
        :is-opt="item.is_opt"
        :is-active="currGroupId === item.group_id"
      />
    </div>
  </div>
</template>

<script>
import GroupItem from './groupItem.vue'
export default {
  name: 'GroupList',
  components: {
    GroupItem
  },
  props: {
    allGroupList: {
      type: Array,
      default: () => [{}]
    },
    groupPkgMap: {
      type: Object,
      default: () => {}
    },
    unMatchedPkg: {
      type: Array,
      default: () => [{}]
    },
    currGroupId: {
      type: Number,
      require: true
    },
    defaultGroup: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    calcIsCurrGroupDefaultGroup(currGroupId) {
      return +currGroupId === +this.defaultGroup?.group_id
    },
    handleSelectGroup(group) {
      const { group_id } = group
      this.$emit('changeCurrGroupId', group_id)
    }
  }
}
</script>

<style lang="scss" scoped>
.bg-white {
  background: #ffffff;
}
.pkg-setting-grouplist {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
</style>
