<template>
  <div class="group-pkg-matched">
    <div class="group-info">
      <div class="group-name">{{ groupName }}</div>
      <div class="group-desc" v-if="groupDesc">{{ groupDesc }}</div>
    </div>
    <div class="ref-pkg-list">
      <div class="pkg-item" v-for="pkg in pkgList" :key="pkg.package_id">
        <div class="pkg-name">{{ pkg.package_id }} - {{ pkg.package_name }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GroupPkgItem',
  components: {},
  props: {
    groupName: {
      type: String,
      default: ''
    },
    groupDesc: {
      type: String,
      default: ''
    },
    pkgList: {
      type: Array,
      default: () => [{}]
    }
  }
}
</script>

<style lang="scss" scoped>
.group-pkg-matched {
  padding: 20px 8px;
  background: rgba(245, 245, 245, 1);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.group-info {
  display: flex;
  flex-direction: column;
  .group-name {
    font-size: 16px;
    font-weight: 600;
  }
  .group-desc {
    font-size: 14px;
    font-weight: 400;
    color: rgba(33, 33, 33, 1);
  }
}
.ref-pkg-list {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .pkg-item {
    background: rgba(255, 255, 255, 1);
    padding: 12px;
    border-radius: 12px;
  }
}
</style>
