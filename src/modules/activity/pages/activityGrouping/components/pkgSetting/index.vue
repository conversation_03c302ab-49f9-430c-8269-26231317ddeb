<template>
  <div class="pkg-setting-area">
    <div class="pkg-setting-status">
      <a-tag v-if="grouping_publish_status" color="blue" size="large">{{
        GROUPING_PUBLISHED_STATUS_LABLE_MAP[grouping_publish_status]
      }}</a-tag>
    </div>

    <div class="pkg-setting">
      <group-list
        class="pkg-setting-grouplist flex-1 y-scroll"
        :all-group-list="allGroupList"
        :curr-group-id="currGroupId"
        :group-pkg-map="groupPkgMap"
        :un-matched-pkg="unMatchedPkg"
        :default-group="defaultGroup"
        @changeCurrGroupId="(groupId) => (currGroupId = groupId)"
      />
      <package-search
        v-model="calcCurrGroupMatchedPkg"
        class="pkg-setting-pkglist flex-1 bg-white border-radius-8 y-scroll"
        :curr-group-id="currGroupId"
        :group-pkg-map="groupPkgMap"
        :package-list="packageList"
      />
      <pkg-setting-preview
        v-model="calcCurrGroupMatchedPkg"
        class="pkg-setting-preview flex-1 bg-white border-radius-8 y-scroll"
        :is-defatul-group-in-operation="isDefatulGroupInOperation"
        :curr-group-name="calcGroupInfo(currGroupId).group_name"
        :group-pkg-map="groupPkgMap"
        @clearMapping="handleClearMapping"
        @removeMatchedPkg="handleRemoveMatchedPkg"
      />
    </div>
    <div class="operate-block">
      <preview-modal :disabled="!isCurrGroupingCanSave" :group-pkg-list="previewMappingList" />
      <a-button
        v-if="grouping_publish_status !== GROUPING_PUBLISHED_STATUS['PUBLISHED']"
        @click="handleUpdateGroupingRels"
      >
        {{ $t(GROUPING_TEXT_ID_MAP['SAVE_AS_DRAFT']) }}
      </a-button>
      <a-button type="primary" :disabled="!isCurrGroupingCanSave" @click="handleSubmit">
        {{ $t(GROUPING_TEXT_ID_MAP['PUBLISH']) }}
      </a-button>
    </div>
  </div>
</template>

<script>
import _ from 'lodash'
import GroupList from './groupList.vue'
import PackageSearch from './packageSearch.vue'
import PkgSettingPreview from './pkgSettingPreview.vue'
import PreviewModal from './previewModal.vue'
import { getGroupingRels, updateGroupingRels, updateGroupingStatus } from '../../apis'
import {
  GROUPING_PUBLISHED_STATUS,
  GROUPING_PUBLISHED_STATUS_LABLE_MAP,
  PKG_PUBLISHED_STATUS,
  DEFAULT_GROUP_ID,
  GROUPING_TEXT_ID_MAP
} from '../../constant'
import { calcIsPkgMatchingEqual } from '../../utils'
import { pmsConfirm } from '@activity/utils'

export default {
  name: 'PkgSetting',
  components: {
    GroupList,
    PackageSearch,
    PkgSettingPreview,
    PreviewModal
  },
  inject: ['activityId'],
  data() {
    return {
      groupList: [],
      packageList: [],
      currGroupId: 0,
      groupPkgMap: {}, // groupId与套餐信息的map的obj，不包含others组的
      oldGroupingPkgRelList: [], // 旧数据
      hasUnSavedSetting: false,
      unMatchedPkg: [], // others组内套餐
      grouping_publish_status: GROUPING_PUBLISHED_STATUS['UNDEFINED'],
      defaultGroupId: DEFAULT_GROUP_ID,
      defaultGroup: {},
      GROUPING_PUBLISHED_STATUS,
      GROUPING_PUBLISHED_STATUS_LABLE_MAP,
      PKG_PUBLISHED_STATUS,
      GROUPING_TEXT_ID_MAP
    }
  },
  computed: {
    allGroupList() {
      const { groupList: _groupList = [], defaultGroup } = this
      return [..._groupList, defaultGroup]
    },
    isDefatulGroupInOperation() {
      return this.currGroupId === this.defaultGroup.group_id
    },
    calcCurrGroupMatchedPkg: {
      get() {
        const { isDefatulGroupInOperation = false, groupPkgMap = {}, currGroupId, unMatchedPkg = [] } = this
        return !isDefatulGroupInOperation ? groupPkgMap[currGroupId] || [] : unMatchedPkg
      },
      set(value) {
        if (!this.isDefatulGroupInOperation) {
          this.groupPkgMap[this.currGroupId] = value
        } else {
          this.unMatchedPkg = value
        }
      }
    },
    // 包含完整数据的mappingList
    groupPkgMappingList() {
      const { groupPkgMap = {}, groupList = [], defaultGroup, unMatchedPkg = [] } = this
      const groupPkgList = groupList?.map((group) => {
        /* eslint-disable-next-line no-unused-vars */
        const { group_id, packages, ...groupInfo } = group
        return {
          group_id,
          ...groupInfo,
          pkg_rel: groupPkgMap[+group_id] || []
        }
      })
      groupPkgList.push({
        ...defaultGroup,
        pkg_rel: unMatchedPkg
      })
      return groupPkgList?.filter((ele) => ele?.pkg_rel?.length)
    },
    // 过滤掉未发布套餐的mappingList
    previewMappingList() {
      return this.groupPkgMappingList
        ?.map((ele) => {
          const { pkg_rel } = ele
          return {
            ...ele,
            pkg_rel: pkg_rel?.filter((item) => this.calcIsPublished(item))
          }
        })
        ?.filter((ele) => ele?.pkg_rel?.length)
    },
    // groupid与pkgIds的list
    groupingPkgRelList() {
      return this.groupPkgMappingList?.map((ele) => {
        const { group_id, pkg_rel = [] } = ele
        return {
          group_id,
          package_ids: pkg_rel?.map((item) => +item?.package_id)
        }
      })
    },
    // 保存条件：至少有一个分组内下有已经发布了的套餐
    isCurrGroupingCanSave() {
      const groupPkgMappingList = this.groupPkgMappingList
      return groupPkgMappingList
        ?.filter((ele) => +ele?.group_id !== +this.defaultGroupId)
        ?.some((item) => {
          const { pkg_rel = [] } = item
          return pkg_rel?.filter((pkg) => this.calcIsPublished(pkg))?.length >= 1
        })
    }
  },
  watch: {
    groupList: {
      handler(_groupList) {
        const currGroupId = _groupList?.[0]?.group_id || this.defaultGroupId
        this.currGroupId = currGroupId
      }
    },
    groupPkgMap: {
      immediate: true,
      deep: true,
      handler(currentMatchedMap) {
        const matchedPkgIds = Object.keys(currentMatchedMap)
          .reduce((pkgList, group) => {
            const currGroupMatchedPkgs = currentMatchedMap[group] || []
            pkgList = [...pkgList, ...currGroupMatchedPkgs]
            return pkgList
          }, [])
          ?.map((ele) => ele?.package_id)
        const unMatchedPkg = this.packageList?.filter((ele) => !matchedPkgIds.includes(ele?.package_id))
        this.unMatchedPkg = unMatchedPkg
      }
    },
    groupingPkgRelList: {
      immediate: true,
      deep: true,
      handler(_groupingPkgRelList) {
        const { oldGroupingPkgRelList } = this
        this.hasUnSavedSetting = !calcIsPkgMatchingEqual(_groupingPkgRelList, oldGroupingPkgRelList)
      }
    }
  },
  created() {
    this.getGroupingRels()
  },
  methods: {
    async getGroupingRels() {
      const { defaultGroupId } = this
      const { group_items: grouping_pkg_rel = [], status: grouping_publish_status = -1 } =
        await getGroupingRels(this.activityId)
      /* eslint-disable-next-line no-unused-vars */
      const { packages, ..._defaultGroup } = grouping_pkg_rel?.find((ele) => ele?.group_id === defaultGroupId)

      this.grouping_publish_status = grouping_publish_status
      this.defaultGroup = { ..._defaultGroup }
      this.groupList = grouping_pkg_rel?.filter((item) => +item?.group_id !== +defaultGroupId)
      this.packageList = grouping_pkg_rel?.reduce((list, { packages }) => {
        list = [...list, ...(packages || [])]
        return list
      }, [])

      this.groupPkgMap = grouping_pkg_rel?.reduce((currMap, group) => {
        const { group_id = defaultGroupId, packages = [] } = group || {}
        if (group_id && group_id !== defaultGroupId) {
          currMap[group_id] = packages || []
        }
        return currMap
      }, {})

      this.oldGroupingPkgRelList = grouping_pkg_rel
        ?.map((group) => {
          const { group_id = defaultGroupId, packages = [] } = group
          return {
            group_id,
            package_ids: packages?.map((item) => item?.package_id)
          }
        })
        ?.filter((ele) => !!ele?.package_ids?.length)
    },
    handleRefreshGroupingSource() {
      this.getGroupingRels()
    },
    handleClearMapping() {
      this.groupPkgMap[this.currGroupId] = []
    },
    handleRemoveMatchedPkg(pkg) {
      const { package_id: pkgId } = pkg
      const currMatchedPkg = this.groupPkgMap?.[this.currGroupId] || []
      this.groupPkgMap[this.currGroupId] = currMatchedPkg?.filter((ele) => ele.package_id !== pkgId)
    },
    async handleUpdateGroupingRels() {
      const grouping_pkg_rel = this.groupingPkgRelList
      try {
        await updateGroupingRels(this.activityId, grouping_pkg_rel)
        this.handleRefreshGroupingSource()
        this.$message.success({ content: 'Success' })
      } catch (e) {
        this.$message.error({ content: e || '' })
      }
    },
    handleSubmit: _.throttle(function () {
      this.handleUpdateAndPublishedGroupingRels()
    }, 300),
    async handleUpdateAndPublishedGroupingRels() {
      const {
        grouping_publish_status,
        groupingPkgRelList: grouping_pkg_rel,
        GROUPING_TEXT_ID_MAP,
        GROUPING_PUBLISHED_STATUS
      } = this
      let confirm = true
      if (
        [GROUPING_PUBLISHED_STATUS['DRAFT'], GROUPING_PUBLISHED_STATUS['UNDEFINED']].includes(
          grouping_publish_status
        )
      ) {
        confirm = await pmsConfirm.call(this, {
          content: this.$t(GROUPING_TEXT_ID_MAP['PUBLISH_WARNING_TEXT'])
        })
      }

      if (!confirm) {
        return
      }
      try {
        await updateGroupingRels(this.activityId, grouping_pkg_rel)
        await updateGroupingStatus(this.activityId, GROUPING_PUBLISHED_STATUS['PUBLISHED'])
        this.$message.success({ content: 'Success' })
        this.handleRefreshGroupingSource()
      } catch (e) {
        this.$message.error({ content: e || '' })
      }
    },
    calcIsPublished(pkg) {
      const { PKG_PUBLISHED_STATUS } = this
      return [
        +PKG_PUBLISHED_STATUS['PUBLISHED'],
        +PKG_PUBLISHED_STATUS['SUSPEND'],
        +PKG_PUBLISHED_STATUS['ONSALESOON']
      ].includes(+pkg?.publish_status)
    },
    calcGroupInfo(groupId) {
      return this.groupList?.find((ele) => ele?.group_id === groupId) || this.defaultGroup || {}
    }
  }
}
</script>

<style lang="scss" scoped>
.flex-1 {
  flex: 1;
}
.bg-white {
  background: #ffffff;
}
.border-radius-8 {
  border-radius: 8px;
}
.pkg-setting-area {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.pkg-setting {
  display: flex;
  gap: 20px;
}
.y-scroll {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}
.operate-block {
  justify-content: flex-end;
  display: flex;
  gap: 4px;
  position: fixed;
  bottom: 20px;
  right: 20px;
}
</style>
