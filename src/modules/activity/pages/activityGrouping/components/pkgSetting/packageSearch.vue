<template>
  <div class="pkg-setting-pkglist">
    <div class="pkg-setting-pkglist-header divider">
      <div class="pkg-setting-pkglist-header_pkgname">
        <div class="title">{{ $t(GROUPING_TEXT_ID_MAP['PACKAGE_LIST']) }}</div>
        <a-input-search
          v-model="pkgSearchValue"
          :placeholder="$t(GROUPING_TEXT_ID_MAP['SEARCH_PACKAGES'])"
          style="width: 200px"
        />
      </div>
      <div>
        <span>Publisht Stauts: </span>
        <a-select
          v-model="pkgFilterStauts"
          :options="statusOpions"
          placeholder="please select publish status"
          mode="multiple"
          style="width: 250px"
        ></a-select>
      </div>
    </div>
    <template v-if="filteredPackageList.length">
      <a-checkbox-group v-model="pkgOptionList">
        <a-checkbox
          v-for="pkg in sortedPkgList"
          v-show="calcCurrPkgShow(pkg.package_id)"
          :key="pkg.package_id"
          :value="pkg"
          :disabled="calcCurrPkgSelectable(pkg.package_id)"
        >
          <pkg-item :pkg="pkg" />
        </a-checkbox>
      </a-checkbox-group>
    </template>
    <template v-else>
      <a-empty :description="$t(GROUPING_TEXT_ID_MAP['NO_PACKAGES_FOUNDED'])" />
    </template>
  </div>
</template>

<script>
import PkgItem from './pkgItem.vue'
import { DEFAULT_GROUP_ID, GROUPING_TEXT_ID_MAP, PKG_PUBLISHED_STATUS_MAP } from '../../constant'
export default {
  name: 'PackageSearch',
  components: {
    PkgItem
  },
  props: {
    value: {
      type: Array,
      default: () => [{}]
    },
    packageList: {
      type: Array,
      default: () => [{}]
    },
    groupPkgMap: {
      type: Object,
      default: () => {}
    },
    currGroupId: {
      type: Number,
      require: true
    }
  },
  data() {
    return {
      pkgSearchValue: '',
      defaultGroupId: DEFAULT_GROUP_ID,
      GROUPING_TEXT_ID_MAP,
      PKG_PUBLISHED_STATUS_MAP,
      pkgFilterStauts: []
    }
  },
  computed: {
    statusOpions() {
      const { PKG_PUBLISHED_STATUS_MAP } = this
      return Object.keys(PKG_PUBLISHED_STATUS_MAP)?.map((key) => ({
        value: +key,
        label: PKG_PUBLISHED_STATUS_MAP[key]
      }))
    },
    sortedPkgList() {
      const { packageList = [] } = this
      return [...packageList]?.sort((a, b) => a.default_order - b.default_order) || []
    },
    filteredPackageList() {
      const {
        pkgSearchValue = '',
        pkgFilterStauts: _pkgFilterStauts = [],
        PKG_PUBLISHED_STATUS_MAP,
        packageList: _packageList = [],
        currGroupId,
        groupPkgMap = {}
      } = this
      const allPkgStatus = Object.keys(PKG_PUBLISHED_STATUS_MAP)?.map((key) => +key)
      const packageList = _packageList?.sort((a, b) => a.default_order - b.default_order)
      const pkgFilterStauts = !_pkgFilterStauts?.length ? allPkgStatus : _pkgFilterStauts
      const matchedPkgIds = Object.keys(groupPkgMap)
        .reduce((pkgList, group) => {
          if (+group !== +currGroupId) {
            const currGroupMatchedPkgs = groupPkgMap[group] || []
            pkgList = [...pkgList, ...currGroupMatchedPkgs]
          }
          return pkgList
        }, [])
        ?.map((ele) => ele?.package_id)
      const filterBySearchValue = !pkgSearchValue
        ? packageList
        : packageList?.filter(
            (ele) =>
              ele?.package_name.includes(pkgSearchValue) || `${ele?.package_id}`.includes(pkgSearchValue)
          )
      return filterBySearchValue
        ?.filter((ele) => !matchedPkgIds?.includes(ele?.package_id))
        ?.filter((ele) => pkgFilterStauts.includes(+ele?.publish_status))
    },
    pkgOptionList: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    calcCurrPkgSelectable(pkgId) {
      const { groupPkgMap, currGroupId, defaultGroupId } = this
      if (+currGroupId === +defaultGroupId) {
        return true
      }
      const matchedPkg = Object.keys(groupPkgMap).reduce((pkgList, group) => {
        if (+group !== +currGroupId) {
          const currGroupMatchedPkgs = groupPkgMap[group] || []
          pkgList = [...pkgList, ...currGroupMatchedPkgs]
        }
        return pkgList
      }, [])
      return matchedPkg?.map((ele) => ele?.package_id)?.includes(pkgId)
    },
    calcCurrPkgShow(pkgId) {
      const { filteredPackageList } = this
      return filteredPackageList?.map((ele) => ele?.package_id).includes(pkgId)
    }
  }
}
</script>

<style lang="scss" scoped>
.divider {
  border-bottom: 1px solid #d9d9d9;
  padding-bottom: 10px;
  margin-bottom: 10px;
}
.pkg-setting-pkglist {
  padding: 10px;
  ::v-deep .ant-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  &-header {
    display: flex;
    flex-direction: column;
    gap: 8px;

    &_pkgname {
      display: flex;
      justify-content: space-between;
    }

    .title {
      font-weight: 600;
    }
  }

  ::v-deep .ant-checkbox-wrapper {
    display: flex;
    margin-left: 0px;
  }

  ::v-deep .ant-checkbox {
    margin-top: 4px;
  }
}
</style>
