<template>
  <div class="pkg-item-block">
    <div class="pkg-item">
      <div class="pkg-name">
        {{ `${pkg.package_id}-${pkg.package_name}` }}
      </div>
      <div
        class="pkg-status"
        :style="{ color: PKG_PUBLISHED_STATUS_COLOR_MAP[pkg.publish_status] || 'gray' }"
      >
        {{ PKG_PUBLISHED_STATUS_MAP[pkg.publish_status] || '' }}
      </div>
    </div>
    <slot name="operate-block" />
  </div>
</template>

<script>
import { PKG_PUBLISHED_STATUS_MAP, PKG_PUBLISHED_STATUS_COLOR_MAP } from '../../constant'
export default {
  name: 'PkgItem',
  components: {},
  props: {
    pkg: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      PKG_PUBLISHED_STATUS_MAP,
      PKG_PUBLISHED_STATUS_COLOR_MAP
    }
  }
}
</script>

<style lang="scss" scoped>
.pkg-item-block {
  display: flex;
  justify-content: space-between;
}
.pkg-status {
  font-weight: 300;
}
</style>
