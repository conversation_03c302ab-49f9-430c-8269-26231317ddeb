<template>
  <div class="pkg-setting-preview">
    <div class="pkg-setting-preview-header divider">
      <div class="group-name">{{ currGroupName }}</div>
      <div class="group-number-block">
        <div class="group-number">
          {{ `${pkgOptionList.length} ${$t(GROUPING_TEXT_ID_MAP['PACKAGES_ADDED_BY_THIS_GROUP'])}` }}
        </div>
        <a-button :disabled="isDisabled" @click="handleClearMapping">Clear</a-button>
      </div>
    </div>
    <div class="pkg-setting-preview-body">
      <template v-if="pkgOptionList.length">
        <draggable
          v-model="pkgOptionList"
          :disabled="isDisabled"
          :options="{ handle: '.drag-handle' }"
          class="pkg-setting-preview-list"
          :style="{
            cursor: !isDisabled ? 'pointer' : 'not-allowed'
          }"
        >
          <div v-for="(pkg, index) in pkgOptionList" :key="index" class="preview-pkg-item">
            <pkg-item :pkg="pkg">
              <template slot="operate-block">
                <div class="pkg-item-operate-block">
                  <div class="drag-handle">☰</div>
                  <div @click="handleRemoveMatchedPkg(pkg)">
                    <a-icon
                      type="delete"
                      :style="{
                        color: 'red',
                        cursor: !isDisabled ? 'pointer' : 'not-allowed'
                      }"
                    />
                  </div>
                </div>
              </template>
            </pkg-item>
          </div>
        </draggable>
      </template>
      <a-empty v-else :description="$t(GROUPING_TEXT_ID_MAP['EMPTY_GROUP_TIPS'])" />
    </div>
  </div>
</template>

<script>
import PkgItem from './pkgItem.vue'
import { GROUPING_TEXT_ID_MAP } from '../../constant'
import draggable from 'vuedraggable'
export default {
  name: 'PkgSettingPreview',
  components: {
    PkgItem,
    draggable
  },
  props: {
    value: {
      type: Array,
      default: () => [{}]
    },
    currGroupName: {
      type: String,
      require: ''
    },
    isDefatulGroupInOperation: {
      type: Boolean,
      default: false
    },
    defaultGroup: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      pkgSearchValue: '',
      GROUPING_TEXT_ID_MAP
    }
  },
  computed: {
    pkgOptionList: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    isDisabled() {
      return this.isDefatulGroupInOperation
    }
  },
  methods: {
    handleClearMapping() {
      this.$emit('clearMapping')
    },
    handleRemoveMatchedPkg(pkg) {
      this.$emit('removeMatchedPkg', pkg)
    }
  }
}
</script>

<style lang="scss" scoped>
.divider {
  border-bottom: 1px solid #d9d9d9;
  padding-bottom: 10px;
  margin-bottom: 10px;
}
.pkg-setting-preview {
  padding: 10px 12px;
  .pkg-setting-preview-header {
    .group-name {
      font-size: 14px;
      font-weight: 700;
      color: #000000d9;
    }
    .group-number-block {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .group-number {
      font-size: 14px;
      font-weight: 400;
    }
  }
  .pkg-setting-preview-body {
    .preview-pkg-item {
      width: 100%;
    }
    .pkg-item-operate-block {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }
  .pkg-setting-preview-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
}
</style>
