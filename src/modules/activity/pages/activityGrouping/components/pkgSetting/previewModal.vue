<template>
  <div>
    <a-drawer
      v-if="visible"
      title="Grouping Preview"
      :visible.sync="visible"
      width="500"
      :header-style="{
        position: 'relative'
      }"
      @close="handleClose"
    >
      <div class="group-pkg-list">
        <group-pkg-item
          v-for="(groupPkg, index) in groupPkgList"
          :group-name="groupPkg.group_name"
          :group-desc="groupPkg.group_id !== DEFAULT_GROUP_ID ? groupPkg.group_desc : ''"
          :pkg-list="groupPkg.pkg_rel"
          :key="index"
        />
      </div>
    </a-drawer>
    <a-button :disabled="disabled" @click="handlePreview">{{ $t(GROUPING_TEXT_ID_MAP['PREVIEW']) }}</a-button>
  </div>
</template>

<script>
import GroupPkgItem from './groupPkgItem.vue'
import { GROUPING_TEXT_ID_MAP, DEFAULT_GROUP_ID } from '../../constant'
export default {
  name: 'PreviewModal',
  components: {
    GroupPkgItem
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    groupPkgList: {
      type: Array,
      default: () => [{}]
    }
  },
  data() {
    return {
      visible: false,
      GROUPING_TEXT_ID_MAP,
      DEFAULT_GROUP_ID
    }
  },
  methods: {
    handlePreview() {
      this.visible = true
    },
    handleClose() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.group-pkg-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

// .drag-handle {
//   cursor: grab;
// }
</style>
