<template>
  <div class="grouping-container">
    <h3>Create Group</h3>
    <a-tabs :active-key="activeTab" @tabClick="handleChangeTabs">
      <a-tab-pane :tab="$t(GROUPING_TEXT_ID_MAP['GROUP_INFO'])" key="groupInfo">
        <group-info
          v-if="activeTab === 'groupInfo'"
          class="bg-white pd-20"
          @changeTabs="(tab) => (activeTab = tab)"
        />
      </a-tab-pane>
      <a-tab-pane :tab="$t(GROUPING_TEXT_ID_MAP['PACKAGE_MAPPING'])" key="packageSetting">
        <pkg-setting v-if="activeTab === 'packageSetting'" class="bg-white pd-20" ref="pkgSetting" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import GroupInfo from './components/groupInfo/index.vue'
import PkgSetting from './components/pkgSetting/index.vue'
import { GROUPING_TEXT_ID_MAP } from './constant'
export default {
  name: 'ActivityGrouping',
  components: {
    GroupInfo,
    PkgSetting
  },
  provide() {
    return {
      hasSubmitPermissions: this.hasSubmitPermissions,
      activityId: this.activityId
    }
  },
  data() {
    return {
      activeTab: 'groupInfo',
      GROUPING_TEXT_ID_MAP
    }
  },
  created() {
    const { activeTab = 'groupInfo' } = this.$route.query || {}
    this.activeTab = activeTab
  },
  computed: {
    hasSubmitPermissions() {
      // todo
      return klook.checkPermission('experience/act/activity_edit')
    },
    activityId() {
      return +this.$route.params.activity_id || ''
    }
  },
  watch: {
    activeTab: {
      immediate: true,
      handler(v) {
        this.updateURLParameter('activeTab', v)
      }
    }
  },
  methods: {
    handleChangeTabs(targetTab) {
      const hasUnSavedSetting = this.$refs.pkgSetting?.hasUnSavedSetting || false
      if (hasUnSavedSetting && targetTab === 'groupInfo') {
        this.$modal.confirm({
          title: this.$t(GROUPING_TEXT_ID_MAP['UNSAVED_CHANGE_WARNING']),
          onOk: () => {
            this.activeTab = targetTab
          },
          onCancel() {
            console.log('Cancel')
          }
        })
      } else {
        this.activeTab = targetTab
      }
    },
    updateURLParameter(key, value) {
      let currentUrl = new URL(window.location.href)
      currentUrl.searchParams.set(key, value)
      window.history.replaceState({}, '', currentUrl)
    }
  }
}
</script>

<style lang="scss" scoped>
.bg-white {
  background: #f5f5f5;
}
.pd-20 {
  padding: 20px;
}
.grouping-container {
  padding: 10px;
}
</style>
