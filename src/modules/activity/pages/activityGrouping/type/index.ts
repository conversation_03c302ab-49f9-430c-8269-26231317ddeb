export interface Ii18n {
  language: string;
  group_name: string;
  group_desc?: string;
}
export interface IGroup {
  group_id?: number;
  group_name: string;
  group_desc: string;
  is_opt: boolean; // 是否是运营组，感觉可以不要了
  priority?: number; // 新建、修改、删除的时候不需要，整体update的时候需要，查找的时候需要
  i18ns?: Array<Ii18n>;
  translate_status: 0 | 1; // 0没翻译，1翻译完了,
  pkg_rel_numbers: number; // 绑定的套餐数量
}

export interface IActivityGroupList {
  activity_id: number;
  grouping_status: 0 | 1 | 2;
  group_list: Array<IGroup> | [];
}
