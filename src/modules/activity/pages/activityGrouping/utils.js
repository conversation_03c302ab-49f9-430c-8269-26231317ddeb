const lang_conf = require('lang_conf')
// 语言下拉
export const lang_array = lang_conf
  .getAllLangOption()
  .map((v) => ({
    name: v.options,
    val: v.B_LANG
  }))
  ?.filter((ele) => ele?.val !== 'en_US')
// key: B_LANG, value: LANG_TITLE
export const { en_US, ...lang_obj } = lang_conf.getLangObj('B_LANG', 'LANG_TITLE')
export function isValidText(input) {
  // 正则表达式，匹配所有的ASCII字符
  // eslint-disable-next-line
  const regex = /^[\x00-\x7F]*$/
  return regex.test(input)
}

export function isArrayOrderEqual(array1, array2) {
  if (array1.length !== array2.length) {
    return false
  }
  for (let i = 0; i < array1.length; i++) {
    if (array1[i] !== array2[i]) {
      return false
    }
  }
  return true
}

export function calcIsPkgMatchingEqual(array1, array2) {
  if (array1.length !== array2.length) {
    return false
  }
  for (let i = 0; i < array1.length; i++) {
    if (array1?.[i]?.group_id !== array2?.[i]?.group_id) {
      return false
    }
    if (!isArrayOrderEqual(array1?.[i]?.package_ids, array2?.[i]?.package_ids)) {
      return false
    }
  }
  return true
}
