<template>
  <div class="act-create-footer">
    <slot name="top"></slot>
    <div class="act-create-footer-box">
      <div v-if="calcCount" class="btns-box left-box">
        <a-button :disabled="!isReady" @click="clickBack">{{ $t('44926') }}</a-button>
      </div>
      <div v-if="showProgress" class="middle-box">
        <div v-if="calcStepType === 'step_activity'" class="progress-box">
          <a-progress v-bind="progressObj"></a-progress>
          <span v-show="calcCount < calcActStepNum" class="progress-box__text">
            <a-icon
              v-if="progressObj.percent === 100"
              type="check-circle"
              theme="filled"
              class="progress-box__per100"
            ></a-icon>
            <span>{{ $t('83866', { num: progressObj.percent + '%' }) }}</span>
          </span>
        </div>
        <div v-else class="progress-box">
          <div class="progress-box__pkg-per">
            <a-progress v-for="(pkg, i) in packagesList" :key="i" v-bind="calcPkgProgressObj(i)"></a-progress>
          </div>
          <span v-show="calcCount >= calcActStepNum" class="progress-box__text">{{
            $t('83867', { num: `${calcDonePkgIdx + 1}/${calcPidList.length}` })
          }}</span>
        </div>
      </div>
      <div v-else>
        <!-- 占位专用 勿删 -->
      </div>
      <div class="btns-box right-box">
        <span v-if="calcCount === calcMaxCount" class="submit-all-alert">
          <a-alert :message="$t('88709')" type="success" show-icon />
        </span>
        <span>
          <!-- 添加key解决埋点共享问题 -->
          <a-button
            :key="calcCount"
            class="js-guide-create-save-btn"
            v-bind="calcNextBtn"
            :disabled="!isReady"
            @click.stop="clickNext"
          >
            {{ calcNextBtn.text }}
          </a-button>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { merge } from 'lodash'
import StepMixin from '../step-mixin.js'
export default {
  name: 'ActCreateFooter',
  mixins: [StepMixin],
  props: {
    activityId: {
      type: Number,
      default: 0
    },
    isReady: {
      type: Boolean,
      default: false
    },
    showProgress: {
      type: Boolean,
      default: false
    },
    isStepFinished: {
      type: Boolean,
      default: false
    }
  },
  updated() {
    const data = { height: this.$el.offsetHeight }
    this.$emit('updated', data)
  },
  data() {
    return {
      backDisabled: false,
      nextDisabled: false,
      progressObj: {
        addPercent: 5,
        showInfo: false,
        percent: 0
      }
    }
  },
  watch: {
    calcPercent: {
      immediate: true,
      handler(per) {
        const { progressObj } = this
        const addPer = per + progressObj.addPercent
        progressObj.percent = addPer < 100 ? addPer : 100
      }
    }
  },
  // eslint-disable-next-line vue/order-in-components
  computed: {
    calcNextBtn() {
      const { calcCount, calcMaxCount, calcOnePkgStepNum, calcCurrPkg, calcCurrStepData } = this

      if (!calcCurrStepData) {
        return {
          type: 'primary',
          text: this.$t('save_and_next')
        }
      }

      const { spm } = calcCurrStepData
      let obj = {}
      let vbindData = {}
      if (spm) {
        merge(vbindData, {
          'data-spm-module': `${spm}?trg=manual`,
          'data-spm-virtual-item': '__virtual?trg=manual'
        })
      }
      if (calcCount === calcMaxCount) {
        obj = {
          'data-spm-module':
            vbindData['data-spm-module'] && `${vbindData['data-spm-module']}&oid=activity_${this.activityId}`,
          type: 'primary',
          text: this.$t('83895')
        }
      } else if (calcCount === 4) {
        obj = {
          type: 'primary',
          text: this.$t('83865')
        }
      } else if (calcOnePkgStepNum === (calcCurrPkg.steps || []).length - 1) {
        obj = {
          type: 'primary',
          text: this.$t('83896')
        }
      } else {
        obj = {
          type: 'primary',
          text: this.$t('save_and_next')
        }
      }
      return merge(vbindData, obj)
    },
    calcPkgProgressObj() {
      return (i) => {
        const { calcDonePkgIdx, progressObj } = this
        if (i < calcDonePkgIdx) {
          return {
            showInfo: false,
            percent: 100
          }
        } else if (i > calcDonePkgIdx) {
          return {
            showInfo: false,
            percent: 0
          }
        }
        return progressObj
      }
    },
    calcPercent() {
      const { calcCount, isStepFinished, calcStepType, calcActStepNum, calcOnePkgStepNum, calcCurrPkg } = this
      const currPkgStepLen = calcCurrPkg?.steps?.length || 0

      if (calcStepType === 'step_activity') {
        return Math.round(((calcCount + Number(isStepFinished)) / calcActStepNum) * 100)
      } else {
        return Math.round(((calcOnePkgStepNum + Number(isStepFinished)) / currPkgStepLen) * 100)
      }
    }
  },
  methods: {
    clickBack() {
      this.$emit('back')
    },
    clickNext() {
      this.$emit('next')
    }
  }
}
</script>

<style lang="scss" scoped>
.submit-all-alert {
  display: inline-block;
  max-width: 552px;
  margin: 0 32px;
  .ant-alert ::v-deep {
    .ant-alert-icon {
      top: calc(50% - 7px);
    }
  }
}
.act-create-foter {
  //
}
.act-create-footer-box {
  box-shadow: inset 0px 1px 0px #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: #fff;
  .left-box {
    margin-right: 32px;
  }
  .middle-box {
    flex: 1 1 auto;
    text-align: center;
  }
  .right-box {
    display: flex;
    align-items: center;
    margin-left: 32px;
  }
  .progress-box {
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.45);
    .ant-progress.ant-progress-line {
      font-size: 14px;
      line-height: 22px;
    }
    &__pkg-per {
      width: 100%;
      display: flex;
      align-items: center;
      .ant-progress {
        &:not(:last-of-type) {
          margin-right: 2px;
        }
      }
    }
    &__text {
      margin-left: 10px;
      flex: none;
    }
    &__per100 {
      margin-right: 10px;
      flex: none;
      color: rgba(82, 196, 26, 1);
    }
  }
}
</style>
