<template>
  <div id="js-guide-create-page-spm" class="act-create-guide" :data-spm-page="calcStepPageSpm">
    <div class="act-create-guide-box">
      <ActCreate v-if="initialized" :is-guide-flow="true" :style-type="'page-type'"></ActCreate>
      <FloatReportGuide />
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import ActCreate from './index.vue'
import FloatReportGuide from '@activity/components/float-report-guide/index.vue'
import { defaultSupplyApiMappingSupportConfig } from '@activity/pages/activityManagement/act-create/utils/index.js'

export default {
  name: 'ActCreateGuide',
  components: {
    ActCreate,
    FloatReportGuide
  },
  data() {
    return {
      // 如果路由直接打开 step_count 不为0，则需要等待 updateActItemData2actions 完成
      initialized: false
    }
  },
  computed: {
    ...mapGetters(['currSupplyApiMappingSupportConfigGetter']),
    activityId() {
      return this.$route.query.activity_id
    },
    calcStep() {
      return klook.parse2id(this.$route.query.step_count) || 0
    },
    calcStepPageSpm() {
      if (!this.initialized) {
        return ''
      }

      const { calcStep, calcFlattenGuideStepPageSpm } = this
      const spm = calcFlattenGuideStepPageSpm[calcStep]
      if (!spm) {
        return
      }
      const track = `${spm}?trg=manual`
      return track
    },
    calcSupplyApiMappingSupportConfig() {
      return this.currSupplyApiMappingSupportConfigGetter || defaultSupplyApiMappingSupportConfig
    },
    calcFlattenGuideStepPageSpm() {
      const actSpmMap = [
        'CreationBasicInfo',
        'CreationLocation',
        'CreationPhoto',
        'CreationHighlights',
        'CreationDetails'
      ]
      const pkgSpmMap = ['CreationPackageInfo', 'CreationPrice', 'CreationOtherInfo']

      const { support_supply_api_mapping } = this.calcSupplyApiMappingSupportConfig
      if (support_supply_api_mapping) {
        pkgSpmMap.splice(-1, 0, 'packageSupplyApiMapping')
      }

      return [...actSpmMap, ...pkgSpmMap]
    }
  },
  watch: {
    calcStep: {
      immediate: true,
      async handler(stepCount) {
        this.$nextTick(() => {
          this.$tracker.track('pageview', '#js-guide-create-page-spm')
        })

        if ((stepCount > 0 && this.activityId) || this.activityId) {
          await this.updateActItemData()
        }

        this.initialized = true
      }
    }
  },
  methods: {
    ...mapActions(['updateActItemData2actions']),
    async updateActItemData() {
      await this.updateActItemData2actions({ activity_id: this.activityId })
    }
  }
}
</script>

<style lang="scss" scoped>
.act-create-guide {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  &-box {
    width: 100%;
    background: #fff;
  }
}
.act-create-guide-box {
  position: relative;
  height: 100vh;
}
</style>
