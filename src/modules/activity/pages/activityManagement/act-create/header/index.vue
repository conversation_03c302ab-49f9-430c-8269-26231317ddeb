<template>
  <div class="act-create-header" :class="styleType">
    <div class="act-create-header-box">
      <div class="steps-box-wrap">
        <div class="steps-box">
          <a-steps :current="calcCurrent">
            <a-step disabled>
              <template slot="title">
                {{ $t('29168') }}
              </template>
              <span slot="description" class="desc nowrap">{{
                $t(calcStepType === 'step_activity' ? '83847' : '83848', { num: calcActStepNum })
              }}</span>
            </a-step>
            <a-step disabled>
              <template slot="title">
                {{ $t('29169') }}
              </template>
              <span v-if="calcStepType === 'step_package'" slot="description" class="desc">
                <span v-show="calcDonePkgIdx" class="desc-done">{{
                  $t('83850', { num: calcDonePkgIdx })
                }}</span>
                <span
                  v-show="calcDonePkgIdx && packagesList && packagesList.length >= 2"
                  class="desc-poin"
                ></span>
                <span v-show="packagesList.length - calcDonePkgIdx" class="desc-submit">{{
                  $t('83849', { num: packagesList.length - calcDonePkgIdx })
                }}</span>
              </span>
            </a-step>
          </a-steps>
        </div>
      </div>
      <div v-if="calcCount >= calcActStepNum" class="pkg-box">
        {{ calcPkgInfo }}
      </div>
    </div>
    <!-- <span v-if="calcCount" class="create-close-btn" @click="createCloseHandler(calcCount)">
      <a-icon type="close"></a-icon>
    </span> -->
    <span v-if="calcCount > 1" class="create-close-btn" @click="createCloseHandler(calcCount)">
      <a-button type="danger" ghost>{{ $t('83851') }}</a-button>
    </span>
  </div>
</template>

<script>
import StepMixin from '../step-mixin.js'
export default {
  name: 'ActCreateHeader',
  mixins: [StepMixin],
  props: {
    styleType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  updated() {
    const data = { height: this.$el.offsetHeight }
    this.$emit('updated', data)
  },
  computed: {
    calcPkgInfo() {
      const { calcPkgData } = this
      const str = `${this.$t('29169')} ${this.calcDonePkgIdx + 1}: ${calcPkgData?.package_title || ''}`
      return str
    }
  },
  methods: {
    createCloseHandler(stepCount) {
      this.$modal.confirm({
        zIndex: 1111,
        title: this.$t('83897'),
        content: this.$t('83898'),
        maskClosable: true,
        cancelText: this.$t('global_cancel'),
        okText: this.$t('83899'),
        onOk: async () => {
          const { query } = this.$route
          this.$router.push({
            path: `/act/activity/basic/${query.activity_id}`,
            query: {
              lang: klook.getEditLang()
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.center-type.act-create-header {
  .act-create-header-box {
    width: 800px;
    padding: 0 36px;
    margin: 0 auto;
  }
}
.act-create-header {
  position: relative;
  padding: 24px 24px 8px 24px;
  background: #fafafa;
  .create-close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 8;
    font-size: 16px;
    padding: 8px;
    cursor: pointer;
  }
  ::v-deep .ant-steps {
    .ant-steps-item {
      &:nth-of-type(1) {
        flex: none;
      }
      &:nth-of-type(2) {
        flex: 1 1 auto;
      }
    }
    &.ant-steps-horizontal:not(.ant-steps-label-vertical) .ant-steps-item-description {
      max-width: 300px;
      min-width: 200px;
    }
  }
}
.act-create-header-box {
  width: 100%;
  .steps-box-wrap {
    padding: 0 28px 0 100px;
  }
  .steps-box {
    width: 100%;
    .desc {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      color: rgba(0, 0, 0, 0.45);
      &.nowrap {
        white-space: nowrap;
      }
      &-done {
        color: #52c41a;
      }
      &-poin {
        flex: none;
        display: inline-block;
        margin: 0 8px;
        width: 3px;
        height: 3px;
        border-radius: 50%;
        color: #212121;
        background: #212121;
      }
      &-submit {
        color: #faad14;
      }
    }
  }
  .pkg-box {
    margin-top: 20px;
    font-size: 14px;
    line-height: 22px;
    color: #212121;
  }
}
</style>
