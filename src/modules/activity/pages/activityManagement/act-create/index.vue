<template>
  <div class="create-guide-mode" :class="[styleType, calcAid && 'spec-units-style']">
    <!-- <div class="create-header-wrap">
      <CreateHeader
        :style-type="'center-type'"
        :step-data="stepObj"
        :packages-list="calcPackages"
        @updated="updatedHeader"
        @updatePid="updatePid"
      ></CreateHeader>
    </div> -->
    <div
      class="create-main-wrap"
      :class="calcAid && !stepObj.stepCount ? 'pointer-events-none' : ''"
      :style="calcMainStyle"
    >
      <a-affix :offset-top="1">
        <div class="guide-nav-box">
          <div
            v-bind="getQuitTrack()"
            :style="{ visibility: stepObj.stepCount > 1 ? 'visible' : 'hidden' }"
            class="create-close-btn"
            @click="createCloseHandler(stepObj.stepCount)"
          >
            <a-button>{{ $t('83851') }}</a-button>
          </div>
          <CreateNav
            :activity-id="calcAid"
            :step-data="stepObj"
            :packages-list="calcAllPackage"
            :language="calcLang"
            :current-pid="currentPid"
            :act-steps="calcActSteps"
            @updatePid="updatePid"
          ></CreateNav>
        </div>
      </a-affix>
      <CreateMain
        ref="createMainRef"
        :is-guide-flow="isGuideFlow"
        :step-data="stepObj"
        :packages-list="calcAllPackage"
        :actSectionData="actSectionApiData"
        :pkgSectionData="pkgSectionApiData"
        :leafCategoryId="leaf_category_id"
        :packageId="currentPid"
        :prevPidData="prevPidData"
        :act-steps="calcActSteps"
        @updateFinished="updateFinishedHandler"
        @guideMounted="guideMountedHandler"
      ></CreateMain>
    </div>
    <div v-show="actSectionApiData && actSectionApiData.sections" class="create-footer-wrap">
      <CreateFooter
        :activity-id="calcAid"
        :step-data="stepObj"
        :packages-list="calcAllPackage"
        :is-step-finished="isCurrentFinished"
        :is-ready="isReady"
        :act-steps="calcActSteps"
        @back="stepObj.ons.backHandler"
        @next="stepObj.ons.nextHandler"
        @updated="updatedFooter"
      >
        <div v-show="!stepObj.stepCount" slot="top" class="custom-tips-wrap">
          <!-- 使用v-if，先保存在返回，custom-tips-box会丢失样式 -->
          <div class="custom-tips-box">
            {{ $t('83894') }}
          </div>
        </div>
      </CreateFooter>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
// import CreateHeader from './header/index.vue'
import CreateNav from './nav/index.vue'
import CreateMain from './main/index.vue'
import CreateFooter from './footer/index.vue'
import { action as pkgsAction, state as pkgsState } from '@activity/store/pkgs-cache-observable.js'
import { setScrollTop } from '@activity/utils/index.js'
import {
  getGuideCreationStepList,
  defaultSupplyApiMappingSupportConfig,
  creationStepKeyDict
} from '@activity/pages/activityManagement/act-create/utils/index.js'

export default {
  name: 'ActCreate',
  components: { CreateMain, CreateFooter, CreateNav },
  provide() {
    return {
      getFinishedStatus2provide: this.getFinishedStatus,
      getOnePkgStepNum: this.getOnePkgStepNum
    }
  },
  props: {
    isGuideFlow: {
      type: Boolean,
      default: false
    },
    styleType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isReady: true,
      prevPidData: null,
      currentPidStepStatus: null,
      isCurrentFinished: false,
      pkgsCacheState: pkgsState['pkgsCache'],
      currentPid: 0,
      actSectionApiData: null,
      pkgSectionApiData: null,
      cachePkgsApiData: {},
      headerData: {
        height: 0
      },
      footerData: {
        height: 80
      },
      stepObj: {
        stepCount: Number(this.$route.query.step_count) || 0, // 初始化
        type: 'step_activity',
        ons: {
          beforeNextHandler: () => {
            return false
          },
          backHandler: () => {
            const { stepObj } = this
            if (stepObj.stepCount === 0) {
              return
            }
            stepObj.stepCount -= 1
            stepObj.ons.typeHandler()
          },
          nextHandler: async () => {
            const { stepObj, calcMaxCount } = this
            const res = await stepObj.ons.beforeNextHandler()
            if (!res) {
              return false
            }
            if (stepObj.stepCount === calcMaxCount) {
              return
            }
            stepObj.stepCount += 1
            this.isCurrentFinished = false
            stepObj.ons.typeHandler()
          },
          typeHandler: (isInit) => {
            let isReplace = !isInit
            const { stepObj, calcMaxCount } = this
            if (stepObj.stepCount < 0) {
              stepObj.stepCount = 0
              isReplace = true
            }
            if (stepObj.stepCount > calcMaxCount) {
              stepObj.stepCount = calcMaxCount
              isReplace = true
            }

            stepObj.type = stepObj.stepCount < this.calcActStepNum ? 'step_activity' : 'step_package'
            if (isReplace) {
              const { path, query } = this.$route
              this.$router.push({
                path,
                query: {
                  ...query,
                  step_count: stepObj.stepCount || 0
                }
              })
            }
          }
        }
      }
    }
  },
  computed: {
    ...mapGetters(['currSupplyApiMappingSupportConfigGetter']),
    calcPackages() {
      const { sections } = this.actSectionApiData || {}
      return (sections && sections[0]?.packages) || []
    },
    calcAllPackage() {
      return this.calcPackages.map((item) => {
        const pid = item.package_id
        const { pkg } = getGuideCreationStepList()
        const steps = pkg.reduce((accSteps, currStep) => {
          if (currStep.step === 'supply_api_mapping') {
            const supplyApiMappingSupportConfig =
              this.$store.getters.getSupplyApiMappingSupportConfigByPid(pid)
            const { support_supply_api_mapping, is_required } = supplyApiMappingSupportConfig

            if (support_supply_api_mapping !== 0) {
              accSteps.push({ ...currStep, require: is_required })
            }
          } else {
            accSteps.push(currStep)
          }

          return accSteps
        }, [])

        return {
          ...item,
          steps
        }
      })
    },
    calcAid() {
      return klook.parse2id(this.$route.query.activity_id)
    },
    calcLang() {
      return this.$route.query.lang
    },
    leaf_category_id() {
      return klook.parse2id(this.$route.params.id)
    },
    calcMaxCount() {
      const { calcAllPackage, calcActStepNum } = this
      const pkgLength = calcAllPackage.reduce((acc, curr) => (acc += curr.steps.length || 0), 0)

      return calcActStepNum + pkgLength - 1
    },
    calcPkgStepStatusData() {
      return (pid) => {
        const { pkgsCacheState } = this
        const pkgStepStatus = pkgsCacheState?.[pid]?.[ADMIN_API.act.get_package_step_status] || {}
        return pkgStepStatus
      }
    },
    calcMainStyle() {
      const addHeight = this.stepObj.stepCount ? 0 : 40
      return {
        paddingBottom: this.footerData.height + addHeight + 'px',
        paddingTop: this.headerData.height + 'px'
      }
    },
    calcSupplyApiMappingSupportConfig() {
      return this.currSupplyApiMappingSupportConfigGetter || defaultSupplyApiMappingSupportConfig
    },
    calcActSteps() {
      return getGuideCreationStepList().act
    },
    calcActStepNum() {
      return this.calcActSteps.length
    }
  },
  watch: {
    'stepObj.stepCount': {
      handler() {
        this.stepObj.ons.beforeNextHandler = () => false
        setScrollTop(0)
      }
    },
    calcLang: {
      immediate: true,
      handler(language) {
        language && this.initPackageFloatingFields(this.calcPackages)
      }
    },
    currentPid: {
      immediate: true,
      async handler(v) {
        if (v) {
          this.pkgSectionApiData = null
          this.prevPidData = await this.getCopyPrevPkgData(v)
          await this.updateBasicInfoByPid({ loading: true, needCopyFlag: true })
        }
      }
    }
  },
  async created() {
    klook.bus.$off('sendGuideCreateSave2bus').$on('sendGuideCreateSave2bus', this.sendGuideCreateSave2bus)
    klook.bus.$off('addHandleSave2bus').$on('addHandleSave2bus', this.setGuideCreateNextHandler)
    klook.bus.$off('successNextHandler2bus').$on('successNextHandler2bus', this.successNextHandler)
    klook.bus.$off('updateBasicInfoByPid2bus').$on('updateBasicInfoByPid2bus', this.updateBasicInfoByPid)
    klook.bus.$off('setStepStatusFinished2bus').$on('setStepStatusFinished2bus', this.setStepStatusFinished)
    if (this.calcAid && this.calcLang) {
      this.actSectionApiData = (await this.getBasicInfoByAid(this.getActParams())) || {}
    } else {
      this.gotoCreateStep()
      this.actSectionApiData =
        (await this.getActBasicSectionData({ leaf_category_id: this.leaf_category_id })) || {}
    }
    await this.initAllStepStatus()
    this.initData()
  },
  beforeDestroy() {
    klook.bus
      .$off('sendGuideCreateSave2bus', this.sendGuideCreateSave2bus)
      .$off('addHandleSave2bus', this.setGuideCreateNextHandler)
      .$off('successNextHandler2bus', this.successNextHandler)
      .$off('updateBasicInfoByPid2bus', this.updateBasicInfoByPid)
      .$off('setStepStatusFinished2bus', this.setStepStatusFinished)
  },
  methods: {
    ...mapActions([
      'updateActivityStepStatusById',
      'updateActStepStatus2action',
      'updatePackageStepStatusById',
      'updatePkgStepStatus2action'
    ]),
    sendGuideCreateSave2bus(calcCurrStepData, resData) {
      const spm = calcCurrStepData
      if (!spm) {
        return
      }

      const { success, error } = resData || {}
      const extObj = {
        err_message: success ? '' : error?.message || 'Error: false'
      }
      this.$root.trackIHEvent('.js-guide-create-save-btn', extObj)
    },
    getQuitTrack() {
      return this.$root.bindIHTrack({
        type: 'module',
        spm: 'QuitCreation',
        exposure: false,
        query: { oid: `activity_${this.calcAid}` }
      })
    },
    getOnePkgStepNum(count, packages = this.calcAllPackage) {
      let remainNum = count - this.calcActStepNum
      let temp = remainNum

      for (let pkg of packages) {
        if (temp <= 0) {
          return temp === 0 ? 0 : remainNum
        }
        remainNum = temp
        temp = remainNum - pkg.steps.length
      }

      return remainNum
    },
    async initPkgStepStatus(sync, mergeData) {
      let data = {
        activity_id: this.calcAid,
        package_id: this.currentPid,
        language: this.calcLang,
        refresh: true,
        ...(mergeData || {})
      }
      if (!data.package_id) {
        return
      }
      if (sync) {
        return await this.updatePackageStepStatusById(data)
      } else {
        return this.updatePackageStepStatusById(data)
      }
    },
    async initAllStepStatus() {
      if (!this.calcAid || !this.calcLang) {
        return
      }
      const response = await ajax.get(ADMIN_API.act.get_data_status, {
        params: {
          activity_id: this.calcAid,
          language: this.calcLang
        }
      })
      await this.initActStepStatus(true, { resultData: response?.activity_steps })
      const pkgsStepsObj = response?.package_steps || {}
      for (let key in pkgsStepsObj) {
        const resultData = pkgsStepsObj[key]
        await this.initPkgStepStatus(true, { refresh: true, package_id: Number(key), resultData })
      }
    },
    async initActStepStatus(sync = false, mergeData) {
      if (!this.calcAid || !this.calcLang) {
        return
      }
      let data = {
        activity_id: this.calcAid,
        language: this.calcLang,
        refresh: true,
        ...(mergeData || {})
      }
      if (sync) {
        return await this.updateActivityStepStatusById(data)
      } else {
        return this.updateActivityStepStatusById(data)
      }
    },
    gotoCreateStep() {
      const { path, query } = this.$route
      if (!query.activity_id && !query.lang) {
        return
      }
      this.$router.push({
        path
      })
    },
    updateFinishedHandler(isb) {
      this.isCurrentFinished = isb
      if (!this.stepObj.stepCount && this.calcAid) {
        this.isCurrentFinished = true
      }
    },
    updatePid(pid) {
      this.currentPid = pid
    },
    async getFinishedStatus() {
      const ref = this.$refs.createMainRef
      const status = ref?.checkStepCountStatus ? await ref.checkStepCountStatus() : true
      return status
    },
    getStepInfoByCount(stepCount, params) {
      const isAct = this.stepObj.type === 'step_activity'

      if (isAct) {
        return this.calcActSteps[stepCount]
      } else {
        const currPkgStepNum = this.getOnePkgStepNum(this.stepObj.stepCount)
        const currPkg = this.calcAllPackage.find((pkg) => pkg.package_id === params.package_id)
        return currPkg.steps?.[currPkgStepNum]
      }
    },
    async setStepStatusFinished(stepCount, params) {
      const isAct = this.stepObj.type === 'step_activity'
      const step = this.getStepInfoByCount(stepCount, params)?.step

      if (!step) {
        return
      }
      const { query } = this.$route
      const { activity_id, lang: language } = query
      const data = {
        activity_id: +activity_id,
        status: 1,
        step,
        language,
        ...(params || {})
      }
      if (isAct) {
        this.updateActStepStatus2action(data)
      } else {
        this.updatePkgStepStatus2action(data)
      }
    },
    async successNextHandler(successStepCount, data = {}, callback) {
      const { query, path } = this.$route
      if (successStepCount) {
        // 活动保存成功回调
        if (this.stepObj.type === 'step_activity') {
          let statusParams = {}
          if ([3, 4].includes(successStepCount)) {
            const arr = data?.field_items || []
            const isFinished =
              arr.length && arr.every((item) => item.value?.find((o) => o.language === data?.language)?.name)
            statusParams.status = Number(isFinished)
          }
          this.setStepStatusFinished(successStepCount, statusParams)
        }
        const params = { section: successStepCount + 1 } // step_count前端从0开始，后端从1开始
        const updateSections = (await this.getBasicInfoByAid(params))?.sections
        if (updateSections && updateSections[0]) {
          this.$set(this.actSectionApiData?.sections, successStepCount, updateSections[0])
        }
      }
      switch (successStepCount) {
        case 0:
          this.setStepStatusFinished(successStepCount, {
            activity_id: data.activity_id,
            language: data.language
          })
          this.$set(this, 'actSectionApiData', data)
          this.$router.replace({
            path,
            query: {
              ...query,
              activity_id: data.activity_id,
              lang: data.language
            }
          })
          break
        default:
          break
      }
      callback?.()
    },
    guideMountedHandler() {
      setTimeout(() => {
        this.isReady = true
      }, 600)
    },
    setGuideCreateNextHandler(callback, params) {
      const defFn = () => {
        return true
      }
      const fn = typeof callback === 'function' ? callback : defFn
      this.isReady = false
      this.stepObj.ons.beforeNextHandler = fn.bind(params)
    },
    getActParams() {
      const { activity_id } = this.$route.query
      return {
        activity_id,
        language: this.calcLang
      }
    },
    getPkgParams() {
      return {
        package_id: this.currentPid,
        language: this.calcLang
      }
    },
    async getBasicInfoByAid(mergeParams) {
      const params = {
        ...this.getActParams(),
        ...(mergeParams || {})
      }
      const res = await ajax.get(ADMIN_API.act.get_basic_info_by_section2act, {
        params
      })

      this.$store.commit('setMarkdownSupportConfig', res.markdown_support_config)
      return res
    },
    async updateBasicInfoByPid(config) {
      const opt = config || {}
      await this.getPkgData({ refleshApiData: true, ...opt })
      const res = await this.getBasicInfoByPid({}, { refleshApiData: true, ...opt })
      this.pkgSectionApiData = res?.result
    },
    getPrevPkgData(pid) {
      const { calcAllPackage } = this
      const idx = calcAllPackage?.findIndex((o) => o.package_id === pid)
      if (idx) {
        return calcAllPackage[idx - 1]
      }
      return null
    },
    async getCopyPrevPkgData(pid) {
      const obj = this.getPrevPkgData(pid)
      if (obj) {
        const statusObj = this.calcPkgStepStatusData(pid)
        const currPkg = this.calcAllPackage.find((pkg) => pkg.package_id === pid)

        const copyList = (currPkg.steps || []).filter((item) => !statusObj[item.step])
        if (!copyList.length) {
          return null
        }

        const prevPid = obj.package_id
        const params = {
          package_id: prevPid,
          language: this.calcLang
        }
        const result = await ajax.get(
          ADMIN_API.act.get_package_info,
          {
            params
          },
          {
            loading: true
          }
        )
        const data = {
          params,
          result
        }
        return data
      }
      return null
    },
    async getBasicInfoByPid(mergeParams, opt) {
      const params = {
        ...this.getPkgParams(),
        ...(mergeParams || {})
      }
      const { package_id } = params

      const prevData = this.getPrevPkgData(package_id)
      const isCopyFlag =
        opt?.needCopyFlag && prevData && !this.calcPkgStepStatusData(package_id)?.package_main_info
      let apiUrl = ADMIN_API.act.get_basic_info_by_section2pkg
      let res = isCopyFlag || opt?.refleshApiData ? null : pkgsAction.get(package_id, apiUrl)
      if (!res?.success) {
        res = await ajax.getBody(
          apiUrl,
          {
            params: {
              ...params,
              ...(isCopyFlag ? { package_id: prevData.package_id } : {})
            }
          },
          opt
        )
        if (isCopyFlag && res?.result?.package_id) {
          res.result.package_id = package_id

          const currPkgStepNum = this.getOnePkgStepNum(this.stepObj.stepCount)
          const currPkg = this.calcAllPackage.find((pkg) => pkg.package_id === package_id)
          const isTips = currPkg.steps[currPkgStepNum].step !== creationStepKeyDict.extra_information // other info不复制
          isTips && this.$message.info(this.$t('84418'))
        }
        pkgsAction.set(package_id, apiUrl, res)
      }
      return res
    },
    async getPkgData(opt) {
      let { currentPid: package_id } = this
      if (package_id) {
        let apiUrl = ADMIN_API.act.get_floating_fields2pkg
        let res = opt?.refleshApiData ? null : pkgsAction.get(package_id, apiUrl)
        if (!res?.success) {
          res = await this.getPackageFloatingFieldsById(package_id, opt)
          pkgsAction.set(package_id, apiUrl, res)
        }
        const result = res?.result || {}
        this.$store.commit('setPackageFloatingFieldMap', result)
        this.$store.commit('setCurrentPackageFloatingField', result)
      } else {
        this.$store.commit('setCurrentPackageFloatingField', {})
      }
    },
    async initPackageFloatingFields(packages) {
      for (let pkg of packages) {
        const response = await this.getPackageFloatingFieldsById(pkg.package_id)
        this.$store.commit('setPackageFloatingFieldMap', response?.result || {})
      }
    },
    async getPackageFloatingFieldsById(id, opt = {}) {
      return await ajax.getBody(
        ADMIN_API.act.get_floating_fields2pkg,
        {
          params: {
            package_id: id,
            page_from: klook.getPlatformRoleKey(),
            language: this.calcLang
          }
        },
        opt
      )
    },
    async getActBasicSectionData(params) {
      const res = await ajax.get(ADMIN_API.act.get_basic_info_by_category, {
        params
      })
      return res
    },
    initData() {
      const { stepObj } = this
      stepObj.stepCount = klook.parse2id(this.$route.query.step_count) || 0
      stepObj.ons.typeHandler(true)
    },
    updatedFooter(data) {
      this.footerData.height = data.height
    },
    updatedHeader(data) {
      this.headerData.height = data.height
    },
    createCloseHandler(stepCount) {
      this.$modal.confirm({
        zIndex: 1111,
        title: this.$t('83897'),
        content: this.$t('83898'),
        maskClosable: true,
        cancelText: this.$t('global_cancel'),
        okText: this.$t('83899'),
        onOk: async () => {
          const { query } = this.$route
          this.$router.push({
            path: `/act/activity/basic/${query.activity_id}`,
            query: {
              lang: klook.getEditLang()
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.guide-nav-box {
  width: 280px;
  height: calc(100vh - 64px);
  overflow: hidden;
  overflow-y: auto;
  padding: 20px 40px;
  flex: none;
  background-color: #fafafa;
}
.create-close-btn {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}
.spec-units-style.create-guide-mode ::v-deep {
  .use-customized-name {
    display: none;
  }
}
.page-type.create-guide-mode {
  .create-header-wrap,
  .create-footer-wrap {
    position: fixed;
  }
  .create-main-wrap {
    display: flex;
    height: auto;
    overflow: auto;
  }
}
.custom-tips-wrap {
  position: absolute;
  bottom: 100%;
  left: 280px;
  width: 100%;
  padding: 0 calc(280px + 320px) 0 0;
  // 上面实现一，下面实现二
  // width: 100%;
  // padding: 0 320px 0 280px;
  // background-color: #fafafa;
}
.custom-tips-box {
  padding: 9px 24px;
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  background-color: #fffbe6;
}
.create-guide-mode {
  position: relative;
  width: 100%;
  height: 100%;
  .create-header-wrap {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 88;
  }
  .create-footer-wrap {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 88;
  }
  .create-main-wrap {
    height: 100%;
    padding: 85px 0 74px 0;
    overflow: hidden;
    overflow-y: auto;
    box-sizing: border-box;
    .act-create-main {
      width: 800px;
    }
    &.pointer-events-none {
      cursor: not-allowed;
      .act-create-main {
        position: relative;
        pointer-events: none;
        &::after {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          z-index: 66;
          width: 100%;
          height: 100%;
          background-color: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
}
</style>
