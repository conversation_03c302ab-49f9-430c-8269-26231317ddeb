<script>
import { mapState, mapActions, mapGetters } from 'vuex'
import { getEditLang, getRefLang } from '@activity/utils'
import language_destination from '@activity/components/language_destination'
import binded_area from '@activity/components/language_destination/binded_area'
import PhotoBanner from '@activity/components/photo-upload/photo_banner'
import PhotoActivity from '@activity/components/photo-upload/photo_activity'
import tpl_special_multi_language_input from '@activity/components/tpl_special_multi_language_input/index.vue'
import tpl_activity_map from '@activity/components/tpl_activity_map'
import Blocklist from '@activity/components/activity_blocklist'
import StrongTips from '@activity/components/StrongTips.vue'
import VideoUpload from '@activity/pages/activityManagement/basicInfo/videoUpload/index.vue'
import renderItem from '@activity/pages/activityManagement/basicInfo/renderItem.js'
import ShimAntdTooltip from '@activity/components/shimAntdTooltip.vue'
import { cloneDeep } from 'lodash'
import UploadDesc from '@activity/pages/activityManagement/act-create/main/component/upload-desc/index.vue'
import MultipleLocations from '@activity/components/multiple-locations/index.vue'
import DepartureSelectCity from '@activity/components/language_destination/departure-select-city.vue'

import { getFlang2Blang, getPromiseInterceptor } from '@activity/utils/index.js'
import PopConfirmSwitch from '@activity/components/popconfirm-switch/index.vue'
// OBT 出发地、目的地、POI三个字段分离
import ActivityPOI from '@activity/components/binded-area/activity-poi/index.vue'
import ActivityDeparture from '@activity/components/binded-area/activity-departure/index.vue'
import ActivityDestination from '@activity/components/binded-area/activity-destination/index.vue'
import { shouldIncludeUnpublishedCountryByAIDs } from '@activity/components/binded-area/const.js'

export default {
  inject: ['rootChangeLoading', 'page_from', 'setAlertFixed2provide', 'getFinishedStatus2provide'],
  props: {
    isGuideCreate: {
      type: Boolean,
      default: false
    },
    language: {
      type: String,
      default: ''
    },
    leafCategoryId: {
      type: Number,
      default: undefined
    },
    packagesParams: {
      type: Array,
      default: () => []
    },
    obtConfig: {
      type: Object,
      default: () => {}
    }
  },
  provide() {
    return {
      activityId: this.activity_id,
      curLang: getEditLang(),
      refLang: getRefLang(),
      isEMpage: this.isEMpage
    }
  },
  components: {
    UploadDesc,
    ShimAntdTooltip,
    VideoUpload,
    language_destination,
    binded_area,
    tpl_special_multi_language_input,
    tpl_activity_map,
    Blocklist,
    PhotoBanner,
    PhotoActivity,
    renderItem,
    StrongTips,
    PopConfirmSwitch,
    ActivityPOI,
    ActivityDeparture,
    ActivityDestination,
    MultipleLocations,
    DepartureSelectCity
  },
  data() {
    return {
      photoBannerList: [],
      photoActivityList: [],
      cacheObj: {
        isUnpublishId: false,
        isInitUnpublish: false,
        tipsType: '',
        isShow: true,
        destinationInfosNodes: undefined,
        previous_country_previous_city: '',
        new_country_new_city: ''
      },
      isNewFlowFlag: false,
      initLoadFinish: false,
      form: {
        affiliate: 0,
        viewable_platform: 0,
        best_price_guarantee: 0,
        need_default_select_package: 0,
        language: klook.urlParam('lang')
      },

      schemaConfig: {},

      all_banners_desktop: [],
      trigger_banner_check: 0,
      allCountryOptions: [],
      destinationCountry: [],
      fieldVersion: {
        activity_location: undefined,
        departure_country_city: undefined
      }
    }
  },
  computed: {
    ...mapGetters(['isPublishWithAI']),
    ...mapState(['isMerchant', 'merchantActHaveBeenApproved']),
    MULTI_LANG_INPUT() {
      const list = ['activity_title', 'activity_subtitle', 'act_description']
      if (this.isActivityLocationV2) {
        return list
      }
      return [...list, 'activity_address']
    },
    isActivityLocationV2() {
      return this.fieldVersion.activity_location === 'V2'
    },
    isDepartureCityV2() {
      return this.fieldVersion.departure_country_city === 'V2'
    },
    activity_id() {
      return klook.parse2id(this.$route.query.activity_id)
    },
    editLanguage() {
      return klook.urlParam('lang') || undefined
    },
    showCurLangOnly() {
      return ['admin', 'em'].includes(this.page_from) && this.editLanguage !== 'en_US' && !this.isMerchant
    },
    minBannerCount() {
      let len = 0
      if (_.get(this.schemaConfig, 'activity_picture.required', false)) {
        len = 1
      }

      if (this.page_from === 'merchant') {
        len = 3
      }

      return len
    },
    bannerQualityType() {
      return this.schemaConfig?.activity_banner?.banner_quality_type || 2
    },
    isEMpage() {
      return this.$root.roles.is_em
    },
    isCreated() {
      return !_.isEmpty(klook.urlParam('lang'))
    },
    refer_language() {
      let ref = this.$route.query.ref || 'en_US'
      let { language } = this.form
      return !language || language === ref ? '' : ref
    },
    banner_count() {
      return this.all_banners_desktop.length
    },
    videoUploadConf() {
      return {
        folder: 'activities',
        tags: [this.activity_id],
        cropping: true,
        croppingShowDimensions: true,
        croppingDefaultSelectionRatio: 1,
        croppingAspectRatio: 1160 / 460
      }
    },
    /*
      content params
      @param {boolean} [independent] - Itself is an independent ant-form-model
      @param {function} [getData] - when the getData function exists, it will be replace the this.form[field]
      @param {function} validator - get value(by getData() or this.form[field]) to verify, and return boolean, use to time line
      @param {object} [customSchema] - Setting up custom schema
      @param {string} [defaultLabel] - the label value of a-form-model-item
      @param {string} errorMsg - can be modified by setChunkContentData
      @param {string} tips - same as above
     */
    chunkPageConfig2merchant() {
      let result = [
        {
          title: this.getGuideTitle(),
          required: false,
          content: [
            {
              field: 'language',
              component: 'language_destination',
              ref: 'language_destination',
              independent: true,
              defaultLabel: __('83821'),
              validator: this.validatorCommon
            },
            {
              field: 'published_conditions',
              component: 'blocklist',
              ref: 'blocklist',
              independent: true,
              validator: () => {
                const { status, language_list } = this.$refs.blocklist[0].save()

                return status === 1 || language_list.length
              }
            },
            {
              field: 'activity_title',
              component: 'multi-language-input',
              independent: true,
              showSuffixCount: true,
              defaultLabel: __('83822'),
              validator: this.validatorMultiLangArray
            },
            {
              field: 'activity_subtitle',
              component: 'multi-language-input',
              independent: true,
              defaultLabel: __('act_activity_subtitle'),
              validator: this.validatorMultiLangArray
            },
            {
              field: 'viewable_platform',
              defaultLabel: __('49403'),
              component: 'popConfirmSwitch',
              validator: this.validatorCommon
            },
            {
              field: 'best_price_guarantee',
              defaultLabel: __('28444'),
              component: 'switch',
              validator: this.validatorCommon
            },
            {
              field: 'display_package_card',
              defaultLabel: __('30717'),
              component: 'switch',
              validator: this.validatorCommon
            },
            {
              field: 'remove_watermark',
              defaultLabel: __('28445'),
              component: 'switch',
              validator: this.validatorCommon
            }
          ].filter(
            (item) =>
              hasOwnProperty.call(this.schemaConfig, item.field) &&
              this.schemaConfig[item.field].access_permission !== 0
          ) // just return the fields with the account authorization.
        },
        {
          title: this.getGuideTitle(),
          required: false,
          content: [
            {
              field: 'departure_country_city',
              component: this.isDepartureCityV2 ? 'departure-select-city' : 'departure_country_city',
              independent: true
            },
            {
              field: 'destination_country_city',
              component: 'destination_country_city',
              independent: true
            },
            {
              field: 'activity_poi',
              component: 'activity_poi',
              independent: true
            },
            {
              field: 'destination_model_type',
              component: 'binded_area',
              independent: true,
              getData() {
                return (this.getRefs('binded_area').length && this.$refs.binded_area[0].form) || {}
              },
              validator: (data, schema) => {
                let { model } = schema
                let fields = ['area.country_id', 'area.city_id']

                if (model === 2) {
                  fields = [...fields, 'area.country_id_from', 'area.city_id_from']
                } else if (model === 3) {
                  fields = ['dest_ids']
                }

                return fields.every((field) => {
                  let value = _.get(data, field, null)
                  return Array.isArray(value) ? value.length : value
                })
              }
            },
            {
              isNewFlowFlag: false,
              field: 'activity_location',
              component: this.isActivityLocationV2 ? 'multiple-locations' : 'activity-map',
              validator: this.validatorActMap,
              defaultLabel: this.isActivityLocationV2 ? __('174971') : __('act_address'),
              customSchema: {}
            },
            {
              isNewFlowFlag: false,
              field: 'activity_address',
              component: 'multi-language-input',
              independent: true,
              defaultLabel: __('ob_act_addr'),
              validator: this.validatorMultiLangArray
            }
          ].filter(
            (item) =>
              hasOwnProperty.call(this.schemaConfig, item.field) &&
              this.schemaConfig[item.field].access_permission !== 0
          ) // just return the fields with the account authorization.
        },
        {
          title: this.getGuideTitle(),
          checkRequired: true,
          required: false,
          content: [
            {
              field: 'activity_highlights',
              component: 'multi-language-input',
              independent: false,
              validator: this.validatorMultiLangArray,
              type: 'markdown',
              showSuffixCount: true,
              className: 'big-markdown',
              customSchema: {
                mdConfig: {
                  toolbar: ['unordered-list']
                },
                label: '',
                placeholder: __('83939').replace(/(<br\/>)+/g, '\n')
              }
            },
            {
              isNewFlowFlag: false,
              field: 'act_description',
              component: 'multi-language-input',
              independent: true,
              validator: this.validatorMultiLangArray,
              type: 'markdown',
              showSuffixCount: true,
              className: 'big-markdown',
              customSchema: {
                mdConfig: {
                  toolbar: ['bold', 'unordered-list', 'preview']
                },
                label: '',
                placeholder: __('85884')
              }
            }
          ].filter(
            (item) =>
              hasOwnProperty.call(this.schemaConfig, item.field) &&
              this.schemaConfig[item.field].access_permission !== 0
          ) // just return the fields with the account authorization.
        }
      ]
      result.forEach((obj) => {
        obj.content = obj.content.filter((o) => {
          return typeof o.isNewFlowFlag !== 'boolean' || this.isNewFlowFlag === o.isNewFlowFlag // isNewFlowFlag渲染逻辑
        })
        if (obj.checkRequired && !obj.required) {
          obj.required = obj.content.some((o) => {
            return _.get(this.schemaConfig, `${o.field}.required`, false)
          })
        }
      })

      const photoVideo = {
        title: __('83900'),
        required: false,
        customMode: true, // custom or form
        content: []
      }
      if (_.get(this.schemaConfig, 'activity_banner.access_permission', 0)) {
        photoVideo.required =
          photoVideo.required || _.get(this.schemaConfig, 'activity_banner.required', false)
        photoVideo.content.push({
          field: 'activity_banner',
          component: 'PhotoBanner',
          independent: true,
          validator: this.validatorBanner,
          getData() {
            return this.getPhotoList('photo_banner')
          }
        })
      }
      if (_.get(this.schemaConfig, 'activity_picture.access_permission', 0)) {
        photoVideo.required =
          photoVideo.required || _.get(this.schemaConfig, 'activity_picture.required', false)
        photoVideo.content.push({
          field: 'activity_picture',
          component: 'PhotoActivity',
          independent: true,
          validator: this.validatorActPhoto,
          getData() {
            return this.getPhotoList('photo_activity')
          }
        })
      }
      if (_.get(this.schemaConfig, 'activity_video.access_permission', 0)) {
        photoVideo.required =
          photoVideo.required || _.get(this.schemaConfig, 'activity_video.required', false)
        photoVideo.content.push({
          field: 'activity_video',
          component: 'video-upload',
          ref: 'video',
          independent: true,
          validator: this.validatorCommon
        })
      }
      photoVideo.required = false

      result.push(photoVideo)

      return result.filter((o) => o.content?.length)
    },
    //
    chunkPageResult() {
      const arr = this.chunkPageConfig2merchant
      return arr.map((chunk) => {
        return {
          ...chunk,
          content: chunk.content.map((content) => {
            let validator = content.validator
            let schema = this.schemaConfig[content.field]
            schema = {
              ...this.getCustomConf(schema, {
                defaultLabel: content.defaultLabel || ''
              }), // label, validate...
              ...schema,
              ...content.customSchema
            }

            return {
              ...content,
              tips: '',
              errorMsg: '',
              schema: {
                ...schema,
                hideTooltip: true,
                rules: {
                  required: schema.required,
                  trigger: 'blur,change',
                  validator: (rule, value, callback) => {
                    // the validator of form item
                    if (
                      !rule.required || // no-required
                      (validator && validator(value, schema)) ||
                      !validator
                    ) {
                      // no-validator
                      callback()
                    } else {
                      callback(new Error('Please fill in this field.'))
                    }
                  }
                }
              }
            }
          })
        }
      })
    },

    vDisabled() {
      const lock = this.merchantActHaveBeenApproved

      if (lock) {
        this.setAlertFixed2provide({
          show: true,
          html: __('48231')
        })
      }

      return {
        lock,
        defaultScope: 'freeText'
      }
    }
  },
  watch: {
    language: {
      handler(v) {
        this.$set(this.form, 'language', v)
      }
    },
    'cacheObj.isUnpublishId'(val) {
      this.setGsaveBtnDisabled()
    },
    'cacheObj.tipsType'(val) {
      this.setGsaveBtnDisabled()
    },
    all_banners_desktop() {
      this.initFloorTimelineData()
    },
    form: {
      handler() {
        this.initFloorTimelineData()
      },
      deep: true
    },
    'form.activity_location.location': {
      handler(val, oldVal) {
        if (!this.isActivityLocationV2) return

        if (val && val !== oldVal && this.isNewFlowFlag) {
          this.lickEq2str(val, oldVal || this.cacheObj.location) || this.debounceQueryCity(val)
        }
      },
      deep: true
    }
  },
  async created() {
    klook.bus.$emit('addHandleSave2bus', this.handleSave, { stepCount: this.$route.query.step_count })
    window.clickHandle2native = (type) => {
      let target = null
      switch (type) {
        case 'click_here':
          target = _.get(this.$refs, 'binded_area[0].$refs.region_binding.applyHandle', false)
          target && target()
          break
        case 'click_modify':
          target = document.querySelector('.pos-gps-location')
          klook.scrollElError(target, { block: 'start' })
          target.focus()
          klook.setScrollTop(klook.getScrollTop() - 120)
          break
      }
    }
    await this.initObConfig()
    await this.initBannerInfo()
    this.initLoadFinish = true
  },
  async mounted() {
    await this.$nextTick()

    // access_permission: 0, 不可看; 1, read only; 2, editable
    // 当是 merchant update 的时候, 有只读字段需要提示
    if (
      this.isMerchant &&
      this.isCreated &&
      Object.values(this.schemaConfig).some((item) => item.access_permission === 1)
    ) {
      this.setAlertFixed2provide({
        show: true,
        html: __('48231')
      })
    }
    await this.getCountryOptions()
    this.$emit('guideMounted')
  },
  beforeRouteLeave(to, from, next) {
    this.$root.gsaveBtnDisabled = false
    this.setAlertFixed2provide({
      show: false
    })
    next()
  },
  methods: {
    ...mapActions(['getActLang', 'updateActStepStatus2action']),
    // activity_banner 和 activity_picture 字段共用 picture_list tips
    // captions 填写部分特殊处理，与 picture_captions 对应
    // 关联代码 src/modules/activity/utils/fieldGuideTipsDict.js
    handleDestinationChange(data) {
      const { country_id } = data
      if (!country_id) {
        this.destinationCountry = []
        return
      }
      this.destinationCountry = Array.isArray(country_id) ? country_id : [country_id]
    },
    async getCountryOptions() {
      const res = await ajax.get(ADMIN_API.act.get_all_countries, {
        params: {
          // 7, 8, 9, 206, 331 wifi/sim
          include_not_show_country:
            [7, 8, 9, 206, 331].includes(+this.subCategoryId) ||
            shouldIncludeUnpublishedCountryByAIDs.includes(+this.activity_id)
              ? 1
              : 0 // 0 不包含 Unpublished 国家
        }
      })
      this.allCountryOptions = res || []
    },
    getDatasetField({ field }) {
      return ['activity_banner', 'activity_picture'].includes(field) ? 'picture_list' : field
    },
    getFlang2Blang(lang) {
      return getFlang2Blang(lang) || undefined
    },
    getGuideTitle() {
      const obj = {
        0: __('activity_basic_page_basic_info'),
        1: __('83826'),
        2: __('83900'),
        3: __('83833'),
        4: __('83834')
      }
      return obj[this.$attrs.stepCount] || ''
    },
    getFreetextDisabled(schema, field) {
      const excludeFields = ['activity_title']
      if (excludeFields.includes(field)) {
        return schema
      }
      return schema || this.isPublishWithAI
    },
    getCurrentLocal: klook.getCurrentLocal,
    setGsaveBtnDisabled() {
      let { isUnpublishId, tipsType } = this.cacheObj
      if (!this.isNewFlowFlag || isUnpublishId) {
        this.$root.gsaveBtnDisabled = false
      } else {
        this.$root.gsaveBtnDisabled = tipsType === 'initNoCity'
      }
    },
    lickEq2str(val, oldVal) {
      if (!val || !oldVal) return false
      let arr = val.split(',').map((v) => parseInt(v * 100))
      let oldArr = oldVal.split(',').map((v) => parseInt(v * 100))
      return arr[0] === oldArr[0] && arr[1] === oldArr[1]
    },
    onceCheckLocation(res) {
      // initCityNeq 历史数据清洗，城市不一致
      // initNoCity 历史数据清洗，城市不存在时字段下方的提示文案 has_calculate_result=false
      let { has_calculate_result, binded_area_info } = res
      let { city = {} } = this.cacheObj
      if (has_calculate_result === false) {
        this.cacheObj.tipsType = 'initNoCity'
        this.setAlertFixed2provide({
          show: true,
          html: klook.parseStr1(__('30442'), {
            // 历史数据清洗，城市不存在时页面顶部的提示文案
            create_a_new_city: `<a href="javascript:window.clickHandle2native('click_here')" class="common-link">${__(
              '30443'
            )}</a>`,
            modify_the_location_coordinates: `<a href="javascript:window.clickHandle2native('click_modify')" class="common-link">${__(
              '30444'
            )}</a>`
          })
        })
      } else if (has_calculate_result === true) {
        let { city: newCity } = binded_area_info || {}
        if (city.area_id !== newCity.area_id) {
          this.cacheObj.destinationInfosNodes = klook.parseStr1(__('30440'), {
            city: `${city.area_id} - ${city.area_name}`
          })
          this.setAlertFixed2provide({
            show: true,
            html: this.cacheObj.destinationInfosNodes
          })
          this.cacheObj.tipsType = 'initCityNeq'
        } else {
          this.checkCityPage(res)
        }
      }
    },
    getCountryCity() {
      let { area = {}, cityOptions = [], countryOptions = [] } = this.$refs.binded_area[0].getAreaFormData()
      let prevCity = cityOptions.find((o) => o.id === area.city_id) || {}
      let prevCountry = countryOptions.find((o) => o.id === area.country_id) || {}
      return {
        city: prevCity,
        country: prevCountry,
        text: `${prevCity.id || ''} - ${prevCity.name || ''}`
      }
    },
    async get2calculate_area_info_by_location(location) {
      return ajax.get(
        {
          url: ADMIN_API.act.calculate_area_info_by_location,
          params: {
            latlng: location
          }
        },
        {
          loading: true
        }
      )
    },
    checkCityPage(res) {
      if (klook.getType(res.destination_info) === 'Array') {
        this.cacheObj.destinationInfosNodes = klook.parseStr1(__('30438'), {
          Page_ID_Page_Name_publish_status: res.destination_info
            .map((o) => {
              return `${o.destination_id}-${o.destination_name} (${
                o.publish_status ? __('global_published') : __('global_unpublished')
              })`
            })
            .join(', ')
        })
        this.cacheObj.tipsType = 'queryCityPageSuc'
      } else {
        this.cacheObj.tipsType = 'queryCityPageErr'
      }
    },
    debounceQueryCity: _.debounce(async function (location) {
      let res = await this.get2calculate_area_info_by_location(location)
      if (klook.getType(res) !== 'Object') return
      this.cacheObj.tipsType = ''
      this.setAlertFixed2provide({
        show: false
      })
      this.showChangeLocationTip(res)
      this.updateFormData(res.binded_area_info)
      if (this.cacheObj.tipsType) return
      // queryCityPageErr 没有查询到关联的城市页
      // queryCityPageSuc 查询到关联的城市页
      // queryNoCity 无法推算出城市的提示文案
      if (res.has_calculate_result) {
        this.checkCityPage(res)
      } else {
        this.cacheObj.tipsType = 'queryNoCity'
      }
    }, 300),
    updateFormData(binded_area_info) {
      let { city, country } = binded_area_info || {}
      let { destination_model_type: dmt } = this.form
      if (dmt && dmt.binded_area_info) {
        let obj = {
          ...city,
          city,
          country
        }
        if (klook.getType(dmt.binded_area_info) === 'Object') {
          this.$set(dmt.binded_area_info.area_items, 0, obj)
          this.cacheObj.isShow = false
          this.$nextTick(() => {
            this.cacheObj.isShow = true
          })
        }
      }
    },
    showChangeLocationTip(res) {
      let { previous_country_previous_city, new_country_new_city } = this.cacheObj
      this.cacheObj.previous_country_previous_city = previous_country_previous_city =
        this.getCountryCity().text
      let { city = {} } = res.binded_area_info || {}
      this.cacheObj.new_country_new_city = new_country_new_city = `${city.area_id || ''} - ${
        city.area_name || ''
      }`
      if (previous_country_previous_city === new_country_new_city) return
      let content = (h) => (
        <span>
          {klook.parseStr1(__('30435'), {
            previous_country_previous_city,
            new_country_new_city
          })}
        </span>
      )
      this.modalInfo2self && this.modalInfo2self.destroy()
      this.modalInfo2self = this.$info({
        content: content //输入经纬度后导致城市发生变更的提示toast
      })
    },
    initFloorTimelineData: _.debounce(function () {
      let list = this.chunkPageResult.map((chunk) => {
        let count = chunk.content.reduce(
          (acc, curr) => {
            acc.count++
            if (curr.schema.required) {
              acc.requireCount++
            }
            let data = typeof curr.getData === 'function' ? curr.getData.call(this) : this.form[curr.field]
            acc.data.push({
              field: curr.field,
              value: data
            })
            if ((curr.validator && curr.validator(data, curr.schema)) || !curr.validator) {
              acc.num++
              if (curr.schema.required) {
                acc.requireNum++
              }
            } else {
              acc.notFilled.push({
                field: curr.field,
                required: curr.schema.required
              })
            }

            return acc
          },
          {
            count: 0,
            num: 0,
            requireCount: 0,
            requireNum: 0,
            notFilled: [], // location no data completed
            data: [] // all data
          }
        )
        return {
          title: chunk.title,
          ...count
        }
      })

      // klook.bus.$emit('floorTimeline2bus', {
      //   floorData: {
      //     list
      //   }
      // })
      const isb = list.every((o) => o.requireNum >= o.requireCount)
      this.$emit('updateFinished', isb)
      return list
    }, 300),
    setChunkContentData(field, data) {
      if (toString.call(data) === '[object Object]') {
        for (let chunk of this.chunkPageResult) {
          for (let content of chunk.content) {
            if (content.field === field) {
              Object.assign(content, data)
              this.$forceUpdate()
              return content
            }
          }
        }
      }
    },
    initPackageParams(list, language = 'en_US') {
      const arr = cloneDeep(list || [])
      arr.forEach((pkg) => {
        pkg.skus?.forEach((sku) => {
          sku.required = Number(sku.required)
          sku.use_customized_name = Number(sku.use_customized_name)
          sku.local = (sku.local || []).map((local) => ({
            ...local,
            language
          }))
        })
      })
      return arr
    },
    async handleSave() {
      if (this.activity_id && !Number(this.$route.query.step_count)) {
        klook.bus.$emit('setStepStatusFinished2bus', 0, {
          activity_id: this.activity_id,
          language: this.language
        })
        return true
      }
      this.setChunkContentData('activity_picture', { errorMsg: '' })

      let modify_id = klook.getModifyId()
      // am 才能设置屏蔽语言
      if (hasOwnProperty.call(this.schemaConfig, 'published_conditions')) {
        this.getRefs('blocklist').length && (await this.$refs.blocklist[0].remind())
      }
      this.rootChangeLoading(true)
      if (!this.validateBannerCount()) {
        this.rootChangeLoading(false)
        return
      }
      if (await this.getValidateFromStatus()) {
        if (hasOwnProperty.call(this.schemaConfig, 'activity_picture')) {
          let len = this.minBannerCount
          let maxLen = this.$refs.photo_activity[0].limit
          const photo_activity = this.getPhotoList('photo_activity')
          if ((len && photo_activity.length < len) || photo_activity.length > maxLen) {
            let content =
              photo_activity.length < len
                ? __('upload_4_photos_at_least').replace(/\d{1}/, len)
                : klook.parseStr1(this.$t('28992'), { 20: maxLen })
            this.setChunkContentData('activity_picture', {
              errorMsg: content
            })
            this.$message.error({
              content
            })
            this.rootChangeLoading(false)
            klook.scrollElError()
            return
          }
        }

        let data = cloneDeep(this.form)

        if (hasOwnProperty.call(this.schemaConfig, 'activity_banner')) {
          data.activity_banner = this.getPhotoFormatParams('photo_banner')
        }
        if (hasOwnProperty.call(this.schemaConfig, 'activity_picture')) {
          data.activity_picture = this.getPhotoFormatParams('photo_activity')
        }

        const pictures = [...(data.activity_banner || []), ...(data.activity_picture || [])]

        if (pictures.length) {
          let isValid = pictures
            .filter((pic) => {
              const isEn = ['en_US', 'ALL'].includes(pic.language_type)

              return data.language === 'en_US' ? isEn : !isEn // 英语的话需要取 'en_US' 或 'ALL'，其他语言则取相对应语言图片
            })
            .some((pic) => pic.image.some((img) => img.is_card))
          if (!isValid) {
            this.$message.error(this.$t('75803'))
            this.rootChangeLoading(false)
            return
          }
        }

        if (this.$refs.binded_area) {
          data.destination_model_type = this.$refs.binded_area[0].getAreaData()
        }
        if (this.$refs.departure_country_city) {
          data.departure_country_city = this.$refs.departure_country_city[0].getAreaData()
        }

        if (this.$refs.destination_country_city) {
          data.destination_country_city = this.$refs.destination_country_city[0].getAreaData()
        }

        if (this.$refs.activity_poi) {
          data.activity_poi = this.$refs.activity_poi[0].getAreaData()
        }
        if (this.$refs.blocklist) {
          data.published_conditions = this.$refs.blocklist[0].save() || {}
        }
        data = Object.keys(data).reduce(
          (acc, curr) => [
            ...acc,
            {
              field_key: curr,
              value: data[curr]
            }
          ],
          []
        )
        if (!(await this.getFinishedStatus2provide())) {
          this.rootChangeLoading(false)
          return
        }

        let resp = [
          ...(await Promise.all([
            (async () => {
              if (this.isGuideCreate) {
                let res = await ajax.postBody(ADMIN_API.act.postGuideCreate, {
                  noDefaultResponseInterceptor: true, // 获取error.message
                  data: {
                    packages: this.initPackageParams(this.packagesParams, this.form.language),
                    field_items: data,
                    page_from: klook.getPlatformRoleKey(),
                    leaf_category_id: this.leafCategoryId,
                    modify_id,
                    language: this.form.language
                  }
                })
                klook.bus.$emit('sendGuideCreateSave2bus', this.calcCurrStepData, res)
                if (res.success) {
                  // klook.bus.$emit('initQueryData2bus')
                  // this.$refs.blocklist && this.$refs.blocklist[0].saveSucCallback(res.success) // 回写数据
                  const interceptor = getPromiseInterceptor()
                  klook.bus.$emit('successNextHandler2bus', 0, res.result, () => {
                    interceptor.done()
                  })
                  await interceptor // 同步等待emit事件
                }

                return res.success
              } else {
                const field_items = data
                  .filter((o) => o.field_key !== 'language')
                  .map((curr) => {
                    if (this.isActivityLocationV2 && curr.field_key === 'activity_location') {
                      curr.value = curr.value.map((item) => {
                        // backend - HTML tags are not supported in the text
                        // eslint-disable-next-line no-unused-vars
                        const { path, path_sep, ...other } = item

                        return other
                      })
                    }

                    return curr
                  })

                const postData = {
                  field_items,
                  page_from: klook.getPlatformRoleKey(),
                  activity_id: this.activity_id,
                  modify_id,
                  language: this.form.language
                }
                let res = await ajax.postBody(ADMIN_API.act.update_activity_basic_info, {
                  noDefaultResponseInterceptor: true,
                  data: postData
                })
                klook.bus.$emit('sendGuideCreateSave2bus', this.calcCurrStepData, res)
                if (res.success) {
                  klook.bus.$emit('initQueryData2bus')
                  this.$refs.blocklist && this.$refs.blocklist[0].saveSucCallback(res.success) // 回写数据
                  const interceptor = getPromiseInterceptor()
                  klook.bus.$emit(
                    'successNextHandler2bus',
                    Number(this.$route.query.step_count) || undefined,
                    postData,
                    () => {
                      interceptor.done()
                    }
                  )
                  await interceptor // 同步等待emit事件
                }

                return res.success
              }
            })()
          ]))
        ]
        if (resp.every((item) => item)) {
          this.rootChangeLoading(false)
          this.$message.success(this.$t('global_success'))
          this.setAlertFixed2provide({
            show: false
          })
        } else {
          this.rootChangeLoading(false)
          return
        }
      } else {
        this.rootChangeLoading(false)
        this.$message.warn(__('package_error_mandatory'))
        klook.scrollElError(document.querySelector('.common-has-error'))
        return false
      }
      this.rootChangeLoading(false)
      return true
    },
    getRefs(key) {
      let refs = this.$refs[key]
      if (!refs) {
        return []
      }
      if (!Array.isArray(refs)) {
        refs = [refs]
      }

      return refs
    },
    getPhotoList(key) {
      return _.flatten(this.getRefs(key).map((item) => item.getList()))
    },
    getPhotoFormatParams(key) {
      return _.flatten(this.getRefs(key).map((item) => item.getFormatParams()))
    },
    // 获取所有 banner desktop
    getAllBannersDesktop() {
      const photo_banner = this.getPhotoList('photo_banner')
      const photo_activity = this.getPhotoList('photo_activity')
      let banner_desktops =
        photo_banner.map((v) => {
          return {
            ...v.DESKTOP,
            typeFlag: 'banner'
          }
        }) || []
      const banner_in_activity_images = photo_activity.filter((v) => v.banner_display)
      banner_desktops = [
        ...banner_desktops,
        ...banner_in_activity_images.map((v) => {
          return {
            ...v.DESKTOP,
            typeFlag: 'activity'
          }
        })
      ]

      return banner_desktops
    },
    async initBannerInfo() {
      let promise_arr = []
      if (hasOwnProperty.call(this.schemaConfig, 'activity_banner')) {
        promise_arr.concat(this.getRefs('photo_banner').map((item) => item.$nextTick((_) => true)))
      }
      if (hasOwnProperty.call(this.schemaConfig, 'images')) {
        promise_arr.concat(this.getRefs('photo_activity').map((item) => item.$nextTick((_) => true)))
      }
      await Promise.all(promise_arr)
      this.all_banners_desktop = this.getAllBannersDesktop()
    },
    initObConfig: _.debounce(async function () {
      let { field_items = [], is_calculate_location_act = false, field_version } = this.obtConfig || {}
      Object.assign(this.fieldVersion, field_version || {})
      this.$root.isNewFlowFlag = this.isNewFlowFlag = is_calculate_location_act
      let { result, schemaConfig } = field_items.reduce(
        (acc, curr) => {
          let { field_key, value, ...schema } = curr

          // fmt data
          let config = (schema.config && JSON.parse(schema.config)) || {}
          schema.attr_id = field_key
          schema.config = config

          return {
            result: {
              ...acc.result,
              [field_key]: value
            },
            schemaConfig: {
              ...acc.schemaConfig,
              [field_key]: {
                ...schema, // by backend
                ...config // obt
              }
            }
          }
        },
        {
          result: {},
          schemaConfig: {}
        }
      )
      if (this.$root.isNewFlowFlag && !this.isActivityLocationV2) {
        await this.initLocationFn(result)
      }
      // Fix priority: prop > custom select > schema result
      result.language = this.language || this.form.language || result.language
      this.form = result
      this.schemaConfig = schemaConfig

      const switchFields = ['viewable_platform', 'best_price_guarantee', 'need_default_select_package']
      switchFields.forEach((field) => {
        if (hasOwnProperty.call(result, field)) {
          let value = result[field]
          this.form[field] = [0, 1].includes(value)
            ? value
            : _.get(schemaConfig, `${field}.config.default_choices`, 0)
        }
      })

      this.cacheForm = _.cloneDeep(this.form)
    }, 50),
    async initLocationFn(formData) {
      if (klook.getType(formData) !== 'Object') return
      let location = _.get(formData, 'activity_location.location', '')
      if (!location) return
      this.cacheObj.location = location
      let obj = _.get(formData, 'destination_model_type.binded_area_info.area_items[0]', {})
      let { city = {}, country = {} } = obj
      this.cacheObj.city = city
      this.cacheObj.country = country
      if (country.area_id === 1017) {
        this.cacheObj.isInitUnpublish = true
        return
      }
      let res = await this.get2calculate_area_info_by_location(location)
      if (klook.getType(res) !== 'Object') return
      this.onceCheckLocation(res)
      let { binded_area_info } = res
      let { city: newCity = {}, country: newCountry = {} } = binded_area_info || {}
      if (newCity.area_id) {
        // 覆盖
        obj.city = newCity
        obj.country = newCountry
      }
    },
    getMultiLangData(data, { langField = 'language', valField = 'value', defaultVal = 'please input' } = {}) {
      let current = _.find(data, {
        [langField]: lang_conf.getLangObj('F_LANG', 'B_LANG')[KLK_LANG]
      })

      return (current && current[valField]) || defaultVal
    },
    // 根据OB 的字段，生成
    getCustomConf(data = {}, { config = {}, defaultLabel = '' } = {}) {
      let common = {
        disabled: data.access_permission === 1,
        valField: data.attr_id,
        label: defaultLabel || this.getMultiLangData(_.get(data, 'config.name_multilang', {})),
        // description: this.getMultiLangData(_.get(data, 'config.desc_multilang', {})),
        // strongTips: this.getMultiLangData(_.get(data, 'config.obvious_multilang', {}), {
        //   defaultVal: ''
        // }),
        required: data.required,
        ...config
      }

      if (this.MULTI_LANG_INPUT.includes(data.attr_id)) {
        return {
          ...common,
          requiredEn: data.attr_id === 'activity_title'
        }
      } else if (['activity_banner', 'activity_picture'].includes(data.attr_id)) {
        return common
      } else {
        return common
      }
    },
    getRequired(field) {
      return (this.schemaConfig[field] && this.schemaConfig[field].required) || false
    },
    handleMapChange({ location, place_id, location_wgs84, name }) {
      this.form.activity_location = {
        location,
        place_id,
        location_wgs84
      }
      const arr = this.form.activity_address
      if (arr?.length) {
        const item = arr.find((o) => o.language === this.language)
        item && (item.name = name)
      }
    },
    async publishedLanguage(v) {
      if (!this.isCreated) {
        // 仅在创建的时候才能更改
        this.form.language = Array.isArray(v) ? v[0] : v
        await this.initObConfig()
      }
    },

    handleBannerCountChange(fieldKey, refKey) {
      // 当没有勾选 set as card 的时候，上传 banner 的时候需要自动勾选
      if (refKey === 'photo_banner') {
        const list = document.querySelectorAll('.set_as_card.ant-radio-wrapper-checked')
        if (!list.length) {
          this.$nextTick(() => {
            document.querySelector('.activity_banner .set_as_card')?.click?.()
          })
        }
      }
      this.validateBannerCount()
      this.photoBannerList = this.getPhotoList('photo_banner')
      this.photoActivityList = this.getPhotoList('photo_activity')
      // set the value
      // this.form[fieldKey] = this.$refs[refKey][0].getFormatParams()
    },
    // 转换图片的pre_process: c_crop,h_750,w_1891,x_494,y_250/
    getCropObj(pre_process) {
      let str = pre_process.replace('/', '').replace(/_/g, '":"').replace(/,/g, '","')
      return JSON.parse(`{"${str}"}`)
    },
    // 检测 banner 数量以及比例是否匹配
    validateBannerCount() {
      this.all_banners_desktop = this.getAllBannersDesktop()
      const photo_banner = this.getPhotoList('photo_banner')
      const photo_activity = this.getPhotoList('photo_activity')
      const all_photo = [...photo_banner.map((v) => v.DESKTOP), ...photo_activity.map((v) => v.DESKTOP)]
      // banner 数量大于 1
      if (this.banner_count > 1) {
        // 如果有 banner 图片的比例是 1160:460, 提示全部重裁
        let unvalidate_item = all_photo.find((v) => {
          if (!v.pre_process) {
            return false
          } else if (v.pre_process.includes('1160:460')) {
            return true
          } else {
            let obj = this.getCropObj(v.pre_process)
            return +(obj.w / obj.h).toFixed(1) === 2.5
          }
        })
        if (unvalidate_item) {
          this.$info({
            content: __('act_photo_add_banner'),
            title: __('global_confirm'),
            type: 'info'
          })
          this.trigger_banner_check += 1
          return false
        }
      } else {
        // 如果有 banner 图片的比例是 960:460, 提示全部重裁
        let unvalidate_item = all_photo.find((v) => {
          if (!v.pre_process) {
            return false
          } else if (v.pre_process.includes('960:460')) {
            return true
          } else {
            let obj = this.getCropObj(v.pre_process)
            return +(obj.w / obj.h).toFixed(1) === 2.1
          }
        })
        if (unvalidate_item) {
          this.$info({
            content: this.$t('act_photo_less_banner'),
            title: __('global_confirm'),
            type: 'info'
          })
          this.trigger_banner_check += 1
          return false
        }
      }
      return true
    },
    async validateForm() {
      return (
        await Promise.all(
          this.getRefs('form').map(
            (form) => new Promise((resolve) => form.validate((valid) => resolve(valid)))
          )
        )
      ).every((item) => item)
    },
    async getCompnentsValidate(compName) {
      const comp = this.getRefs(compName)
      if (comp.length) {
        return await comp[0].validateForm()
      }
      return true
    },
    async getValidateFromStatus() {
      let result = await Promise.all([
        (async () => {
          return await this.validateForm()
        })(),
        (async () => {
          if (this.getRefs('binded_area').length) {
            return await this.$refs.binded_area[0].validateForm()
          }
          return true
        })(),
        this.getCompnentsValidate('departure_country_city'),
        this.getCompnentsValidate('departureSelectCity'), // departure_country_city v2
        this.getCompnentsValidate('destination_country_city'),
        ...this.MULTI_LANG_INPUT.reduce((acc, curr) => {
          let refs = this.getRefs(curr)
          // 0 不可看
          // 1 只读
          // 2 // 可读写
          if (_.get(this, `schemaConfig[${curr}].access_permission`, 0) === 2 && refs.length) {
            return [...acc, ...refs.map((item) => item.validateForm())]
          }
          return acc
        }, []),
        (async () => {
          // 校验 banner / activity 图片
          const photo_banner = this.getRefs('photo_banner').map((item) => item.validate())
          const photo_activity = this.getRefs('photo_activity').map((item) => item.validate())
          // let minCount = photo_activity.getList().length >= 3
          const validate_pass = await Promise.all([...photo_banner, ...photo_activity])
          return !validate_pass.includes(false)
        })()
      ])
      return result.every((item) => item)
    },
    //
    setChangeVal(value, field) {
      this.form[field] = value
    },
    videoUploadCb(result) {
      if (result[0].width / result[0].height !== 16 / 9) {
        // the ending klook part is 16:9 so video ratio should also be 16:9
        this.$message.warn('Please upload video with ratio of 16:9 aspect ratio')
        klook.abort()
      }
    },
    // chunk content's validator
    validatorCommon(data) {
      return data !== '' && data !== undefined
    },
    validatorMultiLangArray(data, schema) {
      return data.every((item, index) => {
        if (index === 1) {
          // ref
          return item.language === 'en_US' && schema.requiredEn ? item.name : true
        }
        return item.name
      })
    },
    validatorActMap(data) {
      if (this.isActivityLocationV2) {
        return !!data?.length
      }
      return data.location
    },
    validatorBanner(photo) {
      return photo.length || undefined
    },
    validatorActPhoto(photo) {
      return photo.length >= this.minBannerCount || undefined
    }
  }
}
</script>

<style lang="scss">
#basic-info-editable {
  // fix ant-form-model validator bug
  .has-error .ant-input:disabled,
  .has-error .ant-input:disabled:hover {
    border-color: rgb(217, 217, 217);
  }
}
</style>
