<template>
  <div id="basic-info-editable" class="basic-info">
    <div class="basic-info-box" v-disabled="vDisabled">
      <div v-if="!initLoadFinish" class="common-spin-box"><a-spin /></div>
      <div v-else>
        <div v-for="(chunk, i) in chunkPageResult" :key="chunk.title + i" class="common-guide-basic-box">
          <h2 v-if="chunk.content && chunk.content.length" :id="chunk.title">
            <i v-if="chunk.required" class="common-required-star">*</i>
            {{ chunk.title }}
          </h2>

          <component
            :is="chunk.customMode ? 'div' : 'a-form-model'"
            :ref="chunk.customMode ? 'customMode' : 'form'"
            :model="form"
            @submit.prevent
          >
            <div
              v-for="item in chunk.content"
              :key="item.field"
              class="common-chunk-item js-dataset-field"
              :data-field="getDatasetField(item)"
              :class="item.className ? item.className : ''"
            >
              <component
                :is="item.independent ? 'div' : 'a-form-model-item'"
                class="basic-form-item"
                :colon="false"
                :prop="item.field"
                :label="item.schema.label"
                :rules="item.schema.rules"
              >
                <language_destination
                  v-if="item.component === 'language_destination'"
                  ref="language_destination"
                  :schema-config="item.schema"
                  :activity_id="+activity_id"
                  :required="getRequired(item.field)"
                  :result="form"
                  :disabled="item.schema.disabled"
                  v-bind="item.schema"
                  @published_language="publishedLanguage"
                >
                </language_destination>

                <blocklist
                  v-else-if="item.component === 'blocklist'"
                  ref="blocklist"
                  :result="form"
                  :required="getRequired(item.field)"
                  :disabled="item.schema.disabled"
                  :activity-id="+activity_id"
                  :schema-config="item.schema"
                  v-bind="item.schema"
                />

                <tpl_special_multi_language_input
                  v-else-if="item.component === 'multi-language-input'"
                  v-model="form[item.field]"
                  class="large-input"
                  valField="name"
                  :ref="item.field"
                  :type="item.type"
                  :required="getRequired(item.field)"
                  :current-create-lang="form.language"
                  :disabled="getFreetextDisabled(item.schema.disabled, item.field)"
                  :showSuffixCount="item.showSuffixCount"
                  :data-field="item.field"
                  v-bind="item.schema"
                />

                <ActivityDeparture
                  v-else-if="item.component === 'departure_country_city'"
                  ref="departure_country_city"
                  :area-data="form[item.field]"
                  :schema-config="item.schema"
                  :disabled="item.schema.disabled"
                  :country-options="allCountryOptions"
                />

                <!-- departure_country_city v2 版本 -->
                <DepartureSelectCity
                  v-else-if="item.component === 'departure-select-city'"
                  ref="departureSelectCity"
                  v-model="form[item.field]"
                  :schema-config="item.schema"
                  :required="getRequired(item.field)"
                  :disabled="item.schema.disabled"
                />

                <ActivityDestination
                  v-else-if="item.component === 'destination_country_city'"
                  ref="destination_country_city"
                  :area-data="form[item.field]"
                  :schema-config="item.schema"
                  :disabled="item.schema.disabled"
                  :country-options="allCountryOptions"
                  @change="handleDestinationChange"
                />

                <ActivityPOI
                  v-else-if="item.component === 'activity_poi'"
                  ref="activity_poi"
                  :poi-data="form[item.field]"
                  :schema-config="item.schema"
                  :disabled="item.schema.disabled"
                  :activity-id="+activity_id"
                />

                <binded_area
                  v-else-if="cacheObj.isShow && item.component === 'binded_area'"
                  ref="binded_area"
                  :schema-config="item.schema"
                  :result="form"
                  :activity_id="+activity_id"
                  :disabled="item.schema.disabled"
                  :tipsType="cacheObj.tipsType"
                  :destinationInfosNodes="cacheObj.destinationInfosNodes"
                  :countryLabel="$t('83827')"
                  :cityLabel="$t('83828')"
                  v-bind="item.schema"
                  @change="(data) => (cacheObj.isUnpublishId = data.isUnpublishId)"
                ></binded_area>

                <tpl_activity_map
                  v-else-if="item.component === 'activity-map'"
                  ref="activity_map"
                  :init="form[item.field] || null"
                  :disabled="item.schema.disabled"
                  :schema-config="item.schema"
                  v-bind="item.schema"
                  :language="getFlang2Blang(language)"
                  @change="handleMapChange"
                />

                <!-- activity_location -->
                <multiple-locations
                  v-else-if="item.component === 'multiple-locations'"
                  ref="multipleLocations"
                  v-model="form[item.field]"
                  :disabled="item.schema.disabled || vDisabled.lock"
                  :schema-config="item.schema"
                  :language="getFlang2Blang(language)"
                  v-bind="item.schema"
                />

                <div v-else>
                  <shim-antd-tooltip
                    placement="right"
                    :title="item.schema.description"
                    :overlay-style="{
                      maxWidth: '400px'
                    }"
                  >
                    <!-- fix ant-design -->
                    <template v-if="item.component === 'affiliate'">
                      <a-checkbox
                        :checked="Boolean(form[item.field])"
                        :true-label="1"
                        :false-label="0"
                        :disabled="item.schema.disabled"
                        @change="(e) => setChangeVal(Number(e.target.checked), item.field)"
                      >
                        Is this a special activities (2% mark up for affilliates)?
                      </a-checkbox>
                    </template>

                    <template v-else-if="['switch', 'popConfirmSwitch'].includes(item.component)">
                      <component
                        :is="item.component === 'popConfirmSwitch' ? 'popConfirmSwitch' : 'a-switch'"
                        :checked="Boolean(form[item.field])"
                        :on-value="1"
                        :off-value="0"
                        :disabled="item.schema.disabled"
                        :no-tips="$t('93289')"
                        @change="(v) => setChangeVal(Number(v), item.field)"
                      />
                    </template>

                    <template v-else-if="item.field === 'activity_video'">
                      <component
                        v-show="calcShowPhoto('activity_video')"
                        :is="'a-form-model'"
                        :ref="'form'"
                        :model="form"
                        :style="{ margin: '16px 0 0 0' }"
                        @submit.prevent
                      >
                        <component
                          :is="'a-form-model-item'"
                          class="basic-form-item"
                          :colon="false"
                          :prop="item.field"
                          :label="''"
                          :rules="item.schema.rules"
                        >
                          <video-upload
                            ref="video"
                            v-model="form[item.field]"
                            :watermark="true"
                            :is_btn_trigger="true"
                            :disabled="item.schema.disabled"
                            :custom-conf="videoUploadConf"
                            :upload-callback="videoUploadCb"
                            :upload-view="{
                              fieldKey: 'activity_video',
                              required: getRequired(item.field),
                              title: $t('83832'),
                              tipsList: [{ text: $t('29636'), isHtml: true }]
                            }"
                            class="activity-video-style"
                          ></video-upload>
                        </component>
                      </component>
                    </template>
                    <template v-else-if="item.component === 'PhotoBanner'">
                      <UploadDesc></UploadDesc>
                      <PhotoBanner
                        class="activity_banner"
                        ref="photo_banner"
                        :currentCreateLang="language || form.language"
                        :list-all.sync="form[item.field]"
                        :required="getRequired(item.field)"
                        :banner-count="banner_count"
                        :trigger-banner-check="trigger_banner_check"
                        :allow-download-image="!isMerchant"
                        :show-cur-lang-only="showCurLangOnly"
                        :upload-view="{
                          fieldKey: 'activity_banner',
                          required: getRequired(item.field),
                          title: $t('83831')
                        }"
                        :bannerMustBeClear="+bannerQualityType === 1"
                        v-bind="item.schema"
                        @banner-count-change="handleBannerCountChange(item.field, 'photo_banner')"
                      />
                    </template>

                    <div v-else-if="item.component === 'PhotoActivity'" class="__component">
                      <PhotoActivity
                        v-show="calcShowPhoto('photo_activity')"
                        ref="photo_activity"
                        :currentCreateLang="language || form.language"
                        :list-all.sync="form[item.field]"
                        :required="getRequired(item.field)"
                        :banner-count="banner_count"
                        :trigger-banner-check="trigger_banner_check"
                        :minBannerCount="minBannerCount"
                        :allow-download-image="!isMerchant"
                        :show-cur-lang-only="showCurLangOnly"
                        :limit="99"
                        :bannerMustBeClear="+bannerQualityType === 1"
                        v-bind="item.schema"
                        :upload-view="{
                          showIndex: true,
                          isSort: true,
                          fieldKey: 'activity_picture',
                          required: getRequired(item.field),
                          title: $t('83860')
                        }"
                        :style="{ margin: '16px 0 0 0' }"
                        @banner-count-change="handleBannerCountChange(item.field, 'photo_activity')"
                      />
                    </div>

                    <div v-else-if="item.render">
                      <renderItem
                        ref="renderItem"
                        :render="item.render"
                        :schema-config="item.schema"
                        :result="form"
                        :activity_id="+activity_id"
                        :disabled="item.schema.disabled"
                      ></renderItem>
                    </div>
                  </shim-antd-tooltip>
                  <StrongTips :tips="item.schema && item.schema.strongTips" />
                </div>

                <div v-if="item.tips" class="tips-style">
                  <span v-html="item.tips"></span>
                </div>
                <div v-if="item.errorMsg" class="common-error-style">
                  <span v-html="item.errorMsg"></span>
                </div>
              </component>
            </div>
          </component>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BaseMixin from './base-mixin.vue'
export default {
  mixins: [BaseMixin],
  data() {
    return {}
  },
  computed: {
    calcShowPhoto() {
      return (key) => {
        const { form, photoBannerList, photoActivityList } = this
        let isb = false
        const bannerPhototLength = form?.activity_banner?.[0]?.image?.length || photoBannerList?.length
        const activityPhotoLength =
          form?.activity_picture?.[0]?.images_groups?.length || photoActivityList?.length >= 3
        switch (key) {
          case 'photo_activity':
            isb = bannerPhototLength || activityPhotoLength
            break
          case 'activity_video':
            isb = activityPhotoLength
            break
          default:
            break
        }
        return isb
      }
    },
    calcFinishedFieldKey() {
      return (key) => {
        const { form, language } = this
        const val = form[key]
        let isb = true
        switch (key) {
          case 'activity_banner':
            isb = val?.find((o) => o.language_type === language)?.image?.some((img) => img.name)
            break
          case 'activity_picture':
            isb = val
              ?.find((o) => o.language_type === language)
              ?.images_groups?.some((o) => o.image?.some((img) => img.name))
            break
          default:
            break
        }
        // console.log(key, !!isb, val)
        return !!isb
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-video-style ::v-deep {
  .upload-view-box__tips-list {
    .tips-box span {
      font-weight: 400 !important;
    }
  }
}
.basic-form-item {
  margin-bottom: 0;
  ::v-deep {
    .ant-col.ant-form-item-label {
      padding: 16px 0 8px 0;
      line-height: 20px;
    }
    .ant-input {
      width: 440px;
    }
    .ant-form-item-children {
      width: 100%;
      display: block;
    }
  }
}

.tips-style {
  line-height: 1.5em;
  font-style: italic;
  color: #a6a6a6;
}

.big-markdown {
  ::v-deep .ant-col.ant-form-item-control-wrapper {
    width: 100%;
    max-width: none;
  }
}
</style>
