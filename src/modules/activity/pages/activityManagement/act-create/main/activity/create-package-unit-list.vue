<template>
  <div class="create-package-unit-list">
    <div class="ant-col ant-form-item-label">
      <label class="ant-form-item-required ant-form-item-no-colon">
        {{ $t('83901') }}
      </label>
    </div>
    <div v-if="packages" class="pkg-unit-list-wrap">
      <CreatePackageUnit
        v-for="(item, i) in packages"
        ref="pkgUnitFormRefs"
        :key="i"
        :pkg-unit-form="item"
        :index="i"
        :show-delete="packages.length >= 2"
        class="create-package-unit-item"
        @delete="deletePkgUnit(i, packages)"
        @copy="copyPkgUnit(i, packages)"
      ></CreatePackageUnit>
      <span class="add-pkg-btn">
        <a-button type="primary" @click="addPkgUnit(packages)">
          <a-icon type="plus" />{{ $t('83098') }}
        </a-button>
      </span>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { getUuid } from '@activity/utils'
import CreatePackageUnit from './create-package-unit.vue'
import { getDefaultUnitForm } from '@activity/pages/package/units/utils'

export default {
  props: {
    activity_id: {
      type: Number,
      default: 0
    },
    packages: {
      type: Array,
      default: () => []
    }
  },
  components: {
    CreatePackageUnit
  },
  watch: {
    packages: {
      immediate: true,
      deep: true,
      handler() {
        this.$emit('updateFinished', this.getPkgSkuListFinhished())
      }
    }
  },
  data() {
    return {}
  },
  methods: {
    getUuid,
    getPkgSkuListFinhished() {
      const { packages } = this
      return packages?.length
        ? packages.every((pkg) => {
            const isb = pkg.skus?.every((o) => typeof o.unit_type === 'number')
            return pkg.package_title && isb
          })
        : false
    },
    deletePkgUnit(index, list) {
      list.splice(index, 1)
    },
    copyPkgUnit(index, list) {
      this.$store.commit('setCustomUnitType', this.initCustomUnitType(list))
      const copyData = cloneDeep(list[index])
      copyData.package_title && list.splice(index, 0, copyData)
    },
    initCustomUnitType(list) {
      // 处理创建过程中的自定义unit type
      let arr = (list || []).reduce((a, o) => {
        const a2 = o.skus?.filter((o2) => {
          return o2.unit_type === 5
        })
        a2 && a.push(...a2)
        return a
      }, [])
      return arr.map((o) => {
        return this.addOptions(o)
      })
    },
    addOptions(o) {
      const newOpt = {
        unit_type: 5,
        text: o.unit_name,
        value: o.value,
        local: [
          {
            language: klook.getEditLang(),
            unit_name: o.unit_name
          }
        ]
      }
      return newOpt
    },
    addPkgUnit(list) {
      const pkg = {
        id: 0,
        package_title: '',
        skus: [getDefaultUnitForm()]
      }
      list.push(pkg)
    },
    async checkPkgUnitForm() {
      const arr = Array.from(this.$refs.pkgUnitFormRefs || [])
      if (!arr.length) {
        return true
      }
      const res = arr.map((ref) => {
        return ref.handleConfirm()
      })
      const isb = (await Promise.all(res)).every((v) => v)
      return isb
    }
  }
}
</script>

<style lang="scss" scoped>
.add-pkg-btn {
  display: inline-block;
  margin: 16px 0 0 0;
}
.create-package-unit-list {
  background-color: #fff;
}
.create-package-unit-item {
  &:not(:last-of-type) {
    margin-bottom: 16px;
  }
}
</style>
