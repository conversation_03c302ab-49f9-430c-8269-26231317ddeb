<template>
  <div class="js-create-package-node">
    <div v-if="current === 0" class="pkg-unit-box">
      <a-form-model ref="formRef" :model="pkgUnitForm" layout="vertical" class="form" :rules="rules">
        <a-form-model-item :label="$t('83823')" prop="package_title" required>
          <a-input
            v-model="pkgUnitForm.package_title"
            class="form-ctrl"
            :placeholder="$t('global_please_input')"
          />
        </a-form-model-item>
      </a-form-model>
      <CreateUnit
        ref="units"
        class="create-unit-wrap"
        :list="pkgUnitForm.skus"
        :subCategoryId="subCategoryId"
        :hideSort="true"
      />
      <span class="pkg-unit-box__left-pos">
        <span v-if="index + 1 > 0" class="flag-index">{{ index + 1 }}</span>
      </span>
      <span class="pkg-unit-box__right-pos">
        <span v-if="showDelete" class="btn-icon delete" @click="$emit('delete')"
          ><a-icon type="delete"></a-icon
        ></span>
        <span
          :class="!pkgUnitForm.package_title && 'copy-disabled'"
          class="btn-icon copy"
          @click="$emit('copy')"
          ><a-icon type="copy"></a-icon
        ></span>
      </span>
    </div>
    <div v-if="current === 1" class="form">
      <RuleSettings
        ref="ruleSettings"
        :is-edit="true"
        :init-data="skuRuleSetting"
        :list="calcUnitList"
        class="guide-rule-settings"
        @updateFinished="$emit('updateFinished', $event)"
      />
    </div>
  </div>
</template>

<script>
import CreateUnit from '@activity/pages/package/units/new-components/create-unit/index.vue'
import RuleSettings from '@activity/pages/package/units/new-components/rule-settings/index.vue'

export default {
  provide() {
    return {
      activityId: this.activityId
    }
  },
  components: {
    CreateUnit,
    RuleSettings
  },
  props: {
    isSettings: {
      type: Boolean,
      default: false
    },
    skuRuleSetting: {
      type: Object,
      default: () => {}
    },
    skuList: {
      type: Array,
      default: () => []
    },
    pkgUnitForm: {
      type: Object,
      default: () => {}
    },
    index: {
      type: Number,
      default: -1
    },
    showDelete: {
      type: Boolean,
      default: false
    },
    current: {
      type: Number,
      default: 0
    }
  },
  data() {
    const validator = (r, v, callback) => {
      if (!v.trim()) {
        return callback(new Error(this.$t('global_please_input')))
      }
      callback()
    }
    return {
      rules: {
        package_title: [
          {
            required: true,
            validator: validator
          }
        ]
      }
    }
  },
  computed: {
    calcUnitList() {
      const { global_inv_quantity } = this.skuRuleSetting || {}
      const arr = (this.skuList || []).map((o) => {
        const { fix_price, unit_name_format, unit_type, sku_id } = o
        const { cost_price, retail_price } = fix_price || {}
        return {
          sku_id,
          unit_name: unit_name_format,
          cost_price: cost_price || '',
          retail_price: retail_price || '',
          unit_type,
          inv_quantity: global_inv_quantity < 0 ? '' : global_inv_quantity
        }
      })
      return arr
    },
    subCategoryId() {
      return klook.parse2id(this.$route.params.id)
    },
    activityId() {
      return klook.parse2id(this.$route.query.activity_id, undefined)
    }
  },
  methods: {
    validateRuleSettings() {
      const ruleSettings = this.$refs.ruleSettings
      return ruleSettings.validateForm()
    },
    validateUnits() {
      const units = this.$refs.units
      return units.validateUnits().catch(() => false)
    },
    validateForm() {
      const form = this.$refs.formRef
      return form.validate().catch(() => false)
    },
    getUnitData() {
      const units = this.$refs.units
      return units.getData()
    },
    scrollHasError(el = '.js-create-package-node .has-error') {
      setTimeout(() => {
        const dom = el && document.querySelector(el)
        if (dom) {
          dom?.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })
        }
      }, 1)
    },
    async handleConfirm() {
      if (this.current === 0) {
        const validate = await Promise.all([this.validateForm(), this.validateUnits()])
        if (!validate.every((item) => item)) {
          this.scrollHasError()
          return
        }
      } else if (this.current === 1) {
        const validate = await Promise.all([this.validateRuleSettings()])
        if (!validate.every((item) => item)) {
          return
        }
      }
      return this.postData()
    },
    postData() {
      if (this.current === 1) {
        const ruleSettings = this.$refs.ruleSettings
        const rule_setting = ruleSettings?.getData()
        return {
          rule_setting
        }
      }
      const { package_title } = this.pkgUnitForm
      const units = this.$refs.units
      const skus = units?.getData()
      const data = {
        activity_id: this.activityId,
        language: klook.getEditLang(),
        package_title: package_title?.trim(),
        skus
      }
      return data
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .guide-rule-settings {
  .alert-tips {
    margin: 16px 0;
  }
}
::v-deep .ant-steps-item-content {
  width: 160px;
}
.form {
  .form-ctrl {
    width: 400px;
  }
}
.create-unit-wrap ::v-deep {
  // 覆盖isSetting样式 start
  .form-wrap.settings-form-wrap {
    width: auto;
  }
  .unit-wrap .unit-opear-wrap {
    top: 12px;
    right: 20px;
  }
  // 覆盖isSetting样式 end
  .form-wrap {
    background: #fff;
    padding: 12px 20px;
    border-radius: 4px;
    .form-item {
      margin-bottom: 0;
    }
  }
  .unit-wrap {
    padding: 0;
    &:not(:last-of-type) {
      margin-bottom: 10px;
    }
  }
}

.pkg-unit-box {
  position: relative;
  padding: 10px 10px 10px 36px;
  background-color: rgba(240, 247, 255, 0.6);
  &__left-pos {
    position: absolute;
    top: 0;
    left: 10px;
    z-index: 3;
    .flag-index {
      display: inline-block;
      min-width: 18px;
      height: 16px;
      border-radius: 0 0 4px 4px;
      background: #437dff;
      font-size: 12px;
      line-height: 14px;
      font-weight: 600;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  &__right-pos {
    position: absolute;
    top: 6px;
    right: 6px;
    z-index: 3;
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #595959;
    .btn-icon {
      padding: 4px;
      cursor: pointer;
      &:not(:last-of-type) {
        margin-right: 12px;
      }
      &.copy-disabled {
        cursor: not-allowed;
        color: #bfbfbf;
      }
    }
  }
}
</style>
