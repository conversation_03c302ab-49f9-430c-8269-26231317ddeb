<template>
  <div class="create-price-inventory">
    <div v-if="loading" class="common-spin-style">
      <a-spin />
    </div>
    <div v-if="skuRuleSetting" class="common-guide-basic-box">
      <h2>
        {{ $t('81825') }}
      </h2>
      <CreatePackageUnit
        ref="pkgUnitRef"
        :current="1"
        :skuRuleSetting="skuRuleSetting"
        :skuList="skuList"
        @updateFinished="$emit('updateFinished', $event)"
      ></CreatePackageUnit>
    </div>
  </div>
</template>

<script>
import CreatePackageUnit from './create-package-unit.vue'
import { matchLang } from '@activity/utils'
export default {
  inject: ['rootChangeLoading'],
  components: {
    CreatePackageUnit
  },
  props: {
    copyData: {
      type: Object,
      default: null
    },
    language: {
      type: String,
      default: ''
    },
    packageId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      loading: false,
      skuRuleSetting: null,
      skuList: []
    }
  },
  mounted() {
    this.$emit('guideMounted')
  },
  async created() {
    klook.bus.$emit('addHandleSave2bus', this.handleSave, { stepCount: this.$route.query.step_count })
    this.loading = true
    let res = await this.getPackageModel()
    if (res && this.copyData) {
      const savedData = {
        sku_list: _.cloneDeep(res.sku_list || [])
      }
      this.copyData.sku_list = savedData.sku_list
      res = _.cloneDeep(this.copyData)
      res.sku_list = savedData.sku_list
    }
    this.loading = false
    if (res) {
      this.$set(this, 'skuRuleSetting', res || {})
      const skuList = (res?.sku_list || []).map((item) => {
        const { local } = item
        const name = matchLang('unit_name', 'language_type', local, klook.getAPILang())
        item.unit_name_format = name || local?.[0]?.unit_name
        return {
          ...item,
          name: `${name}`
        }
      })
      this.$set(this, 'skuList', skuList)
    }
  },
  methods: {
    async getPackageModel() {
      const res = await ajax.get(ADMIN_API.act.getPackageModel, {
        params: {
          package_id: this.packageId,
          language: this.language
        }
      })
      return res
    },
    async handleSave() {
      const data = await this.checkPriceInventoryForm()
      if (data) {
        this.rootChangeLoading(true)
        const res = await ajax.postBody(ADMIN_API.act.postUpdatePackageModel, {
          noDefaultResponseInterceptor: true, // 获取error.message
          data: {
            ...(data?.rule_setting || {}),
            package_id: this.packageId
          }
        })
        this.rootChangeLoading(false)
        klook.bus.$emit('sendGuideCreateSave2bus', this.calcCurrStepData, res)
        if (res?.success) {
          klook.bus.$emit('setStepStatusFinished2bus', this.$route.query.step_count, {
            package_id: this.packageId
          })
          this.$emit('successSave')
        }
        return res?.success
      }
      return false
    },
    async checkPriceInventoryForm() {
      const ref = this.$refs.pkgUnitRef
      const res = ref?.handleConfirm ? await ref.handleConfirm() : true
      return res
    }
  }
}
</script>

<style lang="scss" scoped>
.create-price-inventory {
  //
}
</style>
