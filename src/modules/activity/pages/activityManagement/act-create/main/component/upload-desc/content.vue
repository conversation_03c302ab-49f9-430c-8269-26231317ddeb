<template>
  <div class="upload-content" :class="styleType">
    <div class="upload-content-box">
      <p v-for="(item, i) in arr" :key="i" class="upload-content-box__item">
        <span v-if="item.isHtml" v-html="$t(item.textid)"></span>
        <span v-else>{{ $t(item.textid) }}</span>
      </p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    styleType: {
      type: String,
      default: '' // modal-type
    }
  },
  data() {
    return {
      descTitel: '83840',
      arr: [
        { textid: '83949' },
        { textid: '83950' },
        { textid: '83951' },
        { textid: '83952' },
        { textid: '83953' },
        { textid: '83954', isHtml: true }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-content {
  font-size: 14px;
  line-height: 22px;
  color: #757575;
  &.modal-type {
    color: #000;
    .upload-content-box {
      &__item {
        &:not(:last-of-type) {
          margin-bottom: 8px;
        }
      }
    }
  }
}
.upload-content-box {
  //
}
</style>
