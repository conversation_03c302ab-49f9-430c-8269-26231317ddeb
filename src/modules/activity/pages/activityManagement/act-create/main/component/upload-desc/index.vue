<template>
  <div class="upload-desc">
    <div class="upload-desc-box">
      <UploadDescContent class="upload-desc-box__content-wrap"></UploadDescContent>
      <span class="more-link" @click="descObj.visible = true">{{ $t('78870') }}</span>
    </div>
    <a-modal v-model="descObj.visible" v-bind="descObj" @ok="descObj.visible = false">
      <UploadDescContent :style-type="'modal-type'"></UploadDescContent>
    </a-modal>
  </div>
</template>

<script>
import UploadDescContent from './content.vue'
export default {
  components: {
    UploadDescContent
  },
  data() {
    return {
      descObj: {
        title: __('83840'),
        centered: true,
        okText: __('btn.okay'),
        wrapClassName: 'common-modal-hide-cancel',
        visible: false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-desc {
  margin-top: -12px;
  margin-bottom: 24px;
}
.upload-desc-box {
  &__content-wrap {
    max-height: 63px;
    overflow: hidden;
  }
  .more-link {
    display: inline-block;
    margin-top: 4px;
    font-size: 14px;
    line-height: 22px;
    color: #2073f9;
    cursor: pointer;
  }
}
</style>
