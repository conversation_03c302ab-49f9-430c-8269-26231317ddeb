<template>
  <div class="upload-view">
    <div class="upload-view-box">
      <div class="upload-view-box__content">
        <div v-if="title" class="upload-view-box__content-title">
          <span v-if="required" class="common-required-star">*</span>
          {{ title }}
        </div>
        <div class="upload-view-box__content-images">
          <div
            v-if="images && images.length"
            class="img-box-wrap"
            :class="['common-draggable-wrap', dragCursor, !hideUpload && images.length > 5 && 'show-line']"
          >
            <draggable
              v-model="dragList"
              chosen-class="chosen"
              force-fallback="true"
              :group="'common-draggable'"
              animation="1000"
              :sort="isSort"
              @change="changeDragHandler"
              @start="onStart"
              @end="onEnd"
            >
              <transition-group
                key="common-draggable__transition-group"
                tag="div"
                class="common-draggable-box"
              >
                <div
                  v-for="(img, i) in images"
                  :key="'k' + i"
                  class="img-box"
                  :class="focusList.includes(i) && 'focus-style'"
                >
                  <PrefixImg
                    v-if="showPrefixImg"
                    :wh-rate="'1:1'"
                    :real-height="104"
                    :src="imageUrlPrefix + img.ORIGIN.name"
                  />
                  <img v-else :src="imageUrlPrefix + img.ORIGIN.name" />
                  <span v-if="showIndex" class="index-flag">{{ i + 1 }}</span>
                  <div class="img-box__hover-box">
                    <span
                      v-if="!hidePreview"
                      class="img-box__icon"
                      @click.stop="previewHandler(imageUrlPrefix + img.ORIGIN.name)"
                    >
                      <a-icon type="eye"></a-icon>
                    </span>
                    <span v-if="!hideDelete" class="img-box__icon" @click.stop="$emit('delete', img, i)">
                      <a-icon type="delete"></a-icon>
                    </span>
                  </div>
                </div>
              </transition-group>
            </draggable>
          </div>
          <div
            v-if="!hideUpload && uploadText"
            class="upload-view-box__content-default"
            @click="$emit('upload')"
          >
            <a-icon :type="loading ? 'loading' : 'picture'" />
            <span class="upload-text">
              {{ uploadText }}
            </span>
          </div>
        </div>
        <div class="upload-view-box__tips-list">
          <div v-for="(tips, i) in tipsList" :key="i" class="tips-box">
            <span v-if="tips.isHtml" v-html="tips.text"></span>
            <span v-else>{{ tips.text }}</span>
          </div>
        </div>
      </div>
    </div>
    <a-modal v-model="imgView.visible" v-bind="imgView">
      <div class="big-img-view">
        <img :src="imgView.img.url" />
      </div>
    </a-modal>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import PrefixImg from '@activity/components/prefix-img/index.vue'
export default {
  components: {
    PrefixImg,
    draggable
  },
  props: {
    focusList: {
      type: Array,
      default: () => []
    },
    isSort: {
      type: Boolean,
      default: false
    },
    showIndex: {
      type: Boolean,
      default: false
    },
    hideUpload: {
      type: Boolean,
      default: false
    },
    hidePreview: {
      type: Boolean,
      default: false
    },
    hideDelete: {
      type: Boolean,
      default: false
    },
    tipsList: {
      type: Array,
      default: () => []
    },
    images: {
      type: Array,
      default: () => []
    },
    isSyncImages: {
      type: Boolean,
      default: true
    },
    imageUrlPrefix: {
      type: String,
      default: ''
    },
    showPrefixImg: {
      type: Boolean,
      default: false
    },
    uploadText: {
      type: String,
      default: ''
    },
    required: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dragList: [],
      dragCursor: '',
      imgView: {
        wrapClassName: 'big-img-view-modal',
        footer: null,
        centered: true,
        width: '800px',
        visible: false,
        img: {
          url: ''
        }
      },
      currentItem: null,
      loading: false
    }
  },
  watch: {
    images: {
      immediate: true,
      deep: true,
      handler(arr) {
        this.dragList = _.cloneDeep(arr)
      }
    }
  },
  methods: {
    changeDragHandler({ moved }) {
      const arr = this.dragList?.map((item, index) => ({
        ...item,
        display_order: index + 1
      }))
      if (this.isSyncImages) {
        this.$emit('update:images', arr)
      }
      this.$emit('drag', arr)
    },
    onStart() {
      this.dragCursor = 'grabbing'
    },
    onEnd() {
      this.dragCursor = ''
    },
    previewHandler(url) {
      const { imgView } = this
      imgView.img.url = url || ''
      imgView.visible = !!url
    }
  }
}
</script>

<style lang="scss" scoped>
.common-draggable-wrap {
  .common-draggable-box {
    display: flex;
    flex-wrap: wrap;
  }
  &.grabbing,
  &.grabbing * {
    cursor: grabbing !important;
  }
}
.common-required-star {
  color: #ff4d4f;
  font-size: 14px;
  margin-right: 4px;
  font-family: SimSun, sans-serif;
}
::v-deep .big-img-view-modal {
  .ant-modal-content .ant-modal-body {
    max-height: 90vh;
  }
}
.big-img-view {
  padding: 40px 4px 4px 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    max-width: 100%;
  }
}
.upload-view-box {
  font-size: 14px;
  line-height: 22px;
  font-weight: 400;
  cursor: default;
  &__content-title {
    margin-bottom: 8px;
  }
  &__content-default {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 104px;
    height: 104px;
    background: #fafafa;
    border: 1px dashed #d9d9d9;
    border-radius: 2px;
    color: rgba(0, 0, 0, 0.45);
    cursor: pointer;
    .upload-text {
      margin-top: 8px;
    }
  }
  &__content-images {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    .img-box-wrap {
      position: relative;
      max-width: calc(112px * 5);
      margin-right: 8px;
      display: flex;
      flex-wrap: wrap;
      &.show-line {
        margin-right: 23px;
        &::after {
          position: absolute;
          top: 0;
          right: -8px;
          width: 1px;
          height: calc(100% - 8px);
          display: inline-block;
          content: '';
          background-color: #eee;
        }
      }
    }
    .img-box {
      position: relative;
      display: inline-block;
      width: 104px;
      height: 104px;
      padding: 9px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #d9d9d9;
      border-radius: 2px;
      margin-bottom: 8px;
      &:not(:last-of-type) {
        margin-right: 8px;
      }
      &.focus-style {
        border: 2px solid #437dff;
      }
      .index-flag {
        position: absolute;
        top: 0;
        left: 4px;
        z-index: 8;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 5px;
        line-height: 16px;
        color: #fff;
        background-color: #437dff;
        border-radius: 0 0 4px 4px;
        font-size: 12px;
        font-weight: 600;
      }
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      &__icon {
        padding: 4px;
        font-size: 16px;
        color: #fff;
        cursor: pointer;
      }
      &:hover {
        .img-box__hover-box {
          display: flex;
          cursor: grabbing;
        }
      }
      .img-box__hover-box {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 4;
        width: 100%;
        height: 100%;
        display: none;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.5);
      }
    }
  }
  &__tips-list {
    margin-top: 8px;
    font-size: 14px;
    line-height: 22px;
    color: #757575;
  }
}
</style>
