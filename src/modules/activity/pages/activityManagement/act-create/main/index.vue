<template>
  <div class="act-create-main" :style="calcStepCount === 3 ? { width: 'calc(100% - 280px - 320px)' } : {}">
    <div v-if="!isRefresh" class="act-create-main-box" @mousemove="onMouseover" @mouseleave="onMouseleave">
      <template v-if="isActBasicInfoStep">
        <ActBasicInfo
          :obtConfig="copySectionData"
          :packagesParams="copySectionData.packages"
          :leafCategoryId="leafCategoryId"
          :isGuideCreate="!calcStepCount && !calcAid"
          :stepCount="calcStepCount"
          :language="calcLang"
          @hook:mounted="onMountedActBasicInfo"
          @updateFinished="updateFinishedHandler('actFinished', $event)"
          @guideMounted="$emit('guideMounted')"
        ></ActBasicInfo>
        <div v-if="!calcStepCount" class="pkg-unit-list-box" @mouseenter="onMouseover">
          <CreatePackageUnitList
            ref="pkgUnitListRef"
            :activity_id="calcAid"
            :packages="copySectionData.packages"
            @updateFinished="updateFinishedHandler('actPkgUnitFinished', $event)"
          ></CreatePackageUnitList>
        </div>
      </template>
      <template v-if="isPkgBasicInfoStep && calcCurrStepData">
        <template v-if="calcCurrStepData.step === creationStepKeyDict.package_main_info">
          <PkgBasicInfo
            v-if="pkgSectionData"
            ref="pkgBasicInfo"
            :is-guide-flow="isGuideFlow"
            :activity_id="calcAid"
            :package_id="pkgSectionData.package_id"
            :package_type="pkgSectionData.package_type"
            :pkgSectionData="pkgSectionData"
            @hook:mounted="onMountedPkgBasicInfo"
            @updateFinished="updateFinishedHandler('pkgFinished', $event)"
            @guideMounted="$emit('guideMounted')"
          ></PkgBasicInfo>
          <div v-else class="common-spin-style">
            <a-spin />
          </div>
        </template>
        <CreatePriceInventory
          v-else-if="calcCurrStepData.step === creationStepKeyDict.package_model"
          ref="priceInventoryRef"
          :language="calcLang"
          :package-id="pkgSectionData.package_id"
          :pkg-unit-form="calcPkgData"
          :copy-data="calcCopyData('package_model')"
          @updateFinished="updateFinishedHandler('priceInventoryFinished', $event)"
          @guideMounted="$emit('guideMounted')"
        ></CreatePriceInventory>

        <SupplyApiMapping
          v-else-if="calcCurrStepData.step === creationStepKeyDict.supply_api_mapping"
          show-title
          is-guide-model
          :package-id="pkgSectionData.package_id"
          @hook:mounted="$emit('guideMounted')"
          @successSave="successSaveHandler"
        />

        <PkgExtraMerchant
          v-else-if="calcCurrStepData.step === creationStepKeyDict.extra_information"
          :is-guide-flow="isGuideFlow"
          :activity_id="calcAid"
          :language="calcLang"
          :package_id="pkgSectionData.package_id"
          :style-type="'create-guide-type'"
          :copy-data="calcCopyData('extra_information')"
          @successSave="successSaveHandler"
          @updateFinished="updateFinishedHandler('extraFinished', $event)"
          @guideMounted="$emit('guideMounted')"
        ></PkgExtraMerchant>
      </template>
    </div>
    <div v-if="alert2fixed.show && alert2fixed.html" class="common-alert-fixed">
      <a-alert type="warning" :showIcon="true">
        <div slot="message" style="display: flex; align-items: center">
          <span v-html="alert2fixed.html"></span>
          <a-icon type="close-circle" class="close-warning" @click="closeWarning" />
        </div>
      </a-alert>
    </div>

    <AnchorTips
      ref="anchorTips"
      :visible.sync="anchorTips.visible"
      :links="anchorTips.links"
      :wrap-style="wrapStyle"
      :target-offset="targetOffset"
      :get-drawer-container="getDrawerContainer"
    />
    <slot name="slot-footer"></slot>
  </div>
</template>

<script>
import ActBasicInfo from './activity/basic-info.vue'
import PkgBasicInfo from './package/basic-info.vue'
import CreatePackageUnitList from './activity/create-package-unit-list.vue'
import CreatePriceInventory from './activity/create-price-inventory.vue'
import { getDefaultUnitForm, initUnitForm } from '@activity/pages/package/units/utils'
import StepMixin from '../step-mixin.js'
import PkgExtraMerchant from '@activity/pages/package/extra/merchant-index.vue'
import { ROUTE_NAME_DICT, saveTrackEventCustom } from '@activity/utils/actGTM'
import { state as pkgsState } from '@activity/store/pkgs-cache-observable.js'

import AnchorTips from '@activity/components/anchor-tips/index'
import { getFieldTips } from '@activity/utils/fieldGuideTipsDict'
import { getParentNode } from '@activity/utils/index'

import SupplyApiMapping from '@activity/pages/package/supply-api-mapping/index.vue'

const anchor_tips_mixin = {
  data() {
    return {
      pkgsCacheState: pkgsState['pkgsCache'],
      anchorTips: {
        visible: false,
        links: []
      },
      targetOffset: 0,
      footerHeight: 0
    }
  },
  watch: {
    'anchorTips.links': {
      immediate: true,
      deep: true,
      handler(data) {
        this.$nextTick(() => {
          this.anchorTips.visible = !!data.length
        })
      }
    }
  },
  computed: {
    calcPkgStepStatusData() {
      return (pid) => {
        const { pkgsCacheState } = this
        const pkgStepStatus = pkgsCacheState?.[pid]?.[ADMIN_API.act.get_package_step_status] || {}
        return pkgStepStatus
      }
    },
    calcCopyData() {
      return (step) => {
        if (!step) {
          return
        }
        const { prevPidData, pkgSectionData, calcPkgStepStatusData } = this
        const isFinished = calcPkgStepStatusData(pkgSectionData.package_id)?.[step]
        if (isFinished) {
          return
        }
        const res = _.cloneDeep(prevPidData?.result?.[step])
        this.initLoopPid(res, { package_id: pkgSectionData.package_id })
        return res
      }
    },
    wrapStyle() {
      return {
        height: `calc(100vh - ${this.targetOffset}px - ${this.footerHeight}px)`,
        marginTop: `${this.targetOffset}px`
      }
    }
  },
  methods: {
    onMouseover: _.debounce(function onMouseover(event) {
      const node = getParentNode(event.target, 'js-dataset-field', 30)
      klook.bus.$emit('highlightAnchorTipsField2bus', { field: (node && node.dataset.field) || null })
    }, 1000 / 60),
    onMouseleave() {
      klook.bus.$emit('highlightAnchorTipsField2bus', { field: null })
    },
    initLoopPid(obj, mergeData) {
      for (let key in obj) {
        if (typeof obj[key] === 'object') {
          this.initLoopPid(obj[key], mergeData)
        } else if (key === 'package_id') {
          obj[key] = mergeData[key]
        }
      }
    },
    initContainerUpperAndLowerHeight() {
      if (!this.targetOffset) {
        this.targetOffset = this.getTargetEleHeight('.act-create-header')
      }

      if (!this.footerHeight) {
        this.footerHeight = this.getTargetEleHeight('.act-create-footer-box')
      }
    },
    getTargetEleHeight(selector) {
      return document.querySelector(selector)?.getBoundingClientRect()?.height
    },
    onMountedActBasicInfo() {
      this.initContainerUpperAndLowerHeight()
      this.$set(this.anchorTips, 'links', this.getActFieldTips())
    },
    onMountedPkgBasicInfo() {
      this.initContainerUpperAndLowerHeight()
      this.$set(this.anchorTips, 'links', this.getPkgFieldTips())
    },
    getPkgFieldTips() {
      const pkgBasicInfo = this.$refs.pkgBasicInfo
      if (!pkgBasicInfo) {
        return []
      }

      const fieldItems = pkgBasicInfo.currOrderArr.reduce((acc, curr) => {
        const { orderList } = curr

        return [...acc, ...orderList]
      }, [])

      return this.getLinksBuFields(fieldItems, getFieldTips('pkgBasicInfo'))
    },
    getActFieldTips() {
      let fields = []
      // pic && video
      if (this.calcStepCount === 2) {
        fields = ['picture_list', 'picture_captions', 'activity_video']
      } else {
        fields = this.copySectionData.field_items.map((item) => item.field_key)
      }

      return this.getLinksBuFields(fields, getFieldTips('actBasicInfo'))
    },
    getLinksBuFields(fields, fieldTips) {
      return fields.reduce((acc, field) => {
        const currFieldTips = fieldTips.find((item) => {
          if (Array.isArray(item.associatedFields)) {
            return item.associatedFields.includes(field)
          }

          return item.field === field
        })

        if (currFieldTips) {
          acc.push({
            ...currFieldTips,
            target: `.js-dataset-field[data-field=${field}]`
          })
        }

        return acc
      }, [])
    }
  }
}

export default {
  name: 'CreateMain',
  mixins: [StepMixin, anchor_tips_mixin],
  provide() {
    return {
      setAlertFixed2provide: (opt) => {
        // hack, 取消 在 computed 调用时导致的关闭失效问题
        this.$nextTick(() => {
          this.$emit('updateAlert2fixed', opt)
        })
      },
      reloadPage2provide: () => {
        this.refreshView()
      },
      handleGTMBtn: (data) => {
        this.handleGTMBtn(data)
      }
    }
  },
  props: {
    isGuideFlow: {
      type: Boolean,
      default: false
    },
    prevPidData: {
      type: Object,
      default: null
    },
    packageId: {
      type: Number,
      default: undefined
    },
    leafCategoryId: {
      type: Number,
      default: undefined
    },
    pkgSectionData: {
      type: Object,
      default: null
    },
    actSectionData: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    PkgExtraMerchant,
    CreatePriceInventory,
    PkgBasicInfo,
    CreatePackageUnitList,
    ActBasicInfo,
    AnchorTips,
    SupplyApiMapping
  },
  data() {
    return {
      finishedObj: {
        actFinished: false,
        actPkgUnitFinished: false,
        pkgFinished: false,
        priceInventoryFinished: false,
        extraFinished: false
      },
      copySectionData: null,
      isRefresh: false,
      alert2fixed: {
        show: false,
        html: ''
      }
    }
  },
  watch: {
    actSectionData: {
      immediate: true,
      handler() {
        this.copySectionData = _.cloneDeep(this.getStepSection())
      }
    },
    calcStepCount: {
      handler() {
        this.copySectionData = _.cloneDeep(this.getStepSection())
        this.refreshView()
      }
    },
    $route: {
      immediate: true,
      deep: true,
      handler(route, oldRoute) {
        this.anchorTips.visible = false
        this.resetFinishedObj()
        this.alert2fixed.show = false
        this.$oldRoute = oldRoute
        this.saveTrackEventCustom.bind(this)({
          previousPageUrl: this.getPreviousPageUrl(oldRoute),
          event: 'virtualPageView'
        })
      }
    }
  },
  // eslint-disable-next-line vue/order-in-components
  computed: {
    calcLang() {
      return this.$route.query.lang
    },
    calcAid() {
      return klook.parse2id(this.$route.query.activity_id, 0)
    },
    calcStepCount() {
      return klook.parse2id(this.$route.query.step_count, 0)
    },
    isActBasicInfoStep() {
      return this.calcStepType === 'step_activity' && this.copySectionData
    },
    isPkgBasicInfoStep() {
      return this.calcStepType === 'step_package' && this.pkgSectionData
    }
  },
  mounted() {
    this.$on('updateAlert2fixed', this.updateAlert2fixed)
  },
  beforeDestroy() {
    this.$off('updateAlert2fixed', this.updateAlert2fixed)
  },
  methods: {
    saveTrackEventCustom: saveTrackEventCustom,
    updateAlert2fixed(opt) {
      _.merge(this.alert2fixed, opt)
    },
    getDrawerContainer() {
      return document.querySelector('.merchant-container')
    },
    resetFinishedObj() {
      const { finishedObj } = this
      for (let key in finishedObj) {
        finishedObj[key] = false
      }
    },
    updateFinishedHandler(key, value) {
      const { finishedObj } = this
      finishedObj[key] = value
      const isb = this.getCurrentFinished()
      this.$emit('updateFinished', isb)
      return isb
    },
    getCurrentFinished() {
      const { calcStepCount, finishedObj, calcStepType, calcOnePkgStepNum } = this
      if (calcStepType === 'step_activity') {
        if (!calcStepCount) {
          return finishedObj.actFinished && finishedObj.actPkgUnitFinished
        }
        return finishedObj.actFinished
      } else {
        if (calcOnePkgStepNum === 0) {
          return finishedObj.pkgFinished
        } else if (calcOnePkgStepNum === 1) {
          return finishedObj.priceInventoryFinished
        } else if (calcOnePkgStepNum === 2) {
          return finishedObj.extraFinished
        }
      }
      return false
    },
    successSaveHandler() {
      if (this.calcStepCount === this.calcMaxCount) {
        this.$router.push({
          path: `/act/activity/basic/${this.calcAid}`,
          query: {
            lang: this.calcLang
          }
        })
      }
    },
    getStepSection() {
      const { calcStepCount } = this
      const { sections = [] } = this.actSectionData || {}
      const obj = sections[calcStepCount]
      if (!obj) {
        return
      }
      if (!calcStepCount) {
        if (obj?.packages?.length) {
          obj?.packages?.forEach((pkg) => {
            const skus = pkg.skus?.map((sku) => {
              return sku.sku_id ? initUnitForm(sku) : getDefaultUnitForm()
            })
            this.$set(pkg, 'skus', skus)
          })
        } else {
          const defObj = {
            package_title: '',
            skus: [getDefaultUnitForm()]
          }
          obj.packages = [defObj]
        }
      }
      return obj
    },
    getPreviousPageUrl(oldRoute) {
      let previousPageUrl = document.referrer || 'none'

      if (oldRoute && oldRoute.path !== '/') {
        let oldInfo = _.find(ROUTE_NAME_DICT, {
          name: oldRoute.name
        })

        previousPageUrl = `${KLK_LANG}/act/${oldInfo ? oldInfo.path : 'other_' + oldRoute.name}`
      }

      return previousPageUrl
    },
    async handleGTMBtn({
      event = 'virtualPageView',
      eventAction = 'none',
      groupId = 'none',
      isBtnSave = true,
      customPath = undefined
    } = {}) {
      await this.saveTrackEventCustom({
        event,
        groupId,
        isBtnSave,
        customPath,
        eventAction,
        previousPageUrl: this.getPreviousPageUrl(this.$oldRoute)
      })
    },
    async refreshView() {
      this.isRefresh = true
      await this.$nextTick()
      this.isRefresh = false
    },
    async checkStepCountStatus() {
      const { calcStepCount } = this
      if (calcStepCount === 0) {
        const step0 = this.$refs.pkgUnitListRef?.checkPkgUnitForm
          ? await this.$refs.pkgUnitListRef.checkPkgUnitForm()
          : true
        return step0
      }
      return true
    },
    closeWarning() {
      this.alert2fixed.show = false
    }
  }
}
</script>

<style lang="scss" scoped>
.act-create-main-box {
  padding: 60px 24px 24px 24px;
}
.pkg-unit-list-box {
  margin-top: 8px;
}
</style>
