<template>
  <div id="package-basic-info-editable" class="basic-info js-basic-info-vm" :data-spm-page="getSpm">
    <div v-disabled="vDisabled" class="basic-info-box common-basic-form-style">
      <div v-if="!initLoadFinish" class="common-spin-style">
        <a-spin />
      </div>
      <div v-show="initLoadFinish">
        <div class="common-guide-basic-box">
          <h2 :id="$t('82054')" class="title">
            <!-- <i class="common-required-star">*</i> -->
            {{ $t('82054') }}
          </h2>
        </div>
        <!-- eslint-disable vue/no-use-v-if-with-v-for,vue/no-confusing-v-for-v-if -->
        <div v-for="(item, i) in currOrderArr" :key="i" class="common-guide-basic-box">
          <common-field
            :key="i"
            ref="ref_common_fields"
            v-model="form"
            v-bind="$props"
            :title="''"
            :pkg="package_info"
            :order-list="item.orderList || []"
            :schema-config="schemaConfig"
            :lock-auto-publish="lockAutoPublish"
            :original-data="reqData"
            :lock-auto-warm-up="lockAutoWarmUp"
            :ref-form="refForm"
            :disabled-open-ticket="disabledOpenTicket"
            :combo-confirmation-time="comboConfirmationTime"
            :combo-cancellation-policy="comboCancellationPolicy"
            :option-group="optionGroup"
            :is-publish-with-ai="isPublishWithAI"
            :is-create="isCreate"
            :hide-desc-tips="true"
            :hide-strong-tips="true"
            v-on="item.on"
            @fieldAttributeGroupVN="handleFieldAttributeGroupVN"
          ></common-field>
        </div>
        <div v-if="$root.isAdmin && $root.isFNB" class="common-copy-wrap">
          <a-button @click="openFnbTemplate">{{ $t('fnb_use_template') }}</a-button>
        </div>
      </div>
    </div>

    <List
      v-if="groupSelectionData.visible"
      :visible.sync="groupSelectionData.visible"
      v-bind="groupSelectionData"
    />
  </div>
</template>

<script>
import CommonField from '@activity/components/common_field'
import { mapState, mapGetters, mapMutations, mapActions } from 'vuex'
import { validatorDefault, getEditLang, scrollElError, getComboNameI18ns } from '@activity/utils'
import {
  FNB_TICKET_TYPE as fnb_ticket_types,
  FNB_TICKET_TYPE,
  cancel_policy
} from '@activity/pages/package/package_const.js'
import moment from 'moment'
import { pmsConfirm } from '@activity/utils'
import { isAdmin } from '@/env'
import common_mixin from '@activity/pages/activityManagement/detailV2/common_mixin'
import {
  voucher_usages,
  SUPPORT_WITHOUT_CALENDAR_SUB_CATEGORY_IDS_V2
} from '@activity/pages/package/package_const.js'
import List from '@activity/pages/activityManagement/detailV2/components/list/index'
import { reservationProductsMixin } from '@activity/pages/package/mixins/index'
import { getFieldTips } from '@activity/utils/fieldGuideTipsDict'

const selection_list_mixin = {
  components: {
    List
  },
  computed: {
    ...mapState({
      groupSelectionData: (state) => state.attr.groupSelectionData
    })
  }
}

const open_ticket_mixin = {
  watch: {
    disabledOpenTicket(v, oldV) {
      if (!oldV && v) {
        let { access_permission = 0 } = this.schemaConfig['ticket_type'] || {}
        if (access_permission !== 1) {
          this.form.ticket_type = undefined
        }
      }
    }
  },
  computed: {
    templateId() {
      return this.$store.state.categoryInfo?.template_id ?? 0
    },
    // 修改代码需同步到 @activity/pages/package/basicInfo/index.vue
    disabledOpenTicket() {
      // const validCategoryId =
      //   SUPPORT_WITHOUT_CALENDAR_SUB_CATEGORY_IDS.includes(this.subCategoryId) ||
      //   SUPPORT_WITHOUT_CALENDAR_CATEGORY_IDS.includes(this.categoryId)
      const validSubCategoryId = SUPPORT_WITHOUT_CALENDAR_SUB_CATEGORY_IDS_V2.includes(this.subCategoryId)
      const validInvType = ['OTHERS', 'API'].includes(this.form.inventory_type)

      return !(validInvType && validSubCategoryId)
    }
  }
}

const attribute_mixin = {
  data() {
    return {
      fieldAttributeGroupVN: {}
    }
  },
  methods: {
    handleFieldAttributeGroupVN({ field, $vm }) {
      this.$set(this, 'fieldAttributeGroupVN', {
        ...this.fieldAttributeGroupVN,
        [field]: $vm
      })
    },
    getCountObjectByAttributeFieldKey(key) {
      if (this.form.validity_model !== 1 && key === 'activation_validity') {
        return true
      }

      let instance = _.get(this.fieldAttributeGroupVN, key, null)
      let flag = (instance && instance.groupsSummary.some((item) => item.summary.length)) || !instance

      return flag || undefined
    }
  }
}

export default {
  components: {
    CommonField
  },
  mixins: [selection_list_mixin, open_ticket_mixin, attribute_mixin, common_mixin, reservationProductsMixin],
  inject: ['refreshPkgs', 'refreshPage', 'rootChangeLoading', 'setAlertFixed2provide'],
  provide: function () {
    return {
      initFloorTimelineData: this.initFloorTimelineData,
      calcAttrGroupDisplayState: this.calcAttrGroupDisplayState
    }
  },
  props: {
    isGuideFlow: {
      type: Boolean,
      default: false
    },
    pkgSectionData: {
      type: Object,
      default: null
    },
    activity_id: {
      type: [Number, String],
      required: true
    },
    package_id: {
      type: [Number, String],
      required: true
    },
    package_type: {
      type: Number
    }
  },
  data() {
    return {
      cancel_policy,
      initLoadFinish: false,
      reqData: null,
      actGlobal: null,
      cacheForm: null, // form的快照
      form: {},
      schemaConfig: {},
      new_package_id: this.package_id,
      lockAutoPublish: false,
      lockAutoWarmUp: false,
      serverTime: '',
      startTimestamp: 0,
      MAX_AUTO_PUBLISH_TIME: 1000 * 60 * 30,
      refForm: {},
      comboConfirmationTime: -1,
      comboCancellationPolicy: -1,

      optionGroup: {}
    }
  },
  computed: {
    ...mapState({
      isMerchant: 'isMerchant',
      pkgInfos: 'pkgInfos',
      packages: 'packages',
      merchantActHaveBeenApproved: 'merchantActHaveBeenApproved',
      lockMerchantActEditing: 'lockMerchantActEditing'
    }),
    ...mapGetters(['isPublishWithAI']),
    pkgBasicInfoFieldTips() {
      return getFieldTips('pkgBasicInfo')
    },
    vDisabled() {
      const deleteElements = [
        {
          selector: 'compound-extend__btn',
          force: true
        }
      ]

      if (this.lockMerchantActEditing) {
        return {
          lock: true,
          deleteElements,
          scope: 'all'
        }
      }

      const excludeElements = this.$store.state.isMC2BD ? [] : ['exclude-disabled']

      if (this.merchantActHaveBeenApproved && !this.isCreate) {
        this.setAlertFixed2provide({
          show: true,
          html: __('48231')
        })

        return {
          lock: true,
          defaultScope: 'freeText',
          excludeElements,
          deleteElements
        }
      } else {
        this.setAlertFixed2provide({
          show: false,
          html: ''
        })

        return {
          lock: false
        }
      }
    },
    refer_language() {
      let { lang, ref } = this.$route.query
      ref = ref || 'en_US'

      return lang === ref ? '' : ref
    },
    subCategoryId() {
      return this.pkgSectionData?.sub_category_id
    },
    categoryId() {
      let obj = this.$attrs.actGlobal ? this.$attrs.actGlobal : {}
      return obj.categoryInfo && obj.categoryInfo.category_id
    },
    klk_gvars() {
      const packages = (this.pkgInfos && this.pkgInfos.packages) || []
      const package_all = packages.filter((item) => item.product_type !== 1)
      return {
        package_all: package_all
      }
    },
    serverTimestamp() {
      return this.getUTCTimestamp(this.serverTime)
    },
    package_info() {
      let form = _.cloneDeep(this.form)
      return {
        ...form,
        ...this._get_packages_basic_info
      }
    },
    language() {
      return this.$store.state.edit_lang.map((v) => v.language_type)
    },
    hasCreateFields() {
      return !!this.merchant_id
    },
    orderArr() {
      let list = this.initPageData()
      let product_type = this.form.product_type
      if (product_type === 1) {
        list.splice(1, 0, {
          keyFlag: 'combo',
          title: this.$t('73096'),
          orderList: ['combo_standalone_pkgs'],
          countObj: {
            combo_standalone_pkgs: {
              mapValue: 'combo_standalone_pkgs',
              callback: (arr) => {
                const validate = (arr || []).every((item) => {
                  const valueValidate =
                    Array.isArray(item.value) && item.value.length > 1 && item.value.every((it) => !!it)
                  return valueValidate
                })
                return validate ? true : ''
              }
            }
          }
        })
      }
      return list
    },
    currOrderArr() {
      return this.orderArr.filter((item) => this.showCurrentCommonFieldFn(item))
    },
    showCopyButton() {
      // 获取是否显示copy按钮的状态
      const { product_type = 0 } = this.form
      const { inventory_type } = this.cacheForm || {}
      return inventory_type != 'INVENTORY' && product_type != 1
    },
    getSpm() {
      if (this.isGuideFlow) {
        return
      }
      const oid = `activity_${this.activity_id || 0}`
      return `PackageInfo?oid=${oid}`
    },
    isCreate: {
      catch: false,
      get: function () {
        return !this.package_id
      }
    }
  },
  watch: {
    form: {
      handler(v) {
        this.$nextTick(function () {
          this.initFloorTimelineData()
          if (!this.cacheForm) {
            this.cacheForm = _.cloneDeep(this.form)
          }
        })
      },
      deep: true
    },
    'form.package_type': {
      immediate: false,
      handler(v) {
        if (this.isCreate) {
          if ([6, 7, 8].includes(v)) {
            this.form.ticket_type = fnb_ticket_types.fnb_open_ticket
          } else if ([9, 10, 11].includes(v)) {
            this.form.ticket_type = fnb_ticket_types.fnb_fixed_date
          }
        }
      }
    },
    'form.validity_model': {
      immediate: true,
      handler(v) {
        this.computedUsageValidityTag()

        this.resetGroupDataAndSummary(['activation_validity', 'participation_validity'])

        // validity_model 为 1 的时候才需要填写 activation_validity，所以切换需要清空数据
        if (v !== 1 && this.fieldAttributeGroupVN?.activation_validity) {
          this.$set(this.form.activation_validity.group_data, 'groups_instance', null)
          this.fieldAttributeGroupVN.activation_validity.initGroupData()
        }
      }
    },
    'form.product_type': {
      handler(v) {
        if (v === 1 && this.isCreate) {
          this.setFormData(this._combo_default_package_info)
        } else if (v === 0) {
          this.setFormData(this._get_packages_basic_info)
        }
      }
    },
    'form.pkg_merchant': {
      handler(v, oldV) {
        if (v && oldV) {
          this.$nextTick(() => {
            this.form.merchant_confirm = 0
          })
        }
        // combo 时强制更新merchant
        if (v && this.form.product_type === 1) {
          klook.bus.$emit('updateMerchants', v)
        }
      }
    },
    // handle default value
    'form.cancellation_policy'(v) {
      if (v == -1) {
        this.form.cancellation_policy = undefined
      }
      // 选择了活动开始前无条件退改则不可选open date ticket类型
      if (v === this.cancel_policy.cancel_before_start && this.form.ticket_type === 3) {
        this.form.ticket_type = undefined
      }
    },
    'form.inventory_type'(v, oldV) {
      if (oldV !== undefined && v !== 'OTHERS') {
        this.form.ticket_type = undefined
      }
    },
    // handle default value
    'form.voucher_usage': {
      immediate: true,
      handler(v) {
        if (v == -1) {
          this.form.voucher_usage = 0
        }
      }
    },
    'form.ticket_type': {
      immediate: true,
      handler(v) {
        if (this.openDate && this.form.confirmation_time !== 1) {
          this.form.cancellation_policy = 9
        }

        switch (v) {
          case 1: {
            this.form.validity_model = 0
            this.computedUsageValidityTag()
            this.resetGroupDataAndSummary(['activation_validity', 'participation_validity'])
            break
          }
          case 3: {
            if (this.isPassProducts && this.form.validity_model === 0) {
              this.form.validity_model = undefined
            }
            this.form.inventory_type = 'OTHERS'
            break
          }
        }
        // 新建的时候切换product_type可能出现展示不正确的情况
        // 分析原因是因为组件未渲染出来导致$refs获取不到，所以未执行初始化函数
        // 使用nextTick的时候又会导致选项选中状态丢失、所以做这个逻辑处理
        if (this.isCreate) {
          this.$nextTick(() => {
            this.computedUsageValidityTag()
          })
        } else {
          this.computedUsageValidityTag()
        }

        this.usageValidityLinkageFn()
      }
    },
    'form.combo_standalone_pkgs': {
      handler(v) {
        // 阻止watch，防止死循环。
        if (this._stop_combo_standalone_skus_watch) {
          this._stop_combo_standalone_skus_watch = false
          return
        }
        this.debounceGetComboBaseInfo(v)
      },
      deep: true
    },
    '$route.query': {
      handler(q, oldQuery) {
        if (+q.package_id !== +oldQuery.package_id) {
          this.initPkgId(+q.package_id)
          this.getRefDataByPkgId()
        }
      }
    }
  },
  async mounted() {
    await this.$nextTick()

    // access_permission: 0, 不可看; 1, read only; 2, editable
    // 当是 merchant update 的时候, 有只读字段需要提示
    if (
      this.isMerchant &&
      !this.isCreate &&
      Object.values(this.schemaConfig).some((item) => item.access_permission === 1)
    ) {
      this.setAlertFixed2provide({
        show: true,
        html: __('48231')
      })
    }
    this.$emit('guideMounted')
  },
  async created() {
    klook.bus.$emit('addHandleSave2bus', this.handleSave, { stepCount: this.$route.query.step_count })
    await this.initPkgId(this.package_id)
    await this.getRefDataByPkgId()

    this.initFloorTimelineData()

    if (this.$root.isHotel) {
      this.package_info.ticket_type = FNB_TICKET_TYPE.fnb_open_ticket
    }

    if (this.form.auto_pub_unpub) {
      let obj = this.form.auto_pub_unpub
      if (obj.package_published) {
        this.lockAutoPublish = true
      } else {
        this.serverTime = this.getServerTime()
        this.startTimestamp = this.getCurrentTimestamp()
        let { published, published_time, suspended_config } = obj
        const { suspended_start_time } = suspended_config
        if (published) {
          // 自动发布或者预热暂停前30分钟都不能操作
          const diffPub = this.getUTCTimestamp(published_time) - this.getCurrentTimestamp()
          const diffSuspend = this.getUTCTimestamp(suspended_start_time) - this.getCurrentTimestamp()

          this.lockAutoPublish = diffPub > 0 && diffPub < this.MAX_AUTO_PUBLISH_TIME
          this.lockAutoWarmUp = diffSuspend > 0 && diffSuspend < this.MAX_AUTO_PUBLISH_TIME
        }
      }
    }
    this.$nextTick(() => {
      this.initLoadFinish = true
    })

    this.$store.dispatch('getActCategory2action', {
      activity_id: this.activity_id
    })
  },
  methods: {
    ...mapGetters(['getPackages', 'getServerTime']),
    ...mapActions(['getPkgInfo2actions', 'updatePkgStepStatus2action']),
    ...mapMutations({
      setPackages: 'setPackages',
      setInvalidWidgetTagsOnField: 'attr/setInvalidWidgetTagsOnField',
      setFilterAttrValOptOnField: 'attr/setFilterAttrValOptOnField',
      setDisabledVarOptionOnField: 'attr/setDisabledVarOptionOnField'
    }),
    validatorDefault: validatorDefault,
    usageValidityLinkageFn() {
      // ticket type 与 group usage_validity 的联动限制
      let res = {
        invalid_attr_value_tags: [],
        invalid_widget_tags: []
      }
      const invalidTags = this.form?.usage_validity?.invalid_tags ?? {}
      if (hasOwnProperty.call(invalidTags, this.form.ticket_type)) {
        res = invalidTags[this.form.ticket_type]
      }
      this.setFilterStore({
        field: 'usage_validity',
        invalidWidgetTags: res.invalid_widget_tags,
        filterAttrValOpt: res.invalid_attr_value_tags
      })
    },
    async resetGroupDataAndSummary(list = []) {
      await this.$nextTick()
      list.forEach((field) => {
        this.orderArr.forEach((item, index) => {
          if (item.orderList.includes(field)) {
            this.$refs?.ref_common_fields?.[index]?.resetGroupDataAndSummary?.([field])
          }
        })
      })
    },
    debounceGetComboBaseInfo: _.debounce(async function (standalone = []) {
      await this.getComboBaseInfo(standalone)
    }, 500),
    computedUsageValidityTag() {
      // 对 ticket type 做联动锁定操作
      // 1  valid_time_options_a 中 {{specific date}} 与 {{specific time}} 联动，锁死specific time，不能编辑
      // 2  如果是 3(no calendar) ，ref_field_tag: "attr_item_specific_date" 不能选择 ref_field_tag: "attr_value_participation_date"
      // x_options_x:   title attr value 选项
      // attr_item_x:   var ref
      // attr_value_x:  var select option ref
      let filterAttrValOpt = []
      let disabledVarOption = [
        'valid_time_options_a-attr_item_specific_time',
        'valid_time_options_a-attr_item_specific_time_same_time'
      ]
      if (this.form.ticket_type === 3) {
        // open ticket without calendar
        filterAttrValOpt.push('valid_time_options_f', 'activation_valid_time_options_d')
        disabledVarOption.push('attr_item_specific_date-attr_value_participation_date')

        if (this.isPassProducts) {
          disabledVarOption.push('attr_item_specific_date_2-attr_value_participation_date_2')
        } else {
          filterAttrValOpt.push('activation_valid_time_options_a')
        }
      }

      if (!this.displayValidityModel) {
        setTimeout(() => {
          klook.bus.$emit('valid_time_options_g', { action: 'autoSelect' })
        })
      }

      if (this.form.validity_model === 1) {
        filterAttrValOpt.push('valid_time_options_d', 'valid_time_options_e', 'valid_time_options_f')
        disabledVarOption.push(
          'attr_item_specific_date-attr_value_participation_date',
          'attr_item_specific_date-attr_value_booking_confirmation_date'
        )
      }

      if (this.form.validity_model === 2) {
        filterAttrValOpt.push(
          'valid_time_options_a',
          'valid_time_options_c',
          'valid_time_options_d',
          'valid_time_options_e',
          'valid_time_options_f',
          'valid_time_options_g',
          'valid_time_options_h'
        )
        disabledVarOption.push(
          'attr_item_specific_date-attr_value_participation_date',
          'attr_item_specific_date-attr_value_redemption_date'
        )

        setTimeout(() => {
          klook.bus.$emit('valid_time_options_b', { action: 'autoSelect' })
        })
      }

      const relatedField = ['usage_validity', 'activation_validity', 'participation_validity']

      for (let field of relatedField) {
        this.setFilterStore({
          field,
          filterAttrValOpt,
          disabledVarOption
        })
      }

      this.resetGroupDataAndSummary(relatedField)
    },
    setFilterStore({ field, invalidWidgetTags, filterAttrValOpt, disabledVarOption }) {
      if (invalidWidgetTags) {
        this.setInvalidWidgetTagsOnField({
          field,
          data: invalidWidgetTags
        })
      }

      if (filterAttrValOpt) {
        this.setFilterAttrValOptOnField({
          field,
          data: filterAttrValOpt
        })
      }

      if (disabledVarOption) {
        this.setDisabledVarOptionOnField({
          field,
          data: disabledVarOption
        })
      }
    },
    getStaticFormData() {
      const staticData = {
        pkg_name: [],
        pkg_subname: [],
        pkg_contact: [],
        package_tips: '',
        price_displaying: 0
      }
      for (const key in staticData) {
        staticData[key] = this.form[key]
      }
      return _.cloneDeep(staticData)
    },
    async getComboBaseInfo(standalone = []) {
      // if (!this.isCreate) {
      //   return
      // }
      // 过滤出有效的 combo_standalone_pkgs
      let list = standalone
        .map((item) => item.value)
        .filter((item) => {
          return Array.isArray(item) && item.length === 2 && item.every((item) => !!item)
        })

      const standalone_pkg_ids = list.map((item) => item[1]).join(',')
      if (!standalone_pkg_ids || standalone_pkg_ids === this._cacheIds) {
        return
      }
      const result = await ajax.getBody(ADMIN_API.act.get_combo_default_basic_info, {
        params: {
          combo_activity_id: this.activity_id,
          standalone_pkg_ids,
          language: klook.getEditLang(),
          ref_language: klook.getRefLang(),
          page_from: klook.getPlatformRoleKey(),
          show_standalone_info: true
        }
      })
      if (!result || !result.success || !result.result) {
        // this.$modal.error({
        //   title: 'Error',
        //   content: result.error && result.error.message
        // })
        return
      }
      let data = result.result
      const form = this.initFormData(data)
      this.comboCancellationPolicy = form.cancellation_policy
      // comboConfirmationTime: combo 后的确认时间，用于筛选出可以选择的项目
      this.comboConfirmationTime = form.confirmation_time
      if (!this.isCreate) {
        return
      }
      // 缓存standalone_pkg_ids
      this._cacheIds = standalone_pkg_ids
      // 接口没有返回这两个字段，在此处加上，并使用form已有的值。
      data.field_items.push(
        {
          access_permission: 2,
          field_key: 'product_type',
          required: true,
          value: this.form.product_type
        },
        {
          access_permission: 2,
          field_key: 'combo_standalone_pkgs',
          required: true,
          value: this.form.combo_standalone_pkgs
        }
      )
      this._staticFormData = this.getStaticFormData()
      this.setFormData(data, true)
    },
    showCurrentCommonFieldFn(item) {
      let orderList = item.orderList || []

      return orderList.some((field) => _.get(this.schemaConfig, `${field}.access_permission`, 0) > 0)
    },
    openFnbTemplate() {
      klook.gotoOldAdmin(
        `/act/package/info${location.search}&activity_id=${this.activity_id}`,
        {},
        { click_func: 'openFnbTemplate' }
      )
    },
    initPageData() {
      let arr = [
        {
          orderList: ['product_type']
        },
        {
          keyFlag: 'basicInfo',
          title: this.$t('activity_basic_page_basic_info'),
          orderList: [
            'pkg_name',
            'package_tips',
            'package_type',
            'pkg_subname',
            'price_displaying',
            'pkg_merchant',
            'merchant_confirm',
            'pkg_contact'
          ],
          countObj: {
            pkg_name: 'pkg_name',
            // pkg_name: {
            //   mapValue: 'pkg_name',
            //   callback: (arr) => {
            //     let list = arr
            //     if (klook.getRefLang() && klook.getRefLang() !== 'en_US') {
            //       list = list.filter((o) => o.language !== klook.getRefLang())
            //     }
            //     let res = list.map((o) => !!_.trim(o.name))
            //     return res.every((o) => o) ? true : ''
            //   }
            // },
            package_tips: 'package_tips',
            package_type: 'package_type',
            pkg_subname: {
              mapValue: 'pkg_subname',
              callback: (arr) => {
                let obj = arr.find((o) => o.language === klook.getEditLang())
                return obj && obj.name
              }
            },
            price_displaying: 'price_displaying',
            pkg_merchant: 'pkg_merchant',
            merchant_confirm: 'merchant_confirm',
            pkg_contact: 'pkg_contact.0.number'
          }
        },
        {
          keyFlag: 'policiesRules',
          title: this.$t(this.$root.isMerchant ? '82054' : 'pkg.policiesRules'),
          orderList: [
            'confirmation_time',
            'inventory_type',
            'voucher_get_method',
            'voucher_type',
            'voucher_usage',
            'voucher_code_level'
          ],
          countObj: {
            confirmation_time: 'confirmation_time',
            inventory_type: 'inventory_type',
            voucher_get_method: 'voucher_get_method',
            voucher_type: 'voucher_type',
            voucher_usage: 'voucher_usage',
            voucher_code_level: 'voucher_code_level'
          }
        },
        {
          keyFlag: 'cancellationPolicySetting',
          title: this.$t('cancellation_policy'),
          orderList: ['cancellation_policy'],
          countObj: {
            cancellation_policy: 'cancellation_policy'
          }
        },
        {
          keyFlag: 'ValiditySetting',
          title: this.$t('30545'),
          orderList: [
            'ticket_type',
            'validity_model',
            'activation_validity',
            'usage_validity',
            'participation_validity'
            // 'field_group_summary'
          ],
          countObj: {
            ticket_type: 'ticket_type',
            validity_model: 'validity_model',
            activation_validity: {
              mapValue: 'activation_validity',
              callback: () => {
                return this.getCountObjectByAttributeFieldKey('activation_validity')
              }
            },
            usage_validity: {
              mapValue: 'usage_validity',
              callback: () => {
                return this.getCountObjectByAttributeFieldKey('usage_validity')
              }
            },
            participation_validity: {
              mapValue: 'participation_validity',
              callback: () => {
                return this.getCountObjectByAttributeFieldKey('participation_validity')
              }
            }
          }
        },
        {
          keyFlag: 'specialSettings',
          title: this.$t('pkg.specialSettings'),
          orderList: [
            'auto_pub_unpub',
            'min_max_bookings',
            'calendar_day',
            'calendar_extend',
            'guarantee_group'
          ],
          countObj: {
            auto_pub_unpub: 'auto_pub_unpub',
            min_max_bookings: 'min_max_bookings',
            calendar_day: 'calendar_day',
            calendar_extend: 'calendar_extend',
            guarantee_group: 'guarantee_group'
          }
        },
        {
          keyFlag: 'checkItems',
          title: this.$t('pkg.checkItems'),
          orderList: [
            'show_account_type',
            'transferable',
            'escrow_guarantee',
            'does_it_include',
            'sensitive_info'
          ],
          countObj: {
            show_account_type: 'show_account_type',
            transferable: 'transferable',
            escrow_guarantee: 'escrow_guarantee',
            does_it_include: {
              mapValue: 'does_it_include',
              callback: (val) => {
                return val === 8 ? undefined : val
              }
            },
            sensitive_info: 'sensitive_info.0'
          }
        }
      ]
      return arr
    },
    initFloorTimelineData: _.debounce(function () {
      let { orderArr } = this
      let obj = {
        floorData: {
          list: orderArr.map((item) => {
            let obj = _.merge({}, item, this.getCountObj(item, item.keyFlag))
            return obj
          })
        }
      }
      const list = obj?.floorData?.list || []
      const isb = list.every((o) => o.requireNum >= o.requireCount)
      this.$emit('updateFinished', isb)
      return obj
    }, 300),
    // 待重写。。。
    getCountObj(data, key) {
      let { form, schemaConfig } = this
      let { countObj = {} } = data
      let countKeys = Object.entries(countObj)
      let num = 0,
        count = 0
      let requireNum = 0,
        requireCount = 0
      let notFilled = []
      countKeys.forEach((arr) => {
        let basic_attr_config = schemaConfig[arr[0]]
        if (basic_attr_config && basic_attr_config.access_permission) {
          count++
          let val = arr[1]
          if (typeof val === 'string') {
            val = _.get(form, val)
          } else {
            val = val.callback(_.get(form, val.mapValue))
          }
          let isAdd = val || typeof val === 'number' || typeof val === 'boolean'
          isAdd && num++
          if (basic_attr_config.required) {
            requireCount++
            isAdd && requireNum++
          }

          if (!isAdd) {
            const { field_key, required, config } = basic_attr_config
            notFilled.push({
              field: field_key,
              required,
              config
            })
          }
        }
      })
      //特殊处理 start
      if (key == 'policiesRules') {
        // 修复count / num 可能出现负数或不准确的情况
        // 在选择了库存模型的情况下count / num / requireCount / requireNum 可能出现计算两次的的情况
        let mapList = ['voucher_type', 'voucher_code_level', 'voucher_usage']
        switch (form['inventory_type']) {
          case 'API':
            mapList = ['voucher_usage']
            ;['voucher_type', 'voucher_code_level'].forEach((k) => {
              count--
              form[k] !== undefined && num--
              if (schemaConfig[k].required) {
                requireCount--
                form[k] !== undefined && requireNum--
              }
            })
            break
          case 'INVENTORY':
            mapList = ['voucher_code_level', 'voucher_usage']
            ;['voucher_type'].forEach((k) => {
              count--
              form[k] !== undefined && num--
              if (schemaConfig[k].required) {
                requireCount--
                form[k] !== undefined && requireNum--
              }
            })
            break
          default:
          //
        }
        if (form['voucher_get_method'] !== 0) {
          mapList.forEach((k) => {
            if (schemaConfig[k] && schemaConfig[k].access_permission) {
              count--
              form[k] !== undefined && num--
              if (schemaConfig[k].required) {
                requireCount--
                form[k] !== undefined && requireNum--
              }
            }
          })
        }
      }
      //特殊处理 end
      let obj = {
        num,
        count,
        requireNum,
        requireCount,
        require: !!requireCount,
        notFilled
      }
      return obj
    },
    async getPackagesBasicInfo(params = {}) {
      return await ajax.get(ADMIN_API.act.get_packages_basic_info, { params })
    },
    async getRefDataByPkgId() {
      if (this.isMerchant) {
        return
      }

      const { package_id, refer_language } = this
      if (package_id && refer_language) {
        let result = await this.getPackagesBasicInfo({
          package_id: +package_id,
          language: refer_language,
          refer_language: klook.getEditLang(),
          page_from: klook.getPlatformRoleKey()
        })

        let formData = this.initFormData(result || {})
        this.$set(this, 'refForm', { ...formData })
      }
    },
    initFormData({ field_items = [] }) {
      let obj = {}
      field_items.forEach((item) => {
        obj[item.field_key] = item.value
      })
      return _.cloneDeep(obj)
    },
    async initPkgId(package_id) {
      const { pkgSectionData } = this
      let reqData = {
        default_package_info: pkgSectionData
      }
      this.reqData = reqData

      this.defaultPackageInfo = reqData.default_package_info
      let result = pkgSectionData

      this._get_packages_basic_info = result || {}
      this._combo_default_package_info = reqData.combo_default_package_info || {}
      if (result) {
        this.merchant_id = result.merchant_id
        this.initGroupData(result)
        await this.setFormData(this._get_packages_basic_info)
      }
    },
    initGroupData(result) {
      const groupsData = result?.option_group?.group_data ?? null

      if (!groupsData) {
        this.optionGroup = {}
        return
      }

      if (!groupsData.groups_instance) {
        groupsData.groups_instance = {}
      }

      groupsData.groups_summary.forEach((group) => {
        let { widgetsData } = this.getCurrentGroupData({
          group_id: group.id,
          data: groupsData,
          getEditData: false
        })
        groupsData.groups_instance[group.id] = widgetsData
      })

      this.optionGroup = result.option_group
    },
    async setFormData(result, isCombo = false) {
      const {
        print_mobile_voucher,
        80294: evoucher_offline_voucher,
        80333: print_mobile_offline_voucher
      } = voucher_usages
      let formData = this.initFormData(result)
      // 特殊字段处理 start
      let cpForm = _.merge({}, formData, {
        sensitive_info: formData.sensitive_info ? formData.sensitive_info.split(',') : [],
        // - 如果用户没有勾选 offline voucher，使用默认值 E-voucher supported (0)
        // - 如果用户勾选 offline voucher，则使用 E-voucher + offline voucher supported (4)
        voucher_usage: [evoucher_offline_voucher, print_mobile_offline_voucher].includes(
          formData.voucher_usage
        )
          ? print_mobile_offline_voucher
          : print_mobile_voucher
      })
      if (cpForm.pkg_contact && !cpForm.pkg_contact.length) {
        // obt存在且为空数组
        this.$set(cpForm, 'pkg_contact', [
          {
            media: 'phone',
            number: ''
          }
        ])
      }
      // 给combo_standalone_skus加上默认值，最少1个列表
      // 默认语言 en_US klook.getEditLang()
      if (this.isCreate && !Array.isArray(formData.combo_standalone_pkgs) && !isCombo) {
        this.$set(cpForm, 'combo_standalone_pkgs', [
          {
            package_id: undefined,
            i18ns: [...getComboNameI18ns()]
          }
        ])
      }
      // 如果是combo套餐，初始化value值
      if (!this.isCreate && Array.isArray(formData.combo_standalone_pkgs)) {
        this.$set(
          cpForm,
          'combo_standalone_pkgs',
          formData.combo_standalone_pkgs.map((item) => {
            item.value = [item.activity_id, item.package_id]
            item.i18ns = item.i18ns.sort((item) => (item.language === 'en_US' ? 1 : -1))
            return item
          })
        )
      }

      if (hasOwnProperty.call(cpForm, 'calendar_day') && !cpForm.calendar_day) {
        cpForm.calendar_day =
          this.defaultPackageInfo.field_items.find((item) => item.field_key === 'calendar_day')?.value ??
          undefined
      }

      // 特殊字段处理 end

      /**
       * staticFormData: 保存修改combo_standalone_pkgs前的form数据
       */
      let staticFormData = {}
      if (isCombo) {
        staticFormData = this._staticFormData || {}
      }
      this._stop_combo_standalone_skus_watch = isCombo
      this.$set(this, 'form', {
        ...this.form,
        ...cpForm,
        ...staticFormData
      })
      await this.initObConfig(result)
      this.usageValidityLinkageFn()
    },
    getCurrentTimestamp() {
      return this.getUTCTimestamp(moment().format('YYYY-MM-DD HH:mm:ss'))
    },
    getUTCTimestamp(date) {
      return +moment.utc(date).valueOf() || 0
    },
    async publishValidator() {
      if (this.lockAutoPublish || !this.package_info.auto_pub_unpub) {
        // If start locking, it cannot update, pass
        return true
      }
      let currentServerTimestamp = this.getCurrentTimestamp() - this.startTimestamp + this.serverTimestamp
      let diff =
        this.getUTCTimestamp(this.package_info.auto_pub_unpub.published_time) - currentServerTimestamp
      if (diff > 0 && diff < this.MAX_AUTO_PUBLISH_TIME) {
        await this.$modal.info({
          title: this.$t('auto_publish_unpublish_tips_3')
        })
        return false
      }
      return true
    },
    async invalidAutoPublish() {
      let autoPublishValid = await this.publishValidator()
      if (!autoPublishValid) {
        await this.refreshPkgs()
        setTimeout(() => {
          this.refreshPage()
        })
        return true
      }
      return false
    },
    async handleSaveCopy({ source }) {
      if (await this.invalidAutoPublish()) {
        return
      }
      let { target } = await ajax.post(ADMIN_API.act.copy_package, {
        data: {
          source,
          activity_id: +this.activity_id,
          edit_language: getEditLang(),
          target: !this.package_id ? [] : [+this.package_id]
        }
      })

      this.$router.replace({
        path: `/act/package/info/${this.activity_id}`,
        query: {
          ...this.$route.query,
          package_id: target[0]
        }
      })
      await this.refreshPkgs()
      setTimeout(() => {
        this.$store.commit('attr/setGroupSelectionStore')
        this.refreshPage()
      })
    },
    validGuaranteeGroup(rule, value, callback) {
      // 最小值为必填项
      if (this.form.ticket_type === 1) {
        const { min_group = 0, max_group = 0 } = this.form.guarantee_group
        // 允许max_group 为空, 最小为1
        if (_.isNumber(min_group) && min_group > 0) {
          if (_.isNumber(max_group) && max_group > 0 && max_group < min_group) {
            callback(new Error(__('81816')))
          } else {
            callback()
          }
        } else if (_.isNumber(max_group) && min_group === 0 && _.isNumber(max_group) && max_group > 0) {
          callback(new Error(__('85441')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    validMinAndMaxBooking(rule, value, callback) {
      const { min_participants, max_participants } = this.form.min_max_bookings

      if (
        _.isNumber(min_participants) &&
        _.isNumber(max_participants) &&
        min_participants >= 0 &&
        max_participants >= 0
      ) {
        if (max_participants === 0) {
          callback(new Error(__('80128')))
        } else {
          callback()
        }
      } else {
        callback(new Error('Please input number >= 0'))
      }
    },
    validLevy(rule, value, callback) {
      if (this.form.does_it_include === 8) {
        callback(new Error('Please fill this form'))
      } else {
        callback()
      }
    },
    validMerchantContact(rule, value, callback) {
      if (!_.isArray(value)) {
        this.formValid = false
      } else {
        this.formValid = value.every((item, index) => {
          if (value.length === 1 && index === 0) {
            // 对于第一个对象：
            // true: 都填 或 都不填 或 只填了media
            // false: 只填了number
            return !(_.isEmpty(item.media) && !_.isEmpty(item.number))
          } else {
            // 对于其它对象：
            // true: 都填
            // false: 只填了某一个 或 都不填
            return !(_.isEmpty(item.media) || _.isEmpty(item.number))
          }
        })
        if (this.formValid) {
          callback()
        } else {
          callback(new Error('Please input contact'))
        }
      }
    },
    async initObConfig({ field_items = [] }) {
      let result = field_items
      this.schemaConfig = {
        ...result.reduce((acc, curr) => {
          let validator
          switch (curr.field_key) {
            case 'pkg_contact': {
              validator = this.validMerchantContact
              break
            }
            case 'does_it_include': {
              validator = this.validLevy
              break
            }
            case 'min_max_bookings': {
              validator = this.validMinAndMaxBooking
              break
            }
            case 'combo_standalone_pkgs':
              validator = (rule, value, callback) => {
                const validate = value.every((item) => {
                  const valueValidate =
                    Array.isArray(item.value) && item.value.length > 1 && item.value.every((it) => !!it)
                  return valueValidate
                })
                if (!validate && this.isCreate) {
                  callback(new Error('Please fill this form'))
                } else {
                  callback()
                }
              }
              break
            case 'escrow_guarantee_photo':
              validator = (rule, value, callback) => {
                if (!this.form.escrow_guarantee) {
                  return callback()
                }
                if (rule.required && (!Array.isArray(value) || value.length === 0)) {
                  callback(new Error('Please fill this form'))
                } else {
                  callback()
                }
              }
              break
            case 'voucher_usage':
              validator = (rule, value, callback) => {
                const { print_mobile_voucher, 80333: print_mobile_offline_voucher } = voucher_usages
                if ([print_mobile_voucher, print_mobile_offline_voucher].includes(this.form.voucher_usage)) {
                  return callback()
                }

                callback(new Error('Please fill this form'))
              }
              break
            case 'guarantee_group':
              validator = this.validGuaranteeGroup
              break
            default:
              validator = this.validatorDefault
          }
          return {
            ...acc,
            [curr.field_key]: {
              ...curr,
              config: (curr.config && JSON.parse(curr.config)) || {},
              ...(curr.field_key === 'pkg_name' // 只有套餐名才是多语言组件
                ? {
                    onlyEditLanguage: this.$root.isMerchant,
                    required: curr.required,
                    requiredEn: true
                  }
                : {
                    // 其余的需要有对应的 rules 去判断
                    rules: {
                      trigger: 'blur',
                      required: curr.required,
                      validator
                    }
                  })
            }
          }
        }, {})
      }
    },
    fmtPostData() {
      let data = _.cloneDeep(this.form)
      // 还原数据格式 start
      data.sensitive_info && (data.sensitive_info = data.sensitive_info.join())
      if (
        data.pkg_contact &&
        data.pkg_contact.length === 1 &&
        ((data.pkg_contact[0].media && _.isEmpty(data.pkg_contact[0].number)) ||
          (_.isEmpty(data.pkg_contact[0].media) && _.isEmpty(data.pkg_contact[0].number)))
      ) {
        data.pkg_contact.splice(0)
      }

      // 还原数据格式 end
      for (let key in data) {
        let obt = this.schemaConfig[key]
        // 去除obt没有的字段
        if (!hasOwnProperty.call(this.schemaConfig, key)) {
          delete data[key]
        } else {
          //非必填字段类型校验
          if (obt && obt.required === false && data[key] === undefined) {
            // 非必填、类型不一致(v-check-val转为undefined)，则不返回给后端作为参数
            delete data[key]
          }
        }
      }

      // combo_standalone_pkgs 格式化
      if (Array.isArray(data.combo_standalone_pkgs)) {
        data.combo_standalone_pkgs = data.combo_standalone_pkgs.map((item) => {
          return {
            i18ns: item.i18ns.map((it) => {
              if (this.isCreate) {
                it.id = item.value[1]
              }
              delete it.lock
              return it
            }),
            package_id: this.isCreate ? item.value[1] : item.package_id
          }
        })
      }

      if (data.voucher_get_method === 1 && hasOwnProperty.call(data, 'voucher_type')) {
        // 无凭证
        data.voucher_type = -1
      }

      if (this.form.voucher_get_method !== 0) {
        // !== e_voucher
        data.voucher_usage = 0
      }

      // 台湾履保证明加上默认值
      if (data.escrow_guarantee === false && hasOwnProperty.call(this.form, 'escrow_guarantee_photo')) {
        data.escrow_guarantee_photo = []
      }

      let option_groups = []
      let field_items = Object.entries(data).map((arr) => {
        let field_key = arr[0]

        const fieldAttrCompList = [
          'activation_validity',
          'usage_validity',
          'participation_validity',
          'pkg_unique_selling_point'
        ]
        if (fieldAttrCompList.includes(field_key)) {
          return {
            field_key,
            value:
              (this.fieldAttributeGroupVN[field_key] &&
                this.fieldAttributeGroupVN[field_key].getGroupPostData()) ||
              arr[1]
          }
        }

        const optAttrCompList = ['cancellation_policy', 'confirmation_time', 'voucher_get_method']
        if (optAttrCompList.includes(field_key)) {
          const postData = this.fieldAttributeGroupVN[field_key]?.getGroupPostData?.()

          if (postData) {
            option_groups = [...option_groups, ...postData.groups]
          }
        }

        return {
          field_key,
          value: arr[1]
        }
      })
      return {
        activity_id: this.activity_id,
        field_items,
        language: klook.getEditLang(),
        package_id: this.package_id,
        page_from: klook.getPlatformRoleKey(),
        option_groups
      }
    },
    async updatePackage(modify_id) {
      let published_time = _.get(this.form, 'auto_pub_unpub.published_time', '')
      if (
        this.form.published &&
        this.form.auto_pub_unpub.is_set &&
        published_time &&
        this.getUTCTimestamp(published_time) - this.getCurrentTimestamp() > 0
      ) {
        this.$modal.info({
          title:
            'Can not set auto-publish for an already published package, please unpublish current package first.'
        })
        klook.abort()
      }
      let data = this.fmtPostData()
      let res = await ajax.postBody(
        ADMIN_API.act.save_package_basic_info,
        {
          noDefaultResponseInterceptor: true,
          data: {
            ...data,
            modify_id,
            language: getEditLang()
          }
        },
        {
          loading: true
        }
      )
      // 特殊错误不刷新页面
      if (res && !res?.success && res?.error?.code === '50085') {
        const errorDom = document.getElementById('IsCPTypeFixedDate')
        errorDom?.scrollIntoView?.({
          behavior: 'smooth',
          block: 'center'
        })
        return
      }

      klook.bus.$emit('sendGuideCreateSave2bus', this.calcCurrStepData, res)

      if (!res.success) {
        await this.refreshPkgs()
        this.$nextTick(() => {
          this.refreshPage()
        })
        klook.abort()
        return false
      }

      // 更新 store
      this.setPackages(
        this.getPackages().map((item) => {
          if (item.package_id === data.package_id) {
            return {
              ...item,
              ...data
            }
          }
          return item
        })
      )
      return true
    },
    async addPackage(data = null) {
      let confirm = true
      if (this.form.product_type === 1) {
        this.rootChangeLoading(false)
        confirm = await new Promise((resolve) => {
          this.$modal.confirm({
            title: 'Notice',
            content: this.$t('73101'),
            okText: this.$t('73102'),
            cancelText: this.$t('73103'),
            icon: () => <a-icon type="info-circle" style="color: #437DFF" />,
            onOk() {
              resolve(true)
            },
            onCancel() {
              resolve(false)
            }
          })
        })
      }
      if (!confirm) {
        return false
      }
      this.rootChangeLoading(true)
      data = data ? data : this.fmtPostData()
      let result = await ajax.post(
        ADMIN_API.act.create_package,
        {
          data
        },
        {
          msgOpt: {
            isModal: true
          }
        }
      )
      if (result && result.package_id) {
        this.new_package_id = result.package_id
        this.cacheForm = _.cloneDeep(this.form)
        return true
      } else {
        return false
      }
    },
    routerInfo() {
      let package_type = (this._get_packages_basic_info && this._get_packages_basic_info.package_type) || ''
      let query = {
        ...this.$route.query,
        package_id: '' + this.new_package_id, //保证package_id类型一致
        package_type
      }
      if (!_.isEqual(query, this.$route.query)) {
        // 旧数据存在需要重新选 package_type 的情况
        this.$router.replace({
          path: `/act/package/info/${this.activity_id}`,
          query
        })
      }
    },
    async getValidateRes() {
      let ref_common_fields = this.$refs.ref_common_fields
      let validateRes = ref_common_fields
        ? await Promise.all(
            ref_common_fields.map(async (o) => {
              return o.validate(true)
            })
          )
        : [true]
      return validateRes
    },
    async handleSave() {
      let modify_id = klook.getModifyId()
      if (await this.invalidAutoPublish()) {
        return
      }
      let validateRes = await this.getValidateRes()
      if (validateRes.every((item) => item)) {
        if (
          isAdmin &&
          !this.isCreate &&
          this.cacheForm.pkg_merchant &&
          this.cacheForm.pkg_merchant !== this.form.pkg_merchant
        ) {
          let temp = await pmsConfirm.call(this, {
            content: this.$t('48159')
          })

          if (!temp) {
            return
          }
        }

        this.rootChangeLoading(true)
        if (!(await this.updatePackage(modify_id))) {
          return false
        }

        klook.bus.$emit('updateBasicInfoByPid2bus')
        klook.bus.$emit('setStepStatusFinished2bus', this.$route.query.step_count, {
          package_id: this.package_id
        })
        this.rootChangeLoading(false)
        return true
      } else {
        this.$message.warn(__('package_error_mandatory'))
        this.$nextTick(() => {
          scrollElError()
        })
        return
      }
    },
    calcAttrGroupDisplayState(field, form) {
      switch (field) {
        case 'activation_validity':
          return form.validity_model === 1
        case 'usage_validity':
          return form.ticket_type !== undefined
        case 'participation_validity':
          return form.validity_model === 2
      }

      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.common-guide-basic-box {
  margin: 0;
  .title {
    margin: 0;
  }
}
</style>
