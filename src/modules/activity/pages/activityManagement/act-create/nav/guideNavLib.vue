<script>
const getRoleData2actList = function (roleKey) {
  const arr = [
    {
      step: 'activity_create',
      require: true,
      key: 'activityCreate',
      text: __('activity_basic_page_basic_info'),
      path: '/act/create/'
    },
    {
      step: 'activity_destination',
      require: true,
      key: 'activityDestination',
      text: __('83826'),
      path: '/act/create/'
    },
    {
      step: 'activity_photo',
      require: true,
      key: 'activityPhoto',
      text: __('83900'),
      path: '/act/create/'
    },
    {
      step: 'activity_selling_point',
      require: true,
      key: 'activitySellingPoint',
      text: __('83833'),
      path: '/act/create/'
    },
    {
      step: 'activity_about',
      require: false,
      key: 'activityAbout',
      text: __('83834'),
      path: '/act/create/'
    }
  ]

  let filterArr = klook.getRoleKeysData(arr, klook.getPlatformAuthKeys())
  //   console.log(999, roleKey, filterArr.length, filterArr);
  return filterArr
}
const getRoleData2pkgList = function (roleKey) {
  const arr = [
    {
      step: 'package_main_info',
      require: true,
      key: 'packageInfo',
      text: __('82054'),
      path: '/act/create/'
    },
    {
      step: 'package_model',
      require: true,
      key: 'packageModel',
      text: __('81825'),
      path: '/act/create/'
    },
    {
      step: 'supply_api_mapping',
      require: true,
      key: 'packageSupplyApiMapping',
      text: __('172677'),
      path: '/act/create/'
    },
    {
      step: 'extra_information',
      require: true,
      key: 'extraInfomation',
      text: __('83902'),
      path: '/act/create/'
    }
  ]
  let filterArr = klook.getRoleKeysData(arr, klook.getPlatformAuthKeys())
  //   console.log(999, roleKey, filterArr.length, filterArr);
  return filterArr
}

const vm = {
  _getRoleData2actList: getRoleData2actList,
  _getRoleData2pkgList: getRoleData2pkgList,
  data() {
    return {}
  },
  methods: {
    getRoleData(key, options) {
      let roleKey = options ? options.roleKey : klook.getPlatformRoleKey()
      // return vm['_getRoleData2' + key].call(this, 'admin') // test code
      return vm['_getRoleData2' + key].call(this, roleKey)
    }
  }
}
export default vm
</script>
