<template>
  <div class="guide-nav">
    <div class="guide-nav-box">
      <a-collapse v-model="collapseKeys" :bordered="false" class="guide-collapse">
        <template #expandIcon="props">
          <a-icon type="caret-right" :rotate="props.isActive ? 90 : 0" />
        </template>
        <a-collapse-panel key="act" class="guide-collapse-panel">
          <div slot="header">
            <div class="guide-collapse__header">
              <span>{{ actObj.header }}</span>
              <span
                v-if="actObj.require"
                :class="['common-icon-status', calcAllActStepFinished ? 'status-success' : 'status-warn']"
              >
                <a-icon
                  :type="calcAllActStepFinished ? 'check-circle' : 'exclamation-circle'"
                  theme="filled"
                ></a-icon>
              </span>
            </div>
          </div>
          <div
            v-for="(item, i) in actObj.list"
            :key="i"
            :class="['guide-collapse-panel__content', calcCount === i ? 'on' : '']"
          >
            <div class="guide-collapse__header content-box">
              <span class="link">{{ item.text }}</span>
              <span
                :class="[
                  'common-icon-status',
                  calcActStepStatus(item.step) ? 'status-success' : 'status-warn'
                ]"
              >
                <a-icon :type="calcActStepStatus(item.step) ? 'check-circle' : ''" theme="filled"></a-icon>
              </span>
            </div>
          </div>
        </a-collapse-panel>
        <template v-if="packagesList && packagesList.length">
          <a-collapse-panel
            v-for="(pkg, i) in packagesList"
            :key="`pkg-${pkg.package_id}`"
            class="guide-collapse-panel"
            :disabled="calcCurrentPkgStepDisabled(i)"
          >
            <div slot="header">
              <div class="guide-collapse__header">
                <span>{{ `${$t('79661')} ${i + 1}: ${pkg.package_title}` }}</span>
                <span
                  v-if="pkgDefined.require"
                  :class="[
                    'common-icon-status',
                    calcCurrentPkgStepDisabled(i)
                      ? 'status-lock'
                      : calcAllPkgStepFinished(pkg.package_id)
                      ? 'status-success'
                      : 'status-warn'
                  ]"
                >
                  <a-icon
                    :type="
                      calcCurrentPkgStepDisabled(i)
                        ? 'lock'
                        : calcAllPkgStepFinished(pkg.package_id)
                        ? 'check-circle'
                        : 'exclamation-circle'
                    "
                    theme="filled"
                  ></a-icon>
                </span>
              </div>
            </div>
            <div
              v-for="(item, idx) in pkg.steps"
              :key="idx"
              class="guide-collapse-panel__content"
              :class="pkg.package_id === currentPid && calcOnePkgStepNum === idx ? 'on' : ''"
            >
              <div class="guide-collapse__header content-box">
                <span class="link">{{ item.text }}</span>
                <span
                  :class="[
                    'common-icon-status',
                    calcPkgStepStatus(item.step, pkg.package_id) ? 'status-success' : 'status-warn'
                  ]"
                >
                  <a-icon
                    :type="calcPkgStepStatus(item.step, pkg.package_id) ? 'check-circle' : ''"
                    theme="filled"
                  ></a-icon>
                </span>
              </div>
            </div>
          </a-collapse-panel>
        </template>
      </a-collapse>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import StepMixin from '@activity/pages/activityManagement/act-create/step-mixin.js'
import { state as pkgsState } from '@activity/store/pkgs-cache-observable.js'

export default {
  name: 'ActCreateNav',
  mixins: [StepMixin],
  props: {
    language: {
      type: String,
      default: ''
    },
    activityId: {
      type: Number,
      default: 0
    },
    currentPid: {
      type: Number,
      default: 0
    }
  },
  watch: {
    activityId: {
      immediate: true,
      handler(v) {
        this.collapseKeys = ['act']
      }
    },
    currentPid: {
      handler(v) {
        if (v) {
          this.collapseKeys = [`pkg-${v}`]
        } else {
          this.collapseKeys = ['act'] // back to act
        }
      }
    }
  },
  // eslint-disable-next-line vue/order-in-components
  computed: {
    ...mapGetters(['getActStepStatus']),
    actObj() {
      return {
        header: __('global_activity'),
        require: true,
        list: this.actSteps
      }
    },
    pkgDefined() {
      return {
        header: '',
        require: true
      }
    },
    calcCurrentPkgStepDisabled() {
      return (i) => {
        const { packagesList } = this
        if (i === 0) {
          return !this.calcAllActStepFinished
        } else {
          const pid = packagesList[i - 1]?.package_id
          return !this.calcAllPkgStepFinished(pid)
        }
      }
    },
    calcCollapseList() {
      const { calcStepType, calcPid } = this
      const isAct = calcStepType === 'step_activity'
      if (isAct) {
        return ['act']
      }
      return [`pkg-${calcPid}`]
    },
    calcAllActStepFinished() {
      const { actObj, getActStepStatus } = this
      const res = actObj?.list.every((item) => {
        const isb = item.require ? !!getActStepStatus[item.step] : true
        return isb
      })
      return res
    },
    calcPkgStepStatusData() {
      return (pid) => {
        const { pkgsCacheState } = this
        const pkgStepStatus = pkgsCacheState?.[pid]?.[ADMIN_API.act.get_package_step_status] || {}
        return pkgStepStatus
      }
    },
    calcAllPkgStepFinished() {
      return (pid) => {
        const currPackage = this.packagesList.find((pkg) => pkg.package_id === pid)
        const pkgStepStatus = this.calcPkgStepStatusData(pid)

        return currPackage.steps.every((item) => (item.require ? !!pkgStepStatus[item.step] : true))
      }
    },
    calcActStepStatus() {
      return (stepKey) => {
        const { getActStepStatus } = this
        return getActStepStatus?.[stepKey]
      }
    },
    calcPkgStepStatus() {
      return (stepKey, pid) => {
        const pkgStepStatus = this.calcPkgStepStatusData(pid)
        return pkgStepStatus?.[stepKey]
      }
    }
  },
  data() {
    return {
      pkgsCacheState: pkgsState['pkgsCache']
    }
  }
}
</script>

<style lang="scss" scoped>
.common-icon-status {
  &.status-warn {
    color: #f09b0a;
  }
  &.status-success {
    color: #08b371;
  }
  &.status-lock {
    color: #212121;
  }
}
.guide-collapse ::v-deep {
  .ant-collapse-header {
    padding: 9px 0 9px 24px !important;
  }
}
.guide-collapse.ant-collapse-borderless ::v-deep > .ant-collapse-item {
  border-bottom: none;
}
.guide-collapse {
  &__header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    .common-icon-status {
      flex: none;
      margin-left: 10px;
    }
    &.content-box {
      padding: 5px 0 5px 24px;
      font-size: 14px;
      line-height: 20px;
      color: #8c8c8c;
      // cursor: pointer;
    }
  }
}
.guide-collapse-panel {
  &__content.on {
    .content-box {
      color: #437dff;
      font-weight: 600;
    }
  }
}
.guide-nav {
  //
}
</style>
