import { creationStepKeyDict } from '@activity/pages/activityManagement/act-create/utils/index.js'

export default {
  inject: ['getOnePkgStepNum'],
  props: {
    packagesList: {
      required: true,
      type: Array,
      default: () => []
    },
    stepData: {
      required: true,
      type: Object,
      default() {
        return {
          stepCount: 0,
          type: 'step_activity' // step_activity | step_package
        }
      }
    },
    actSteps: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      creationStepKeyDict
    }
  },
  computed: {
    calcPidList() {
      return this.packagesList || []
    },
    calcCount() {
      return klook.parse2id(this.$route.query.step_count) || 0
    },
    calcMaxCount() {
      const { packagesList, calcActStepNum } = this
      const pkgLength = packagesList.reduce((acc, curr) => (acc += curr.steps.length), 0)

      return calcActStepNum + pkgLength - 1
    },
    calcActStepNum() {
      return this.actSteps.length
    },
    calcCurrPkg() {
      const { packagesList, calcPid } = this
      return packagesList.find((pkg) => pkg.package_id === calcPid) || {}
    },
    calcCurrPkgStepNum() {
      const { packagesList, calcCount } = this
      return this.getOnePkgStepNum(calcCount, packagesList)
    },
    calcOnePkgStepNum() {
      const { packagesList, calcCount } = this
      return this.getOnePkgStepNum(calcCount, packagesList)
    },
    calcCurrPkgStepInfo() {
      return this.calcCurrPkg?.steps?.[this.calcCurrPkgStepNum]
    },
    calcCurrent() {
      const currentMap = {
        step_activity: 0,
        step_package: 1
      }
      return currentMap[this.calcStepType] || 0
    },
    calcDonePkgIdx() {
      const { calcCount, packagesList, calcActStepNum } = this
      if (calcCount < calcActStepNum) {
        return 0
      }

      let remainNum = calcCount - calcActStepNum
      for (let idx in packagesList) {
        idx = +idx
        const pkg = packagesList[idx]
        remainNum = remainNum - pkg.steps.length

        if (remainNum === 0) {
          return idx + 1
        }

        if (remainNum < 0) {
          return idx
        }
      }
    },
    calcPkgData() {
      const { calcStepType, calcDonePkgIdx, packagesList } = this
      if (calcStepType === 'step_activity' || !packagesList?.length) {
        return {}
      }
      return packagesList[calcDonePkgIdx] || {}
    },
    calcPid() {
      return this.calcPkgData?.package_id || 0
    },
    calcAid() {
      return klook.parse2id(this.$route.query?.activity_id) || 0
    },
    calcStepType() {
      return this.stepData.type
    },
    calcCurrStepData() {
      const { calcStepType, actSteps, calcCount, calcCurrPkgStepInfo } = this
      return calcStepType === 'step_activity' ? actSteps[calcCount] : calcCurrPkgStepInfo
    }
  },
  watch: {
    calcPid: {
      immediate: true,
      handler(v) {
        this.$emit('updatePid', v)
      }
    }
  }
}
