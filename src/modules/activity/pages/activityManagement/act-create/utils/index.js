export const creationStepKeyDict = {
  package_main_info: 'package_main_info',
  package_model: 'package_model',
  supply_api_mapping: 'supply_api_mapping',
  extra_information: 'extra_information'
}

export const getGuideCreationStepList = () => {
  return {
    act: [
      {
        step: 'activity_create',
        require: true,
        key: 'activityCreate',
        text: __('activity_basic_page_basic_info'),
        path: '/act/create/',
        spm: 'NewCreationSaveBasicInfo'
      },
      {
        step: 'activity_destination',
        require: true,
        key: 'activityDestination',
        text: __('83826'),
        path: '/act/create/',
        spm: 'NewCreationSaveLocation'
      },
      {
        step: 'activity_photo',
        require: true,
        key: 'activityPhoto',
        text: __('83900'),
        path: '/act/create/',
        spm: 'NewCreationSavePhoto'
      },
      {
        step: 'activity_selling_point',
        require: true,
        key: 'activitySellingPoint',
        text: __('83833'),
        path: '/act/create/',
        spm: 'NewCreationSaveHighlights'
      },
      {
        step: 'activity_about',
        require: false,
        key: 'activityAbout',
        text: __('83834'),
        path: '/act/create/',
        spm: 'NewCreationSaveDetails'
      }
    ],
    pkg: [
      {
        step: 'package_main_info',
        require: true,
        key: 'packageInfo',
        text: __('82054'),
        path: '/act/create/',
        spm: 'NewCreationSavePackageInfo'
      },
      {
        step: 'package_model',
        require: true,
        key: 'packageModel',
        text: __('81825'),
        path: '/act/create/',
        spm: 'NewCreationSavePrice'
      },
      // 如果不支持会过滤
      // @activity/pages/activityManagement/act-create/index.vue#268
      {
        step: creationStepKeyDict.supply_api_mapping,
        require: true,
        key: 'packageSupplyApiMapping',
        text: __('172677'),
        path: '/act/create/',
        spm: 'packageSupplyApiMapping'
      },
      {
        step: creationStepKeyDict.extra_information,
        require: true,
        key: 'extraInfomation',
        text: __('83902'),
        path: '/act/create/',
        spm: 'SubmitAllPackage'
      }
    ]
  }
}

export const defaultSupplyApiMappingSupportConfig = {
  support_supply_api_mapping: 0, // 0 隐藏, 1 只读, 2 读写
  is_required: false
}
