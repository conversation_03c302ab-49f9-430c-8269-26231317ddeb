<template>
  <a-modal
    v-model="_visible"
    :mask-closable="maskClosable"
    :cancel-text="$t('global_cancel')"
    :ok-text="$t('global_confirm')"
    :ok-button-props="{
      props: {
        disabled: !checked
      }
    }"
    @ok="onOk"
    @cancel="onCancel"
  >
    <div class="act-terms-box">
      <p class="act-terms-box-content" v-html="content"></p>
      <a-checkbox v-model="checked"><span v-html="$t('27719')"></span></a-checkbox>
    </div>
  </a-modal>
</template>

<script>
export default {
  name: 'ActTerms',
  model: {
    prop: 'visible',
    event: 'change'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    content: {
      type: String,
      default: __('27716')
    },
    maskClosable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      checked: false
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('change', val)
      }
    }
  },
  methods: {
    onOk() {
      this.$emit('ok')
      this.resetVal()
    },
    onCancel() {
      this.$emit('cancel')
      this.resetVal()
    },
    resetVal() {
      this.checked = false
      this._visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.act-terms-box {
  margin-top: 60px;
  &-content {
    background: #fafafa;
    margin-bottom: 30px;
    padding: 18px 10px;
  }
}
</style>
