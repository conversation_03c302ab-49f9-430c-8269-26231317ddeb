<template>
  <div class="new-act">
    <div class="new-act-box">
      <createModal :modal-data="modalData" target="_self" />
    </div>
  </div>
</template>

<script>
import createModal from '../create/modal'
export default {
  components: { createModal },
  data() {
    return {
      modalData: {
        visible: true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.new-act {
  .new-act-box {
    //
  }
}
</style>
