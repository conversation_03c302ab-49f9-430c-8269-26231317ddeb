<template>
  <div class="table-wrapper">
    <a-table
      :columns="addColumn"
      :data-source="dataSource"
      :pagination="false"
      :scroll="scroll"
      :row-key="(record, index) => index"
      size="middle"
    >
      <div slot="sku_name_lang" slot-scope="text, row" :title="`${row.value}-${text}`" class="ellipsis">
        {{ text }}
      </div>
    </a-table>
  </div>
</template>
<script>
import { addColumn } from '../../utils/index'

export default {
  props: {
    dataSource: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      addColumn,
      scroll: {
        x: 0,
        y: 322
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.table-wrapper {
  max-height: 368px;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
<style lang="scss">
th.cell-after-line {
  position: relative;
  &::after {
    display: block;
    content: '';
    width: 1px;
    height: 22px;
    background: rgba(0, 0, 0, 0.06);
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
  }
}
</style>
