<template>
  <div class="addon-info-wrap" :title="title" :class="{ 'gray-color': !data.sku_publish_status }">
    <div class="text-wrap">
      <div class="text-item">{{ data.activity_id }} - {{ data.activity_name_lang }}</div>
      <div class="text-item">/ {{ data.package_id }} - {{ data.package_name_lang }}</div>
      <div class="text-item">/ {{ data.sku_id }} - {{ data.sku_name_lang }}</div>
    </div>
    <div class="num-wrap" :class="{ active: showEdit }">
      <InputNumber
        v-show="showEdit"
        ref="inputNumber"
        v-model="currentDiscount"
        :show-edit="true"
        :wrap-style="{ width: '144px' }"
        @save="updateStrategy"
        @cancel="cancelEdit"
      />
      <div v-show="!showEdit">
        <span style="color: rgba(0, 0, 0, 0.65);">{{ getStrategy() }}%</span>
        <a-icon class="edit-icon" type="edit" @click="edit" />
      </div>
    </div>
    <div class="icon-wrap delete-wrap">
      <a-icon type="delete" class="delete-icon" @click="deleteItem" />
    </div>
    <div class="icon-wrap menu-wrap">
      <svg-icon v-if="showDrag" class="menu-icon handle" icon-name="addon-menu" />
    </div>
  </div>
</template>
<script>
import InputNumber from '../input-number/index.vue'

export default {
  components: {
    InputNumber
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    showDrag: {
      type: Boolean,
      default: false
    },
    mainSku: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      currentDiscount: '',
      showEdit: false
    }
  },
  computed: {
    title() {
      const {
        activity_id,
        activity_name_lang,
        package_id,
        package_name_lang,
        sku_id,
        sku_name_lang
      } = this.data
      return `${activity_id}-${activity_name_lang}/${package_id}-${package_name_lang}/${sku_id}-${sku_name_lang}`
    }
  },
  methods: {
    deleteItem() {
      const { sku_id: add_on_sku } = this.data
      const { sku_id: main_sku } = this.mainSku
      this.$emit('delete', [
        {
          main_sku,
          add_on_sku
        }
      ])
    },
    cancelEdit() {
      this.showEdit = false
    },
    edit() {
      this.currentDiscount = this.getStrategy()
      this.showEdit = true
      this.$nextTick(() => {
        this.$refs.inputNumber.focusInput()
      })
    },
    async updateStrategy() {
      const { sku_id: add_on_sku } = this.data
      const { sku_id: main_sku } = this.mainSku
      const discount = this.currentDiscount
      if (typeof discount != 'number') {
        // this.showEdit = false
        return
      }
      const result = await ajax.postBody(
        ADMIN_API.act.update_add_on_price_strategy,
        {
          data: {
            main_sku,
            add_on_sku,
            price_strategy: {
              discount
            }
          }
        },
        {
          loading: true
        }
      )
      if (!result.success) {
        return
      }
      this.showEdit = false
      this.$emit('changePriceStrategy', { data: this.data, discount })
    },
    getStrategy() {
      return this.data?.price_strategy?.discount ?? undefined
    }
  }
}
</script>
<style lang="scss" scoped>
.addon-info-wrap {
  display: flex;
  align-items: center;
  min-height: 54px;
  &.gray-color {
    color: #aaa;
    // .icon-wrap {
    //   .delete-icon {
    //     color: #aaa;
    //   }
    //   .menu-icon {
    //     color: #aaa;
    //   }
    // }
  }
  .text-wrap {
    padding: 0 16px;
    display: flex;
    align-items: center;
    width: calc(100% - 320px);
    .text-item {
      max-width: 33%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding: 0 2px;
    }
  }
  .num-wrap {
    width: 144px;
    text-align: right;
    padding: 0 16px;
    &.active {
      padding: 0;
    }
    .edit-icon {
      color: rgba(0, 0, 0, 0.45);
      margin-left: 8px;
      cursor: pointer;
      font-size: 16px;
      &:hover {
        color: #1890ff;
      }
    }
  }
  .icon-wrap {
    padding: 0 16px;
    text-align: right;
    &.delete-wrap {
      width: 104px;
    }
    &.menu-wrap {
      width: 72px;
      text-align: left;
    }
    .delete-icon {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.45);
      cursor: pointer;
      &:hover {
        color: #ff4d4f;
      }
    }
    .menu-icon {
      width: 16px;
      height: 16px;
      color: rgba(0, 0, 0, 0.45);
      cursor: move;
      &:hover {
        color: #1890ff;
      }
    }
  }
}
</style>
