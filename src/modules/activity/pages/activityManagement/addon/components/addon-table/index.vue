<template>
  <div>
    <div class="modal-setion-head">
      <div>{{ $t('79682') }}</div>
      <a-button class="btn" icon="plus" type="link" @click="showAddNew">Add new</a-button>
    </div>
    <table class="table">
      <thead class="thead">
        <tr class="table-row">
          <td class="table-cell cell-after-line">{{ $t('79664') }}</td>
          <td width="140" class="table-cell">
            <div class="cell-flex-container">
              <div class="cell-flex-text">{{ $t('79667') }}</div>
              <a-tooltip :title="$t('79685')">
                <a-icon type="question-circle" class="filter-icon" />
              </a-tooltip>
            </div>
          </td>
        </tr>
      </thead>
      <tbody>
        <tr class="table-row" :class="{ error: error }">
          <td class="table-cell border">
            <a-select
              v-model="addon"
              class="addon-select"
              option-label-prop="label"
              :allow-clear="true"
              :placeholder="$t('79683')"
              :disabled="disabledSelect"
            >
              <a-select-option
                v-for="item in addonList"
                :key="item.value"
                :value="item.value"
                :label="getTitle(item.label)"
                :disabled="item.disabled"
              >
                <a-radio
                  :checked="item.value === addon"
                  :disabled="item.disabled"
                  :title="getTitle(item.label, item.disabled)"
                >
                  <div class="option-text-wrap">
                    <div class="option-text">
                      <template v-for="(lb, index) in item.label">
                        <div v-if="index === 0" :key="index" class="option-text-item">
                          {{ lb }}
                        </div>
                        <div v-else :key="index" class="option-text-item">/ {{ lb }}</div>
                      </template>
                    </div>
                  </div>
                </a-radio>
              </a-select-option>
            </a-select>
          </td>
          <td class="table-cell border">
            <InputNumber v-model="currentDiscount" />
          </td>
        </tr>
        <div v-show="error" class="error-text">{{ errorText }}</div>
      </tbody>
    </table>
    <!-- 新增addon -->
    <Drawer
      :title="$t('79671')"
      placement="right"
      :closable="false"
      :visible="showAdd"
      :width="450"
      @cancel="cancelAdd"
      @save="addNew"
    >
      <GeneralAlert :show-icon="false" banner style="margin-bottom: 12px;">
        <div v-html="$t('81796')" />
      </GeneralAlert>
      <a-form-model ref="addonForm" :model="addonForm" :rules="rules">
        <a-form-model-item :label="$t('79664')" prop="selectAddon">
          <RemoteCascader
            ref="remoteCascader"
            v-model="addonForm.selectAddon"
            class="item-cascader"
            popup-class-name="select-multi-addon-popup"
            :remote-fetch-func="remoteFetchFunc"
            :fmt-display-text-func="fmtDisplayTextFunc"
            :popup-style="dropdownMenuStyle"
            :placeholder="$t('global_select')"
            :need-tooltip="false"
            :fmt-options-func="resetFilterOptions"
          ></RemoteCascader>
        </a-form-model-item>
        <a-form-model-item :label="$t('79667')" prop="discount">
          <InputNumber v-model="addonForm.discount" :need-border="true" :wrap-style="{ width: '130px' }" />
        </a-form-model-item>
      </a-form-model>
    </Drawer>
  </div>
</template>
<script>
import { getEditLang } from '@activity/utils/index.js'

import InputNumber from '../input-number/index.vue'
import Drawer from '../drawer/index.vue'
import RemoteCascader from '@activity/pages/package/units/unit-list/unit-bar/components/remoteCascader.vue'
import GeneralAlert from '@activity/pages/components/GeneralAlert.vue'

export default {
  components: {
    InputNumber,
    Drawer,
    RemoteCascader,
    GeneralAlert
  },
  props: {
    options: {
      type: Array,
      default: () => []
    },
    checkedAddon: {
      type: Array,
      default: () => []
    },
    checkedSku: {
      type: Array,
      default: () => []
    }
  },
  data() {
    const selectAddonFn = (rule, value, callback) => {
      if (!value.length) {
        callback(new Error(this.$t('global_please_select')))
        return
      }
      callback()
    }
    const discountFn = (rule, value, callback) => {
      if (typeof value !== 'number') {
        callback(new Error(this.$t('global_please_input')))
        return
      }
      callback()
    }
    return {
      filterOptions: [],
      addon: undefined,
      showSearch: {
        filter(inputValue, path) {
          const [act] = path
          const { label } = act || {}
          const isValidate = label.indexOf(inputValue) >= 0
          return isValidate
        },
        sort() {},
        render: this.searchRender,
        matchInputWidth: true
      },
      optionsItemLength: 3,
      currentDiscount: undefined,
      selectAddon: [],
      newAddon: [],
      newAddonChecked: [],
      showAdd: false,
      addonForm: {
        selectAddon: [],
        discount: undefined
      },
      rules: {
        selectAddon: [
          {
            required: true,
            validator: selectAddonFn
          }
        ],
        discount: [
          {
            required: true,
            validator: discountFn
          }
        ]
      },
      error: false,
      query: '',
      errorText: ''
    }
  },
  computed: {
    disabledSelect() {
      const { addonList } = this
      return addonList.every((item) => item.disabled)
    },
    currentAddon() {
      const { addonList, addon } = this
      return addonList.find((item) => item.value === addon)
    },
    addonList() {
      const listObject = {}
      const { checkedAddon = [], options = [], newAddon = [] } = this
      const sortOptions = options.sort((item) => {
        return checkedAddon.includes(item.sku_id) ? 1 : -1
      })
      const allAddon = [...newAddon, ...sortOptions]
      allAddon.forEach((item) => {
        const { sku_id } = item
        if (!listObject[sku_id]) {
          listObject[sku_id] = item
        }
      })
      return Object.values(listObject).map((item) => {
        item.disabled = checkedAddon.includes(item.sku_id)
        return item
      })
    },
    dropdownMenuStyle() {
      const { filterOptions, query } = this
      const showMenu = filterOptions.length || query
      return showMenu ? {} : { height: '0', overflow: 'hidden' }
    }
  },
  methods: {
    async remoteFetchFunc(v) {
      return await this.getPackageSku(v)
    },
    fmtDisplayTextFunc({ selectedOptions }) {
      const [act, pkg, sku] = selectedOptions

      return `${act.label}/${pkg.label}/${sku.label}`
    },
    reset() {
      this.currentDiscount = ''
      this.addon = undefined
      this.error = false
      this.newAddon = []
      this.newAddonChecked = []
    },
    cancelAdd() {
      this.$refs.addonForm.resetFields()
      this.showAdd = false
      this.resetCascader()
    },
    resetCascader() {
      const remoteCascader = this.$refs.remoteCascader
      if (remoteCascader) {
        remoteCascader.searchValue = ''
        remoteCascader.effectiveValue = null
        remoteCascader.currentSearchEffectiveVal = ''
      }
    },
    async addNew() {
      const validate = await this.$refs.addonForm.validate().catch(() => false)
      if (!validate) {
        return
      }
      const filterOptions = this.filterOptions
      const { selectAddon, discount } = this.addonForm
      const [act, pkg, sku] = selectAddon
      let addon = null
      for (let i = 0; i < filterOptions.length; i++) {
        const { value: actValue, children: actChildren, label: actLabel } = filterOptions[i]
        if (actValue === act) {
          for (let j = 0; j < actChildren.length; j++) {
            const { value: pkgValue, children: pkgChildren, label: pkgLabel } = actChildren[j]
            if (pkgValue === pkg) {
              for (let k = 0; k < pkgChildren.length; k++) {
                const { value: skuValue, label: skuLabel } = pkgChildren[k]
                if (skuValue === sku) {
                  addon = {
                    value: sku,
                    sku_id: sku,
                    package_id: pkg,
                    activity_id: act,
                    main_sku: pkg,
                    price_strategy: {
                      discount: discount
                    },
                    label: [actLabel, pkgLabel, skuLabel]
                  }
                  break
                }
              }
              break
            }
          }
          break
        }
      }
      this.newAddon.unshift(addon)
      this.addon = sku
      this.selectAddon = []
      this.newAddonChecked.push(sku)
      this.showAdd = false
      this.currentDiscount = discount
      this.addonForm = {
        selectAddon: [],
        discount: undefined
      }
      this.error = false
      this.resetCascader()
      this.resetFilterOptions()
    },
    checkAddon() {
      const currentAddon = this.currentAddon || {}
      const discount = this.currentDiscount
      const { sku_id } = currentAddon
      if (typeof discount != 'number' || !sku_id) {
        // this.$message.warning(this.$t('79683'))
        this.error = true
        this.errorText = !sku_id ? this.$t('79683') : this.$t('80277')
        return false
      }
      this.error = false
      return true
    },
    getAddonData() {
      if (!this.checkAddon()) {
        return null
      }
      const currentAddon = this.currentAddon
      const discount = this.currentDiscount
      const { sku_id } = currentAddon
      return [
        {
          sku_id,
          price_strategy: {
            discount
          }
        }
      ]
    },
    getTitle(label, disabled) {
      return disabled ? this.$t('80278') : label.join('/')
    },
    displayRender({ labels, selectedOptions }) {
      const optionsItemLength = this.optionsItemLength
      const vn = labels.map((item, index) => {
        return (
          <div class={`display-render-item display-render-item_${optionsItemLength}`}>
            <div class="display-render-item-con">{item}</div>
            <div class="display-render-item-line">{index === labels.length - 1 ? '' : '/'}</div>
          </div>
        )
      })
      return <div class="addon-display-render-wrap">{vn}</div>
    },
    searchRender({ inputValue, path }) {
      const optionsItemLength = this.optionsItemLength
      const title = []
      const vn = []
      path.forEach((item, index) => {
        title.push(item.label)
        vn.push(
          <div class={`display-render-filter-item display-render-filter-item_${optionsItemLength}`}>
            <div class={'display-render-item-con'}>{item.label}</div>
            <div class="display-render-item-line">{index === path.length - 1 ? '' : '/'}</div>
          </div>
        )
      })
      return (
        <div class="addon-display-render-filter-wrap" title={title.join('/')}>
          {vn}
        </div>
      )
    },
    debounceOnSearch: _.debounce(async function(v) {
      this.$refs.addonForm.clearValidate()
      await this.onSearch(v)
    }, 500),
    async onSearch(v) {
      const list = await this.getPackageSku(v)
      const options = _.cloneDeep(this.filterOptions)
      list.forEach((item) => {
        const [o] = options.filter((it) => it.value === item.value)
        if (!o) {
          this.filterOptions.unshift({ ...item })
        }
      })
    },
    // keyword 无条件
    async getPackageSku(keyword) {
      let kv = keyword.trim()
      if (!kv) {
        this.query = kv
        return []
      }
      const result = await ajax.getBody(ADMIN_API.act.filter_add_on_sku, {
        params: {
          keyword: kv,
          language: getEditLang()
        }
      })
      this.query = kv
      if (!result.success) {
        return []
      }
      const options = _.cloneDeep(this.filterOptions)
      const list = this.initOptions(result?.result?.activity_list || [])
      list.forEach((item) => {
        const [o] = options.filter((it) => it.value === item.value)
        if (!o) {
          this.filterOptions.unshift({ ...item })
        }
      })
      return this.filterOptions
    },
    showAddNew() {
      this.showAdd = true
    },
    resetFilterOptions() {
      const { checkedAddon, newAddonChecked, filterOptions, checkedSku } = this
      const options = _.cloneDeep(filterOptions)
      const newList = []
      options.forEach((act) => {
        const pkg_list = []
        act.children.forEach((pkg) => {
          const sku_list = []
          pkg.children.forEach((sku) => {
            if (!checkedSku.includes(sku.value)) {
              sku.disabled = checkedAddon.includes(sku.value) || newAddonChecked.includes(sku.value)
              sku_list.push(sku)
            }
          })
          if (sku_list.length) {
            pkg_list.push({
              ...pkg,
              children: sku_list
            })
          }
        })
        if (pkg_list.length) {
          newList.push({
            ...act,
            children: pkg_list
          })
        }
      })
      return newList
    },
    initOptions(list) {
      const newList = []
      const checkedAddon = this.checkedAddon
      list.forEach((item) => {
        const { activity_id, activity_name_lang, package_list = [] } = item
        if (package_list && package_list.length) {
          const children = package_list.reduce((acc, pkg) => {
            const { package_id, package_name_lang, sku_list = [] } = pkg

            const [hasPkg] = acc.filter((i) => i.value === package_id)
            const sku = sku_list.map((sku) => {
              const { sku_id, sku_name_lang } = sku
              const disabled = checkedAddon.includes(sku_id)
              return {
                value: sku_id,
                label: `${sku_id}-${sku_name_lang}`,
                disabled
              }
            })
            if (!sku.length) {
              return acc
            }
            if (hasPkg) {
              hasPkg.children = [...hasPkg.children, ...sku]
              return acc
            }
            return [
              ...acc,
              {
                value: package_id,
                label: `${package_id}-${package_name_lang}`,
                children: sku
              }
            ]
          }, [])
          if (children.length) {
            newList.push({
              value: activity_id,
              label: `${activity_id}-${activity_name_lang}`,
              children
            })
          }
        }
      })
      return newList
    }
  }
}
</script>
<style lang="scss" scoped>
.table {
  width: 100%;
  .thead {
    background: #fafafa;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
  }
  .table-row {
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    &.error {
      box-shadow: inset 0px 0px 3px rgba(255, 0, 0, 0.81);
    }
    .table-cell {
      padding: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      .cell-flex-container {
        display: flex;
        align-items: center;
        .cell-flex-text {
          padding-right: 5px;
        }
        .filter-icon {
          color: rgba(0, 0, 0, 0.85);
          font-size: 12px;
        }
      }
      &.cell-after-line {
        position: relative;
        &::after {
          display: block;
          content: '';
          width: 1px;
          height: 22px;
          background: rgba(0, 0, 0, 0.06);
          position: absolute;
          top: 50%;
          right: 0;
          transform: translateY(-50%);
        }
      }
      &.border {
        border-left: 1px solid rgba(0, 0, 0, 0.06);
      }
      .addon-select {
        width: 580px;
      }
    }
  }
}
.error-text {
  color: rgba(255, 0, 0, 0.81);
}
.dropdown-btn-wrap {
  padding: 8px 8px 8px 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: space-between;
  .cascader {
    width: 457px;
  }
}
.option-text-wrap {
  display: inline-flex;
  align-items: center;
  .option-text {
    max-width: 540px;
    padding-right: 10px;
    display: flex;
    .option-text-item {
      max-width: 33.3%;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .option-num {
    padding-left: 10px;
    border-left: 1px solid rgba(0, 0, 0, 0.06);
  }
}
.modal-setion-head {
  font-weight: 500;
  font-size: 14px;
  padding: 44px 0 12px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: rgba(0, 0, 0, 0.85);
}
</style>

<style lang="scss">
.addon-popup-class-name__item {
  .ant-cascader-menu:only-child .ant-cascader-menu-item {
    max-width: 402px;
  }
  .ant-cascader-menu-item {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.addon-display-render-filter-wrap {
  display: flex;
  .display-render-filter-item {
    display: flex;
    .display-render-item-line {
      padding: 0 2px;
    }
    .display-render-item-con {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }
    &.display-render-filter-item_2 {
      max-width: 50%;
    }
    &.display-render-filter-item_3 {
      max-width: 33.33%;
    }
  }
}

.addon-display-render-wrap {
  display: flex;
  width: 100%;
  .display-render-item {
    max-width: 50%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    &.display-render-item_3 {
      max-width: 33%;
    }
    .display-render-item-con {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
    }
    .display-render-item-line {
      padding: 0 2px;
    }
    &:last-child {
      flex: 1;
      max-width: 100%;
    }
  }
}

.select-multi-addon-popup .ant-cascader-menu {
  max-width: 402px;
  &:only-child {
    width: 402px;
  }
  .ant-cascader-menu-item {
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
