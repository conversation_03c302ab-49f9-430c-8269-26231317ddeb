<template>
  <draggable
    v-model="dragList"
    v-bind="dragOptions"
    :disabled="disabled"
    :handle="handle ? '.handle' : ''"
    class="draggable-list"
    :class="{ disabled: disabled || handle }"
    @start="handleStart"
    @end="handleEnd"
    @change="handleChange"
  >
    <transition-group type="transition">
      <div v-for="(item, index) in dragList" :key="`${skuId}-${item.sku_id}`" class="list-group-item">
        <slot :item="item" :index="index" />
      </div>
    </transition-group>
  </draggable>
</template>

<script>
import draggable from 'vuedraggable'

export default {
  name: 'DraggableList',
  components: {
    draggable
  },
  props: {
    value: {
      type: Array,
      required: true
    },
    itemKey: {
      type: String,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    handle: {
      type: Boolean,
      default: false
    },
    packageId: {
      type: Number,
      default: 0
    },
    skuId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {}
  },
  computed: {
    dragList: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    dragOptions() {
      const group = `${this.packageId}_${this.skuId}`
      const options = {
        animation: 200,
        group,
        ghostClass: 'ghost'
      }
      return options
    }
  },
  methods: {
    handleChange() {
      const { packageId, skuId } = this
      this.$emit('change', { package_id: packageId, sku_id: skuId })
    },
    handleStart() {
      this.$emit('start')
    },
    handleEnd() {
      this.$emit('end')
    }
  }
}
</script>

<style lang="scss">
.draggable-list {
  .ghost {
    opacity: 0.5;
    background: #c8ebfb;
  }
  .flip-list-move {
    transition: transform 0.5s;
  }
  .no-move {
    transition: transform 0s;
  }

  .handle,
  .list-group-item {
    cursor: move;
  }
  // .list-group-item i {
  //   cursor: pointer;
  // }

  &.disabled {
    .list-group-item {
      cursor: default;
    }
  }
}
</style>
