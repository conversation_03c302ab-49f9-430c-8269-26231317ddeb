<template>
  <a-checkbox-group :value="value" @change="change">
    <div v-for="item in options" :key="item.value" class="checkbox-container">
      <a-checkbox :value="item.value">
        <div class="checkbox-content" :title="item.label.join('/')">
          <template v-for="(label, index) in item.label">
            <div
              v-if="index === 0"
              :key="index"
              class="checkbox-content-item"
              :class="['checkbox-content-item__' + item.label.length]"
            >
              {{ label }}
            </div>
            <div
              v-else
              :key="index"
              class="checkbox-content-item"
              :class="['checkbox-content-item__' + item.label.length]"
            >
              / {{ label }}
            </div>
          </template>
        </div>
      </a-checkbox>
    </div>
  </a-checkbox-group>
</template>
<script>
export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  methods: {
    change(v) {
      this.$emit('change', v)
    }
  }
}
</script>
<style lang="scss" scoped>
.checkbox-container {
  padding: 5px 12px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  min-width: 400px;
  .checkbox-content {
    display: inline-flex;
    align-items: center;
    width: 344px;
    .checkbox-content-item {
      max-width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      &.checkbox-content-item__3 {
        max-width: 33%;
      }
      &.checkbox-content-item__2 {
        max-width: 50%;
      }
    }
  }
}
</style>
