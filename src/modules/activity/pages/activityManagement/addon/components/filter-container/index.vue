<template>
  <div class="table-filter-container" :style="style" @click.stop>
    <div class="table-filter-content">
      <slot></slot>
    </div>
    <div class="table-filter-footer">
      <a-button type="link" :disabled="resetAbled" size="small" @click="$emit('reset')">
        {{ $t('79680') }}
      </a-button>
      <a-button type="primary" size="small" @click="$emit('ok')">OK</a-button>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    resetAbled: {
      type: Boolean,
      default: true
    },
    filterContainerStyle: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    style() {
      return { ...this.filterContainerStyle }
    }
  }
}
</script>
<style lang="scss" scoped>
.table-filter-container {
  position: absolute;
  z-index: 11;
  background: #fff;
  right: 16px;
  top: 46px;
  max-width: 400px;
  max-height: 142px;
  min-width: 200px;
  min-height: 50px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 2px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .table-filter-content {
    overflow: hidden auto;
    max-height: 104px;
    padding: 4px 0;
  }
  .table-filter-footer {
    padding: 8px;
    display: flex;
    justify-content: space-between;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
  }
}
</style>
