<template>
  <div class="input-wrap" :class="{ active: showEdit || active }" :style="wrapStyle">
    <a-input
      ref="input"
      :value="value"
      class="input"
      :class="{ 'border-none': !needBorder }"
      :placeholder="placeholder"
      suffix="%"
      @blur="inputBlur"
      @change="change"
    ></a-input>
    <a-button v-if="showEdit" class="save" type="link" @click="save">{{ $t('79675') }}</a-button>
  </div>
</template>
<script>
export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    showEdit: {
      type: Boolean,
      default: false
    },
    value: {
      type: [String, Number],
      default: ''
    },
    active: {
      type: Boolean,
      default: false
    },
    needBorder: {
      type: Boolean,
      default: false
    },
    wrapStyle: {
      type: Object,
      default: () => ({})
    },
    placeholder: {
      type: String,
      default: __('79684')
    }
  },
  methods: {
    focusInput() {
      this.$refs.input.focus()
    },
    save() {
      this.$emit('save', +this.value)
    },
    inputBlur() {
      setTimeout(() => {
        this.$emit('cancel')
      }, 250)
    },
    change(event) {
      const v = event.target.value
      let num
      if (!v || !v.trim()) {
        num = ''
      } else {
        num = parseInt(v)
        if (isNaN(num)) {
          num = 100
        }
        if (num < 0) {
          num = 0
        }
        if (num > 100) {
          num = 100
        }
      }
      this.$emit('change', num)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .border-none .ant-input {
  border: none;
  padding-left: 5px;
  &:focus {
    outline: 0;
    box-shadow: none;
  }
}
.input-wrap {
  display: flex;
  align-items: center;
  width: 120px;
  border: 1px solid transparent;
  border-radius: 2px;
  box-shadow: none;

  &.active {
    border: 1px solid #1890ff;
    box-shadow: 0px 0px 0px 2px rgba(24, 144, 255, 0.2);
  }

  .input {
    flex: 1;
    border: none;
    text-align: right;
    padding: 0;
    &:focus {
      outline: 0;
      box-shadow: none;
    }
  }
  .save {
    padding: 0 10px 0 0;
  }
}
</style>
