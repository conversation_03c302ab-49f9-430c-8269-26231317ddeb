<template>
  <div id="addonWrap" class="addon-page-wrap">
    <div class="search-wrap">
      <a-input-search class="search-input" :placeholder="$t('79658')" allow-clear @search="onSearch">
        <a-button slot="enterButton">
          <a-icon type="search" />
        </a-button>
      </a-input-search>
      <div class="content">{{ $t('79672', { num: checkedSku.length }) }}</div>
      <a-button type="primary" icon="plus" @click="addNew">{{ $t('79671') }}</a-button>
    </div>
    <div class="table-wrap">
      <table class="table">
        <thead class="table-thead">
          <tr class="table-row">
            <td class="table-cell border-cell sp-bg" colspan="2">
              <div class="cell-flex-container">
                <div>{{ $t('79659') }}</div>
                <div class="cell-flex-text">{{ $t('79660') }}</div>
                <a-switch :checked="filterOptions.published" @change="publishedChange"></a-switch>
              </div>
            </td>
            <td class="table-cell flex-cell" colspan="4">
              <div class="cell-flex-container">
                <div>{{ $t('79663') }}</div>
                <div class="cell-flex-text">{{ $t('79670') }}</div>
                <a-switch :checked="filterOptions.addedOnly" @change="addedOnlyChange"></a-switch>
              </div>
            </td>
          </tr>
          <tr class="table-row">
            <td class="table-cell border-cell sp-bg" width="260">
              <div class="cell-flex-container">
                <div>{{ $t('79661') }}</div>
                <a-icon
                  type="filter"
                  theme="filled"
                  class="filter-icon"
                  :class="{ active: !!filterOptions.packageChecked.length || showPackageFilter }"
                  @click.stop="showFilter('showPackageFilter', !packageFilter.length, 'packageChecked')"
                />
              </div>
              <FilterContainer
                v-show="showPackageFilter"
                :reset-abled="!filterTemp.packageChecked.length"
                :filter-container-style="{
                  right: '-370px'
                }"
                @reset="filterTemp.packageChecked = []"
                @ok="filterOk('packageChecked')"
              >
                <FilterCheckboxGroup v-model="filterTemp.packageChecked" :options="packageFilter" />
              </FilterContainer>
            </td>
            <td class="table-cell border-cell sp-bg" width="200">
              <div class="cell-flex-container">
                <a-checkbox :checked="checkedAll" :indeterminate="indeterminate" @change="checkAll">
                  {{ $t('79662') }}
                </a-checkbox>
                <a-icon
                  type="filter"
                  theme="filled"
                  class="filter-icon"
                  :class="{ active: !!filterOptions.skuChecked.length || showSkuFilter }"
                  @click.stop="showFilter('showSkuFilter', !skuFilter.length, 'skuChecked')"
                />
              </div>
              <FilterContainer
                v-show="showSkuFilter"
                :reset-abled="!filterTemp.skuChecked.length"
                :filter-container-style="{
                  right: '-370px'
                }"
                @reset="filterTemp.skuChecked = []"
                @ok="filterOk('skuChecked')"
              >
                <FilterCheckboxGroup v-model="filterTemp.skuChecked" :options="skuFilter" />
              </FilterContainer>
            </td>
            <td class="table-cell cell-after-line">
              <div class="cell-flex-container">
                <div>{{ $t('79664') }}</div>
                <a-icon
                  type="filter"
                  theme="filled"
                  class="filter-icon"
                  :class="{ active: !!filterOptions.addonChecked.length || showAddonFilter }"
                  @click.stop="showFilter('showAddonFilter', !duplicateAddon.length, 'addonChecked')"
                />
              </div>
              <FilterContainer
                v-show="showAddonFilter"
                :reset-abled="!filterTemp.addonChecked.length"
                @reset="filterTemp.addonChecked = []"
                @ok="filterOk('addonChecked')"
              >
                <FilterCheckboxGroup v-model="filterTemp.addonChecked" :options="duplicateAddon" />
              </FilterContainer>
            </td>
            <td class="table-cell cell-after-line" width="144">
              <div class="cell-flex-container">
                <div>{{ $t('79667') }}</div>
                <a-icon
                  type="filter"
                  theme="filled"
                  class="filter-icon"
                  :class="{ active: !!filterOptions.strategy.length || showStrategyFilter }"
                  @click.stop="showFilter('showStrategyFilter', !strategyFilter.length, 'strategy')"
                />
              </div>
              <FilterContainer
                v-show="showStrategyFilter"
                :reset-abled="!filterTemp.strategy.length"
                @reset="filterTemp.strategy = []"
                @ok="filterOk('strategy')"
              >
                <div class="strategy-filter-wrap">
                  <FilterCheckboxGroup v-model="filterTemp.strategy" :options="strategyFilter" />
                </div>
              </FilterContainer>
            </td>
            <td class="table-cell cell-after-line" width="104">
              <div class="cell-flex-container">
                <div>{{ $t('79668') }}</div>
                <a-icon type="delete" class="filter-icon-action" @click="deleteAddon(null)" />
              </div>
            </td>
            <td class="table-cell" width="72">{{ $t('79669') }}</td>
          </tr>
        </thead>
      </table>
      <div class="data-table-wrap">
        <a-spin :spinning="tabledLoading">
          <table class="table">
            <tbody class="table-tbody">
              <template v-for="pkg in packages">
                <tr v-for="(sku, idx) in pkg.sku_list" :key="sku.sku_id" class="table-row">
                  <td
                    v-if="idx === 0"
                    :rowspan="pkg.sku_list.length || 1"
                    class="table-cell border-cell bg-cell"
                    :class="{ 'gray-color': !pkg.package_publish_status }"
                    width="260"
                  >
                    {{ pkg.package_id }} - {{ pkg.package_name_lang }}
                  </td>
                  <td
                    class="table-cell border-cell bg-cell"
                    :class="{ 'gray-color': !sku.sku_publish_status }"
                    width="200"
                  >
                    <div
                      class="checkbox-label"
                      :title="`${sku.sku_id}-${sku.sku_name_lang}`"
                      @click="checkSku(sku.sku_id, sku.add_on_list)"
                    >
                      <div v-if="sku.add_on_list.length >= maxAddon" class="checkbox"></div>
                      <a-checkbox
                        v-else
                        :checked="getSkuChecked(sku.sku_id)"
                        :disabled="sku.add_on_list.length >= maxAddon"
                      ></a-checkbox>
                      <div class="checkbox-label-text">
                        {{ sku.sku_name_lang }}
                      </div>
                    </div>
                  </td>
                  <td colspan="4" class="table-cell not-padding">
                    <DraggableList
                      v-model="sku.add_on_list"
                      :handle="true"
                      :package-id="pkg.package_id"
                      :sku-id="sku.sku_id"
                      @change="sortChange"
                    >
                      <template v-slot:default="{ item, index }">
                        <div :style="infoWrapStyle" :class="{ border: index + 1 < sku.add_on_list.length }">
                          <AddonInfo
                            :data="item"
                            :index="index"
                            :show-drag="sku.add_on_list.length > 1"
                            :main-sku="sku"
                            @changePriceStrategy="changePriceStrategy"
                            @delete="deleteAddon"
                          />
                        </div>
                      </template>
                    </DraggableList>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </a-spin>
      </div>
      <div v-show="packages.length === 0" class="not-found-wrap">
        <svg-icon class="not-found-icon" icon-name="addon-notdata" />
        <div class="not-found-text">{{ $t('79674') }}</div>
      </div>
    </div>

    <!-- 新建modal -->
    <Drawer
      :title="$t('79671')"
      placement="right"
      :closable="false"
      :visible="showAdd"
      :width="800"
      @cancel="cancelAdd"
      @save="handleAdd"
    >
      <div class="modal-setion-head">{{ $t('79681') }}</div>
      <AddTable :data-source="selectedSku" />
      <AddonTable
        ref="addonTable"
        :options="duplicateAddon"
        :checked-addon="checkedAddon"
        :checked-sku="checkedSku"
      />
    </Drawer>
  </div>
</template>
<script>
import { mapState } from 'vuex'

import AddonInfo from './components/addon-info/index.vue'
import FilterContainer from './components/filter-container/index.vue'
import FilterCheckboxGroup from './components/filter-checkbox-group/index.vue'
import AddTable from './components/add-table/index.vue'
import AddonTable from './components/addon-table/index.vue'
import DraggableList from './components/draggable-list/index.vue'
import Drawer from './components/drawer/index.vue'
// getEditLang
import { pmsConfirm } from '@activity/utils/index.js'

import { getUnitTypeList } from '@activity/pages/package/package_const.js'

export default {
  components: {
    AddonInfo,
    FilterContainer,
    FilterCheckboxGroup,
    AddTable,
    AddonTable,
    DraggableList,
    Drawer
  },
  data() {
    return {
      showAdd: false,
      packages: [],
      infoWrapStyle: {
        width: '800px'
      },
      originData: [],
      filterOptions: {
        published: false,
        addedOnly: false,
        query: '',
        addonChecked: [],
        packageChecked: [],
        skuChecked: [],
        strategy: []
      },
      filterTemp: {
        addonChecked: [],
        packageChecked: [],
        skuChecked: [],
        strategy: []
      },
      checkedAll: false,
      checkedSku: [],
      // 筛选
      showAddonFilter: false,
      showPackageFilter: false,
      showSkuFilter: false,
      showStrategyFilter: false,
      filterKey: ['showAddonFilter', 'showPackageFilter', 'showSkuFilter', 'showStrategyFilter'],
      maxAddon: 8,
      tabledLoading: false
    }
  },
  computed: {
    ...mapState({
      categoryInfo: (state) => state.categoryInfo
    }),
    unitTypeList() {
      const { sub_category_id } = this.categoryInfo
      return getUnitTypeList(sub_category_id)
    },
    indeterminate() {
      const { allFilterSku, checkedSku } = this
      return checkedSku.length > 0 && checkedSku.length < allFilterSku.length
    },
    packageFilter() {
      const list = this.originData.map((item) => {
        const { package_id, package_name_lang } = item
        return {
          value: package_id,
          label: [`${package_id}-${package_name_lang}`]
        }
      })
      return list
    },
    // 通过sku类型分类
    skuFilter() {
      const list = this.allSku.reduce((acc, curr) => {
        const { sku_name_lang, unit_type, value: sku_id } = curr
        const unit = this.checkUnitType({ unit_type, text: sku_name_lang })
        if (unit) {
          acc[unit_type] = {
            value: unit.value,
            label: [`${unit.text}`],
            unit_type,
            sku_id,
            sku_name_lang
          }
        } else {
          acc[sku_id] = { value: sku_id, label: [`${sku_name_lang}`], unit_type, sku_id, sku_name_lang }
        }
        return acc
      }, {})
      return Object.values(list)
    },
    strategyFilter() {
      const allAddon = this.allAddon
      const list = allAddon
        .map((item) => {
          return item.price_strategy.discount
        })
        .sort((a, b) => b - a)
      return [...new Set(list)].map((item) => {
        return {
          value: item,
          label: [`${item}%`]
        }
      })
    },
    allAddon() {
      return this.getAddonList(this.originData)
    },
    // 去重后的addon列表
    duplicateAddon() {
      const listObject = {}
      const allAddon = this.allAddon || []
      allAddon.forEach((item) => {
        const { sku_id } = item
        if (!listObject[sku_id]) {
          listObject[sku_id] = item
        }
      })
      return Object.values(listObject)
    },
    addonFilter() {
      return this.getAddonList(this.packages)
    },
    allSku() {
      return this.getSkuList(this.originData)
    },
    allFilterSku() {
      return this.getSkuList(this.packages)
    },
    selectedSku() {
      const { allFilterSku, checkedSku } = this
      return allFilterSku.filter((sku) => checkedSku.includes(sku.value))
    },
    checkedAddon() {
      const { allAddon, checkedSku } = this
      const list = []
      allAddon.forEach((item) => {
        const { sku_id, main_sku } = item
        if (checkedSku.includes(main_sku)) {
          list.push(sku_id)
        }
      })
      return [...new Set(list)]
    }
  },
  created() {
    this.getAddon()
  },
  mounted() {
    window.addEventListener('resize', this.getWidth)
    document.body.addEventListener('click', this.hideAllFilter)
    this.getWidth()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.getWidth)
    document.body.removeEventListener('click', this.hideAllFilter)
  },
  methods: {
    checkUnitType(unitType) {
      const { unit_type } = unitType
      // const { unit_type, text } = unitType
      const unit = this.unitTypeList.find((item) => {
        return item.value === unit_type
        // return item.value === unit_type && text.indexOf(item.text) >= 0
      })
      return unit
    },
    filterOk(checkKey) {
      const { filterTemp } = this
      this.filterOptions[checkKey] = [...filterTemp[checkKey]]
      this.hideAllFilter()
      this.filterPackage()
    },
    showFilter(key, disabled, checkKey) {
      if (disabled) {
        return
      }
      const { filterOptions } = this
      this.filterTemp[checkKey] = [...filterOptions[checkKey]]
      const status = this[key]
      this.hideAllFilter()
      this.$nextTick(() => {
        this[key] = !status
      })
    },
    hideAllFilter() {
      this.filterKey.forEach((item) => {
        this[item] = false
      })
    },
    sortChange({ package_id, sku_id }) {
      const pkg = this.packages.find((item) => item.package_id === package_id)
      const sku = pkg.sku_list.find((item) => item.sku_id === sku_id) || []
      const add_on_sku_list = sku.add_on_list.map((item) => {
        const { sku_id, price_strategy } = item
        return { sku_id, price_strategy }
      })

      ajax.postBody(ADMIN_API.act.save_add_on_sku, {
        data: {
          main_sku_list: [sku_id],
          add_on_sku_list
        }
      })
    },
    async deleteAddon(data) {
      const sku_list = data ? data : this.getDeleteAddon()
      const length = sku_list.length
      if (!length) {
        this.$message.warning(this.$t('79674'))
        return
      }
      const { allAddon } = this
      let title = this.$t('79677', { num: length })
      if (allAddon.length === sku_list.length && !data) {
        title = this.$t('79676', { num: length })
      }
      let confirm = true
      if (!data) {
        confirm = await pmsConfirm.call(this, {
          title,
          content: '',
          okText: this.$t('79678'),
          cancelText: this.$t('79679')
        })
      }
      if (!confirm) {
        return
      }
      const result = await ajax.postBody(
        ADMIN_API.act.delete_add_on_sku,
        {
          data: {
            sku_list
          }
        },
        {
          loading: true
        }
      )
      if (!result.success) {
        return
      }
      this.filterOptions.addonChecked = []
      this.getAddon()
    },
    getDeleteAddon() {
      const sku = this.addonFilter.map((item) => {
        const { main_sku, sku_id } = item
        return {
          main_sku,
          add_on_sku: sku_id
        }
      })
      return sku
    },
    changePriceStrategy({ data, discount }) {
      data.price_strategy.discount = discount
      this.getAddon(false)
    },
    getSkuList(data = []) {
      const list = data.reduce((acc, curr) => {
        const { sku_list = [], package_name_lang, package_id } = curr
        const allSku = sku_list.map((item) => {
          const { sku_name_lang, sku_id, unit_type, add_on_list = [] } = item
          const disabled = add_on_list.length >= this.maxAddon
          return {
            value: sku_id,
            unit_type,
            package_name_lang: `${package_id}-${package_name_lang}`,
            sku_name_lang,
            disabled
          }
        })
        return [...acc, ...allSku]
      }, [])
      return list
    },
    getAddonList(data = []) {
      const list = data.reduce((acc, curr) => {
        const { sku_list = [] } = curr
        const addon = sku_list.reduce((acc, curr) => {
          const { sku_id: pSku, add_on_list = [] } = curr
          const allAddon = add_on_list.map((addon) => {
            const {
              sku_id,
              sku_name_lang,
              package_id,
              package_name_lang,
              activity_id,
              activity_name_lang,
              price_strategy
            } = addon
            return {
              // value: `${pSku}_${sku_id}`,
              value: sku_id,
              sku_id,
              package_id,
              activity_id,
              main_sku: pSku,
              price_strategy,
              label: [
                `${activity_id}-${activity_name_lang}`,
                `${package_id}-${package_name_lang}`,
                `${sku_id}-${sku_name_lang}`
              ]
            }
          })
          return [...acc, ...allAddon]
        }, [])
        return [...acc, ...addon]
      }, [])
      return list
    },
    filterCheckedSku() {
      const { checkedSku, allFilterSku } = this
      const newList = checkedSku.filter((item) => {
        const [data] = allFilterSku.filter((sku) => sku.value === item && !sku.disabled)
        return !!data
      })
      this.checkedSku = newList
      this.calcCheckedAll()
    },
    calcCheckedAll() {
      if (this.allFilterSku.length && this.allFilterSku.length === this.checkedSku.length) {
        this.checkedAll = true
      } else {
        this.checkedAll = false
      }
    },
    getSkuChecked(sku) {
      return this.checkedSku.includes(sku)
    },
    checkSku(sku, addon = []) {
      if (addon.length >= this.maxAddon) {
        this.$message.warning(this.$t('79692'))
        return
      }
      const { checkedSku } = this
      if (checkedSku.includes(sku)) {
        this.checkedSku = checkedSku.filter((item) => item != sku)
      } else {
        this.checkedSku.push(sku)
      }
      this.calcCheckedAll()
    },
    checkAll() {
      const { allFilterSku, checkedAll } = this
      this.checkedAll = !checkedAll
      if (this.checkedAll) {
        const checked = []
        allFilterSku.forEach((item) => {
          if (!item.disabled) {
            checked.push(item.value)
          }
        })
        this.checkedSku = checked
      } else {
        this.checkedSku = []
      }
    },
    publishedChange(checked) {
      this.filterOptions.published = checked
      this.filterPackage()
    },
    addedOnlyChange(checked) {
      this.filterOptions.addedOnly = checked
      this.filterPackage()
    },
    filterPackage() {
      this.tabledLoading = true
      const {
        query,
        published,
        addedOnly,
        addonChecked,
        packageChecked,
        skuChecked,
        strategy
      } = this.filterOptions
      let newList = _.cloneDeep(this.originData)
      // 关键字搜索
      if (query) {
        newList = this.filterQuery(newList, query)
      }
      // 套餐发布状态
      if (published) {
        // item.package_publish_status
        newList = newList.reduce((acc, curr) => {
          if (!curr.package_publish_status) {
            return acc
          }
          const sku_list = curr.sku_list || []
          const sku = sku_list.filter((sku) => sku.sku_publish_status)
          return [...acc, { ...curr, sku_list: sku }]
        }, [])
      }
      // sku是否有addon
      if (addedOnly) {
        const addooList = []
        newList.forEach((item) => {
          const { sku_list } = item
          const newSkuList = sku_list.filter((sku) => sku.add_on_list && sku.add_on_list.length)
          if (newSkuList.length) {
            item.sku_list = newSkuList
            addooList.push(item)
          }
        })
        newList = addooList
      }
      // 搜索指定的addon
      if (addonChecked.length) {
        newList = this.filterCheckedAddon(newList)
      }
      // 过滤price_strategy
      if (strategy.length) {
        newList = this.filterPriceStrategy(newList)
      }

      // 搜索指定package
      if (packageChecked.length) {
        newList = newList.filter((item) => packageChecked.includes(item.package_id))
      }

      // 搜索指定类型的sku
      if (skuChecked.length) {
        newList = this.filterCheckedSkuType(newList)
      }
      this.packages = newList
      this.filterCheckedSku()
      setTimeout(() => {
        this.tabledLoading = false
      }, 250)
      return newList
    },
    filterQuery(list = [], query = '') {
      const q = query.toLowerCase()
      const newList = list.reduce((acc, curr) => {
        const { package_name_lang = '', package_id, sku_list = [] } = curr
        const pkgName = package_name_lang.toLowerCase()
        const isPkg = pkgName.indexOf(q) >= 0 || (package_id + '').indexOf(q) >= 0
        if (isPkg) {
          return [...acc, curr]
        }
        const sku = sku_list.reduce((acc, curr) => {
          const { sku_name_lang, sku_id, add_on_list } = curr
          const skuName = sku_name_lang.toLowerCase()
          const validateSku = skuName.indexOf(q) >= 0 || (sku_id + '').indexOf(q) >= 0
          if (validateSku) {
            return [...acc, curr]
          }
          const addon = add_on_list.filter((item) => {
            const {
              sku_name_lang,
              sku_id,
              activity_name_lang,
              activity_id,
              package_name_lang,
              package_id
            } = item
            const addonName = sku_name_lang.toLowerCase()
            const actName = activity_name_lang.toLowerCase()
            const plkName = package_name_lang.toLowerCase()
            const isSku = addonName.indexOf(q) >= 0 || (sku_id + '').indexOf(q) >= 0
            const isAct = actName.indexOf(q) >= 0 || (activity_id + '').indexOf(q) >= 0
            const isPkg = plkName.indexOf(q) >= 0 || (package_id + '').indexOf(q) >= 0
            return isSku || isAct || isPkg
          })
          if (addon.length) {
            return [...acc, { ...curr, add_on_list: addon }]
          }
          return acc
        }, [])
        if (sku.length) {
          return [...acc, { ...curr, sku_list: sku }]
        }
        return acc
      }, [])
      return newList
    },
    filterCheckedSkuType(list) {
      const skuChecked = this.filterOptions.skuChecked
      const skuFilter = this.skuFilter.filter((item) => skuChecked.includes(item.value))
      const newList = []
      list.forEach((item) => {
        const { sku_list } = item
        const newSkuList = sku_list.filter((sku) => {
          const { unit_type, sku_id, sku_name_lang } = sku
          const unit = this.checkUnitType({ unit_type, text: sku_name_lang })
          if (unit) {
            return !!skuFilter.filter((item) => item.value === unit.value).length
          }
          return !!skuFilter.filter((item) => item.value === sku_id).length
        })
        if (newSkuList.length) {
          newList.push({
            ...item,
            sku_list: newSkuList
          })
        }
      })
      return newList
    },
    filterPriceStrategy(list) {
      const { strategy } = this.filterOptions
      const newList = []
      list.forEach((item) => {
        const { sku_list } = item
        const newSkuList = []
        sku_list.forEach((sku) => {
          const { add_on_list = [] } = sku
          const newAddon = add_on_list.filter((addon) => {
            const discount = addon.price_strategy?.discount
            return strategy.includes(discount)
          })
          if (newAddon.length) {
            newSkuList.push({
              ...sku,
              add_on_list: newAddon
            })
          }
        })
        if (newSkuList.length) {
          newList.push({
            ...item,
            sku_list: newSkuList
          })
        }
      })
      return newList
    },
    filterCheckedAddon(list) {
      const { addonChecked } = this.filterOptions
      const newList = []
      list.forEach((item) => {
        const { sku_list } = item
        const newSkuList = []
        sku_list.forEach((sku) => {
          const { add_on_list = [] } = sku
          const newAddon = add_on_list.filter((addon) => {
            const key = addon.sku_id
            return addonChecked.includes(key)
          })
          if (newAddon.length) {
            newSkuList.push({
              ...sku,
              add_on_list: newAddon
            })
          }
        })
        if (newSkuList.length) {
          newList.push({
            ...item,
            sku_list: newSkuList
          })
        }
      })
      return newList
    },
    getWidth() {
      clearTimeout(this.widthTimer)
      this.widthTimer = setTimeout(() => {
        const addonWrap = document.querySelector('#addonWrap')
        const w = addonWrap.offsetWidth - 461
        this.infoWrapStyle = {
          width: `${w}px`
        }
      }, 350)
    },
    onSearch(v) {
      const query = v.trim()
      this.filterOptions.query = query
      this.filterPackage()
    },
    addNew() {
      if (!this.checkedSku.length) {
        this.$message.warning(this.$t('79673'))
        return
      }
      this.showAdd = true
    },
    async handleAdd() {
      const checkedSku = this.checkedSku
      const addonTable = this.$refs.addonTable
      const add_on_sku_list = addonTable.getAddonData()
      if (!add_on_sku_list) {
        return
      }
      const result = await ajax.postBody(
        ADMIN_API.act.append_add_on_sku,
        {
          data: {
            main_sku_list: checkedSku,
            add_on_sku_list
          }
        },
        {
          loading: true
        }
      )
      if (!result.success) {
        return
      }
      this.cancelAdd()
      this.getAddon()
    },
    cancelAdd() {
      const addonTable = this.$refs.addonTable
      addonTable.reset()
      this.showAdd = false
    },
    async getAddon(loading = true) {
      const language = klook.getAPILang()
      const activity_id = this.$route.params.id
      const result = await ajax.getBody(
        ADMIN_API.act.get_activity_add_on_list,
        {
          params: {
            activity_id,
            language
          }
        },
        {
          loading
        }
      )
      if (!result.success) {
        return
      }
      const package_list = this.initData(result?.result?.package_list || [])
      console.log('package_list===>', package_list)
      // this.packages = package_list
      this.originData = _.cloneDeep(package_list)
      this.filterPackage()
    },
    initData(list) {
      const newList = []
      list.forEach((item) => {
        const { package_id, sku_list = [] } = item
        const [pkg] = newList.filter((pkg) => pkg.package_id === package_id)
        if (pkg) {
          pkg.sku_list = [...pkg.sku_list, ...sku_list]
        } else {
          newList.push(item)
        }
      })
      return newList
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .ant-modal-body {
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}
.addon-page-wrap {
  margin-top: -12px;
  .search-wrap {
    background: #fff;
    padding: 16px 8px;
    display: flex;
    align-items: center;
    .search-input {
      width: 400px;
    }
    .content {
      flex: 1;
      text-align: right;
      padding: 0 12px;
      font-size: 14px;
    }
  }
  .table-wrap {
    font-size: 14px;
    .data-table-wrap {
      max-height: calc(100vh - 308px);
      overflow: auto;
    }
    .table {
      width: 100%;
    }
    .table-thead {
      font-weight: 600;
      background: #f5f5f5;
    }
    .table-tbody {
      background: #fff;
    }
    .table-row {
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    }
    .table-cell {
      padding: 16px;
      position: relative;
      word-break: break-all;
      &.sp-bg {
        background: #e6f1ff;
      }

      &.gray-color {
        color: #aaa;
      }
      &.not-padding {
        padding: 0;
      }
      &.border-cell {
        border-right: 1px solid rgba(0, 0, 0, 0.06);
      }
      &.bg-cell {
        background: #f4f9ff;
      }
      &.cell-after-line {
        position: relative;
        &::after {
          display: block;
          content: '';
          width: 1px;
          height: 22px;
          background: rgba(0, 0, 0, 0.06);
          position: absolute;
          top: 50%;
          right: 0;
          transform: translateY(-50%);
        }
      }
      .cell-flex-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .cell-flex-text {
          flex: 1;
          text-align: right;
          padding: 0 6px;
        }
        .filter-icon {
          color: rgba(0, 0, 0, 0.25);
          font-size: 12px;
          cursor: pointer;

          &.active {
            color: #1890ff;
          }
        }
        .filter-icon-action {
          color: rgba(0, 0, 0, 0.45);
          font-size: 16px;
          cursor: pointer;
          &:hover {
            color: #ff4d4f;
          }
        }
      }
      .border {
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      }
      .checkbox-label {
        display: flex;
        align-items: center;
        cursor: pointer;
        .checkbox-label-text {
          white-space: nowrap;
          overflow: hidden;
          max-width: 150px;
          text-overflow: ellipsis;
          padding-left: 4px;
        }
      }
    }
  }
}

.strategy-filter-wrap {
  width: 260px;
  .strategy-filter-text {
    padding-right: 10px;
  }
}

.not-found-wrap {
  padding: 64px;
  .not-found-icon {
    display: block;
    margin: 0 auto 5px auto;
    width: 120px;
    height: 92px;
  }
  .not-found-text {
    padding: 10px 0;
    text-align: center;
    color: rgba(0, 0, 0, 0.85);
  }
}

.modal-setion-head {
  font-weight: 500;
  font-size: 14px;
  padding: 12px 0;
  color: rgba(0, 0, 0, 0.85);
}
.checkbox {
  border: 1px solid #cacaca;
  width: 16px;
  height: 16px;
  background: #eee;
  border-radius: 2px;
}
</style>
