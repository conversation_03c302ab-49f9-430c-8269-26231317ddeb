const customCell = () => {
  return {
    style: {
      padding: '16px'
    }
  }
}

const customHeaderCell = (column) => {
  return {
    style: {
      padding: '16px',
      backgroundColor: '#fafafa !important'
    }
  }
}
export const addColumn = [
  {
    title: __('79661'),
    dataIndex: 'package_name_lang',
    ellipsis: true,
    width: '600px',
    customCell,
    customHeaderCell,
    className: 'cell-after-line'
  },
  {
    title: __('79662'),
    dataIndex: 'sku_name_lang',
    ellipsis: true,
    scopedSlots: { customRender: 'sku_name_lang' },
    customCell,
    customHeaderCell
  }
]
