/**
 * 需要扩展组件 type 支持，请移步  getDynamicComponent
 */
import SchemaFormItemRender from './SchemaFormItemRender'
import { getEditLang } from '@activity/utils'
import LazyLongSelect from '@activity/components/LazyLongSelect.vue'
import AllCheckbox from '@activity/pages/package/units/bulkEdit/AllCheckbox'
import AllSelector from '@activity/pages/package/units/bulkEdit/AllSelector'

export default {
  name: 'SchemaForm',
  components: {
    SchemaFormItemRender,
    LazyLongSelect,
    AllCheckbox,
    AllSelector
  },
  model: {
    prop: 'schemaConfig',
    event: 'change'
  },
  props: {
    schemaConfig: {
      type: Object,
      required: true
    },
    // Modifying will emit submit
    submitOnChange: {
      type: Boolean,
      default: false
    },
    // emit submit when mounted
    shouldEmitInitData: {
      type: Boolean,
      default: false
    }
  },
  data() {
    this.DEFAULT_CONTAINER_STYLE = {
      padding: '20px'
    }
    this.DEFAULT_FORM_CONF = {
      layout: 'inline'
    }
    this.DEFAULT_FORM_ITEM_LABEL_STYLE = {}
    this.DEFAULT_FIELD_STYLE = {}
    this.DEFAULT_RULES_MSG = 'Please fill in this field.'

    return {
      proxyFormData: {},
      showViewMore: false,
      hasViewMore: false,

      getOptionsResponseQueue: [],
      cacheGetOptionsByField: {}
    }
  },
  watch: {
    realFormData: {
      deep: true,
      handler(v) {
        if (this.submitOnChange) {
          this.handleSubmit()
        }
      }
    }
  },
  computed: {
    currentFormItems() {
      const formItems = _.get(this.schemaConfig, 'formItems', [])
      const { normal, peekaboo } = formItems.reduce(
        (acc, curr) => {
          // 会被隐藏，只有点击 view more 才显示
          if (curr.isPeekaboo) {
            acc.peekaboo.push(curr)
          } else {
            acc.normal.push(curr)
          }

          return acc
        },
        {
          normal: [],
          peekaboo: []
        }
      )

      if (peekaboo.length) {
        this.hasViewMore = true
      }

      return this.showViewMore ? [...normal, ...peekaboo] : normal
    },
    // 表单值
    realFormData() {
      const { schemaConfig } = this

      if (this.isObject(schemaConfig) && Array.isArray(schemaConfig.formItems)) {
        return schemaConfig.formItems.reduce((acc, curr) => {
          if (!curr.field) {
            throw new Error('The lack of field')
          }

          const formatValue = _.get(curr, 'inputConfig.formatValue', null)
          _.set(acc, curr.field, typeof formatValue === 'function' ? formatValue(curr.value) : curr.value)

          return acc
        }, {})
      }

      return {}
    }
  },
  beforeDestroy() {
    this.$off('getOptionsEvent')
  },
  async mounted() {
    // 对外暴露且可修改表格数据的代理
    this.proxyFormData = new Proxy(this.realFormData, {
      get: (target, key) => {
        return Reflect.get(this.realFormData, key)
      },
      set: (target, key, value) => {
        // options 数据量太大 cloneDeep 有性能问题
        // let formItems = _.cloneDeep(this.schemaConfig.formItems)
        const formItems = _.get(this.schemaConfig, 'formItems', [])

        formItems.forEach((item) => {
          if (item.field === key) {
            item.value = value
          }
        })

        this.$set(this.schemaConfig, 'formItems', formItems)

        return true
      }
    })

    await this.$nextTick()
    this.cacheFormData = _.cloneDeep(this.proxyFormData)
    if (this.shouldEmitInitData) {
      this.handleSubmit()
    }
  },
  created() {
    this.$on('getOptionsEvent', async function ({ itemField, getOptions, optionType }) {
      if (Object.prototype.hasOwnProperty.call(this.cacheGetOptionsByField, itemField)) {
        return
      }
      // 因为 options 没有做隔离，导致初始化选项的时候赋值会触发 render
      // 这里解决多次重复触发 render 而造成的接口多次请求问题，存在 Key 说明请求已经有过触发
      this.cacheGetOptionsByField[itemField] = undefined

      let options
      switch (optionType) {
        case 'merchant': {
          let allMerchants =
            (await ajax.get(ADMIN_API.act.get_merchant_list, {
              cache: true,
              version: 3,
              maxAge: 60 * 10 * 1000 // 10 mins
            })) || []

          options = allMerchants.map((merchant) => ({
            label: `${merchant.id} - ${window.KLK_LANG === 'en' ? merchant.en_name : merchant.ch_name}`,
            value: merchant.id
          }))

          break
        }
        default: {
          if (typeof getOptions === 'function') {
            options = (await getOptions.call(this)) || []
          } else {
            options = []
          }
        }
      }

      const formItemIndex = this.schemaConfig.formItems.findIndex((item) => item.field === itemField)

      this.getOptionsResponseQueue.push({
        formItemIndex,
        options
      })

      this.clearGetOptionsResponseQueue()
    })
  },
  methods: {
    // 这里通过防抖来实现收集 getOptions 触发队列
    // 减少 $set 造成的多次触发 render
    clearGetOptionsResponseQueue: _.debounce(function () {
      let formItems = _.cloneDeep(this.schemaConfig.formItems)

      let curr = this.getOptionsResponseQueue.pop()
      while (curr) {
        const { formItemIndex, options } = curr

        formItems[formItemIndex].inputConfig.options = options

        curr = this.getOptionsResponseQueue.pop()
      }

      this.$set(this.schemaConfig, 'formItems', formItems)
      this.$forceUpdate()
    }, 200),
    handleViewMore() {
      this.showViewMore = !this.showViewMore
    },
    getAttrValueList: _.debounce(async function (attr_item_id_list) {
      return (
        await ajax.get(ADMIN_API.act.get_attr_values_by_attr_item_idlist, {
          params: {
            attr_item_id_list,
            language: getEditLang()
          }
        })
      ).map((item) => ({
        value: item.attr_value_id,
        label: item.ref_field_tag + ' - ' + item.attr_value_name
      }))
    }, 300),
    isObject(data) {
      return Object.prototype.toString.call(data) === '[object Object]'
    },
    handleReset() {
      Object.entries(this.cacheFormData).forEach((item) => {
        const [key, value] = item

        this.proxyFormData[key] = value
      })
      this.$emit('reset', this.realFormData)
    },
    handleSubmit: _.debounce(function () {
      this.$emit('submit', this.realFormData)
    }, 100),
    // 获得 options 的 event bus
    getOptions2Select(inputConfig, itemField) {
      let { options = undefined, getOptions = undefined, optionType = undefined } = inputConfig

      if (Array.isArray(options)) {
        return options
      }

      this.$emit('getOptionsEvent', {
        itemField,
        getOptions,
        optionType
      })

      return []
    },
    // 对配置的事件处理函数封装一层当前 schema form 的上下文
    bindContext({ listeners, context = this, formItem } = {}) {
      return Object.entries(listeners).reduce((acc, curr) => {
        const [event, fn] = curr

        return {
          ...acc,
          [event]: (...args) => {
            return fn(
              { formItem, proxyFormData: this.proxyFormData, realFormData: this.realFormData },
              ...args
            )
          }
        }
      }, {})
    },
    /**
     * @param render: renderFunction | null, 自定义渲染函数
     * @param type: enum
     * @param data: object, 组件渲染需要的 style、props、on
     * @param inputConfig: object, 表单输入组件配置
     * @param itemField
     * @returns {*}
     */
    getDynamicComponent({ render, type, data }, inputConfig, itemField) {
      let options = []
      data = data || {}

      if (render) {
        return (
          <SchemaFormItemRender
            proxyFormData={this.proxyFormData}
            render={render}
            inputConfig={inputConfig}
            itemField={itemField}
            {...data}
          />
        )
      }

      switch (type) {
        case 'select': {
          options = this.getOptions2Select(inputConfig, itemField)

          return (
            <a-select {...data}>
              {options.map((opt) => {
                return <a-select-option value={opt.value}>{opt.label}</a-select-option>
              })}
            </a-select>
          )
        }
        case 'allSelect': {
          options = this.getOptions2Select(inputConfig, itemField)

          return <all-selector {...data} options={options} />
        }
        case 'allCheckbox': {
          options = this.getOptions2Select(inputConfig, itemField)

          return <all-checkbox {...data} options={options} />
        }
        case 'input': {
          return <a-input {...data} />
        }
        case 'treeSelect': {
          options = this.getOptions2Select(inputConfig, itemField)

          return <a-tree-select tree-data={options} {...data} />
        }
        case 'lazyLongSelect': {
          options = this.getOptions2Select(inputConfig, itemField)

          return <lazy-long-select {...data} full-opts={options} optValueKey="value" optLabelKey="label" />
        }
        case 'cascader': {
          options = this.getOptions2Select(inputConfig, itemField)

          return <a-cascader {...data} full-opts={options} />
        }
        case 'date': {
          return <a-date-picker {...data} />
        }
        case 'month': {
          return <a-month-picker {...data} />
        }
        case 'range': {
          return <a-range-picker {...data} />
        }
        case 'checkbox': {
          return <a-checkbox {...data} />
        }
        default: {
          return type + ' is invalid'
        }
      }
    }
  },
  render() {
    const { schemaConfig, realFormData, proxyFormData, $attrs, $listeners, $scopedSlots } = this
    let {
      style: formStyle = {},
      common = {},
      attrs: formAttrs = {},
      listeners: formListeners = {},
      title = null,
      container = {},
      actionsStyle = {}
    } = schemaConfig

    // 2 form model layer
    const formModelConf = {
      style: formStyle,
      props: {
        ...this.DEFAULT_FORM_CONF,
        ...$attrs,
        ...formAttrs
      },
      on: {
        ...$listeners,
        ...formListeners
      }
    }

    // 底部 action 操作区，可 slot #action 替换
    const actionListRender = (
      <div class="action-list" style={{ margin: '20px 0', ...actionsStyle }}>
        {$scopedSlots.actions ? (
          $scopedSlots.actions({ realFormData, schemaConfig, proxyFormData })
        ) : (
          <label>
            <a-button onClick={this.handleSubmit} type="primary">
              {__('global_submit')}
            </a-button>
            <a-button onClick={this.handleReset} type="link" style={{ marginRight: '12px' }}>
              {__('global_reset')}
            </a-button>
          </label>
        )}
      </div>
    )

    return (
      // 1 container layer
      <div
        class="schema-form-container"
        style={{
          ...this.DEFAULT_CONTAINER_STYLE,
          ...(container.style || {})
        }}
      >
        {$scopedSlots.title ? $scopedSlots.title() : title ? <h2>{title}</h2> : ''}
        <a-form-model {...formModelConf}>
          {this.currentFormItems.map((item) => {
            const {
              labelStyle: commonLabelStyle,
              formItemAttrs: commonFormItemAttrs,
              formItemStyle: commonFormItemStyle
            } = common // from schemaConfig.common

            // 3 form model item layer
            let {
              required = false,
              field,
              label,
              type,
              value,
              attrs: formItemAttrs = {},
              style: formItemStyle = {},
              labelStyle = {},
              listeners: formItemListeners = {},
              scopedSlots: formItemScopedSlots = {},
              inputConfig = {},
              rules = {
                required,
                message: this.DEFAULT_RULES_MSG
              }
            } = item
            // 4 form item input layer
            let {
              style: fieldStyle = this.DEFAULT_FIELD_STYLE,
              render: fieldRender = null,
              domProps = {},
              props: fieldProps = {},
              attrs: fieldAttrs = {}
            } = inputConfig

            let fieldData = {
              render: fieldRender,
              type,
              data: {
                style: fieldStyle,
                props: {
                  // Component props
                  value,
                  ...fieldProps
                },
                attrs: fieldAttrs, // html attribute
                on: {},
                domProps: domProps
              }
            }
            // 实现 v-model 功能
            if (
              [
                'select',
                'lazyLongSelect',
                'date',
                'month',
                'range',
                'allSelect',
                'allCheckbox',
                'treeSelect'
              ].includes(type)
            ) {
              fieldData.data.on.change = (value) => {
                this.proxyFormData[item.field] = value
              }
            } else {
              fieldData.data.on.input = (event) => {
                this.proxyFormData[item.field] = event.target.value
              }
            }
            // 将当前上下文注入进去
            Object.assign(
              fieldData.data.on,
              this.bindContext({ listeners: inputConfig.listeners || {}, formItem: item })
            )

            return (
              <a-form-model-item
                {...{
                  props: {
                    prop: field,
                    rules: rules,
                    ...commonFormItemAttrs,
                    ...formItemAttrs
                  },
                  style: {
                    ...commonFormItemStyle,
                    ...formItemStyle
                  },
                  on: this.bindContext({ listeners: formItemListeners, formItem: item }),
                  scopedSlots: {
                    // a-form-item label
                    label: () => (
                      <span
                        style={{
                          ...this.DEFAULT_FORM_ITEM_LABEL_STYLE,
                          ...commonLabelStyle,
                          ...labelStyle
                        }}
                      >
                        {label || field}
                      </span>
                    ),
                    ...formItemScopedSlots
                  }
                }}
              >
                {this.getDynamicComponent(fieldData, inputConfig, field)}
              </a-form-model-item>
            )
          })}
        </a-form-model>
        {this.hasViewMore ? (
          <label
            style={{
              marginTop: '24px',
              display: 'block',
              width: '100%',
              textAlign: 'center'
            }}
          >
            <span
              style={{
                cursor: 'pointer'
              }}
              onClick={() => this.handleViewMore()}
            >
              {this.showViewMore ? __('18719') : __('18718')}
              <svg-icon
                style={{
                  marginLeft: '12px',
                  transform: this.showViewMore ? 'rotate(180deg)' : 'inherit'
                }}
                icon-name="view_more"
              />
            </span>
          </label>
        ) : (
          ''
        )}
        {actionListRender}
      </div>
    )
  }
}
