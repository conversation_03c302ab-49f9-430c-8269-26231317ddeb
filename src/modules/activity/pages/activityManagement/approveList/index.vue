<!-- original: klk-admin msp  -->
<template>
  <a-modal
    class="approve-list-container"
    :title="title"
    :width="900"
    :visible="visible"
    :z-index="99"
    @cancel="handleClose"
  >
    <schema-form
      class="approve-list-search-form"
      ref="form"
      :schema-config="schemaConfig"
      :shouldEmitInitData="true"
      @submit="handleFormSubmit"
    />

    <a-table
      :pagination="pagination"
      style="margin-top: 20px; overflow: scroll"
      :columns="columns"
      :data-source="tableData"
      rowKey="ticket_id"
      @change="handlePageChange"
    />

    <logs-table class="change-log-modal" v-model="logData.visible" :logData="logData" />

    <SubmitApproveDrawer
      v-bind="{ ...$attrs, ...submit }"
      :visible.sync="submit.visible"
      @submitCompleted="refresh"
    />

    <stkModal :modal-data="stkData" style="z-index: 1001" />
  </a-modal>
</template>

<script>
import moment from 'moment'
import maps from '@activity/utils/maps.js'
import SchemaForm from './components/schemaForm'
import LogsTable from '../logsTable'
import { TICKET_TYPE_DICT, getTicketUrlByStatus } from '@activity/pages/activityManagement/approveList/utils'
import dataLib from '../dataLib'
const { allTicketSearchSchemaFormConfig } = dataLib
import { isMerchant } from '@/env'
import SubmitApproveDrawer from '../../package/units/unit-list/calendar-setting/components/submitApproveDrawer'
import { act_management_mixin } from '../mixins/index'
import { otherOptions, getOptions, getNoteData } from '@activity/pages/activityManagement/utils/index.js'

const table_mixin = {
  inject: {
    activityManagement: {
      default: undefined
    }
  },
  data() {
    return {
      submit: {
        visible: false,
        sku_id: undefined,
        activityId: undefined
      },
      // 修改 dataIndex, 需要同步替换使用了 getApproveListColumnByDataIndex 方法的相同的 dataIndex
      columns: [
        {
          title: __('48226'),
          dataIndex: 'ticket_id',
          customRender: (text, record) => {
            const { ticket_id } = record

            if (status === 1) {
              // closed
              return <span>{ticket_id}</span>
            }

            let href = getTicketUrlByStatus(record)

            return (
              <router-link to={{ path: href }} target="_blank">
                {ticket_id}
              </router-link>
            )
          }
        },
        {
          title: __('48227'),
          dataIndex: 'ticket_type',
          customRender: (text, record) => {
            const html = TICKET_TYPE_DICT[record.ticket_type]

            return <span>{html}</span>
          }
        },
        {
          title: __('package_id_title'),
          dataIndex: 'package_id',
          customRender: (text, record) => {
            const { ticket_type, package_id, package_title } = record
            // Cost Modification 或 New Package
            if (['new_package', 'cost_modification'].includes(ticket_type)) {
              return (
                <span>
                  {package_id} - {package_title}
                </span>
              )
            }

            return '-'
          }
        },
        {
          title: __('48222'),
          dataIndex: 'sku_id',
          customRender: (text, record) => {
            const { ticket_type, sku_id, sku_title, activity_id, package_id, submitted_language } = record

            // Cost Modification
            if (['cost_modification'].includes(ticket_type) && sku_id) {
              let href = ''
              if (isMerchant) {
                href = `/act/package/unit/${activity_id}?edit_status=1&ref=&lang=${submitted_language}&package_type=-1&package_id=${package_id}&sku_id=${sku_id}`
              } else {
                href = `/act/package/schedule/${activity_id}?edit_status=1&ref=&lang=${submitted_language}&package_type=-1&package_id=${package_id}&page_from=bd_audit`
              }

              return (
                <router-link to={{ path: href }} target="_blank">
                  {sku_id} - {sku_title}
                </router-link>
              )
            }

            return '-'
          }
        },
        {
          title: __('submit_time_bj'),
          dataIndex: 'submit_time_beijing',
          customRender: (text, record) => {
            return (
              <span>
                {record.submit_time_beijing
                  ? moment(record.submit_time_beijing).format('YYYY-MM-DD hh:mm:ss')
                  : '-'}
              </span>
            )
          }
        },
        {
          title: __('48230'),
          dataIndex: 'ticket_status',
          customRender: (text, record) => {
            const { ticket_status } = record
            const statusText = this.mapsFilterFn('ticket_status', ticket_status)
            const statusClass = [
              'common-status-style',
              this.mapsFilterFn('ticket_status', ticket_status, 'class')
            ]

            return (
              <a-tooltip overlayClassName="common-tooltip-style" title={record.note || ''}>
                <label onClick={() => this.handleTicketStatusChangeLog(record)}>
                  <span class={statusClass.join(' ')}>{statusText}</span>
                  <a-icon
                    style={{
                      marginLeft: '4px',
                      cursor: 'pointer'
                    }}
                    type="clock-circle"
                  />
                </label>
              </a-tooltip>
            )
          }
        },
        {
          title: __('global_operation'),
          dataIndex: 'id',
          customRender: (text, record) => {
            const { ticket_status, ticket_type } = record

            const cleanDom =
              ['ToBeSubmitted', 'Rejected'].includes(ticket_status) && ticket_type === 'cost_modification' ? (
                <a-button style="margin-right: 12px;" onClick={() => this.handleTableCleanDraft(record)}>
                  {this.$t('48075')}
                </a-button>
              ) : (
                ''
              )

            const withdrawDom = ['PendingApproval'].includes(ticket_status) ? (
              <a-button onClick={() => this.handleTableWithdraw(record)}>{this.$t('48078')}</a-button>
            ) : (
              ''
            )

            const toText = {
              cost_modification: __('48092'),
              new_activity: __('merchant_submit_activity_action'),
              new_package: __('merchant_submit_package_action'),
              activity_publish: __('merchant_submit_activity_action'),
              package_publish: __('merchant_submit_package_action')
            }[ticket_type]
            const submitDom =
              ['ToBeSubmitted', 'Rejected'].includes(ticket_status) && isMerchant ? (
                <a-button
                  type="primary"
                  style="margin-right: 12px;"
                  onClick={() => this.handleTableSubmit(record)}
                >
                  {toText}
                </a-button>
              ) : (
                ''
              )

            let publishAction = ''
            if (
              ['PendingApproval'].includes(ticket_status) &&
              (ticket_type === 'activity_publish' || ticket_type === 'package_publish')
            ) {
              publishAction = (
                <div>
                  <a-button
                    type="primary"
                    style="margin-right: 12px;"
                    onClick={() => this.handleApprove(record)}
                  >
                    {this.$t('approve')}
                  </a-button>
                  <a-button onClick={() => this.handleReject(record)}>{this.$t('reject')}</a-button>
                </div>
              )
            }

            if (isMerchant) {
              return (
                <label class="operation-list">
                  {submitDom}
                  {withdrawDom}
                  {cleanDom}
                </label>
              )
            } else {
              return <label class="operation-list">{publishAction}</label>
            }
          }
        }
      ],
      pagination: {
        pageSizeOptions: ['10', '20', '50'],
        showSizeChanger: true,
        total: 0,
        current: 1,
        pageSize: 10
      },
      tableData: [],
      logData: {
        visible: false,
        loading: false,
        logTable: {
          dataSource: []
        }
      }
    }
  },
  watch: {
    'pagination.pageSize'() {
      this.handleSearch()
    },
    'pagination.current'() {
      this.handleSearch(false)
    }
  },
  methods: {
    async handleApprove(record) {
      const { ticket_type, ticket_id, activity_id, package_id } = record
      const urls = {
        activity_publish: ADMIN_API.act.act_update_status_by_ticket,
        package_publish: ADMIN_API.act.pkg_update_status_by_ticket
      }
      const datas = {
        activity_publish: {
          ticket_id,
          status: 1,
          activity_id,
          language: klook.getEditLang(),
          page_from: klook.getPlatformRoleKey()
        },
        package_publish: {
          ticket_id,
          status: 1,
          package_id
        }
      }
      const result = await ajax.postBody(
        urls[ticket_type],
        {
          data: datas[ticket_type]
        },
        { loading: true }
      )

      if (result && result.success) {
        this.$refs.form.handleSubmit()
      }
    },
    async handleReject(row) {
      const { ticket_type } = row
      const isAct = ticket_type === 'activity_publish'
      row.note = ''
      let title = isAct ? this.$t('activity_reject') : this.$t('package_reject')
      let okButtonProps = {
        props: {
          disabled: true
        }
      }
      const url = isAct ? 'get_act_reject_reason' : 'get_pkg_reject_reason'
      const cache = isAct ? this.actRejectOptions : this.pkgRejectOptions
      // 用缓存
      const opts = cache || (await getOptions(ADMIN_API.act[url], { loading: true }))
      if (isAct) {
        this.actRejectOptions = opts
      } else {
        this.pkgRejectOptions = opts
      }
      let node = (
        <a-form-model>
          <a-form-model-item label={__('85321')} required>
            <a-cascader
              options={opts}
              onchange={(e) => {
                const value = e
                row.value = value
                const validate = value.some((val) => {
                  return otherOptions.includes(val)
                })
                okButtonProps.props.disabled = value.length === 0 || (!row.note && validate)
              }}
            ></a-cascader>
          </a-form-model-item>
          <a-form-model-item label="Please specify your reason here">
            <a-textarea
              v-model={row.note}
              placeholder={__('global_please_input')}
              rows={4}
              onkeyup={(e) => {
                const value = row.value || []
                row.note = _.trim(e.target.value)
                const validate = value.some((val) => {
                  return otherOptions.includes(val)
                })
                okButtonProps.props.disabled = value.length === 0 || (!row.note && validate)
              }}
              maxLength={500}
            ></a-textarea>
          </a-form-model-item>
        </a-form-model>
      )
      this.$modal.confirm({
        title,
        maskClosable: true,
        cancelText: this.$t('global_cancel'),
        okText: this.$t('global_submit'),
        okButtonProps,
        content: (h) => node,
        onOk: async () => {
          this.postReject(row)
        },
        onCancel() {
          row.value = []
        }
      })
    },
    async postReject({ ticket_id, note, value }) {
      const noteData = getNoteData(value, note)
      const data = {
        ticket_id,
        note: noteData,
        action: 1
      }
      const result = await ajax.postBody(
        ADMIN_API.act.update_ticket_status_v2,
        {
          data
        },
        { loading: true }
      )

      if (result && result.success) {
        this.$refs.form.handleSubmit()
      }
    },
    mapsFilterFn: maps.filterFn,
    handlePageChange(pagination) {
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
    },
    refresh() {
      this.handleSearch() // 刷新自身列表数据
      this?.activityManagement?.get_activities?.() // 刷新活动审核列表数据
      // 在上新页面点开时，有操作需要更新上新状态
      klook.bus.$emit('initQueryData2bus')
      klook.bus.$emit('updatePkgInfo2bus')
    },
    async handleTableSubmit(row) {
      const { ticket_type } = row

      switch (ticket_type) {
        case 'cost_modification':
          this.submit.visible = true
          this.submit.sku_id = row.sku_id
          this.submit.activityId = row.activity_id
          break
        case 'new_activity':
          await this.actionObj.handleSubmitActivity(row)
          this.refresh()
          break
        case 'package_publish':
        case 'activity_publish':
          await this.updateTickeyStatusV2(row, 4)
          this.refresh()
          break
        default:
          await this.actionObj.handleSubmitPackage(row)
          this.refresh()
      }
    },
    async updateTickeyStatus(row) {
      await ajax.postBody(ADMIN_API.act.update_ticket_status, {
        data: {
          sku_id: row.sku_id,
          note: '',
          action: 3
        }
      })
    },
    async updateTickeyStatusV2(row, action) {
      await ajax.postBody(ADMIN_API.act.update_ticket_status_v2, {
        data: {
          ticket_id: row.ticket_id,
          note: '',
          action
        }
      })
    },
    async handleWithdrawSubmit(row) {
      const { ticket_type } = row
      const type = {
        new_activity: 'activity',
        new_package: 'package'
      }[ticket_type]

      await this.actionObj.handleWithdrawSubmit(row, type, () => {
        // 自带有 get_activities
        this.refresh()
      })
    },
    async handleTableWithdraw(row) {
      const { ticket_type } = row
      switch (ticket_type) {
        case 'cost_modification':
          this.updateTickeyStatus(row)
          this.$message.success(this.$t('global_success'))
          this.refresh()
          break
        case 'package_publish':
        case 'activity_publish':
          this.updateTickeyStatusV2(row, 3)
          this.$message.success(this.$t('global_success'))
          this.refresh()
          break
        default:
          this.handleWithdrawSubmit(row)
      }
    },
    async handleTableCleanDraft(row) {
      await ajax.postBody(ADMIN_API.act.update_ticket_status, {
        data: {
          sku_id: row.sku_id,
          note: '',
          action: 2
        }
      })
      this.$message.success(this.$t('global_success'))
      this.refresh()
    }
  }
}

export default {
  name: 'ApproveList',
  model: {
    value: 'visible',
    event: 'update'
  },
  mixins: [table_mixin, act_management_mixin],
  components: { SubmitApproveDrawer, LogsTable, SchemaForm },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: __('48224')
    },
    data: {
      type: Object,
      required: true
    },
    autoFillData: {
      type: Object,
      default: undefined
    }
  },
  data() {
    const that = this

    return {
      schemaConfig: {
        ...allTicketSearchSchemaFormConfig,
        formItems: [
          {
            field: 'ticket_id'
          },
          {
            field: 'package_id',
            inputConfig: {
              async getOptions() {
                return (
                  // only use to merchant platform, getOptions rewrite and cover on admin
                  (
                    await ajax.post(ADMIN_API.act.filter_ticket_option, {
                      data: {
                        activity_id: [+that.activity_id]
                      }
                    })
                  ).package
                )
              }
            }
          },
          {
            field: 'sku_id',
            inputConfig: {
              async getOptions() {
                return (
                  // only use to merchant platform, getOptions rewrite and cover on admin
                  (
                    await ajax.post(ADMIN_API.act.filter_ticket_option, {
                      data: {
                        activity_id: [+that.activity_id]
                      }
                    })
                  ).sku
                )
              }
            }
          },
          {
            field: 'ticket_type'
          },
          {
            field: 'ticket_status'
          },
          {
            field: 'submitted_time'
          }
        ].map((item) => {
          let curr = _.find(allTicketSearchSchemaFormConfig.formItems, { field: item.field })

          return {
            ...curr,
            inputConfig: {
              ...(curr.inputConfig || {}),
              ...(item.inputConfig || {})
            }
          }
        })
      }
    }
  },
  watch: {
    autoFillData: {
      immediate: true,
      deep: true,
      async handler() {
        if (this.autoFillData) {
          const { package_id, sku_id } = this.autoFillData

          await this.$nextTick()

          package_id && (this.$refs.form.proxyFormData.package_id = [package_id])
          sku_id && (this.$refs.form.proxyFormData.sku_id = [sku_id])
        }
      }
    }
  },
  computed: {
    activity_id() {
      return +(this.data.activity_id || this.$route.query.activity_id) // 表格进来 或者 上新步骤进来
    }
  },
  methods: {
    handleSearch(resetPaginationCurrent = true) {
      if (resetPaginationCurrent) {
        this.pagination.current = 1
      }

      this.$refs.form.handleSubmit()
    },
    async handleFormSubmit(data) {
      let { current, pageSize } = this.pagination
      let { submitted_time, ticket_id, package_id, sku_id, ...otherData } = data
      let [start, end] = submitted_time

      let { items, total } =
        (await ajax.post(ADMIN_API.act.filter_ticket, {
          data: {
            activity_id: [+this.activity_id],
            page: current,
            limit: pageSize,
            submitted_time_utc_start: start || '',
            submitted_time_utc_end: end || '',
            ticket_id: this.formatPostNumber(ticket_id),
            package_id: this.formatPostNumber(package_id),
            sku_id: this.formatPostNumber(sku_id),
            ...otherData
          }
        })) || {}

      this.$set(this, 'tableData', items || [])
      this.pagination.total = total || 0
    },
    formatPostNumber(data) {
      return Array.isArray(data) ? data.map((item) => +item) : data ? [Number(data)] : []
    },
    handleClose() {
      let { query = {}, name } = this.$route

      if (name === 'activityManagement' && hasOwnProperty.call(query, 'activity_id')) {
        this.$router.replace({
          name: 'activityManagement'
        })
      }

      setTimeout(() => {
        this.$emit('update', false)
      })
    },
    async handleTicketStatusChangeLog(row) {
      if (row.package_archived) return

      let { logData } = this
      logData.loading = true

      logData.logTable.dataSource = []
      logData.visible = true

      logData.logTable.dataSource =
        (await ajax.get(ADMIN_API.act.get_ticket_log, {
          params: {
            ticket_id: row.ticket_id
          }
        })) || []

      logData.loading = false
    }
  }
}
</script>

<style lang="scss">
@import '../../package/units/unit-list/calendar-setting/mixins';

.ant-select-dropdown-menu-item {
  white-space: normal;
}

.approve-list-container {
  .common-status-style {
    @include mixin-status-text();
  }
}
</style>
