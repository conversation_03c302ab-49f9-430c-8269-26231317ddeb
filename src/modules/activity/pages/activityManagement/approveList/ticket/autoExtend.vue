<template>
  <div class="ticket-auto-extend-contaienr">
    <auto-extend-table
      v-if="renderTable"
      ref="table"
      v-bind="$attrs"
      v-on="$listeners"
      :table-data="tableData"
      :cache-table-data="cacheTableData"
      :sku_id="skuId"
      :skuModel="skuModel"
      :selling_currency="selling_currency"
      :cost_currency="cost_currency"
      :exchange_rate="exchange_rate"
      :customColumnFieldList="customColumnFieldList"
      :viewField="ticketViewFieldList"
      :invalidRecordList.sync="invalidRecordList"
      :showBtnList="canOperate"
      :openApprovalDetailPopover="false"
      :showCostInputSuffixData="false"
      :unitData="unitData"
      @computedInvalidData="computedInvalidData"
    />
  </div>
</template>

<script>
import AutoExtendTable from '../../../package/units/autoExtend/table'
import { table_mixin } from '../../../package/units/autoExtend/mixins'
import { isMerchant } from '@/env'
import { customColumnFieldList } from './utils'
import { mapState } from 'vuex'

export default {
  name: 'ticketAutoExtend',
  mixins: [table_mixin],
  components: {
    AutoExtendTable
  },
  props: {
    originalData: {
      type: Object,
      required: true
    },
    unitData: {
      type: Object,
      required: true
    },
    // 0: none, 1: pending, 2: reject, 3: resolve
    approvalStatus: {
      type: Number,
      default: 0
    },
    viewField: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableData: [],
      cacheTableData: [],

      invalidRecordList: [],

      exchange_rate: [],

      skuModel: {},

      selling_currency: '',
      cost_currency: '',

      isMerchant,

      renderTable: false,

      customColumnFieldList
    }
  },
  watch: {
    originalData: {
      immediate: true,
      deep: true,
      handler(v) {
        !this.renderTable && this.initData(v)
      }
    }
  },
  computed: {
    ...mapState(['ticketViewFieldList']),
    skuId() {
      return this.unitData.sku_id
    },
    canOperate() {
      return !this.isMerchant && this.unitData.ticket_status === 'PendingApproval'
    }
  },
  methods: {
    async getData() {
      this.invalidRecordList = []

      let { weekly, invalidRecordList, notEmptyCount } = this.getInvalidData({
        tableData: this.tableData,
        skuId: this.skuId,
        tableFiledList: this.customColumnFieldList,
        skuFieldDisabledStateObj: this.$refs.table.skuFieldDisabledStateObj
      })

      if (invalidRecordList.length) {
        this.invalidRecordList = invalidRecordList
        this.$refs.table.goToTableByUUID(invalidRecordList[0].uuid)
        this.$message.warn(__('package_error_mandatory'))
        return
      }

      if (notEmptyCount === 0) {
        this.$message.warn('Please fill in at least one')
        return
      }

      return {
        ...this.originalData,
        weekly: Object.values(weekly)
          .filter((item) => item.schedules.length) // empty 不需要传给后端
          .map((item) => ({
            ...item,
            schedules: item.schedules.map((schedule) => {
              if (!isNaN(schedule.price?.take_rate)) {
                schedule.price.take_rate = String(schedule.price.take_rate)
              }

              return schedule
            })
          }))
      }
    },
    computedInvalidData(rowIndex) {
      const { invalidRecordList } = this.getInvalidRecordByIndex({
        rowIndex,
        tableData: this.tableData,
        skuId: this.skuId,
        tableFiledList: this.customColumnFieldList,
        skuFieldDisabledStateObj: this.$refs.table.skuFieldDisabledStateObj
      })

      this.$set(
        this,
        'invalidRecordList',
        _.uniqWith([...this.invalidRecordList, ...invalidRecordList], _.isEqual)
      )
    },
    async initData(response) {
      let { cost_currency, selling_currency, inv_model, pricing_model, exchange_rate, tableData } =
        await this.fmtInitData(
          response,
          { ticket_status: this.unitData.ticket_status },
          {
            fmtPrice: ({ price }) => {
              price.selling_price = ''

              return price
            }
          }
        )

      this.$set(this, 'skuModel', { inv_model, pricing_model })
      this.cost_currency = cost_currency
      this.selling_currency = selling_currency
      this.exchange_rate = exchange_rate

      this.$set(
        this,
        'tableData',
        tableData.map((item, index) => ({
          ...item,
          uuid: '_' + index // row index
        }))
      )
      this.cacheTableData = _.cloneDeep(this.tableData)

      this.renderTable = true
    }
  }
}
</script>
