<template>
  <div class="ticket-bulk-edit-table">
    <a-spin :spinning="spinning" :delay="300">
      <bulk-table
        v-if="renderTable"
        ref="table"
        :list="list"
        :sku-data="[unitData]"
        :key="refreshKey"
        :view-field="ticketViewFieldList"
        :customColumnFieldList="customColumnFieldList"
        :hiddenActPkgInfo="true"
        :showTableFix="canOperate"
        :showHeaderOperatorRow="canOperate"
        :openGuide="false"
        :activateDisplayTableIndexByFilter="true"
        :openApprovalDetailPopover="false"
        :showCostInputSuffixData="false"
        @differenceField="changeDifferenceField"
        @tableState="changeCanSubmit"
      />
    </a-spin>
  </div>
</template>

<script>
import { isMerchant } from '@/env'
import { customColumnFieldList } from './utils'
import BulkTable from '../../../package/units/bulkEdit/BulkTable'
import { sendGuide } from '@activity/utils/guide'
import { mapState } from 'vuex'

export default {
  name: 'ticketBulkEditTable',
  provide() {
    return {
      activity_id: this.basicInfo.activity_id,
      sendGuide: (...args) => {
        return sendGuide.apply(this, args)
      }
    }
  },
  components: {
    BulkTable
  },
  props: {
    unitData: {
      type: Object,
      required: true
    },
    list: {
      type: Array,
      required: true
    },
    basicInfo: {
      type: Object,
      required: true
    },
    ticketStatus: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isMerchant,
      spinning: false,

      tableState: {
        updated: false,
        deleted: false
      },
      refreshKey: 0,

      customColumnFieldList,

      renderTable: false
    }
  },
  watch: {
    list: {
      immediate: true,
      deep: true,
      handler(v) {
        if (v.length && !this.renderTable) {
          this.renderTable = true
          this.cacheData = _.cloneDeep(v)
        }
      }
    }
  },
  computed: {
    ...mapState(['ticketViewFieldList']),
    canOperate() {
      return !this.isMerchant && this.ticketStatus === 'PendingApproval'
    }
  },
  methods: {
    async getData() {
      let data = await this.$refs.table.triggerToSubmit({
        passPriceFillReason: true,
        hasTakeRateFieldData: true
      })
      let schedules = _.get(data, 'sku_list.0.schedules', [])

      return _.uniqBy([...schedules, ...this.cacheData[0].calendar], 'start_time')
    },
    changeCanSubmit(v) {
      this.$set(this, 'tableState', v)
    },
    changeDifferenceField(v) {
      this.$store.commit('setTicketViewFieldList', [...this.viewField, ...v])
    }
  }
}
</script>
