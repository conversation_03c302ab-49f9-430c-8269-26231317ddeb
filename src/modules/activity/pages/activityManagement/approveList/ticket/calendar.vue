<template>
  <div class="ticket-calendar">
    <a-spin :spinning="spinning" :delay="300">
      <Calendar
        v-if="calendarSettingData"
        :calendar-date="calendarDate"
        :unit-data="unitData"
        :hideCalendarBtns="true"
        :hideCalendarFilter="true"
        :calendarSettingData="calendarSettingData"
        :closeSetDateRange="true"
      />
    </a-spin>
  </div>
</template>

<script>
import Calendar from '../../../package/units/unit-list/calendar-setting/Calendar'

export default {
  name: 'ticketCalendar',
  components: {
    Calendar
  },
  props: {
    unitData: {
      type: Object,
      required: true
    }
  },
  provide() {
    return {
      // 兼容 calendar 组件方法调用
      calendarSetting: this,
      unitBar: {
        setCalendarDate: (v) => {
          this.$set(this, 'calendarDate', v)
        }
      }
    }
  },
  data() {
    return {
      calendarDate: new Date(),
      dateRange: [null, null],
      calendarSettingData: null
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    async initData() {
      this.spinning = true

      this.calendarSettingData =
        (await ajax.get(ADMIN_API.act.get_calendar_setting, {
          params: {
            sku_id: this.unitData.sku_id
          }
        })) || {}

      this.spinning = false
    }
  }
}
</script>
