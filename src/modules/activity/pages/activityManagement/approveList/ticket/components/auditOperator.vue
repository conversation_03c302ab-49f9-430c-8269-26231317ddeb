<template>
  <div class="ticket-operator-container">
    <a-tooltip placement="top" :title="$t('activity_event_logs')">
      <div
        :class="['common-status-style', mapsFilterFn('ticket_status', ticketStatus, 'class')]"
        style="cursor: pointer"
        @click="handleTicketStatusChangeLog"
      >
        {{ mapsFilterFn('ticket_status', ticketStatus) }}
        <a-icon style="margin-left: 4px; cursor: pointer" type="clock-circle" />
      </div>
    </a-tooltip>

    <template v-for="item in actions">
      <a-popconfirm
        v-if="item.confirm"
        class="operator-item"
        :title="item.confirm.title"
        :ok-text="item.confirm.okText || $t('global_confirm')"
        :cancel-text="item.confirm.cancelText || $t('global_button_cancel')"
        :key="item.label"
        placement="bottomRight"
        @confirm="item.handle"
      >
        <a-button :type="item.btnType || 'default'">{{ item.label }}</a-button>
      </a-popconfirm>
      <a-button v-else :key="item.label" :type="item.btnType || 'default'" @click="item.handle">{{
        item.label
      }}</a-button>
    </template>

    <TicketConfirm :visible.sync="confirm.visible" v-bind="confirm" @submit="confirm.cb"> </TicketConfirm>

    <logs-table class="change-log-modal" v-model="logData.visible" :logData="logData" />
  </div>
</template>

<script>
import { isMerchant } from '@/env'
import maps from '@activity/utils/maps.js'
import TicketConfirm from './confirm'
import LogsTable from '../../../logsTable'
import { priceOtherOptions, getOptions, getNoteData } from '@activity/pages/activityManagement/utils/index.js'

export default {
  name: 'TicketAuditOperator',
  components: {
    LogsTable,
    TicketConfirm
  },
  inject: ['refreshTicketDetail'],
  props: {
    ticketStatus: {
      type: String,
      required: true
    },
    sku_id: {
      type: Number,
      required: true
    },
    unitData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      logData: {
        visible: false,
        loading: false,
        logTable: {
          dataSource: []
        }
      },

      isMerchant,

      confirm: {
        visible: false,
        cb: () => {},
        title: '',
        okBtn: ''
      }
    }
  },
  computed: {
    ticketId() {
      return this.$route.params.ticket_id
    },
    actions() {
      let actions = []
      const { ticketStatus } = this

      if (this.isMerchant) {
        switch (ticketStatus) {
          case 'ToBeSubmitted': {
            actions.push(
              ...[
                {
                  label: __('48090'),
                  confirm: {
                    title: `${__('48090')} ?`,
                    cancelText: __('48093')
                  },
                  handle: () => {
                    this.openConfirm({
                      cb: this.handleSubmit,
                      title: __('48094')
                    })
                  },
                  btnType: 'primary'
                },
                {
                  label: 'Close',
                  confirm: {
                    title: 'Close ?'
                  },
                  handle: () => {
                    this.openConfirm({
                      cb: this.handleClose
                    })
                  }
                }
              ]
            )
            break
          }
          case 'PendingApproval': {
            actions.push(
              ...[
                {
                  label: __('48108'),
                  confirm: {
                    title: `${__('48108')} ?`
                  },
                  handle: () => {
                    this.handleWithdraw()
                  }
                }
              ]
            )
            break
          }
          case 'Rejected': {
            actions.push(
              ...[
                {
                  label: 'Close',
                  confirm: {
                    title: 'Close ?'
                  },
                  handle: () => {
                    this.openConfirm({
                      cb: this.handleClose
                    })
                  }
                }
              ]
            )
            break
          }
        }
      } else {
        // !merchant
        if (ticketStatus === 'PendingApproval') {
          actions.push(
            ...[
              {
                label: __('48109'),
                handle: this.handleReject
              },
              {
                label: __('48110'),
                confirm: {
                  title: `${__('48110')} ?`
                },
                handle: this.handleApprove,
                btnType: 'primary'
              }
            ]
          )
        }
      }

      return actions
    }
  },
  methods: {
    mapsFilterFn: maps.filterFn,
    async handleTicketStatusChangeLog() {
      let { logData } = this
      logData.loading = true

      logData.logTable.dataSource = []
      logData.visible = true

      logData.logTable.dataSource =
        (await ajax.get(ADMIN_API.act.get_ticket_log, {
          params: {
            ticket_id: this.ticketId
          }
        })) || []

      logData.loading = false
    },
    refresh() {
      this.$set(this, 'confirm', this.$options.data().confirm)
      this.refreshTicketDetail?.()
    },
    openConfirm({ cb, title = '', okBtn = '' } = {}) {
      this.$set(this, 'confirm', {
        visible: true,
        cb,
        title,
        okBtn
      })
    },
    async handleClose(note) {
      await ajax.postBody(ADMIN_API.act.update_ticket_status, {
        data: {
          sku_id: this.sku_id,
          note,
          action: 2
        }
      })

      this.refresh()
    },
    async handleSubmit(note) {
      await ajax.postBody(ADMIN_API.act.submit_cost_multi, {
        data: {
          sku_list: [
            {
              sku_id: this.sku_id,
              note
            }
          ]
        }
      })

      this.refresh()
    },
    async handleWithdraw() {
      await ajax.postBody(ADMIN_API.act.update_ticket_status, {
        data: {
          sku_id: this.sku_id,
          note: '',
          action: 3
        }
      })

      this.refresh()
    },
    async handleReject({ okCallback = null } = {}) {
      let row = {
        note: ''
      }

      let okButtonProps = {
        props: {
          disabled: true
        }
      }
      // 用缓存
      const opts =
        this.cacheOptions ||
        (await getOptions(ADMIN_API.act.get_price_ticket_reject_reason, { loading: true }))
      this.cacheOptions = opts
      let node = (
        <a-form-model>
          <a-form-model-item label={__('85321')} required>
            <a-cascader
              options={opts}
              onchange={(e) => {
                const value = e
                row.value = value
                const validate = value.some((val) => {
                  return priceOtherOptions.includes(val)
                })
                okButtonProps.props.disabled = value.length === 0 || (!row.note && validate)
              }}
            ></a-cascader>
          </a-form-model-item>
          <a-form-model-item label="Please specify your reason here">
            <a-textarea
              v-model={row.note}
              placeholder={__('global_please_input')}
              rows={4}
              onkeyup={(e) => {
                const value = row.value || []
                row.note = _.trim(e.target.value)
                const validate = value.some((val) => {
                  return priceOtherOptions.includes(val)
                })
                okButtonProps.props.disabled = value.length === 0 || (!row.note && validate)
              }}
              maxLength={500}
            ></a-textarea>
          </a-form-model-item>
        </a-form-model>
      )

      this.$modal.confirm({
        title: __('orderdetail_text_reject_reason'),
        maskClosable: true,
        cancelText: this.$t('global_cancel'),
        okText: this.$t('48109'),
        okButtonProps,
        content: (h) => node,
        onOk: async () => {
          const noteData = getNoteData(row.value, row.note)
          okCallback && okCallback({ row })
          this.$emit('reject', { ...row, note: noteData })
          this.$set(this, 'confirm', this.$options.data().confirm)
        },
        onCancel() {
          row.value = []
        }
      })
    },
    handleApprove() {
      this.$emit('approve')
      this.$set(this, 'confirm', this.$options.data().confirm)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../package/units/unit-list/calendar-setting/mixins';

.ticket-operator-container {
  display: flex;

  .common-status-style {
    width: max-content;

    @include mixin-status-text();
  }
}
</style>
