<template>
  <a-modal :visible.sync="_visible" :width="600" title="Note" @cancel="() => (_visible = false)">
    <a-form-model ref="form" :model="form" label-width="120px">
      <a-form-model-item prop="select" :rules="{ required: true, message: 'Please select' }">
        <a-select v-model="form.select" style="width: 356px;" @change="() => changeNoteSelect(index)">
          <a-select-option v-for="note in noteDict" :key="note.value" :value="note.value">
            {{ note.label }}
          </a-select-option>
        </a-select>
      </a-form-model-item>

      <a-form-model-item
        v-if="form.select === 4"
        prop="note"
        :rules="{ required: form.select === 4, message: 'Please input note' }"
      >
        <a-textarea v-model="form.note" style="margin-top: 8px;"></a-textarea>
      </a-form-model-item>
    </a-form-model>

    <div slot="footer">
      <a-button @click="_visible = false">{{ $t('global_cancel') }}</a-button>
      <a-button type="primary" @click="handleSubmit">{{ $t('global_submit') }}</a-button>
    </div>
  </a-modal>
</template>

<script>
const noteDict = [
  { label: 'Seasonality', value: 1 },
  { label: 'Promotion', value: 2 },
  { label: 'Change in operational cost/ strategy', value: 3 },
  { label: 'Other', value: 4 }
]

export default {
  name: 'TicketConfirm',
  props: {
    visible: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      noteDict,

      form: {
        select: undefined,
        note: ''
      }
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    async handleSubmit() {
      const valid = await this.$refs.form.validate().catch(() => false)

      if (!valid) {
        return
      }

      if (this.form.select === 4) {
        this.$emit('submit', this.form.note)
      } else {
        const curr = this.noteDict.find((item) => item.value === this.form.select)
        this.$emit('submit', curr.label)
      }
    },
    changeNoteSelect() {
      this.$refs.form?.validate?.()
    }
  }
}
</script>

<style scoped lang="scss"></style>
