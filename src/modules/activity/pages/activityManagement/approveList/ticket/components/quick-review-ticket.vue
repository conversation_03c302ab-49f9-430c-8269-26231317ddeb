<template>
  <span>
    <a-popover
      v-if="isCostModification"
      :visible.sync="visible"
      overlay-class-name="quick-review-ticket-popover"
      placement="rightTop"
      trigger="click"
      :overlay-style="{ width: '720px' }"
      :get-popup-container="getPopupContainer"
      arrow-point-at-center
      destroy-tooltip-on-hide
    >
      <template slot="title">
        <div class="title">
          <span class="title-content">Quick review for price change (Fixed price model only)</span>
          <a-button type="link" @click="clickTicketItem(false)">
            View details <a-icon type="right" />
          </a-button>
        </div>
      </template>

      <div slot="content" class="wrapper">
        <div class="body">
          <TicketFixedPriceForm
            ref="fixedPriceForm"
            :unit-data="unitData"
            :ticket-status="ticketStatus"
            :activity-id="activityId"
            :calendar-setting-data="calendarSettingData"
            :custom-hidden-field="['ssp']"
          ></TicketFixedPriceForm>

          <a-form-model v-if="isPendingApproval" ref="form" :model="form" :colon="false">
            <a-form-model-item prop="action" label="Review result" required>
              <CardRadioGroup v-model="form.action" :options="options"></CardRadioGroup>
            </a-form-model-item>
            <template v-if="isReject">
              <a-form-model-item prop="reason" :label="$t('85321')" required>
                <a-cascader
                  v-model="form.reason"
                  :options="rejectOptions || []"
                  :get-popup-container="getCascaderPopupContainer"
                ></a-cascader>
              </a-form-model-item>
              <a-form-model-item prop="note" label="Please specify your reason here">
                <a-textarea v-model="form.note" :rows="4" :max-length="500"></a-textarea>
              </a-form-model-item>
            </template>
          </a-form-model>

          <div v-else class="ticket-result">
            <div class="ticket-result-title">Review result</div>
            <div :class="['common-status-style', mapsFilterFn('ticket_status', ticketStatus, 'class')]">
              {{ mapsFilterFn('ticket_status', ticketStatus) }}
            </div>
            <div v-if="logItems.length" class="latest-log">
              <div v-for="(item, index) in logItems" :key="index" class="latest-log-item">
                <div class="latest-log-item-label">{{ item.label }}</div>
                <div class="latest-log-item-value">{{ item.value }}</div>
              </div>
            </div>
          </div>
        </div>

        <footer class="footer">
          <a-button @click="onClose">{{ $t('78466') }}</a-button>
          <a-button
            v-if="isPendingApproval"
            :disabled="form.action === undefined"
            type="primary"
            @click="onConfirm"
          >
            {{ $t('global_confirm') }}
          </a-button>
        </footer>
      </div>
      <a-button type="link" @click="clickTicketItem(true)">
        <slot></slot>
      </a-button>
    </a-popover>

    <a-button v-else type="link" @click="clickTicketItem(false)">
      <slot></slot>
    </a-button>
  </span>
</template>

<script>
import { mapState } from 'vuex'
import { isMerchant } from '@/env'
import maps from '@activity/utils/maps.js'
import { getTicketUrlByStatus } from '@activity/pages/activityManagement/approveList/utils'
import { getOptions } from '@activity/pages/activityManagement/utils/index.js'
import TicketFixedPriceForm from '~src/modules/activity/pages/activityManagement/approveList/ticket/fixedPriceForm.vue'
import CardRadioGroup from '~src/modules/aidRevamp/components/common/card-radio-group/index.vue'

const COST_MODIFICATION = 'cost_modification'
let currInstance = null

export default {
  name: 'QuickReviewTicket',
  components: {
    TicketFixedPriceForm,
    CardRadioGroup
  },
  provide() {
    return {
      calendarSetting: null
    }
  },
  props: {
    width: {
      type: Number,
      default: 800
    },
    data: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  data() {
    this.COST_MODIFICATION = COST_MODIFICATION
    return {
      isPendingApproval: false,

      visible: false,
      unitData: {},
      activityId: undefined,
      basicInfo: {},
      calendarSettingData: {},
      ticketStatus: '',

      form: {
        action: undefined, // 0 approve, 1 reject
        reason: [],
        note: ''
      },
      logs: [],
      rejectOptions: []
    }
  },
  computed: {
    ...mapState(['clickTarget']),
    options() {
      return [
        { label: this.$t('48110'), value: 0 },
        { label: this.$t('48109'), value: 1 }
      ]
    },
    isReject() {
      return this.form.action === 1
    },
    isCostModification() {
      return this.data.ticket_type === COST_MODIFICATION
    },
    latestLog() {
      const log = this.logs?.[0]

      if (!log) {
        return {}
      }

      const note = log.note.split('/')

      return {
        ...log,
        reason: note.slice(0, -1).join('/'),
        text: note.slice(-1)[0]
      }
    },
    logItems() {
      if (!this.isRejected) {
        return []
      }

      const { reason = '', text = '' } = this.latestLog

      return [
        { label: 'Rejection reason', value: reason },
        { label: 'Specifying', value: text }
      ]
    }
  },
  watch: {
    clickTarget(clickTarget) {
      this.clickTargetHandler(clickTarget)
    },
    visible(visible) {
      if (visible) {
        this.getExchangeRate()
        this.$store.dispatch('actionGetSkuModel', { packageId: this.data.package_id })
      } else {
        if (currInstance === this) {
          currInstance = null
        }
        this.$set(this, 'form', this.$options.data().form)
      }
    }
  },
  beforeDestroy() {
    this.timer && clearTimeout(this.timer)
  },
  methods: {
    mapsFilterFn: maps.filterFn,
    onClose() {
      this.visible = false
      currInstance = null
    },
    async validateForm() {
      const flag = await this.$refs.fixedPriceForm.getData()

      return new Promise((resolve) => this.$refs.form.validate((valid) => resolve(valid && flag)))
    },
    async onConfirm() {
      if (!this.isPendingApproval) {
        return
      }

      if (!(await this.validateForm())) {
        this.$message.error(this.$t('197731'))
        const ele = document.querySelector('.quick-review-ticket-popover .ant-form-item-control.has-error')
        ele &&
          ele.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })
        return
      }

      if (this.isReject) {
        return this.handleReject()
      }

      return this.handleApprove()
    },
    async handleApprove() {
      const fix_price_model = await this.$refs.fixedPriceForm?.getData?.()
      const data = {
        ...this.unitData,
        fix_price_model: {
          ...this.calendarSettingData,
          ...fix_price_model
        }
      }

      await ajax.postBody(
        ADMIN_API.act.ticket_approve_cost_ticket,
        {
          data,
          noDefaultErrorHandler: true
        },
        {
          responseInterceptor: this.responseInterceptor
        }
      )

      this.$emit('refresh')
      this.onClose()
    },
    async handleReject() {
      await ajax.postBody(
        ADMIN_API.act.update_ticket_status,
        {
          data: {
            sku_id: this.basicInfo.sku_id,
            note: JSON.stringify({
              reason_id: this.form.reason[0],
              other_text: this.form.note
            }),
            action: 1
          }
        },
        {
          responseInterceptor: this.responseInterceptor
        }
      )

      this.$emit('refresh')
      this.onClose()
    },
    clickTargetHandler(clickTarget) {
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        if (
          !(
            this.$el?.contains?.(clickTarget) ||
            document.querySelector('.quick-review-ticket-popover')?.contains(clickTarget)
          )
        ) {
          currInstance = null
          this.visible = false
        }
      }, 100)
    },
    async getExchangeRate() {
      const res = await ajax.get(ADMIN_API.act.get_exchange_rate, {
        params: {
          currency_from: this.unitData.merchant_currency,
          currency_to_list: this.unitData.selling_currency
        }
      })
      this.$store.commit('setExchangeRate', res?.[0]?.to_value ?? '')
    },
    async clickTicketItem(quickReview = true) {
      const { ticket_id } = this.data

      if (!isMerchant && quickReview && this.isCostModification) {
        const ticketDetail = await ajax.get(ADMIN_API.act.get_cost_ticket_detail, {
          params: { ticket_id }
        })
        const { fix_price_model, ...unitData } = ticketDetail

        if (fix_price_model) {
          this.activityId = this.data.activity_id

          this.getRejectOptions()
          const basicInfo = await ajax.get(ADMIN_API.act.get_ticket_related_info, {
            params: { ticket_id }
          })
          unitData.merchant_currency = basicInfo.merchant_currency
          this.$set(this, 'unitData', unitData)
          this.$set(this, 'basicInfo', basicInfo)
          this.$set(this, 'calendarSettingData', ticketDetail.fix_price_model || {})

          this.ticketStatus = unitData.ticket_status || ''
          this.isPendingApproval = this.ticketStatus === 'PendingApproval'
          this.isRejected = this.ticketStatus === 'Rejected'

          if (!this.isPendingApproval) {
            const logs =
              (await ajax.get(ADMIN_API.act.get_ticket_log, {
                params: {
                  ticket_id
                }
              })) || []
            this.logs = logs
          }
          if (currInstance) {
            currInstance.visible = false
            currInstance = null
          }
          this.visible = true
          currInstance = this

          return
        }
      }

      const url = getTicketUrlByStatus(this.data)
      window.open('/mspa/experiencesadmincommon' + url, '_blank')
    },
    getPopupContainer() {
      return document.querySelector('.activity-management') || document.body
    },
    getCascaderPopupContainer() {
      return this.$refs.form.$el
    },
    async getRejectOptions() {
      const rejectOptions = await getOptions(ADMIN_API.act.get_price_ticket_reject_reason, {
        loading: true
      })
      this.$set(this, 'rejectOptions', rejectOptions)
    },
    responseInterceptor() {
      return [
        (response) => {
          if (!response.success) {
            const message = _.get(response, 'error.message', '')
            const editProtection = message.match(/activity is edited by.+/gi)

            if (message.includes('err:code:P50501')) {
              const messageRes = message.match(/{{([^}]+)}}/)

              if (messageRes) {
                this.$message.error(messageRes[1]?.trim() || messageRes)
              } else {
                this.$message.error(message || 'Error!')
              }
            } else if (editProtection) {
              this.$message.error(editProtection[0])
            } else {
              this.$warning({
                title: 'Warning',
                content: (response.error.message || 'Warning').replace(/<br \/>/gi, '\n')
              })
            }

            return {
              success: true
            }
          } else {
            this.$message.success(this.$t('global_success'))

            return response
          }
        }
      ]
    }
  }
}
</script>

<style lang="scss">
$approveColor: #08b371;
$rejectColor: #f44622;

.quick-review-ticket-popover {
  .ant-popover-title {
    border-bottom: 1px solid #f0f0f0 !important;
    padding: 5px 16px;

    .title-content {
      font-size: 16px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
  }

  .ant-popover-inner-content {
    height: 408px;
  }

  .ticket-fixed-price-form {
    margin-top: 0;
  }

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .ticket-result-title {
    margin-bottom: 4px;
    font-weight: 500;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.85);
  }

  .ticket-result-title {
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }

  .latest-log {
    display: flex;
    justify-content: space-between;
    gap: 4px;
    margin-top: 8px;

    &-item {
      display: inline-flex;
      flex-direction: column;
      gap: 2px;
      line-height: 22px;

      &-label {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }

  .wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .body {
    flex: 1;
    overflow-y: auto;

    .ant-radio-group {
      .ant-radio-wrapper {
        &:first-child {
          border-color: $approveColor;
          &.card-radio-checked {
            background-color: #e6f7f2;
          }
          .card-radio-label {
            color: $approveColor;
          }
          .ant-radio-checked .ant-radio-inner {
            border-color: $approveColor;
          }
          .ant-radio-inner:after {
            background-color: $approveColor;
          }
        }

        &:last-child {
          border-color: $rejectColor;
          &.card-radio-checked {
            background-color: #faf0f0;
          }
          .card-radio-label {
            color: $rejectColor;
          }
          .ant-radio-checked .ant-radio-inner {
            border-color: $rejectColor;
          }
          .ant-radio-inner:after {
            background-color: $rejectColor;
          }
        }
      }
    }
  }

  .footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 8px;
  }
}
</style>
