<template>
  <div class="approve-detail-container">
    <a-anchor :offsetTop="60">
      <header class="header content-box">
        <div class="__info">
          <OverflowItem
            v-for="item in headerBasicInfo"
            :key="item.field"
            :title="item.title"
            :style="item.style || {}"
          >
            <template #content>
              <span class="__text">{{ item.value || '-' }}</span>
            </template>
          </OverflowItem>
        </div>

        <div class="__actions">
          <TicketAuditOperator
            :ticketStatus="ticketStatus"
            :sku_id="basicInfo.sku_id"
            :unitData="unitData"
            @reject="handleReject"
            @approve="handleApprove"
          />
        </div>
      </header>
    </a-anchor>

    <div class="ticket-source content-box">
      <div class="__template-info">
        <span>{{ templateName }}</span>
      </div>

      <div class="source-box" v-for="item in actAndPkgLink" :key="item.title" :class="item.selfClassName">
        <div class="--title">
          {{ item.title }}
          <span class="ticket-status" :class="item.className">
            {{ item.label }}
          </span>
        </div>
        <router-link class="--link" target="_blank" :to="item.to">
          {{ item.linkText }}
        </router-link>
      </div>
    </div>

    <div class="ticket-body">
      <div class="sku-info-list">
        <OverflowItem v-for="item in skuInfo" :key="item.field" :title="item.title" style="min-width: 150px">
          <template #content>
            {{ item.value || '-' }}
            <QuestionIcon v-if="item.tips" :message="$t('29095')" placement="top" />
          </template>
        </OverflowItem>
      </div>

      <a-spin :spinning="spinning" :delay="300">
        <template v-if="inited">
          <TicketFixedPriceForm
            v-if="isFixedPrice"
            ref="fixedPriceForm"
            :unitData="unitData"
            :ticketStatus="ticketStatus"
            :activityId="basicInfo.activity_id"
            :calendarSettingData="calendarSettingData"
          />

          <div v-else class="body-content">
            <div class="body-box" v-if="autoExtend.visible">
              <div class="__title">{{ $t('48118') }}</div>
              <TicketAutoExtend
                ref="autoExtend"
                v-if="autoExtend.visible"
                :unitData="unitData"
                :approvalStatus="approvalStatus"
                v-bind="autoExtend"
              />
            </div>

            <div class="body-box" v-if="bulkEdit.visible">
              <div class="__title">{{ viewTypeTxt }}</div>

              <TicketOperatorBar
                :viewType.sync="viewType"
                :unitData="basicInfo"
                :showSwitchView="false"
                :filterRawMaterial="filterRawMaterial"
              />

              <div style="margin-top: 24px">
                <TicketBulkEditTable
                  v-if="viewType === 'list' && bulkEdit.visible"
                  ref="bulkEditTable"
                  :list="bulkEdit.list"
                  :basicInfo="basicInfo"
                  :unitData="unitData"
                  :ticketStatus="ticketStatus"
                />

                <TicketCalendar v-if="viewType === 'calendar'" ref="calendar" :unitData="unitData">
                  <template name="priceTips"></template>
                </TicketCalendar>
              </div>
            </div>
          </div>
        </template>
      </a-spin>
    </div>
  </div>
</template>

<script>
import maps from '@activity/utils/maps.js'
import OverflowItem from '../../../package/units/components/OverflowItem'
import QuestionIcon from '../../../package/units/components/QuestionIcon'
import TicketAutoExtend from './autoExtend'
import TicketOperatorBar from './operatorBar'
import TicketBulkEditTable from './bulkEditTable'
import TicketCalendar from './calendar'
import TicketFixedPriceForm from './fixedPriceForm'
import { isMerchant } from '@/env'
import moment from 'moment'
import TicketAuditOperator from './components/auditOperator'
import {
  isSPUType,
  isSPUTypeDefault,
  isHotelType,
  isNotSPUType,
  SHARE_RELATIONSHIPS
} from '@activity/pages/package/package_const.js'
import { bestMatchLang } from '@activity/utils'
import { customColumnFieldList } from './utils'
import { computeTR } from '@activity/utils'
import { mapState } from 'vuex'

export default {
  name: 'ActApproveDetail',
  components: {
    TicketAuditOperator,
    TicketFixedPriceForm,
    TicketCalendar,
    TicketBulkEditTable,
    TicketOperatorBar,
    QuestionIcon,
    OverflowItem,
    TicketAutoExtend
  },
  provide() {
    return {
      calendarSetting: null, // fixedPriceForm component
      packageId: this.basicInfo.package_id,
      skuId: this.unitData.sku_id,
      activityId: this.basicInfo.activity_id,
      refreshTicketDetail: this.initData
    }
  },
  data() {
    return {
      spinning: false,
      inited: false,
      viewType: 'list',

      isFixedPrice: true,
      // fixed price
      calendarSettingData: {},

      autoExtend: {
        visible: false,
        originalData: {}
      },

      // bulk edit table
      bulkEdit: {
        visible: false,
        list: []
      },
      filterRawMaterial: [], // for filter raw material

      activity_id: 41278,
      activity_name: 'activity_name',
      activity_status: 1,
      package_id: 81912,
      package_name: 'package_name',

      basicInfo: {
        sku_id: 0
      },
      unitData: {},
      isMerchant,

      approvalStatus: 0
    }
  },
  computed: {
    ...mapState({
      exchangeRate: (state) => state.exchangeRate
    }),
    viewTypeTxt() {
      return this.$t('48127')
    },
    ticketStatus() {
      return this.unitData.ticket_status || ''
    },
    canSubmitFlag() {
      return Boolean(this.tableState.updated || this.tableState.deleted)
    },
    actAndPkgLink() {
      const {
        activity_id,
        package_id,
        activity_title,
        package_title,
        activity_status,
        submitted_language,
        page_type = []
      } = this.basicInfo

      let aidPath = ''
      if ((page_type || []).includes('SPU')) {
        aidPath = `/aid/tours/basic/${activity_id}?lang=${submitted_language}&package_id=${package_id}&package_type=1${
          isMerchant ? '' : '&page_from=bd_audit'
        }`
      }

      return [
        {
          title: 'Activity title',
          ...this.getStatusInfo(activity_status),
          selfClassName: '__act',
          linkText: `${activity_id} - ${activity_title}`,
          to:
            aidPath ||
            `/act/activity/basic/${activity_id}?lang=${submitted_language}${
              isMerchant ? '' : '&page_from=bd_audit'
            }`
        },
        {
          title: 'Package title',
          ...this.getStatusInfo(this.basicInfo.package_status),
          selfClassName: '__pkg',
          linkText: `${package_id} - ${package_title}`,
          to:
            aidPath ||
            `/act/package/info/${activity_id}?lang=${submitted_language}&package_id=${package_id}&package_type=1${
              isMerchant ? '' : '&page_from=bd_audit'
            }`
        }
      ]
    },
    ticketId() {
      return this.$route.params.ticket_id
    },
    relationshipText() {
      const { share_rel_ship } = this.basicInfo

      const target = SHARE_RELATIONSHIPS.find((item) => item.value === share_rel_ship)
      return target ? target.text : '-'
    },
    unitTypeName() {
      const { unitName, unitType, unit_type, local } = this.basicInfo || {}

      if (!this.isSaved && unitName) {
        return unitName
      }

      const unitTypeList = [...isSPUType, ...isSPUTypeDefault, ...isHotelType, ...isNotSPUType]
      const target = unitTypeList.find((item) => item.value === (unitType == null ? unit_type : unitType))

      if (target) {
        return target.text
      }

      // 已保存的
      return bestMatchLang('unit_name', 'language', local || []) || '-'
    },
    headerBasicInfo() {
      const {
        ticket_id,
        ticket_type,
        submitted_language,
        submit_time_bj,
        merchant_id
        // ticket_note
      } = this.basicInfo

      return [
        { field: 'ticket_id', title: __('48097'), value: ticket_id },
        { field: 'ticket_type', title: __('48098'), value: this.mapsFilterFn('ticket_type', ticket_type) },
        {
          field: 'submit_language',
          title: __('48100'),
          value: lang_conf.getLangObj('B_LANG', 'LANG_TITLE')[submitted_language]
        },
        {
          field: 'submit_time',
          title: __('48102'),
          value: submit_time_bj ? moment(submit_time_bj).format('YYYY-MM-DD hh:mm:ss') : ''
        },
        { field: 'merchant_id', title: __('48103'), value: merchant_id }
        // { field: 'note', title: __('note'), value: ticket_note }
      ]
    },
    relationshipTip() {
      const { share_rel_ship } = this.basicInfo

      const target = SHARE_RELATIONSHIPS.find((item) => item.value === share_rel_ship)

      return target ? target.tip : ''
    },
    skuInfo() {
      const { sku_id, sku_title, sku_status } = this.basicInfo

      return [
        { field: 'unit', title: __('48113'), value: sku_title },
        { field: 'sku_id', title: __('48114'), value: sku_id },
        { field: 'unit_type', title: __('48115'), value: this.unitTypeName },
        { field: 'status', title: __('48116'), value: this.mapsFilterFn('sku_status', sku_status) },
        {
          field: 'Relationship:',
          title: __('48117'),
          value: this.relationshipText,
          tips: this.relationshipTip
        }
      ]
    },
    templateName() {
      const { sub_category_id_name, leaf_category_id_name } = this.basicInfo

      return `${sub_category_id_name} - ${leaf_category_id_name}`
    },
    responseInterceptor() {
      return [
        (response) => {
          if (!response.success) {
            const message = _.get(response, 'error.message', '')
            const editProtection = message.match(/activity is edited by.+/gi)

            if (message.includes('err:code:P50501')) {
              const messageRes = message.match(/{{([^}]+)}}/)

              if (messageRes) {
                this.$message.error(messageRes[1]?.trim() || messageRes)
              } else {
                this.$message.error(message || 'Error!')
              }
            } else if (editProtection) {
              this.$message.error(editProtection[0])
            } else {
              this.$warning({
                title: 'Warning',
                content: (response.error.message || 'Warning').replace(/<br \/>/gi, '\n')
              })
            }

            return {
              success: true
            }
          } else {
            this.$message.success(this.$t('global_success'))
            this.initData()

            return response
          }
        }
      ]
    }
  },
  mounted() {
    this.initData()
    window.addEventListener('beforeunload', this.onBeforeunload)
  },
  beforeDestroy() {
    window.removeEventListener('beforeunload', this.onBeforeunload)
  },
  methods: {
    mapsFilterFn: maps.filterFn,
    async onBeforeunload(e) {
      const { auto_extend_model, calendar, fix_price_model } = await this.getPostData()

      if (
        !_.isEqual(fix_price_model, this.cacheInitData.fix_price_model) ||
        !_.isEqual(calendar, this.cacheInitData.calendar) ||
        !_.isEqual(auto_extend_model, this.cacheInitData.auto_extend_model)
      ) {
        e.preventDefault()

        e.returnValue = '数据还未保存，确定退出吗？'
      } else {
        window.removeEventListener('beforeunload', this.onBeforeunload)
        delete e['returnValue']
      }
    },
    async getPostData() {
      let data
      if (this.isFixedPrice) {
        const fix_price_model = await this.$refs.fixedPriceForm?.getData?.()

        data = {
          fix_price_model:
            this.cacheInitData?.fix_price_model && fix_price_model
              ? {
                  ...this.calendarSettingData,
                  ...fix_price_model
                }
              : null
        }
      } else {
        const [auto_extend_model, calendar] = await Promise.all([
          this.$refs?.autoExtend?.getData(),
          this.$refs?.bulkEditTable?.getData()
        ])

        data = {
          auto_extend_model: this.cacheInitData?.auto_extend_model
            ? {
                ...this.cacheInitData.auto_extend_model,
                weekly: auto_extend_model?.weekly || []
              }
            : null,
          calendar: this.cacheInitData?.calendar ? calendar : null
        }
      }

      return {
        ...this.unitData,
        ...data
      }
    },
    async handleApprove() {
      if (!this.inited) {
        return
      }

      await ajax.postBody(
        ADMIN_API.act.ticket_approve_cost_ticket,
        {
          data: await this.getPostData(),
          noDefaultErrorHandler: true
        },
        {
          responseInterceptor: this.responseInterceptor
        }
      )
    },
    async handleReject({ note }) {
      if (!this.inited) {
        return
      }

      await ajax.postBody(
        ADMIN_API.act.update_ticket_status,
        {
          data: {
            sku_id: this.basicInfo.sku_id,
            note,
            action: 1
          }
        },
        {
          responseInterceptor: this.responseInterceptor
        }
      )
    },
    async initData() {
      this.spinning = true
      this.inited = false

      const [ticketDetail, basicInfo] = await Promise.all([
        await ajax.get(ADMIN_API.act.get_cost_ticket_detail, {
          params: {
            ticket_id: this.ticketId
          }
        }),
        await ajax.get(ADMIN_API.act.get_ticket_related_info, {
          params: {
            ticket_id: this.ticketId
          }
        })
      ])

      const { auto_extend_model, calendar, fix_price_model, ...unitData } = ticketDetail
      unitData.merchant_currency = basicInfo.merchant_currency
      this.$set(this, 'unitData', unitData)
      this.approvalStatus = basicInfo.approve_status
      this.$set(this, 'basicInfo', basicInfo)

      await this.getExchangeRate()

      if (calendar || auto_extend_model) {
        this.$set(this, 'autoExtend', {
          visible: !!auto_extend_model,
          originalData: {
            ...unitData,
            ...Object.entries(auto_extend_model || {}).reduce((acc, curr) => {
              let [key, value] = curr
              if (key === 'weekly' && Array.isArray(value)) {
                value = value.map((item) => {
                  return {
                    ...item,
                    // 下面的逻辑主要是生成 current_take_rate 数据，待加到表格里自动生成（像 take_rate 生成那般）
                    schedules: (item.schedules || []).map((schedule) => {
                      return this.computedTakeRate(schedule)
                    })
                  }
                })
              }

              return {
                ...acc,
                [key]: value
              }
            }, {})
          }
        })
        this.$set(this, 'bulkEdit', {
          visible: !!calendar,
          list: [
            {
              ...unitData,
              calendar: (calendar || []).map((item) => {
                return this.computedTakeRate(item)
              })
            }
          ]
        })
        this.$set(this, 'filterRawMaterial', calendar)
        // return  computeTR(current_cost_price, current_selling_price, this.exchangeRate)

        this.isFixedPrice = false
      } else if (fix_price_model) {
        this.$set(this, 'calendarSettingData', fix_price_model)

        this.isFixedPrice = true
      }

      this.cacheInitData = _.cloneDeep(ticketDetail)

      this.$store.commit(
        'setTicketViewFieldList',
        customColumnFieldList.map((item) => item.key)
      )

      this.inited = calendar || auto_extend_model || fix_price_model
      this.spinning = false

      this.$store.dispatch('actionGetSkuModel', { packageId: this.basicInfo.package_id })
    },
    computedTakeRate(data) {
      let { price, ...other } = data

      if (price && price.current_cost_price !== undefined && price.current_selling_price !== undefined) {
        price.current_take_rate = price.default_take_rate
          ? price.default_take_rate
          : computeTR(price.current_cost_price, price.current_selling_price, this.exchangeRate)

        // 工单页面下，表格的 draft take rate 需要展示成 current_take_rate
        if (price.cost_price) {
          price.selling_price = undefined
          price.selling_currency = price.selling_currency || this.unitData.selling_currency
          price.take_rate = price.current_take_rate
        }
      }

      return {
        ...other,
        price
      }
    },
    async getExchangeRate() {
      const res = await ajax.get(ADMIN_API.act.get_exchange_rate, {
        params: {
          currency_from: this.unitData.merchant_currency,
          currency_to_list: this.unitData.selling_currency
        }
      })
      this.$store.commit('setExchangeRate', res?.[0]?.to_value ?? '')
    },
    getStatusInfo(status) {
      switch (status) {
        case 0: {
          return { label: __('global_unpublished'), className: 'unpublished' }
        }
        case 1: {
          return { label: __('global_published'), className: 'published' }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../package/units/unit-list/calendar-setting/mixins';

@mixin content-box {
  padding: 12px 20px;
  margin-top: 20px;
  background-color: #fff;
}

.approve-detail-container {
  min-height: calc(100vh - 64px);
  margin-top: -30px;
  background-color: #fafafa;

  .__text {
    display: inline-block;
    height: 20px;
    line-height: 20px;
  }

  ::v-deep .overflow-item {
    padding: 0 12px;
  }

  ::v-deep .ant-anchor-ink {
    display: none;
  }

  .content-box {
    @include content-box;
  }

  .header {
    display: flex;
    justify-content: space-between;
    margin-top: 0;
    border-bottom: 1px solid #fafafa;

    .__info {
      display: inline-flex;
      flex-wrap: wrap;
    }

    .__actions {
      display: inline-flex;
      width: fit-content;
      margin-left: 12px;
      ::v-deep .ant-btn {
        margin-left: 12px;
      }
    }
  }

  .ticket-source {
    margin-top: 20px;

    .__template-info span {
      padding: 4px;
      background: rgba(0, 0, 0, 0.04);
      border-radius: 2px;
    }

    .source-box {
      margin-top: 16px;

      .--title span {
        @include mixin-status-text();
      }

      .--link {
        display: block;
        margin-top: 12px;
        color: #0091ff;
        cursor: pointer;
        width: max-content;
        &:hover {
          opacity: 0.85;
        }
      }

      &.__act .--link {
        font-size: 16px;
        font-weight: 800;
      }

      &.__pkg .--link {
        font-size: 14px;
      }
    }
  }

  .ticket-body {
    margin: 20px 22px;

    .sku-info-list {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      padding: 12px 36px;
      background-color: #f5f5f5;
      border-radius: 6px;
      border: #e8e8e8 1px solid;
    }

    .body-content {
      padding: 16px 24px;
      background-color: #fff;

      .body-box {
        margin-top: 24px;
        &:first-child {
          margin-top: 0;
        }
      }

      .__title {
        font-size: 18px;
        font-weight: 500;
        color: #000;
      }
    }
  }
}
</style>
