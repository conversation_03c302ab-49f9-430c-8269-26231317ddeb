<template>
  <div class="ticket-fixed-price-form">
    <a-spin :spinning="spinning" :delay="300">
      <fixed-price-form
        class="body-box"
        ref="form"
        :unit-data="unitData"
        :calendar-setting-data="calendarSettingData"
        :disabled="disabled"
        :showRetailPrice="false"
        :showInventory="false"
        :needHiddenField="needHiddenField"
        :showCostInputSuffixData="false"
        :showBtnSsp="!disabled"
        v-bind="$attrs"
      >
        <template #costPriceLabel>
          {{ $t('48144') }}
        </template>
        <template #sellingPriceLabel>
          {{ $t('48147') }}
        </template>
        <template #takeRateLabel>
          {{ $t('48148') }}
        </template>
        <template #extra="scope">
          <div class="fixed-price-form-extra">
            <a-form-model-item v-for="item in currentData" :key="item.label" :label="item.label">
              <a-input
                v-model="calendarSettingData.fix_price[item.field]"
                disabled
                style="width: 200px"
                :suffix="item.suffixText || scope[item.suffixField]"
              />
            </a-form-model-item>
          </div>
        </template>
      </fixed-price-form>
    </a-spin>
  </div>
</template>

<script>
import { isMerchant } from '@/env'
import { mapState } from 'vuex'
import FixedPriceForm from '../../../package/units/unit-list/calendar-setting/components/FixedPriceForm'
import { computeTR } from '@activity/utils'

export default {
  name: 'ticketFixedPriceForm',
  components: {
    FixedPriceForm
  },
  props: {
    unitData: {
      type: Object,
      required: true
    },
    calendarSettingData: {
      type: Object,
      default: () => ({})
    },
    ticketStatus: {
      type: String,
      default: ''
    },
    customHiddenField: {
      type: Array,
      default: () => []
    }
  },
  provide() {
    return {
      unitBar: {},
      skuId: this.unitData.sku_id,
      activityId: this.$attrs.activityId,
      getEditLockValueByPath: null
    }
  },
  data() {
    return {
      isMerchant,
      spinning: false
    }
  },
  computed: {
    ...mapState({
      exchangeRate: (state) => state.exchangeRate
    }),
    needHiddenField() {
      if (this.isMerchant) {
        return ['inventory', 'retailPrice', 'takeRate', 'ssp', 'sellingPrice', ...this.customHiddenField]
      }

      return ['inventory', 'retailPrice', ...this.customHiddenField]
    },
    currentData() {
      if (this.isMerchant) {
        return [{ label: __('48145'), field: 'current_cost_price', suffixField: 'costCurrency' }]
      }

      return [
        { label: __('48145'), field: 'current_cost_price', suffixField: 'costCurrency' },
        { label: __('48150'), field: 'current_selling_price', suffixField: 'sellingCurrency' },
        { label: __('48151'), field: 'current_take_rate', suffixText: '%' }
      ]
    },
    currentTakeRate() {
      const { current_cost_price, current_selling_price } = this.calendarSettingData.fix_price

      return computeTR(current_cost_price, current_selling_price, this.exchangeRate)
    },
    disabled() {
      return this.isMerchant || this.ticketStatus !== 'PendingApproval'
    }
  },
  watch: {
    currentTakeRate: {
      immediate: true,
      deep: true,
      handler() {
        if (!isNaN(this.currentTakeRate)) {
          this.$set(this.calendarSettingData.fix_price, 'current_take_rate', this.currentTakeRate)
          this.$set(this.calendarSettingData.fix_price, 'take_rate', this.currentTakeRate)
          this.calendarSettingData.fix_price.selling_price = ''
        }

        // 若存在商户入驻 draft_take_rate 则用这个利率做售价反算
        const { draft_take_rate } = this.calendarSettingData.fix_price
        if (draft_take_rate !== '') {
          this.$set(this.calendarSettingData.fix_price, 'take_rate', draft_take_rate)
        }
      }
    }
  },
  methods: {
    async getData() {
      const valid = await this.$refs.form.validateForm()
      if (!valid) {
        return
      }

      const formData = this.$refs.form.getData()
      const params = _.cloneDeep(this.calendarSettingData)

      if (formData.inventory) {
        params.global_inv_quantity = formData.inventory
      }

      params.fix_price = {
        ...params.fix_price,
        cost_price: Number(formData.costPrice),
        retail_price: Number(formData.retailPrice),
        selling_price: Number(formData.sellingPrice),
        special_selling_price: formData.specialSellingPriceList,
        draft_take_rate: formData.draft_take_rate
      }

      return params
    }
  }
}
</script>

<style lang="scss" scoped>
.ticket-fixed-price-form {
  margin-top: 20px;

  .fixed-price-form-extra {
    display: flex;
    width: 100%;

    ::v-deep .ant-form-item {
      margin-right: 24px;
    }
  }
}
</style>
