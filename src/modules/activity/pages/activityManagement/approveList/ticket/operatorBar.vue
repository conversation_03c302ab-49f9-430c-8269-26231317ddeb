<template>
  <div class="operator-bar">
    <div class="filter-explain-list">
      <div class="__item" v-for="item of explainList" :key="item.icon">
        <label v-if="item.customIconDOM" class="__icon" v-html="item.customIconDOM"></label>
        <svg-icon v-else :icon-name="item.icon" class="__icon"></svg-icon>
        <label v-html="item.label"></label>
        <QuestionIcon v-if="item.message" :message="item.message" placement="top" />
      </div>
    </div>

    <div class="filter-content">
      <div class="__item">
        <a-popover
          title="Filter by Schedule"
          trigger="hover"
          placement="topRight"
          :visible="popoverFilterVisible"
          @visibleChange="changePopoverFilterVisible"
        >
          <template slot="content">
            <schema-form
              ref="schemaForm"
              style="width: 560px; padding: 0;"
              :shouldEmitInitData="true"
              :schema-config="schemaConfig"
            >
              <template #actions="scoped">
                <div style="display: flex; justify-content: flex-end;">
                  <a-button type="link" @click="handleReset">{{ $t('48140') }}</a-button>
                  <a-button style="margin-right: 12px;" @click="popoverFilterVisible = false">{{
                    $t('44916')
                  }}</a-button>
                  <a-button type="primary" @click="() => handleSubmitFilter(scoped.realFormData)">{{
                    $t('48139')
                  }}</a-button>
                </div>
              </template>
            </schema-form>
          </template>
          <div class="__action" :style="{ color: popoverFilterVisible ? '#0091FF' : '#515a6e' }">
            <a-icon type="filter" class="__icon" /> {{ $t('48143') }}
          </div>
        </a-popover>
      </div>

      <div class="__item" v-if="isAdmin">
        <a-popover
          title=""
          trigger="hover"
          overlay-class-name="view-field-popover"
          v-model="popoverViewFieldVisible"
          @visibleChange="changePopoverViewFieldVisible"
        >
          <template slot="content">
            <div class="view-field-filter-list">
              <div class="__item" v-for="(item, index) in viewFieldFilterForm" :key="index">
                <div class="__title">{{ item.chunkTitle || '' }}</div>
                <div class="checkbox-group" v-for="field in item.children" :key="field.key">
                  <a-checkbox
                    v-model="field.value"
                    :disabled="field.value && ticketViewFieldList.length === 1"
                    @change="changeViewField"
                  >
                    {{ field.title }}
                  </a-checkbox>
                </div>
              </div>
            </div>
          </template>
          <div class="__action" :style="{ color: popoverViewFieldVisible ? '#0091FF' : '#515a6e' }">
            <a-icon type="table" class="__icon" /> View Field
          </div>
        </a-popover>
      </div>

      <div class="__item" v-if="showSwitchView">
        <slot name="switchView">
          <a-radio-group v-model="_viewType" @change="changeViewType">
            <a-radio-button value="calendar">{{ $t('48141') }}</a-radio-button>
            <a-radio-button value="list">{{ $t('48142') }}</a-radio-button>
          </a-radio-group>
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
import { isAdmin } from '@/env'
import { PRICE_MODELS } from '@activity/pages/package/package_const.js'
import QuestionIcon from '../../../package/units/components/QuestionIcon'
import { weeklyOpts } from '@activity/pages/package/units/bulkEdit/utils'
import SchemaForm from '../components/schemaForm'
import { mapState } from 'vuex'
import moment from 'moment'

export default {
  name: 'ticketOperatorBar',
  inject: ['activityId'],
  components: {
    SchemaForm,
    QuestionIcon
  },
  props: {
    viewType: {
      type: String,
      default: 'calendar'
    },
    unitData: {
      type: Object,
      required: true
    },
    showSwitchView: {
      type: Boolean,
      default: true
    },
    filterRawMaterial: {
      type: Array,
      default: () => []
    }
  },
  data() {
    const that = this
    this.originalFilterData = []

    return {
      isAdmin,

      popoverFilterVisible: false,
      popoverViewFieldVisible: false,

      schemaConfig: {
        actionsStyle: {
          margin: '0'
        },
        attrs: {
          layout: 'horizontal',
          labelCol: { span: 4 },
          wrapperCol: { span: 19, offset: 0 }
        },
        formItems: [
          {
            label: __('48128'),
            type: 'range',
            value: [],
            field: 'date',
            inputConfig: {
              render: (h, ctx) => {
                return (
                  <a-range-picker
                    value={ctx.props.proxyFormData.date}
                    ref="picker"
                    dropdownClassName="bulk-edit-search-form-range"
                    style="width: 100%;"
                    {...{
                      on: {
                        change: (date) => {
                          ctx.props.proxyFormData.date = date
                        }
                      },
                      scopedSlots: {
                        renderExtraFooter: () => {
                          return (
                            <div class="range-picker-footer">
                              <span class="has-filled-dot"></span>
                              {that.$t('30286')}
                            </div>
                          )
                        },
                        dateRender: (current) => {
                          return <div class={that.hasInvStyle(current)}>{current.date()}</div>
                        }
                      }
                    }}
                  />
                )
              }
            }
          },
          {
            label: __('48129'),
            type: 'allSelect',
            value: ['selectAll'],
            field: 'timeslot',
            inputConfig: {
              async getOptions() {
                return that.time_points
              },
              props: {
                maxTagCount: 1
              }
            }
          },
          {
            label: __('48130'),
            type: 'allCheckbox',
            inputConfig: {
              options: weeklyOpts,
              attrs: {
                checkAllStyle: {
                  marginLeft: '8px'
                }
              }
            },
            field: 'weekly',
            value: weeklyOpts.map((item) => item.value)
          }
        ]
      },

      time_points: [],
      has_inv_days: [],
      viewFieldFilterForm: [
        {
          chunkTitle: __('package_price_cost'),
          children: [
            { title: __('48120'), value: true, key: 'cost_price' },
            { title: __('48121'), value: true, key: 'current_cost_price' }
          ]
        },
        {
          chunkTitle: 'Selling Price',
          children: [
            { title: __('48122'), value: true, key: 'selling_price' },
            { title: __('48125'), value: true, key: 'current_selling_price' }
          ]
        },
        {
          chunkTitle: __('act_list_take_rate'),
          children: [
            { title: __('48123'), value: true, key: 'take_rate' },
            { title: __('48126'), value: true, key: 'current_take_rate' }
          ]
        },
        {
          chunkTitle: '',
          children: [{ title: __('48124'), value: true, key: 'special_selling_price' }]
        }
      ]
    }
  },
  computed: {
    ...mapState(['ticketViewFieldList']),
    _viewType: {
      get() {
        return this.viewType
      },
      set(v) {
        this.$emit('update:viewType', v)
      }
    },
    explainList() {
      return [
        {
          customIconDOM: '<label class="square-yellow"></label>',
          label: this.priceText,
          message: __('27739')
        },
        {
          customIconDOM: '<span class="hh-mm">hh:mm</span>',
          label: __('27799'),
          message: __('29095')
        },
        {
          icon: 'finance',
          label: __('package_price_cost')
        },
        {
          icon: 'Icon_common_currency_yuan',
          label: __('package_price_selling')
        }
      ]
    },
    priceText() {
      const { price_model } = this.unitData

      if (price_model !== 0) {
        return ''
      }

      const target = PRICE_MODELS.find((item) => item.value === price_model)

      return target ? target.text : '-'
    }
  },
  watch: {
    filterRawMaterial: {
      immediate: true,
      handler() {
        this.getFilterDataByCalendar()
      }
    }
  },
  methods: {
    hasInvStyle(current) {
      if (this.has_inv_days.includes(+moment(current).startOf('date'))) {
        return 'search-form-picker-cell has-inv'
      }

      return 'search-form-picker-cell'
    },
    getFilterDataByCalendar() {
      const { time_points, has_inv_days, calendar } = (this.filterRawMaterial || []).reduce(
        (acc, curr, index) => {
          const [start_date, start_time] = curr.start_time.split(' ')

          const has_inv_day = `${start_date} 00:00:00`
          acc.has_inv_days.push(has_inv_day)
          acc.time_points.push(start_time)

          acc.calendar.push({
            uuid: '_' + index,
            start_date,
            start_time,
            has_inv_day,
            day: moment(start_date).day(),
            timestamp: +new Date(curr.start_time)
          })

          return acc
        },
        {
          has_inv_days: [],
          calendar: [],
          time_points: []
        }
      )

      this.$emit('getOptions', {
        time_points,
        has_inv_days
      })

      this.$set(
        this,
        'time_points',
        _.uniq(time_points).map((item) => ({ label: item, value: item }))
      )
      this.$set(
        this,
        'has_inv_days',
        _.uniq(has_inv_days).map((item) => ({ label: item, value: item }))
      )
      this.originalFilterData = calendar
      this.$store.commit(
        'setDisplayTableIndexByFilter',
        calendar.map((item) => item.uuid)
      )
    },
    changePopoverFilterVisible() {
      this.popoverFilterVisible = true
    },
    changePopoverViewFieldVisible() {
      this.popoverFilterVisible = false
    },
    changeViewType(v) {
      this.$emit('changeViewType', v)
    },
    handleReset() {
      this.$refs.schemaForm.handleReset()
    },
    handleSubmitFilter(v) {
      this.popoverFilterVisible = false
      console.log('#handleSubmitFilter# ', v)
      const { date, timeslot, weekly } = v

      let start, end
      if (date.length) {
        start = +new Date(moment(date[0]).format('YYYY-MM-DD 00:00:00'))
        end = +new Date(moment(date[1]).format('YYYY-MM-DD 23:59:59'))
      }

      const searchRes = this.originalFilterData.reduce((acc, curr) => {
        let inTheTimeRange = true
        if (start && end && (curr.timestamp < start || curr.timestamp > end)) {
          inTheTimeRange = false
        }

        if (timeslot.includes(curr.start_time) && weekly.includes(curr.day) && inTheTimeRange) {
          return [...acc, curr.uuid]
        }

        return acc
      }, [])

      console.log('#searchRes', searchRes)
      this.$store.commit('setDisplayTableIndexByFilter', searchRes)
    },
    changeViewField() {
      this.$store.commit(
        'setTicketViewFieldList',
        this.viewFieldFilterForm.reduce(
          (acc, curr) => [
            ...acc,
            ...curr.children.reduce(
              (accField, currField) => (currField.value ? [...accField, currField.key] : accField),
              []
            )
          ],
          []
        )
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.operator-bar {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-top: 24px;

  .filter-explain-list,
  .filter-content {
    display: inline-flex;
    align-items: center;
  }

  .filter-explain-list {
    flex: 1;
    flex-wrap: wrap;
  }

  .__item {
    margin-right: 24px;
    cursor: pointer;

    &:last-child {
      margin-right: 0;
    }

    .__action:hover {
      color: #0091ff !important;
    }
  }

  .__icon {
    margin-right: 4px;
  }

  ::v-deep {
    .square-yellow {
      display: inline-block;
      width: 32px;
      height: 16px;
      background: rgba(255, 171, 0, 0.1);
      border-radius: 2px;
    }

    .hh-mm {
      text-decoration: line-through;
    }
  }
}

.view-field-popover {
  .view-field-filter-list {
    display: flex;
    justify-content: flex-end;
    flex-direction: column;
    margin-bottom: 12px;

    .__title {
      margin: 6px 0;
      font-size: 14px;
      font-weight: 500;
    }

    .checkbox-group {
      padding: 6px;
      border-radius: 4px;
      background-color: #fafafa;
    }
  }
}
</style>

<style lang="scss">
%dot {
  width: 4px;
  height: 4px;
  display: inline-flex;
  border-radius: 50%;
  background-color: #36b37e;
}

.is-expires {
  color: rgba(0, 0, 0, 0.25);
}

.has-filled:after {
  @extend %dot;
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
}

.has-filled-dot {
  @extend %dot;
  margin-right: 6px;
}

.search-form-picker-cell {
  position: relative;
  z-index: 1;
  cursor: pointer;
  &.has-inv:after {
    content: ' ';
    position: absolute;
    top: 100%;
    left: 50%;
    display: inline-block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: #36b37e;
    transform: translateX(-50%);
    z-index: 999;
  }
}
</style>
