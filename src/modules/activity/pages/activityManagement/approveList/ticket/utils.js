import { isMerchant } from '@/env'

export const customColumnFieldList = isMerchant
  ? [
      {
        key: 'cost_price',
        title: __('48120'),
        disabledFn: () => true,
        style: {
          minWidth: '220px'
        },
        width: 'fit-content'
      },
      {
        key: 'current_cost_price',
        title: __('48121'),
        disabledFn: () => true,
        style: {
          minWidth: '220px'
        },
        width: 'fit-content'
      }
    ]
  : [
      {
        key: 'cost_price',
        title: __('48144'),
        disabledFn: () => true,
        style: {
          minWidth: '220px'
        }
      },
      {
        key: 'selling_price',
        title: __('48147'),
        disabledFn: ({ ticketStatus }) => {
          return ticketStatus !== 'PendingApproval'
        },
        style: {
          minWidth: '220px'
        }
      },
      {
        key: 'take_rate',
        title: __('48148'),
        disabledFn: ({ ticketStatus }) => {
          return ticketStatus !== 'PendingApproval'
        },
        style: {
          minWidth: '220px'
        }
      },
      {
        style: {
          minWidth: '220px'
        },
        key: 'special_selling_price',
        title: __('28682'),
        hidden: ['merchant'],
        disabledFn({ pricing_model, role, ticketStatus }) {
          return (
            pricing_model !== 1 ||
            role === 'merchant' || // 1: 日历价格
            ticketStatus !== 'PendingApproval'
          )
        }
      },
      {
        key: 'current_cost_price',
        title: __('48145'),
        disabledFn: () => true,
        style: {
          minWidth: '220px'
        }
      },
      {
        key: 'current_selling_price',
        title: __('48150'),
        disabledFn: () => true,
        style: {
          minWidth: '220px'
        }
      },
      {
        key: 'current_take_rate',
        title: __('48151'),
        disabledFn: () => true
      }
    ]
