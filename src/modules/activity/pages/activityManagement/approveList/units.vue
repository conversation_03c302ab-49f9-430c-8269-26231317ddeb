<template>
  <a-drawer
    title="Unit(s)"
    placement="right"
    :visible="visible"
    v-if="visible"
    :width="860"
    :body-style="{ padding: '50px 0px' }"
    class="units-approve-list-container"
    @close="handleClose"
  >
    <p class="package-title">
      {{ headerTitle }}
    </p>

    <a-table
      style="width: 100%; padding: 10px 20px"
      :scroll="{
        x: true
      }"
      :bordered="true"
      :pagination="false"
      :columns="columns"
      :loading="loading"
      :data-source="tableData"
    />

    <logs-table class="change-log-modal" v-model="logData.visible" :logData="logData" />

    <SubmitApproveDrawer
      v-bind="{ ...$attrs, ...submit }"
      :onlyThisUnit="true"
      :visible.sync="submit.visible"
      @submitCompleted="submitCompleted"
    />
  </a-drawer>
</template>

<script>
import maps from '@activity/utils/maps.js'
import LogsTable from '../logsTable'
import approveListComponent from '@activity/pages/activityManagement/approveList/index.vue'
import {
  isSPUType,
  isSPUTypeDefault,
  isHotelType,
  isNotSPUType,
  SHARE_RELATIONSHIPS
} from '@activity/pages/package/package_const.js'
import SubmitApproveDrawer from '../../package/units/unit-list/calendar-setting/components/submitApproveDrawer'

export default {
  name: 'UnitsApprovalList',
  components: { SubmitApproveDrawer, LogsTable },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      submit: {
        visible: false,
        sku_id: undefined,
        activityId: undefined,
        packageId: undefined
      },

      tableData: [],
      loading: false,
      columns: [
        {
          title: __('48222'),
          dataIndex: 'unit_id',
          customRender: (text, record) => {
            const { package_archived, sku_id, local, local_unit_name } = record
            const { activity_id, language, package_id } = this.data
            const unit_name = this.getMultiLangData(local, { valField: 'unit_name', defaultVal: '' })

            const unitTitle = (
              <span>
                {sku_id} - {local_unit_name || unit_name}
              </span>
            )

            if (package_archived) {
              return unitTitle
            }

            const package_type = global_env.isMerchant ? '' : '&page_from=bd_audit'
            let path = ''
            if (this.data.had_been_inherit_spu) {
              path = `/aid/tours/inventory-schedule/${activity_id}?ref=&package_id=${package_id}&lang=${language}&package_type=${package_type}`
            } else {
              if (global_env.isMerchant) {
                path = `/act/package/unit/${activity_id}?lang=${language}&package_id=${package_id}&package_type=${package_type}&sku_id=${sku_id}`
              } else {
                path = `/act/package/schedule/${activity_id}?lang=${language}&package_id=${package_id}&package_type=${package_type}`
              }
            }

            return (
              <router-link
                to={{
                  path: path
                }}
                target="_blank"
              >
                {unitTitle}
              </router-link>
            )
          }
        },
        {
          title: __('48068'),
          dataIndex: 'cost_ticket_status',
          customRender: (text, record) => {
            const { ticket_status } = record

            if (!ticket_status || ticket_status === 3) {
              return <span>{ticket_status || '-'}</span>
            }

            const statusText = this.mapsFilterFn('ticket_status', ticket_status)
            const statusClass = [
              'common-status-style',
              this.mapsFilterFn('ticket_status', ticket_status, 'class') || ''
            ]

            return (
              <a-tooltip overlayClassName="common-tooltip-style" title={record.note || ''}>
                <span class={statusClass.join(' ')}>
                  {statusText || '-'}
                  <a-icon
                    style={{
                      marginLeft: '6px',
                      cursor: 'pointer'
                    }}
                    type="clock-circle"
                    onClick={() => this.handleTicketStatusChangeLog(record)}
                  />
                </span>
              </a-tooltip>
            )
          }
        },
        {
          title: __('48223'),
          dataIndex: 'status',
          customRender: (text, record) => {
            let { published } = record
            published = Number(published)
            const statusText = this.mapsFilterFn('activity_status', published)
            const statusClass = [
              'common-status-style',
              this.mapsFilterFn('activity_status', published, 'class')
            ]

            return <span class={statusClass.join(' ')}>{statusText}</span>
          }
        },
        {
          title: __('20943'),
          dataIndex: 'unit_type',
          customRender: (text, record) => {
            const { unit_type } = record
            const unitTypeList = [...isSPUType, ...isSPUTypeDefault, ...isHotelType, ...isNotSPUType]
            const target = unitTypeList.find((item) => item.value === unit_type)

            if (target) {
              return target.text
            }

            return <span>{record.unit_type}</span>
          }
        },
        {
          title: __('27753'),
          dataIndex: 'shared_relationship',
          customRender: (text, record) => {
            const { share_rel_ship } = record
            const target = SHARE_RELATIONSHIPS.find((item) => item.value === share_rel_ship)

            return <span>{target ? target.text : '-'}</span>
          }
        },
        {
          title: __('package_price_cost'),
          dataIndex: 'cost',
          customRender: (text, record) => {
            if (record.is_fixed) {
              return (
                <span>
                  {record.cost_currency} {record.cost_price}
                </span>
              )
            }

            return <span>{this.$t('48225')}</span>
          }
        },
        {
          title: __('package_price_retail'),
          dataIndex: 'retail',
          customRender: (text, record) => {
            if (record.is_fixed) {
              return (
                <span>
                  {record.retail_currency} {record.retail_price}
                </span>
              )
            }

            return <span>{this.$t('48225')}</span>
          }
        },
        {
          title: __('global_operation'),
          dataIndex: 'operation',
          customRender: (text, record) => {
            const { ticket_status } = record

            return (
              <label>
                {['ToBeSubmitted', 'Rejected'].includes(ticket_status) ? (
                  <a-button type="link" onClick={() => this.handleSubmit(record)}>
                    {this.$t('48081')}
                  </a-button>
                ) : (
                  ''
                )}
                {['PendingApproval'].includes(ticket_status) ? (
                  <a-button type="link" onClick={() => this.handleWithdraw(record)}>
                    {this.$t('48078')}
                  </a-button>
                ) : (
                  ''
                )}
                <a-button type="link" onClick={() => this.handleGoToApprovalList(record)}>
                  {this.$t('48224')}
                </a-button>
                {['ToBeSubmitted', 'Rejected', 'PendingApproval'].includes(ticket_status) ? (
                  <a-button type="link" onClick={() => this.handleGoToApprovalDetail(record)}>
                    {this.$t('48076')}
                  </a-button>
                ) : (
                  ''
                )}
              </label>
            )
          }
        }
      ],

      logData: {
        visible: false,
        loading: false,
        logTable: {
          dataSource: []
        }
      }
    }
  },
  watch: {
    $route: {
      deep: true,
      immediate: true,
      async handler() {
        await this.getTableData()
      }
    },
    visible: {
      immediate: true,
      async handler(v) {
        if (v) {
          await this.getTableData()
        }
      }
    }
  },
  computed: {
    headerTitle() {
      const { package_id = '', package_title = '' } = this.data

      return `${package_id} - ${package_title}`
    },
    currentViewLanguage() {
      return lang_conf.getLangObj('F_LANG', 'B_LANG')[KLK_LANG]
    }
  },
  methods: {
    mapsFilterFn: maps.filterFn,
    handleTicketStatusChangeLog: approveListComponent?.methods?.handleTicketStatusChangeLog,
    submitCompleted() {
      this.getTableData()
      this.$emit('refresh')
    },
    getMultiLangData(data, { langField = 'language', valField = 'value', defaultVal = '' } = {}) {
      let current = _.find(data, {
        [langField]: this.currentViewLanguage
      })

      return (current && current[valField]) || defaultVal
    },
    handleSubmit(row) {
      const { activity_id, package_id } = this.data
      this.submit.visible = true
      this.submit.sku_id = row.sku_id

      this.$set(this, 'submit', {
        visible: true,
        sku_id: row.sku_id,
        activityId: activity_id,
        packageId: package_id
      })
    },
    async handleWithdraw(row) {
      await ajax.postBody(ADMIN_API.act.update_ticket_status, {
        data: {
          sku_id: row.sku_id,
          note: '',
          action: 3
        }
      })

      this.submitCompleted()
    },
    handleGoToApprovalList(row) {
      this.$router.replace({
        name: 'activityManagement',
        query: {
          package_id: this.data.package_id,
          activity_id: this.data.activity_id,
          sku_id: row.sku_id
        }
      })
    },
    handleGoToApprovalDetail(row) {
      const { ticket_id } = row

      this.$router.push({
        name: 'ticketDetail',
        params: {
          ticket_id: ticket_id
        }
      })
    },
    async getTableData() {
      this.loading = true

      const { units = [] } = await ajax.get(ADMIN_API.act.query_package_sku_list, {
        params: {
          package_id: this.data.package_id,
          language: 'en_US'
        }
      })

      this.tableData = units
      this.loading = false
    },
    handleClose() {
      this.$emit('refresh')
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../package/units/unit-list/calendar-setting/mixins';

.package-title {
  padding: 20px;
  background-color: #fafafa;
}

.common-status-style {
  @include mixin-status-text();
}
</style>
