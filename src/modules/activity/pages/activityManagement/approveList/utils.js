/*1: 'Cost Modification',
2: 'New Activity',
3: 'New Package'*/

import maps from '../../../utils/maps'

export const TICKET_TYPE_DICT = maps.ticket_type.reduce((acc, curr) => {
  return {
    ...acc,
    [curr.value]: curr.label
  }
}, {})

export const ticketTypeOptions = maps.ticket_type

export const getTicketUrlByStatus = ({
  ticket_type,
  activity_id,
  package_id,
  ticket_id,
  submitted_language,
  had_been_inherit_spu
}) => {
  if (ticket_type !== 'cost_modification' && had_been_inherit_spu) {
    const query = `ref=&package_id=${package_id}&lang=${submitted_language || 'en_US'}&page_from=bd_audit`

    return `/aid/tours/basic/${activity_id}?${query}`
  }

  let href = ''

  const page_from = global_env.isMerchant ? '' : '&page_from=bd_audit'

  switch (ticket_type) {
    case 'cost_modification': {
      // Cost Modification
      href = `/act/ticket/${ticket_id}/detail`
      break
    }
    case 'new_activity': {
      // New Activity
      href = `/act/activity/basic/${activity_id}?lang=${submitted_language || 'en_US'}${page_from}`
      break
    }
    case 'new_package': {
      // New Package
      href = `/act/package/info/${activity_id}?lang=${
        submitted_language || 'en_US'
      }&edit_status=1&ref=&package_id=${package_id}&package_type=-1${page_from}`
      break
    }

    case 'package_publish': {
      // 套餐发布
      href = `/act/package/info/${activity_id}?lang=${
        submitted_language || 'en_US'
      }&edit_status=1&ref=&package_id=${package_id}&package_type=-1${page_from}`
      break
    }

    case 'activity_publish': {
      // 活动发布
      href = `/act/activity/basic/${activity_id}?lang=${submitted_language || 'en_US'}${page_from}`
      break
    }
  }

  return href
}
