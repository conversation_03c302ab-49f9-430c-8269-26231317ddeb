<template>
  <div class="common-basic-box">
    <h2>{{ schema.title }}</h2>
    <a-form-model ref="result" :model="result" v-bind="getValue(schema, 'attrs', {})" @submit.native.prevent>
      <template v-for="(schemaItem, index) in schemaList">
        <component
          :is="schemaItem.independent ? 'div' : 'a-form-model-item'"
          :key="index"
          :prop="schemaItem.field"
          label="ttt"
        >
          <component
            :is="getValue(schemaItem, 'config.tooltip', false) ? 'a-tooltip' : 'span'"
            overlayClassName="common-tooltip-style"
            :title="getValue(schemaItem, 'config.tooltip.title', '')"
            :placement="getValue(schemaItem, 'config.tooltip.placement', 'right')"
          >
            <component
              :is="getComponent(schemaItem)"
              :render="schemaItem.config.render"
              :result="result"
              :schema="schemaItem"
              :activity_id="activity_id"
              :schema-config="getValue(schemaConfig, `[${schemaItem.field}].config`, {})"
              v-model="result[schemaItem.field]"
              v-bind="{
                ...$attrs,
                ...getValue(schemaItem, 'config.attrs', {}),
                ...getMultiAttrFactory(schemaItem.obtField)
              }"
              v-on="{
                ...$listeners,
                ...getValue(schemaItem, 'config.listeners', {})
              }"
            ></component>
          </component>
        </component>
      </template>
    </a-form-model>
  </div>
</template>

<script>
import multiLanguageInput from '@activity/components/tpl_special_multi_language_input'
import activityMap from '@activity/components/tpl_activity_map'

export default {
  components: {
    multiLanguageInput,
    activityMap
  },
  props: {
    result: {
      type: Object,
      required: true
    },
    // the chunk of config schema
    schema: {
      type: Object,
      required: true
    },
    schemaConfig: {
      type: Object,
      required: true
    },
    activity_id: Number
  },
  computed: {
    componentDict() {
      return {
        text: 'article',
        select: 'a-select',
        multiLanguageInput: 'multiLanguageInput',
        render: 'render-form-item',
        activityMap: 'activityMap'
      }
    },
    schemaList() {
      return this.getValue(this.schema, 'data', [])
    }
  },
  methods: {
    getCurrentLocal: klook.getCurrentLocal,
    getMultiAttrFactory(type, conf = {}) {
      let current = this.schemaConfig[type] || {}
      return {
        required: current.required,
        requiredEn: current.requiredEn,
        valField: type,
        label: this.getCurrentLocal(this.schemaConfig, {
          type,
          field: 'name_multilang'
        }),
        description: this.getCurrentLocal(this.schemaConfig, {
          type,
          field: 'desc_multilang',
          defaultVal: ''
        }),
        ...conf
      }
    },
    getValue(object, path, defaultValue = '') {
      return _.get(object, path, defaultValue)
    },
    getComponent(schemaItem) {
      let type = this.getValue(schemaItem, 'config.type', 'text')

      return this.getValue(this.componentDict, type, type)
    },
    getRule(schemaItem, rules) {
      return this.getValue(schemaItem, rules, {
        required: false,
        message: `Please fill this ${schemaItem.label}`
      })
    }
  }
}
</script>
