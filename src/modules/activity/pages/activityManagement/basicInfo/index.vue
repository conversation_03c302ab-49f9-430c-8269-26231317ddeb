<template>
  <div id="basic-info-editable" class="basic-info js-basic-info-vm" :data-spm-page="getSpm">
    <div v-disabled="vDisabled" class="basic-info-box">
      <div v-if="!initLoadFinish" class="common-spin-box"><a-spin /></div>
      <div v-else>
        <div v-for="chunk in chunkPageResult" :key="chunk.title" class="common-basic-box">
          <h2 :id="chunk.id || chunk.title">
            <i v-if="chunk.required" class="common-required-star">*</i>
            {{ chunk.title }}
          </h2>

          <component
            :is="chunk.customMode ? 'div' : 'a-form-model'"
            :ref="chunk.customMode ? 'customMode' : 'form'"
            :model="form"
            @submit.prevent
          >
            <div
              v-for="item in chunk.content"
              :key="item.field"
              class="common-chunk-item js-dataset-field"
              :data-field="item.field"
              :data-is-required="getRequiredBy<PERSON>ey(item)"
              :data-is-suggest-filling="getSuggestFillingByKey(item)"
              :class="item.className ? item.className : ''"
            >
              <component
                :is="item.independent ? 'div' : 'a-form-model-item'"
                class="basic-form-item"
                :colon="false"
                :prop="item.field"
                :label="item.schema.label"
                :rules="item.schema.rules"
              >
                <language_destination
                  v-if="item.component === 'language_destination'"
                  ref="language_destination"
                  :schema-config="item.schema"
                  :activity_id="+activity_id"
                  :required="getRequired(item.field)"
                  :result="form"
                  :disabled="item.schema.disabled"
                  v-bind="item.schema"
                  @published_language="publishedLanguage"
                >
                </language_destination>

                <blocklist
                  v-else-if="item.component === 'blocklist'"
                  ref="blocklist"
                  :form="form"
                  :required="getRequired(item.field)"
                  :disabled="item.schema.disabled"
                  :activity-id="+activity_id"
                  :schema-config="item.schema"
                  v-bind="item.schema"
                  @changeData="onChangeData"
                />

                <tpl_special_multi_language_input
                  v-else-if="item.component === 'multi-language-input'"
                  v-model="form[item.field]"
                  class="large-input"
                  valField="name"
                  :ref="item.field"
                  :type="item.type"
                  :required="getRequired(item.field)"
                  :current-create-lang="form.language"
                  :disabled="getFreetextDisabled(item.schema.disabled, item.field)"
                  :show-suffix-count="item.showSuffixCount"
                  :data-field="item.field"
                  v-bind="item.schema"
                />

                <ActivityDeparture
                  v-else-if="item.component === 'departure_country_city'"
                  ref="departure_country_city"
                  :area-data="form[item.field]"
                  :schema-config="item.schema"
                  :disabled="item.schema.disabled"
                  :country-options="allCountryOptions"
                  :required="getRequired(item.field)"
                  @changeData="onChangeData"
                />

                <!-- departure_country_city v2 版本 -->
                <DepartureSelectCity
                  v-else-if="item.component === 'departure-select-city'"
                  ref="departure_country_city_v2"
                  v-model="form[item.field]"
                  :schema-config="item.schema"
                  :disabled="item.schema.disabled"
                  :country-options="allCountryOptions"
                  :required="getRequired(item.field)"
                  @changeData="onChangeData"
                />

                <ActivityDestination
                  v-else-if="item.component === 'destination_country_city'"
                  ref="destination_country_city"
                  :area-data="form[item.field]"
                  :schema-config="item.schema"
                  :disabled="item.schema.disabled"
                  :country-options="allCountryOptions"
                  @change="handleDestinationChange"
                  @changeData="onChangeData"
                />
                <ActivityPOI
                  v-else-if="item.component === 'activity_poi'"
                  ref="activity_poi"
                  :poi-data="form[item.field]"
                  :schema-config="item.schema"
                  :disabled="item.schema.disabled"
                  :activity-id="+activity_id"
                  :country-id="destinationCountry"
                  @changeData="onChangeData"
                />

                <binded_area
                  v-else-if="cacheObj.isShow && item.component === 'binded_area'"
                  ref="binded_area"
                  :schema-config="item.schema"
                  :result="form"
                  :activity_id="+activity_id"
                  :disabled="item.schema.disabled"
                  :tipsType="cacheObj.tipsType"
                  :destinationInfosNodes="cacheObj.destinationInfosNodes"
                  v-bind="item.schema"
                  @change="(data) => (cacheObj.isUnpublishId = data.isUnpublishId)"
                ></binded_area>

                <tpl_activity_map
                  v-else-if="item.component === 'activity-map'"
                  ref="activity_map"
                  :init="form[item.field] || null"
                  :disabled="item.schema.disabled || vDisabled.lock"
                  :schema-config="item.schema"
                  :language="mapLanguage"
                  v-bind="item.schema"
                  @change="handleMapChange"
                />

                <!-- activity_location -->
                <multiple-locations
                  v-else-if="item.component === 'multiple-locations'"
                  ref="multipleLocations"
                  v-model="form[item.field]"
                  :disabled="item.schema.disabled || vDisabled.lock"
                  :schema-config="item.schema"
                  :language="mapLanguage"
                  :freetext-disabled="getFreetextDisabled(item.schema.disabled, item.field)"
                  v-bind="item.schema"
                  @changeData="onChangeData('multiple-locations')"
                />

                <div v-else>
                  <DescMarkdownContent
                    class="strong-tips"
                    placement="right"
                    :is-inline="false"
                    :desc="item.schema && item.schema.strongTips"
                    :line-clamp="3"
                  />
                  <ShimAntdTooltip
                    :title="item.schema.description"
                    :rich-tips="item.schema.richTips"
                    :is-fit-content="['activity_video'].includes(item.field)"
                  >
                    <!-- fix ant-design -->
                    <template v-if="item.component === 'affiliate'">
                      <a-checkbox
                        :checked="Boolean(form[item.field])"
                        :true-label="1"
                        :false-label="0"
                        :disabled="item.schema.disabled"
                        @change="(e) => setChangeVal(Number(e.target.checked), item.field)"
                      >
                        Is this a special activities (2% mark up for affilliates)?
                      </a-checkbox>
                    </template>

                    <template v-else-if="['switch', 'popConfirmSwitch'].includes(item.component)">
                      <component
                        :is="item.component === 'popConfirmSwitch' ? 'popConfirmSwitch' : 'a-switch'"
                        :checked="Boolean(form[item.field])"
                        :on-value="1"
                        :off-value="0"
                        :disabled="item.schema.disabled"
                        :no-tips="$t('93289')"
                        @change="(v) => setChangeVal(Number(v), item.field)"
                      />
                    </template>

                    <div v-else-if="item.field === 'activity_video'" class="__component">
                      <video-upload
                        ref="video"
                        v-model="form[item.field]"
                        :watermark="true"
                        :is_btn_trigger="true"
                        :disabled="item.schema.disabled"
                        :custom-conf="videoUploadConf"
                        :upload-callback="videoUploadCb"
                      ></video-upload>

                      <p v-html="$t('29636')"></p>
                    </div>

                    <PhotoBanner
                      v-else-if="item.component === 'PhotoBanner'"
                      ref="photo_banner"
                      class="activity_banner"
                      :current-create-lang="form.language"
                      :list-all.sync="form[item.field]"
                      :required="getRequired(item.field)"
                      :banner-count="banner_count"
                      :trigger-banner-check="trigger_banner_check"
                      :allow-download-image="!isMerchant"
                      :show-cur-lang-only="showCurLangOnly"
                      :bannerMustBeClear="+bannerQualityType === 1"
                      v-bind="item.schema"
                      @banner-count-change="handleBannerCountChange(item.field, 'photo_banner')"
                      @changeData="onChangeData('photo_banner')"
                    />

                    <div v-else-if="item.component === 'PhotoActivity'" class="__component">
                      <PhotoActivity
                        ref="photo_activity"
                        :currentCreateLang="form.language"
                        :list-all.sync="form[item.field]"
                        :required="getRequired(item.field)"
                        :banner-count="banner_count"
                        :trigger-banner-check="trigger_banner_check"
                        :minBannerCount="minBannerCount"
                        :allow-download-image="!isMerchant"
                        :show-cur-lang-only="showCurLangOnly"
                        :bannerMustBeClear="+bannerQualityType === 1"
                        :limit="99"
                        support-upload-long-image
                        v-bind="item.schema"
                        @banner-count-change="handleBannerCountChange(item.field, 'photo_activity')"
                        @changeData="onChangeData"
                      />
                    </div>

                    <BannerDiscount
                      v-else-if="item.component === 'BannerDiscount'"
                      ref="banner_discount"
                      v-model="form[item.field]"
                      :result="form"
                    ></BannerDiscount>

                    <div v-else-if="item.render">
                      <renderItem
                        ref="renderItem"
                        :render="item.render"
                        :schema-config="item.schema"
                        :result="form"
                        :activity_id="+activity_id"
                        :disabled="item.schema.disabled"
                      ></renderItem>
                    </div>
                  </ShimAntdTooltip>
                </div>

                <div v-if="item.tips" class="tips-style">
                  <span v-html="item.tips"></span>
                </div>
                <div v-if="item.errorMsg" class="common-error-style">
                  <span v-html="item.errorMsg"></span>
                </div>
              </component>
            </div>
          </component>
        </div>
      </div>
    </div>
    <div id="save_act_info_spm" v-bind="$root.bindIHTrack({ type: 'module', spm: 'SaveBasicInfo' })"></div>
  </div>
</template>

<script>
import { mapState, mapActions, mapGetters, mapMutations } from 'vuex'
import { getEditLang, getRefLang, getFlang2Blang } from '@activity/utils'
import language_destination from '@activity/components/language_destination'
import binded_area from '@activity/components/language_destination/binded_area'
import PhotoBanner from '@activity/components/photo-upload/photo_banner'
import PhotoActivity from '@activity/components/photo-upload/photo_activity'
import tpl_special_multi_language_input from '@activity/components/tpl_special_multi_language_input'
import tpl_activity_map from '@activity/components/tpl_activity_map'
import Blocklist from '@activity/components/activity_blocklist'
import StrongTips from '@activity/components/StrongTips.vue'
import VideoUpload from './videoUpload/index'
import renderItem from './renderItem'
import ShimAntdTooltip from '@activity/components/shimAntdTooltip'
import { getFieldTips } from '@activity/utils/fieldGuideTipsDict'
import DescMarkdownContent from '@activity/pages/activityManagement/detailV2/components/descMarkdownContent'
import PopConfirmSwitch from '@activity/components/popconfirm-switch/index.vue'
import BannerDiscount from '@activity/components/banner_discount/index.vue'
import MultipleLocations from '@activity/components/multiple-locations/index.vue'
import DepartureSelectCity from '@activity/components/language_destination/departure-select-city.vue'

// OBT 出发地、目的地、POI三个字段分离
import ActivityPOI from '@activity/components/binded-area/activity-poi/index.vue'
import ActivityDeparture from '@activity/components/binded-area/activity-departure/index.vue'
import ActivityDestination from '@activity/components/binded-area/activity-destination/index.vue'
import { shouldIncludeUnpublishedCountryByAIDs } from '@activity/components/binded-area/const.js'

export default {
  name: 'ActBasicInfo',
  components: {
    ShimAntdTooltip,
    VideoUpload,
    language_destination,
    binded_area,
    tpl_special_multi_language_input,
    tpl_activity_map,
    Blocklist,
    PhotoBanner,
    PhotoActivity,
    renderItem,
    StrongTips,
    DescMarkdownContent,
    PopConfirmSwitch,
    ActivityPOI,
    ActivityDeparture,
    ActivityDestination,
    BannerDiscount,
    MultipleLocations,
    DepartureSelectCity
  },
  inject: ['rootChangeLoading', 'page_from', 'setAlertFixed2provide', 'setBeforeEachSaveConfirmInterceptor'],
  provide() {
    return {
      activityId: this.activity_id,
      curLang: getEditLang(),
      refLang: getRefLang(),
      isEMpage: this.isEMpage,
      getPhotoList: this.getPhotoList
    }
  },
  beforeRouteLeave(to, from, next) {
    this.$root.gsaveBtnDisabled = false
    this.setAlertFixed2provide({
      show: false
    })
    next()
  },
  data() {
    return {
      cacheObj: {
        isUnpublishId: false,
        isInitUnpublish: false,
        tipsType: '',
        isShow: true,
        destinationInfosNodes: undefined,
        previous_country_previous_city: '',
        new_country_new_city: ''
      },
      isNewFlowFlag: false,
      initLoadFinish: false,

      activity_id: parseInt(this.$route.params.id),
      form: {
        affiliate: 0,
        viewable_platform: 0,
        best_price_guarantee: 0,
        need_default_select_package: 0,
        activity_location: [],
        language: klook.urlParam('lang') || undefined
      },

      schemaConfig: {},

      all_banners_desktop: [],
      trigger_banner_check: 0,
      allCountryOptions: [],
      destinationCountry: [],
      fieldVersion: {
        activity_location: undefined,
        departure_country_city: undefined
      }
    }
  },
  computed: {
    ...mapGetters([
      'isPublishWithAI',
      'saveBeforeStatus2getters',
      'merchantEditFreeTextType2getter',
      'lockMerchantEditFreeText2getter'
    ]),
    ...mapState([
      'isMerchant',
      'merchantActHaveBeenApproved',
      'lockMerchantActEditing',
      'activateFooterWrapperState',
      'actStepStatus'
    ]),
    isActivityLocationV2() {
      return this.fieldVersion.activity_location === 'V2'
    },
    isDepartureCityV2() {
      return this.fieldVersion.departure_country_city === 'V2'
    },
    MULTI_LANG_INPUT() {
      const list = ['activity_title', 'activity_subtitle', 'act_description']
      if (this.isActivityLocationV2) {
        return list
      }
      return [...list, 'activity_address']
    },
    mapLanguage() {
      const lang = this.$route.query.lang || 'en_US'

      return getFlang2Blang(lang)
    },
    editLanguage() {
      return klook.urlParam('lang') || undefined
    },
    showCurLangOnly() {
      return ['admin', 'em'].includes(this.page_from) && this.editLanguage !== 'en_US' && !this.isMerchant
    },
    minBannerCount() {
      let len = 0
      if (_.get(this.schemaConfig, 'activity_picture.required', false)) {
        len = 1
      }

      if (this.page_from === 'merchant') {
        len = 3
      }

      return len
    },
    bannerQualityType() {
      return this.schemaConfig?.activity_banner?.banner_quality_type || 2
    },
    isEMpage() {
      return this.$root.roles.is_em
    },
    isCreated() {
      return !_.isEmpty(klook.urlParam('lang'))
    },
    refer_language() {
      let ref = this.$route.query.ref || 'en_US'
      let { language } = this.form
      return !language || language === ref ? '' : ref
    },
    banner_count() {
      return this.all_banners_desktop.length
    },
    videoUploadConf() {
      return {
        folder: 'activities',
        tags: [this.activity_id],
        cropping: true,
        croppingShowDimensions: true,
        croppingDefaultSelectionRatio: 1,
        croppingAspectRatio: 1160 / 460
      }
    },
    /*
      content params
      @param {boolean} [independent] - Itself is an independent ant-form-model
      @param {function} [getData] - when the getData function exists, it will be replace the this.form[field]
      @param {function} validator - get value(by getData() or this.form[field]) to verify, and return boolean, use to time line
      @param {object} [customSchema] - Setting up custom schema
      @param {string} [defaultLabel] - the label value of a-form-model-item
      @param {string} errorMsg - can be modified by setChunkContentData
      @param {string} tips - same as above
     */
    chunkPageConfig2admin() {
      let result = [
        {
          title: __('activity_basic_page_basic_info'),
          required: true,
          content: [
            {
              field: 'language',
              defaultLabel: __('83821'),
              component: 'language_destination',
              ref: 'language_destination',
              independent: true,
              validator: this.validatorCommon
            },
            {
              field: 'published_conditions',
              component: 'blocklist',
              ref: 'blocklist',
              independent: true,
              validator: () => {
                const { status, language_list = [] } = this.$refs.blocklist?.[0]?.save?.() ?? {}

                return status === 1 || language_list.length
              }
            },
            {
              field: 'activity_title',
              component: 'multi-language-input',
              independent: true,
              showSuffixCount: true,
              defaultLabel: __('act_activity_title'),
              validator: this.validatorMultiLangArray
            },
            {
              field: 'activity_subtitle',
              component: 'multi-language-input',
              independent: true,
              showSuffixCount: true,
              defaultLabel: __('act_activity_subtitle'),
              validator: this.validatorMultiLangArray
            },
            {
              isNewFlowFlag: false,
              field: 'act_description',
              component: 'multi-language-input',
              independent: true,
              validator: this.validatorMultiLangArray,
              type: 'markdown',
              showSuffixCount: true,
              defaultLabel: __('48286'),
              className: 'big-markdown'
            },
            {
              field: 'restricted_content',
              defaultLabel: __('107100'),
              component: 'switch',
              validator: this.validatorCommon
            },
            {
              isNewFlowFlag: true,
              field: 'activity_location',
              component: 'activity-map',
              validator: this.validatorActMap,
              defaultLabel: __('act_address'),
              customSchema: {}
            },
            {
              isNewFlowFlag: true,
              field: 'activity_address',
              component: 'multi-language-input',
              independent: true,
              showSuffixCount: true,
              defaultLabel: __('ob_act_addr'),
              validator: this.validatorMultiLangArray
            },
            {
              field: 'departure_country_city',
              component: 'departure_country_city',
              independent: true
            },
            {
              field: 'destination_country_city',
              component: 'destination_country_city',
              independent: true
            },
            {
              field: 'activity_poi',
              component: 'activity_poi',
              independent: true
            },
            {
              field: 'destination_model_type',
              component: 'binded_area',
              independent: true,
              getData() {
                return (this.getRefs('binded_area').length && this.$refs.binded_area[0].form) || {}
              },
              validator: (data, schema) => {
                let { model } = schema
                let fields = ['area.country_id', 'area.city_id']

                if (model === 2) {
                  fields = [...fields, 'area.country_id_from', 'area.city_id_from']
                } else if (model === 3) {
                  fields = ['dest_ids']
                }

                return fields.every((field) => {
                  let value = _.get(data, field, null)
                  return Array.isArray(value) ? value.length : value
                })
              }
            },
            {
              isNewFlowFlag: true,
              field: 'act_description',
              component: 'multi-language-input',
              independent: true,
              validator: this.validatorMultiLangArray,
              type: 'markdown',
              showSuffixCount: true,
              className: 'big-markdown'
            },
            {
              isNewFlowFlag: false,
              field: 'activity_location',
              component: 'activity-map',
              validator: this.validatorActMap,
              defaultLabel: __('act_address'),
              customSchema: {}
            },
            {
              isNewFlowFlag: false,
              field: 'activity_address',
              component: 'multi-language-input',
              independent: true,
              showSuffixCount: true,
              defaultLabel: __('ob_act_addr'),
              validator: this.validatorMultiLangArray
            },
            {
              field: 'viewable_platform',
              defaultLabel: __('49403'),
              component: 'popConfirmSwitch',
              validator: this.validatorCommon
            },
            {
              field: 'need_default_select_package',
              defaultLabel: __('82173'),
              component: 'switch',
              validator: this.validatorCommon
            },
            {
              field: 'best_price_guarantee',
              defaultLabel: __('28444'),
              component: 'switch',
              validator: this.validatorCommon
            },
            // {
            //   field: 'affiliate',
            //   defaultLabel: 'Affiliate',
            //   component: 'affiliate',
            //   ref: 'video',
            //   validator: this.validatorCommon
            // },
            {
              field: 'display_package_card',
              defaultLabel: __('30717'),
              component: 'switch',
              validator: this.validatorCommon
            },
            {
              field: 'remove_watermark',
              defaultLabel: __('28445'),
              component: 'switch',
              validator: this.validatorCommon
            },
            {
              field: 'banner_discount',
              defaultLabel: __('114446'),
              component: 'BannerDiscount',
              validator: this.validatorCommon
            }
          ].filter(
            (item) =>
              hasOwnProperty.call(this.schemaConfig, item.field) &&
              this.schemaConfig[item.field].access_permission !== 0
          ) // just return the fields with the account authorization.
        }
      ]
      result[0].content = result[0].content.filter((o) => {
        return typeof o.isNewFlowFlag !== 'boolean' || this.isNewFlowFlag === o.isNewFlowFlag // isNewFlowFlag渲染逻辑
      })

      this.fmtMultiLocationChunkResult2admin(result)

      if (_.get(this.schemaConfig, 'activity_banner.access_permission', 0)) {
        result.push({
          title: __('activity_basic_page_banner_photo'),
          required: _.get(this.schemaConfig, 'activity_banner.required', false),
          customMode: true, // custom or form
          content: [
            {
              field: 'activity_banner',
              component: 'PhotoBanner',
              independent: true,
              validator: this.validatorBanner,
              getData() {
                return this.getPhotoList('photo_banner')
              }
              // customSchema: {
              //   required: true
              // }
            }
          ]
        })
      }

      if (_.get(this.schemaConfig, 'activity_picture.access_permission', 0)) {
        result.push({
          title: __('basic.activityPhoto'),
          required: _.get(this.schemaConfig, 'activity_picture.required', false),
          customMode: true, // custom or form
          content: [
            {
              field: 'activity_picture',
              component: 'PhotoActivity',
              independent: true,
              validator: this.validatorActPhoto,
              getData() {
                return this.getPhotoList('photo_activity')
              }
            }
          ]
        })
      }

      if (_.get(this.schemaConfig, 'activity_video.access_permission', 0)) {
        result.push({
          title: __('29635'),
          required: _.get(this.schemaConfig, 'activity_video.required', false),
          content: [
            {
              field: 'activity_video',
              component: 'video-upload',
              ref: 'video',
              // defaultLabel: __('act_activity_video'),
              validator: this.validatorCommon
            }
            // {
            //   defaultLabel: 'form test item',
            //   render: (h, ctx) => {
            //     let { result } = ctx.props
            //     return (
            //       <a-switch
            //         checked={ Boolean(result['best_price_guarantee']) }
            //         onChange={checked => {
            //           this.setChangeVal(Number(checked), 'best_price_guarantee')
            //         }}
            //       ></a-switch>
            //     )
            //   },
            // }
          ]
        })
      }

      return result
    },
    notAllowedEditActTitle() {
      return this.merchantEditFreeTextType2getter === 2
    },
    chunkPageConfig2merchant() {
      let result = [
        {
          title: __('activity_basic_page_basic_info'),
          required: true,
          content: [
            {
              field: 'language',
              defaultLabel: __('83821'),
              component: 'language_destination',
              ref: 'language_destination',
              independent: true,
              validator: this.validatorCommon
            },
            {
              field: 'published_conditions',
              component: 'blocklist',
              ref: 'blocklist',
              independent: true,
              validator: () => {
                const { status, language_list = [] } = this.$refs.blocklist?.[0]?.save?.() ?? {}

                return status === 1 || language_list.length
              }
            },
            {
              field: 'activity_title',
              component: 'multi-language-input',
              independent: true,
              showSuffixCount: true,
              defaultLabel: __('act_activity_title'),
              validator: this.validatorMultiLangArray,
              customSchema: {}
            },
            {
              field: 'activity_subtitle',
              component: 'multi-language-input',
              independent: true,
              showSuffixCount: true,
              defaultLabel: __('act_activity_subtitle'),
              validator: this.validatorMultiLangArray
            },
            {
              isNewFlowFlag: false,
              field: 'act_description',
              component: 'multi-language-input',
              independent: true,
              validator: this.validatorMultiLangArray,
              type: 'markdown',
              showSuffixCount: true,
              defaultLabel: __('48286'),
              className: 'big-markdown',
              customSchema: {
                mdConfig: {
                  toolbar: ['bold', 'unordered-list', 'preview']
                }
              }
            },
            {
              isNewFlowFlag: true,
              field: 'act_description',
              component: 'multi-language-input',
              independent: true,
              validator: this.validatorMultiLangArray,
              type: 'markdown',
              showSuffixCount: true,
              className: 'big-markdown',
              customSchema: {
                mdConfig: {
                  toolbar: ['bold', 'unordered-list', 'preview']
                }
              }
            },
            {
              field: 'restricted_content',
              defaultLabel: __('107100'),
              component: 'switch',
              validator: this.validatorCommon
            },
            {
              field: 'viewable_platform',
              defaultLabel: __('49403'),
              component: 'popConfirmSwitch',
              validator: this.validatorCommon
            },
            {
              field: 'best_price_guarantee',
              defaultLabel: __('28444'),
              component: 'switch',
              validator: this.validatorCommon
            },
            // {
            //   field: 'affiliate',
            //   defaultLabel: 'Affiliate',
            //   component: 'affiliate',
            //   ref: 'video',
            //   validator: this.validatorCommon
            // },
            {
              field: 'display_package_card',
              defaultLabel: __('30717'),
              component: 'switch',
              validator: this.validatorCommon
            },
            {
              field: 'remove_watermark',
              defaultLabel: __('28445'),
              component: 'switch',
              validator: this.validatorCommon
            }
          ].filter(
            (item) =>
              hasOwnProperty.call(this.schemaConfig, item.field) &&
              this.schemaConfig[item.field].access_permission !== 0
          ) // just return the fields with the account authorization.
        },
        {
          title: __('activity.detail.location.map'),
          required: true,
          content: [
            {
              isNewFlowFlag: true,
              field: 'activity_location',
              component: 'activity-map',
              validator: this.validatorActMap,
              defaultLabel: __('act_address'),
              customSchema: {}
            },
            {
              isNewFlowFlag: true,
              field: 'activity_address',
              component: 'multi-language-input',
              independent: true,
              defaultLabel: __('ob_act_addr'),
              validator: this.validatorMultiLangArray
            },
            {
              field: 'departure_country_city',
              component: this.isDepartureCityV2 ? 'departure-select-city' : 'departure_country_city',
              independent: true
            },
            {
              field: 'destination_country_city',
              component: 'destination_country_city',
              independent: true
            },
            {
              field: 'activity_poi',
              component: 'activity_poi',
              independent: true
            },
            {
              field: 'destination_model_type',
              component: 'binded_area',
              independent: true,
              getData() {
                return (this.getRefs('binded_area').length && this.$refs.binded_area[0].form) || {}
              },
              validator: (data, schema) => {
                let { model } = schema
                let fields = ['area.country_id', 'area.city_id']

                if (model === 2) {
                  fields = [...fields, 'area.country_id_from', 'area.city_id_from']
                } else if (model === 3) {
                  fields = ['dest_ids']
                }

                return fields.every((field) => {
                  let value = _.get(data, field, null)
                  return Array.isArray(value) ? value.length : value
                })
              }
            },
            {
              isNewFlowFlag: false,
              field: 'activity_location',
              component: this.isActivityLocationV2 ? 'multiple-locations' : 'activity-map',
              validator: this.validatorActMap,
              defaultLabel: this.isActivityLocationV2 ? __('174971') : __('act_address'),
              customSchema: {
                displayDetails2tooltip: true
              }
            },
            {
              isNewFlowFlag: false,
              field: 'activity_address',
              component: 'multi-language-input',
              independent: true,
              defaultLabel: __('ob_act_addr'),
              validator: this.validatorMultiLangArray
            }
          ].filter(
            (item) =>
              hasOwnProperty.call(this.schemaConfig, item.field) &&
              this.schemaConfig[item.field].access_permission !== 0
          ) // just return the fields with the account authorization.
        }
      ]

      result[1].required = result[1].content.reduce(
        (acc, curr) => this.schemaConfig[curr.field].required || acc,
        false
      )

      result.forEach((obj) => {
        obj.content = obj.content.filter((o) => {
          return typeof o.isNewFlowFlag !== 'boolean' || this.isNewFlowFlag === o.isNewFlowFlag // isNewFlowFlag渲染逻辑
        })
      })

      if (_.get(this.schemaConfig, 'activity_banner.access_permission', 0)) {
        result.push({
          title: __('activity_basic_page_banner_photo'),
          required: _.get(this.schemaConfig, 'activity_banner.required', false),
          customMode: true, // custom or form
          content: [
            {
              field: 'activity_banner',
              component: 'PhotoBanner',
              independent: true,
              validator: this.validatorBanner,
              getData() {
                return this.getPhotoList('photo_banner')
              }
              // customSchema: {
              //   required: true
              // }
            }
          ]
        })
      }

      if (_.get(this.schemaConfig, 'activity_picture.access_permission', 0)) {
        result.push({
          title: __('basic.activityPhoto'),
          required: _.get(this.schemaConfig, 'activity_picture.required', false),
          customMode: true, // custom or form
          content: [
            {
              field: 'activity_picture',
              component: 'PhotoActivity',
              independent: true,
              validator: this.validatorActPhoto,
              getData() {
                return this.getPhotoList('photo_activity')
              }
            }
          ]
        })
      }

      if (_.get(this.schemaConfig, 'activity_video.access_permission', 0)) {
        result.push({
          title: __('29635'),
          required: _.get(this.schemaConfig, 'activity_video.required', false),
          content: [
            {
              field: 'activity_video',
              component: 'video-upload',
              ref: 'video',
              // defaultLabel: __('act_activity_video'),
              validator: this.validatorCommon
            }
            // {
            //   defaultLabel: 'form test item',
            //   render: (h, ctx) => {
            //     let { result } = ctx.props
            //     return (
            //       <a-switch
            //         checked={ Boolean(result['best_price_guarantee']) }
            //         onChange={checked => {
            //           this.setChangeVal(Number(checked), 'best_price_guarantee')
            //         }}
            //       ></a-switch>
            //     )
            //   },
            // }
          ]
        })
      }

      return result
    },
    //
    chunkPageResult() {
      const arr = this.$root.isMerchant ? this.chunkPageConfig2merchant : this.chunkPageConfig2admin
      return arr.map((chunk) => {
        return {
          ...chunk,
          id:
            'act-chunk-' +
            encodeURIComponent(chunk.title).replace(/%/g, '').replace(/ /gi, '_').toLowerCase(),
          content: chunk.content.map((content) => {
            let validator = content.validator
            let schema = this.schemaConfig[content.field]
            const customConf = this.getCustomConf(schema, {
              defaultLabel: content.defaultLabel || ''
            })
            let { customSchema, ...otherCustomConf } = customConf
            customSchema = Object.assign({}, customSchema || {}, content.customSchema || {})
            schema = {
              ...otherCustomConf, // label, validate...
              ...schema,
              ...customSchema
            }

            return {
              ...content,
              tips: '',
              errorMsg: '',
              schema: {
                ...schema,
                rules: {
                  required: schema.required,
                  trigger: 'blur,change',
                  validator: (rule, value, callback) => {
                    // the validator of form item
                    if (
                      !rule.required || // no-required
                      (validator && validator(value, schema)) ||
                      !validator
                    ) {
                      // no-validator
                      callback()
                    } else {
                      callback(new Error('Please fill in this field.'))
                    }
                  }
                }
              }
            }
          })
        }
      })
    },

    vDisabled() {
      if (this.lockMerchantActEditing) {
        return {
          lock: true
        }
      }

      return {
        lock: this.lockMerchantEditFreeText2getter,
        defaultScope: 'freeText'
      }
    },
    getSpm() {
      const oid = `activity_${this.activity_id || 0}`
      return `ActivityBasicInfo?oid=${oid}`
    },
    actBasicInfoFieldTips() {
      return getFieldTips('actBasicInfo')
    },
    subCategoryId() {
      return _.get(this.$store.state, 'categoryInfo.sub_category_id', 0)
    }
  },
  watch: {
    'cacheObj.isUnpublishId'(val) {
      this.setGsaveBtnDisabled()
    },
    'cacheObj.tipsType'(val) {
      this.setGsaveBtnDisabled()
    },
    all_banners_desktop() {
      this.initFloorTimelineData()
    },
    form: {
      handler() {
        this.initFloorTimelineData()

        if (this.cacheForm && !_.isEqualWith(this.cacheForm, this.form)) {
          this.setActivateFooterWrapper(true)
        }
      },
      deep: true
    },
    'form.activity_location.location': {
      handler(val, oldVal) {
        if (!this.isActivityLocationV2) return

        if (val && val !== oldVal && this.isNewFlowFlag) {
          this.lickEq2str(val, oldVal || this.cacheObj.location) || this.debounceQueryCity(val)
        }
      },
      deep: true
    }
  },
  async created() {
    if (this.$root.isMerchant) {
      this.setBeforeEachSaveConfirmInterceptor({
        confirmSave: {
          fn: this.handleSave.bind(this),
          params: false
        }
      })
    }

    window.clickHandle2native = (type) => {
      let target = null
      switch (type) {
        case 'click_here':
          target = _.get(this.$refs, 'binded_area[0].$refs.region_binding.applyHandle', false)
          target && target()
          break
        case 'click_modify':
          target = document.querySelector('.pos-gps-location')
          klook.scrollElError(target, { block: 'start' })
          target.focus()
          klook.setScrollTop(klook.getScrollTop() - 120)
          break
      }
    }
    await this.initObConfig()
    await this.initBannerInfo()
    klook.bus.$emit('addHandleSave2bus', this.handleSave)
    this.initLoadFinish = true

    this.getCountryOptions()
  },
  methods: {
    ...mapMutations(['setActivateFooterWrapper']),
    ...mapActions(['getActLang', 'updateActStepStatus2action']),
    fmtMultiLocationChunkResult2admin(result) {
      const { schemaConfig } = this
      // 产品要求就交互要保留, 所以原有的 chunk content 不变, 新交互字段迁移到 content[1]
      if (this.isActivityLocationV2) {
        const locationFields = [
          'departure_country_city',
          'destination_country_city',
          'activity_poi',
          'destination_model_type'
        ]

        const locationContent = result[0].content.reduce((acc, curr) => {
          const { field } = curr
          if (locationFields.includes(field)) {
            if (field === 'departure_country_city' && this.isDepartureCityV2) {
              curr.component = 'departure-select-city'
            }
            acc.push(curr)
          }

          return acc
        }, [])

        const allFields = [...locationFields, 'activity_location']
        const required = allFields.some((field) => schemaConfig[field]?.require)

        result[0].content = result[0].content.filter((item) => !allFields.includes(item.field))
        const content = [
          ...locationContent,
          {
            field: 'activity_location',
            component: 'multiple-locations',
            defaultLabel: __('174971'),
            validator: this.validatorActMap,
            getData() {
              return this.getRefs('multipleLocations')?.[0]?.getData?.() || {}
            }
          }
        ].filter(
          (item) =>
            hasOwnProperty.call(this.schemaConfig, item.field) &&
            this.schemaConfig[item.field].access_permission !== 0
        ) // just return the fields with the account authorization.

        result.push({
          title: __('174964'),
          required,
          content
        })
      }
    },
    handleDestinationChange(data) {
      const { country_id } = data
      if (!country_id) {
        this.destinationCountry = []
        return
      }
      this.destinationCountry = Array.isArray(country_id) ? country_id : [country_id]
    },
    async getCountryOptions() {
      const res = await ajax.get(ADMIN_API.act.get_all_countries, {
        params: {
          // 7, 8, 9, 206, 331 wifi/sim
          include_not_show_country:
            [7, 8, 9, 206, 331].includes(+this.subCategoryId) ||
            shouldIncludeUnpublishedCountryByAIDs.includes(+this.activity_id)
              ? 1
              : 0 // 0 不包含 Unpublished 国家
        }
      })
      this.allCountryOptions = res || []
    },
    onChangeData(refKey) {
      if (refKey === 'multiple-locations') {
        this.$refs.form?.[1]?.validateField?.('activity_location')
      }
      // 更换了 banner 图需要重新生成 banner_discount
      if (refKey === 'photo_banner' && this.form.banner_discount) {
        this.form.banner_discount.discount_images = this.getBannerDiscountUrl()
      }

      this.setActivateFooterWrapper(true)
    },
    getRequiredByKey({ schema }) {
      return !!schema.required ?? false
    },
    getSuggestFillingByKey({ schema }) {
      return !!schema.config?.suggest_filling
    },
    getFreetextDisabled(schema, field) {
      const excludeFields = ['activity_title']
      if (excludeFields.includes(field)) {
        return schema || this.lockMerchantEditFreeText2getter
      }
      return schema || this.isPublishWithAI || this.lockMerchantEditFreeText2getter
    },
    getCurrentLocal: klook.getCurrentLocal,
    setGsaveBtnDisabled() {
      let { isUnpublishId, tipsType } = this.cacheObj
      if (!this.isNewFlowFlag || isUnpublishId) {
        this.$root.gsaveBtnDisabled = false
      } else {
        this.$root.gsaveBtnDisabled = tipsType === 'initNoCity'
      }
    },
    lickEq2str(val, oldVal) {
      if (!val || !oldVal) return false
      let arr = val.split(',').map((v) => parseInt(v * 100))
      let oldArr = oldVal.split(',').map((v) => parseInt(v * 100))
      return arr[0] === oldArr[0] && arr[1] === oldArr[1]
    },
    onceCheckLocation(res) {
      // initCityNeq 历史数据清洗，城市不一致
      // initNoCity 历史数据清洗，城市不存在时字段下方的提示文案 has_calculate_result=false
      let { has_calculate_result, binded_area_info } = res
      let { city = {} } = this.cacheObj
      if (has_calculate_result === false) {
        this.cacheObj.tipsType = 'initNoCity'
        this.setAlertFixed2provide({
          show: true,
          html: klook.parseStr1(__('30442'), {
            // 历史数据清洗，城市不存在时页面顶部的提示文案
            create_a_new_city: `<a href="javascript:window.clickHandle2native('click_here')" class="common-link">${__(
              '30443'
            )}</a>`,
            modify_the_location_coordinates: `<a href="javascript:window.clickHandle2native('click_modify')" class="common-link">${__(
              '30444'
            )}</a>`
          })
        })
      } else if (has_calculate_result === true) {
        let { city: newCity } = binded_area_info || {}
        if (city.area_id !== newCity.area_id) {
          this.cacheObj.destinationInfosNodes = klook.parseStr1(__('30440'), {
            city: `${city.area_id} - ${city.area_name}`
          })
          this.setAlertFixed2provide({
            show: true,
            html: this.cacheObj.destinationInfosNodes
          })
          this.cacheObj.tipsType = 'initCityNeq'
        } else {
          this.checkCityPage(res)
        }
      }
    },
    getCountryCity() {
      let { area = {}, cityOptions = [], countryOptions = [] } = this.$refs.binded_area[0].getAreaFormData()
      let prevCity = cityOptions.find((o) => o.id === area.city_id) || {}
      let prevCountry = countryOptions.find((o) => o.id === area.country_id) || {}
      return {
        city: prevCity,
        country: prevCountry,
        text: `${prevCity.id || ''} - ${prevCity.name || ''}`
      }
    },
    async get2calculate_area_info_by_location(location) {
      return ajax.get(
        {
          url: ADMIN_API.act.calculate_area_info_by_location,
          params: {
            latlng: location
          }
        },
        {
          loading: true
        }
      )
    },
    checkCityPage(res) {
      if (klook.getType(res.destination_info) === 'Array') {
        this.cacheObj.destinationInfosNodes = klook.parseStr1(__('30438'), {
          Page_ID_Page_Name_publish_status: res.destination_info
            .map((o) => {
              return `${o.destination_id}-${o.destination_name} (${
                o.publish_status ? __('global_published') : __('global_unpublished')
              })`
            })
            .join(', ')
        })
        this.cacheObj.tipsType = 'queryCityPageSuc'
      } else {
        this.cacheObj.tipsType = 'queryCityPageErr'
      }
    },
    debounceQueryCity: _.debounce(async function (location) {
      let res = await this.get2calculate_area_info_by_location(location)
      if (klook.getType(res) !== 'Object') return
      this.cacheObj.tipsType = ''
      this.setAlertFixed2provide({
        show: false
      })
      this.showChangeLocationTip(res)
      this.updateFormData(res.binded_area_info)
      if (this.cacheObj.tipsType) return
      // queryCityPageErr 没有查询到关联的城市页
      // queryCityPageSuc 查询到关联的城市页
      // queryNoCity 无法推算出城市的提示文案
      if (res.has_calculate_result) {
        this.checkCityPage(res)
      } else {
        this.cacheObj.tipsType = 'queryNoCity'
      }
    }, 300),
    updateFormData(binded_area_info) {
      let { city, country } = binded_area_info || {}
      let { destination_model_type: dmt } = this.form
      if (dmt && dmt.binded_area_info) {
        let obj = {
          ...city,
          city,
          country
        }
        if (klook.getType(dmt.binded_area_info) === 'Object') {
          this.$set(dmt.binded_area_info.area_items, 0, obj)
          this.cacheObj.isShow = false
          this.$nextTick(() => {
            this.cacheObj.isShow = true
          })
        }
      }
    },
    showChangeLocationTip(res) {
      let { previous_country_previous_city, new_country_new_city } = this.cacheObj
      this.cacheObj.previous_country_previous_city = previous_country_previous_city =
        this.getCountryCity().text
      let { city = {} } = res.binded_area_info || {}
      this.cacheObj.new_country_new_city = new_country_new_city = `${city.area_id || ''} - ${
        city.area_name || ''
      }`
      if (previous_country_previous_city === new_country_new_city) return
      let content = (h) => (
        <span>
          {klook.parseStr1(__('30435'), {
            previous_country_previous_city,
            new_country_new_city
          })}
        </span>
      )
      this.modalInfo2self && this.modalInfo2self.destroy()
      this.modalInfo2self = this.$info({
        content: content //输入经纬度后导致城市发生变更的提示toast
      })
    },
    initFloorTimelineData: _.debounce(function () {
      let list = this.chunkPageResult.map((chunk) => {
        let count = chunk.content.reduce(
          (acc, curr) => {
            acc.count++
            if (curr.schema.required) {
              acc.requireCount++
            }
            let data = typeof curr.getData === 'function' ? curr.getData.call(this) : this.form[curr.field]
            acc.data.push({
              field: curr.field,
              value: data
            })
            if ((curr.validator && curr.validator(data, curr.schema)) || !curr.validator) {
              acc.num++
              if (curr.schema.required) {
                acc.requireNum++
              }
            } else {
              acc.notFilled.push({
                field: curr.field,
                required: curr.schema.required,
                config: curr.schema.config
              })
            }

            return acc
          },
          {
            count: 0,
            num: 0,
            requireCount: 0,
            requireNum: 0,
            notFilled: [], // location no data completed
            data: [] // all data
          }
        )
        return {
          title: chunk.title,
          id: chunk.id,
          ...count
        }
      })

      klook.bus.$emit('scrollToSuggestField2bus', {
        data: list
      })

      klook.bus.$emit('floorTimeline2bus', {
        floorData: {
          list
        }
      })
      return list
    }, 300),
    setChunkContentData(field, data) {
      if (toString.call(data) === '[object Object]') {
        for (let chunk of this.chunkPageResult) {
          for (let content of chunk.content) {
            if (content.field === field) {
              Object.assign(content, data)
              this.$forceUpdate()
              return content
            }
          }
        }
      }
    },
    markdownContentValidator() {
      for (let curr of this.MULTI_LANG_INPUT) {
        let refs = this.getRefs(curr)

        if (_.get(this, `schemaConfig[${curr}].access_permission`, 0) === 2 && refs.length) {
          if (refs.some((item) => !item.markdownContentValidator())) {
            return false
          }
        }
      }

      return true
    },
    async handleSave(skipRoute = true) {
      this.setChunkContentData('activity_picture', { errorMsg: '' })

      let modify_id = klook.getModifyId()
      // am 才能设置屏蔽语言
      if (hasOwnProperty.call(this.schemaConfig, 'published_conditions')) {
        this.getRefs('blocklist').length && (await this.$refs.blocklist[0].remind())
      }
      this.rootChangeLoading(true)
      if (!this.validateBannerCount()) {
        this.rootChangeLoading(false)
        return
      }

      if (await this.getValidateFromStatus()) {
        if (hasOwnProperty.call(this.schemaConfig, 'activity_picture')) {
          let len = this.minBannerCount
          let maxLen = this.$refs.photo_activity[0].limit
          const photo_activity = this.getPhotoList('photo_activity')
          if ((len && photo_activity.length < len) || photo_activity.length > maxLen) {
            let content =
              photo_activity.length < len
                ? __('upload_4_photos_at_least').replace(/\d{1}/, len)
                : klook.parseStr1(this.$t('28992'), { 20: maxLen })
            this.setChunkContentData('activity_picture', {
              errorMsg: content
            })
            this.$message.error({
              content
            })
            this.rootChangeLoading(false)
            klook.scrollElError()
            return
          }
        }

        let { ...data } = this.form

        if (hasOwnProperty.call(this.schemaConfig, 'activity_banner')) {
          data.activity_banner = this.getPhotoFormatParams('photo_banner')
        }
        if (hasOwnProperty.call(this.schemaConfig, 'activity_picture')) {
          data.activity_picture = this.getPhotoFormatParams('photo_activity')
        }

        const pictures = [...(data.activity_banner || []), ...(data.activity_picture || [])]

        if (pictures.length) {
          let isValid = pictures
            .filter((pic) => {
              const isEn = ['en_US', 'ALL'].includes(pic.language_type)

              return data.language === 'en_US' ? isEn : !isEn // 英语的话需要取 'en_US' 或 'ALL'，其他语言则取相对应语言图片
            })
            .some((pic) => pic?.image?.some((img) => img.is_card))
          if (!isValid) {
            this.$message.error(this.$t('75803'))
            this.rootChangeLoading(false)
            return
          }
        }

        if (this.$refs.binded_area) {
          data.destination_model_type = this.$refs.binded_area[0].getAreaData()
        }

        if (this.$refs.departure_country_city) {
          data.departure_country_city = this.$refs.departure_country_city[0].getAreaData()
        }

        if (this.$refs.destination_country_city) {
          data.destination_country_city = this.$refs.destination_country_city[0].getAreaData()
        }

        if (this.$refs.activity_poi) {
          data.activity_poi = this.$refs.activity_poi[0].getAreaData()
        }

        if (this.$refs.blocklist) {
          data.published_conditions = this.$refs.blocklist[0].save() || {}
        }

        if (Array.isArray(data.activity_location)) {
          data.activity_location = data.activity_location.map((item) => {
            // backend - HTML tags are not supported in the text
            // eslint-disable-next-line no-unused-vars
            const { path, path_sep, ...other } = item

            return other
          })
        }

        data = Object.keys(data).reduce(
          (acc, curr) => [
            ...acc,
            {
              field_key: curr,
              value: data[curr]
            }
          ],
          []
        )

        let resp = [
          ...(await Promise.all([
            (async () => {
              let res = await ajax.postBody(ADMIN_API.act.update_activity_basic_info, {
                data: {
                  field_items: data,
                  page_from: klook.getPlatformRoleKey(),
                  activity_id: this.activity_id,
                  modify_id,
                  language: this.form.language
                },
                noDefaultResponseInterceptor: true
              })
              const extObj = {
                save_type: this.saveBeforeStatus2getters ? 1 : 0,
                err_message: res?.success ? '' : res?.error?.message || 'Error: false'
              }
              this.$root.trackIHEvent('.js-edit-save-btn', extObj)
              if (res?.success) {
                klook.bus.$emit('initQueryData2bus')
                this.$refs.blocklist && this.$refs.blocklist[0].saveSucCallback(res.success) // 回写数据
              } else {
                this.$root.trackIHEvent('#save_act_info_spm', {
                  err_message: res?.error?.message ?? 'SaveBasicInfo Error'
                })
              }

              return res?.success
            })()
          ]))
        ]
        if (resp.every((item) => item)) {
          await this.updateActStepStatus2action({
            activity_id: +this.activity_id,
            status: 1,
            step: 'activity_basic_info',
            language: new URLSearchParams(location.search).get('lang') || this.form.language
          })
          klook.bus.$emit('refreshGuideStatus')
          this.rootChangeLoading(false)
          this.$message.success(this.$t('global_success'))
          this.setAlertFixed2provide({
            show: false
          })
          // 保存好了之后要收起
          this.setActivateFooterWrapper(false)
          skipRoute &&
            this.$router.push({
              path: `/act/activity/detail/${this.activity_id}`,
              query: {
                ...this.$route.query,
                lang: this.form.language
              }
            })
        } else {
          this.rootChangeLoading(false)
        }
      } else {
        this.rootChangeLoading(false)
        if (!this.markdownContentValidator()) {
          this.$message.error(this.$t('110654'))
          return
        }
        this.$message.warn(__('package_error_mandatory'))
        klook.scrollElError(document.querySelector('.common-has-error'))
        return false
      }
      await ajax.get(ADMIN_API.act.modify_completed, {
        params: {
          snapshot_modify_id: modify_id
        }
      })
      this.rootChangeLoading(false)
    },
    getRefs(key) {
      let refs = this.$refs[key]
      if (!refs) {
        return []
      }
      if (!Array.isArray(refs)) {
        refs = [refs]
      }

      return refs
    },
    getPhotoList(key) {
      return _.flatten(this.getRefs(key).map((item) => item.getList()))
    },
    getPhotoFormatParams(key) {
      return _.flatten(this.getRefs(key).map((item) => item.getFormatParams()))
    },
    getBannerDiscountUrl() {
      return _.flatten(this.getRefs('banner_discount').map((item) => item.handleComputedUrl()))[0] || ''
    },
    // 获取所有 banner desktop
    getAllBannersDesktop() {
      const photo_banner = this.getPhotoList('photo_banner')
      const photo_activity = this.getPhotoList('photo_activity')
      let banner_desktops =
        photo_banner.map((v) => {
          return {
            ...v.DESKTOP,
            typeFlag: 'banner'
          }
        }) || []
      const banner_in_activity_images = photo_activity.filter((v) => v.banner_display)
      banner_desktops = [
        ...banner_desktops,
        ...banner_in_activity_images.map((v) => {
          return {
            ...v.DESKTOP,
            typeFlag: 'activity'
          }
        })
      ]

      return banner_desktops
    },
    async initBannerInfo() {
      let promise_arr = []
      if (hasOwnProperty.call(this.schemaConfig, 'activity_banner')) {
        promise_arr.concat(this.getRefs('photo_banner').map((item) => item.$nextTick((_) => true)))
      }
      if (hasOwnProperty.call(this.schemaConfig, 'images')) {
        promise_arr.concat(this.getRefs('photo_activity').map((item) => item.$nextTick((_) => true)))
      }
      await Promise.all(promise_arr)
      this.all_banners_desktop = this.getAllBannersDesktop()
    },
    initObConfig: _.debounce(async function () {
      let { field_items, is_calculate_location_act, field_version } = await ajax.get({
        url: ADMIN_API.act.get_act_basic_info_schema,
        params: {
          activity_id: this.activity_id,
          language: this.form.language,
          refer_language: this.refer_language,
          page_from: klook.getPlatformRoleKey()
        }
      })
      Object.assign(this.fieldVersion, field_version || {})
      this.$root.isNewFlowFlag = this.isNewFlowFlag = is_calculate_location_act
      let { result, schemaConfig } = (field_items || []).reduce(
        (acc, curr) => {
          let { field_key, value, ...schema } = curr

          // fmt data
          let config = (schema.config && JSON.parse(schema.config)) || {}
          schema.attr_id = field_key
          schema.config = config

          return {
            result: {
              ...acc.result,
              [field_key]: value
            },
            schemaConfig: {
              ...acc.schemaConfig,
              [field_key]: {
                ...schema, // by backend
                ...config // obt
              }
            }
          }
        },
        {
          result: {},
          schemaConfig: {}
        }
      )

      if (this.$root.isNewFlowFlag && !this.isActivityLocationV2) {
        await this.initLocationFn(result)
      }
      this.$set(this, 'form', result)
      this.schemaConfig = schemaConfig

      const switchFields = ['viewable_platform', 'best_price_guarantee', 'need_default_select_package']
      switchFields.forEach((field) => {
        if (hasOwnProperty.call(result, field)) {
          let value = result[field]
          this.form[field] = [0, 1].includes(value)
            ? value
            : _.get(schemaConfig, `${field}.config.default_choices`, 0)
        }
      })

      await this.$nextTick()
      this.cacheForm = _.cloneDeep(this.form)
      this.autoUpdatePackageStepStatus()
    }, 50),
    async autoUpdatePackageStepStatus() {
      const { isMerchant, activity_id, actStepStatus } = this
      if (
        !isMerchant ||
        _.get(actStepStatus, 'activity_basic_info', 0) ||
        !this.checkRequiredSchemaConfigValue()
      ) {
        return
      }

      await this.updateActStepStatus2action({
        activity_id: +activity_id,
        status: 1,
        step: 'activity_basic_info',
        language: new URLSearchParams(location.search).get('lang') || this.form.language
      })
    },
    checkRequiredSchemaConfigValue() {
      const { chunkPageResult = [] } = this

      const requiredContent = chunkPageResult.reduce((acc, item) => {
        const { content = [] } = item
        return [
          ...acc,
          ...content.filter((con) => {
            return (
              _.get(con, 'schema.required', false) ||
              ['activity_picture', 'activity_banner'].includes(con.field) // 特殊处理，这两个字段不受obt控制
            )
          })
        ]
      }, [])

      const validate = requiredContent.map((item) => {
        const { field, validator, schema } = item
        const value = _.get(this.form, field, undefined)

        if (typeof validator === 'function') {
          return validator(value, schema)
        }

        if (['departure_country_city', 'activity_poi'].includes(field)) {
          return Array.isArray(value) && value.length
        }

        if (['destination_country_city'].includes(field)) {
          const { area_items = [], dest_ids = [] } = value || {}
          return area_items.length || dest_ids.length
        }
        return true
      })

      return validate.every((item) => item)
    },
    async initLocationFn(formData) {
      if (klook.getType(formData) !== 'Object') return
      let location = _.get(formData, 'activity_location.location', '')
      if (!location) return
      this.cacheObj.location = location
      let obj = _.get(formData, 'destination_model_type.binded_area_info.area_items[0]', {})
      let { city = {}, country = {} } = obj
      this.cacheObj.city = city
      this.cacheObj.country = country
      if (country.area_id === 1017) {
        this.cacheObj.isInitUnpublish = true
        return
      }
      let res = await this.get2calculate_area_info_by_location(location)
      if (klook.getType(res) !== 'Object') return
      this.onceCheckLocation(res)
      let { binded_area_info } = res
      let { city: newCity = {}, country: newCountry = {} } = binded_area_info || {}
      if (newCity.area_id) {
        // 覆盖
        obj.city = newCity
        obj.country = newCountry
      }
    },
    getMultiLangData(data, { langField = 'language', valField = 'value', defaultVal = 'please input' } = {}) {
      let current = _.find(data, {
        [langField]: lang_conf.getLangObj('F_LANG', 'B_LANG')[KLK_LANG]
      })

      return (current && current[valField]) || defaultVal
    },
    // 根据OB 的字段，生成
    getCustomConf(data = {}, { config = {}, defaultLabel = '' } = {}) {
      const currentField = data.attr_id
      let description = this.getMultiLangData(_.get(data, 'config.desc_multilang', {}))
      let customSchema = {}

      let richTips // tooltip hover content
      if (this.isMerchant) {
        if (['activity_banner', 'activity_picture'].includes(currentField)) {
          // 图片不需要在 chunk item 里显示 tooltip，在组件内实现
          richTips = description = ''
          // 在图片组件内分开显示 tooltip
          customSchema = {
            photoCropRichTips: {
              content: this.$t('83839')
            },
            captionsRichTips: {
              content: this.$t('85994')
            }
          }

          if ('activity_location' === currentField) {
            richTips = description = ''
          }
        } else {
          richTips = this.actBasicInfoFieldTips.find((item) => item.field === currentField) || ''
        }
      } else {
        richTips = ''
      }

      let strongTips
      if (this.isMerchant && currentField === 'activity_picture') {
        strongTips = this.$t('83840')
      } else {
        strongTips = this.getMultiLangData(_.get(data, 'config.obvious_multilang', {}), {
          defaultVal: ''
        })
      }

      let common = {
        disabled: data.access_permission === 1,
        valField: currentField,
        label: defaultLabel || this.getMultiLangData(_.get(data, 'config.name_multilang', {})),
        description,
        richTips,
        strongTips,
        required: data.required,
        customSchema,
        ...config
      }

      // 可编辑freetext的情况下活动标题不能翻译
      if (currentField === 'activity_title' && this.notAllowedEditActTitle) {
        Object.assign(common, {
          disabled: true,
          description: this.$t('84278'),
          richTips: ''
        })
      }

      if (currentField === 'activity_location' && this.isActivityLocationV2) {
        Object.assign(common, { description: '' })
      }

      if (this.MULTI_LANG_INPUT.includes(currentField)) {
        return {
          ...common,
          requiredEn: currentField === 'activity_title'
        }
      } else {
        return common
      }
    },
    getRequired(field) {
      return (this.schemaConfig[field] && this.schemaConfig[field].required) || false
    },
    handleMapChange({ location, place_id, location_wgs84, formatted_address }) {
      this.form.activity_location = {
        location,
        place_id,
        location_wgs84
      }
      if (formatted_address) {
        const schema = _.get(this.schemaConfig, 'activity_address.access_permission')
        const disabled = this.getFreetextDisabled(schema === 1, 'activity_address')
        if (!disabled) {
          const addressList = this.form.activity_address || []
          const lang = getEditLang()
          this.form.activity_address = addressList.map((item) => {
            if (lang === item.language) {
              item.name = formatted_address
            }
            return item
          })
        }
      }
    },
    async publishedLanguage(v) {
      if (!this.isCreated) {
        // 仅在创建的时候才能更改
        this.form.language = Array.isArray(v) ? v[0] : v
        await this.initObConfig()
      }
    },

    handleBannerCountChange(fieldKey, refKey) {
      // 当没有勾选 set as card 的时候，上传 banner 的时候需要自动勾选
      if (refKey === 'photo_banner') {
        const list = document.querySelectorAll('.set_as_card.ant-radio-wrapper-checked')
        if (!list.length) {
          this.$nextTick(() => {
            document.querySelector('.activity_banner .set_as_card')?.click?.()
          })
        }
      }
      this.validateBannerCount()
      // set the value
      // this.form[fieldKey] = this.$refs[refKey][0].getFormatParams()
    },
    // 转换图片的pre_process: c_crop,h_750,w_1891,x_494,y_250/
    getCropObj(pre_process) {
      let str = pre_process.replace('/', '').replace(/_/g, '":"').replace(/,/g, '","')
      return JSON.parse(`{"${str}"}`)
    },
    // 检测 banner 数量以及比例是否匹配
    validateBannerCount() {
      this.all_banners_desktop = this.getAllBannersDesktop()
      const photo_banner = this.getPhotoList('photo_banner')
      const photo_activity = this.getPhotoList('photo_activity')
      const all_photo = [...photo_banner.map((v) => v.DESKTOP), ...photo_activity.map((v) => v.DESKTOP)]
      // banner 数量大于 1
      if (this.banner_count > 1) {
        // 如果有 banner 图片的比例是 1160:460, 提示全部重裁
        let unvalidate_item = all_photo.find((v) => {
          if (!v.pre_process) {
            return false
          } else if (v.pre_process.includes('1160:460')) {
            return true
          } else {
            let obj = this.getCropObj(v.pre_process)
            return +(obj.w / obj.h).toFixed(1) === 2.5
          }
        })
        if (unvalidate_item) {
          this.$info({
            content: __('act_photo_add_banner'),
            title: __('global_confirm'),
            type: 'info'
          })
          this.trigger_banner_check += 1
          return false
        }
      } else {
        // 如果有 banner 图片的比例是 960:460, 提示全部重裁
        let unvalidate_item = all_photo.find((v) => {
          if (!v.pre_process) {
            return false
          } else if (v.pre_process.includes('960:460')) {
            return true
          } else {
            let obj = this.getCropObj(v.pre_process)
            return +(obj.w / obj.h).toFixed(1) === 2.1
          }
        })
        if (unvalidate_item) {
          this.$info({
            content: this.$t('act_photo_less_banner'),
            title: __('global_confirm'),
            type: 'info'
          })
          this.trigger_banner_check += 1
          return false
        }
      }
      return true
    },
    async validateForm() {
      return (
        await Promise.all(
          this.getRefs('form').map(
            (form) => new Promise((resolve) => form.validate((valid) => resolve(valid)))
          )
        )
      ).every((item) => item)
    },
    async getCompnentsValidate(compName) {
      const comp = this.getRefs(compName)
      if (comp.length) {
        return await comp[0].validateForm()
      }
      return true
    },
    async getValidateFromStatus() {
      let result = await Promise.all([
        (async () => {
          return await this.validateForm()
        })(),
        (async () => {
          if (this.getRefs('binded_area').length) {
            return await this.$refs.binded_area[0].validateForm()
          }
          return true
        })(),
        this.getCompnentsValidate('departure_country_city'),
        this.getCompnentsValidate('departure_country_city_v2'),
        this.getCompnentsValidate('destination_country_city'),
        ...this.MULTI_LANG_INPUT.reduce((acc, curr) => {
          let refs = this.getRefs(curr)
          // 0 不可看
          // 1 只读
          // 2 // 可读写
          if (_.get(this, `schemaConfig[${curr}].access_permission`, 0) === 2 && refs.length) {
            return [...acc, ...refs.map((item) => item.validateForm())]
          }
          return acc
        }, []),
        (async () => {
          // 校验 banner / activity 图片
          const photo_banner = this.getRefs('photo_banner').map((item) => item.validate())
          const photo_activity = this.getRefs('photo_activity').map((item) => item.validate())
          // let minCount = photo_activity.getList().length >= 3
          const validate_pass = await Promise.all([...photo_banner, ...photo_activity])
          return !validate_pass.includes(false)
        })()
      ])
      return result.every((item) => item)
    },
    //
    setChangeVal(value, field) {
      this.form[field] = value
    },
    videoUploadCb(result) {
      // @TODO 针对 ttd banner ab test 做临时判断
      const ABtestAidList = [
        20722, 23958, 99418, 104942, 107743, 122939, 132220, 100799, 127327, 46179, 8187, 1025, 118598, 82561
      ]
      if (ABtestAidList.includes(+this.activity_id)) return

      if (result[0].width / result[0].height !== 16 / 9) {
        // the ending klook part is 16:9 so video ratio should also be 16:9
        this.$message.warn('Please upload video with ratio of 16:9 aspect ratio')
        klook.abort()
      }
    },
    // chunk content's validator
    validatorCommon(data) {
      return data !== '' && data !== undefined && data !== null
    },
    validatorMultiLangArray(data, schema) {
      return data.every((item, index) => {
        if (index === 1) {
          // ref
          return item.language === 'en_US' && schema.requiredEn ? item.name : true
        }
        return item.name
      })
    },
    validatorActMap(data) {
      if (this.isActivityLocationV2) {
        return !!this.form.activity_location?.length
      }
      return data.location
    },
    validatorBanner(photo) {
      return photo.length || undefined
    },
    validatorActPhoto(photo) {
      return photo.length >= this.minBannerCount || undefined
    }
  }
}
</script>

<style lang="scss">
.merchant-container {
  .basic-form-item {
    max-width: calc(100% - 280px);
  }
}
#basic-info-editable {
  // fix ant-form-model validator bug
  .has-error .ant-input:disabled,
  .has-error .ant-input:disabled:hover {
    border-color: rgb(217, 217, 217);
  }
}
</style>

<style lang="scss" scoped>
.basic-form-item {
  margin-bottom: 0;

  ::v-deep {
    .ant-col.ant-form-item-label {
      padding: 16px 0 8px 0;
      line-height: 20px;
    }
    .ant-input {
      width: 440px;
    }
    .ant-form-item-children {
      width: 100%;
      display: block;
    }
  }
}

.tips-style {
  line-height: 1.5em;
  font-style: italic;
  color: #a6a6a6;
}

.big-markdown {
  ::v-deep .ant-col.ant-form-item-control-wrapper {
    width: 100%;
    max-width: none;
  }
}
</style>
