<template>
  <div class="video-upload">
    <div v-if="uploadView" class="upload-view-video">
      <UploadView
        v-bind="uploadView"
        :upload-text="$t(_videoUrl ? '83893' : '83892')"
        @upload="uploadVideoHandle"
      ></UploadView>
      <div style="display: table; position: relative">
        <video
          v-if="_videoUrl"
          class="video-upload-ele"
          :src="_videoUrl"
          style="display: block"
          controls
        ></video>
      </div>
    </div>
    <div v-else-if="is_btn_trigger">
      <a-button @click="uploadVideoHandle" type="primary" style="margin: 12px 0" :disabled="disabled">
        {{ _videoUrl ? $t('global_reupload') : $t('taxonomy_destination_upload_video') }}
      </a-button>
      <div style="display: table; position: relative">
        <video
          v-if="_videoUrl"
          class="video-upload-ele"
          :src="_videoUrl"
          style="display: block"
          controls
        ></video>
        <label
          v-if="_videoUrl && !disabled"
          class="video-upload-clear position-right-clear"
          @click.stop="clearVideoHandle"
          >&#10005;</label
        >
      </div>
    </div>

    <div v-else class="video-container" @click="uploadVideoHandle">
      <label v-if="_videoUrl && !disabled" class="video-upload-clear" @click.stop="clearVideoHandle"
        >&#10005;</label
      >
      <div class="video-content">
        <span v-if="!_videoUrl" class="el-icon-upload2 video-upload-btn"></span>
        <video v-if="_videoUrl" class="video-upload-ele" :src="_videoUrl" controls></video>
      </div>
    </div>
    <a-input
      size="small"
      v-if="show_url"
      class="input-video-url"
      placeholder="Please input URL"
      v-model="inputVideoUrl"
    ></a-input>
  </div>
</template>

<script>
import { cloudinaryOptions } from '@activity/components/photo-group/const'
import UploadView from '@activity/pages/activityManagement/act-create/main/component/upload-view.vue'
export default {
  name: 'video-upload',
  components: {
    UploadView
  },
  install(Vue) {
    Vue.component(this.name, this)
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    uploadView: {
      type: Object,
      default: null
    },
    value: String,
    watermark: {
      type: Boolean,
      default: false
    },
    show_url: {
      type: Boolean,
      default: false
    },
    width: {
      type: [Number, String]
    },
    height: {
      type: [Number, String]
    },
    video_url: {
      type: String,
      default: ''
    },
    is_btn_trigger: {
      type: Boolean,
      default: false
    },
    customConf: {
      type: Object,
      default: () => ({})
    },
    uploadCallback: {
      type: Function,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      inputVideoUrl: ''
    }
  },
  computed: {
    _videoUrl: {
      get() {
        // eslint-disable-next-line
        this.inputVideoUrl = this.value || this.video_url
        return this.inputVideoUrl
      },
      set(val) {
        this.$emit('change', val)
      }
    }
  },
  watch: {
    inputVideoUrl(url) {
      this.$emit('uploaded', url)
      this.$emit('change', url)
    }
  },
  methods: {
    clearVideoHandle() {
      this.$emit('clear')
      this.$emit('change', '')
    },
    uploadVideoHandle: _.debounce(async function () {
      if (this.disabled) {
        return
      }
      let [result, error] = await new Promise((resolve) => {
        window.cloudinary &&
          window.cloudinary.openUploadWidget(
            {
              ...cloudinaryOptions(),
              resourceType: 'video',
              clientAllowedFormats: ['video'],
              // @TODO 这个 preset 在视频结尾合并了 klook 品牌动画，需要针对本次 AB test 屏蔽 preset
              uploadPreset: 'shgz81sr',
              ...this.customConf
            },
            (error, result) => {
              if (result.event === 'success') {
                resolve([[result.info || {}], error])
                // this.$message.success(__('global_success'))
              }
            }
          )
      })
      if (error) {
        console.error(error)
        return
      }

      this.uploadCallback && (await this.uploadCallback(result))

      let width = this.width ? `w_${this.width}` : '',
        height = this.height ? `w_${this.height}` : '',
        url = result[0].url,
        params = `/fl_lossy.progressive,q_85,f_auto/c_fill,${width},${height}`

      if (this.watermark) {
        url = result[0].eager[0].url
      }

      url = url.replace(/upload/, `upload${params}`)
      url = url.replace('http', 'https')

      this.$emit('change', url)
      this.$emit('uploaded', url)
    }, 300)
  }
}
</script>

<style lang="scss" scoped>
$hoverColor: #40a9ff;
.video-container {
  width: 100%;
  padding: 12px 0;
  box-sizing: border-box;
  background-color: #fff;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  max-width: 360px;
  height: 180px;
  text-align: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  &:hover {
    border-color: $hoverColor;
    &.video-upload-btn {
      color: $hoverColor;
    }
  }
}

.video-upload-clear {
  position: absolute;
  top: 0;
  right: 12px;
  font-weight: 200;
  font-size: 16px;
  cursor: pointer;
  z-index: 9999;
  &:hover {
    opacity: 0.8;
    color: #ff4949;
  }
}

.video-content {
  height: 100%;
  padding: 0 30px;
  box-sizing: border-box;
}

.video-upload-ele {
  max-width: 100%;
  max-height: 250px;
  height: 100%;
}

.video-upload-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  &:before {
    content: '\e77b';
  }
}

.input-video-url {
  max-width: 360px;
}

label.position-right-clear {
  right: -20px;
  top: 0px;
  line-height: 1;
}
</style>
