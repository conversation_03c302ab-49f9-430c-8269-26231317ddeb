<template>
  <ChooseCategory
    ref="chooseCategory"
    v-model="visible"
    target="_self"
    :mask-closable="false"
    @cancel="onCancel"
    @cancel-choose="onCancel"
  />
</template>

<script>
import ChooseCategory from './index'

export default {
  name: 'DraftMerchantChoose',
  components: {
    ChooseCategory
  },
  data() {
    return {
      visible: true
    }
  },
  methods: {
    onCancel() {
      window.open('/', '_self')
    }
  }
}
</script>
