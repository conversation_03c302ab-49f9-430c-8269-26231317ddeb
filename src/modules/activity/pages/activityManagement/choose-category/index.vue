<template>
  <div class="choose-category-container">
    <ActTerms v-model="_visible" :mask-closable="maskClosable" v-on="$listeners" @ok="onOk" />

    <CreateNewModal
      v-if="isNewVersion"
      :visible="modalData.visible"
      @close="modalData.visible = false"
      @submit="handleSubmit"
    />

    <CreateModal
      v-else
      :target="target"
      :mask-closable="maskClosable"
      :modal-data="modalData"
      v-on="$listeners"
    />
  </div>
</template>

<script>
import ActTerms from '@activity/pages/activityManagement/act-terms/index.vue'
import CreateModal from '@activity/pages/activityManagement/create/modal.vue'
import CreateNewModal from '@activity/pages/activityManagement/create/create-new-modal/index.vue'
import { mapActions } from 'vuex'

export default {
  name: 'ChooseCategory',
  components: {
    ActTerms,
    CreateModal,
    CreateNewModal
  },
  model: {
    prop: 'visible',
    event: 'change'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    content: {
      type: String,
      default: __('27716')
    },
    target: {
      type: String,
      default: '_blank'
    },
    maskClosable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      modalData: {
        isGuideMode: true,
        visible: false
      },
      categoryArr: []
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('change', val)
      }
    },
    isNewVersion() {
      return this.categoryArr.some((item) => item.version === 'v2')
    }
  },
  async created() {
    this.categoryArr = (await this.getActPublishedCategoryInfo2action()) || []
  },
  methods: {
    ...mapActions(['getActPublishedCategoryInfo2action']),
    handleSubmit(data) {
      const { href } = this.$router.resolve({
        path: `/act/create/${data.subCategory}`,
        params: data
      })
      setTimeout(() => {
        klook.newOpenWin(href, this.target)
      }, 60)

      this.modalData.visible = false
    },
    onOk() {
      this.modalData.visible = true
    }
  }
}
</script>
