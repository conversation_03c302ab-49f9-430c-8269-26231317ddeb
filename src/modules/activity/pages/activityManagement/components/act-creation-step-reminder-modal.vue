<template>
  <a-modal
    v-model="_visible"
    :title="$t('174934')"
    :width="width"
    :cancel-button-props="cancelButtonProps"
    :ok-text="$t('global.user_guide_got_it')"
    @ok="handleOk"
  >
    <StepDiagram class="act-creation-step-reminder" />
  </a-modal>
</template>

<script>
import StepDiagram from '@activity/pages/activityManagement/components/step-diagram.vue'

export default {
  name: 'ActCreationStepReminderModal',
  components: {
    StepDiagram
  },
  model: {
    prop: 'visible',
    event: 'change'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      width: 1080
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(visible) {
        this.$emit('change', visible)
      }
    },
    cancelButtonProps() {
      return {
        style: {
          display: 'none'
        }
      }
    }
  },
  methods: {
    handleOk() {
      this._visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.act-creation-step-reminder {
  height: 268px;
}
</style>
