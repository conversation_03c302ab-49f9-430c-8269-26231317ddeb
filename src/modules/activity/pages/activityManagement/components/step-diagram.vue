<template>
  <div class="step-diagram-wrap">
    <template v-for="(step, i) of stepDiagramList">
      <div :key="i" class="step-diagram-item">
        <div class="step-diagram-item__img" :style="{ backgroundImage: `url(${step.url})` }" />
        <div class="step-diagram-item__title">{{ step.title }}</div>
        <div class="step-diagram-item__desc">{{ step.desc }}</div>
        <div class="step-diagram-item__index">{{ i + 1 }}</div>
      </div>
      <svg-icon
        v-if="!step.hideIcon"
        :key="i + '_icon'"
        class="step-diagram-item__icon"
        icon-name="arrowRight"
      />
    </template>
  </div>
</template>

<script>
export default {
  name: 'StepDiagram',
  data() {
    return {
      stepDiagramList: [
        {
          url: 'https://res.klook.com/image/upload/fl_lossy.progressive,q_auto/q_80,c_fill,w_400/activities/x1so1dnmhtamei3vxb7c.jpg',
          title: __('174916'),
          desc: __('174917')
        },
        {
          url: 'https://res.klook.com/image/upload/fl_lossy.progressive,q_auto/q_80,c_fill,w_400/activities/wf2h4bcvcf8zt6tlati8.jpg',
          title: __('174918'),
          desc: __('174919')
        },
        {
          url: 'https://res.klook.com/image/upload/fl_lossy.progressive,q_auto/q_80,c_fill,w_400/activities/lntfz7vxvlnjoqisuyr2.jpg',
          title: __('174920'),
          desc: __('174921')
        },
        {
          url: 'https://res.klook.com/image/upload/fl_lossy.progressive,q_auto/q_80,c_fill,ar_4:3,w_400/activities/eimdmxbxek6uoohtq6kv.jpg',
          title: __('174922'),
          desc: __('174923'),
          hideIcon: true
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.step-diagram-wrap {
  display: flex;
  gap: 24px;
  align-items: center;

  .step-diagram-item {
    position: relative;
    display: inline-flex;
    flex-direction: column;
    align-items: flex-start;
    flex: 1;
    height: 100%;
    padding: 16px;
    line-height: 21px;
    border: 1px solid #f0f0f0;
    text-align: left;
    color: rgba(0, 0, 0, 0.85);

    &__icon {
      font-size: 24px;
      width: 24px;
    }

    &__index {
      position: absolute;
      top: 12px;
      right: 16px;
      width: 24px;
      height: 24px;
      background-color: #f0f7ff;
      border-radius: 50%;
      text-align: center;
      line-height: 24px;
      color: #437dff;
    }

    &__img {
      width: 80px;
      height: 80px;
      flex: 0 0 80px;
      background-origin: center;
      background-repeat: no-repeat;
      background-size: 100%;
    }

    &__title {
      margin: 16px 0 8px;
      font-weight: 600;
    }
  }
}
</style>
