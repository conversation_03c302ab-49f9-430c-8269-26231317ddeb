<template>
  <div class="cate-info">
    <div class="cate-info-box">
      <ul class="nav-box">
        <li>
          <span class="nav-title">{{ $t('category') }}</span
          ><span class="nav-value">{{ valObj.category }}</span>
        </li>
        <li class="arrow-gt">&gt;</li>
        <li>
          <span class="nav-title">{{ $t('price_engine_sub-category') }}</span
          ><span class="nav-value">{{ valObj.subCategory }}</span>
        </li>
        <li class="arrow-gt">&gt;</li>
        <li>
          <span class="nav-title">{{ $t('actCreate.leafCategory') }}</span
          ><span class="nav-value text">{{ valObj.leafCategory }}</span>
        </li>
      </ul>
      <ul class="info-box">
        <li class="note-text">note: {{ data.note }}</li>
        <li class="category-flow">{{ $t('act_tips_check_same_category') }}</li>
        <li v-show="!infos[data.id]" class="ta-center"><a-spin /></li>
        <li v-for="info in infos[data.id] || []" :key="info.activity_id">
          <span>{{ `${info.activity_id} - ${info.activity_title}` }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      ajaxData: {
        params: {
          category_id: '', // number
          rank: 5 // number
        }
      },
      valObj: {
        category: '',
        subCategory: '',
        leafCategory: ''
      },
      infos: {},
      queryArr: new Set()
    }
  },
  watch: {
    data: {
      handler({ id, parent_flow = [] }) {
        this.getCateInfo(id)
        this.valObj = {
          category: parent_flow[0] || '',
          subCategory: parent_flow[1] || '',
          leafCategory: parent_flow[2] || ''
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    getCateInfo(id) {
      if (!id) return
      if (this.queryArr.has(id)) return
      this.queryArr.add(id)
      let { params } = this.ajaxData
      params.category_id = id
      // console.log('params = ', params, this.infos)
      !this.infos[id] &&
        ajax
          .get({
            url: ADMIN_API.act.get_sell_top_activity,
            params
          })
          .then((data) => {
            this.$set(this.infos, id, data)
            this.$forceUpdate()
          })
    }
  }
}
</script>

<style lang="scss" scoped>
.cate-info {
  min-width: 324px;
  padding: 10px;
  background: #fff;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.09);
  border-radius: 4px;
  .nav-box {
    background: #fafafa;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    padding: 4px;
    li {
      display: inline-block;
      width: 80px;
      line-height: 16px;
      vertical-align: top;
      &.arrow-gt {
        text-align: center;
        width: 28px;
      }
    }
    .nav-title {
      display: block;
    }
    .nav-value {
      display: block;
      color: #000;
      &.text {
        color: #000;
      }
    }
  }
  .info-box {
    color: #000;
    line-height: 16px;
    li {
      margin: 10px 0;
    }
    .note-text {
      color: #ffab00;
    }
    .category-flow {
      color: rgba(0, 0, 0, 0.65);
    }
  }
}
</style>
