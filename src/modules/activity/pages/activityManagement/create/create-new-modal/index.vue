<template>
  <a-modal :visible="visible" width="572px" @cancel="handleCancel">
    <div slot="title">
      {{ $t('actCreate.modalTitle') }}
    </div>
    <a-alert :message="$t('177147')" type="info" show-icon />
    <a-spin :spinning="spinning">
      <div v-show="step === 1" class="modal-body">
        <div class="label">{{ $t('177148') }}</div>
        <div class="cate-options-group">
          <div
            v-for="item in categoryList"
            :key="item.id"
            :class="{ active: category === item.id }"
            class="select-options"
            @click="handleSelect(item.id)"
          >
            <a-radio :checked="category === item.id"></a-radio>
            <div class="options-content">
              <div class="head">
                <div>{{ item.title }}</div>
                <QuestionCircleTooltip :title="item.tip" class="head-icon" />
              </div>
              <div class="content">
                <div>{{ item.content }}</div>
              </div>
              <template v-if="item.showSpa && spaSubCategoryList.length">
                <div v-show="category === item.id" class="sub-options">
                  <div class="head">
                    <div>{{ $t('177152') }}</div>
                    <QuestionCircleTooltip :title="$t('177155')" class="head-icon" />
                  </div>
                  <a-radio-group v-model="isSpa">
                    <a-radio :value="true">
                      {{ $t('28635') }}
                    </a-radio>
                    <a-radio :value="false">
                      {{ $t('28633') }}
                    </a-radio>
                  </a-radio-group>
                </div>
              </template>
            </div>
            <svg-icon class="options-icon" :icon-name="item.icon" />
          </div>
        </div>
      </div>

      <div v-show="step === 2" class="modal-body">
        <div class="label">{{ $t('177156') }}</div>
        <div class="sub-cate-options-group-wrap common-scrollbar">
          <div class="sub-cate-options-group">
            <div
              v-for="item in currentSubCategoryList"
              :key="item.id"
              class="select-options cate-options"
              :class="{ active: subCategory === item.id }"
              @click="handleSelectSubCate(item.id)"
            >
              <a-radio :checked="subCategory === item.id"></a-radio>
              <div class="options-content">{{ item.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
    <div slot="footer">
      <a-button @click="handleCancel">{{ $t('Cancel') }}</a-button>
      <a-button v-show="step === 2" @click="prevStep">{{ $t('Previous step') }}</a-button>
      <a-button type="primary" :disabled="nextDisabled" @click="nextStep">
        {{ $t('Next step') }} ({{ step }}/2)
      </a-button>
    </div>

    <a-modal
      v-if="toursFlowModal.visible"
      v-bind="toursFlowModal"
      :title="$t('196660')"
      :ok-button-props="{
        props: { disabled: calcToursCreatedDisabled },
        attrs: {
          ...$root.bindIHTrack({ type: 'module', spm: 'CreateNewActNewVersion', exposure: false })
        }
      }"
      class="common-modal-style"
      data-spm-page="SPUMigrationChoosing"
      v-on="toursFlowModal.on"
    >
      <ToursCreate
        :api-params="toursData.apiParams"
        :form-data="toursData.params"
        :support-spu-page-conf="supportSpuPageConf"
        @clickOldFlow="clickOldFlowHandler"
      />
    </a-modal>
  </a-modal>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { TOURS_MENU_KEY_DICT } from '~src/modules/aidRevamp/utils/const.js'
import QuestionCircleTooltip from '@activity/components/question-circle-tooltip/index.vue'
import { PAGE_LEVEL_DICT } from '~src/modules/aidRevamp/utils/const.js'
import ToursCreate from '~src/modules/activity/pages/activityManagement/create/tours-create.vue'
import { DEFAULT_SUPPORT_SPU_PAGE_CONF } from '~src/modules/activity/pages/activityManagement/utils/index.js'

export default {
  name: 'CreateNewModal',
  components: {
    QuestionCircleTooltip,
    ToursCreate
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      supportSpuPageConf: DEFAULT_SUPPORT_SPU_PAGE_CONF,
      spinning: false,
      step: 1,
      categoryArr: [],
      category: null,
      subCategory: null,
      isSpa: false,
      // aid
      toursData: {
        apiParams: {},
        params: {
          type: 1,
          activity_id: undefined,
          package_id_list: [],
          activity_inherit_to_spu: true
        }
      },
      toursFlowModal: {
        width: '850px',
        visible: false,
        okText: this.$t('global_confirm'),
        cancelText: this.$t('global_cancel'),
        maskClosable: false,
        on: {
          ok: async () => {
            const data = this.getSpuCreatedParams()
            const result = await ajax.post(
              ADMIN_API.aidRevamp.postSpuCreate,
              {
                noDefaultResponseInterceptor: true,
                data
              },
              ajax.sucOptions
            )

            let source_language = ''
            if (data.inherit_from_activity_id) {
              source_language = (
                await ajax.get(ADMIN_API.act.get_activity_category_id, {
                  params: {
                    activity_id: +data.inherit_from_activity_id
                  }
                })
              ).source_language
            }

            if (result?.activity_id) {
              this.$router.push({
                name: TOURS_MENU_KEY_DICT.basic.page,
                params: {
                  id: result.activity_id
                },
                query: {
                  lang: source_language || '',
                  package_id: result.spu_id
                }
              })
              return
            } else if (result?.error?.debug_message) {
              this.$message.error(result.error.debug_message)
            }
          },
          cancel: () => {
            const { toursFlowModal: tfm, toursData } = this
            toursData.params.activity_id = undefined
            toursData.params.package_id_list = []
            tfm.visible = false
          }
        }
      },
      categoryList: [
        {
          id: 2,
          title: 'Tours',
          content: this.$t('177150'),
          tip: this.$t('177153'),
          icon: 'create-icon-tour'
        },
        {
          id: 15,
          title: 'Activity & Experiences',
          content: this.$t('177151'),
          tip: this.$t('177154'),
          icon: 'create-icon-exp',
          showSpa: true
        }
      ]
    }
  },
  computed: {
    ...mapGetters(['spaCategoryIds']),
    calcToursCreatedDisabled() {
      const { params } = this.toursData

      if (params.type === 2) {
        return false
      }

      const hasPkg = Array.isArray(params.package_id_list)
        ? params.package_id_list.length
        : params.package_id_list
      return (params.type === 1 && !params.activity_id) || !hasPkg
    },
    nextDisabled() {
      const { step, category, subCategory } = this
      if (step === 1) {
        return !category
      } else {
        return !subCategory
      }
    },
    allSubCategoryList() {
      const ids = [2, 15, ...this.spaCategoryIds]
      return this.categoryArr.filter((item) => ids.includes(item.id))
    },
    spaSubCategoryList() {
      const ids = this.spaCategoryIds
      return this.allSubCategoryList.filter((item) => ids.includes(item.id))
    },
    currentSubCategoryList() {
      const { category, isSpa } = this
      let ids = [category]
      if (isSpa && ids.includes(15)) {
        ids = this.spaCategoryIds
      }
      return this.allSubCategoryList.reduce((acc, item) => {
        if (ids.includes(item.id)) {
          const subcategory_list = item.subcategory_list || []
          acc = [...acc, ...subcategory_list]
        }
        return acc
      }, [])
    }
  },
  created() {
    this.getPublishedCategoryInfo()
    this.initSupportSpuPageConf()
  },
  methods: {
    ...mapActions(['getActPublishedCategoryInfo2action']),
    async initSupportSpuPageConf() {
      const resp = await ajax.get(ADMIN_API.act.check_support_spu_page)
      this.$set(this, 'supportSpuPageConf', {
        ...DEFAULT_SUPPORT_SPU_PAGE_CONF,
        ...(resp || {})
      })
    },
    handleSelectSubCate(value) {
      this.subCategory = value
    },
    handleSelect(value) {
      this.category = value
    },
    prevStep() {
      this.step = 1
      this.subCategory = null
    },
    getSpuCreatedParams() {
      const { leaf_category_id } = this.toursData.apiParams
      const { type, activity_id, package_id_list, activity_inherit_to_spu } = this.toursData.params
      const obj = {
        leaf_category_id,
        inherit_from_activity_id: type === 1 ? activity_id : undefined, // 选填
        inherit_from_package_id_list:
          type === 1 ? (Array.isArray(package_id_list) ? package_id_list : [package_id_list]) : undefined, // 选填
        page_type:
          this.toursData.params.type === 1 // 关联旧活动
            ? [PAGE_LEVEL_DICT.act, PAGE_LEVEL_DICT.spu]
            : [PAGE_LEVEL_DICT.spu],
        activity_inherit_to_spu
      }
      return obj
    },
    clickOldFlowHandler() {
      this.toursFlowModal.visible = false // 关闭新旧版本分流弹窗
      const { category, subCategory } = this
      this.$emit('submit', { category, subCategory })
      this.reset()
    },
    nextStep() {
      const { step } = this
      if (step === 1) {
        this.step = 2
        return
      }
      const { category, subCategory } = this
      if (category === 2 && this.supportSpuPageConf.support_spu_page) {
        this.toursData.apiParams = {
          leaf_category_id: subCategory,
          sub_category_id: category
        }
        this.toursFlowModal.visible = true
        return
      }
      this.$emit('submit', { category, subCategory })
      this.reset()
    },
    handleCancel() {
      this.reset()
      this.$emit('close')
    },
    reset() {
      this.category = null
      this.subCategory = null
      this.isSpa = false
      this.step = 1
    },
    async getPublishedCategoryInfo() {
      if (!this.categoryArr.length) {
        this.spinning = true
        this.categoryArr = await this.getActPublishedCategoryInfo2action()
        this.spinning = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .ant-modal-body {
    max-height: 95vh;
  }
}

.label {
  padding: 10px 0;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
}

.cate-options-group {
  max-height: calc(85vh - 200px);
}

.sub-cate-options-group-wrap {
  min-height: 300px;
  max-height: calc(70vh - 200px);
  overflow-y: scroll;
}

.sub-cate-options-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.select-options {
  padding: 12px;
  border-radius: 8px;
  border: 1px solid rgba(240, 240, 240, 1);
  display: flex;
  margin-bottom: 10px;
  cursor: pointer;

  &.cate-options {
    width: 257px;

    .options-content {
      padding: 0 0 0 10px;
    }
  }

  .options-icon {
    width: 80px;
    height: 80px;
  }

  .options-content {
    flex: 1;
    padding: 0 10px;
    font-size: 16px;

    .sub-options {
      margin-top: 8px;
      padding: 8px;
      background: rgba(245, 245, 245, 1);
      border-radius: 4px;

      .sub-radio {
        margin-right: 16px;
      }

      .head {
        font-weight: 400;
        font-size: 14px;
        margin-bottom: 4px;
        color: #000;
      }
    }

    .head {
      font-weight: 700;
      display: flex;
      align-items: center;
      color: rgba(0, 0, 0, 0.85);

      .head-icon {
        color: rgba(0, 0, 0, 0.45);
        margin-left: 4px;
      }
    }

    .content {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
    }
  }

  &.active {
    border: 1px solid rgba(67, 125, 255, 1);
  }
}
</style>
