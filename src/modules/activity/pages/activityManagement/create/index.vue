<template>
  <div class="act-create">
    <div class="act-create-box">
      <a-popover overlay-class-name="common-popover-style hide-arrow">
        <div v-show="cateInfo.visible" class="cate-info-wrap" :style="cateInfo.mouseObj">
          <CateInfo :data="cateInfo.item" />
        </div>
        <div v-if="!editLeafId" class="recent-used-box">
          <div class="search-box">
            <a-input-search
              v-model="leafObj.searchVal"
              allow-clear
              :placeholder="$t('act_search_keywords')"
              class="search-style"
            />
          </div>
          <template v-if="recentUsedArr.length">
            <p class="text">{{ $t('act_recent_used') }}：</p>
            <a-radio-group
              v-model="reqData.params.leaf_category_id"
              @change="handleRecentUsedChange"
              @mouseleave="cateInfo.mouseleave"
            >
              <a-radio
                v-for="(item, idx) in filterRecentUsed"
                :key="idx"
                :data-item="JSON.stringify(item)"
                :value="item.value"
              >
                <span
                  :class="['radio-style', reqData.params.leaf_category_id === item.value ? 'active' : '']"
                  @mouseenter="cateInfo.mouseenter($event, item)"
                  >{{ item.label }}</span
                >
              </a-radio>
            </a-radio-group>
          </template>
        </div>
        <div class="sub-category">
          <div class="title">
            {{ $t('actCreate.subLeafCategory') }}
          </div>
          <div class="group-box common-scrollbar">
            <a-radio-group v-model="reqData.params.leaf_category_id" @mouseleave="cateInfo.mouseleave">
              <a-collapse v-model="subObj.activeKeys" :bordered="false">
                <a-collapse-panel
                  v-for="(sub, i) in filterRadios.filter(
                    (i) => !provideGlobalData.lockSubCategoryId.includes(i.value)
                  )"
                  :key="'' + i"
                >
                  <div slot="header" class="panel-item-header">
                    {{ `${sub.id} - ${sub.name}` }}
                    <span v-if="sub.tip" class="panel-item-header--tip">{{ sub.tip }}</span>
                  </div>

                  <div class="radio-box">
                    <a-radio
                      v-for="item in sub.subcategory_list"
                      :key="item.value"
                      :value="item.value"
                      @focus="leafObj.handleFocus($event, item)"
                    >
                      <span @mouseenter="cateInfo.mouseenter($event, item)">{{ item.label }}</span>
                    </a-radio>
                  </div>
                </a-collapse-panel>
              </a-collapse>
            </a-radio-group>
          </div>
        </div>
        <div v-if="showBtns" class="fixed-btns">
          <div class="fixed-btns-box ta-right">
            <a-button @click="cancel">{{ $t('global_cancel') }}</a-button
            ><a-button type="primary" @click="save">{{ $t('global_button_save') }}</a-button>
          </div>
        </div>
      </a-popover>
    </div>
  </div>
</template>

<script>
const isProd = process.env.NODE_ENV === 'production'
export const ATT_SUB_CATEGORY_ID = !isProd ? [1, 416] : [1, 415]

import { mapActions } from 'vuex'
import { getType, setLocalStorage, getLocalStorage } from '@activity/utils'
import CateInfo from './cateInfo'
import { checkAuth } from '~src/plugins/authDirective.js'
import { isMerchant } from '@/env'
import { DEFAULT_SUPPORT_SPU_PAGE_CONF } from '~src/modules/activity/pages/activityManagement/utils/index.js'

export default {
  name: 'ActManagementCreate',
  components: { CateInfo },
  inject: {
    provideGlobalData: {
      default: {
        lockSubCategoryId: []
      }
    },
    // aid 需要走新接口
    isAid: { default: false }
  },
  props: {
    categoryInfo: {
      default: () => {}
    },
    supportSpuPageConf: {
      type: Object,
      default: () => DEFAULT_SUPPORT_SPU_PAGE_CONF
    }
  },
  data() {
    return {
      activity_id: +this.$route.params.id,
      cateInfo: {
        mouseObj: {
          left: 0,
          top: 0
        },
        visible: false,
        item: {},
        mouseenter: (e, item) => {
          clearTimeout(this._timer)
          this._timer = setTimeout(() => {
            let { cateInfo } = this
            if (cateInfo.item.id !== item.id) {
              cateInfo.mouseObj.left = e.x + 40 + 'px'
              cateInfo.mouseObj.top = e.y + 'px'
              cateInfo.item = item
            }
            cateInfo.visible = true
          }, 800)
        },
        mouseleave: () => {
          clearTimeout(this._timer)
          let { cateInfo } = this
          cateInfo.visible = false
        },
        getPopupContainer: () => {
          return document.querySelector('.act-create-box')
        }
      },
      recentUsedKey: 'chooseCategory.recentUsedObj', //localStorage
      recentUsedArr: [],
      showBtns: false,
      reqData: {
        params: {
          leaf_category_id: undefined, // number,
          sub_category_id: undefined // number,
        }
      },
      subObj: {
        activeKeys: [],
        options: [],
        filterOption: (input, option) => {
          return (
            option.data.props &&
            option.data.props.name &&
            option.data.props.name.toLowerCase().indexOf(input) >= 0
          )
        },
        handleChange: (value) => {
          this.recentUsedArr = []
          let list = this.subObj.options.filter((item) => item.value === value)[0].subcategory_list
          this.leafObj.options = list
        }
      },
      leafObj: {
        searchVal: '',
        options: [],
        handleFocus: (e, item) => {
          this.leafObj.item = item
          let params = this.reqData.params
          params.leaf_category_id = item.value
          params.sub_category_id = item.parent_id
        },
        item: {}
      }
    }
  },

  computed: {
    editLeafId() {
      let id = this.categoryInfo?.category?.leaf_category_id || this.categoryInfo?.leaf_category_id
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      id && this.$set(this.reqData.params, 'leaf_category_id', id)
      return id
    },

    filterRecentUsed() {
      let { leafObj } = this
      let arr = []
      this.recentUsedArr.forEach((item) => {
        arr.push(...item.subcategory_list)
      })
      return leafObj.searchVal
        ? arr.filter((item) => item.label.toLowerCase().indexOf(leafObj.searchVal) >= 0)
        : arr
    },
    filterRadios() {
      let { subObj, leafObj } = this
      let editOptions = this.editLeafId
        ? subObj.options.filter((item) => item.id === this.categoryInfo.sub_category_id)
        : subObj.options

      // 在aidRevamp模块中，只显示sub_category_id为1和415的数据
      if (this.isAid) {
        editOptions = editOptions.filter((item) => ATT_SUB_CATEGORY_ID.includes(item.id))
      }
      if (!leafObj.searchVal) return editOptions || []
      let copyOptions = _.merge([], editOptions)
      let arr = copyOptions.filter((item) => {
        if (item.subcategory_list) {
          item.subcategory_list = item.subcategory_list.filter((leaf) => {
            return leaf.label.toLowerCase().indexOf(leafObj.searchVal) >= 0
          })
          return !!item.subcategory_list.length
        }
        return false
      })

      const activeKeys = Object.keys(arr).filter((key) => !arr[key]?.is_collapsed)
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.$set(subObj, 'activeKeys', activeKeys)

      return arr
    }
  },
  watch: {
    'reqData.params': {
      handler(obj) {
        this.$emit('change', obj)
      },
      deep: true
    },
    categoryInfo: {
      handler(obj) {
        this.reqData.params.leaf_category_id = obj.leaf_category_id
      },
      deep: true
    },
    '$attrs.data': {
      handler() {
        this.subObj.options = this.$attrs.data
        this.subObj.activeKeys = Object.keys(this.$attrs.data).filter(
          (key) => !this.$attrs.data[key]?.is_collapsed
        )
      },
      immediate: true
    }
  },
  created() {
    this.initRecentUsed()
  },
  methods: {
    ...mapActions(['updateActItemData2actions']),
    // 重置操作
    resetParams() {
      let params = this.reqData.params
      params.leaf_category_id = this.editLeafId || undefined
      params.sub_category_id = undefined
      this.leafObj.searchVal = ''
      this.leafObj.options = []
      return true
    },
    // 选择 最近使用 category
    handleRecentUsedChange(e) {
      let params = this.reqData.params
      let target = e.nativeEvent.target
      let item = JSON.parse(target.labels[0].getAttribute('data-item'))
      params.sub_category_id = item.parent_id
      params.leaf_category_id = item.id
    },

    // 点击 recent used 按钮
    initRecentUsed() {
      let obj = getLocalStorage(this.recentUsedKey) || {}
      this.recentUsedArr = obj.dataArr || []
    },
    // 校验参数
    checkParams() {
      let params = this.reqData.params
      if (!params.leaf_category_id) {
        this.$message.warning(__('global_please_select'))
        return false
      }
      return params.leaf_category_id
    },

    getCategory2parmas(params) {
      let list = this.subObj.options
      let arr = []
      list.forEach((item) => {
        if (item.value === params.sub_category_id) {
          let cp = _.merge({}, item)
          if (!arr.filter((obj) => obj.value === params.sub_category_id).length) {
            cp.subcategory_list = []
            arr.push(cp)
          }
          let leafArr = item.subcategory_list.filter((obj) => {
            return obj.value === params.leaf_category_id
          })
          if (leafArr.length) {
            if (!cp.subcategory_list.filter((obj) => obj.value === params.leaf_category_id).length) {
              cp.subcategory_list.push(...leafArr)
            }
          }
        }
      })
      return arr
    },
    setCategory(key, params = {}) {
      let obj =
        getType(getLocalStorage(key)) === 'Object'
          ? getLocalStorage(key)
          : {
              paramsArr: [],
              dataArr: []
            }
      let arr = obj.paramsArr.filter((item, i) => {
        for (let k in item) {
          if (item[k] !== params[k]) return false
        }
        obj.paramsArr.splice(i, 1) //存在则删除 -> 移动到最新
        obj.dataArr.splice(i, 1)
        return true
      })
      if (!arr.length) {
        if (obj.paramsArr.length >= 10) {
          obj.paramsArr.pop()
          obj.dataArr.pop()
        }
      }
      //移动到最新
      obj.paramsArr.unshift(params)
      obj.dataArr.unshift(...this.getCategory2parmas(params))
      setLocalStorage(key, obj)
      return obj
    },
    // 取消 操作
    cancel(cb) {
      this.resetParams()
      cb && cb()
    },
    // isGuideMode 保存 操作
    async save2guide(cb) {
      let { reqData } = this
      if (this.checkParams()) {
        const data = {
          leaf_category_id: reqData.params.leaf_category_id
        }
        cb && cb(data)
      }
    },
    // 保存 操作
    async save(cb, options) {
      let { recentUsedKey, reqData, editLeafId } = this
      if (this.isAid) {
        this.aidSaveMethod()
        return
      }
      if (this.checkParams()) {
        if (editLeafId) {
          if (editLeafId === reqData.params.leaf_category_id) {
            cb && cb(false)
            return false
          }
          let data = await ajax.postBody(
            ADMIN_API.act.save_act_leaf_category_id,
            {
              data: {
                activity_id: this.activity_id,
                leaf_category_id: reqData.params.leaf_category_id
              }
            },
            ajax.sucOptions
          )
          if (data.success) {
            this.updateActItemData2actions({
              activity_id: this.activity_id,
              refresh: true
            })
            this.setCategory(recentUsedKey, reqData.params)
            this.resetParams()
            klook.bus.$emit('initQueryData2bus')
          }
          cb && cb(false)
        } else {
          if (
            (checkAuth('spuCreated') || (isMerchant && this.supportSpuPageConf.support_spu_page)) &&
            !options?.isFouceCreate &&
            [2].includes(reqData.params.sub_category_id)
          ) {
            // tours创建：新老版本分流
            this.$emit('customNewSave', reqData.params)
            return false
          }

          if (isMerchant) {
            this.$emit('clickOldFlow')
            return
          }

          let data = await ajax.post(
            ADMIN_API.act.create_activity,
            {
              data: {
                leaf_category_id: reqData.params.leaf_category_id
              }
            },
            ajax.sucOptions
          )
          if (data) {
            this.setCategory(recentUsedKey, reqData.params)
            this.resetParams()
          }
          cb && cb(data)
        }
      }
    },
    // aidRevamp 新接口请求逻辑
    async aidSaveMethod() {
      let { reqData } = this
      if (this.checkParams()) {
        const data = {
          leaf_category_id: reqData.params.leaf_category_id
        }
        // 新接口请求逻辑
        this.$emit('setLoading', true)
        let response = await ajax.post(ADMIN_API.act.save_aid_leaf_category_id, { data }, ajax.sucOptions)
        if (response) {
          klook.bus.$emit('aidCreateSuc', response)
        }
        this.$emit('setLoading', false)
      }
    }
  }
}
</script>
<style lang="scss">
.act-create {
  .ant-collapse-content-box {
    padding: 0 !important;
  }
  .recent-used-box {
    .ant-radio {
      display: none;
    }
  }
}
</style>

<style lang="scss" scoped>
.act-create {
  // width: 720px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  background-color: #fff;
  .search-style {
    width: 400px;
    margin-bottom: 12px;
  }
  .title {
    line-height: 20px;
    margin: 12px 0;
    font-size: 16px;
  }

  ::v-deep .ant-collapse-header {
    .panel-item-header {
      position: relative;

      &--tip {
        display: inline-block;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        padding: 4px 6px;
        margin-left: 12px;
        background-color: rgb(255, 52, 52);
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(255, 52, 52, 0.15);
        color: #fff;

        &::before {
          display: block;
          position: absolute;
          width: 6px;
          height: 6px;
          left: -3px;
          top: 50%;
          transform: translateY(-50%) rotate(45deg);
          background-color: rgb(255, 52, 52);
        }
      }
    }
  }

  .radio-box {
    padding: 12px 24px;
    background-color: #fff;
  }
  .sub-category {
    padding: 0 24px;
    .ant-select {
      width: 300px;
    }
    .ant-input-search {
      width: 300px;
    }
    .ant-radio-group {
      width: 100%;
      font-weight: 500;
    }
    .ant-radio-wrapper {
      width: calc(33.3% - 8px);
      box-sizing: border-box;
      color: rgba(0, 0, 0, 0.85);
      font-size: 14px;
      line-height: 26px;
      overflow: hidden;
    }
  }
  .search-box {
    display: flex;
    align-items: center;
    button {
      margin-left: 12px;
      background: rgba(0, 145, 255, 0.05);
    }
  }
  .group-box {
    position: relative;
    min-height: 220px;
    max-height: 440px;
    overflow: hidden;
    overflow-y: auto;
    padding-bottom: 128px;
  }
  .fixed-btns-box {
    padding: 24px;
    button:not(:last-child) {
      margin-right: 12px;
    }
  }
  .cate-info-wrap {
    width: 325px;
    position: fixed;
    z-index: 9999;
    top: 4px;
    right: 4px;
  }
  .recent-used-box {
    padding: 12px 24px 0 24px;
    background-color: #fafafa;
    .text {
      color: rgba(0, 0, 0, 0.65);
      margin: 0;
    }
    .ant-radio-group {
      padding: 12px 0;
    }
    .ant-radio-wrapper {
      overflow: hidden;
      .radio-style {
        display: inline-block;
        box-sizing: border-box;
        border-radius: 2px;
        color: rgba(0, 0, 0, 0.65);
        border: 1px solid rgba(0, 0, 0, 0.25);
        font-size: 12px;
        line-height: 20px;
        padding: 0 4px;
        &.active {
          color: #0091ff;
          border: 1px solid #0091ff;
        }
      }
    }
  }
}
</style>
