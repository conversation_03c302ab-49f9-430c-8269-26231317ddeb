<template>
  <div v-if="display" class="create-modal">
    <div class="create-modal-box">
      <a-modal
        class="common-modal-style no-scroll"
        v-bind="modalProps"
        :mask-closable="maskClosable"
        v-on="modalProps.on"
        :confirmLoading="loading"
        :body-style="{ paddingBottom: '80px' }"
        @cancel="handleCancel"
      >
        <div slot="title">
          {{ $t('actCreate.modalTitle') }}
        </div>
        <a-spin :spinning="spinning">
          <createContent
            :categoryInfo="categoryInfo"
            ref="createContent"
            :data="categoryArr"
            :support-spu-page-conf="supportSpuPageConf"
            @customNewSave="customNewSaveHandler"
            @change="handleChange"
            @setLoading="setLoading"
            @clickOldFlow="clickOldFlowHandler"
          />
        </a-spin>
      </a-modal>
      <a-modal
        v-if="toursFlowModal.visible"
        v-bind="toursFlowModal"
        :title="$t('196660')"
        :okButtonProps="{
          props: { disabled: calcToursCreatedDisabled },
          attrs: {
            ...$root.bindIHTrack({ type: 'module', spm: 'CreateNewActNewVersion', exposure: false })
          }
        }"
        class="common-modal-style"
        data-spm-page="SPUMigrationChoosing"
        v-on="toursFlowModal.on"
      >
        <ToursCreate
          :apiParams="toursData.apiParams"
          :formData="toursData.params"
          :support-spu-page-conf="supportSpuPageConf"
          @clickOldFlow="clickOldFlowHandler"
        />
      </a-modal>
    </div>
  </div>
</template>

<script>
// import { checkAuth } from '@/plugins/authDirective'
import createContent from './index'
import ToursCreate from '~src/modules/activity/pages/activityManagement/create/tours-create.vue'
import { mapActions } from 'vuex'
import { PAGE_LEVEL_DICT } from '~src/modules/aidRevamp/utils/const.js'
import { isMerchant } from '@/env'
import { DEFAULT_SUPPORT_SPU_PAGE_CONF } from '~src/modules/activity/pages/activityManagement/utils/index.js'

export default {
  name: 'CreateModal',
  components: {
    ToursCreate,
    createContent
  },
  props: {
    categoryInfo: {
      default: () => {}
    },
    modalData: {
      type: Object,
      default: () => ({})
    },
    target: {
      type: String,
      default: '_blank'
    },
    maskClosable: {
      type: Boolean,
      default: false
    }
  },
  inject: {
    // aid 需要走新接口
    isAid: { default: false }
  },
  data() {
    return {
      supportSpuPageConf: DEFAULT_SUPPORT_SPU_PAGE_CONF,
      loading: false,
      toursData: {
        apiParams: {},
        params: {
          type: undefined,
          activity_id: undefined,
          package_id_list: [],
          activity_inherit_to_spu: true
        }
      },
      toursFlowModal: {
        width: '850px',
        visible: false,
        okText: this.$t('global_confirm'),
        cancelText: this.$t('global_cancel'),
        maskClosable: false,
        on: {
          ok: async () => {
            const data = this.getSpuCreatedParams()
            const result = await ajax.post(
              ADMIN_API.aidRevamp.postSpuCreate,
              {
                noDefaultResponseInterceptor: true,
                data
              },
              ajax.sucOptions
            )

            let source_language = ''
            if (data.inherit_from_activity_id) {
              source_language = (
                await ajax.get(ADMIN_API.act.get_activity_category_id, {
                  params: {
                    activity_id: +data.inherit_from_activity_id
                  }
                })
              ).source_language
            }

            if (result?.activity_id) {
              this.$router.push({
                path: `/aid/tours/basic/${result.activity_id}`,
                query: {
                  lang: source_language || '',
                  package_id: result.spu_id
                }
              })
              return
            } else if (result?.error?.debug_message) {
              this.$message.error(result.error.debug_message)
            }
          },
          cancel: () => {
            const { toursFlowModal: tfm, toursData } = this
            toursData.params.activity_id = undefined
            toursData.params.package_id_list = []
            tfm.visible = false
          }
        }
      },
      spinning: false,
      modalProps: {
        width: '850px',
        visible: false,
        okText: this.$t('global_button_save'),
        cancelText: this.$t('global_cancel'),
        okButtonProps: {
          props: {
            disabled: true
          }
        },
        on: {
          cancel: () => {
            let { modalData: md } = this
            this.$refs.createContent.cancel(() => {
              md.visible = false
            })
          },
          ok: (options) => {
            this.$tracker.track('action', '#save-new-activity-track')
            // let { modalData: md } = this
            if (isMerchant && options.isFouceCreate) {
              this.$refs.createContent.save2guide(async (data) => {
                if (data && data.leaf_category_id) {
                  const { href } = this.$router.resolve({
                    path: `/act/create/${data.leaf_category_id}`,
                    params: data
                  })
                  setTimeout(() => {
                    klook.newOpenWin(href, this.target)
                    // window.open(href)
                  }, 60)
                }
                // eslint-disable-next-line vue/no-mutating-props
                this.modalData.visible = false
              })
              return
            }

            this.$refs.createContent.save(async (data) => {
              if (data && data.activity_id) {
                await this.updateActStepStatus2action({
                  activity_id: +data.activity_id,
                  status: 1,
                  step: 'choose_category',
                  language: 'ALL',
                  loading: true
                })
                const { href } = this.$router.resolve({
                  path: `/act/activity/basic/${data.activity_id}`,
                  params: data
                })
                setTimeout(() => {
                  klook.newOpenWin(href, this.target)
                }, 60)
              }
              // eslint-disable-next-line vue/no-mutating-props
              this.modalData.visible = false
            }, options)
          }
        }
      },
      categoryArr: []
    }
  },
  computed: {
    calcToursCreatedDisabled() {
      const { params } = this.toursData

      if (params.type === 2) return false

      const hasPkg = Array.isArray(params.package_id_list)
        ? params.package_id_list.length
        : params.package_id_list
      return (params.type === 1 && !params.activity_id) || !hasPkg
    },
    display() {
      return true
      // return !this.$root.nonEdit2act || checkAuth('draftMerchantActCreate')
    }
  },
  watch: {
    modalData: {
      handler() {
        this.initData()
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.get_published_category_info()
    this.initSupportSpuPageConf()
  },
  methods: {
    ...mapActions(['getActPublishedCategoryInfo2action', 'updateActStepStatus2action']),
    async initSupportSpuPageConf() {
      const resp = await ajax.get(ADMIN_API.act.check_support_spu_page)
      this.$set(this, 'supportSpuPageConf', {
        ...DEFAULT_SUPPORT_SPU_PAGE_CONF,
        ...(resp || {})
      })
    },
    getSpuCreatedParams() {
      const { leaf_category_id } = this.toursData.apiParams
      const { type, activity_id, package_id_list, activity_inherit_to_spu } = this.toursData.params
      const obj = {
        leaf_category_id,
        inherit_from_activity_id: type === 1 ? activity_id : undefined, // 选填
        inherit_from_package_id_list:
          type === 1 ? (Array.isArray(package_id_list) ? package_id_list : [package_id_list]) : undefined, // 选填
        page_type:
          this.toursData.params.type === 1 // 关联旧活动
            ? [PAGE_LEVEL_DICT.act, PAGE_LEVEL_DICT.spu]
            : [PAGE_LEVEL_DICT.spu],
        activity_inherit_to_spu
      }
      return obj
    },
    clickOldFlowHandler() {
      this.toursFlowModal.visible = false // 关闭新旧版本分流弹窗
      this.modalProps.on.ok({ isFouceCreate: true }) // 重新触发save事件
    },
    customNewSaveHandler(params) {
      this.toursData.apiParams = params
      this.toursFlowModal.visible = true
    },
    handleCancel() {
      this.$emit('cancel-choose')
      if (-1 !== location.pathname.indexOf('/act/activity/add')) {
        this.$router.go(-1)
      }
    },
    handleChange(params) {
      this.modalProps.okButtonProps.props.disabled = !(params && params.leaf_category_id)
    },
    initData() {
      _.merge(this.modalProps, this.modalData)
    },
    setLoading(loading) {
      this.loading = loading
    },
    async get_published_category_info() {
      if (!this.categoryArr.length) {
        this.spinning = true
        this.categoryArr = await this.getActPublishedCategoryInfo2action({
          page_type: this.isAid ? 'ATT' : 'ACT'
        })
        this.spinning = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.common-modal-style ::v-deep .ant-collapse-borderless > .ant-collapse-item {
  border: none;
}
</style>
