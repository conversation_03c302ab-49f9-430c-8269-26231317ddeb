<template>
  <div class="tours-create">
    <!-- <div class="tours-create__title-box">
      {{ $t('196660') }}
    </div> -->
    <div class="tours-create__btns-box">
      <CardRadioGroup
        v-model="formData.type"
        :options="calcOptions"
        class="radio-group-style"
      ></CardRadioGroup>
      <TipsContainer>
        <span v-if="formData.type === 1" v-html="$t('197675')"></span>
        <span v-else v-html="$t('210670')"></span>
      </TipsContainer>
    </div>

    <div v-if="formData.type === 1" class="tours-create__aid-pkg">
      <div class="tours-create__item-box">
        <div class="item-title common-required-star-before">{{ $t('196663') }}</div>
        <a-select
          show-search
          :value="formData.activity_id"
          :placeholder="$t('global_please_input')"
          style="width: 100%"
          :default-active-first-option="false"
          :show-arrow="false"
          :filter-option="false"
          :not-found-content="fetching ? 'Loading......' : keyword ? 'No match is found' : null"
          size="large"
          @search="aidSearchHandler"
          @change="aidChangeHandler"
        >
          <a-select-option
            v-for="(item, i) in apiResult ? apiResult.items : []"
            :key="i"
            :value="item.activity_id"
          >
            {{ `${item.activity_id}-${item.activity_name}` }}
          </a-select-option>
        </a-select>
        <a-radio v-model="formData.activity_inherit_to_spu" :style="{ margin: '4px 0' }">{{
          $t('196664')
        }}</a-radio>
        <a-alert v-if="hadBeenInheritSpu" :message="$t('196665')" type="error" :show-icon="true">
          <a-icon slot="icon" type="info-circle" theme="filled" />
        </a-alert>
      </div>
      <div class="tours-create__item-box">
        <div class="item-title common-required-star-before">{{ $t('196666') }}</div>
        <a-select
          v-model="formData.package_id_list"
          size="large"
          mode="multiple"
          :disabled="!formData.activity_id || hadBeenInheritSpu"
          :placeholder="$t('global_please_select')"
          class="pkg-select"
        >
          <a-select-option v-for="(pkg, i) in calcPkgList" :key="i" :value="pkg.package_id">
            {{ getPkgLabel(pkg) }}
          </a-select-option>
        </a-select>
      </div>
    </div>
    <div v-auth="'edit'" class="old-flow-wrap">
      <a-button
        v-bind="$root.bindIHTrack({ type: 'module', spm: 'CreateNewActOldVersion', exposure: false })"
        class="old-flow-btn"
        icon="plus"
        @click="$emit('clickOldFlow')"
      >
        {{ isMerchant ? $t('206119') : $t('196659') }}
      </a-button>
    </div>
  </div>
</template>

<script>
import { isMerchant } from '@/env'
import CardRadioGroup from '~src/modules/aidRevamp/components/common/card-radio-group/index.vue'
import TipsContainer from '@aidRevamp/components/common/tips-container/index.vue'
import { DEFAULT_SUPPORT_SPU_PAGE_CONF } from '~src/modules/activity/pages/activityManagement/utils/index.js'

export default {
  name: 'ToursCreate',
  components: {
    CardRadioGroup,
    TipsContainer
  },
  props: {
    apiParams: {
      type: Object,
      default: () => {}
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    supportSpuPageConf: {
      type: Object,
      default: () => DEFAULT_SUPPORT_SPU_PAGE_CONF
    }
  },
  data() {
    return {
      isMerchant,
      hadBeenInheritSpu: false,
      fetching: false,
      keyword: '',
      cacheKeyword: '',
      cacheApiData: {},
      apiResult: {
        items: [],
        total: 0
      },
      cachePkgData: {}
    }
  },
  computed: {
    calcPkgList() {
      return this.cachePkgData[this.formData.activity_id]?.packages || []
    },
    calcOptions() {
      return [
        { value: 1, label: this.$t('196661'), desc: this.$t('210666') },
        {
          value: 2,
          label: this.$t('196662'),
          desc: this.$t('210667'),
          disabled: !this.supportSpuPageConf.support_create_new_spu
        }
      ]
    }
  },
  methods: {
    aidSearchHandler: _.debounce(async function (keyword) {
      if (!keyword) {
        this.apiResult = this.cacheApiData?.[this.cacheKeyword] || {}
        return
      }
      this.keyword = keyword
      if (this.cacheApiData?.[keyword]) {
        this.apiResult = this.cacheApiData?.[keyword]
        return
      }
      this.fetching = true
      this.apiResult = await this.getActivitiesResult(keyword)
      this.$set(this.cacheApiData, keyword, this.apiResult)
      this.fetching = false
    }, 600),
    async getActivitiesResult(keyword) {
      const { sub_category_id, leaf_category_id } = this.apiParams
      return await ajax.get(ADMIN_API.act.get_immigrant_activity_list, {
        params: {
          keyword,
          leaf_category_id,
          sub_category_id,
          support_inherit_spu: 1,
          limit: 50,
          page: 1
        }
      })
    },
    async aidChangeHandler(val) {
      this.cacheKeyword = this.keyword
      this.formData.activity_id = val
      const result = await this.getPackagesResult(val)
      this.$set(this.cachePkgData, val, result)

      const currItem = (this.apiResult.items || []).find((item) => item.activity_id === val) || {}
      this.hadBeenInheritSpu = currItem?.had_been_inherit_spu || false

      this.$set(this.formData, 'package_id_list', [])
    },
    async getPackagesResult(activity_id) {
      if (this.cachePkgData[activity_id]) {
        return this.cachePkgData[activity_id]
      }
      const result = await ajax.get(ADMIN_API.act.get_immigrant_package_list, {
        params: {
          activity_id,
          language: klook.getBLanguage(),
          page_from: klook.getPlatformRoleKey()
        }
      })
      return result
    },
    getPkgLabel(item) {
      const obj =
        item.package_local?.find((o) => o.language_type === klook.getBLanguage()) || item.package_local?.[0]
      const str = `${item.package_id} - ${obj?.name}`
      return str
    }
  }
}
</script>
<style lang="scss" scoped>
.card-radio-group ::v-deep {
  .card-radio {
    width: 100%;
    .radio-wrapper {
      justify-content: start;
    }
  }
}
.old-flow-wrap {
  margin-top: 20px;
}
.tours-create {
  color: rgba(0, 0, 0, 0.85);
  &__btns-box {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .radio-group-style {
      flex: 1 1 auto;
    }
    .old-flow-btn {
      flex: none;
      margin-left: 24px;
    }
  }
  &__title-box {
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: bold;
  }
  &__aid-pkg {
    margin-top: 16px;
    padding: 16px;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
  }
  &__item-box {
    &:not(:last-of-type) {
      margin-bottom: 12px;
    }
  }
  .item-title {
    margin-bottom: 4px;
    font-weight: bold;
  }
  .pkg-select {
    width: 100%;
  }
}
</style>
