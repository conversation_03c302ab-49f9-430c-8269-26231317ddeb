<template>
  <div class="data-lib"></div>
</template>

<script>
import { isMerchant, isAdmin, platform } from '@/env'
import { checkAuth } from '@/plugins/authDirective'
import { ticketTypeOptions } from '@activity/pages/activityManagement/approveList/utils'
import maps from '@activity/utils/maps.js'

const actColumns2merchant = [
  {
    title: __('activity_id_title'),
    dataIndex: 'activity_id',
    scopedSlots: { customRender: 'activity_id' },
    ellipsis: true,
    fixed: 'left',
    width: 200
  },
  {
    title: __('package_count'),
    dataIndex: 'package_count',
    scopedSlots: { customRender: 'package_count' }
  },
  // {
  //   title: __('merchant_id'),
  //   dataIndex: 'merchant_id',
  //   customRender(merchant_id, row) {
  //     return `${merchant_id}-${row.merchant_name}`
  //   },
  //   width: 250
  // },
  {
    title: __('48219'),
    dataIndex: 'approval_status',
    scopedSlots: { customRender: 'approval_status' },
    width: 180
  },
  {
    title: __('global_activity_status'),
    dataIndex: 'activity_status',
    scopedSlots: { customRender: 'activity_status' }
  },
  {
    title: __('205933'),
    dataIndex: 'migrated_to_spu',
    scopedSlots: { customRender: 'migrated_to_spu' }
  },
  {
    title: __('act_binding_destination_city'),
    dataIndex: 'city_name'
  },
  // {
  //   title: __('category'),
  //   dataIndex: 'category.category_name'
  // },
  {
    title: __('sub_category'),
    dataIndex: 'category.sub_category_name'
  },
  {
    title: __('leaf_category'),
    dataIndex: 'category.leaf_category_name'
  },
  {
    title: __('28246'),
    dataIndex: 'create_show_name'
  },
  {
    title: __('create_time_bj'),
    dataIndex: 'create_time_bj',
    width: 180
  },
  {
    title: __('28235'),
    align: 'center',
    dataIndex: 'activity_language_view',
    scopedSlots: { customRender: 'activity_language_view' },
    width: 160
  },
  {
    title: __('manage'),
    align: 'center',
    dataIndex: 'manage',
    fixed: 'right',
    scopedSlots: { customRender: 'action' },
    width: 150
  }
]

const LANG_TITLE_DICT = lang_conf.getLangObj('B_LANG', 'LANG_TITLE')

let initAct2admin = function () {
  const actColumns2admin = [
    {
      title: __('48226'),
      dataIndex: 'ticket_id',
      fixed: 'left',
      scopedSlots: { customRender: 'ticket_id' },
      width: 120
    },
    {
      title: __('48230'),
      dataIndex: 'ticket_status',
      fixed: 'left',
      scopedSlots: { customRender: 'ticket_status' },
      width: 180
    },
    {
      title: __('48227'),
      dataIndex: 'ticket_type',
      scopedSlots: { customRender: 'ticket_type' },
      width: 200
    },

    {
      title: __('merchant_id'),
      dataIndex: 'merchant_id',
      customRender(merchant_id, row) {
        return `${merchant_id} - ${row.merchant_name}`
      },
      width: 200
    },
    {
      title: __('activity_id_title'),
      dataIndex: 'activity_id',
      scopedSlots: { customRender: 'activity_id' },
      ellipsis: true,
      width: 200
    },
    {
      title: __('global_activity_status'),
      dataIndex: 'activity_status',
      scopedSlots: { customRender: 'activity_status' }
    },
    {
      title: __('205933'),
      dataIndex: 'migrated_to_spu',
      scopedSlots: { customRender: 'migrated_to_spu' }
    },
    {
      title: 'Activity responsible bd',
      dataIndex: 'activity_responsible_bd',
      width: 180
    },
    {
      title: 'Merchant responsible bd',
      dataIndex: 'merchant_responsible_bd',
      width: 200,
      customRender(value, row) {
        return row.merchant_responsible_bd.join(', ')
      }
    },
    // pkg id
    {
      title: __('package_id_title'),
      dataIndex: 'package_id',
      scopedSlots: { customRender: 'package_id' },
      ellipsis: true,
      width: 200
    },
    {
      title: __('package_list_package_status'),
      dataIndex: 'package_status',
      scopedSlots: { customRender: 'package_status' }
    },
    {
      title: __('48222'),
      dataIndex: 'sku_id',
      scopedSlots: { customRender: 'sku_id' },
      width: 200
    },
    {
      title: __('48223'),
      dataIndex: 'sku_status',
      scopedSlots: { customRender: 'sku_status' }
    },
    {
      title: __('act_binding_destination_city'),
      dataIndex: 'city_name'
    },
    {
      title: __('sub_category'),
      dataIndex: 'category.sub_category_name',
      customRender(value, row) {
        return `${row.sub_category_id} - ${row.sub_category_id_name || ''}`
      }
    },
    {
      title: __('leaf_category'),
      dataIndex: 'category.leaf_category_name',
      customRender(value, row) {
        return `${row.leaf_category_id} - ${row.leaf_category_id_name || ''}`
      }
    },
    {
      title: __('submit_time_bj'),
      dataIndex: 'submit_time_beijing',
      width: 190
    },
    {
      title: __('submitted_language'),
      dataIndex: 'submitted_language',
      width: 180,
      customRender(value, row) {
        return `${LANG_TITLE_DICT[value]}`
      }
    },

    // {
    //   title: __('package_count'),
    //   dataIndex: 'package_count',
    //   scopedSlots: { customRender: 'package_count' },
    //   width: 200
    // },
    // {
    //   title: __('merchant_approve_status'),
    //   dataIndex: 'approval_status',
    //   scopedSlots: { customRender: 'approval_status' },
    //   width: 180
    // },
    {
      title: __('manage'),
      align: 'center',
      dataIndex: 'manage',
      fixed: 'right',
      scopedSlots: { customRender: 'action2admin' },
      width: 180
    }
  ]
  let approval_activity = {
    title: __('approval_activity'),
    align: 'center',
    dataIndex: 'approval_activity',
    scopedSlots: { customRender: 'approval_activity' },
    width: 202,
    fixed: 'right'
  }
  checkAuth(['actApprove', 'actReject']) &&
    actColumns2admin.splice(actColumns2admin.length - 1, 0, approval_activity)
  return actColumns2admin
}

let initPkg2merchant = function () {
  const pkgColumns2merchant = [
    {
      title: __('package_id_title'),
      dataIndex: 'package_id',
      scopedSlots: { customRender: 'package_id' },
      ellipsis: true,
      fixed: 'left',
      width: 200
    },
    {
      title: __('48221'),
      dataIndex: 'units',
      scopedSlots: { customRender: 'units' },
      width: 120
    },
    {
      title: __('48219'),
      dataIndex: 'approval_status',
      scopedSlots: { customRender: 'approval_status' },
      width: 180
    },
    {
      title: __('package_list_package_status'),
      dataIndex: 'package_status',
      scopedSlots: { customRender: 'package_status' }
    },
    {
      title: __('package_archived'),
      dataIndex: 'package_archived',
      scopedSlots: { customRender: 'package_archived' }
    },
    {
      title: __('activity_id_title'),
      dataIndex: 'activity_id',
      scopedSlots: { customRender: 'activity_id' },
      ellipsis: true
    },
    // {
    //   title: __('category'),
    //   dataIndex: 'category.category_name'
    // },
    {
      title: __('leaf_category'),
      dataIndex: 'category.leaf_category_name'
    },
    {
      title: __('sub_category'),
      dataIndex: 'category.sub_category_name'
    },
    {
      title: __('28247'),
      dataIndex: 'create_show_name'
    },
    {
      title: __('create_time_bj'),
      dataIndex: 'create_time_bj',
      width: 180
    }
  ]
  const pkgManage2merchant = {
    title: __('manage'),
    align: 'center',
    dataIndex: 'manage',
    fixed: 'right',
    scopedSlots: { customRender: 'action' },
    width: 150
  }
  pkgColumns2merchant.push(pkgManage2merchant)
  return pkgColumns2merchant
}

// 这个已经废弃，合并到 actColumns2merchant 了
let initPkg2admin = function () {
  const pkgColumns2admin = [
    {
      title: __('package_id_title'),
      dataIndex: 'package_id',
      scopedSlots: { customRender: 'package_id' },
      ellipsis: true,
      fixed: 'left',
      width: 200
    },
    {
      title: __('activity_id_title'),
      dataIndex: 'activity_id',
      scopedSlots: { customRender: 'activity_id' },
      ellipsis: true,
      fixed: 'left',
      width: 200
    },
    {
      title: __('merchant_id'),
      dataIndex: 'merchant_id',
      customRender(merchant_id, row) {
        return `${merchant_id}-${row.merchant_name}`
      },
      width: 200
    },
    {
      title: __('act_binding_destination_city'),
      dataIndex: 'city_name'
    },
    {
      title: __('sub_category'),
      dataIndex: 'category.sub_category_name'
    },
    {
      title: __('leaf_category'),
      dataIndex: 'category.leaf_category_name'
    },
    {
      title: __('submit_time_bj'),
      dataIndex: 'submit_time_bj',
      width: 190
    },
    {
      title: __('submitted_language'),
      dataIndex: 'language',
      width: 180
    },
    {
      title: __('merchant_approve_status'),
      dataIndex: 'approval_status',
      scopedSlots: { customRender: 'approval_status' },
      width: 180
    },
    {
      title: __('package_list_package_status'),
      dataIndex: 'package_status',
      scopedSlots: { customRender: 'package_status' }
    },
    {
      title: __('manage'),
      align: 'center',
      dataIndex: 'manage',
      fixed: 'right',
      scopedSlots: { customRender: 'action2admin' },
      width: 180
    }
  ]
  let approval_activity = {
    title: __('approval_activity'),
    align: 'center',
    dataIndex: 'approval_activity',
    scopedSlots: { customRender: 'approval_activity' },
    width: 202,
    fixed: 'right'
  }
  checkAuth(['pkgApprove', 'pkgReject']) &&
    pkgColumns2admin.splice(pkgColumns2admin.length - 1, 0, approval_activity)
  return pkgColumns2admin
}

const logColumns = [
  {
    title: __('modify_time_bj'),
    dataIndex: 'modify_time_bj',
    scopedSlots: { customRender: 'timestamp_utc' },
    width: 110
  },
  {
    title: __('performed_by'),
    dataIndex: 'performed_by',
    width: 120
  },
  {
    title: __('before_status'),
    dataIndex: 'before_status'
  },
  {
    title: __('after_status'),
    dataIndex: 'after_status'
  },
  {
    title: __('note'),
    dataIndex: 'note',
    ellipsis: true,
    width: 176
  }
]

const ticketLogColumns = [
  {
    title: __('modify_time_bj'),
    dataIndex: 'operator_time_beijing',
    width: 190
  },
  {
    title: __('performed_by'),
    dataIndex: 'operator_name',
    width: 200
  },
  {
    title: __('before_status'),
    dataIndex: 'before_state',
    width: 160
  },
  {
    title: __('after_status'),
    dataIndex: 'after_state',
    width: 160
  },
  {
    title: __('note'),
    dataIndex: 'note',
    width: 200
  }
]

const ticketStatusFilterDict = [
  { label: __('48072'), value: 'PendingApproval' },
  { label: __('48069'), value: 'Rejected' },
  { label: __('48071'), value: 'Approved' },
  { label: __('48218'), value: 'Closed' },
  { label: __('48070'), value: 'ToBeSubmitted' }
]

const allTicketSearchSchemaFormConfig = {
  container: {
    style: {
      backgroundColor: '#fafafa'
    }
  },
  attrs: {
    layout: 'vertical'
  },
  style: {
    display: 'flex',
    flexWrap: 'wrap'
  },
  common: {
    labelStyle: {
      display: 'inline-block',
      width: '100%'
    }
  },
  formItems: [
    {
      label: __('48097'),
      type: 'input',
      value: undefined,
      field: 'ticket_id',
      attrs: { placeholder: 'Please input keyword' }
    },
    {
      label: __('48098'),
      type: 'select',
      value: [],
      field: 'ticket_type',
      inputConfig: {
        props: {
          mode: 'multiple',
          maxTagCount: 1
        },
        options: ticketTypeOptions
      }
    },
    {
      label: __('48230'),
      type: 'select',
      value: [],
      field: 'ticket_status',
      inputConfig: {
        props: {
          mode: 'multiple',
          maxTagCount: 1
        },
        options: maps.ticket_status
      }
    },
    {
      label: __('submit_time_bj'),
      type: 'range',
      value: [],
      field: 'submitted_time', //
      inputConfig: {
        listeners: {
          change({ proxyFormData }, date, dateString) {
            proxyFormData.submitted_time = dateString
          }
        }
      }
    },
    {
      label: __('48103'),
      type: 'lazyLongSelect',
      value: [],
      field: 'merchant_id',
      attrs: { placeholder: 'Please input keyword' },
      inputConfig: {
        props: {
          treeCheckable: true,
          multiple: true,
          treeDefaultExpandAll: true,
          opsLength: 30
        },
        attrs: {
          maxTagCount: 1
        }
      }
    },
    {
      label: __('28010'),
      type: 'input',
      value: '',
      field: 'activity_id',
      attrs: { placeholder: 'Please input keyword' },
      inputConfig: {
        formatValue(value) {
          return Array.isArray(value) || isNaN(value) || !value ? [] : [+value]
        }
      }
    },
    {
      label: __('pm_pkg_id'),
      type: 'input',
      value: '',
      field: 'package_id',
      attrs: { placeholder: 'Please input keyword' },
      inputConfig: {
        formatValue(value) {
          return Array.isArray(value) || isNaN(value) || !value ? [] : [+value]
        }
      }
    },
    {
      label: __('SKU_ID'),
      type: 'input',
      value: '',
      field: 'sku_id',
      attrs: { placeholder: 'Please input keyword' },
      inputConfig: {
        formatValue(value) {
          return Array.isArray(value) || isNaN(value) || !value ? [] : [+value]
        }
      }
    },
    {
      label: __('92294'),
      field: 'market_region',
      type: 'lazyLongSelect',
      value: [],
      inputConfig: {
        props: {
          treeCheckable: true,
          multiple: true,
          treeDefaultExpandAll: true
        },
        attrs: {
          maxTagCount: 1
        }
      },
      isPeekaboo: false
    },
    {
      label: __('taxonomy_belong_county'),
      field: 'country',
      type: 'lazyLongSelect',
      value: [],
      inputConfig: {
        props: {
          treeCheckable: true,
          multiple: true,
          treeDefaultExpandAll: true
        },
        attrs: {
          maxTagCount: 1
        }
      },
      isPeekaboo: true
    },
    {
      label: __('107889'),
      field: 'city',
      type: 'lazyLongSelect',
      value: [],
      inputConfig: {
        props: {
          treeCheckable: true,
          multiple: true,
          treeDefaultExpandAll: true
        },
        attrs: {
          maxTagCount: 1
        }
      },
      isPeekaboo: true
    },
    {
      label: __('category'),
      field: 'category',
      type: 'treeSelect',
      value: [],
      inputConfig: {
        props: {
          treeCheckable: true,
          multiple: true,
          treeDefaultExpandAll: false,
          dropdownStyle: {
            maxHeight: '500px'
          }
        },
        attrs: {
          maxTagCount: 1
        }
      },
      isPeekaboo: true
    },
    {
      label: __('global_activity_status'),
      field: 'activity_status',
      type: 'select',
      value: [],
      inputConfig: {
        props: {
          mode: 'multiple'
        },
        options: maps.activity_status2admin,
        attrs: {
          maxTagCount: 1
        }
      },
      isPeekaboo: true
    },
    {
      label: __('48230'),
      field: 'ticket_status',
      value: [],
      inputConfig: {
        props: {
          mode: 'multiple',
          maxTagCount: 1
        },
        options: ticketStatusFilterDict
      }
    },
    {
      label: __('205933'),
      type: 'select',
      field: 'migrated_to_spu',
      value: undefined,
      inputConfig: {
        // 是否迁移 spu, 0 不参与筛选, 1- 已迁移, 2- 未迁移
        options: [
          { label: 'YES', value: 1 },
          { label: 'NO', value: 2 }
        ]
      }
    },
    {
      field: 'activity_responsible_bd',
      label: 'Activity responsible bd',
      value: [],
      inputConfig: {
        options: [],
        timer: null,
        render(h, ctx) {
          return (
            <a-select
              show-search
              mode="multiple"
              placeholder="Please input keyword"
              show-arrow={false}
              filter-option={false}
              default-active-first-option={false}
              not-found-content={null}
              value={ctx.props.proxyFormData.activity_responsible_bd || ''}
              options={ctx.data.attrs.inputConfig.options || []}
              onSearch={(query) => {
                if (ctx.timer) {
                  clearTimeout(ctx.timer)
                }

                const requestUniqueId = Number(+new Date() + String(Math.random()).slice(2, 8)).toString(32)
                ctx.timer = setTimeout(async () => {
                  ctx.requestUniqueId = requestUniqueId
                  const response = await ajax.get(ADMIN_API.act.filter_activity_responsible_bd, {
                    params: {
                      activity_responsible_bd: query
                    }
                  })

                  if (ctx.requestUniqueId === requestUniqueId) {
                    ctx.data.attrs.inputConfig.options = response.map((item) => ({
                      label: item,
                      value: item
                    }))
                  }
                }, 200)
              }}
              onChange={(value) => {
                ctx.props.proxyFormData.activity_responsible_bd = value
              }}
            />
          )
        }
      }
    }
  ].map((item) => ({
    ...item,
    style: {
      width: '250px',
      marginRight: '20px'
    },
    inputConfig: {
      ...(item.inputConfig || {}),
      style: {
        width: '100%'
      }
    }
  }))
}

export default {
  platform,
  isAdmin,
  isMerchant,
  actColumns: isMerchant ? actColumns2merchant : initAct2admin(),
  allTicketSearchSchemaFormConfig,
  pkgColumns: isMerchant ? initPkg2merchant() : initPkg2admin(),
  logColumns,
  ticketLogColumns,
  data() {
    return {}
  }
}

const editSaveSpmDefined = {
  basicInfo: 'SaveActivityBasicInfo',
  detail: 'SaveActivityDetail',
  packageBasicInfo: 'SavePackageInfo',
  packageDetail: 'SavePackageDetail',
  packageItinerary: 'SaveItinerary',
  packageExtra: 'SaveOtherInfo'
}

export const getMerchantEditSaveSpm = (routeName) => {
  if (!isMerchant) {
    return
  }
  const spm = editSaveSpmDefined[routeName]
  return spm
}
</script>

<style lang="scss" scoped></style>
