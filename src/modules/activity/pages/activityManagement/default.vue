<template>
  <div class="act-default">
    <div class="act-default-box">
      {{ defTips }}
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      defTips: ''
    }
  },
  created() {
    if (this.$root.isMerchant) {
      window.location.href = '/home'
    } else {
      this.defTips = 'Welcome to ADMIN platform boilerplate project.'
    }
  }
}
</script>

<style lang="scss" scoped>
.act-default {
  .act-default-box {
    padding: 30px;
  }
}
</style>
