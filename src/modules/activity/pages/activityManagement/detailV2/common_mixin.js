const markdownIt = require('markdown-it')().disable(['code'])
import { getUuid, broadcastDisabled } from '@activity/utils'
import { genUUID } from '@activity/utils'

// 这部分的函数在套餐基本信息和详情页都有使用
export default {
  data() {
    return {
      mapObj: {
        old_markdown_content: {}
      },

      groupWidgetsObj: {}
    }
  },
  watch: {
    'mapObj.old_markdown_content': {
      handler(obj) {
        for (let key in obj) {
          let v = obj[key]
          let ref = this.$refs[`widgetsList${key}`]
          if (v && ref) {
            broadcastDisabled.call(ref, ['a-input', 'a-button', 'a-radio', 'a-checkbox'], null)
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleResetWidget(widget, groupId) {
      if (!this.originalGroupData) {
        return
      }

      const { uuid } = widget
      let { groups_instance } = this.originalGroupData
      let widgetsData = groups_instance[groupId]
      const index = _.findIndex(widgetsData, { uuid })
      this.$set(widgetsData, index, {
        ...this.getWidgetDefaultData(widgetsData[index]),
        uuid
      })

      // 重置 UI 渲染的旧数值
      let refWidget = this.$refs[`G-${groupId}-widget`]
      if (_.get(refWidget, `${index}.refreshUIData`, null)) {
        refWidget[index].refreshUIData()
      }

      this.$message.success('Successfully reset.')
      this.$forceUpdate()
    },
    getWidgetCount(widget) {
      let type = widget.ui_conf.widget_type
      let num = widget.attr_values.length
      let count = widget.ui_conf.attr_value_list.length
      let str = ''
      let obj = {}
      // item.id===3568 && console.log(item.id, widget.attr_values, item)
      let subNum = 0 // 特殊数组差值
      switch (type) {
        case 6:
          obj = _.get(widget, 'attr_values.0.value', {})
          if (typeof obj === 'string') {
            obj = JSON.parse(obj)
          }
          num = obj.single === undefined ? 0 : 1
          count = num || 1
          break
        case 3:
        case 2:
          obj = _.get(widget, 'attr_values.0.value')
          num = !obj ? 0 : 1
          count = num || 1
          break
        case 1:
          if (_.get(widget, 'ui_conf.config.multiple_selection')) {
            subNum =
              widget.attr_values.map((o) => o.id).length -
              _.uniqWith(widget.attr_values.map((o) => o.id)).length
            count = count + subNum
          } else {
            count = num || 1
          }
          break
        default:
          count = num || 1
      }
      str = `${num}/${count}`
      this.$set(widget, 'isFinishFlag', num > 0) // 两种方法都不完美
      // let temp = item.summary.find(o=>o.id===widget.id)
      // temp && this.$set(widget, 'isFinishFlag', temp.required )
      return str
    },
    /**
     * @param {boolean} getEditData - 是否需要处理老未结构化 Markdown 数据
     * @param {boolean} isCreate - 如果是第一次创建，直接取默认 group data 就行
     * @param {boolean} hasDraftData - 草稿态 order 作为排序和唯一索引，在初始化的时候需要对数据排序
     */
    getCurrentGroupData({ group_id, data, getEditData = true, isCreate = false, hasDraftData = false } = {}) {
      if (!data) return

      let { groups_schema, groups_instance } = data
      let groupInstance = groups_instance?.[group_id] ?? {}
      let widgets = groupInstance?.widgets ?? []

      if (getEditData) {
        let old_markdown_content = groupInstance?.old_markdown_content ?? ''
        if (old_markdown_content) {
          old_markdown_content = markdownIt.render(old_markdown_content)
        }
        this.mapObj.old_markdown_content[group_id] = old_markdown_content
      }

      // widget_list 的 widget 需要获得重复的情况
      const widget_list = groups_schema[group_id]?.widget_list ?? []
      return widget_list.reduce(
        (acc, widget) => {
          widget.config = typeof widget.config === 'string' ? JSON.parse(widget.config) : widget.config || {}
          const isMulti = widget.config.multiple_selection
          let currWidgetInstanceData = _.find(widgets, {
            id: widget.id
          })

          if (currWidgetInstanceData) {
            // 可能存在重复的ID
            const relevantWidgetsData = widgets
              .filter((item) => item.id === widget.id)
              .map((item) => {
                let prodAttrValues = item.attr_values || []
                let draftAttrValues = item.draft_attr_values || []
                // 有草稿态需要排序，用于 autoCheckedToFront
                if (hasDraftData) {
                  prodAttrValues = _.orderBy(prodAttrValues, 'order')
                  draftAttrValues = _.orderBy(draftAttrValues, 'order')
                } else {
                  let maxOrder = prodAttrValues
                    .map((v) => v.order || 0)
                    .reduce((acc, curr) => Math.max(acc, curr), 0)

                  prodAttrValues = prodAttrValues.map((v) => {
                    return {
                      ...v,
                      order: v.order !== undefined ? v.order : maxOrder++
                    }
                  })
                }

                let attrValues = _.cloneDeep(draftAttrValues)

                prodAttrValues.forEach((curr) => {
                  // basic info 和 detail 不一样的数据结构，没有 instance_id 和草稿态的概念
                  const draftInstance = attrValues.find(
                    (attrVal) => attrVal.instance_id && attrVal.instance_id === curr.instance_id
                  )
                  if (!draftInstance) {
                    // 将产线数据补充到草稿数据里
                    attrValues.push(curr)
                  }
                })

                let data = {
                  ...item,
                  refAttrId: widget.ref_attr_id,
                  ui_conf: {
                    ...widget,
                    order: item.order
                  },
                  uuid: getUuid('widget'),
                  order: item.order,
                  current_attr_values: prodAttrValues,
                  draft_attr_values: draftAttrValues
                }

                data.draftAttrValuesDeleted = prodAttrValues
                  .filter((item) => item.deleted)
                  .map((item) => genUUID(item))

                // 如果是单选的需要把重复的 id 去掉
                if (!isMulti) {
                  attrValues = attrValues.reduce((acc, curr, i, arr) => {
                    if (!curr.deleted) {
                      return [...acc, curr]
                    }

                    return acc
                  }, [])
                }
                data.attr_values = attrValues.map((v) => ({
                  ...v,
                  isInstanceData: true // 是否接口存在的实例数据
                }))

                // 如果有草稿的 draft_effective_time 数据，则补充到 effective_time 里面去
                if (item.draft_effective_time) {
                  const prodEffectiveTime = data.effective_time || {}
                  data.effective_time = {
                    start_time: item.draft_effective_time.start_time || prodEffectiveTime.start_time || '',
                    end_time: item.draft_effective_time.end_time || prodEffectiveTime.end_time || ''
                  }
                }

                data.checkList = this.getCheckListData(data)

                return data
              })

            acc.widgetsData.push(...relevantWidgetsData)
            acc.effectiveWidgetUUID.push(...relevantWidgetsData.map((widget) => widget.uuid))
          } else {
            const isMulti = widget.config.multiple_selection
            let checkList = isMulti ? [] : ''

            if (isCreate) {
              checkList = widget.attr_value_list.reduce((acc, curr, index) => {
                const conf = typeof curr.config === 'string' ? JSON.parse(curr.config) : curr.config || {}

                if (conf.default_selected) {
                  const uuid = genUUID({ ...curr, order: index })
                  if (isMulti) {
                    acc.push(uuid)
                  } else {
                    acc = uuid
                  }
                }

                return acc
              }, checkList)
            }

            acc.widgetsData.push({
              refAttrId: widget.ref_attr_id,
              ...this.getWidgetDefaultData(
                {
                  ui_conf: widget,
                  uuid: getUuid('widget')
                },
                isCreate
              ),
              checkList,
              current_attr_values: null,
              draft_attr_values: null
            })
          }

          return acc
        },
        {
          widgetsData: [],
          effectiveWidgetUUID: []
        }
      )
    },
    // 初始化 widget 对应的数据结构
    getWidgetDefaultData(widget, isCreate = false) {
      let { ui_conf, uuid } = widget

      if (!uuid) {
        throw new TypeError('uuid is invalid')
      }

      const common = {
        ui_conf,
        id: ui_conf.id,
        uuid
      }
      // widget_type: 1,// widget 类型：1-Attribute 2-Single Text 3-Markdown 4-Image 5-Address, 6: duration
      if ([1, 4].includes(ui_conf.widget_type)) {
        return {
          ...common,
          attr_values: ui_conf.attr_value_list.reduce((acc, curr, index) => {
            let current = null
            if (curr.config) {
              current = JSON.parse(curr.config)
              if (isCreate && current.default_selected) {
                let var_map = null
                if (curr.var_map) {
                  var_map = Object.entries(curr.var_map).reduce((accVar, currVar) => {
                    const currVariable = this.allVariables.find((variable) => variable.id === currVar[1])

                    return {
                      ...accVar,
                      [currVar[0]]: {
                        key: currVar[0],
                        attr_item_id: currVar[1],
                        type: currVariable?.type,
                        values: []
                      }
                    }
                  }, {})
                }

                return [
                  ...acc,
                  {
                    ...curr,
                    var_map: var_map,
                    variable_map: var_map,
                    id: curr.id,
                    value: curr.value,
                    order: index,
                    published: -1
                  }
                ]
              }
            }

            return acc
          }, [])
        }
      }

      return {
        ...common,
        attr_values: [
          {
            create: true,
            is_free_text: true,
            published: -1,
            value: [5, 6].includes(ui_conf.widget_type) ? '{}' : '',
            id: ui_conf.ref_attr_id,
            widget_type: ui_conf.widget_type
          }
        ]
      }
    },
    // 格式化展示属性 groups 数据（会将数据都整合到 group_instance group 数组上）
    formatResponseData(data, { getEditData = true, isCreate = false, hasDraftData = false } = {}) {
      data = _.cloneDeep(data)

      if (!data?.groups_instance) {
        data.groups_instance = {}
      }

      data.groups_summary = (data.groups_summary || []).map((group) => {
        const groupId = group.id
        data.groups_schema[groupId].uuid = getUuid('group')

        let { widgetsData } = this.getCurrentGroupData({
          group_id: groupId,
          data,
          getEditData,
          isCreate,
          hasDraftData
        })

        data.groups_instance[group.id] = widgetsData

        return group
      })

      return data
    },
    // 获取展示属性勾选数据，需要注意 multi or not
    getCheckListData(widget) {
      const { attr_values, ui_conf } = widget

      const multi = ui_conf.config?.multiple_selection ?? false
      const defaultV = multi ? [] : ''

      if (!attr_values?.length) {
        return defaultV
      }

      const checked = (attr_values.filter((v) => !(v.is_free_text || v.deleted)) || []).map((v) => genUUID(v))

      if (checked.length) {
        return multi ? checked : checked[0]
      }

      return defaultV
    }
    //
  }
}
