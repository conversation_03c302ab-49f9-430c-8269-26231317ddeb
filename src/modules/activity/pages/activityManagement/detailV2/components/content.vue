<template>
  <div v-show="displayContent" class="group-content-container">
    <div
      v-if="$slots.customTitle"
      class="ant-col ant-form-item-label"
      :class="{
        'ant-form-item-required': required
      }"
    >
      <slot name="customTitle" class="ant-form-item-no-colon"></slot>
    </div>

    <div
      v-for="group of groupList"
      :key="group.uuid"
      :data-group-id="`group-${group.id}`"
      class="common-box group-item"
      :style="{
        padding: groupItemPadding
      }"
      :class="{
        'is-active': currActiveGroupId === group.id
      }"
    >
      <GroupItem
        ref="group"
        v-bind="$attrs"
        :group="group"
        :all-variables="allVariables"
        :field="field"
        :original-group-data="originalGroupData"
        :fold-group-ids.sync="foldGroupIds"
        :required="getConfigRequired(group) || required"
        :mandatoryGroupFill="getConfigRequired(group)"
        @fold="handleFold"
        @mouseenterGroup="mouseenterGroup"
        @mouseleaveGroup="mouseleaveGroup"
        @unselectGroup="unselectGroup"
        @groupUnfold="groupUnfold"
        @refreshSummary="refreshSummary"
        @displayCount="displayCount"
        @hook:mounted="getGroupsSummaryDebounce"
      />
    </div>

    <div v-if="isInvalidate" class="has-error">
      {{ $t('package_error_mandatory') }}
    </div>

    <slot name="footer"></slot>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { computedGroupSummary } from '@activity/components/groupSummary/utils'
import GroupItem from './groupItem'

export default {
  name: 'GroupContent',
  components: {
    GroupItem
  },
  props: {
    originalGroupData: {
      type: Object,
      required: true,
      validator: function (data) {
        return ['groups_summary', 'groups_instance', 'groups_schema'].every((key) =>
          hasOwnProperty.call(data, key)
        )
      }
    },
    groupList: {
      type: Array,
      required: true
    },
    allVariables: {
      type: Array,
      required: true
    },
    showSummary: {
      type: Boolean,
      default: false
    },
    orderList: {
      type: Array,
      default: () => []
    },
    initCallback: {
      type: Function,
      default: null
    },
    fieldTitle: {
      type: String,
      default: ''
    },
    required: {
      type: Boolean,
      default: false
    },
    field: {
      type: String,
      required: true
    },
    groupItemPadding: {
      type: String,
      default: '20px'
    },
    firstInitialFold: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isInvalidate: false,
      saveSpinning: {},
      groupsSummary: [],
      currActiveGroupId: null,

      foldGroupIds: [],

      displayCountOnGroup: {}
    }
  },
  computed: {
    ...mapState({
      selectionList: (state) => state.attr.selectionList
    }),
    checkedTitlesOnGroup() {
      return this.$store.getters['attr/checkedTitlesOnGroupGetter']({
        field: this.field
      })
    },
    displayContent() {
      const keys = Object.keys(this.displayCountOnGroup)

      return keys.length && keys.some((key) => !!this.displayCountOnGroup[key])
    }
  },
  watch: {
    displayContent: {
      immediate: true,
      handler(v) {
        this.$emit('changeDisplayContent', v)
      }
    },
    foldGroupIds: {
      deep: true,
      handler(v) {
        this.$emit('changeFoldGroupIds', v)
        // 修复默认折叠状态下导致 markdown 初始化的内容不显示
        setTimeout(() => {
          klook.bus.$emit('markdownCodemirrorRefresh')
        })
      }
    },
    groupList: {
      immediate: true,
      deep: true,
      handler(v) {
        if (this.firstInitialFold) {
          // 仅第一次的时候需要
          this.$emit('update:firstInitialFold', false)
          this.foldAllGroups()
        }
        // 清除已经无效的 group
        const foldGroupIds = this.foldGroupIds.filter((groupId) => v.find((group) => group.id === groupId))
        this.$set(this, 'foldGroupIds', foldGroupIds)
      }
    }
  },
  async mounted() {
    klook.bus.$off(`${this.field}_UnfoldGroup`).$on(`${this.field}_UnfoldGroup`, this.handleUnfoldGroup)

    await this.$nextTick()
    this.getGroupsSummaryDebounce()
  },
  beforeDestroy() {
    klook.bus.$off(`${this.field}_UnfoldGroup`, this.handleUnfoldGroup)
  },
  methods: {
    //  从config中获取group_need_one_title字段，表示改组至少需要填写一个组件
    getConfigRequired(group) {
      let data = {}
      try {
        data = JSON.parse(group?.config || '{}')
      } catch (e) {
        console.error('Invalid JSON string:', group?.config)
      }
      return data['group_need_one_title'] === 1
    },
    computedGroupSummary,
    mouseenterGroup(groupId) {
      this.currActiveGroupId = groupId
    },
    mouseleaveGroup() {
      this.currActiveGroupId = null
    },
    unfoldAllGroups() {
      this.$set(this, 'foldGroupIds', [])
      this.$emit('unfoldAllGroups')
    },
    foldAllGroups() {
      const foldGroupIds = this.groupList.map((group) => group.id)
      this.$set(this, 'foldGroupIds', foldGroupIds)
    },
    handleUnfoldGroup({ groupId }) {
      if (this.foldGroupIds.includes(groupId)) {
        this.handleFold({ groupId })
      }
    },
    handleFold({ groupId }) {
      let foldGroupIds = _.cloneDeep(this.foldGroupIds)
      if (foldGroupIds.includes(groupId)) {
        foldGroupIds = foldGroupIds.filter((item) => item !== groupId)
        // 展开所有
        this.$emit('unfoldAllGroups')
      } else {
        foldGroupIds.push(groupId)
      }

      this.$set(this, 'foldGroupIds', foldGroupIds)
    },
    // 取消勾选 group，即将 store 对应的 titles data 清空
    unselectGroup(data) {
      this.$emit('unselectGroup', data)
    },
    groupUnfold(group) {
      if (this.groupList.length === 1) {
        this.unfoldAllGroups()
      } else {
        this.$set(
          this,
          'foldGroupIds',
          this.foldGroupIds.filter((item) => item !== group.id)
        )
      }
    },
    tplDataNotSaved() {
      return (this.$refs?.group || []).map((item) => item.tplDataNotSaved()).some((notSaved) => notSaved)
    },
    async validateForm() {
      return (await Promise.all((this.$refs.group || []).map((item) => item.validateForm()))).every(
        (valid) => valid
      )
    },
    handleResetBasicWidget(widget, groupId) {
      this.handleResetWidget(widget, groupId, false)
      this.getGroupsSummaryDebounce()
    },
    getGroupsSummaryDebounce: _.debounce(function () {
      this.getGroupsSummary()
    }, 400),
    getGroupsSummary() {
      let reqResult = this.originalGroupData

      let groupsSummary = this.groupList.map((group) => {
        let widgetsData = reqResult.groups_instance[group.id]
        let summary = this.computedGroupSummary(
          widgetsData,
          reqResult.groups_schema[group.id].widget_list,
          this.allVariables
        )

        return {
          ...group,
          summary
        }
      })

      this.$set(this, 'groupsSummary', groupsSummary)
      this.$emit('changeGroupSummary', {
        field: this.field,
        groupsSummary
      })

      if (this.summaryInitialized) {
        this.$emit('changeData')
      } else {
        this.summaryInitialized = true
      }
    },
    refreshSummary() {
      this.isInvalidate = false
      this.getGroupsSummaryDebounce()
    },
    displayCount({ groupId, count }) {
      this.$set(this, 'displayCountOnGroup', {
        ...this.displayCountOnGroup,
        [groupId]: count
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.common-box {
  position: relative;
  max-width: 100%;
  min-width: 600px;
  padding: 20px;
  border-radius: 4px;
  box-sizing: border-box;
  background-color: #fff;
}

.group-content-container {
  .group-item {
    border: 1px solid transparent;

    &.is-active {
      background-color: #edf5ff;
    }
  }
}
</style>

<style lang="scss">
.common-blink-scroll-into-view {
  border: 1px solid transparent;
  animation: blink2view 5s cubic-bezier(0.55, 0.06, 0.68, 0.19) 1;
}

@keyframes blink2view {
  0%,
  33%,
  66%,
  100% {
    background-color: transparent;
    border-color: transparent;
  }
  17%,
  50%,
  83% {
    border-color: #437dff;
    background-color: #edf5ff;
  }
}
</style>
