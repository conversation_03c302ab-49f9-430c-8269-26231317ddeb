<template>
  <OverflowTextReadMore v-if="desc" v-bind="$attrs" class="desc-markdown-content-container" placement="right">
    <span v-html="currDesc"></span>
  </OverflowTextReadMore>
</template>

<script>
import OverflowTextReadMore from '@activity/pages/activityManagement/detailV2/components/overflowTextReadMore'
const markdownIt = require('markdown-it')().disable(['code'])

export default {
  name: 'DescMarkdownContent',
  components: {
    OverflowTextReadMore
  },
  props: {
    desc: {
      type: String,
      default: ''
    }
  },
  computed: {
    currDesc() {
      return ((this.desc && markdownIt.render(this.desc)) || '')
        .replace(
          /<img/,
          // 由于 img 没加载完成时 height 为 0, 会导致计算有误, 导致 read more 没有出来
          // 这里给个最小高度 1em, 使其在计算时不至于被忽略
          '$& style="display: block; max-width: 100%; min-height: 1em; background-color: #f7f7f8;"'
        )
        .replace(
          'https://res.klook.com/image/upload/',
          'https://res.klook.com/image/upload/fl_lossy.progressive,q_auto/q_80,c_fill,w_600/'
        )
    }
  }
}
</script>
