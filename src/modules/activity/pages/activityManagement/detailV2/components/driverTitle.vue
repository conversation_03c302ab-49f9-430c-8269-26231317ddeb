<template>
  <div class="driver-title-container">
    <div class="content">
      <span class="icon"><a-icon type="question-circle"></a-icon></span>
      <span>{{ config.content }}</span>
    </div>
    <a-checkbox
      v-if="noReminderStorageKey && config.index + 1 === config.total"
      class="no-reminder-checkbox"
      @change="changeCheckbox"
    >
      {{ noReminderTxt }}
    </a-checkbox>
    <footer class="footer">
      <label class="progress"> {{ config.index + 1 }}/{{ config.total }} </label>
      <label class="btn" @click="onNext">
        {{ config.btnTxt }}
      </label>
    </footer>
  </div>
</template>

<script>
export default {
  props: {
    config: {
      type: Object,
      required: true
    },
    onNext: {
      type: Function,
      required: true
    },
    noReminderStorageKey: {
      type: String,
      default: ''
    }
  },
  computed: {
    noReminderTxt() {
      return __('73073')
    }
  },
  methods: {
    changeCheckbox(evt) {
      localStorage.setItem(this.noReminderStorageKey, evt.target.checked)
    }
  }
}
</script>

<style lang="scss" scoped>
.driver-title-container {
  padding: 5px;
  font-weight: 400;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);

  .content {
    position: relative;
    display: flex;
    align-items: flex-start;
    line-height: 24px;

    .icon {
      flex: none;
      display: inline-block;
      color: #437dff;
      margin-right: 12px;
      font-size: 20px;
    }
  }

  .no-reminder-checkbox {
    margin-top: 12px;
  }

  .footer {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
    padding-left: 32px;

    .progress {
      color: rgba(0, 0, 0, 0.45);
    }

    .btn {
      font-size: 16px;
      font-weight: 600;
      color: #437dff;
    }
  }
}
</style>
