<template>
  <div v-if="display" class="fold-to-preview-container">
    <div
      class="widget-name"
      :class="{
        'ant-form-item-required': !!widget.ui_conf.required
      }"
    >
      {{ widget.ui_conf.name }}
    </div>
    <ul class="attr-value-list" :class="{ 'is-inline': widget.ui_conf.widget_type === 4 }">
      <li v-for="attrVal of attrValues" :key="attrVal.uuid" class="attr-value-item">
        <span v-html="getPreviewHtml(attrVal)"></span>
      </li>
    </ul>
    <div v-if="showTimeControl" class="attr-value-time-control">
      <div v-if="timeControlStart" style="margin-top: 8px">
        Display Start Time（UTC）：{{ timeControlStart }}
      </div>
      <div v-if="timeControlEnd" style="margin-top: 8px">Display End Time（UTC）：{{ timeControlEnd }}</div>
    </div>
  </div>
</template>

<script>
import { parseSecond2Date } from '@activity/pages/activityManagement/detailV2/utils'
// const markdownIt = require('markdown-it')().disable(['code'])
import { previewRender } from '@activity/components/simple-md/config.js'

export default {
  name: 'FoldToPreview',
  props: {
    widget: {
      required: true,
      type: Object
    }
  },
  data() {
    return {}
  },
  computed: {
    attrValues() {
      const attrValues = this.widget?.attr_values ?? []

      if ([1, 4].includes(this.widget.ui_conf.widget_type)) {
        return _.sortBy(attrValues, 'ui_index')
      }

      return attrValues[0] ? [attrValues[0]] : []
    },
    display() {
      const { hide, access_permission } = this.widget.ui_conf
      if (hide || access_permission === 0) {
        return false
      }

      const { attrValues } = this
      if ([2, 3].includes(this.widget.ui_conf.widget_type)) {
        return attrValues.every((attrVal) => attrVal.value)
      }

      return !!attrValues.length
    },
    warningText() {
      return 'The content has expired. Please delete it if no longer needed.'
    },
    showTimeControl() {
      return this.widget.ui_conf.config.display_time_control
    },
    timeControlStart() {
      const { start_time } = this.widget.effective_time || {}
      return start_time || null
    },
    timeControlEnd() {
      const { end_time } = this.widget.effective_time || {}
      return end_time || null
    }
  },
  methods: {
    parseDateText(data) {
      return `${data.days} ${this.$t('act_days')} ${data.hours} ${this.$t('act_hours')} ${
        data.minutes
      } ${this.$t('act_minutes')}`
    },
    parseMarkdown(data) {
      return previewRender(data?.value ?? '')
    },
    getPreviewHtml(attrVal) {
      let value = attrVal.value
      const type = this.widget.ui_conf.widget_type

      if (type === 6) {
        value = JSON.parse(value)
        const from = parseSecond2Date(value.from)

        if (value.single) {
          return this.parseDateText(from)
        }

        const to = parseSecond2Date(value.to)

        return `${this.parseDateText(from)} - ${this.parseDateText(to)}`
      }

      if (type === 4) {
        try {
          const res = JSON.parse(value)

          return res?.url
            ? `<img src="https://res.klook.com/image/upload/fl_lossy.progressive,q_auto/q_80,c_fill,w_100/activities/${res.url}" />`
            : ''
        } catch (e) {
          return ''
        }
      }

      if (type === 3) {
        return this.parseMarkdown(this.widget.attr_values[0])
      }

      if (attrVal?.showText?._html) {
        return attrVal.showText._html
      }

      let allVariable = typeof value === 'string' ? value.match(/{{[^{}]+}}/g) || [] : []

      allVariable.forEach((variable) => {
        value = value.replace(variable, '<span class="variable">$&</span>')
      })

      return value
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../../../../src/assets/css/lib/mixins';

.fold-to-preview-container {
  max-height: 300px;
  padding: 12px;
  overflow-y: hidden;
  overscroll-behavior: contain;

  @include mixin-hover-display-scrollbar;

  .widget-name {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    color: #000;
    font-size: 16px;
    font-weight: 600;
  }

  .attr-value-list {
    margin-left: 20px;
    list-style: disc;

    &.is-inline {
      display: flex;
      list-style: none;

      .attr-value-item {
        margin-right: 4px;
      }
    }
  }

  .attr-value-item {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    ::v-deep ul {
      list-style: disc;
    }
  }

  .attr-value-time-control {
    margin-top: 0px;
  }

  ::v-deep {
    .variable {
      color: #e64340;
    }
    .variable-complete {
      color: #16aa77;
      &.variable-input {
        background-color: rgba(198, 241, 221, 0.3);
      }
    }
    .variable-undone {
      color: #e64340;
      &.variable-input {
        background-color: rgba(255, 210, 210, 0.3);
      }
    }
  }
}
</style>
