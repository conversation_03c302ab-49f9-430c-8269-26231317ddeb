<template>
  <div class="group-item-container">
    <header v-if="displayTitle" class="group-header">
      <a-tooltip
        placement="top"
        overlay-class-name="common-tooltip-style"
        :title="isFold ? 'Unfold group' : 'Fold group'"
      >
        <div class="group-title" @click="handleFold">
          <div class="group-name">
            <span class="group-rectangle"></span>
            <span class="__name">
              <i v-if="isRequired" class="common-required-star">*</i>
              {{ group.name }}
            </span>
          </div>

          <div class="group-operator">
            <a-icon
              v-if="displayDeleteGroupBtn"
              type="delete"
              class="unselect-btn"
              @mouseenter="mouseenterGroup"
              @mouseleave="mouseleaveGroup"
              @click.stop="handleDeleteGroup"
            ></a-icon>

            <div class="fold-btn">
              <a-icon :type="isFold ? 'right' : 'down'" />
            </div>
          </div>
        </div>
      </a-tooltip>

      <DescMarkdownContent :desc="group.desc" class="group-desc" placement="right" />
    </header>

    <a-alert v-if="groupAlert" type="error" :message="groupAlert" style="margin: 8px 0" />

    <a-spin :spinning="spinning" :delay="300">
      <div
        v-tooltip="{
          visible: isFold,
          content: 'Unfold group'
        }"
        class="group-widget-list"
        :class="{ 'is-fold': isFold }"
        @click="handleGroupUnfold"
      >
        <div
          v-for="(widget, index) of widgetList"
          :key="widget.uuid"
          class="group-widget-box"
          :class="{
            'has-shading': hasShading,
            'is-required': group.required_widget_num > 0
          }"
        >
          <FoldToPreview v-if="cpShowWidget(widget) && isFold" :widget="widget" />
          <WidgetItem
            v-show="cpShowWidget(widget) && getDisplayByWidget(widget)"
            ref="widgetItem"
            class="group-widget-item"
            :data-widget-id="widget.id"
            :data-uuid="widget.uuid"
            :group="group"
            :widget="widget"
            :display="cpShowWidget(widget)"
            :ref-data="
              getRefData({
                groupId: group.id,
                widgetType: widget.ui_conf.widget_type,
                instance_id: widget.instance_id
              })
            "
            :index="index"
            :widget-list="widgetList"
            :is-ash="group.ash"
            :field="field"
            :original-group-data="originalGroupData"
            :all-variables="allVariables"
            :display-delete-btn="!!displayCount[widget.id]"
            :widget-count="displayCount[widget.id]"
            :required="getWidgetItemRequired(widget)"
            v-bind="$attrs"
            @change="changeAttributeVal"
            @copyWidget="copyWidget"
            @validate="validateWidgetRes"
            @unfoldGroupEvt="handleGroupUnfold"
            @setGroupConfig="setGroupConfig"
          />
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script>
import { mapMutations } from 'vuex'
import DescMarkdownContent from '@activity/pages/activityManagement/detailV2/components/descMarkdownContent'
import FoldToPreview from '@activity/pages/activityManagement/detailV2/components/foldToPreview'
import WidgetItem from './widgetItem.vue'
import { genUUID } from '@activity/utils/index'
import draft2prod_mixin from '@activity/pages/activityManagement/detailV2/draft2prod_mixin'
import cancellation_mixin from '@activity/pages/package/basicInfo/mixins/cancellation/groupItem.js'
import { scrollIntoView } from '@activity/pages/activityManagement/detailV2/utils'

export default {
  name: 'GroupItem',
  inject: ['rootChangeLoading'],
  components: {
    WidgetItem,
    DescMarkdownContent,
    FoldToPreview
  },
  mixins: [draft2prod_mixin, cancellation_mixin],
  props: {
    originalGroupData: {
      type: Object,
      required: true,
      validator: function (data) {
        return ['groups_summary', 'groups_instance', 'groups_schema'].every((key) =>
          hasOwnProperty.call(data, key)
        )
      }
    },
    group: {
      type: Object,
      required: true
    },
    refData: {
      type: [Array, Object],
      default: () => []
    },
    allVariables: {
      type: Array,
      required: true
    },
    displayTitle: {
      type: Boolean,
      default: true
    },
    // 组强制校验
    mandatoryGroupFill: {
      type: Boolean,
      default: false
    },
    foldGroupIds: {
      type: Array,
      default: () => []
    },
    field: {
      type: String,
      required: true
    },
    // group 有底纹背景色
    hasShading: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      spinning: false,
      groupAlert: ''
    }
  },
  computed: {
    isRequired() {
      return this.required || this.widgetList.some((widget) => !!widget.ui_conf.required)
    },
    groupId() {
      return this.group.id
    },
    invalidWidgetTags() {
      return this.$store.getters['attr/invalidWidgetTagsByFieldGetter']({
        field: this.field
      })
    },
    checkedTitlesOnGroup() {
      return this.$store.getters['attr/checkedTitlesOnGroupGetter']({
        field: this.field
      })
    },
    widgetList() {
      const { groupId } = this
      const checkedTitlesData = this.checkedTitlesOnGroup[groupId] || []

      const curGroupsInstance = this.originalGroupData.groups_instance[groupId]
      const result =
        curGroupsInstance && curGroupsInstance.filter
          ? curGroupsInstance.filter(
              (widget) =>
                checkedTitlesData.includes(genUUID(widget)) && // 需要左边列表勾选才展示
                !this.invalidWidgetTags.includes(widget.ui_conf.ref_field_tag) // open ticket 旧有联动逻辑支持
            )
          : []

      return result
    },
    // 是否显示删除 group 按钮
    displayDeleteGroupBtn() {
      return !this.widgetList.some((widget) => widget.ui_conf.required)
    },
    displayCount() {
      return this.widgetList.reduce((accWidget, widget) => {
        const status = this.getWidgetDisplayStatus(widget)

        let count = accWidget[widget.id] || 0
        if (status) {
          count += 1
        }

        return {
          ...accWidget,
          [widget.id]: count
        }
      }, {})
    },
    isFold() {
      return this.foldGroupIds.includes(this.group.id)
    }
  },
  watch: {
    displayCount: {
      immediate: true,
      handler(v) {
        this.$emit('displayCount', {
          groupId: this.groupId,
          count: Object.values(v).reduce((acc, curr) => acc + curr, 0)
        })
      }
    },
    isFold(value) {
      if (!value) {
        klook.bus.$emit('recalcItemDescDisplayReadMore')
      }
    }
  },
  methods: {
    ...mapMutations({
      insertCheckedTitlesOnGroup: 'attr/insertCheckedTitlesOnGroup',
      setCheckedTitlesOnGroup: 'attr/setCheckedTitlesOnGroup'
    }),
    scrollIntoView,
    getDisplayByWidget(widget) {
      return !this.foldGroupIds.includes(this.group.id) && !!this.displayCount[widget.id]
    },
    getWidgetItemRequired(widget) {
      const { ui_conf = {} } = widget
      const { ref_field_tag: fieldTag = '' } = ui_conf
      return !!this.$attrs?.['default-required-gourp-items']?.includes(fieldTag)
    },
    tplDataNotSaved() {
      const result = (this.$refs.widgetItem || [])
        .map((item) => item.tplDataNotSaved())
        .some((notSaved) => notSaved)

      if (!result) {
        this.$emit(
          'update:foldGroupIds',
          this.foldGroupIds.filter((groupId) => this.groupId !== groupId)
        )
      }

      return result
    },
    validateAttrValues(attr_values) {
      if (Array.isArray(attr_values) && attr_values.length > 0) {
        return attr_values.every((item) => {
          // 如果是freetext且没有值，则返回false,其他还是按照原来的逻辑
          if (item.is_free_text && !item.value) {
            return false
          }
          return !!attr_values.length
        })
      }
      return !!attr_values.length
    },
    async validateForm() {
      const widgetItems = this.$refs.widgetItem || []
      const widgetValid = (
        await Promise.all(
          widgetItems
            .filter((_widget, idx) => {
              // cancellation_policy 排除手动隐藏widget
              return !this.cpPassValidate(this.widgetList?.[idx]) && this.displayCount[_widget.widget.id]
            })
            .map((item) => item.validateForm())
        )
      ).every((valid) => valid)

      const fillValid =
        this.required && (_.sum(Object.values(this.displayCount)) || this.mandatoryGroupFill)
          ? widgetItems.some((ref) => {
              const { attr_values } = ref.widget
              // 如果组不是强制必填，并且组件未显示，跳过
              if (!this.mandatoryGroupFill && !ref.display) return true
              // 校验 attr_values 是否有效
              return this.validateAttrValues(attr_values)
            })
          : true
      const result = widgetValid && fillValid
      if (!result) {
        this.$parent?.handleUnfoldGroup?.({ groupId: this.groupId })

        if (widgetValid && !fillValid) {
          await this.$nextTick()
          this.scrollIntoView(this.$el, {
            type: 'scrollIntoView'
          })
        }
      }

      return result
    },
    handleFold() {
      this.$emit('fold', { groupId: this.groupId })
    },
    mouseenterGroup() {
      this.$emit('mouseenterGroup', this.groupId)
    },
    mouseleaveGroup() {
      this.$emit('mouseenterGroup', null)
    },
    async handleDeleteGroup() {
      const widgets = this.originalGroupData.groups_instance[this.groupId]
      this.rootChangeLoading(true)

      const res = await ajax.postBody(ADMIN_API.act[this.saveDraftDataUrlKey], {
        data: {
          ...this.commonDraftParams,
          // 草稿状态是更新，其余状态是插入
          op: 'delete',
          groups: [
            {
              ...this.commonGroupParams,
              widgets: widgets.reduce((acc, curr) => {
                if (curr.attr_values.length && !curr.attr_values.every((item) => item.published === -1)) {
                  acc.push({
                    id: curr.id,
                    order: curr.order || 0,
                    attr_values: curr.attr_values,
                    effective_time: curr.effective_time || null
                  })
                }

                return acc
              }, [])
            }
          ]
        }
      })

      this.rootChangeLoading(false)

      if (res) {
        this.setCheckedTitlesOnGroup({
          field: this.field,
          groupId: this.groupId,
          data: []
        })
        this.$emit('unselectGroup', { group: this.group })
      }
    },
    handleGroupUnfold() {
      this.$emit('groupUnfold', this.group)
    },
    // reqResult.groups_instance
    getRefData({ groupId, widgetType, instance_id }) {
      let refWidgetsData = this.refData?.[groupId] ?? []
      // #待优化-由于数据初始化逻辑不一致，导致详情页是 refWidgetsData 而基本信息页是 refWidgetsData.widgets
      let current = _.find(Array.isArray(refWidgetsData) ? refWidgetsData : refWidgetsData.widgets || [], {
        instance_id
      })

      if (widgetType && [1, 4].includes(widgetType)) {
        return _.get(current, 'attr_values', [])
      }

      return _.get(current, 'attr_values[0]', null)
    },
    getWidgetDisplayStatus(widget) {
      const { attr_value_list, config, access_permission, widget_type } = widget.ui_conf
      if (widget_type === 1) {
        const allHidden = attr_value_list.every((item) => {
          const curr = Object.assign({}, item, item.extendConf || {})

          return curr.hide || curr.access_permission === 0
        })
        const attr_values = widget.attr_values.filter(
          (attrVal) => !attr_value_list.find((item) => item.id !== attrVal.id)
        )

        if (
          (!attr_value_list.length || allHidden) && // 没有项可操作
          !attr_values.length && // 没有旧值需要展示
          !config.allow_free_text // 且不能主动增加 free text
        ) {
          return false
        }
      }

      return access_permission !== 0
    },
    validateWidgetRes(valid) {
      // !valid && this.handleDeleteGroup()
    },
    copyWidget({ group, data }) {
      const groupId = group.id
      // this.cacheWidgetsData.splice(index, 0, _.cloneDeep(widgetData));
      Object.assign(this.group, {
        total_widget_num: this.group.total_widget_num + 1
      })

      this.$message.success('Successfully copy.')
      this.insertCheckedTitlesOnGroup({
        field: this.field,
        groupId,
        data: [genUUID(data)]
      })
      this.$forceUpdate()
    },
    changeAttributeVal() {
      this.$emit('refreshSummary')
    },
    setGroupConfig(alert = '') {
      this.groupAlert = alert
    }
  }
}
</script>

<style lang="scss" scoped>
.group-item-container {
  .group-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 4px;
    justify-content: space-between;
    line-height: 32px;

    .group-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #000;
      user-select: none;
      cursor: pointer;

      &:hover {
        background-color: #edf5ff;
      }

      .group-rectangle {
        height: 24px;
        margin-right: 16px;
        border-left: 4px solid #0091ff;
        border-radius: 1px;
      }

      .group-name {
        font-size: 24px;
        font-weight: 600;
      }

      .group-operator {
        display: flex;
        align-items: center;

        .unselect-btn {
          display: none;
          margin-right: 12px;
          color: #ff4d4f;
          z-index: 101;
        }

        .fold-btn {
          padding: 4px 6px;
          text-align: center;
          border-radius: 4px;
          transition: background-color 0.6s, color 0.6s;
          user-select: none;
          cursor: pointer;
          z-index: 999;
        }
      }

      &:hover .group-operator .unselect-btn {
        display: block;
      }
    }

    .group-desc {
      margin-top: 4px;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.65);
    }
  }

  .group-widget-list {
    border-radius: 4px;
    // 折叠状态
    &.is-fold {
      // padding: 8px;
      cursor: pointer;

      &:hover {
        background-color: #edf5ff;
      }

      .group-widget-box {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .group-widget-box {
      border-radius: 4px;

      &.has-shading {
        background-color: #fafafa;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    &.is-fold .has-shading .fold-to-preview-container {
      padding: 16px 8px;
    }
  }

  .group-widget-item {
    margin-top: 4px;

    &:first-child {
      margin-top: 0;
    }
  }
}
</style>
