<template>
  <div class="group-list-container">
    <header class="header-title">{{ $t('80685') }}</header>

    <div class="search-box">
      <a-input-search
        v-model="search.value"
        class="input-search"
        placeholder="search here"
        allow-clear
        @change="handleSearch"
      />
    </div>

    <div class="expand-all-btn" @click="handleExpandAll">
      {{ foldGroupIds.length !== currGroupList.length ? $t('act_fold_all') : $t('act_unfold_all') }}
    </div>

    <div class="group-list">
      <div
        v-for="group of currGroupList"
        :key="group.id"
        class="group-item"
        :class="{ 'clear-pointer': !group.hasWidget }"
      >
        <div class="group-content">
          <div class="group-name-box" @click="handleSwitchUnfoldGroup(group)">
            <a-icon
              class="unfold-btn"
              :type="foldGroupIds.includes(group.id) ? 'caret-right' : 'caret-down'"
              :style="{
                visibility: group.hasWidget ? 'initial' : 'hidden'
              }"
            />
            <label class="group-name">
              <span v-if="getRequiredOnGroup(group)" class="--is-required">*</span>
              {{ group.name }}
            </label>
          </div>
          <DescMarkdownContent
            class="group-desc"
            placement="right"
            :is-inline="false"
            :desc="group.desc"
            :line-clamp="2"
          />
        </div>

        <div class="group-titles">
          <div
            v-for="(title, index) of getTitleList(group)"
            :key="`${title.id}_${index}`"
            class="group-title"
            @click="handleGoToTitle({ group, title })"
          >
            <a-tooltip
              placement="right"
              overlay-class-name="common-tooltip-style"
              :disabled="!getTitleTooltipDisplay(title)"
            >
              <template v-if="getTitleTooltipDisplay(title)" slot="title">
                <span v-html="getTitleTooltipContent(title)"></span>
              </template>
              <div
                class="title-item js-scroll-to-view"
                :data-is-suggest-filling="title.ui_conf.config.suggest_filling"
                :data-is-checked="String(isOnChecked({ group, title }))"
              >
                <a-checkbox
                  :id="title.uuid"
                  :checked="isOnChecked({ group, title })"
                  :disabled="getTitleCheckboxDisabled({ group, title })"
                  @change="(evt) => changeTitleChecked(evt.target.checked, { group, title })"
                ></a-checkbox>

                <label :for="title.uuid" class="title-name">
                  <span v-if="!!title.ui_conf.required" class="--is-required">*</span>
                  <span class="--name">{{ title.ui_conf.name }}</span>
                  <span v-if="title.ui_conf.config.suggest_filling" class="--is-tag">
                    {{ $t('80674') }}
                  </span>
                </label>

                <svg-icon v-if="foldGroupIds.includes(group.id)" class="pin-btn" icon-name="pin" />
              </div>
            </a-tooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapMutations } from 'vuex'
import DescMarkdownContent from '@activity/pages/activityManagement/detailV2/components/descMarkdownContent'
const markdownIt = require('markdown-it')().disable(['code'])
import { genUUID } from '@activity/utils'
import { scrollIntoView } from '@activity/pages/activityManagement/detailV2/utils'
import draft2prod_mixin from '@activity/pages/activityManagement/detailV2/draft2prod_mixin'

export default {
  name: 'GroupList',
  components: {
    DescMarkdownContent
  },
  inject: {
    getAllVariables: {
      default: () => []
    },
    getWidgetDefaultData: {
      default: () => {}
    }
  },
  mixins: [draft2prod_mixin],
  props: {
    originalGroupData: {
      type: Object,
      required: true
    },
    groupList: {
      type: Array,
      required: true
    },
    field: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      search: {
        value: ''
      },

      foldGroupIds: []
    }
  },
  computed: {
    allVariables() {
      return this.getAllVariables()
    },
    currGroupList() {
      const { groups_summary, groups_instance } = this.originalGroupData
      const searchVal = this.search.value.toLowerCase()

      return (groups_summary || []).reduce((acc, group) => {
        group = _.cloneDeep(group)
        const curr = this.originalGroupData.groups_instance[group.id]
        const required = curr.some((widget) => widget.ui_conf.required)
        const hasWidget = !!curr.length
        const suggestFilling = curr.some((widget) => !!widget.ui_conf.config?.suggest_filling)
        let titleList = groups_instance[group.id].filter((title) => {
          return title.ui_conf.access_permission !== 0
        })

        let groupData = {
          ...group,
          required,
          hasWidget,
          suggestFilling,
          titleList
        }

        if (group.name.toLowerCase().indexOf(searchVal) !== -1) {
          return [...acc, groupData]
        }

        titleList = groups_instance[group.id].filter(
          (title) => title.ui_conf.name.toLowerCase().indexOf(searchVal) !== -1
        )

        if (titleList.length) {
          return [...acc, { ...groupData, titleList }]
        }

        return acc
      }, [])
    },
    checkedTitlesOnGroup() {
      return this.$store.getters['attr/checkedTitlesOnGroupGetter']({
        field: this.field
      })
    }
  },
  methods: {
    genUUID,
    scrollIntoView,
    ...mapMutations({
      setCheckedTitlesOnGroup: 'attr/setCheckedTitlesOnGroup',
      insertCheckedTitlesOnGroup: 'attr/insertCheckedTitlesOnGroup',
      removeCheckedTitlesOnGroup: 'attr/removeCheckedTitlesOnGroup'
    }),
    getConfigRequired(group) {
      let data = {}
      try {
        data = JSON.parse(group?.config || '{}')
      } catch (e) {
        console.error('Invalid JSON string:', group?.config)
      }
      return data['group_need_one_title'] === 1
    },
    getRequiredOnGroup(group) {
      const widgets = this.getTitleList(group)

      return widgets.some((widget) => !!widget.ui_conf?.required) || this.getConfigRequired(group)
    },
    handleExpandAll() {
      let foldGroupIds = this.foldGroupIds
      if (this.foldGroupIds.length === this.currGroupList.length) {
        foldGroupIds = []
      } else {
        foldGroupIds = this.currGroupList.map((group) => group.id)
      }

      this.$set(this, 'foldGroupIds', foldGroupIds)
    },
    handleGoToTitle({ group, title }) {
      if (this.foldGroupIds.includes(group.id)) {
        this.scrollIntoView(`.group-widget-item[data-uuid=${title.uuid}]`)
      }
    },
    getTitleList(group) {
      if (this.foldGroupIds.includes(group.id)) {
        return group.titleList.filter((title) => this.isOnChecked({ group, title }))
      }

      return group.titleList
    },
    handleSwitchUnfoldGroup(group) {
      const groupId = group.id
      let foldGroupIds = _.cloneDeep(this.foldGroupIds)

      if (this.foldGroupIds.includes(groupId)) {
        foldGroupIds = this.foldGroupIds.filter((item) => item !== groupId)
      } else {
        foldGroupIds.push(group.id)
      }

      this.$set(this, 'foldGroupIds', foldGroupIds)
    },
    handleSearch() {},
    isOnChecked({ group, title }) {
      return this.checkedTitlesOnGroup?.[group.id]?.includes(genUUID(title))
    },
    getTitleCheckboxDisabled({ group, title }) {
      return (
        this.foldGroupIds.includes(group.id) ||
        !!title.ui_conf.required ||
        title.ui_conf.access_permission === 1
      )
    },
    getDesc(desc) {
      return desc && markdownIt.render(desc)
    },
    getTitleTooltipDisplay(title) {
      return Boolean(title.ui_conf.desc || title.ui_conf.attr_value_list.length)
    },
    getTitleTooltipContent(title) {
      const desc = title.ui_conf.desc || (title.ui_conf.config.allow_free_text ? this.$t('80892') : '')
      const descHtml = desc
        ? `<div class="desc" style="margin-top: 12px; padding: 12px;; background-color: #fafafa; border-radius: 4px;">${markdownIt.render(
            desc
          )}</div>`
        : ''
      const ulHtml = title.ui_conf.attr_value_list.reduce((acc, curr) => {
        acc = acc + '<li>' + curr.value + '</li>'

        return acc
      }, '')

      return `<div>${descHtml}<ul style="padding: 12px 24px; list-style: disc; line-height: 2em;">${ulHtml}</ul></div>`
    },
    getCheckedByGroup(group) {
      return !!this.groupList.find((item) => item.id === group.id)
    },
    async changeTitleChecked(checked, { group, title }) {
      const order = title.order
      const groupId = group.id
      const uuid = genUUID(title)
      const data = {
        field: this.field,
        groupId,
        data: [uuid]
      }

      if (checked) {
        // 重置默认值
        Object.assign(title.attr_values, this.getWidgetDefaultData(title).attr_values)
        this.insertCheckedTitlesOnGroup(data)
        klook.bus.$emit(`${this.field}_UnfoldGroup`, { groupId })
        setTimeout(() => {
          this.scrollIntoView(`.group-widget-item[data-uuid=${title.uuid}]`)
        }, 60)
      } else {
        this.removeCheckedTitlesOnGroup(data)
        // 若存在复制的重复id, 直接移除
        this.originalGroupData.groups_instance[groupId] = this.originalGroupData.groups_instance[
          groupId
        ].reduce((acc, curr, index, array) => {
          if (
            genUUID(curr) === uuid &&
            // 存在多个重复的 id (表示复制过) 需要清除
            array.filter((item) => item.id === curr.id).length > 1
          ) {
            return acc
          }

          return [...acc, curr]
        }, [])
      }

      this.rootChangeLoading(true)

      // widget_type: 1,// widget 类型：1-Attribute 2-Single Text 3-Markdown 4-Image 5-Address, 6: duration
      if (checked && [2, 3, 4, 5, 6].includes(title.ui_conf.widget_type)) {
        this.rootChangeLoading(false)
        return
      }

      // // 6: duration 默认情况不需要操作
      await ajax.postBody(ADMIN_API.act[this.saveDraftDataUrlKey], {
        data: {
          language: this.edit_lang,
          activity_id: this.activity_id,
          op: checked ? 'insert' : 'delete',
          groups: [
            {
              extra_id: this.extra_id,
              extra_type: this.extra_type,
              group_id: groupId,
              widgets: [
                {
                  id: title.id,
                  order,
                  attr_values: title.attr_values
                }
              ]
            }
          ]
        }
      })
      klook.bus.$emit('refreshPreview')

      this.rootChangeLoading(false)
    }
  }
}
</script>

<style lang="scss" scoped>
$hoverBackgroundColor: #edf5ff;

.group-list-container {
  height: 100%;
  padding: 0 24px;
  overflow-y: auto;
  overflow-x: hidden;

  .header-title {
    margin-bottom: 21px;
    font-weight: 600;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
  }

  .search-box {
    position: sticky;
    top: 0;
    height: 32px;
    background-color: #fff;
    z-index: 999;
  }

  .expand-all-btn {
    margin-top: 16px;
    cursor: pointer;
    color: #2073f9;
  }

  .group-list {
    position: relative;
    width: calc(100% + 48px);
    margin-left: -24px;
    z-index: 99;
  }

  .group-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    padding: 4px 24px;
    cursor: pointer;

    &:last-child {
      margin-bottom: 0;
    }

    &.clear-pointer {
      cursor: initial;
      .group-name {
        cursor: initial;
      }
    }

    .--is-required {
      color: red;
    }

    .--name {
      display: inline-block;
      margin-right: 8px;
    }

    .group-name {
      font-weight: 600;
      font-size: 16px;
    }

    .--is-tag {
      display: inline-block;
      padding: 1px 8px;
      background: #f0f7ff;
      border: 1px solid #bdd8ff;
      border-radius: 2px;
      font-size: 12px;
      font-weight: 400;
      color: #437dff;
    }
  }

  .group-content {
    position: sticky;
    top: 32px;
    flex: 1;
    width: 100%;
    margin: 0 8px;
    background-color: #fff;
    padding: 4px 0;
    z-index: 999;

    .group-name-box {
      &:hover {
        background-color: $hoverBackgroundColor;
      }
    }

    .group-desc {
      margin-top: 4px;
    }

    .group-desc {
      margin-left: 22px;
      cursor: initial;
    }

    .unfold-btn {
      margin-right: 8px;
    }
  }

  .group-titles {
    width: 100%;
    .group-title {
      margin-top: 12px;
      padding: 4px 12px 4px 20px;

      &:first-child {
        margin-top: 0;
      }

      &:hover {
        background-color: $hoverBackgroundColor;
      }

      ::v-deep {
        .ant-checkbox-wrapper {
          display: flex;
          align-items: flex-start;
        }

        .ant-checkbox {
          margin-top: 4px;
        }
      }

      .title-overflow {
        display: block;
        width: 208px;
      }
    }
  }

  .title-item {
    display: flex;
    align-items: flex-start;

    ::v-deep .ant-checkbox-wrapper {
      margin-right: 8px;
    }

    .title-name {
      flex: 1;
    }

    .pin-btn {
      font-size: 12px;
    }
  }
}
</style>
