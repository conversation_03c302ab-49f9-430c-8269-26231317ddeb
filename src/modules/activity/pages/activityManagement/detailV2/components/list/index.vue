<template>
  <a-drawer
    class="chose-list-container"
    :visible.sync="_visible"
    :width="drawerWidth"
    :closable="true"
    :mask="false"
    :get-container="containerEle"
    :header-style="{
      position: 'relative',
      height: '80px'
    }"
    :body-style="{
      height: 'calc(100vh - 80px)',
      padding: '12px 0 48px'
    }"
    placement="left"
    @close="onClose"
  >
    <template #title>
      <span class="title">{{ $t('80684') }}</span>
    </template>

    <GroupList :original-group-data="originalGroupData" :group-list.sync="groupList" :field="field" />
  </a-drawer>
</template>

<script>
import GroupList from './groupList'
import { isMerchant } from '@/env'

export default {
  name: 'ChosenList',
  components: {
    GroupList
  },
  model: {
    prop: 'groupList',
    event: 'change'
  },
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    groupList: {
      type: Array,
      default: () => []
    },
    field: {
      type: String,
      required: true
    },
    originalGroupData: {
      type: Object,
      required: true,
      validator: function(data) {
        return ['groups_summary', 'groups_instance', 'groups_schema'].every((key) =>
          hasOwnProperty.call(data, key)
        )
      }
    }
  },
  data() {
    return {}
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(v) {
        this.$emit('update:visible', v)
      }
    },
    drawerWidth() {
      return isMerchant ? 290 : 342
    }
  },
  methods: {
    containerEle() {
      return document.querySelector('#app')
    },
    onClose() {
      this._visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.chose-list-container {
  .title {
    display: block;
    padding-right: 24px;
  }

  ::v-deep {
    .ant-tabs-content {
      height: 100%;
    }

    .ant-tabs-bar {
      display: none;
    }

    .ant-tabs {
      height: 100%;
    }
  }
}
</style>
