<template>
  <div class="wrapper" :class="{ 'display-read-more': isInline && displayReadMore }">
    <div class="overflow-box" :style="`--line-clamp: ${lineClamp}; width: 100%;`">
      <a-tooltip
        v-if="isInline && displayReadMore"
        :overlay-class-name="currOverlayClassName"
        :placement="placement"
        trigger="click"
      >
        <template slot="title">
          <span v-html="tooltip"></span>
        </template>
        <span type="link" class="btn margin-l">{{ readMoreText }}</span>
      </a-tooltip>
      <span ref="text" class="text"><slot /></span>
    </div>

    <a-tooltip
      v-if="!isInline && displayReadMore"
      style="max-width: 600px;"
      :overlay-class-name="currOverlayClassName"
      :placement="placement"
      trigger="click"
    >
      <template slot="title">
        <span v-html="tooltip"></span>
      </template>
      <span type="link" class="btn">{{ readMoreText }}</span>
    </a-tooltip>
  </div>
</template>

<script>
// 如果文字超出，则显示点点点，并且有tooltip
// 如果文字没有超出，则没有点点点，也没有tooltip
export default {
  name: 'OverflowTextTooltip',
  props: {
    overlayClassName: {
      type: String,
      default: 'common-tooltip-style'
    },
    placement: {
      type: String,
      default: 'top'
    },
    lineClamp: {
      type: Number,
      default: 1
    },
    readMoreText: {
      type: String,
      default: 'Learn more'
    },
    isInline: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      show: false,
      tooltip: '',
      displayReadMore: false
    }
  },
  computed: {
    currOverlayClassName() {
      return this.overlayClassName + ' overflow-text-width'
    }
  },
  mounted() {
    this.calcShouldBeDisplayReadMore()
    klook.bus.$on('recalcItemDescDisplayReadMore', this.calcShouldBeDisplayReadMore)
    this.$once('hook:beforeDestroy', () => {
      klook.bus.$off('recalcItemDescDisplayReadMore', this.calcShouldBeDisplayReadMore)
    })
  },
  methods: {
    async calcShouldBeDisplayReadMore() {
      await this.$nextTick()
      const domText = this.$refs.text

      if (
        domText &&
        window.getComputedStyle(domText).overflowY === 'visible' &&
        domText.getBoundingClientRect().height > 14 * 1.5 * this.lineClamp // font-size: 14, line-height: 1.5em
      ) {
        this.tooltip = domText.innerHTML
        this.displayReadMore = true
      }
    },
    handleMouseLeave() {
      this.show = false
    }
  }
}
</script>

<style lang="scss">
.ant-tooltip.overflow-text-width {
  max-width: 500px;
  ul {
    padding-left: 20px;
    list-style: disc;
  }
}
</style>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  flex-direction: column;
  overflow: hidden;

  &.display-read-more {
    max-height: 3em;

    .overflow-box {
      max-height: calc(var(--line-clamp) * 1.5em);
    }
  }

  .overflow-box {
    position: relative;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    // text-align: justify;
    -webkit-line-clamp: var(--line-clamp);
    -webkit-box-orient: vertical;
    font-size: 14px;
    line-height: 1.5em;
    color: #757575;

    &::before {
      content: '';
      height: calc(100% - 24px);
      float: right;
    }
    &::after {
      content: '';
      width: 999vw;
      height: 999vw;
      position: absolute;
      box-shadow: inset calc(100px - 999vw) calc(30px - 999vw) 0 0 #fff;
      margin-left: -100px;
    }
  }

  .btn {
    width: fit-content;
    float: right;
    clear: both;
    border-radius: 4px;
    color: #2073f9;
    cursor: pointer;

    &:hover {
      text-decoration-line: underline;
    }

    &.margin-l {
      margin-left: 10px;
    }
  }
}
</style>
