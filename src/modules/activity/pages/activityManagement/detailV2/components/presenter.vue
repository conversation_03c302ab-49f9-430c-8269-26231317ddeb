<template>
  <div
    v-disabled="vDisabled"
    class="presenter-container"
    :class="{ 'is-display-preview': shouldDisplayPreview }"
  >
    <a-spin :spinning="spinning">
      <label class="header common-box">
        <slot name="header"></slot>
      </label>
      <slot name="extra">
        <div class="add-group-btn common-box" @click="handleDisplaySectionList">
          <a-icon type="edit" style="margin-right: 6px" />
          {{ $t('80673') }}
        </div>

        <div v-if="groupList.length" class="operator common-box">
          <div class="btns">
            <slot name="foldBtn" :to-unfold-all="toUnfoldAll">
              <span class="fold-btn" @click="handleUnfoldAll(toUnfoldAll)">
                {{ toUnfoldAll ? $t('act_unfold_all') : $t('act_fold_all') }}
              </span>
            </slot>
          </div>
          <div v-if="displayPreviewOnTTD" class="to-c-preview-btn">
            {{ $t('80680') }}
            <a-switch v-model="toCPreview.visible" size="small"></a-switch>
          </div>
        </div>
        <a-empty v-else-if="!groupList.length || !displayContent" style="margin-top: 120px">
          <span slot="description">
            {{ emptyHTML.front }}
            <a-button type="link" style="padding: 0 4px" @click="handleDisplaySectionList">
              {{ emptyHTML.btn }}
            </a-button>
            {{ emptyHTML.behind }}
          </span>
        </a-empty>
      </slot>

      <div class="content-container">
        <Content
          ref="content"
          field="detail"
          class="content"
          :original-group-data="originalGroupData"
          :group-list="groupList"
          :all-variables="allVariables"
          :has-draft-state="true"
          :ref-data="refWidgetsData"
          :first-initial-fold.sync="firstInitialFold"
          :readonly="readonly"
          :is-create="isCreate"
          @unselectGroup="unselectGroup"
          @changeFoldGroupIds="changeFoldGroupIds"
          @changeDisplayContent="changeDisplayContent"
        >
          <template v-if="groupList.length && hasScrolled" #footer>
            <div class="footer-go-ahead">
              {{ $t('82783') }}
              <slot name="addMoreContentBtn">
                <a-button type="primary" size="small" @click="handleDisplaySectionList">
                  {{ $t('82784') }}
                </a-button>
              </slot>
            </div>
          </template>
        </Content>

        <div class="preview"></div>
      </div>
    </a-spin>

    <List
      v-if="groupSelectionData.visible"
      :group-list="groupList"
      :visible.sync="groupSelectionData.visible"
      v-bind="groupSelectionData"
      :display-back-group-list="true"
    />

    <ToCPreView
      v-if="shouldDisplayPreview"
      ref="toCPreView"
      :visible.sync="toCPreview.visible"
      v-bind="toCPreview"
    />

    <CommonFooter
      class="js-presenter-footer-wrapper"
      :style="{ marginRight: shouldDisplayPreview ? '352px' : 0 }"
    >
      <a-button v-if="hasEventLog" type="link" style="margin: 0" @click="handleGoEventLog">
        {{ $t('activity_event_logs') }}
      </a-button>

      <a-popconfirm
        v-if="hasDraftData"
        :title="$t('48085')"
        ok-text="Yes"
        cancel-text="No"
        @confirm="confirmClearDraft"
      >
        <a-button>
          {{ $t('80679') }}
        </a-button>
      </a-popconfirm>
      <a-button
        v-bind="calcSaveData"
        class="js-save-tooltips js-edit-save-btn"
        type="primary"
        @click="handleNext"
      >
        {{ $t('global_save') }}
      </a-button>
    </CommonFooter>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations, mapActions } from 'vuex'
import {
  getEditLang,
  getRefLang
  // getLangTitle,
  // scrollElError
} from '@activity/utils'
import common_mixin from '@activity/pages/activityManagement/detailV2/common_mixin'
import { ACTIVATE_EDITING_EVENT_KEY, scrollIntoView } from '@activity/pages/activityManagement/detailV2/utils'
import { INTERCEPTOR_BTN_TYPES } from '@activity/utils/const'

import List from './list/index.vue'
import Content from './content.vue'
import ToCPreView from './toCPreview.vue'

import CommonFooter from '@activity/components/CommonFooter.vue'
import { genUUID } from '../../../../utils'
import { checkAuth } from '@/plugins/authDirective'

// https://kamranahmed.info/driver.js/
import Driver from 'driver.js'
import 'driver.js/dist/driver.min.css'
import Vue from 'vue'
import DriverTitle from './driverTitle.vue'
import { getUuid } from '@activity/utils'

import footer_transitionend_mixin from '../mixins/footerTransitionend'
import { getMerchantEditSaveSpm } from '@activity/pages/activityManagement/dataLib.vue'

const driverStackingClassName = 'js-class-driver-fix-stacking-container'

const driver_mixin = {
  destroyed() {
    this.driver = null
  },
  props: {
    noReminderStorageKey: {
      type: String,
      default: 'act_detail_driver_no_reminder'
    }
  },
  data() {
    this.getAddMoreGroupsStep = (isEmpty = false) => {
      return {
        element: '.add-group-btn',
        popover: {
          title: isEmpty ? __('80690') : __('80687')
        },
        preFunc: () => {
          // 隐藏导航球，避免遮挡
          const $guideBall = document.querySelector('.guide-ball-container')
          $guideBall && ($guideBall.style.display = 'none')

          return Promise.resolve()
        }
      }
    }
    this.getChoseListStep = (isEmpty = false) => {
      return {
        element: '.group-list-container',
        popover: {
          title: isEmpty ? __('80691') : __('80688'),
          position: 'right',
          padding: 0
        },
        preFunc: () => {
          return new Promise((resolve) => {
            this.handleDisplaySectionList()
            setTimeout(async () => {
              await this.$nextTick()
              resolve()
            }, 360) // 等抽屉动画完成
          })
        },
        doneFunc: () => {
          this.setGroupSelectionStore({
            visible: false
          })
          localStorage.setItem(this.noReminderStorageKey, true)
          // 导航球复原
          const $guideBall = document.querySelector('.guide-ball-container')
          $guideBall && ($guideBall.style.display = 'block')
        }
      }
    }
    this.widgetItemStep = {
      element: '.group-widget-box',
      popover: {
        title: __('80689')
      }
    }

    return {}
  },
  computed: {
    ...mapGetters([
      'isPublishWithAI',
      'saveBeforeStatus2getters',
      'lockMerchantEditFreeText2getter',
      'activityIsUnablePreview'
    ]),
    calcSaveData() {
      const { name } = this.$route
      const spm = getMerchantEditSaveSpm(name)
      if (!spm) {
        return {}
      }
      const obj = {
        'data-spm-module': `${spm}`,
        'data-spm-virtual-item': '__virtual?trg=manual'
      }
      return obj
    },
    driverSteps() {
      const { getAddMoreGroupsStep, getChoseListStep, widgetItemStep } = this

      if (this.groupList.length) {
        return [widgetItemStep, getAddMoreGroupsStep(), getChoseListStep()]
      }

      return [getAddMoreGroupsStep(true), getChoseListStep(true)]
    }
  },
  methods: {
    noReminderDriver() {
      return localStorage.getItem(this.noReminderStorageKey) === 'true'
    },
    setHasScrolled: _.debounce(function () {
      this.$nextTick(() => {
        this.hasScrolled = document.body.scrollHeight > document.body.clientHeight
      })
    }, 30),
    startDriver() {
      if (this.noReminderDriver()) {
        return
      }

      this.runDriver(0)
    },
    isDomElement(element) {
      return element && typeof element === 'object' && 'nodeType' in element
    },
    async runDriver(currStepIndex = 0) {
      this.currStepTitleVMInstance = null
      this.currStepIndex = currStepIndex

      const driver = this.getDriverInstance()
      const currStep = this.driverSteps[currStepIndex]
      const total = this.driverSteps.length
      const container = document.createElement('div')
      const id = 'driver_uuid_' + getUuid()
      container.setAttribute('id', id)

      driver.reset()

      if (this.isFunc(currStep.preFunc)) {
        await currStep.preFunc()
      }

      let element
      if (typeof currStep.element === 'string') {
        element = document.querySelector(currStep.element)
      }

      if (!this.isDomElement(element)) {
        this.handleDriverNext()
        return
      }

      driver.highlight({
        ...currStep,
        element,
        popover: {
          ...currStep.popover,
          title: container.outerHTML
        }
      })

      setTimeout(async () => {
        await this.$nextTick()
        const span = document.createElement('span')
        document.querySelector('#' + id).appendChild(span)
        this.currStepTitleVMInstance = this.getDriverTitleInstance(span, {
          index: this.currStepIndex,
          total,
          content: currStep.popover?.title ?? '',
          btnTxt: this.currStepIndex === total - 1 ? this.$t('80693') : this.$t('global.user_guide_next')
        })
      })
    },
    isFunc(value) {
      return ['[object Function]', '[object AsyncFunction]'].includes(toString.call(value))
    },
    async handleDriverNext() {
      const preStep = this.driverSteps[this.currStepIndex++]
      const currStep = this.driverSteps[this.currStepIndex]

      if (this.isFunc(preStep?.doneFunc)) {
        await preStep.doneFunc()
      }

      if (!currStep) {
        this.driver?.reset?.()
        this.currStepTitleVMInstance = null
        return
      }

      await this.runDriver(this.currStepIndex)
    },
    getDriverInstance() {
      if (!this.driver) {
        this.driver = new Driver({
          allowClose: false,
          showButtons: false,
          overlayClickNext: false,
          padding: 4
        })

        this.currStepIndex = 0
      }

      return this.driver
    },
    getDriverTitleInstance(container, { index, total, content, btnTxt }) {
      const componentClass = Vue.extend(DriverTitle)

      return new componentClass({
        propsData: {
          config: {
            index,
            total,
            content,
            btnTxt
          },
          onNext: this.handleDriverNext
        }
      }).$mount(container)
    }
  }
}

export default {
  name: 'DetailPresenter',
  components: {
    List,
    Content,
    ToCPreView,
    CommonFooter
  },
  mixins: [common_mixin, driver_mixin, footer_transitionend_mixin],
  inject: [
    'refreshPkgs',
    'refreshPage',
    'page_from',
    'handleGTMBtn',
    'setAlertFixed2provide',
    'rootChangeLoading',
    'waitPromiseQueueFinCallback',
    'beforeInterceptorConfirm',
    'setBeforeEachSaveConfirmInterceptor'
  ],
  provide() {
    // `provide` can be hoisted up in higher component
    return {
      activity_id: this.activityId,
      ref_lang: getRefLang(),
      edit_lang: getEditLang(),
      locale_info: this.locale_info,
      extra_id: this.extraType === 'ACT' ? this.activityId : this.packageId,
      extra_type: this.extraType,
      getAllVariables: () => this.allVariables,
      getWidgetDefaultData: this.getWidgetDefaultData
    }
  },
  props: {
    extraType: {
      type: String,
      required: true,
      validator: function (value) {
        return ['ACT', 'PKG'].includes(value)
      }
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      displayContent: true,
      allVariables: [],

      locale_info: {
        react_data: []
      },

      originalGroupData: {
        groups_summary: [],
        groups_instance: {},
        groups_schema: {}
      },
      // ref language
      refWidgetsData: [],
      refAllVariables: [],

      field: 'detail',
      // groupList: [],

      isCreate: false,

      foldGroupIds: [],
      spinning: false,

      toCPreview: {
        visible: false,
        activityId: 0,
        packageId: 0
      },

      // 有生成新草稿数据
      hasChangeDraftData: false,
      // 生成草稿数据之后有点保存按钮
      hasSaveDraftData2Pub: false,

      // 判断是否存在草稿，进而控制显示清除草稿按钮
      hasDraftData: false,
      // 容器是否存在滚动条
      hasScrolled: false,
      // 第一次初始化的时候是否需要折叠 group
      firstInitialFold: false
    }
  },
  computed: {
    ...mapState({
      groupSelectionData: (state) => state.attr.groupSelectionData,
      lockMerchantActEditing: 'lockMerchantActEditing'
    }),
    shouldDisplayPreview() {
      return this.displayPreviewOnTTD && this.toCPreview.visible && !this.activityIsUnablePreview
    },
    // 临时屏蔽非exp类目的 admin preview
    displayPreviewOnTTD() {
      return [1, 2, 3, 5, 103].includes(this.$root.template_id) && !this.activityIsUnablePreview
    },
    hasEventLog() {
      let { name } = this.$route

      return (
        !this.pagePlatformRole === 'merchant' &&
        ['detail', 'packageDetail'].includes(name) &&
        checkAuth(['actEventLogEntry'])
      )
    },
    vDisabled() {
      if (this.lockMerchantActEditing) {
        return {
          lock: true,
          scope: 'all'
        }
      }

      if (this.lockMerchantEditFreeText2getter) {
        return {
          lock: true,
          deleteElements: [
            {
              selector: 'compound-extend__btn',
              force: true
            }
          ],
          defaultScope: 'freeText'
        }
      }

      return {
        lock: false
      }
    },
    emptyHTML() {
      const res = this.$t('82553').split(/{(.+)}/)

      return {
        front: res[0] || '',
        btn: this.$t('28690'), // add
        behind: res[2] || ''
      }
    },
    toUnfoldAll() {
      return this.foldGroupIds.length === this.groupList.length
    },
    extra_id() {
      return this.extraType === 'ACT' ? this.activityId : this.packageId
    },
    activityId() {
      return +this.$route.params.id
    },
    packageId() {
      return +this.$route.query.package_id
    },
    refLang() {
      return getRefLang()
    },
    categoryId() {
      return this.$store.state.categoryInfo?.sub_category_id
    },
    checkedTitlesOnGroup() {
      return this.$store.getters['attr/checkedTitlesOnGroupGetter']({
        field: this.field
      })
    },
    groupList() {
      const { groups_summary } = this.originalGroupData

      return (groups_summary || []).reduce((acc, curr) => {
        const groupId = curr.id

        if (this.checkedTitlesOnGroup[groupId]?.length) {
          return [...acc, curr]
        }

        return acc
      }, [])
    }
  },
  watch: {
    foldGroupIds: {
      deep: true,
      handler() {
        this.setHasScrolled()
      }
    },
    categoryId: {
      immediate: true,
      async handler(v) {
        if (v) {
          this.spinning = true
          await this.getCurrWidgetsData()
          this.getRefWidgetsData()
          this.spinning = false
          this.$emit('inited')
        }
      }
    },
    'toCPreview.visible'(v) {
      if (v) {
        Object.assign(this.toCPreview, {
          activityId: this.activityId,
          packageId: this.extraType === 'ACT' ? 0 : this.packageId
        })

        this.hasDraftData = true

        // 有修改需要激活底部
        this.setActivateFooterWrapper(true)
      }
    }
  },
  async created() {
    if (!this.$root.nonEdit2act) {
      this.setBeforeEachSaveConfirmInterceptor({
        confirmSave: {
          fn: this.handleNext.bind(this, false, { saveStatus: 'confirm-status' }),
          params: false
        },
        customConf: {
          before: this.beforeUnload.bind(this)
        }
      })
    }

    this.setGroupSelectionStore()
    klook.bus.$off('refreshPreview').$on('refreshPreview', this.refreshPreview)
  },
  async mounted() {
    document.body.classList.add(driverStackingClassName)
    this.locale_info.react_data = await ajax.get(ADMIN_API.act.get_activity_status_by_langs, {
      params: {
        activity_id: this.activityId,
        language_list: getEditLang()
      }
    })
  },
  beforeDestroy() {
    document.body.classList.remove(driverStackingClassName)
    klook.bus.$off('refreshPreview')
    this.groupSelectionData.visible = false
  },
  methods: {
    ...mapActions(['getActVariables2actions', 'updateActStepStatus2action', 'updatePkgStepStatus2action']),
    ...mapMutations({
      setGroupSelectionStore: 'attr/setGroupSelectionStore',
      setBeforeEachInterceptor: 'setBeforeEachInterceptor',
      clearBeforeEachInterceptor: 'clearBeforeEachInterceptor',
      setCheckedTitlesOnGroup: 'attr/setCheckedTitlesOnGroup',
      insertCheckedTitlesOnGroup: 'attr/insertCheckedTitlesOnGroup',
      setActivateFooterWrapper: 'setActivateFooterWrapper'
    }),
    scrollIntoView,
    getDraftDataDom() {
      return document.querySelectorAll('.data-publish-dot-style-container.is-draft-data')
    },
    handleGoEventLog() {
      let url = `/${window.KLK_LANG_PATH}act/activity/event_logs/${this.actGlobal.activity_id}`
      let query = {
        edit_flag: '1,2,3'
      }
      let { package_id } = this.$route.query
      if (!isNaN(package_id)) {
        query.package_id = package_id
      }
      query = new URLSearchParams(query).toString()

      window.open(`${url}?${query}`)
    },
    async confirmClearDraft() {
      const urlKey =
        this.extraType === 'ACT'
          ? 'reset_activity_instance_draft_data_by_group_ids'
          : 'reset_package_instance_draft_data_by_group_ids'

      const response = await ajax.postBody(ADMIN_API.act[urlKey], {
        data: {
          ...this.commonDraftParams,
          extra_id: this.extra_id,
          extra_type: this.extraType,
          group_ids: this.groupList.map((group) => group.id)
        }
      })

      if (response) {
        this.$message.success('Successfully clear.')
        this.hasDraftData = false
        this.setActivateFooterWrapper(false)
        this.refreshPage()
      }
    },
    refreshPreview() {
      this.setHasScrolled()

      if (this.toCPreview.visible) {
        this.$refs.toCPreView?.refreshPreview?.()
      } else {
        this.toCPreview.visible = true
      }

      this.hasSaveDraftData2Pub = false
      this.hasChangeDraftData = true
    },
    unselectGroup({ group }) {
      const groupId = group.id
      this.setCheckedTitlesOnGroup({
        field: this.field,
        groupId,
        data: []
      })
    },
    handleUnfoldAll(unfoldAll = true) {
      if (unfoldAll) {
        this.$refs.content?.unfoldAllGroups?.()
        klook.bus.$emit(ACTIVATE_EDITING_EVENT_KEY.activate)
      } else {
        this.$refs.content?.foldAllGroups?.()
        klook.bus.$emit(ACTIVATE_EDITING_EVENT_KEY.close)
      }
    },
    changeFoldGroupIds(data) {
      this.$set(this, 'foldGroupIds', data)
    },
    handleDisplaySectionList() {
      this.setGroupSelectionStore({
        visible: true,
        groupList: this.groupList,
        field: this.field,
        originalGroupData: this.originalGroupData
      })
    },
    scrollPromptSaveTips() {
      setTimeout(async () => {
        await this.$nextTick()
        this.scrollIntoView(document.querySelector('.group-content-container .prompt-save-tips'), {
          type: 'scrollIntoView'
        })
      }, 100)
    },
    async beforeUnload() {
      const dataNotSaved = this.$refs.content?.tplDataNotSaved?.()

      if (!this.readonly && (dataNotSaved || this.hasDraftData)) {
        const content = dataNotSaved ? this.$t('act_leave_tips') : this.$t('80861')
        return await this.beforeInterceptorConfirm({
          content,
          ...(dataNotSaved
            ? {
                closable: false,
                ok: {
                  value: INTERCEPTOR_BTN_TYPES.stay,
                  callback: () => {
                    this.scrollPromptSaveTips()
                  }
                },
                cancelText: this.$t('common.leave_title'),
                okText: this.$t('73075')
              }
            : {})
        })
      }

      return true
    },
    async handleNext(skipRoute = true, options) {
      const valid = await this.$refs.content.validateForm()

      if (valid) {
        const dataNotSaved = this.$refs.content?.tplDataNotSaved?.()
        if (dataNotSaved) {
          this.scrollPromptSaveTips()
          return
        }

        this.rootChangeLoading(true)
        if (options?.saveStatus) {
          // 未保存弹窗点击保存，同步获取正确埋点
          await this.waitPromiseQueueFinCallback(async () => {
            await this.handleSave(skipRoute, options)
          })
        } else {
          // 预留部分 widget blur 触发保存草稿的接口时间
          setTimeout(() => {
            this.waitPromiseQueueFinCallback(() => {
              this.handleSave(skipRoute, options)
            })
          }, 100)
        }
      } else {
        this.$message.warn('Please fill in the form')
        this.scrollIntoView(document.querySelector('.group-content-container .has-error'), {
          offsetY: this.$root.isMerchant ? -164 : -72
        })
      }
    },
    async handleSave(skipRoute = true, options) {
      const response = await this.saveDraft2Prod()

      // 关闭 preview 和 selection
      this.toCPreview.visible = false
      this.setGroupSelectionStore({
        visible: false
      })
      this.hasDraftData = false
      const extObj = {
        save_type: options?.saveStatus ? 1 : 0,
        err_message: response?.success ? '' : response?.error?.message || 'Error: false'
      }
      this.$root.trackIHEvent('.js-edit-save-btn', extObj)
      if (response.success) {
        this.successfulResponseCb(skipRoute)
        this.$emit('saveSuccess')
      } else {
        this.$emit('saveError', response)
      }

      klook.bus.$emit('refreshGuideStatus')
      skipRoute && this.refreshPage()
      this.rootChangeLoading(false)
    },
    async saveDraft2Prod() {
      const urlKey =
        this.extraType === 'ACT'
          ? 'publish_activity_instance_draft_data'
          : 'publish_package_instance_draft_data'
      const language = new URLSearchParams(location.search).get('lang') || 'en_US'

      return await ajax.postBody({
        url: ADMIN_API.act[urlKey],
        data: {
          language,
          extra_type: this.extraType,
          extra_id: this.extra_id,
          activity_id: this.activityId
        },
        noDefaultResponseInterceptor: true
      })
    },
    async successfulResponseCb(skipRoute = true) {
      let data = {
        status: 1,
        activity_id: this.activityId,
        language: new URLSearchParams(location.search).get('lang') || 'en_US'
      }

      if (this.extraType === 'ACT') {
        await this.updateActStepStatus2action({
          ...data,
          step: 'activity_detail_info'
        })
        klook.bus.$emit('updateActStepStatus2bus')
      } else {
        await this.updatePkgStepStatus2action({
          ...data,
          step: 'package_detail_info',
          package_id: +this.packageId
        })
        klook.bus.$emit('updatePkgInfos2bus')
        await this.refreshPkgs()
      }

      skipRoute && this.$message.success(this.$t('global_success'))
      this.clearBeforeEachInterceptor()
      this.setActivateFooterWrapper(false)

      setTimeout(() => {
        let { query, params } = this.$route
        if (this.extraType === 'ACT') {
          if (this.$root.isMerchant || !skipRoute) {
            return false
          }

          if (['admin', 'em'].includes(this.page_from)) {
            this.$router.push({
              name: 'notes',
              query,
              params
            })
          } else {
            let packages = this.$store.getters.pkgList
            let package_id = _.get(packages, '[0].package_id', '')
            let package_type = _.get(packages, '[0].package_type', '1')

            this.$router.push({
              name: 'packageBasicInfo',
              params,
              query: {
                ...query,
                package_id,
                package_type
              }
            })
          }
        } else {
          this.hasSaveDraftData2Pub = true
        }
      }, 300)
    },
    async getCurrWidgetsData() {
      this.allVariables = await this.getActVariables2actions()

      const data = await this.getGroupDataByLanguage(getEditLang())

      this.$set(this, 'originalGroupData', data)
      this.initGroupList(data)
      // this.$set(this, 'groupList', this.getGroupList(data))
      this.$emit('groupListInited', {
        groupList: this.groupList,
        data,
        allGroups: data.groups_summary
      })

      // 数据初始化完成之后需要的回调任务
      setTimeout(async () => {
        await this.$nextTick()
        this.hasDraftData = !!this.getDraftDataDom().length
        // 存在草稿则自动展开底部操作栏
        if (this.hasDraftData) {
          this.setActivateFooterWrapper(true)
        }

        this.startDriver()
        this.setHasScrolled()

        klook.bus.$emit('scrollToSuggestField2bus')
      }, 100)
    },
    initGroupList(data) {
      const { groups_summary, groups_instance } = data

      ;(groups_summary || []).forEach((group) => {
        const groupId = group.id
        const currInstanceData = groups_instance[groupId]

        if (currInstanceData) {
          const data = currInstanceData.reduce((acc, title) => {
            if (
              title.attr_values.some((attrVal) => !!attrVal.isInstanceData) ||
              title.ui_conf.required ||
              (this.isCreate && title.ui_conf.config.suggest_filling)
            ) {
              acc.push(genUUID(title))
            }

            return acc
          }, [])

          this.setCheckedTitlesOnGroup({
            field: this.field,
            groupId,
            data
          })
        }
      })
    },
    async getRefWidgetsData() {
      if (this.refLang) {
        if (!this.refAllVariables.length) {
          this.refAllVariables = await ajax.get(ADMIN_API.act.get_variable_list, {
            params: {
              attr_id_list: '',
              language: this.refLang
            }
          })
        }

        let data = await this.getGroupDataByLanguage(this.refLang, false)
        this.$set(this, 'refWidgetsData', data.groups_instance)
      }
    },
    async getGroupDataByLanguage(language, getEditData = true) {
      let urlKey, params
      if (this.extraType === 'ACT') {
        urlKey = 'get_group_list_v2'
        params = {
          activity_id: this.activityId
        }
      } else {
        urlKey = 'get_package_group_list_v2'
        params = {
          package_id: this.packageId,
          category_id: this.categoryId
        }
      }

      const response = await ajax.get(ADMIN_API.act[urlKey], {
        params: {
          ...params,
          page_from: this.page_from,
          language
        }
      })

      this.isCreate =
        !Object.keys(response.groups_instance || {}).length || // groups_instance: null
        Object.values(response.groups_instance).every((item) => !(item.widgets && item.widgets.length)) // widgets 空值可能为 null // []

      if (!this.isCreate && this.noReminderDriver()) {
        this.firstInitialFold = true
      }

      return this.formatResponseData(response, { getEditData, isCreate: this.isCreate, hasDraftData: true })
    },
    changeDisplayContent(v) {
      this.displayContent = v
    }
    //
  }
}
</script>

<style lang="scss" scoped>
.common-box {
  width: 100%;
  will-change: width;
}

.presenter-container {
  .add-group-btn {
    padding: 12px 0;
    text-align: center;
    border: 1px dashed #437dff;
    border-radius: 8px;
    color: #2073f9;
    justify-content: center;
    transition: width 0.3s ease-in-out, opacity 0.3s ease-in-out;
    opacity: 0.95;
    cursor: pointer;
    user-select: none;

    &:hover {
      opacity: 1;
    }
  }

  .operator {
    display: flex;
    margin: 12px 0;
    justify-content: space-between;
    transition: width 0.3s ease-in-out;
    color: #212121;

    .btns {
      display: inline-flex;
      user-select: none;
      color: #2073f9;
    }

    .fold-btn {
      margin-right: 12px;
      cursor: pointer;
    }

    .activate-edit-btn {
      cursor: pointer;
    }
  }

  ::v-deep .ant-affix .add-group-btn {
    background-color: #fff;
  }

  .content-container {
    position: relative;
    display: flex;
    z-index: 1;

    .content {
      flex: 1;
      max-width: 100%;
    }

    .preview {
      width: 0;
      transition: width 0.3s;
    }
  }

  .footer-go-ahead {
    margin: 24px 0 60px;
    color: rgba(0, 0, 0, 0.85);

    ::v-deep .ant-btn {
      margin-left: 8px;
    }
  }

  &.is-display-preview {
    .add-group-btn,
    .operator.common-box,
    .group-content-container {
      width: calc(100% - 326px);
    }

    .preview {
      width: 326px;
    }
  }
}
</style>

<style lang="scss">
.js-class-driver-fix-stacking-container .driver-fix-stacking {
  z-index: 99 !important;
}

.driver-popover-footer {
  display: none;
}

// https://github.com/kamranahmedse/driver.js/issues/133
div#driver-highlighted-element-stage,
div#driver-page-overlay {
  background: transparent !important;
  outline: 99999px solid rgba(0, 0, 0, 0.75);
}

.ant-layout.hide-header {
  .presenter-container.is-display-preview {
    .add-group-btn,
    .operator.common-box,
    .group-content-container {
      width: calc(100% - 346px) !important;
    }
    .content-container .preview {
      width: 346px;
    }
  }
}
</style>
