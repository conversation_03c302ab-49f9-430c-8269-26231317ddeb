<template>
  <a-drawer
    class="drawer-to-c-preview"
    :visible.sync="_visible"
    :width="352"
    :closable="true"
    :mask="false"
    :get-container="containerEle"
    :header-style="{
      position: 'relative',
      height: '64px'
    }"
    :body-style="{
      height: 'calc(100vh - 64px)',
      padding: '12px 20px 48px',
      display: 'flex',
      flexDirection: 'column'
    }"
    placement="right"
    @close="onClose"
  >
    <template #title>
      <span class="title">{{ $t('80680') }}</span>
    </template>

    <header class="header">
      <div class="header-title" @click="handlePreviewFront">{{ $t('80681') }}</div>
      <div class="header-tip">{{ $t('80682') }}</div>
    </header>

    <a-spin :spinning="spinning">
      <iframe
        v-if="switchIframeIndex === 0"
        :key="refreshKey"
        ref="iframe"
        class="iframe"
        :src="src"
        @load="onLoad"
      ></iframe>
    </a-spin>
  </a-drawer>
</template>

<script>
import { getEditLang } from '@activity/utils'
import { languageConfig } from '@klook/site-config'
import { getPreviewValiditySync } from '@activity/components/preview-validity'

export default {
  name: 'ToCPreView',
  model: {
    prop: 'groupList',
    event: 'change'
  },
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    activityId: {
      type: Number,
      required: true
    },
    packageId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      refreshKey: 0,
      spinning: true,
      switchIframeIndex: 0
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(v) {
        this.$emit('update:visible', v)
      }
    },
    b2cLanguageMap() {
      return Object.entries(languageConfig.f2bLanguageMap).reduce(
        (acc, curr) => ({ ...acc, [curr[1]]: curr[0] }),
        {}
      )
    },
    src() {
      let previewUrl = klook.getUrlByEnv('')
      const params = this.packageId ? '?package_id=' + this.packageId : ''
      let lang = this.b2cLanguageMap[getEditLang()]
      lang = lang === 'en' ? '' : `${lang}/`

      return `${previewUrl}/${lang}experiences/admin_preview/${this.activityId}/${params}`
    }
  },
  mounted() {
    window.addEventListener('message', (event) => {
      if (event.data && event.data.action === 'stop-spinning') {
        this.spinning = false
      }
    })
  },
  methods: {
    onLoad() {
      this.spinning = false
    },
    containerEle() {
      return document.querySelector('#app')
    },
    refreshPreview: _.debounce(function refreshPreview() {
      try {
        this.$refs.iframe.contentWindow.postMessage(
          {
            action: 'refresh'
          },
          '*'
        )
      } catch (err) {
        this.spinning = true
        this.refreshKey += 1
      }
    }, 100),
    onClose() {
      this._visible = false
    },
    handlePreviewFront() {
      getPreviewValiditySync(this.$route.params.id, this.$i18n, (date) => {
        if (date) {
          let preview_url = klook.getUrlByEnv('')
          let activity_id = this.$route.params.id
          let language = this.$route.query.lang
          let url = `${preview_url}/en-US/preview/activity/${activity_id}/?lang=${language}&deadline_version=1&deadline=${encodeURIComponent(
            date
          )}`
          window.open(url)
          this.$tracker.track('action', '#glb_preview_spm')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.header-title {
  color: #2073f9;
  cursor: pointer;
}

.header-tip {
  margin: 8px 0 12px;
  color: #757575;
}

.iframe {
  border: 1px solid #f0f0f0;
}

::v-deep .ant-spin-nested-loading {
  flex: 1;
  .ant-spin-container,
  .iframe {
    height: 100%;
  }
}
</style>
