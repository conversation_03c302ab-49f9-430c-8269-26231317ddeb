<template>
  <div
    class="widget-item-container"
    :class="{
      [`is-active-${activeEle}`]: activeEle && isAttrWidget
    }"
    :data-is-empty="isEmpty"
    :data-is-required="isRequired"
    :data-is-suggest-filling="isSuggestFilling"
  >
    <header v-if="displayTitle" class="widget-header">
      <div class="widget-name">
        <label>
          <DataPublishDotStyle v-if="hasDraftState && publishStatus === 0" :published="publishStatus" />
          <span v-if="isRequired" class="ant-form-item-required"></span>
          <span>{{ widget.ui_conf.name }}</span>
          <span v-if="isSuggestFilling" class="widget-tag">
            {{ $t('80674') }}
          </span>
        </label>

        <span class="widget-operator">
          <a-tooltip
            v-if="hasDraftState && displayDeleteBtn && (!isRequired || widgetCount > 1)"
            :title="$t('global_delete')"
          >
            <a-icon
              type="delete"
              class="btn-unselect btn-hover-display"
              @mouseenter="activeEle = 'delete'"
              @mouseleave="activeEle = ''"
              @click="handleDeleteWidget"
            />
          </a-tooltip>

          <!-- <a-tooltip v-if="widget.attr_values.length" :title="$t('global_reset')">
            <a-icon
              type="undo"
              class="btn-reset btn-hover-display"
              @mouseenter="activeEle = 'reset'"
              @mouseleave="activeEle = ''"
              @click="handleResetWidget"
            />
          </a-tooltip> -->

          <a-tooltip v-if="isAttrWidget && widget.ui_conf.config.allow_copy" :title="$t('76544')">
            <a-icon
              type="copy"
              class="btn-copy"
              size="small"
              :alt="$t('global_copy')"
              @click.stop="handleCopyWidget"
            />
          </a-tooltip>

          <label
            v-if="displayWidgetCount"
            class="widget-def-count"
            :class="isRequired ? 'widget-required-count' : ''"
            :style="isRequired && widget.isFinishFlag ? 'color: #36b37e;' : ''"
          >
            <span v-if="isRequired" style="margin-right: 2px">
              <a-icon theme="filled" :type="widget.isFinishFlag ? 'check-circle' : 'exclamation-circle'" />
            </span>
            <span>{{ getWidgetCount }}</span>
          </label>
        </span>
      </div>

      <DescMarkdownContent
        v-if="widget.ui_conf.desc"
        :desc="widget.ui_conf.desc"
        class="widget-desc"
        placement="right"
      />
    </header>

    <div class="widget-item">
      <template v-if="isAttrWidget">
        <attribute
          ref="widget"
          v-model="widget.attr_values"
          v-bind="attrs"
          :uuid="widget.uuid"
          :ui="widget.ui_conf"
          :check-list="widget.checkList"
          :data="widget.attr_values"
          :invalid_tag="invalidTag"
          :display-sort="displaySort"
          :order="widget.order || 0"
          :draft-attr-values-deleted.sync="widget.draftAttrValuesDeleted"
          :auto-checked-to-front="autoCheckedToFront"
          :disabled-var-option="disabledVarOption"
          :has-draft-state="hasDraftState"
          :is-publish-with-ai="isPublishWithAI"
          v-on="$listeners"
          @update:checkList="(data) => updateCheckList(data, widget)"
          @validate="(valid) => $emit('validate', valid)"
        />
      </template>

      <template v-else-if="currWidgetType === 4">
        <p>
          {{ $t('act_image_en_us_handle') }}
        </p>
        <photo-guide
          ref="widget"
          v-model="widget.attr_values"
          :ui="widget"
          :current-group="group"
          :language-all="locale_info.react_data"
          :data="widget.attr_values"
          v-bind="{ ...attrs, word_count_limit: wordCountLimit }"
          :disabled="readonly"
          :is-publish-with-ai="isPublishWithAI"
          @changeList="changeList"
        />
      </template>

      <component
        :is="widgetTypeDict[currWidgetType]"
        v-else-if="widgetTypeDict[currWidgetType] && display"
        ref="widget"
        v-model="widget.attr_values[0]"
        :data="widget.attr_values[0]"
        v-bind="attrs"
        :is-publish-with-ai="isPublishWithAI"
        :readonly="attrs.readonly || getAIDisabled(currWidgetType)"
        @save2Draft="onSave2Draft"
      />
    </div>

    <WidgetTimeControl
      v-if="showTimeControl"
      :effective-time.sync="widget.effective_time"
      @change="handleEffectiveTimeChange"
    ></WidgetTimeControl>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import moment from 'moment'
import DurationWidget from '@activity/components/duration_widget'
import SingleWidget from '@activity/components/single_widget'
import MarkdownWidget from '@activity/components/markdown_widget'
import Attribute from '@activity/components/widgets/Attribute/src/Widget'
import PhotoGuide from '@activity/components/photo-upload/photo_guide'
import DescMarkdownContent from '@activity/pages/activityManagement/detailV2/components/descMarkdownContent'
import { getUuid } from '@activity/utils'
import CommonMixin from '../common_mixin'
import draft2prod_mixin from '@activity/pages/activityManagement/detailV2/draft2prod_mixin'
import DataPublishDotStyle from '@activity/components/widgets/Attribute/src/DataPublishDotStyle'
import WidgetTimeControl from './widgetTimeControl.vue'
import { genUUID } from '../../../../utils'

export default {
  name: 'WidgetItem',
  components: {
    Attribute,
    MarkdownWidget,
    SingleWidget,
    DurationWidget,
    PhotoGuide,
    DescMarkdownContent,
    DataPublishDotStyle,
    WidgetTimeControl
  },
  mixins: [draft2prod_mixin],
  inject: ['insertPromiseQueueItem', 'waitPromiseQueueFinCallback'],
  props: {
    group: {
      type: Object,
      required: true
    },
    widget: {
      type: Object,
      required: true
    },
    field: {
      type: String,
      required: true
    },
    refData: {
      type: [Array, Object],
      default: () => null
    },
    isAsh: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    invalidTag: {
      type: Object,
      default: () => ({})
    },
    autoCheckedToFront: {
      type: Boolean,
      default: true
    },
    displayDeleteBtn: {
      type: Boolean,
      default: true
    },
    displaySort: {
      type: Boolean,
      default: true
    },
    displayTitle: {
      type: Boolean,
      default: true
    },
    originalGroupData: {
      type: Object,
      required: true,
      validator: function (data) {
        return ['groups_summary', 'groups_instance', 'groups_schema'].every((key) =>
          hasOwnProperty.call(data, key)
        )
      }
    },
    displayWidgetCount: {
      type: Boolean,
      default: false
    },
    hasDraftState: {
      type: Boolean,
      default: false
    },
    index: {
      type: Number,
      required: true
    },
    allVariables: {
      type: Array,
      required: true
    },
    hideWidgetType: {
      type: Array,
      default: () => []
    },
    // 当前 widget id 的数量
    widgetCount: {
      type: Number,
      default: 0
    },
    // 用于折叠再展开情境下，Markdown value 不显示（需要点击才触发显示）的 bug
    display: {
      type: Boolean,
      default: true
    },
    widgetList: {
      type: Array,
      default: () => []
    },
    required: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeEle: '',
      delBtnActive: false,
      publishStatus: 1
    }
  },
  computed: {
    ...mapGetters(['isPublishWithAI']),
    isEmpty() {
      switch (this.currWidgetType) {
        // attr / photo
        case 1:
        case 4:
          return !this.widget.attr_values.length
        // single / markdown
        case 2:
        case 3:
          return this.widget.attr_values?.[0]?.value === ''
        // duration
        case 6:
          return this.widget.attr_values?.[0]?.value === '{}'
        default:
          return false
      }
    },
    isRequired() {
      return this.required || !!this.widget.ui_conf.required
    },
    isSuggestFilling() {
      return !!this.widget.ui_conf.config.suggest_filling
    },
    checkedTitlesOnGroup() {
      return this.$store.getters['attr/checkedTitlesOnGroupGetter']({
        field: this.field
      })
    },
    calcMdConfig() {
      let obj = {}
      switch (this.calcRefFieldTag) {
        case 'summary':
          obj = {
            toolbar: ['unordered-list']
          }
          break
        case 'insider_tips':
          obj = {
            toolbar: ['bold', 'unordered-list', 'preview']
          }
          break
        default:
          break
      }
      return obj
    },
    calcRefFieldTag() {
      const { originalGroupData, group } = this
      const tag = originalGroupData?.groups_schema?.[group?.id]?.ref_field_tag
      return tag
    },
    currWidgetType() {
      return this.widget.ui_conf.widget_type
    },
    isAttrWidget() {
      return this.currWidgetType === 1
    },
    currWidget: {
      get() {
        return this.widget
      },
      set() {}
    },
    isDisabled(widget) {
      return widget?.ui_conf?.access_permission === 1
    },
    widgetTypeDict() {
      // widget_type: 1,// widget 类型：1-Attribute 2-Single Text 3-Markdown 4-Image 5-Address, 6: duration
      const dict = {
        1: 'attribute',
        2: 'single-widget',
        3: 'markdown-widget',
        4: 'photo-guide',
        // 5: 'address',
        6: 'duration-widget'
      }

      return Object.keys(dict).reduce((acc, curr) => {
        if (this.hideWidgetType.includes(curr)) {
          return curr
        }

        return {
          ...acc,
          [curr]: dict[curr]
        }
      }, {})
    },
    wordCountLimit() {
      return this.widget?.ui_conf?.config?.word_count_limit ?? []
    },
    attrs() {
      const { widget, refData, group } = this
      const disabled =
        this.readonly || this.isAsh || this.isDisabled || widget.ui_conf.access_permission === 1

      return {
        required: this.isRequired,
        config: widget.ui_conf.config,
        disabled,
        key: widget.uuid,
        readonly: disabled,
        ui: widget,
        refData,
        field: this.field,
        groupId: group.id,
        uuid: widget.uuid,
        mdConfig: this.calcMdConfig,
        ...this.$attrs,
        extraInfo: widget.extra_info
      }
    },
    getWidgetCount() {
      const { currWidget: widget } = this
      let type = widget.ui_conf.widget_type
      let num = widget.attr_values.length
      // let count = widget.ui_conf.attr_value_list.length
      let count = this.widget.ui_conf.attr_value_list.reduce((acc, curr) => {
        const extendConf = curr.extendConf || {}
        const item = Object.assign({}, curr, extendConf)

        if (item.hide || item.access_permission === 0) {
          return acc
        }

        return acc + 1
      }, 0)
      let str = ''
      let obj = {}
      let subNum = 0
      switch (type) {
        case 6:
          obj = _.get(widget, 'attr_values.0.value', {})
          if (typeof obj === 'string') {
            obj = JSON.parse(obj)
          }
          num = obj.single === undefined ? 0 : 1
          count = num || 1
          break
        case 3:
        case 2:
          obj = _.get(widget, 'attr_values.0.value')
          num = !obj ? 0 : 1
          count = num || 1
          break
        case 1:
          if (_.get(widget, 'ui_conf.config.multiple_selection')) {
            subNum =
              widget.attr_values.map((o) => o.id).length -
              _.uniqWith(widget.attr_values.map((o) => o.id)).length
            count = count + subNum
          } else {
            count = num || 1
          }
          break
        default:
          count = num || 1
      }
      str = `${num}/${count}`
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.$set(widget, 'isFinishFlag', num > 0) // 两种方法都不完美

      return str
    },
    disabledVarOption() {
      return this.$store.getters['attr/disabledVarOptionByFieldGetter']({
        field: this.field
      })
    },
    groupId() {
      return this.group.id
    },
    showTimeControl() {
      return this.widget.ui_conf.config.display_time_control
    }
  },
  mounted() {
    this.publishStatus = this.widget.attr_values.some((av) => av.published === 0) ? 0 : 1
    if (this.showTimeControl) {
      this.checkEffectiveTime()
    }
  },
  beforeDestroy() {
    this.$emit('setGroupConfig')
  },
  methods: {
    ...mapMutations({
      setCheckedTitlesOnGroup: 'attr/setCheckedTitlesOnGroup'
    }),
    updateCheckList(checkList, widget) {
      Object.assign(widget, { checkList })
      // 修复勾选后可能存在不刷新的情况
      !this.hasDraftState && this.$forceUpdate()
    },
    emitUnfoldGroup() {
      this.$emit('unfoldGroupEvt')
    },
    getAIDisabled(type) {
      const types = [6]
      if (types.includes(type)) {
        return false
      }

      return this.isPublishWithAI
    },
    // 2 single input / 3 markdown 是 blur 触发
    // 6 duration 是点选之后触发
    onSave2Draft() {
      const cb = ({ widget, response }) => {
        const widgets = response.result[this.groupId]?.widgets ?? []
        const draftWidget = widgets.find((item) => item.id === widget.id)
        const draftAttrValues = draftWidget?.draft_attr_values
        const draftEffectiveTime = draftWidget?.draft_effective_time
        if (draftAttrValues) {
          const draftAttrValue = draftAttrValues.filter((item) => !item.deleted)?.[0]
          draftAttrValue &&
            Object.assign(this.widget.attr_values[0], {
              create: false,
              ...draftAttrValue
            })
        }
        if (draftEffectiveTime) {
          this.widget.effective_time = draftEffectiveTime
        }
      }

      this.save2Draft(this.widget, cb)
    },
    // type: 4 images
    changeList: _.debounce(async function (data) {
      this.save2Draft({
        ...this.widget,
        attr_values: data
      })
    }, 600),
    async save2Draft(widget, cb = null) {
      if (!this.hasDraftState) return

      this.publishStatus = 0
      widget.attr_values.forEach((av) => {
        Object.assign(av, { published: 0 })
      })
      widget = _.cloneDeep(widget)
      const { attr_values, effective_time, extra_info } = widget

      // 初始化数据不需要
      if (widget.ui_conf.widget_type === 6 && attr_values?.[0]?.published === -1) return

      const promiseItem = this.insertPromiseQueueItem()
      const response = await ajax.postBody(ADMIN_API.act[this.saveDraftDataUrlKey], {
        data: {
          ...this.commonDraftParams,
          op: 'replace',
          groups: [
            {
              ...this.commonGroupParams,
              widgets: [
                {
                  id: widget.id,
                  order: widget.order || 0,
                  attr_values: attr_values.map((item) => ({ ...item, published: 0 })),
                  ...(effective_time ? { effective_time } : {}),
                  extra_info
                }
              ]
            }
          ]
        }
      })

      promiseItem.done()
      if (response?.success) {
        cb?.({ widget, response })
        klook.bus.$emit('refreshPreview')
      }

      return response
    },
    async saveWidgetData2Draft(data, type) {
      this.rootChangeLoading(true)

      const res = await ajax.postBody(ADMIN_API.act[this.saveDraftDataUrlKey], {
        data: {
          ...this.commonDraftParams,
          op: type,
          groups: [
            {
              ...this.commonGroupParams,
              ...data
            }
          ]
        }
      })

      this.rootChangeLoading(false)

      return res
    },
    // async handleResetWidget() {
    //   const res = await this.saveWidgetData2Draft({ widgets: [] }, 'update')
    //   if (res) {
    //     this.activeEle = ''
    //     const uuid = genUUID(this.widget)
    //     this.originalGroupData.groups_instance[this.group.id].forEach((widget, index, arr) => {
    //       if (genUUID(widget) === uuid) {
    //         const isMulti = this.widget.ui_conf.config.multiple_selection
    //         Object.assign(widget, {
    //           checkList: isMulti ? [] : '',
    //           attr_values: []
    //         })
    //       }
    //     })

    //     klook.bus.$emit('refreshPreview')
    //   }
    // },
    handleDeleteWidget() {
      this.rootChangeLoading(true)
      // 预留部分 widget blur 触发保存草稿的接口时间
      this.delWidgetTimer && clearTimeout(this.delWidgetTimer)
      this.delWidgetTimer = setTimeout(() => {
        this.delWidgetTimer = null
        this.waitPromiseQueueFinCallback(() => {
          this.deleteWidget()
        })
      }, 100)
    },
    async deleteWidget() {
      let { id, order, attr_values, effective_time, extra_info } = this.widget

      let response = true
      if (this.hasDraftState) {
        response = await ajax.postBody(ADMIN_API.act[this.saveDraftDataUrlKey], {
          data: {
            ...this.commonDraftParams,
            op: 'delete',
            groups: [
              {
                ...this.commonGroupParams,
                widgets: [
                  {
                    id,
                    order,
                    attr_values,
                    ...(effective_time ? { effective_time } : {}),
                    extra_info
                  }
                ]
              }
            ]
          }
        })
      }

      this.rootChangeLoading(false)

      if (response) {
        this.activeEle = ''
        klook.bus.$emit('refreshPreview')

        const uuid = genUUID(this.widget)
        const index = this.originalGroupData.groups_instance[this.groupId].findIndex(
          (title) => genUUID(title) === uuid
        )
        // const currWidget = this.originalGroupData.groups_instance[this.groupId][index]
        if (~index) {
          const { groupId } = this
          const checkedTitlesData = this.checkedTitlesOnGroup[groupId] || []

          this.setCheckedTitlesOnGroup({
            field: this.field,
            groupId,
            data: checkedTitlesData.filter((val) => uuid !== val)
          })

          // Object.assign(currWidget, CommonMixin.methods.getWidgetDefaultData.call(this, currWidget))
        }
      }
    },
    tplDataNotSaved() {
      return this.$refs?.widget?.tplDataNotSaved?.() ?? false
    },
    validateForm() {
      if (!this.display) {
        return true
      }

      return new Promise((resolve) => resolve(this.$refs?.widget?.validateForm?.() ?? true))
    },
    async handleCopyWidget() {
      const groupId = this.group.id
      let { groups_instance } = this.originalGroupData
      let widgetsData = groups_instance[groupId]
      let index = widgetsData.findIndex((widget) => widget.uuid === this.widget.uuid)
      // 当前被 copy widget 值
      let currentCopy = widgetsData[index]
      // 获得当前 widget id 最大 order/index 值, 避免错误数据导致复制 order 重复问题
      const maxValueResult = widgetsData.reduce(
        (acc, curr, index) => {
          if (currentCopy.id === curr.id) {
            if (acc.order < curr.order || 0) {
              acc.order = curr.order
            }

            acc.index = index
          }

          return acc
        },
        {
          order: 0,
          index: 0
        }
      )
      let widgetData = {
        ...CommonMixin.methods.getWidgetDefaultData.call(this, currentCopy),
        uuid: getUuid(),
        create: true,
        order: maxValueResult.order + 1,
        checkList: currentCopy.ui_conf.config.multiple_selection ? [] : ''
      }

      widgetData.ui_conf.attr_value_list = widgetData.ui_conf.attr_value_list.filter((x) => !x.ash)

      widgetsData.splice(
        // 塞到相同 widget 的末尾
        maxValueResult.index + 1,
        0,
        _.cloneDeep({
          ...widgetData,
          isFromCopy: true
        })
      )

      let response = true
      if (this.hasDraftState) {
        response = await this.saveWidgetData2Draft(
          {
            widgets: [
              {
                id: widgetData.id,
                order: widgetData.order,
                extra_info: widgetData.extra_info,
                attr_values: []
              }
            ]
          },
          'insert'
        )
      }

      if (response) {
        klook.bus.$emit('refreshPreview')
        this.$emit('copyWidget', { group: this.group, data: widgetData })
      }
    },
    handleEffectiveTimeChange() {
      this.onSave2Draft()
      this.checkEffectiveTime()
    },
    checkEffectiveTime() {
      // 校验 widget 的 value，如果为空，则不显示 group alert
      // 注意：这个校验不完整
      if (!this.widget?.attr_values?.[0]?.value) {
        this.$emit('setGroupConfig')
        return
      }

      let alertMsg = ''
      if (this.widget.effective_time) {
        const { end_time } = this.widget.effective_time
        if (end_time && moment.utc(end_time).local().isBefore()) {
          alertMsg = 'The content has expired. Please delete it if no longer needed.'
        }
      }
      this.$emit('setGroupConfig', alertMsg)
    }
  }
}
</script>

<style lang="scss" scoped>
$spacePixel: 12px;

.widget-item-container {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid transparent;
  margin-bottom: 12px;

  &.is-active-reset {
    background-color: #edf5ff;

    ::v-deep .compound-option {
      background-color: #edf5ff;
    }
  }

  &.is-active-delete {
    background-color: #ffefef;

    ::v-deep .compound-option {
      background-color: #ffefef;
    }
  }

  .widget-header {
    padding: 8px 0;

    &:hover .widget-operator {
      ::v-deep .btn-hover-display {
        display: block !important;
      }
    }

    .widget-operator {
      display: inline-flex;

      ::v-deep .anticon {
        margin-left: 12px;
      }

      .btn-hover-display {
        display: none;
      }

      .btn-unselect {
        color: #ff4d4f;
      }

      .btn-reset {
        color: #757575;
      }

      .btn-copy {
        color: #437dff;
      }
    }

    .widget-name {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin-bottom: 12px;
      color: #000;
      font-size: 16px;
      font-weight: 600;

      .widget-def-count {
        display: inline-flex;
        align-items: center;
        margin-left: $spacePixel;
        color: rgba(0, 0, 0, 0.45);
        font-size: 12px;
      }

      .widget-tag {
        display: inline-block;
        margin-left: 8px;
        padding: 1px 8px;
        background: #f0f7ff;
        border: 1px solid #bdd8ff;
        border-radius: 2px;
        font-size: 12px;
        font-weight: 400;
        color: #437dff;
      }
    }

    .widget-desc {
      margin-top: 4px;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.65);
    }
  }

  .widget-is-empty {
    padding: 7.5px 12px;
    border: 1px dashed #b2b2b2;
    border-radius: 8px;
    text-align: center;
    color: #b2b2b2;
    cursor: pointer;
  }
}
</style>
