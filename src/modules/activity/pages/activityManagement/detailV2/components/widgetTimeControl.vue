<template>
  <div class="widget-time-control">
    <div class="widget-time-control-wrap">
      <span class="widget-time-control-text">Display Start Time（UTC）</span>
      <a-date-picker
        v-model="startTime"
        allow-clear
        :show-today="false"
        :show-time="{
          defaultValue: moment('00:00:00', 'HH:mm:ss'),
          disabledHours: disabledStartHours,
          disabledMinutes: disabledStartMinutes,
          disabledSeconds: disabledStartSeconds
        }"
        :format="dateFormat"
        :value-format="dateFormat"
        :disabled-date="disabledStartDate"
        dropdown-class-name="widget-time-control-picker-dropdown"
        placeholder="If not selected, it defaults to the current time"
        class="widget-time-control-picker"
        @openChange="(status) => onOpenChange(status, startTime)"
        @change="handleChange"
      />
    </div>
    <div class="widget-time-control-wrap">
      <span class="widget-time-control-text">Display End Time（UTC）</span>
      <a-date-picker
        v-model="endTime"
        allow-clear
        :show-today="false"
        :show-time="{
          defaultValue: moment('00:00:00', 'HH:mm:ss'),
          disabledHours: disabledEndHours,
          disabledMinutes: disabledEndMinutes,
          disabledSeconds: disabledEndSeconds
        }"
        :format="dateFormat"
        :value-format="dateFormat"
        :disabled-date="disabledEndDate"
        dropdown-class-name="widget-time-control-picker-dropdown"
        placeholder="If not selected, it will default to a display with no end."
        class="widget-time-control-picker"
        @openChange="(status) => onOpenChange(status, endTime)"
        @change="handleChange"
      />
    </div>
  </div>
</template>

<script>
import moment from 'moment'

/**
 * 目前 effective_time 只支持非 attribute 的属性项，attribute 的属性项在 saveDraftDataUrlKey 接口会有问题
 */
export default {
  name: 'WidgetTimeControl',
  props: {
    effectiveTime: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dateFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },
  computed: {
    startTime: {
      get() {
        const { start_time } = this.effectiveTime || {}
        // 如果为空则返回当前时间，之前有bug
        return start_time || undefined
      },
      set(date) {
        this.$emit('update:effectiveTime', {
          ...(this.effectiveTime || {}),
          start_time: date || undefined
        })
      }
    },
    endTime: {
      get() {
        const { end_time } = this.effectiveTime || {}
        return end_time || null
      },
      set(date) {
        this.$emit('update:effectiveTime', {
          ...(this.effectiveTime || {}),
          end_time: date || undefined
        })
      }
    }
  },
  watch: {
    startTime: {
      deep: true,
      immediate: true,
      handler(v) {
        if (!v) {
          this.startTime = moment().utc().format(this.dateFormat)
        }
      }
    }
  },
  methods: {
    moment: moment,
    onOpenChange(status, date) {
      if (!status) {
        this.$emit('change')
      }
    },
    handleChange(date) {
      if (!date) {
        this.$emit('change')
      }
    },
    disabledStartDate(current) {
      // 如果 endTime 存在,则不能选择比 endTime 更晚的日期
      return this.endTime ? current > moment(this.endTime).endOf('day') : false
    },

    disabledEndDate(current) {
      // 如果 startTime 存在(包括默认的当前时间),则不能选择比 startTime 更早的日期
      if (!this.startTime) {
        return false
      }
      return current && current < moment(this.startTime)
    },
    disabledStartHours() {
      if (this.startTime && this.endTime && moment(this.startTime).isSame(this.endTime, 'day')) {
        return Array(24)
          .fill(null)
          .map((_, index) => index)
          .slice(1 + moment(this.endTime).hour())
      }

      return []
    },
    disabledStartMinutes() {
      if (this.startTime && this.endTime && moment(this.startTime).isSame(this.endTime, 'hour')) {
        return Array(60)
          .fill(null)
          .map((_, index) => index)
          .slice(1 + moment(this.endTime).minute())
      }

      return []
    },
    disabledStartSeconds() {
      if (this.startTime && this.endTime && moment(this.startTime).isSame(this.endTime, 'hour')) {
        return Array(60)
          .fill(null)
          .map((_, index) => index)
          .slice(1 + moment(this.endTime).second())
      }

      return []
    },
    disabledEndHours() {
      if (!this.startTime) return []

      if (moment(this.startTime).isSame(this.endTime, 'day')) {
        return Array(24)
          .fill(null)
          .map((_, index) => index)
          .slice(0, moment(this.startTime).hour())
      }
      return []
    },
    disabledEndMinutes() {
      if (!this.startTime) return []

      if (moment(this.startTime).isSame(this.endTime, 'hour')) {
        return Array(60)
          .fill(null)
          .map((_, index) => index)
          .slice(0, moment(this.startTime).minute())
      }
      return []
    },
    disabledEndSeconds() {
      if (!this.startTime) return []

      if (moment(this.startTime).isSame(this.endTime, 'hour')) {
        return Array(60)
          .fill(null)
          .map((_, index) => index)
          .slice(0, moment(this.startTime).second())
      }
      return []
    }
  }
}
</script>
<style lang="scss" scoped>
.widget-time-control {
  &-wrap {
    margin-top: 16px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
  &-text {
    flex-shrink: 0;
  }
  &-picker {
    width: 100%;
  }
}

.widget-time-control-picker-dropdown {
  ::v-deep {
    .ant-calendar-time-picker {
      z-index: 1;
    }
    .ant-calendar-footer {
      background-color: #fff;
      z-index: 2;
    }
  }
}
</style>

<style lang="scss">
.widget-time-control-picker-dropdown {
  .ant-calendar-time-picker {
    z-index: 1;
  }
  .ant-calendar-footer {
    background-color: #fff;
    z-index: 2;
  }
}
</style>
