export default {
  inject: {
    ref_lang: 'ref_lang',
    edit_lang: 'edit_lang',
    activity_id: 'activity_id',
    rootChangeLoading: 'rootChangeLoading',
    locale_info: {
      default: {
        react_data: []
      }
    },
    extra_type: {
      default: 'default'
    },
    extra_id: {
      default: -1
    }
  },
  computed: {
    commonDraftParams() {
      return {
        language: this.edit_lang,
        activity_id: this.activity_id
      }
    },
    commonGroupParams() {
      return {
        extra_id: this.extra_id,
        extra_type: this.extra_type,
        group_id: this.groupId
      }
    },
    saveDraftDataUrlKey() {
      return this.extra_type === 'ACT'
        ? 'save_activity_draft_instance_data'
        : 'save_package_instance_draft_data'
    },
    resetDraftDataUrlKey() {
      return this.extra_type === 'ACT'
        ? 'reset_activity_instance_draft_data'
        : 'reset_package_instance_draft_data'
    },
    sortDraftDataUrlKey() {
      return this.extra_type === 'ACT'
        ? 'sort_activity_instance_draft_data'
        : 'sort_package_instance_draft_data'
    },
    resetDraftGroupDataUrlKey() {
      return this.extra_type === 'ACT'
        ? 'reset_activity_instance_draft_data_by_group_ids'
        : 'reset_package_instance_draft_data_by_group_ids'
    }
  }
}
