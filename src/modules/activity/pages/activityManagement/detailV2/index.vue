<template>
  <div id="act-detail-editable" class="act-detail" :data-spm-page="getSpm">
    <Presenter
      ref="detailGroups"
      class="act-detail-box"
      extra-type="ACT"
      :readonly="readonly"
      @saveError="saveError"
    />
    <div id="quick_view_spm" :data-spm-module="getQuickViewSpm" data-spm-virtual-item="__virtual"></div>
    <div
      id="save_act_detail_spm"
      v-bind="$root.bindIHTrack({ type: 'module', spm: 'SaveActivtyDetail' })"
    ></div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import Presenter from './components/presenter.vue'

export default {
  components: {
    Presenter
  },
  inject: ['getActGlobal2provide', 'page_from'],
  data() {
    return {
      categoryInfo: {},
      readonly: false
    }
  },
  computed: {
    activityId() {
      return +this.$route.params.id
    },
    getSpm() {
      const oid = `activity_${this.activityId || 0}`
      return `ActivityDetail?oid=${oid}`
    },
    getQuickViewSpm() {
      const oid = `activity_${this.activityId || 0}`
      return `ActivityDetailQuickView?oid=${oid}&trg=manual`
    }
  },
  async created() {
    this.actGlobal = this.getActGlobal2provide()
    // 埋点
    klook.bus.$on('QuickViewClick_Spm', () => {
      this.$tracker.track('action', '#quick_view_spm')
    })
  },
  beforeDestroy() {
    klook.bus.$off('QuickViewClick_Spm')
  },
  methods: {
    ...mapActions(['getActCategory2action']),
    saveError(response) {
      this.$emit('saveError', response)
      this.$root.trackIHEvent('#save_act_detail_spm', {
        err_message: response?.error?.message ?? 'SaveActivtyDetail Error'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.act-detail {
  .act-detail-box {
    padding-bottom: 8px;
  }
}
</style>
