import { mapState } from 'vuex'
import Tooltip from '@activity/utils/v-tooltip/tooltip.js'

export default {
  data() {
    return {
      footerSaveBtnTooltip: {
        defaultVisible: false,
        visible: false,
        content: 'You may need to save the draft now',
        placement: 'topRight'
      }
    }
  },
  computed: {
    ...mapState(['activateFooterWrapperState'])
  },
  mounted() {
    this.triggerEle = document.querySelector('.js-presenter-footer-wrapper')
    this.initTransitionendListenerOnEle(this.triggerEle)
  },
  methods: {
    initTransitionendListenerOnEle(selector) {
      let element
      if (typeof selector === 'object' && selector.nodeType === 1) {
        element = selector
      } else {
        element = document.querySelector(selector)
      }

      element.addEventListener('transitionend', this.onTransitionend)
      this.$once('hook:beforeDestroy', () => {
        element.removeEventListener('transitionend', this.onTransitionend)
        this.tooltip?.unbind?.(this.saveBtnEle)
      })
    },
    onTransitionend() {
      if (this.onceTransitionend || !this.activateFooterWrapperState || !this.hasDraftData) return

      this.onceTransitionend = true
      this.saveBtnEle = this.triggerEle.querySelector('.js-save-tooltips')
      this.tooltip = new Tooltip(this.saveBtnEle, {
        content: this.$t('89272'),
        manual: true,
        placement: 'topRight',
        calcStyleFunc: (config, el, $tooltip) => {
          $tooltip.style.setProperty('--background-color', '#FFD666')

          return config
        },
        overlayStyle: {
          position: 'fixed'
        }
      })

      this.tooltip.show(true)

      setTimeout(() => {
        this.tooltip.unbind(this.saveBtnEle)
      }, 3000)
    }
  }
}
