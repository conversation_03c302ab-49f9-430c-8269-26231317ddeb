export const ACTIVATE_EDITING_EVENT_KEY = {
  close: 'close',
  activate: 'activate'
}

export const parseSecond2Date = (time = 0) => {
  let days = Math.floor(time / 1440 / 60)
  let hours = Math.floor((time - days * 1440 * 60) / 3600)
  let minutes = Math.floor((time - days * 1440 * 60 - hours * 3600) / 60)

  if (hours < 10) {
    hours = '0' + hours
  }

  if (minutes < 10) {
    minutes = '0' + minutes
  }

  return {
    days: days,
    hours: hours,
    minutes: minutes
  }
}

export const scrollIntoView = async function(
  target,
  { offsetY = -72, type = 'scrollTo', config = {}, addAnimation = true } = {}
) {
  await this.$nextTick()

  let ele = null
  if (-1 !== toString.call(target).indexOf('[object HTML')) {
    ele = target
  } else if ('[object String]' === toString.call(target)) {
    ele = document.querySelector(target)
  }

  if (!ele) return

  if (type === 'scrollTo') {
    const elementPosition = ele.getBoundingClientRect().top
    const offsetPosition = window.pageYOffset + elementPosition + offsetY

    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth',
      ...config
    })
  } else {
    ele?.scrollIntoView?.({
      behavior: 'smooth',
      block: 'center',
      ...config
    })
  }

  if (addAnimation) {
    const className = 'common-blink-scroll-into-view'
    const cb = () => {
      ele.classList.remove(className)
      ele.removeEventListener('animationend', cb)
    }
    ele.addEventListener('animationend', cb)
    document.querySelector(`.${className}`)?.classList?.remove?.(className)
    await this.$nextTick()
    ele.classList.add(className)
  }
}
