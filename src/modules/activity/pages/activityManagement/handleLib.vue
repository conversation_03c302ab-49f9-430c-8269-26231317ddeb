<template>
  <div class="handle">
    <div class="handle-box"></div>
  </div>
</template>
<script>
import { checkPassAsync } from '@activity/components/pass-standard-confirm'
import { showMerchantOnboarding } from '../../components/editLang/lib'
import { otherOptions, getOptions, getNoteData } from './utils'

const postUpdateStatus = async (data, isAct) => {
  return await ajax.postBody(
    {
      url: isAct ? ADMIN_API.act.update_status2act : ADMIN_API.act.update_status2pkg,
      data: data,
      noDefaultResponseInterceptor: true
    },
    {
      loading: true,
      msgOpt: {
        isSucMsg: false
      }
    }
  )
}
const postUnarchive = async (row) => {
  return await ajax.postBody(
    ADMIN_API.act.un_archive_package,
    {
      data: {
        package_id: row.package_id
      }
    },
    ajax.sucOptions
  )
}
const postArchive = async (row) => {
  return await ajax.postBody(
    ADMIN_API.act.archive_package,
    {
      data: {
        package_id: row.package_id
      }
    },
    ajax.sucOptions
  )
}
const postWithdrawSubmit = async (row, keyFlag) => {
  let url = keyFlag === 'activity' ? ADMIN_API.act.withdraw2activity : ADMIN_API.act.withdraw2package
  let result = await ajax.postBody(
    {
      url,
      data: {
        activity_id: row.activity_id,
        package_id: keyFlag === 'activity' ? undefined : row.package_id
      }
    },
    ajax.sucOptions
  )
  return result
}
const get_activity_submit_status = async (activity_id) => {
  return await ajax.getBody(
    ADMIN_API.act.get_activity_submit_status,
    {
      params: {
        activity_id
      },
      noDefaultResponseInterceptor: true
    },
    {
      msgOpt: {
        errContent: __('activity_required_fields')
      },
      loading: true
    }
  )
}
const submit_to_klook2package = async (data, ajaxType = 'post') => {
  return await ajax[ajaxType](
    ADMIN_API.act.submit_to_klook2package,
    {
      data,
      noDefaultResponseInterceptor: true
    },
    ajax.sucOptions
  )
}
const submit_to_klook2activity = async (data, ajaxType = 'post') => {
  return await ajax[ajaxType](
    ADMIN_API.act.submit_to_klook,
    {
      noDefaultResponseInterceptor: true, // 获取error.message
      data
      // data: {
      //   activity_id,
      //   note: '', // 原因,
      //   package_id_list: [
      //     // 活动和套餐必须都传，并且套餐属于活动,
      //   ]
      // },
    },
    ajax.sucOptions
  )
}
const get_package_submit_status = async (package_id) => {
  return await ajax.getBody(
    ADMIN_API.act.get_package_submit_status,
    {
      params: {
        package_id
      },
      noDefaultResponseInterceptor: true
    },
    {
      msgOpt: {
        errContent: __('package_required_fields')
      },
      loading: true
    }
  )
}
const get_spu_submit_status = async (spu_id) => {
  return await ajax.getBody(
    ADMIN_API.aidRevamp.get_spu_submit_status,
    {
      params: {
        spu_id
      },
      noDefaultResponseInterceptor: true
    },
    {
      msgOpt: {
        errContent: __('package_required_fields')
      },
      loading: true
    }
  )
}
const postApprove = async function (row, isAct) {
  let { activity_id, package_id, note, ticket_type, sku_id } = row

  // 保留旧逻辑通过 isAct 区分
  if (isAct !== undefined) {
    ticket_type = isAct ? 'new_activity' : 'new_package'
  }

  let current = {
    new_package: {
      url: ADMIN_API.act.pkgApprove,
      data: { activity_id, package_id, note }
    },
    new_activity: {
      url: ADMIN_API.act.actApprove,
      data: { activity_id, note }
    },
    cost_modification: {
      url: ADMIN_API.act.update_ticket_status,
      data: {
        sku_id,
        action: 0,
        note
      }
    }
  }[ticket_type]

  return await ajax.postBody(
    current.url,
    {
      data: current.data
    },
    ajax.sucOptions
  )
}
const postReject = async function (row, isAct) {
  let { activity_id, package_id, note, ticket_type, sku_id, value } = row

  // 保留旧逻辑通过 isAct 区分
  if (isAct !== undefined) {
    ticket_type = isAct ? 'new_activity' : 'new_package'
  }

  const noteData = getNoteData(value, note)

  let current = {
    new_package: {
      url: ADMIN_API.act.pkgReject,
      data: { activity_id, package_id, note: noteData }
    },
    new_activity: {
      url: ADMIN_API.act.actReject,
      data: { activity_id, note: noteData }
    },
    cost_modification: {
      url: ADMIN_API.act.update_ticket_status,
      data: {
        sku_id,
        action: 1,
        note: noteData
      }
    }
  }[ticket_type]

  return await ajax.postBody(
    current.url,
    {
      data: current.data
    },
    ajax.sucOptions
  )
}
export default {
  data() {
    return {}
  },
  methods: {
    strformat: klook.strformat,
    async handleWithdrawSubmit(row, options = {}) {
      let { keyFlag, suc, err } = options
      let title =
        keyFlag === 'activity'
          ? klook.parseStr1(__('merchant_withdraw_activity'), {
              'AID-Name': `${row.activity_id}-${row.activity_title}`
            })
          : klook.parseStr1(__('merchant_withdraw_package'), {
              'PID-Name': `${row.package_id}-${row.package_title}`
            })
      this.$modal.confirm({
        zIndex: 1111,
        title,
        maskClosable: true,
        cancelText: this.$t('global_cancel'),
        okText: this.$t('global_submit'),
        // content: (h) => node,
        onOk: async () => {
          let reqData = await postWithdrawSubmit(row, keyFlag)
          if (reqData && reqData.success) {
            suc && suc()
            this.$store.commit('setLockMerchantActEditing', false)
          } else {
            err && err()
          }
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    },
    get_activity_submit_status,
    submit_to_klook2package,
    submit_to_klook2activity,
    get_package_submit_status,
    get_spu_submit_status,
    handleApprove(row, options = {}) {
      let { isAct, suc, err } = options
      row.note = ''
      let title =
        isAct || row.ticket_type === 'new_activity'
          ? klook.parseStr1(__('activity_approve'), {
              'AID-Name': `${row.activity_id}-${row.activity_title}`
            })
          : klook.parseStr1(__('package_approve'), {
              'PID-Name': `${row.package_id}-${row.package_title}`
            })
      // let node = (
      //   <a-textarea
      //     placeholder={__('global_please_input')}
      //     v-model={row.note}
      //     rows={4}
      //     maxLength={500}
      //   ></a-textarea>
      // )
      this.$modal.confirm({
        title,
        maskClosable: true,
        cancelText: this.$t('global_cancel'),
        okText: this.$t('global_submit'),
        // content: (h) => node,
        onOk: async () => {
          let reqData = await postApprove(row, isAct)
          if (reqData && reqData.success) {
            suc && suc()
          } else {
            err && err()
          }
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    },
    async handleReject(row, options = {}) {
      let { isAct, suc, err } = options
      row.note = ''
      let title = isAct || row.ticket_type === 'new_activity' ? __('activity_reject') : __('package_reject')
      let okButtonProps = {
        props: {
          disabled: true
        }
      }
      const url = isAct ? 'get_act_reject_reason' : 'get_pkg_reject_reason'
      const cache = isAct ? this.actRejectOptions : this.pkgRejectOptions
      // 用缓存
      const opts = cache || (await getOptions(ADMIN_API.act[url], { loading: true }))
      if (isAct) {
        this.actRejectOptions = opts
      } else {
        this.pkgRejectOptions = opts
      }
      let node = (
        <a-form-model>
          <a-form-model-item label={__('85321')} required>
            <a-cascader
              options={opts}
              onchange={(e) => {
                const value = e
                row.value = value
                const validate = value.some((val) => {
                  return otherOptions.includes(val)
                })
                okButtonProps.props.disabled = value.length === 0 || (!row.note && validate)
              }}
            ></a-cascader>
          </a-form-model-item>
          <a-form-model-item label="Please specify your reason here">
            <a-textarea
              v-model={row.note}
              placeholder={__('global_please_input')}
              rows={4}
              onkeyup={(e) => {
                const value = row.value || []
                row.note = _.trim(e.target.value)
                const validate = value.some((val) => {
                  return otherOptions.includes(val)
                })
                okButtonProps.props.disabled = value.length === 0 || (!row.note && validate)
              }}
              maxLength={3000}
            ></a-textarea>
          </a-form-model-item>
        </a-form-model>
      )
      this.$modal.confirm({
        title,
        maskClosable: true,
        cancelText: this.$t('global_cancel'),
        okText: this.$t('global_submit'),
        okButtonProps,
        content: (h) => node,
        onOk: async () => {
          let reqData = await postReject(row, isAct)
          if (reqData && reqData.success) {
            suc && suc()
          } else {
            err && err()
          }
        },
        onCancel() {
          row.value = []
        }
      })
    },
    handlerArchive(row, options = {}) {
      let { isAct, suc, err } = options
      row.note = ''
      let title = __('merchant_archive_package')
      this.$modal.confirm({
        zIndex: 1111,
        title,
        maskClosable: true,
        cancelText: this.$t('global_cancel'),
        okText: this.$t('global_submit'),
        // content: (h) => node,
        onOk: async () => {
          let reqData = await postArchive(row, isAct)
          if (reqData && reqData.success) {
            suc && suc()
          } else {
            err && err()
          }
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    },
    handlerUnarchive(row, options = {}) {
      let { isAct, suc, err } = options
      row.note = ''
      let title = __('merchant_unarchive_package')
      this.$modal.confirm({
        zIndex: 1111,
        title,
        maskClosable: true,
        cancelText: this.$t('global_cancel'),
        okText: this.$t('global_submit'),
        // content: (h) => node,
        onOk: async () => {
          let reqData = await postUnarchive(row, isAct)
          if (reqData && reqData.success) {
            suc && suc()
          } else {
            err && err()
          }
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    },
    async handlerUpdateStatus(row, options = {}) {
      let { isAct, suc, err, reasons = [], item } = options
      let data = options.data
      data.reason = ''
      data.reason_code = undefined
      let title = row.text
      let okButtonProps = {
        props: {
          disabled: false
        }
      }
      let node = ''
      if (data.status === 0 && isAct) {
        // 只有活动：取消发布
        okButtonProps.props.disabled = true
        node = (
          <div class="unpublish-content-box">
            <p>{klook.strformat(__('unpublish_title'), data.activity_id)}</p>
            <a-form-model>
              <a-form-model-item label={__('publish_unpublish_reason')}>
                <a-select
                  dropdownStyle={{ zIndex: 2222, width: '300px' }}
                  showSearch
                  placeholder={__('global_please_select')}
                  optionFilterProp="children"
                  onchange={(value) => {
                    data.reason_code = value
                    okButtonProps.props.disabled = data.reason_code === 299
                    let dom = document.getElementById('unpublish-textarea')
                    dom.value = ''
                    dom.style = `display: ${data.reason_code === 299 ? 'block' : 'none'};`
                  }}
                >
                  {reasons.map((item) => {
                    return <a-select-option value={item.code}>{item.reason_msg}</a-select-option>
                  })}
                </a-select>
              </a-form-model-item>
            </a-form-model>
            <a-textarea
              id="unpublish-textarea"
              style={`display: ${data.reason_code === 299 ? 'block' : 'none'};`}
              ref="unpublishTextarea"
              value={data.reason}
              placeholder={__('global_please_input')}
              rows={2}
              onkeyup={(e) => {
                data.reason = _.trim(e.target.value)
                okButtonProps.props.disabled = !data.reason
              }}
              maxLength={500}
            ></a-textarea>
          </div>
        )
      }

      const onOK = async () => {
        let reqData = await postUpdateStatus(data, isAct)
        if (reqData && reqData.success) {
          if (row.status === 1 && item && item.show_on_merchant_self_onboarding) {
            item.show_on_merchant_self_onboarding.can_show === 2 && showMerchantOnboarding(item)
          }
          suc && suc(reqData)
        } else {
          err && err(reqData)
        }
      }

      // 0: 'Unpublished',
      // 1: 'Published',
      // 2: 'Draft',
      // 3: 'EM to edit',s
      // 4: 'Preview'
      // seo 页面触发 key 是 submit_to_preview, 但此时 nex status 不是 4，则不需要 confirm
      if (row.key === 'submit_to_preview' && !row.status_list.includes(4)) {
        return
      }

      // 下架活动、套餐、sku的时候需要检查是否是 pass standard
      if (data.status === 0) {
        const params = {}
        if (data.activity_id) {
          params.activity_id = data.activity_id
        } else if (data.package_id) {
          params.package_ids = data.package_id
        }

        const { stop, force } = await checkPassAsync(params, this.$i18n, async (res) => {
          // 出现弹窗并点击确认，直接发起下架请求
          if (res && res.force) {
            data.reason = res.reason
            data.reason_code = res.reasonCode
            data.force = res.force
            await onOK()
          }
        })

        // 出现弹窗并点击取消，直接返回; 出现弹窗并点击确认，直接发起下架请求，也直接返回
        if (stop || force) {
          return
        }
      }

      this.$modal.confirm({
        zIndex: 1111,
        title,
        maskClosable: true,
        cancelText: this.$t('global_cancel'),
        okText: this.$t('global_submit'),
        okButtonProps,
        content: (h) => node,
        onOk: async () => {
          await onOK()
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.handle {
  .handle-box {
    //
  }
}
</style>
