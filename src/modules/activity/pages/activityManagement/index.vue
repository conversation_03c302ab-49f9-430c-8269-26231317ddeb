<template>
  <div id="page_spm" class="activity-management" :data-spm-page="getPageSpm">
    <!-- 如果要关闭公告提示，请修改showCommonNotify为false即可 -->
    <a-alert
      v-if="showCommonNotify"
      class="common-alert-style"
      style="position: fixed; top: 0; left: 0; z-index: 9999; width: 100%"
      type="warning"
      closable
      show-icon
    >
      <span slot="message" v-html="$t('44206')"></span>
    </a-alert>
    <div class="activity-management-box">
      <div class="act-tab-box" v-if="isMerchant">
        <a-tabs
          v-model="tabObj.key"
          class="common-tabs-style"
          :type="tabObj.type"
          :default-active-key="tabObj.key"
          :tab-bar-gutter="0"
          @change="tabObj.handleChange"
        >
          <a-tab-pane v-for="tab in tabObj.tabs" :key="tab.key" :tab="tab.title" />
        </a-tabs>
      </div>

      <div v-if="isMerchant" class="act-select">
        <div v-for="item in filterSelect" :key="item.key" class="act-select-box">
          <span class="label-box">{{ $t(`${item.$t}`) + ':' }}</span>
          <template v-if="item.formType === 'treeSelect' || item.treeData">
            <a-tree-select
              v-model="item.value"
              class="common-select-style common-icon-select-down"
              allow-clear
              :max-tag-count="1"
              treeCheckable
              :tree-data="item.treeData"
              :placeholder="item.placeholder || $t('global_please_select')"
              tree-default-expand-all
              treeNodeFilterProp="title"
            >
            </a-tree-select>
          </template>
          <template v-else-if="item.formType === 'cascader' || item.displayRender">
            <a-cascader
              v-model="item.value"
              class="common-ascader-style"
              :options="item.options"
              :display-render="item.displayRender"
              expand-trigger="hover"
              :max-tag-count="1"
              :placeholder="item.placeholder || $t('global_please_select')"
            />
          </template>
          <template v-else-if="item.formType === 'select' || item.options">
            <lazy-long-select
              v-model="item.value"
              class="common-select-style"
              style="display: inline-block; width: 252px"
              opt-value-key="value"
              opt-label-key="label"
              :select-hint="item.placeholder || $t('global_please_select')"
              :ops-length="50"
              :multiple="true"
              :fmtLabelFn="item.fmtLabelFn || null"
              :full-opts="item.options"
              :max-tag-count="1"
              show-arrow
              allow-clear
              optionFilterProp="children"
            />
          </template>
          <template v-else-if="item.formType === 'input-search' || !item.options">
            <a-input
              v-model="item.value"
              class="common-input-style"
              :placeholder="item.placeholder || $t('global_please_input')"
              :maxLength="100"
              allow-clear
            />
          </template>
        </div>
        <div class="act-btn">
          <div class="act-btn-box">
            <a-button
              :data-spm-module="getSearchSpm"
              data-spm-virtual-item="__virtual"
              type="primary"
              @click="clickSearchBtn"
            >
              {{ $t('global_button_search') }}
            </a-button>
            <a-button type="link" @click="clickResetBtn">{{ $t('global_reset') }}</a-button>
          </div>
        </div>
      </div>
      <div v-else>
        <schema-form
          :schema-config="adminActListSearchSchemaForm"
          @reset="handleSubmitAdminSearch"
          @submit="handleSubmitAdminSearch"
        />
      </div>

      <div class="act-table">
        <div v-show="showCreateBtn" class="btn-create-box">
          <a-button v-if="calcShowStepReminderBtn" type="default" @click="handleShowStepReminderModal">
            {{ $t('174933') }}
          </a-button>
          <a-button
            :data-spm-module="getCreateSpm"
            data-spm-virtual-item="__virtual"
            type="primary"
            icon="plus"
            @click="createNew"
          >
            <span>{{ $t('act_create_new_activity') }}</span>
          </a-button>
        </div>

        <div class="act-table-box" :class="{ 'clear-padding': isAdmin }">
          <tableAntd
            v-if="tableData.show && calcIsRichTable"
            v-bind="{ tableData, pageData }"
            class="act-table-box-antd"
            :display-total="calcIsRichTable"
            :show-pagination="calcIsRichTable"
            @pageChange="handlePageChange"
          >
            <template slot="ticket_id" slot-scope="id, row">
              <span v-if="row.ticket_status === 'Closed'" style="padding-left: 16px">{{
                row.ticket_id
              }}</span>
              <QuickReviewTicket v-else :data="row" @refresh="clickSearchBtn">
                {{ row.ticket_id }}
              </QuickReviewTicket>
            </template>

            <template slot="ticket_status" slot-scope="val, row">
              <div
                style="display: inline-block"
                :class="[
                  'approval-status-style',
                  mapsFilterFn('ticket_status', val, 'class'),
                  row['package_archived'] ? 'archived' : ''
                ]"
              >
                <span v-if="val === 3 && !row.ticket_note">{{ mapsFilterFn('ticket_status', val) }} </span>
                <a-tooltip v-else overlayClassName="common-tooltip-style" :title="row.ticket_note">
                  <span>{{ mapsFilterFn('ticket_status', val) }}</span>
                </a-tooltip>
                <a-icon
                  :style="{ cursor: row['package_archived'] ? 'text' : '' }"
                  :class="['icon-style', mapsFilterFn('ticket_status', val, 'class')]"
                  @click="handleTicketStatusChangeLog(row, $event)"
                  type="clock-circle"
                  class="clock-circle-style"
                />
              </div>
              <!-- reject判断是否需要显示 -->
              <div
                v-if="row.tag"
                style="display: inline-block; margin-left: 8px"
                class="approval-status-style closed"
              >
                <span>{{ row.tag }}</span>
                <RejectInfoPopover :ticket-id="row.ticket_id">
                  <template slot="trigger">
                    <a-icon class="icon-style closed" type="clock-circle" />
                  </template>
                </RejectInfoPopover>
              </div>
            </template>

            <template slot="sku_status" slot-scope="val, row">
              <span
                v-if="['cost_modification'].includes(row.ticket_type)"
                :class="[
                  'common-status-style',
                  mapsFilterFn('sku_status', val, 'class'),
                  row['package_archived'] ? 'archived' : ''
                ]"
              >
                {{ mapsFilterFn('sku_status', val) }}
              </span>
              <span v-else></span>
            </template>

            <template slot="ticket_type" slot-scope="val, row">
              <span
                :class="[
                  'common-status-style',
                  mapsFilterFn('ticket_type', val, 'class'),
                  row['package_archived'] ? 'archived' : ''
                ]"
              >
                {{ mapsFilterFn('ticket_type', val) }}
              </span>
            </template>

            <template slot="sku_id" slot-scope="id, row">
              <span v-if="row.package_archived" :title="`${row.sku_id} - ${row.sku_title}`">{{
                `${row.sku_id} - ${row.sku_title}`
              }}</span>
              <router-link
                v-else-if="row.sku_id"
                :to="{
                  path: getSkuPath(row)
                }"
                target="_blank"
                ><span :title="`${row.sku_id} - ${row.sku_title}`">{{
                  `${row.sku_id} - ${row.sku_title}`
                }}</span></router-link
              >
              <span v-else>-</span>
            </template>

            <template slot="package_id" slot-scope="id, row">
              <span v-if="row.package_archived" :title="`${id} - ${row.package_title}`">{{
                `${id} - ${row.package_title}`
              }}</span>
              <router-link
                v-else-if="row.package_id"
                :to="{
                  path: getPkgPath(row)
                }"
                target="_blank"
                ><span :title="`${id} - ${row.package_title}`">{{
                  `${id} - ${row.package_title}`
                }}</span></router-link
              >
              <span v-else>-</span>
            </template>

            <template slot="activity_id" slot-scope="id, row">
              <span v-if="row.package_archived" :title="`${id} - ${row.activity_title}`">{{
                `${id} - ${row.activity_title}`
              }}</span>
              <router-link v-else :to="getActPath(row)" target="_blank">
                <span :title="`${id} - ${row.activity_title}`">
                  {{ `${id} - ${row.activity_title}` }}
                </span>
              </router-link>
            </template>

            <template slot="package_count" slot-scope="pkgCount, row">
              <span
                v-if="pkgCount > 0"
                :data-spm-module="getDataSpm('PackageQuantity', row.activity_id)"
                data-spm-virtual-item="__virtual"
                class="number-right common-link"
                @click="handleGoPackage(row)"
              >
                {{ pkgCount }}
              </span>
              <span v-else class="number-right">{{ pkgCount }}</span>
            </template>
            <template slot="units" slot-scope="val, row">
              <template v-if="row.sku_count">
                <a-button
                  type="link"
                  :data-spm-module="getDataSpm('UnitQuantity', row.activity_id)"
                  data-spm-virtual-item="__virtual"
                  @click="handleShowUnits(row)"
                >
                  {{ row.sku_count || '-' }}
                </a-button>
              </template>
              <span v-else style="padding: 0 15px">-</span>
            </template>
            <template slot="approval_status" slot-scope="val, row">
              <div
                :class="[
                  'approval-status-style',
                  mapsFilterFn('approval_status', val, 'class'),
                  row['package_archived'] ? 'archived' : ''
                ]"
              >
                <span v-if="val === 3 && !row.note">{{ mapsFilterFn('approval_status', val) }} </span>
                <a-tooltip v-else overlayClassName="common-tooltip-style" :title="row.note">
                  <span>{{ mapsFilterFn('approval_status', val) }}</span>
                </a-tooltip>
                <a-icon
                  :data-spm-module="getDataSpm('ApprovalStatusLog', row.activity_id)"
                  data-spm-virtual-item="__virtual"
                  :class="['icon-style', mapsFilterFn('approval_status', val, 'class')]"
                  :style="{ cursor: row['package_archived'] ? 'text' : '' }"
                  type="clock-circle"
                  @click="clickChangeLog(row, $event)"
                />
              </div>
            </template>
            <template slot="activity_status" slot-scope="val, row">
              <span
                :class="[
                  'common-status-style',
                  platformFn('platform_activity_status', val, 'class'),
                  row['package_archived'] ? 'archived' : ''
                ]"
                >{{ platformFn('platform_activity_status', val) }}</span
              >
            </template>
            <template slot="migrated_to_spu" slot-scope="val, row">
              {{ row.migrated_to_spu ? $t('global_yes') : $t('global_no') }}
            </template>
            <template slot="activity_language_view" slot-scope="val, row">
              <a-button
                :data-spm-module="getDataSpm('PublishLanguageView', row.activity_id)"
                data-spm-virtual-item="__virtual"
                :disabled="!row.submitted_language"
                type="link"
                @click="clickActLangView(val, row)"
              >
                {{ $t('global_view') }}
                <!-- 埋点 -->
                <span
                  :id="'switch_' + row.activity_id"
                  :data-spm-module="getDataSpm('SwitchEditableLanguage', row.activity_id)"
                  data-spm-virtual-item="__virtual"
                ></span>
                <span
                  :id="'change_' + row.activity_id"
                  :data-spm-module="getDataSpm('ChangeMerchantActMgmtLanguage', row.activity_id)"
                  data-spm-virtual-item="__virtual"
                ></span>
              </a-button>
            </template>
            <template slot="package_status" slot-scope="val, row">
              <span
                v-if="['new_package', 'cost_modification'].includes(row.ticket_type) || isMerchant"
                :class="[
                  'common-status-style',
                  mapsFilterFn('package_status', val, 'class'),
                  row['package_archived'] ? 'archived' : ''
                ]"
              >
                {{ mapsFilterFn('package_status', val) }}
              </span>
              <span v-else></span>
            </template>
            <template slot="package_archived" slot-scope="val">
              <span
                :class="[
                  'common-status-style',
                  mapsFilterFn('package_archived', val, {
                    mapKey: 'mapValue',
                    retKey: 'class'
                  })
                ]"
                >{{ mapsFilterFn('package_archived', val, { mapKey: 'mapValue' }) }}</span
              >
            </template>
            <template slot="approval_activity" slot-scope="_, row">
              <div class="action-box common-action-style">
                <!-- 临时关闭审核按钮，待后端修复对应的问题 -->
                <span v-if="row.ticket_status === 'Closed'">-</span>
                <QuickReviewTicket v-else :data="row" @refresh="clickSearchBtn">
                  {{ $t('78835') }}
                </QuickReviewTicket>
              </div>
            </template>
            <template slot="action2admin" slot-scope="_, row">
              <div class="action-box common-action-style ta-center">
                <a-button
                  type="link"
                  size="small"
                  :disabled="getFilterDisabled(row, { type: 'preview' })"
                  @click="actionObj.handlerPreview(row)"
                >
                  {{ $t('global_preview') }}
                </a-button>
              </div>
            </template>
            <!-- merchant actions -->
            <template slot="action" slot-scope="_, row">
              <div class="action-box common-action-style">
                <a-button
                  v-for="(action, i) in getFilterActions(row).filter((_, i) => i < 1)"
                  :key="i"
                  type="link"
                  size="small"
                  :disabled="getFilterDisabled(row, action)"
                  v-bind="action.bind"
                  @click="action.on.click(row, `#${action.type}_${i}_${row.activity_id}`)"
                >
                  {{ action.text }}
                </a-button>
                <a-popover
                  v-if="getFilterActions(row).length > 1"
                  placement="topRight"
                  overlay-class-name="common-popover-style common-action-style"
                >
                  <template slot="content">
                    <div
                      v-for="(action, i) in getFilterActions(row).filter((_, i) => i >= 1)"
                      :key="i"
                      class="common-more-box"
                    >
                      <a-button
                        type="link"
                        size="small"
                        :disabled="getFilterDisabled(row, action)"
                        v-bind="action.bind"
                        @click="action.on.click(row, `#${action.type}_${i + 1}_${row.activity_id}`)"
                      >
                        {{ action.text }}
                      </a-button>
                    </div>
                  </template>
                  <a-button class="btn-more-style" type="link" size="small">{{ $t('btn.more') }}</a-button>
                </a-popover>
              </div>
            </template>
          </tableAntd>

          <a-empty v-else class="act-table--empty">
            <span v-if="calcShowActCreationStepReminder" slot="description">
              {{ $t('174915') }}
            </span>
          </a-empty>

          <StepDiagram v-if="calcShowActCreationStepReminder" class="act-creation-step-reminder" />
        </div>
      </div>
    </div>
    <template v-if="!$root.nonEdit2status">
      <ChooseCategory v-model="chooseCategory.visible" />
    </template>
    <stkModal :modal-data="stkData" style="z-index: 1001" />

    <logs-table
      v-model="logData.visible"
      :log-data="logData"
      :custom-columns="customColumns"
      class="change-log-modal"
    />

    <editLangModal :editLang="editLang" />

    <approveList
      style="z-index: 999"
      v-if="approveList.visible"
      v-model="approveList.visible"
      v-bind="approveList"
    />

    <UnitsApprovalList
      v-if="unitsDrawer.visible"
      :visible.sync="unitsDrawer.visible"
      v-bind="unitsDrawer"
      @refresh="get_activities"
    />

    <ActCreationStepReminderModal v-model="stepReminderModal.visible" />

    <!-- 埋点 -->
    <div
      id="save-new-activity-track"
      data-spm-module="SaveNewActivity?trg=manual"
      data-spm-virtual-item="__virtual"
    ></div>
    <div id="pkg_tabs_spm" data-spm-module="PackageTab?trg=manual" data-spm-virtual-item="__virtual"></div>
    <div v-for="(row, index) in tableData.dataSource" :key="index">
      <div
        v-for="(action, i) in getFilterActions(row)"
        :id="`${action.type}_${i}_${row.activity_id}`"
        :key="i"
        :data-spm-module="getActionSpm(action.type, row.activity_id)"
        data-spm-virtual-item="__virtual"
      ></div>
    </div>
  </div>
</template>

<script>
import editLangModal from '@activity/components/editLang/viewModal'
import tableAntd from '@activity/components/tableAntd'
import dataLib from './dataLib'
import handleLib from './handleLib'
import { checkAuth } from '@/plugins/authDirective'
const {
  isMerchant,
  isAdmin,
  platform,
  actColumns,
  pkgColumns,
  allTicketSearchSchemaFormConfig,
  ticketLogColumns
} = dataLib
import { getParamsKey } from '@activity/utils'
import maps from '@activity/utils/maps.js'
import LazyLongSelect from '../../components/lazy-long-select/index'
import LogsTable from './logsTable'
import SchemaForm from './approveList/components/schemaForm'
import approveListComponent from '@activity/pages/activityManagement/approveList/index.vue'
import { approve_list_mixin, act_management_mixin } from './mixins/index'
import ApproveList from './approveList/index'
import UnitsApprovalList from './approveList/units'
import ChooseCategory from '@activity/pages/activityManagement/choose-category/index.vue'
import ActCreationStepReminderModal from '@activity/pages/activityManagement/components/act-creation-step-reminder-modal.vue'
import StepDiagram from '@activity/pages/activityManagement/components/step-diagram.vue'
import RejectInfoPopover from '@activity/components/reject-info-alert/reject-info-popover.vue'
import QuickReviewTicket from '~src/modules/activity/pages/activityManagement/approveList/ticket/components/quick-review-ticket.vue'

import { activityUnablePreviewStatus } from '@activity/utils/const.js'

const admin_platform_mixin = {
  data() {
    const that = this

    return {
      ticketLogColumns,

      statusFilterTabs: {
        current: 1,
        total: 0
      },
      adminSearchFormData: {},

      adminActListSearchSchemaForm: {
        ...allTicketSearchSchemaFormConfig,
        formItems: [
          {
            field: 'ticket_id'
          },
          {
            field: 'ticket_type'
          },
          {
            field: 'ticket_status'
          },
          {
            field: 'submitted_time'
          },
          {
            field: 'merchant_id',
            inputConfig: {
              async getOptions() {
                if (that.allOptions) {
                  return that.allOptions.merchant
                }

                let tempData
                let interceptor = that.pmsInterceptor()

                that.$on('getOptions', (data) => {
                  tempData = data
                  interceptor.done()
                })

                await interceptor

                return tempData.merchant
              }
            }
          },
          {
            field: 'activity_id',
            inputConfig: {
              async getOptions() {
                if (that.allOptions) {
                  return that.allOptions.activity
                }

                let tempData
                let interceptor = that.pmsInterceptor()

                that.$on('getOptions', (data) => {
                  tempData = data
                  interceptor.done()
                })

                await interceptor

                return tempData.activity
              }
            }
          },
          {
            field: 'package_id',
            inputConfig: {
              async getOptions() {
                if (that.allOptions) {
                  return that.allOptions.package
                }

                let tempData
                let interceptor = that.pmsInterceptor()

                that.$on('getOptions', (data) => {
                  tempData = data
                  interceptor.done()
                })

                await interceptor

                return tempData.package
              }
            }
          },
          {
            field: 'sku_id',
            inputConfig: {
              async getOptions() {
                // if (that.allOptions) {
                //   return that.allOptions.sku
                // }

                let tempData
                let interceptor = that.pmsInterceptor()

                that.$on('getOptions', (data) => {
                  tempData = data
                  interceptor.done()
                })

                await interceptor

                return tempData.sku
              }
            }
          },
          {
            field: 'activity_responsible_bd'
          },
          {
            field: 'market_region',
            inputConfig: {
              async getOptions() {
                if (that.allOptions) {
                  return that.allOptions.market_region
                }

                let tempData
                let interceptor = that.pmsInterceptor()

                that.$on('getOptions', (data) => {
                  tempData = data
                  interceptor.done()
                })

                await interceptor

                return tempData.market_region
              }
            }
          },
          {
            field: 'migrated_to_spu'
          },
          {
            field: 'country',
            inputConfig: {
              async getOptions() {
                if (that.allOptions) {
                  return that.allOptions.country
                }

                let tempData
                let interceptor = that.pmsInterceptor()

                that.$on('getOptions', (data) => {
                  tempData = data
                  interceptor.done()
                })

                await interceptor

                return tempData.country
              }
            }
          },
          {
            field: 'city',
            inputConfig: {
              async getOptions() {
                if (that.allOptions) {
                  return that.allOptions.city
                }

                let tempData
                let interceptor = that.pmsInterceptor()

                that.$on('getOptions', (data) => {
                  tempData = data
                  interceptor.done()
                })

                await interceptor

                return tempData.city
              }
            }
          },
          {
            field: 'category',
            inputConfig: {
              async getOptions() {
                if (that.allOptions) {
                  return that.allOptions.sub_category
                }

                let tempData
                let interceptor = that.pmsInterceptor()

                that.$on('getOptions', (data) => {
                  tempData = data
                  interceptor.done()
                })

                await interceptor

                return tempData.sub_category
              }
            }
          },
          {
            field: 'activity_status'
          }
        ].map((item) => {
          let curr = _.find(allTicketSearchSchemaFormConfig.formItems, { field: item.field })

          return {
            ...curr,
            ...item,
            inputConfig: {
              ...(curr.inputConfig || {}),
              ...(item.inputConfig || {})
            }
          }
        })
      }
    }
  },
  async mounted() {
    await this.$nextTick()

    if (this.isAdmin) {
      let response = await ajax.post(ADMIN_API.act.filter_ticket_option_for_admin, {
        data: {}
      })

      response = response || {}
      this.allOptions = response
      this.$emit('getOptions', response)
    }
  },
  beforeDestroy() {
    this.$off('getOptions')
    klook.bus.$off('sendSwitchSpm', this.sendSwitchSpm)
    klook.bus.$off('sendChangeLangSpm', this.sendChangeLangSpm)
  },
  methods: {
    pmsInterceptor() {
      let _resolve
      let pms = new Promise((resolve) => {
        _resolve = resolve
      })

      pms.done = function done() {
        _resolve()
      }

      return pms
    },
    handleTicketStatusChangeLog: approveListComponent?.methods?.handleTicketStatusChangeLog,
    async handleSubmitAdminSearch(form) {
      this.pageData.current = 1
      this.$set(this, 'adminSearchFormData', form)

      this.searchActTableData(form)
    },
    getActivityOnAdmin() {
      this.searchActTableData(this.adminSearchFormData)
    },
    getActPath(row) {
      const { activity_id, package_id = '', submitted_language = '' } = row
      const pageFrom = isMerchant ? '' : '&page_from=bd_audit'
      if (row.had_been_inherit_spu) {
        return `/aid/tours/basic/${activity_id}?lang=${submitted_language}&package_id=${package_id}${pageFrom}`
      }

      return `/act/activity/basic/${activity_id}?lang=${submitted_language}${pageFrom}`
    },
    getPkgPath(row) {
      const { activity_id, package_id, submitted_language } = row
      const pageFrom = isMerchant ? '' : '&page_from=bd_audit'
      const path = row.had_been_inherit_spu ? '/aid/tours/basic' : '/act/package/info'

      return `${path}/${activity_id}?lang=${submitted_language}&package_id=${package_id}&package_type=1${pageFrom}`
    },
    getSkuPath(row) {
      const { activity_id, package_id = '', submitted_language = '', sku_id = '' } = row
      const pageFrom = isMerchant ? '' : '&page_from=bd_audit'

      if (row.had_been_inherit_spu) {
        return `/aid/tours/inventory-schedule/${activity_id}?ref=&package_id=${package_id}&lang=${submitted_language}${pageFrom}`
      }

      return isMerchant
        ? `/act/package/unit/${activity_id}?lang=${submitted_language}&package_id=${package_id}&package_type=1&sku_id=${sku_id}`
        : `/act/package/schedule/${activity_id}?lang=${submitted_language}&package_id=${package_id}&package_type=1${pageFrom}`
    },
    searchActTableData: _.debounce(async function (data) {
      let { tableData, pageData } = this
      const { current, pageSize } = pageData
      tableData.loading = true

      const response = await ajax.post(ADMIN_API.act.filter_ticket, {
        data: {
          ...data,
          ticket_id: Array.isArray(data.ticket_id)
            ? data.ticket_id
            : data.ticket_id
            ? [Number(data.ticket_id)]
            : [],
          page: current,
          limit: pageSize
        }
      })

      this.pageData.total = response ? response.total : 0
      tableData.dataSource = response ? response.items : []
      tableData.loading = false
    }, 200)
  }
}

export default {
  name: 'ActivityManagement',
  components: {
    SchemaForm,
    LogsTable,
    LazyLongSelect,
    editLangModal,
    tableAntd,
    UnitsApprovalList,
    ApproveList,
    ChooseCategory,
    ActCreationStepReminderModal,
    StepDiagram,
    RejectInfoPopover,
    QuickReviewTicket
  },
  mixins: [approve_list_mixin, admin_platform_mixin, act_management_mixin],
  provide: function () {
    return {
      activityManagement: this
    }
  },
  data() {
    return {
      stepReminderModal: {
        visible: false
      },
      isInited: false,
      showActCreationStepReminder: false,
      showStepReminderBtn: false,
      showCommonNotify: false,
      editLang: {
        visible: false,
        row: {}
      },
      isMerchant,
      isAdmin,
      adminObj: {
        handleViewBasicInfo: (row) => {
          this.actionObj.handlerView(row)
        },
        handleApprove: async (row) => {
          const { ticket_id, ticket_type } = row
          if (ticket_type === 'cost_modification') {
            this.handleCostModificationApprove({ ticket_id })

            return
          }

          handleLib.methods.handleApprove.call(this, row, {
            isAct: this.isMerchant ? this.isAct : undefined,
            suc: () => {
              this.get_activities()
            }
          })
        },
        handleReject: (row) => {
          const { ticket_type, sku_id } = row
          if (ticket_type === 'cost_modification') {
            this.handleCostModificationReject({ sku_id })
            return
          }

          handleLib.methods.handleReject.call(this, row, {
            isAct: this.isMerchant ? this.isAct : undefined,
            suc: () => {
              this.get_activities()
            }
          })
        }
      },
      antSelectOpen: false,
      logData: {
        visible: false,
        loading: false,
        logTable: {
          dataSource: []
        }
      },
      reqData: {
        params: {
          // platform2admin
          merchant_ids: '',
          // activity
          activity_status: undefined, // 不传时表示所有状态, 0:下架的, 1:发布的,
          approval_status: undefined, // ,
          leaf_category_ids: undefined, // ,
          city_ids: undefined, // ,
          keyword: '', // ,
          limit: 10, // ,
          page: 1, // ,
          // package
          activity_keyword: '',
          package_status: undefined,
          archive_status: undefined
        }
      },
      chooseCategory: {
        visible: false
      },
      tableData: {
        show: true,
        loading: false,
        dataSource: [],
        columns: actColumns,
        rowKey: (r) => {
          return r.ticket_id || `${r.activity_id}${r.package_id || ''}`
        },
        rowClassName: (r, i) => {
          return r.package_archived ? 'common-row-archived' : ''
        }
      },
      pageData: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      tabObj: {
        type: 'card',
        key: 'activity',
        tabs: [
          { key: 'activity', title: __('global_activity') },
          { key: 'package', title: __('global_package') }
        ],
        handleChange: (key, option) => {
          this.tableData.show = false
          this.$nextTick(() => {
            this.tableData.show = true
          })
          this.tabObj.key = key
          this.tableData.dataSource = []
          this.tableData.columns = this.tableColumns
          this.clickResetBtn(option)
        }
      },
      filterObj: {
        list: [
          {
            $t: 'merchant_id',
            platform: 'ADMIN',
            key: 'merchant_id',
            paramsKey: 'merchant_ids',
            options: [],
            fmtLabelFn(opt) {
              return opt.title
            }
          },
          {
            $t: 'package_id_title',
            tabKey: 'package',
            key: 'pkgKeyword',
            paramsKey: 'keyword',
            defVal: ''
          },
          {
            $t: 'activity_id_title',
            tabKey: 'package',
            key: 'actKeyword',
            paramsKey: 'activity_keyword',
            defVal: ''
          },
          {
            $t: 'activity_id_title',
            tabKey: 'activity',
            key: 'actKeyword',
            paramsKey: 'keyword',
            defVal: ''
          },
          {
            $t: 'global_city',
            key: 'city',
            paramsKey: 'city_ids',
            options: [],
            fmtLabelFn(opt) {
              return opt.title
            }
          },
          {
            $t: 'leaf_category',
            key: 'leafCategory',
            paramsKey: 'leaf_category_ids',
            treeData: []
          },
          {
            $t: 'package_list_archive',
            platform: 'MERCHANT',
            tabKey: 'package',
            key: 'archive',
            paramsKey: 'archive_status',
            options: maps.package_archived
          },
          {
            $t: 'global_activity_status',
            tabKey: 'activity',
            key: 'activityStatus',
            paramsKey: 'activity_status',
            options: isMerchant ? maps.activity_status : maps.activity_status2admin
          },
          {
            $t: 'package_list_package_status',
            tabKey: 'package',
            key: 'packageStatus',
            paramsKey: 'package_status',
            options: maps.package_status
          },
          {
            $t: 'merchant_approve_status',
            key: 'approvalStatus',
            paramsKey: 'approval_status',
            options: maps.approval_status
          }
        ]
      }
    }
  },
  watch: {
    '$route.query': {
      immediate: true,
      handler(v) {
        this.watchRouteToDo()
      }
    },
    'tabObj.key': {
      immediate: true,
      handler(v) {
        this.$nextTick(() => {
          this.$tracker.track('pageview', '#page_spm', { force: true })
        })
        if (v === 'package' && !this.preventTabSpm) {
          this.$tracker.track('action', '#pkg_tabs_spm')
        }
        this.preventTabSpm = false
      }
    }
  },
  // eslint-disable-next-line vue/order-in-components
  computed: {
    customColumns() {
      const merchantExcludeFields = ['operator_name']
      return this.isMerchant
        ? ticketLogColumns.filter((item) => !merchantExcludeFields.includes(item.dataIndex))
        : ticketLogColumns
    },
    calcIsRichTable() {
      return !!this.tableData.dataSource?.length
    },
    calcIsActTab() {
      return this.tabObj.key === 'activity'
    },
    calcShowActCreationStepReminder() {
      return this.showCreateBtn && this.showActCreationStepReminder
    },
    calcShowStepReminderBtn() {
      return this.showCreateBtn && this.showStepReminderBtn
    },
    showCreateBtn() {
      return this.isMerchant && this.tabObj.key === 'activity' && checkAuth('edit')
    },
    getCreateSpm() {
      return 'CreateNewActivity?trg=manual'
    },
    getSearchSpm() {
      const key = this.tabObj.key === 'activity' ? 'ActivityFilterSearch' : 'PackageFilterSearch'
      return `${key}?trg=manual`
    },
    getPageSpm() {
      const pages = this.tabObj.key === 'activity' ? 'ActivityListing' : 'PackageListing'
      return `${pages}?trg=manual&${this.$composeCommonExt()}`
    },
    isAct() {
      return this.tabObj.key === 'activity'
    },
    tableColumns() {
      return this.tabObj.key === 'activity' ? actColumns : pkgColumns
    },
    filterSelect() {
      return this.filterObj.list.filter((item) => {
        return (
          (!item.tabKey || item.tabKey === this.tabObj.key) && (!item.platform || item.platform === platform)
        )
      })
    }
  },
  created() {
    this.initDefData()
    document.addEventListener('keypress', this.debounceKeypress)
    if (this.isMerchant) {
      this.get_filter_option()
    }
    this.get_activities()

    klook.bus.$on('sendSwitchSpm', this.sendSwitchSpm)
    klook.bus.$on('sendChangeLangSpm', this.sendChangeLangSpm)
  },
  methods: {
    handleShowStepReminderModal() {
      this.stepReminderModal.visible = true
    },
    getActionSpm(type = '', oid) {
      const isActivity = this.tabObj.key === 'activity'
      const keyObj = {
        preview: 'Preview',
        view: isActivity ? 'ViewActivityPage' : 'ViewPackagePage',
        delete: isActivity ? 'DeleteActivity' : 'DeletePacakge',
        submitActivity: 'SubmitActivity',
        withdrawSubmit: isActivity ? 'WithdrawActivity' : 'WithdrawPackage',
        archive: 'Archive',
        unarchive: 'Unarchive',
        drawer: 'ApprovalList',
        submitPackage: 'SubmitPackage'
      }
      const key = keyObj[type]
      return `${key}?oid=activity_${oid}&trg=manual&type=${type}`
    },
    sendChangeLangSpm(id) {
      this.$tracker.track('action', `#change_${id}`)
    },
    sendSwitchSpm(id) {
      this.$tracker.track('action', `#switch_${id}`)
    },
    getDataSpm(key, oid) {
      return `${key}?oid=activity_${oid}&trg=manual`
    },
    createNew() {
      this.chooseCategory.visible = true
    },
    async handleCostModificationApprove({ ticket_id }) {
      const ticketDetail = await ajax.get(ADMIN_API.act.get_cost_ticket_detail, {
        params: {
          ticket_id
        }
      })

      const res = await ajax.postBody(ADMIN_API.act.ticket_approve_cost_ticket, {
        data: ticketDetail
      })

      if (res && res.success) {
        this.$message.success(this.$t('global_success'))
      }

      this.get_activities()
    },
    handleCostModificationReject({ sku_id }) {
      let row = {
        note: ''
      }

      let okButtonProps = {
        props: {
          disabled: true
        }
      }
      let node = (
        <a-textarea
          v-model={row.note}
          placeholder={__('global_please_input')}
          rows={4}
          onkeyup={(e) => {
            row.note = _.trim(e.target.value)
            okButtonProps.props.disabled = !row.note
          }}
          maxLength={500}
        />
      )

      this.$modal.confirm({
        title: 'Reason for rejecitng',
        maskClosable: true,
        cancelText: this.$t('global_cancel'),
        okText: this.$t('48109'),
        okButtonProps,
        content: (h) => node,
        onOk: async () => {
          const res = await ajax.postBody(ADMIN_API.act.update_ticket_status, {
            data: {
              sku_id,
              note: row.note,
              action: 1
            }
          })

          if (res && res.success) {
            this.$message.success(this.$t('global_success'))
          }

          this.get_activities()
        },
        onCancel() {}
      })
    },
    async watchRouteToDo() {
      if (!this.isMerchant) {
        return
      }

      const { switchTo, activity_id, package_id = '', sku_id = '' } = this.$route.query

      if (activity_id || package_id || sku_id) {
        if (switchTo) {
          switchTo === 'package' ? this.tabObj.handleChange('package') : this.tabObj.handleChange('activity')
        }

        this.$set(this.approveList, 'autoFillData', {
          package_id,
          sku_id
        })

        this.approveList.visible = true
      }
    },
    parseStr1html(key) {
      return klook.parseStr1(__(key), {
        '2021-08-18': '2021-08-25',
        '6:00-10:00am HKT': '6:00-10:00am HKT'
      })
    },
    clickActLangView(val, row) {
      this.editLang.visible = true
      this.$set(this.editLang, 'row', row)
    },
    initDefData() {
      let approveStatus = this.isMerchant ? undefined : [1]
      this.filterObj.list.forEach((item) => {
        if (item.paramsKey === 'approval_status') {
          this.$set(item, 'value', approveStatus)
          this.$set(item, 'defVal', approveStatus)
        }
      })
    },
    platformFn: maps.platformFn,
    checkAuth: checkAuth,
    handleGoPackage(row) {
      this.tabObj.handleChange('package', {
        noQuery: true
      })
      this.filterObj.list.map((item) => {
        if (item.paramsKey === 'activity_keyword') {
          this.$set(item, 'value', '' + row.activity_id)
          this.clickSearchBtn()
        }
      })

      this.preventTabSpm = true
    },
    async clickChangeLog(row) {
      if (row.package_archived) return
      let { logData, tabObj } = this
      logData.loading = true
      let url =
        tabObj.key === 'activity' ? ADMIN_API.act.get_change_log2act : ADMIN_API.act.get_change_log2pkg
      let params = {
        activity_id: row.activity_id, // in query, required, 查询的活动ID,
        package_id: row.package_id // in query, required, 查询的套餐ID,
      }
      logData.logTable.dataSource = []
      logData.visible = true
      let data = await ajax.get(url, {
        params
      })
      if (data && data.items) {
        logData.logTable.dataSource = data.items
      }
      logData.loading = false
    },
    debounceKeypress: _.debounce(function () {
      this.handleKeypress(...arguments)
    }, 300),
    handleKeypress(...args) {
      let e = args.pop()
      if (e.key === 'Enter') {
        this.get_activities()
      }
      return false
    },
    getFilterDisabled(row, action) {
      let typeArr = ['submitActivity', 'submitPackage']
      const actionType = action.type
      const isPreview = ['preview'].includes(actionType)
      if (isPreview) {
        const activity_real_status = row.activity_real_status ?? -1
        return activityUnablePreviewStatus.includes(+activity_real_status)
      }
      return !!(row.disabled && typeArr.includes(actionType))
    },
    getFilterActions(row) {
      let { actionObj, tabObj } = this
      let arr = []
      let status = `${tabObj.key}_status`
      let btnArr = actionObj.hasbtn[tabObj.key]
      btnArr.forEach((btnObj) => {
        if (typeof btnObj.list === 'function' ? btnObj.list(row) : btnObj.list.includes(row[status])) {
          if (
            (!btnObj.auth || checkAuth(btnObj.auth)) &&
            (!btnObj.platform || btnObj.platform === platform)
          ) {
            arr.push({
              ...btnObj,
              text:
                this.$t(`${btnObj.$t}`) +
                (btnObj.type === 'view'
                  ? this.tabObj.key === 'activity'
                    ? ' activity page'
                    : ' package page'
                  : '')
            })
          }
        }
      })
      return arr
    },
    mapsFilterFn: maps.filterFn,
    handlePageChange(page, pageSize) {
      this.updatePagination(page, pageSize)
      this.get_activities()
    },
    clickSubmitToKlook(row) {
      this.stkData.row = row
      this.stkData.visible = true
    },
    clickSearchBtn() {
      let { reqData, pageData } = this
      pageData.current = reqData.params.page = 1
      this.get_activities()
    },
    get_activities() {
      if (this.isAdmin) {
        this.getActivityOnAdmin()
        return
      }

      let { reqData } = this
      let params = getParamsKey(reqData.params, this.filterSelect)
      for (let k in reqData.params) {
        if (k === 'limit' || k === 'page') {
          continue
        }
        reqData.params[k] = params[k]
      }
      let { tableData } = this
      tableData.loading = true
      let url = this.calcIsActTab ? ADMIN_API.act.filter_activity : ADMIN_API.act.filter_package
      ajax.post(url, { data: reqData.params }).then((data) => {
        this.pageData.total = data ? data.total : 0
        tableData.dataSource = data?.items || []
        tableData.loading = false

        if (this.isMerchant && this.calcIsActTab && !this.isInited) {
          if (tableData.dataSource.length) {
            this.showStepReminderBtn = true
          } else {
            this.showActCreationStepReminder = true
          }
        }

        this.isInited = true
      })
    },
    // 筛选条件
    get_filter_option() {
      ajax.get(ADMIN_API.act.get_filter_option).then((data) => {
        if (!data) return
        let cityArr = this.filterObj.list.filter((item) => item.key === 'city')
        if (cityArr.length && data.cities) {
          cityArr[0].options = data.cities.map((item) => {
            let obj = {
              value: item.city_id,
              title: item.name
            }
            return _.merge({}, item, obj)
          })
        }
        let leafCategoryArr = this.filterObj.list.filter((item) => item.key === 'leafCategory')
        if (leafCategoryArr.length && data.category_tree) {
          leafCategoryArr[0].treeData = this.getCategoryTree(data.category_tree)
        }
        let merchants = this.filterObj.list.filter((item) => item.key === 'merchant_id')
        if (merchants.length && Array.isArray(data.merchants)) {
          merchants[0].options =
            data.merchants.map((item) => {
              let obj = {
                value: item.merchant_id,
                title: `${item.merchant_id}-${item.merchant_name}`
              }
              return _.merge({}, item, obj)
            }) || []
        }
      })
    },
    getCategoryTree(arr) {
      let newArr = arr.map((cate) => {
        cate.value = cate.category_id
        cate.label = cate.category_name
        cate.children = cate.sub_categories.map((sub) => {
          sub.value = sub.category_id
          sub.label = sub.category_name
          sub.children = sub.sub_categories.map((leaf) => {
            leaf.value = leaf.category_id
            leaf.label = leaf.category_name
            return leaf
          })
          return sub
        })
        return cate
      })
      return _.merge([], newArr)
    },
    // click reset btn
    clickResetBtn(option) {
      //注意option为event的情况处理
      this.filterObj.list.forEach((item) => {
        this.$set(item, 'value', item.defVal)
      })
      this.updatePagination(1, 10)
      if (!(option && option.noQuery)) {
        this.get_activities()
      }
    },
    updatePagination(page, limit) {
      let { reqData, pageData } = this
      pageData.current = reqData.params.page = page
      pageData.pageSize = reqData.params.limit = limit
    }
  }
}
</script>
<style lang="scss">
.activity-management {
  .preview-wrap-style {
    max-width: calc(100% - 30px);
    vertical-align: middle;
    padding: 0;
    span {
      display: inline-block;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      vertical-align: middle;
    }
  }
}
</style>
<style lang="scss" scoped>
@import '../package/units/unit-list/calendar-setting/mixins';

.change-log-box {
  max-height: 350px;
}
.activity-management {
  padding-bottom: 20px;
  .clock-circle-style {
    color: #979797;
    margin-left: 10px;
  }
  .act-tab-box {
    padding: 0 24px;
  }
  .act-select {
    background-color: #fafafa;
    padding: 16px 39px;
    .label-box {
      display: inline-block;
      max-width: 134px;
      padding-right: 12px;
      box-sizing: border-box;
      line-height: 20px;
      vertical-align: middle;
    }
    .act-select-box {
      display: inline-block;
      min-width: 386px;
      text-align: right;
      margin: 0 24px 10px 0;
    }
  }
  .act-btn-box {
    button:not(:last-child) {
      margin-right: 4px;
    }
  }
  .btn-create-box {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding: 24px 24px 0 24px;
  }
  .act-table-box {
    padding: 24px 24px 0 24px;
    &.clear-padding {
      padding: 0;
    }
  }
  .number-right {
    display: block;
    text-align: right;
  }
  .btn-more-style {
    padding: 0 0 0 16px;
    font-weight: 800;
  }
  .common-status-style {
    @include mixin-status-text();
  }
  .approval-status-style {
    padding: 1px 8px;
    width: fit-content;
    border-radius: 2px;
    @include mixin-status-text-circle();
  }
  .icon-style {
    cursor: pointer;
    margin-left: 4px;
    font-size: 12px;
    @include mixin-status-icon-color();
  }
  .act-table--empty {
    padding: 40px 0;
  }

  .act-creation-step-reminder {
    height: 268px;
    margin-top: 20px;
  }
}
</style>
