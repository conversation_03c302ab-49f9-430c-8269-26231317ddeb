<template>
  <a-modal v-bind="{ ...defaultModalConf, ...logData }" v-model="logData.visible" class="change-log-modal">
    <div slot="footer" class="footer-wrap">
      <div v-if="isMerchant" class="footer-tips">
        {{ $t('194907') }}
      </div>
      <a-button type="primary" @click="logData.visible = false">{{ $t('btn.okay') }}</a-button>
    </div>
    <div class="change-log-box">
      <a-table v-bind="{ ...defaultTableConf, ...logData.logTable }" :loading="logData.loading">
        <template slot="timestamp_utc" slot-scope="val">
          <template v-if="val">
            <div>{{ val.split(' ')[0] }}</div>
            <div>{{ val.split(' ')[1] }}</div>
          </template>
          <span v-else>-</span>
        </template>
      </a-table>
    </div>
  </a-modal>
</template>

<script>
import dataLib from './dataLib'
const { ticketLogColumns, isMerchant } = dataLib

export default {
  name: 'LogsTable',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    logData: {
      type: Object,
      required: true
    },
    customColumns: {
      type: Array,
      default: undefined
    }
  },
  data() {
    return {
      defaultModalConf: {
        width: 750,
        maskClosable: true,
        title: __('activity_event_logs')
      },
      defaultTableConf: {
        columns: this.customColumns || ticketLogColumns,
        bordered: true,
        pagination: false,
        rowKey: (r, i) => i
      },
      isMerchant
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.footer-wrap {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .footer-tips {
    flex: 1;
    padding-right: 20px;
    text-align: left;
  }
}
</style>
