import handleLib from '../handleLib'
import stkModal from '@activity/components/modalAntd/stkModal'
import { getPreviewValiditySync } from '@activity/components/preview-validity'

export const act_management_mixin = {
  components: {
    stkModal
  },
  data() {
    return {
      stkData: {
        activity_item: {},
        form: {
          note: '', // 原因,
          package_id_list: [
            // 活动和套餐必须都传，并且套餐属于活动,
          ]
        },
        row: {},
        keyFlag: '',
        visible: false,
        packageArr: [],
        on: {
          ok: async () => {
            let { stkData } = this
            let actApproveStatus = stkData.activity_item.approval_status
            let isAct = stkData.keyFlag === 'activity'
            let isFlag = !isAct && [0, 2].includes(actApproveStatus) //特殊处理
            if (isFlag) {
              // 提交套餐判断活动是否可提交，可提交则调用提交活动接口，否则直接调用提交套餐接口
              stkData.form.package_id_list = [stkData.row.package_id]
            }
            let result =
              isAct || isFlag
                ? await handleLib.methods.submit_to_klook2activity({
                    activity_id: stkData.row.activity_id,
                    ...stkData.form
                  })
                : await handleLib.methods.submit_to_klook2package({
                    activity_id: stkData.row.activity_id,
                    note: stkData.form.note,
                    package_id: stkData.row.package_id
                  })
            stkData.visible = false
            if (result) {
              this?.get_activities?.() // act management list
              this?.refresh?.() // approve list
            }
            stkData.form.note = ''
            stkData.form.package_id_list = []
          }
        }
      },
      actionObj: {
        preview_url: klook.getUrlByEnv(''),
        handlerArchive: (row) => {
          handleLib.methods.handlerArchive.call(this, row, {
            suc: () => {
              this?.get_activities?.()
            }
          })
        },
        handlerUnarchive: (row) => {
          handleLib.methods.handlerUnarchive.call(this, row, {
            suc: () => {
              this?.get_activities?.()
            }
          })
        },
        handleApprovalList: ({ row, type }) => {
          this.$router.replace({
            name: 'activityManagement',
            ...this.$route,
            query: {
              ...(this.$route.query || {}),
              activity_id: row.activity_id,
              package_id: (type === 'package' && row.package_id) || ''
            }
          })
          this.$set(this, 'approveList', {
            visible: true,
            data: row,
            title: __('48224')
          })
        },
        handlerPreview: (row) => {
          getPreviewValiditySync(row.activity_id, this.$i18n, async (date) => {
            if (date) {
              const response = await ajax.get(ADMIN_API.aidRevamp.get_page_version, {
                params: {
                  activity_id: row.activity_id
                }
              })

              const pageVersion = response.page_version || 0
              let url = `${this.actionObj.preview_url}/en-US/preview/activity/${row.activity_id}/?lang=${
                row.submitted_language
              }&deadline_version=1&deadline=${encodeURIComponent(date)}&page_version=${pageVersion}`
              window.open(url)
            }
          })
        },
        handlerView: (row) => {
          let url = `${this.actionObj.preview_url}/en-US/activity/${row.activity_id}?lang=${row.submitted_language}`
          window.open(url)
        },
        handleSubmitActivity: async (row) => {
          let result = await handleLib.methods.get_activity_submit_status(row.activity_id)
          if (result) {
            if (result.result?.status) {
              let activity_id = row.activity_id
              let obj = await ajax.get(
                {
                  url: ADMIN_API.act.get_activity_to_submit_packages,
                  params: { activity_id }
                },
                {
                  loading: true
                }
              )
              if (obj && (obj.submited_package || obj.to_be_submit_packages)) {
                let arr = []
                if (obj.submited_package) {
                  obj.submited_package.forEach((o) => (o.isFlag = true))
                  arr.push(...obj.submited_package)
                }
                if (obj.to_be_submit_packages) {
                  obj.to_be_submit_packages.length === 1 &&
                    (this.stkData.form.package_id_list = obj.to_be_submit_packages.map((o) => o.package_id))
                  arr.push(...obj.to_be_submit_packages)
                }
                this.stkData.keyFlag = 'activity'
                this.stkData.packageArr = arr
                this.stkData.visible = true
                this.stkData.row = row
              }
            } else {
              this.$modal.error({
                title: __('merchant_submit_activity_action'),
                content: __('activity_required_fields')
              })
            }
          }
        },
        handleSubmitPackage: async (row) => {
          // 兼容 aid - merchant Approval list
          const isSPU = row.package_version === 'SPU'
          const payload = isSPU ? 'get_spu_submit_status' : 'get_package_submit_status'
          const resp = await handleLib.methods[payload](row.package_id)
          if (resp) {
            const status = resp.result?.[isSPU ? 'spu_status' : 'status']
            if (status) {
              let arr = [{ ...row, checked: true }]
              this.stkData.keyFlag = 'package'
              this.stkData.packageArr = arr
              this.stkData.visible = true
              this.stkData.row = row
              this.stkData.activity_item = resp.result.activity_item
              this.stkData.form.package_id_list = [row.package_id]
            } else {
              this.$modal.error({
                title: __('merchant_submit_package_action'),
                content: __('package_required_fields')
              })
            }
          }
        },
        handleWithdrawSubmit: async (row, keyFlag, cb = null) => {
          await handleLib.methods.handleWithdrawSubmit.call(this, row, {
            keyFlag,
            suc: () => {
              this?.get_activities?.()
              cb?.()
            }
          })
        },
        handlerDelete: (row) => {
          this.$modal.confirm({
            maskClosable: true,
            title: __('global_confirm_delete'),
            cancelText: __('global_cancel'),
            okText: __('btn.okay'),
            onOk: () => {
              let url =
                this.tabObj.key === 'activity' ? ADMIN_API.act.delete_activity : ADMIN_API.act.delete_package
              let data =
                this.tabObj.key === 'activity'
                  ? { activity_id: +row.activity_id }
                  : {
                      package_id: +row.package_id
                    }
              ajax
                .post(
                  url,
                  {
                    data: data
                  },
                  ajax.sucOptions
                )
                .then(() => {
                  this?.get_activities?.()
                })
            }
          })
        },
        // status: { 0:unpublished, 1: published, 2: draft, 3, em to edit, 4: in preview, }
        /*
         * 0: unpublished,
         * 1: published,
         * 2: draft
         * 3: em to edit
         * 4: in preview
         */
        hasbtn: {
          activity: [
            {
              $t: 'global_preview',
              type: 'preview',
              bind: {
                class: 'preview-wrap-style'
              },
              list: [0, 1, 4],
              on: {
                click: (row, id) => {
                  this.$tracker.track('action', `${id}`)
                  this.actionObj.handlerPreview(row)
                }
              }
            },
            {
              platform: 'MERCHANT',
              $t: 'global_view',
              type: 'view', //跳转到c端
              list: [1],
              on: {
                click: (row, id) => {
                  this.actionObj.handlerView(row)
                  this.$tracker.track('action', `${id}`)
                }
              }
            },
            {
              $t: 'global_delete',
              auth: 'delete',
              type: 'delete',
              list: (row) => {
                return row.activity_status === 0 && [0, 2].includes(row.approval_status)
              },
              on: {
                click: (row, id) => {
                  this.actionObj.handlerDelete(row)
                  this.$tracker.track('action', `${id}`)
                }
              },
              bind: {
                class: 'common-button-danger'
              }
            },
            {
              $t: 'merchant_submit_activity_action',
              auth: 'actSubmit',
              type: 'submitActivity',
              list: (row) => {
                let isSubmit =
                  row.activity_status !== 1 && row.package_count && [0, 2].includes(row.approval_status)
                // this.$set(row, 'disabled', !isSubmit)
                return isSubmit
              },
              on: {
                click: (row, id) => {
                  this.actionObj.handleSubmitActivity(row)
                  this.$tracker.track('action', `${id}`)
                }
              }
            },
            {
              $t: 'btn.withdrawSubmit',
              auth: 'actWithdraw',
              type: 'withdrawSubmit',
              list: (row) => {
                return row.approval_status === 1
              },
              on: {
                click: (row, id) => {
                  this.actionObj.handleWithdrawSubmit(row, 'activity')
                  this.$tracker.track('action', `${id}`)
                }
              }
            },
            {
              $t: '48224',
              type: 'drawer',
              list: () => true,
              on: {
                click: (row, id) => {
                  this.actionObj.handleApprovalList({ row, type: 'activity' })
                  this.$tracker.track('action', `${id}`)
                }
              }
            }
          ],
          package: [
            {
              $t: 'global_preview',
              type: 'preview',
              bind: {
                class: 'preview-wrap-style'
              },
              list: (row) => {
                return [0, 1, 4].includes(row.package_status) && !row.package_archived
              },
              on: {
                click: (row, id) => {
                  this.$tracker.track('action', `${id}`)
                  this.actionObj.handlerPreview(row)
                }
              }
            },
            {
              platform: 'MERCHANT',
              $t: 'global_view',
              type: 'view', // 跳转到c端
              list: [1],
              on: {
                click: (row, id) => {
                  this.actionObj.handlerView(row)
                  this.$tracker.track('action', `${id}`)
                }
              }
            },
            {
              $t: 'global_delete',
              auth: 'delete',
              type: 'delete',
              list: (row) => {
                return (
                  (row.package_status === 0 && [0, 2].includes(row.approval_status)) || row.archive_status
                )
              },
              on: {
                click: (row, id) => {
                  this.actionObj.handlerDelete(row)
                  this.$tracker.track('action', `${id}`)
                }
              },
              bind: {
                class: 'common-button-danger'
              }
            },
            {
              $t: 'package_list_archive',
              auth: 'pkgManage',
              type: 'archive',
              list: (row) => {
                return row.package_status === 0 && !row.package_archived
              },
              on: {
                click: (row, id) => {
                  this.actionObj.handlerArchive(row)
                  this.$tracker.track('action', `${id}`)
                }
              }
            },
            {
              $t: 'package_list_unarchive',
              auth: 'pkgManage',
              type: 'unarchive',
              list: (row) => {
                return row.package_archived
              },
              on: {
                click: (row, id) => {
                  this.actionObj.handlerUnarchive(row)
                  this.$tracker.track('action', `${id}`)
                }
              }
            },
            {
              $t: 'merchant_submit_package_action',
              auth: 'pkgSubmit',
              type: 'submitPackage',
              list: (row) => {
                let isSubmit = row.package_status === 0 && [0, 2].includes(row.approval_status)
                // this.$set(row, 'disabled', !isSubmit)
                return isSubmit
              },
              on: {
                click: (row, id) => {
                  this.actionObj.handleSubmitPackage(row)
                  this.$tracker.track('action', `${id}`)
                }
              }
            },
            {
              $t: 'btn.withdrawSubmit',
              auth: 'pkgWithdraw',
              type: 'withdrawSubmit',
              list: (row) => {
                return row.approval_status === 1
              },
              on: {
                click: (row, id) => {
                  this.actionObj.handleWithdrawSubmit(row, 'package')
                  this.$tracker.track('action', `${id}`)
                }
              }
            },
            {
              $t: '48224',
              type: 'drawer',
              list: () => true,
              on: {
                click: (row, id) => {
                  this.actionObj.handleApprovalList({ row, type: 'package' })
                  this.$tracker.track('action', `${id}`)
                }
              }
            }
          ]
        }
      }
    }
  }
}

export const approve_list_mixin = {
  data() {
    return {
      approveList: {
        visible: false,
        data: {},
        title: '',
        autoFillData: undefined
      },
      unitsDrawer: {
        visible: false,
        data: {}
      }
    }
  },
  methods: {
    handleShowUnits(row) {
      this.$set(this, 'unitsDrawer', {
        visible: true,
        data: row
      })
    }
  }
}
