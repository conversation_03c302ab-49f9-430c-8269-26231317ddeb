<template>
  <div id="act-notes-box" class="act-notes-box" v-disabled="vdisabled" :data-spm-page="getSpm">
    <div class="common-basic-box">
      <h2>{{ $t('info_notes') }}</h2>
      <a-form-model class="common-basic-form-style" :model="form" ref="form" :colon="false">
        <a-form-model-item :rules="{ required: true }" prop="responsible_bd">
          <span slot="label"
            >{{ $t('act_responsible_BD')
            }}<a
              style="margin: 0 0 0 14px;"
              v-if="hasUpdateBDPermission"
              href="javascript:void(0)"
              @click="gotoOldAdmin('/act/activity/update_bd')"
            >
              {{ $t('link_bulk_update_responsible_BDs') }}
            </a></span
          >
          <a-tooltip
            placement="right"
            overlayClassName="common-tooltip-style"
            title="Main contact for the activity and responsible for maintaing content, inventory, APIs, etc."
          >
            <a-select :placeholder="$t('global_please_input')" v-model="form.responsible_bd" showSearch>
              <a-select-option v-for="bd in bd_data" :key="bd.account_name" :value="bd.account_name">{{
                bd.account_name
              }}</a-select-option>
            </a-select>
          </a-tooltip>
        </a-form-model-item>
        <!-- <a-form-model-item
          :label="`${$t('act_for_internal_analysis_1')} - ${$t('act_for_internal_analysis_2')}`"
          :rules="{ required: true, message: 'Required' }"
          prop="bi_tree"
        >
          <a-cascader
            class="form-width"
            v-model="form.bi_tree"
            allowClear
            expand-trigger="hover"
            :placeholder="$t('global_please_select')"
            :fieldNames="{
              value: 'id',
              label: 'name',
              children: 'child_tag_list'
            }"
            :options="biTreeOptions"
          >
          </a-cascader>
        </a-form-model-item> -->
        <a-form-model-item :label="$t('act_product_notes')">
          <a-input
            :placeholder="$t('global_please_input')"
            type="textarea"
            v-model.trim="form.text"
            :rows="4"
          ></a-input>
        </a-form-model-item>
      </a-form-model>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'

export default {
  data() {
    return {
      vdisabled: false,
      form: {
        text: '',
        responsible_bd: undefined,
        bi_tree: []
      },
      bd_data: [],
      biTreeOptions: []
    }
  },
  created() {
    this.initData()
  },
  mounted() {
    klook.bus.$emit('addHandleSave2bus', this.save)
    klook.bus.$emit('floorTimeline2bus', { floorData: {} })
    this.$root.setMutationObserver('#act-notes-box', this, () => {
      let isb = !this.$root.roles.is_am
      this.vdisabled = {
        lock: isb
      }
      return isb
    })
  },
  computed: {
    hasUpdateBDPermission() {
      return klook.checkPermission('act/activity_bd_page/update')
    },
    activity_id() {
      return klook.parse2id(this.$route.params.id)
    },
    getSpm() {
      const oid = `activity_${this.activity_id || 0}`
      return `OperationalInfo?oid=${oid}`
    }
  },
  methods: {
    ...mapActions(['updateActStepStatus2action']),
    gotoOldAdmin: klook.gotoOldAdmin,
    async initData() {
      let reqData = await ajax.get({
        url: ADMIN_API.act.get_operate_note,
        params: {
          activity_id: this.activity_id,
          language: klook.getEditLang()
        }
      })
      let { note, bd_am_list, bi_tree, bi_tag_info } = reqData
      this.$set(this.form, 'text', note.text)
      this.$set(this.form, 'responsible_bd', note.responsible_bd || undefined)
      this.bd_data = bd_am_list.bds || []
      this.biTreeOptions = (bi_tree && bi_tree.child_tag_list) || []

      if (bi_tag_info && bi_tag_info[0]) {
        this.form.bi_tree = this.findBiTreeOpt(this.biTreeOptions, bi_tag_info[0].id).parent_flow.slice(1) // del root id
        this.$forceUpdate()
      }
    },
    findBiTreeOpt(list, id) {
      let res = null
      for (let val of list) {
        if (val.id === id) {
          res = val
        }
        if (Array.isArray(val.child_tag_list) && val.child_tag_list.length) {
          res = this.findBiTreeOpt(val.child_tag_list, id)
        }
        if (res) break
      }
      return res
    },
    async save() {
      let valid = await new Promise((resolve) => this.$refs.form.validate(resolve))
      if (!valid) return
      let { bi_tree, responsible_bd, text } = this.form
      let result = await ajax.postBody(
        {
          url: ADMIN_API.act.update_operate_note,
          data: {
            activity_id: this.activity_id,
            tag_id_list: bi_tree.slice(-1).join(),
            responsible_bd,
            text
          }
        },
        ajax.sucOptions
      )
      if (result && result.success) {
        if (!this.$store.state.actStepStatus.operational_info) {
          this.updateActStepStatus2action({
            activity_id: this.activity_id,
            status: 1,
            step: 'operational_info',
            language: 'ALL',
            loading: true
          })
        }
        if (klook.getPlatformRoleKey() === 'admin') {
          if (this.$root.isSPU) {
            // klook.gotoOldAdmin(
            //   `/act/activity/spu/${this.activity_id}${window.location.search}`
            // )
            this.$router.push({
              path: `/act/activity/special/${this.activity_id}`,
              query: {
                ...this.$route.query,
                model: 'activity_spu'
              }
            })
          } else {
            klook.bus.$emit('gotoCreatePkg2bus')
          }
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped></style>
