<template>
  <div class="table">
    <a-table
      ref="table"
      :columns="columns"
      :data-source="dataSource"
      :row-key="(record) => record.activity_id"
      :custom-row="customRow"
      :pagination="false"
      :loading="loading"
      :scroll="scrollConfig"
    >
      <span slot="name" slot-scope="text">
        <a-tooltip placement="topLeft">
          <template slot="title">
            <span>{{ text }}</span>
          </template>
          {{ text }}
        </a-tooltip>
      </span>
      <span slot="custoRank">
        {{ $t('98422') }}
        <a-tooltip placement="top">
          <template slot="title">
            {{ $t('98423') }}
          </template>
          <a-icon type="question-circle" />
        </a-tooltip>
      </span>

      <span slot="action">
        <a-icon type="menu" />
      </span>
    </a-table>
  </div>
</template>

<script>
import { debounce } from 'lodash'

export default {
  props: {
    dataSource: {
      type: Array,
      required: true
    },
    loading: {
      type: <PERSON><PERSON><PERSON>
    }
  },
  data() {
    return {
      height: 0,
      nameWidth: 0,
      flag: false,
      scrollConfig: { x: 'max-content', y: 0 },
      columns: []
    }
  },
  computed: {
    customRow() {
      return (record) => {
        return {
          on: {
            dragstart: (event) => this.handleDragStart(event, record),
            dragover: this.handleDragOver,
            drop: (event) => this.handleDrop(event, record)
          },
          attrs: {
            draggable: true
          },
          class: { dragging: record.dragging }
        }
      }
    }
  },
  mounted() {
    this.calculateTableHeight()
    window.addEventListener('resize', debounce(this.calculateTableHeight, 250))
  },
  beforeDestroy() {
    window.removeEventListener('resize', debounce(this.calculateTableHeight, 250))
  },
  methods: {
    handleDragStart(event, record) {
      event.dataTransfer.setData('text', record.activity_id.toString())
      record.dragging = true
    },
    handleDragOver(event, record) {
      event.preventDefault()
      const tableElement = document.querySelector('.ant-table-body')
      const containerRect = tableElement.getBoundingClientRect()
      const containerTop = containerRect.top
      const containerHeight = containerRect.height
      const mousePosition = event.clientY
      const scrollSpeed = 50

      if (mousePosition < containerTop + scrollSpeed) {
        tableElement.scrollTop -= scrollSpeed
      } else if (mousePosition > containerTop + containerHeight - scrollSpeed) {
        tableElement.scrollTop += scrollSpeed
      }
    },
    handleDrop(event, record) {
      const draggedId = parseInt(event.dataTransfer.getData('text'))
      const draggedRow = this.dataSource.find((item) => item.activity_id === draggedId)
      const dropIndex = this.dataSource.indexOf(record)
      const tableData = this.dataSource.filter((item) => item.activity_id !== draggedId)
      tableData.splice(dropIndex, 0, draggedRow)
      this.$emit('dataSourceChange', tableData, draggedRow.activity_id)
    },
    calculateTableHeight() {
      const tableElement = this.$refs.table.$el
      const { top, width } = tableElement.getBoundingClientRect()
      const windowHeight = window.innerHeight
      const x = width - 20
      const y = windowHeight - top - 110

      // 活动名称
      this.nameWidth = x - 450

      this.scrollConfig = {
        x,
        y
      }

      this.columns = [
        { title: this.$t('98420'), dataIndex: 'activity_id', key: 'activity_id', width: 100 },
        {
          title: this.$t('98421'),
          dataIndex: 'activity_name',
          key: 'activity_name',
          width: this.nameWidth,
          scopedSlots: { customRender: 'name' }
        },
        {
          dataIndex: 'rank',
          key: 'rank',
          width: 250,
          slots: { title: 'custoRank' }
        },
        { title: this.$t('98413'), key: 'action', scopedSlots: { customRender: 'action', width: 100 } }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.table {
  background-color: #fff;
  margin-top: 10px;
}
::v-deep .ant-table-row-cell-break-word {
  overflow: hidden;
}

.dragging {
  opacity: 0.5;
  transition: opacity 0.2s;
}
</style>
