<template>
  <div class="table">
    <a-modal :title="$t('98419')" :visible="visible" :footer="null" :width="1128" @cancel="handleCancel">
      <a-table
        :loading="spinning"
        :columns="columns"
        :pagination="pagination"
        :data-source="dataSource"
        @change="handleChange"
      >
        <span slot="name" slot-scope="text">
          <a-tooltip placement="topLeft">
            <template slot="title">
              <span class="name">{{ text }}</span>
            </template>
            {{ text }}
          </a-tooltip>
        </span>
        <span slot="email" slot-scope="text">
          <a-tooltip placement="topLeft">
            <template slot="title">
              <span class="name">{{ text }}</span>
            </template>
            {{ text }}
          </a-tooltip>
        </span>
      </a-table>
    </a-modal>
  </div>
</template>

<script>
export default {
  name: 'PassSortLog',
  props: {
    visible: Boolean,
    lang: {
      type: String,
      default: () => ''
    },
    activityId: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {
      spinning: false,
      dataSource: [],
      scrollConfig: {
        x: '1300px'
      },
      columns: [
        { title: this.$t('98424'), dataIndex: 'modify_time_bj', width: 180 },

        {
          title: this.$t('98425'),
          dataIndex: 'modify_email',
          key: 'modify_email',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'email' }
        },
        { title: this.$t('98420'), dataIndex: 'activity_id', key: 'activity_id', width: 150 },

        {
          title: this.$t('98421'),
          dataIndex: 'activity_name',
          key: 'activity_name',
          ellipsis: true,
          scopedSlots: { customRender: 'name' }
        },
        { title: this.$t('98426'), dataIndex: 'before_rank', key: 'before_rank', width: 180 },
        { title: this.$t('98427'), dataIndex: 'after_rank', key: 'after_rank', width: 180 }
      ],
      pagination: {
        showSizeChanger: false,
        total: 0,
        current: 1,
        pageSize: 10,
        size: 'small'
      }
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.getList()
        }
      },
      immediate: true
    }
  },

  methods: {
    async getList() {
      this.spinning = true
      const { current, pageSize } = this.pagination
      let response = await ajax.get(ADMIN_API.act.get_standard_activity_sorts_history, {
        params: {
          activity_id: this.activityId,
          page: current,
          limit: pageSize,
          language: this.lang
        }
      })
      if (response) {
        this.dataSource = response.histories || []
        this.pagination.total = response.total
        this.pagination.current = response.page
      }
      this.spinning = false
    },
    handleCancel() {
      this.$emit('handleCancel')
    },
    handleChange(pagination) {
      const { current } = pagination
      this.pagination.current = current
      this.getList()
    }
  }
}
</script>
<style lang="scss" scoped>
.table {
  .name {
    width: 200px;
    overflow: hidden;
  }
  ::v-deep .ant-table-row-cell-break-word {
    overflow: hidden;
  }
}
</style>
