<template>
  <div class="sort-wrap">
    <div class="top">
      <div class="top-info">
        <div class="top-status">
          {{ $t('98418', { status: hasSort ? $t('98417') : $t('98416') }) }}
          <span v-if="hasSort" class="link" @click="showModal">({{ $t('98419') }})</span>
        </div>
        <div class="top-action">
          <a-button type="primary" :loading="editLoading" :disabled="!hasMove" @click="saveSource">
            {{ $t('global_save') }}
          </a-button>
        </div>
      </div>
      <div class="warning">
        <div class="warning-svg">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
            <path
              d="M7 0C3.13438 0 0 3.13438 0 7C0 10.8656 3.13438 14 7 14C10.8656 14 14 10.8656 14 7C14 3.13438 10.8656 0 7 0ZM6.5 3.625C6.5 3.55625 6.55625 3.5 6.625 3.5H7.375C7.44375 3.5 7.5 3.55625 7.5 3.625V7.875C7.5 7.94375 7.44375 8 7.375 8H6.625C6.55625 8 6.5 7.94375 6.5 7.875V3.625ZM7 10.5C6.80374 10.496 6.61687 10.4152 6.47948 10.275C6.3421 10.1348 6.26515 9.9463 6.26515 9.75C6.26515 9.5537 6.3421 9.36522 6.47948 9.225C6.61687 9.08478 6.80374 9.00401 7 9C7.19626 9.00401 7.38313 9.08478 7.52052 9.225C7.6579 9.36522 7.73485 9.5537 7.73485 9.75C7.73485 9.9463 7.6579 10.1348 7.52052 10.275C7.38313 10.4152 7.19626 10.496 7 10.5Z"
              fill="#FAAD14"
            />
          </svg>
        </div>
        <div class="warning-content">
          <div>{{ $t('98431') }}</div>
          <ul>
            <li v-for="item in inforList" :key="item.name">
              <span class="disc"></span>
              <a-popover title="" placement="right">
                <template slot="content">
                  <img
                    :src="item.src"
                    class="img"
                    alt="preiveiw"
                    :style="{ width: '500px', height: '600px' }"
                  />
                </template>
                {{ $t(item.name) }}
              </a-popover>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <DraggableTable :data-source="dataSource" :loading="loading" @dataSourceChange="dataSourceChange" />
    <div v-if="visible">
      <EventLog :lang="lang" :visible.sync="visible" :activity-id="activityId" @handleCancel="handleCancel" />
    </div>
  </div>
</template>

<script>
import DraggableTable from './components/draggableTable.vue'
import EventLog from './components/eventLog.vue'

export default {
  name: 'SortPass',
  components: {
    DraggableTable,
    EventLog
  },
  data() {
    return {
      visible: false,
      editLoading: false,
      hasSort: false,
      dataSource: [],
      modifiedIds: [],
      inforList: [
        {
          name: '98475',
          src: 'https://res.klook.com/image/upload/Klook%20Pass/Snipaste_2023-06-02_16-11-50_uxgaao.png'
        },
        {
          name: '98476',
          src: 'https://res.klook.com/image/upload/Klook%20Pass/Snipaste_2023-06-02_16-12-23_xizgxv.png'
        },
        {
          name: '98478',
          src: 'https://res.klook.com/image/upload/Klook%20Pass/Snipaste_2023-06-02_16-16-46_mpsrs9.png'
        }
      ]
    }
  },
  computed: {
    hasRank() {
      return false
    },
    activityId() {
      return this.$attrs?.actGlobal?.actInfo?.activity_id || 0
    },
    lang() {
      return this.$route.query.lang || ''
    },
    hasMove() {
      return this.modifiedIds.length > 0
    }
  },
  watch: {
    activityId: {
      handler(val) {
        if (val > 0) {
          this.getSource()
        }
      }
    }
  },
  mounted() {
    this.getSource()
  },
  methods: {
    init() {
      this.disabled = true
    },
    showModal() {
      this.visible = true
    },
    handleCancel(e) {
      this.visible = false
    },
    hasMoveHandler(payload) {
      this.disabled = !payload
    },
    dataSourceChange(payload, draggedId) {
      this.dataSource = payload
      this.modifiedIds.push(draggedId)
    },
    async getSource() {
      if (this.activityId === 0) {
        return
      }
      this.loading = true
      let response = await ajax.get(ADMIN_API.act.get_standard_activity_sorts, {
        params: {
          activity_id: this.activityId,
          language: this.lang
        }
      })

      if (response) {
        this.dataSource = response?.items || []
        this.hasSort = response?.is_sorted
      }

      this.loading = false
    },
    async saveSource() {
      this.editLoading = true
      const response = await ajax.postBody(ADMIN_API.act.save_standard_activity_sorts, {
        data: {
          activity_id: this.activityId,
          sorted_activity_ids: this.dataSource.map((item) => item.activity_id),
          modified_activity_ids: Array.from(new Set(this.modifiedIds))
        }
      })

      if (response?.success) {
        this.hasSort = true
        this.getSource()
        this.$message.success(this.$t('global_modify_success'))
        this.modifiedIds = []
      } else {
        this.$message.error(this.$t('global_failure'))
      }

      this.editLoading = false
    }
  }
}
</script>
<style lang="scss" scoped>
.sort-wrap {
  .top {
    background: #fff;
    padding: 12px 20px;
    .top-info {
      display: flex;
      margin-bottom: 17px;
      align-items: flex-start;
    }

    &-status {
      flex: 1;
      color: #000;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      .link {
        cursor: pointer;
        color: #2073f9;
        text-decoration: underline;
      }
    }

    .warning {
      padding: 10px 16px;
      border: 1px solid #ffe58f;
      font-size: 14px;
      line-height: 22px;
      display: flex;
      .img {
        width: 350px;
        height: 500px;
      }
      &-svg {
        width: 18px;
        height: 22px;
        margin-top: 2px;
      }
      &-content {
        flex: 1;
        margin-left: 12px;

        li {
          position: relative;
          line-height: 22px;
          cursor: pointer;
          padding-left: 10px;

          .disc {
            position: absolute;
            left: 0px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background-color: #262626;
          }
        }
      }
    }
  }
}
</style>
