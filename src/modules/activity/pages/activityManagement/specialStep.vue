<template>
  <div class="special-container">
    <p class="tips">
      <template v-if="isCompleted">
        {{ $t('28691') }}
      </template>
      <template v-else>
        {{ status[currentStatus] }}
      </template>
    </p>
    <br />
    {{ $t('28692') }}
    <br /><br />
    <a-button size="small" @click="handleOpen">
      {{ $t('28693') }}
    </a-button>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { getEditLang } from '@activity/utils'

export default {
  name: 'SpecialStep',
  inject: ['refreshPkgs'],
  data() {
    return {
      stepStatus: 0,
      stepWindow: null,
      childPageMounted: false,
      status: {
        0: __('28694'),
        1: __('28695'),
        2: __('28696'),
        3: __('28697'),
        4: __('28698')
      },
      currentStatus: 0,
      isCompleted: false,

      // 30 * 2s = 1 min
      MaximumAttempts: 30,
      confirmInitStatusFrequency: 2000, // Need to wait for child page dom content loaded

      heartbeatFrequency: 1000, // 1s
      surviveFrequency: 5000, // child page live confirm frequency, 5s

      heartbeatTimeout: null,
      sentMsgTimeout: null,
      initInterval: null,

      closedCb: null
    }
  },
  watch: {
    isCompleted(v) {
      if (v) {
        // if the status has been completed, it don't need to update
        if (!this.stepStatus) {
          if (this.isPkgSpecial) {
            this.updatePkgStepStatus2action({
              activity_id: this.activity_id,
              package_id: this.package_id,
              language: getEditLang(),
              status: 1,
              step: this.model
            })
          } else {
            this.updateActStepStatus2action({
              activity_id: this.activity_id,
              status: 1,
              step: this.model,
              language: 'ALL',
              loading: true
            })
          }
        }

        clearInterval(this.initInterval)
      }
    }
  },
  computed: {
    ...mapGetters(['getActStepStatus']),
    isPkgSpecial() {
      return this.$route.name === 'pkgSpecial'
    },
    model() {
      return this.$route.query.model
    },
    host() {
      return location.origin
    },
    activity_id() {
      return +this.$route.params.id
    },
    package_id() {
      return +this.$route.query.package_id
    },
    lang() {
      return this.$route.query.lang
    },
    ref() {
      return this.$route.query.ref || ''
    },
    pathDict() {
      let path = this.$root.roles.is_am ? 'activity' : 'content'

      return {
        act_tags: `activity/tags/${this.activity_id}`,
        act_fnb_menu: `activity/fnb_menu/${this.activity_id}`,
        act_hotel_voucher: `activity/hotel_voucher/${this.activity_id}`,
        activity_spu: `${path}/spu/${this.activity_id}`,
        activity_tags: `activity/tags/${this.activity_id}`,
        pkg_content: 'package/fnb_content',
        pkg_pickup_return_time: 'package/extra_time'
      }
    }
  },
  async mounted() {
    this.init()
  },
  deactivated() {
    this.clearTimeout()
    this.initInterval && clearInterval(this.initInterval)
    window.removeEventListener('message', this.messageListener)
  },
  methods: {
    ...mapActions([
      'updateActStepStatus2action',
      'updateActivityStepStatusById',
      'updatePkgStepStatus2action',
      'updatePackageStepStatusById'
    ]),
    goToPackageCb() {
      this.closedCb = setTimeout(async () => {
        let packages = await this.refreshPkgs()
        klook.bus.$emit('updatePkgInfos2bus')

        if (packages.length) {
          // wait pkg navigation
          this.$nextTick(() => {
            this.$router.push({
              name: 'packageBasicInfo',
              params: { id: this.activity_id },
              query: {
                ...this.$route.query,
                package_id: packages[0].package_id
              }
            })
          })
        }
      }, 60)
    },
    async init() {
      if (!Object.keys(this.pathDict).includes(this.model)) {
        this.$message.error(this.$t('28699'))
        return
      }

      let status = await this.getStepStatus()

      this.stepStatus = status
      if (status) {
        this.isCompleted = true
      } else {
        this.handleOpen()
      }
    },
    async getStepStatus() {
      if (this.isPkgSpecial) {
        let stepObj = this.$store.state.pkgStepStatusMap[this.package_id]
        return _.get(stepObj, this.model, 0)
      } else {
        let actStepStatus = this.$store.state.actStepStatus
        return _.get(actStepStatus, this.model, 0)
      }
    },
    sentMessage(
      stepWindow,
      {
        host = this.host,
        data = {
          type: 'special_step'
        },
        heartbeat = true,
        timeout = this.surviveFrequency // 5s
      } = {}
    ) {
      if (toString.call(data) === '[object Object]') {
        data = JSON.stringify({
          timestamp: +new Date(),
          ...data
        })
      }

      this.heartbeat = false
      stepWindow.postMessage(data, host)

      if (heartbeat) {
        clearTimeout(this.heartbeatTimeout)
        this.heartbeatTimeout = setTimeout(() => {
          if (!this.heartbeat && !this.isCompleted) {
            this.currentStatus = 3
            this.clearAllEvent()
            this.$message.warning(this.$t('28700'))
          }
        }, timeout)
      }
    },
    clearAllEvent() {
      this.clearTimeout()
      clearInterval(this.initInterval)
      this.initInterval = null
      window.removeEventListener('message', this.messageListener)
    },
    clearTimeout() {
      clearTimeout(this.heartbeatTimeout)
      this.heartbeatTimeout = null

      clearTimeout(this.sentMsgTimeout)
      this.sentMsgTimeout = null
    },
    messageListener(event) {
      let data = {}

      try {
        data = JSON.parse(event.data)
      } catch (e) {
        // console.log('# Error json parse: ', event.data)
      }

      if (event.origin === this.host && data.type === 'special_step') {
        this.childPageMounted = true

        if (data.action === 'goToPackage') {
          this.goToPackageCb()
        }

        if (data.status === 'completed') {
          this.currentStatus = 3
          this.isCompleted = true

          if (this.model === 'spu') {
            this.refreshPkgs()
            klook.bus.$emit('updatePkgInfos2bus')
          }
        } else if (data.status === 'pending') {
          this.currentStatus = 2
        } else if (data.status === 'closed') {
          this.sentMessage(this.stepWindow, {
            data: {
              type: 'closedACK'
            },
            heartbeat: false
          })

          return
        } else if (data.status === 'closedACK') {
          this.closedCb && this.closedCb()
          this.closedCb = null
          this.clearAllEvent()

          return
        }

        if (!this.isCompleted) {
          this.heartbeat = true
          this.clearTimeout()

          this.sentMsgTimeout = setTimeout(() => {
            this.sentMessage(this.stepWindow, {
              data: {
                type: 'heartbeat'
              }
            })
          }, this.heartbeatFrequency)
        }
      }
    },
    getUrl() {
      let query = new URLSearchParams({
        ...this.$route.query,
        activity_id: this.activity_id
      }).toString()
      let host = `${this.host}/${window.KLK_LANG_PATH}act`
      let path = this.pathDict[this.model]

      return `${host}/${path}?isSpecial=1&${query}`
    },
    handleOpen() {
      this.isCompleted = false
      this.clearAllEvent()

      let url = this.getUrl()
      if (!url) return
      let MaximumAttempts = this.MaximumAttempts
      this.initInterval = setInterval(() => {
        MaximumAttempts -= 1

        if (MaximumAttempts > 0) {
          if (this.childPageMounted) {
            console.log('# clearInterval, Child page mounted')
            clearInterval(this.initInterval)
            this.currentStatus = 2
          } else {
            this.stepWindow && this.sentMessage(this.stepWindow)
          }
        } else {
          this.clearAllEvent()
          console.log('# MaximumAttempts, failed to contact the child page')
        }
      }, this.confirmInitStatusFrequency)

      let stepWindow = window.open(url, 'stepWindow')

      if (stepWindow) {
        // this.sentMessage(stepWindow)
        this.currentStatus = 1
        this.stepWindow = stepWindow
        console.log('# listener start')
        window.addEventListener('message', this.messageListener)
      } else {
        this.currentStatus = 4
        this.clearAllEvent()
        this.$message.warning(this.$t('28701'))
      }
    }
  }
}
</script>

<style scoped lang="scss">
.special-container {
  padding: 20px;
  background-color: #fff;
}
.tips {
  padding: 20px;
  background-color: rgba(255, 171, 0, 0.05);
}
</style>
