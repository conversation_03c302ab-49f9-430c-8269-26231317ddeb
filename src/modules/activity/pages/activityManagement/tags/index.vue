<template>
  <div>
    <div class="common-basic-box tags-container">
      <div v-if="$root.isFNB" class="item">
        <h2>
          F&B Vertical Filter Management / Cuisine Type
        </h2>
        <br />
        <a-button size="small" @click="handleOpen">
          打开页面编辑
        </a-button>
      </div>
      <div class="item">
        <h2>
          Tag
        </h2>
        <br />
        <p style="color:gray">
          {{ isHotel ? $t('act_choose_tags_hotel') : $t('act_choose_tags') }}
        </p>
        <a-collapse v-model="tab" :bordered="false">
          <a-collapse-panel :header="$t('act_tags')" key="1">
            <span slot="extra">
              <a-popconfirm
                title="Are you sure to reset all tags?"
                placement="topRight"
                @confirm="handleReset(current_template)"
              >
                <a-button size="small" v-if="resetShowFn('1', current_template)" disabled @click.prevent.stop>
                  reset
                </a-button>
              </a-popconfirm>
            </span>

            <div v-for="item in current_template" :key="item.template_name">
              <header class="tag-name">{{ item.template_name }}</header>
              <a-checkbox-group v-model="item.data" class="tag-checkbox-group" disabled>
                <a-checkbox
                  v-for="tag in computedTags(item)"
                  :key="tag.tag_id"
                  :value="tag.tag_id"
                  :disabled="!is_em"
                  >{{ tag.name }}
                </a-checkbox>
              </a-checkbox-group>

              <template v-if="item.tags.length > 16">
                <div class="tag-unfold">
                  <span v-if="item.unfold" @click="handleUnfold(item)"> Pack up <a-icon type="up" /> </span>
                  <span v-else @click="handleUnfold(item)"> View all <a-icon type="down" /> </span>
                </div>
              </template>
            </div>
          </a-collapse-panel>

          <a-collapse-panel :header="$t('act_choose_more')" key="2">
            <span slot="extra">
              <a-popconfirm
                title="Are you sure to reset all tags?"
                placement="topRight"
                @confirm="handleReset(other_template)"
              >
                <a-button size="small" v-if="resetShowFn('2', other_template)" disabled @click.prevent.stop>
                  reset
                </a-button>
              </a-popconfirm>
            </span>

            <div v-for="item in other_template" :key="item.template_name">
              <header class="tag-name">{{ item.template_name }}</header>
              <a-checkbox-group v-model="item.data" disabled>
                <a-checkbox
                  v-for="tag in computedTags(item)"
                  :key="tag.tag_id"
                  :value="tag.tag_id"
                  :disabled="!is_em"
                  >{{ tag.name }}
                </a-checkbox>
              </a-checkbox-group>

              <template v-if="item.tags.length > 16">
                <div class="tag-unfold">
                  <span v-if="item.unfold" @click="handleUnfold(item)"> Pack up <a-icon type="up" /> </span>
                  <span v-else @click="handleUnfold(item)"> View all <a-icon type="down" /> </span>
                </div>
              </template>
            </div>
          </a-collapse-panel>
        </a-collapse>

        <br />
        <div>Checked Tag IDs: {{ checked_tags.join() }} <br /></div>
        <br />
        <br />
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import { getEditLang } from '@activity/utils'

export default {
  inject: ['getActGlobal2provide', 'page_from', 'edit_language'],
  data() {
    return {
      activity_id: parseInt(this.$route.params.id),

      firstCheckedCate: true,

      templates: [],
      platTree: [],
      tabObj: {},
      tab: ['1'],
      checked_tags: [],

      cacheCheckedTags: [],

      current_template_id: null,
      current_template: [],
      other_template: []
    }
  },
  computed: {
    current_tags() {
      return this.current_template.reduce((acc, cur) => [...acc, ...cur.tags], []).map((v) => v.tag_id)
    },
    other_tags() {
      return this.other_template.reduce((acc, cur) => [...acc, ...cur.tags], []).map((v) => v.tag_id)
    },
    is_em() {
      return this.page_from === 'em'
    },
    currentLanguage() {
      return lang_conf.getLangObj('F_LANG', 'B_LANG')[KLK_LANG]
    },
    isHotel() {
      return this.current_template_id === 14
    }
  },
  watch: {
    current_template: {
      deep: true,
      handler() {
        this.getCheckedTags()
      }
    },
    other_template: {
      deep: true,
      handler() {
        this.getCheckedTags()
      }
    },
    checked_tags(v) {
      if (this.isHotel && v.length > 1) {
        this.checked_tags = [v[v.length - 1]]
      }
    }
  },
  async beforeMount() {
    klook.bus.$emit('addHandleSave2bus', this.handleSave)
    let result =
      (await ajax.get(ADMIN_API.act.get_tags, {
        params: {
          activity_id: this.activity_id,
          page_from: this.page_from,
          language: this.edit_language
        }
      })) || {}

    let { template_id } = result
    if (!template_id) {
      console.error('Error: template id')
      throw new Error('Error: template id')
    }

    this.templates = result.templates || []
    this.current_template_id = template_id
    this.checked_tags = result.tag_ids || []
    this.cacheCheckedTags = _.cloneDeep(this.checked_tags)

    let tagsData = this.fmtTagsData()
    this.current_template = tagsData.filter((v) => v.template_id === this.current_template_id)
    this.other_template = tagsData.filter((v) => v.template_id !== this.current_template_id)

    // show other tab if checked
    if (new Set(this.current_tags.concat(this.checked_tags)).size !== new Set(this.current_tags).size) {
      this.tab.push('2')
    }
  },
  methods: {
    ...mapActions(['updateActStepStatus2action']),
    handleOpen() {
      let query = new URLSearchParams(this.$route.query).toString()

      window.open(`/act/activity/tags/${this.activity_id}?isSpecial=1&${query}`)
    },
    getCheckedTags: _.debounce(function() {
      this.checked_tags = [...this.current_template, ...this.other_template].reduce(
        (acc, curr) => [...acc, ...curr.data],
        []
      )
    }, 300),
    fmtTagsData() {
      let checked_tags = this.checked_tags
      return this.templates.map((item) => ({
        ...item,
        unfold: false,
        data: item.tags.reduce((acc, curr) => {
          if (checked_tags.includes(curr.tag_id)) {
            return [...acc, curr.tag_id]
          }
          return acc
        }, [])
      }))
    },
    handleUnfold(item) {
      item.unfold = !item.unfold
      this.$forceUpdate()
    },
    computedTags(data) {
      if (data.unfold) {
        return data.tags
      }

      return data.tags.slice(0, 16)
    },
    resetShowFn(key, data) {
      if (!this.tab.includes(key)) {
        return false
      }

      for (let item of data) {
        let tags = item.tags
        for (let tag of tags) {
          if (this.checked_tags.includes(tag.tag_id)) {
            return true
          }
        }
      }

      return false
    },
    handleReset(data) {
      data.forEach((item) => (item.data = []))
    },
    check() {
      if (
        new Set(this.other_tags.concat(this.checked_tags)).size === new Set(this.other_tags).size &&
        this.is_em
      ) {
        this.$warning('Please choose activity main tag')
        return false
      }
      if (this.checked_tags.length > 3) {
        this.$warning(`already chosen tags: ${this.checked_tags}, 3 tags maximum`)
        return false
      }
      return true
    },
    /*
     * GUESS:
     * edit_staus == 1 means to save for real
     * `c=1` means for real
     */
    async handleSave() {
      // if (_.difference(this.checked_tags, this.cacheCheckedTags).length) {
      //   await ajax.postBody(ADMIN_API.act.update_tags, {
      //     data: {
      //       tag_ids: this.checked_tags,
      //       activity_id: this.activity_id,
      //       language: this.edit_language
      //     }
      //   })
      // }

      if (['admin', 'em'].includes(this.page_from)) {
        this.$message.success(this.$t('global_success'))

        this.updateActStepStatus2action({
          activity_id: +this.activity_id,
          language: getEditLang(),
          status: 1,
          step: 'activity_tags'
        })
        klook.bus.$emit('updateActStepStatus2bus')

        let { query, params } = this.$route
        this.$router.push({
          path: `/act/activity/notes/${this.activity_id}`,
          query,
          params
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tags-container {
  .item {
    margin-bottom: 20px;
  }
  ::v-deep {
    .ant-collapse-content {
      background-color: #fff;
    }
    .ant-collapse div.ant-collapse-item {
      border: none;
    }
    .ant-collapse-header {
      background-color: #fff;
      transition: background-color 0.5s;
    }
    .ant-collapse-item-active .ant-collapse-header {
      background-color: #fafafa;
    }
    .ant-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-top: 20px;
    }
    .ant-checkbox-wrapper {
      display: inline-flex;
      width: 25%;
      flex: 0 0 25%;
      margin-left: 0;
      margin-bottom: 20px;
      .ant-checkbox {
        margin-top: 6px;
      }
    }

    .ant-checkbox {
      height: fit-content;
      & + span {
        white-space: normal;
        text-overflow: ellipsis;
        overflow: hidden;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }
    }
  }
  .tag-name {
    margin-top: 12px;
    font-size: 15px;
    color: #000;
  }
  .tag-checkbox-group {
    margin-top: 20px;
  }
  .tag-unfold {
    margin-bottom: 20px;
    color: #0091ff;
  }
}
</style>
