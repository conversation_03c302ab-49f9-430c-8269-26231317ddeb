export const rejectOptions = [
  {
    label: 'Context Error',
    value: 1000,
    children: [
      {
        label: 'time format error',
        value: 1001
      },
      {
        label: 'undefine characters',
        value: 1002
      }
    ]
  },
  {
    label: 'Incompleted Info',
    value: 2000,
    children: [
      {
        label: 'Incompleted Package Info',
        value: 2001
      },
      {
        label: 'Incompleted Package Detail',
        value: 2002
      },
      {
        label: 'Missing Photo',
        value: 2003
      },
      {
        label: 'Incompleted Other Info',
        value: 2004
      },
      {
        label: 'Incompleted Itinerary',
        value: 2005
      },
      {
        label: 'Others',
        value: 2006
      }
    ]
  },
  {
    label: 'Duplicate/Similar Product Exists',
    value: 3000
  },
  {
    label: 'Product with low demand',
    value: 4000
  },
  {
    label: 'Price&Inventory Issue',
    value: 5000,
    children: [
      {
        label: 'Pricing Error',
        value: 5001
      },
      {
        label: 'Availablity Issue',
        value: 5002
      },
      {
        label: 'Surcharge',
        value: 5003
      }
    ]
  },
  {
    label: 'Product ineligible under operation',
    value: 6000
  },
  {
    label: 'Platform revision needed',
    value: 7000
  },
  {
    label: 'Others',
    value: 8000
  }
]

export const otherOptions = [10206, 108]
export const priceOtherOptions = [4]

const getOptionData = (list) => {
  return list.map((item) => {
    const { reason_id, text, child_list } = item
    const data = {
      value: reason_id,
      label: text
    }
    const children = Array.isArray(child_list) ? getOptionData(child_list) : []
    if (children.length) {
      data.children = children
    }
    return data
  }, [])
}

export const getOptions = async (url, options = {}) => {
  const res = await ajax.get(url, options)
  const list = res || []
  return getOptionData(list)
}

export const getNoteData = (value, note) => {
  const list = Array.isArray(value) ? value : []
  const index = value.length - 1
  const data = {
    reason_id: list[index],
    other_text: note.trim()
  }
  return JSON.stringify(data)
}

import { isMerchant } from '@/env'

export const DEFAULT_SUPPORT_SPU_PAGE_CONF = {
  support_spu_page: !isMerchant,
  support_create_new_spu: false
}
