<template>
  <div class="form-wrap">
    <a-form-model
      ref="form"
      :model="form"
      type="inline"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
      :rules="rules"
    >
      <a-form-model-item class="form-item" :label="$t('79345')" label-align="left" required>
        <a-select v-model="form.redirect_type" class="input" :placeholder="$t('global_please_select')">
          <a-select-option :value="0">
            {{ $t('79328') }}
          </a-select-option>
          <a-select-option :value="1">
            {{ $t('79329') }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item class="form-item" :label="$t('79330')" label-align="left" prop="from" required>
        <a-input
          v-model="form.from"
          class="input"
          :placeholder="$t('global_please_input')"
          :allow-clear="true"
        ></a-input>
      </a-form-model-item>
      <a-form-model-item
        class="form-item"
        label-align="left"
        prop="to"
        required
        :colon="form.redirect_type === 0"
      >
        <a-input
          v-model="form.to"
          class="input"
          :placeholder="$t('global_please_input')"
          :allow-clear="true"
        ></a-input>
        <span slot="label">
          {{ toLabel }}
          <QuestionIcon
            v-show="form.redirect_type === 1"
            :message="$t('79416') + '<br />' + $t('79417')"
            :overlay-style="overlayStyle"
          />
        </span>
      </a-form-model-item>
      <div class="btn-wrap" @click="toggleMore">
        <a-button class="btn" type="link">{{ $t('79346') }}</a-button>
        <a-icon class="btn-suffix" :class="{ active: showMore }" type="double-left" />
      </div>
      <div v-show="showMore">
        <a-form-model-item class="form-item" :label="$t('79959')" label-align="left">
          <a-select
            v-model="form.platform_list"
            class="input"
            mode="multiple"
            :placeholder="$t('global_please_select')"
            :options="platformList"
          ></a-select>
        </a-form-model-item>
        <a-form-model-item class="form-item" :label="$t('79333')" label-align="left">
          <a-select
            v-model="form.language_list"
            class="input"
            mode="multiple"
            :placeholder="$t('global_please_select')"
            :options="languageList"
          ></a-select>
        </a-form-model-item>
        <a-form-model-item class="form-item" label-align="left" prop="app_version" :colon="false">
          <a-input
            v-model="form.app_version"
            class="input"
            :placeholder="$t('global_please_input')"
          ></a-input>
          <span slot="label">
            {{ $t('79338') }}
            <QuestionIcon :message="$t('79419')" :overlay-style="overlayStyle" />
          </span>
        </a-form-model-item>
        <a-form-model-item
          v-auth="'redirectionABTest'"
          class="form-item"
          :label="$t('78260')"
          label-align="left"
          prop="abtest_list"
        >
          <a-textarea
            v-model="form.abtest_list"
            class="input"
            placeholder="abname1:group1,abname1:group2"
          ></a-textarea>
          <div class="tips">{{ $t('78267') }}</div>
        </a-form-model-item>
      </div>
    </a-form-model>
  </div>
</template>
<script>
import { platformList, languageList, checkUrl, checkVersion, checkABTest } from '../utils/index.js'
import QuestionIcon from '@activity/pages/package/units/components/QuestionIcon.vue'

export default {
  components: {
    QuestionIcon
  },
  props: {
    defaultType: {
      type: Number,
      default: 0
    }
  },
  data() {
    const validatorFromFun = (rule, value, callback) => {
      if (!value.trim()) {
        callback(new Error(this.$t('global_please_input')))
      } else {
        callback()
      }
    }
    const validatorToFun = (rule, value, callback) => {
      const val = value.trim()
      if (!val) {
        callback(new Error(this.$t('global_please_input')))
        return
      }
      const from = this.form.from.trim()
      const abtest_list = this.form.abtest_list.trim()
      const redirect_type = this.form.redirect_type
      if (val === from && redirect_type === 0 && !abtest_list) {
        callback(new Error(this.$t('79429', { AID1: ` ${from}`, AID2: ` ${val}` })))
        return
      }
      if (!checkUrl(val) && redirect_type === 1) {
        callback(new Error(this.$t('79960')))
        return
      }
      callback()
    }
    const validatorABTestFun = (rule, value, callback) => {
      const val = value.trim()
      if (!val) {
        callback()
        return
      }
      if (!checkABTest(val)) {
        callback(new Error(' '))
        return
      }
      callback()
    }
    return {
      form: {
        redirect_type: 0,
        from: '',
        to: '',
        app_version: '',
        country_code_list: [],
        language_list: [],
        platform_list: [],
        abtest_list: ''
      },
      rules: {
        from: [
          {
            validator: validatorFromFun
          }
        ],
        to: [
          {
            validator: validatorToFun
          }
        ],
        app_version: [
          {
            validator(rule, value, callback) {
              const val = value.trim()
              if (!val) {
                callback()
                return
              }
              if (!checkVersion(val)) {
                callback(new Error('Ex: 6.27.0'))
                return
              }
              callback()
            }
          }
        ],
        abtest_list: [
          {
            validator: validatorABTestFun
          }
        ]
      },
      platformList,
      languageList,
      showMore: false,
      overlayStyle: {
        maxWidth: '500px'
      }
    }
  },
  computed: {
    toLabel() {
      return this.form.redirect_type === 0 ? this.$t('79331') : this.$t('79453')
    }
  },
  watch: {
    defaultType: {
      immediate: true,
      handler(v) {
        this.form.redirect_type = v
      }
    },
    'form.redirect_type': {
      handler() {
        const form = this.$refs.form
        if (form && this.form.to) {
          form.validateField(['to'])
        }
      }
    }
  },
  methods: {
    toggleMore() {
      this.showMore = !this.showMore
    },
    reset() {
      this.$refs.form.resetFields()
    },
    validateForm() {
      return this.$refs.form.validate().catch(() => false)
    },
    getFromData() {
      const form = this.form
      let {
        from,
        to,
        app_version,
        country_code_list,
        language_list,
        platform_list,
        redirect_type,
        abtest_list
      } = form
      if (language_list.length === this.languageList.length) {
        language_list = []
      }
      if (platform_list.length === this.platformList.length) {
        platform_list = []
      }
      const ab = abtest_list.split(',')
      return {
        to: to.trim(),
        from: from.trim(),
        app_version: app_version.trim(),
        country_code_list,
        platform_list,
        language_list,
        redirect_type,
        abtest_list: ab
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.form-wrap {
  .input {
    width: 300px;
  }
  .btn-wrap {
    display: inline-block;
    cursor: pointer;
    .btn-suffix {
      color: #1890ff;
      transform: rotate(-90deg);
      margin-left: 4px;
      &.active {
        transform: rotate(90deg);
      }
    }
    .btn {
      padding-left: 0;
      padding-right: 0;
      margin: 10px 0;
    }
  }
}
.tips {
  color: #999;
  line-height: 20px;
}
</style>
