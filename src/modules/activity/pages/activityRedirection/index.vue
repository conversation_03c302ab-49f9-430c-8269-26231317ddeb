<template>
  <div class="page-wrapper">
    <div ref="topRef">
      <SearchForm :search-type="activeKey" @search="search" />
    </div>
    <div class="table-tabs">
      <div class="tabs-wrap">
        <div class="tabs" :class="{ active: activeKey === '0' }" @click="tabsChange('0')">
          {{ $t('79328') }}
        </div>
        <div class="tabs" :class="{ active: activeKey === '1' }" @click="tabsChange('1')">
          {{ $t('79329') }}
        </div>
      </div>
      <div v-if="redirectionEdit" class="operate-wrap">
        <a-button type="primary" @click="visible = true">
          {{ $t('79326') }}
        </a-button>
        <div v-if="redirectionBulkUpload" class="right-btn-wrap">
          <a-upload
            accept=".csv, .xls, .xlsx"
            max-count="1"
            :file-list="fileList"
            :custom-request="customRequest"
            @change="onFileChange"
          >
            <a-button class="btn">
              {{ $t('79327') }}
            </a-button>
          </a-upload>
          <a-popover placement="right">
            <a-button class="btn link" type="link">
              {{ $t('79434') }}
            </a-button>
            <div slot="content">
              <div class="pop-text" @click="exoprtFile(0)">{{ $t('79328') }}</div>
              <div class="pop-text" @click="exoprtFile(1)">{{ $t('79329') }}</div>
            </div>
          </a-popover>
        </div>
      </div>
    </div>
    <div v-show="activeKey === '0'" class="table-wrap">
      <a-table
        id="tableA2A"
        :data-source="dataSourceA2A"
        :columns="aidColumns"
        :row-key="(record, index) => index"
        :pagination="false"
        :loading="tableLoading"
        bordered
      >
        <div slot="action" slot-scope="row">
          <a-button type="link" :disabled="!redirectionEdit" @click="deleteRedirect(row)">
            {{ $t('global_delete') }}
          </a-button>
          <a-button
            v-if="redirectionMigrateReviews"
            type="link"
            :disabled="getDisabled(row) || !redirectionMigrateReviews || !redirectionEdit"
            @click="copyReview(row)"
          >
            {{ $t('79433') }}
          </a-button>
        </div>
        <template slot="conditions" slot-scope="row">
          <a-tooltip :overlay-style="overlayStyle" placement="topLeft">
            <div slot="title">{{ getConditions(row) }}</div>
            {{ getConditions(row) }}
          </a-tooltip>
        </template>
        <div slot="migrate_state" slot-scope="row, data">
          <a-tooltip :title="data.migrate_result">
            {{ getState(row) }}
            <a-icon v-if="row === 4" class="tips-icon" type="exclamation-circle" />
          </a-tooltip>
        </div>
      </a-table>
      <div class="pagination-wrap">
        <a-pagination
          v-model="paginationA2A.current"
          :total="paginationA2A.total"
          :page-size="paginationA2A.pageSize"
          @change="tableChange"
        />
      </div>
    </div>
    <div v-show="activeKey === '1'" class="table-wrap">
      <a-table
        id="tableA2U"
        :data-source="dataSourceA2U"
        :columns="urlColumns"
        :row-key="(record, index) => index"
        :pagination="false"
        :loading="tableLoading"
        bordered
      >
        <div slot="action" slot-scope="row">
          <a-button type="link" :disabled="!redirectionEdit" @click="deleteRedirect(row)">
            {{ $t('global_delete') }}
          </a-button>
        </div>
        <template slot="abtest_list" slot-scope="row">
          {{ row.join(',') }}
        </template>
        <template slot="conditions" slot-scope="row">
          <a-tooltip :overlay-style="overlayStyle" placement="topLeft">
            <div slot="title">{{ getConditions(row) }}</div>
            {{ getConditions(row) }}
          </a-tooltip>
        </template>
        <template slot="to" slot-scope="row">
          <a-tooltip :overlay-style="overlayStyle" placement="topLeft">
            <div slot="title">{{ row }}</div>
            {{ row }}
          </a-tooltip>
        </template>
        <div slot="migrate_state" slot-scope="row">
          {{ getState(row) }}
        </div>
      </a-table>
      <div class="pagination-wrap">
        <a-pagination
          v-model="paginationA2U.current"
          :total="paginationA2U.total"
          :page-size="paginationA2U.pageSize"
          @change="tableChange"
        />
      </div>
    </div>

    <!-- 新建modal -->
    <a-modal
      v-model="visible"
      width="650px"
      title="Create new redirection"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <CreateForm ref="createForm" :default-type="Number(activeKey)" />
    </a-modal>
  </div>
</template>
<script>
import SearchForm from './searchForm/index.vue'
import CreateForm from './createForm/index.vue'
import { platformList, languageList, statusList, checkUrl, checkVersion, getColumns } from './utils/index.js'
import { exportExcel, readExcel } from './utils/excel'
import { pmsConfirm } from '@activity/utils'
import { checkAuth } from '@/plugins/authDirective'

export default {
  components: {
    SearchForm,
    CreateForm
  },
  data() {
    return {
      activeKey: '0',
      dataSourceA2A: [],
      dataSourceA2U: [],
      aidColumns: [],
      urlColumns: [],
      paginationA2U: {
        current: 1,
        total: 0,
        pageSize: 20
      },
      paginationA2A: {
        current: 1,
        total: 0,
        pageSize: 20
      },
      visible: false,
      tableLoading: false,
      overlayStyle: {
        maxWidth: '400px'
      },
      fileList: [],
      scrollY: 500,
      searchForm: {
        from: '',
        redirect_url: ''
      },
      // 权限
      redirectionEdit: false,
      redirectionBulkUpload: false,
      redirectionMigrateReviews: false
    }
  },
  created() {
    this.getDataSourceA2A()
    this.getDataSourceA2U()
    this.redirectionEdit = checkAuth('redirectionEdit')
    this.redirectionBulkUpload = checkAuth('redirectionBulkUpload')
    this.redirectionMigrateReviews = checkAuth('redirectionMigrateReviews')

    const abtestAuth = checkAuth('redirectionABTest')
    const auth = {
      'experience/act/redirection/abtest': abtestAuth
    }
    this.aidColumns = getColumns(1, auth)
    this.urlColumns = getColumns(2, auth)
  },
  mounted() {
    this.$nextTick(() => {
      this.getScrollY()
    })
  },
  methods: {
    customRequest() {},
    checkAuth,
    getDisabled(row) {
      const status = row.migrate_state
      if (status === 2) {
        return true
      }
      return false
    },
    tabsChange(tab) {
      this.activeKey = tab
    },
    search(data) {
      this.searchForm = data
      this.getData(true)
    },
    tableChange(pagination) {
      const activeKey = this.activeKey
      const current = pagination
      if (activeKey === '0') {
        this.paginationA2A.current = current
        this.getDataSourceA2A()
        return
      }
      this.paginationA2U.current = current
      this.getDataSourceA2U()
    },
    getScrollY() {
      const topRef = this.$refs.topRef
      const ch = window.innerHeight
      this.scrollY = ch - topRef.offsetHeight - 320
    },
    getData(isSearch = false, key) {
      const activeKey = key || this.activeKey
      if (activeKey === '0') {
        if (isSearch) {
          this.paginationA2A.current = 1
        }
        this.getDataSourceA2A()
        return
      }
      if (isSearch) {
        this.paginationA2U.current = 1
      }
      this.getDataSourceA2U()
    },
    scrollTable(table) {
      const tableA2A = document.querySelector(`#${table}`)
      const tableBody = tableA2A.querySelector('.ant-table-body')
      tableBody.scrollTo(0, 0)
    },
    async getDataSourceA2A() {
      this.tableLoading = true
      const { from } = this.searchForm
      const { current } = this.paginationA2A
      const data = await this.getDataSource({
        limit: 20,
        page: current,
        from,
        redirect_type: 0
      })
      const total = data.total
      const items = data.items || []
      this.dataSourceA2A = items
      this.paginationA2A.total = total
      this.tableLoading = false
      this.scrollTable('tableA2A')
    },
    async getDataSourceA2U() {
      this.tableLoading = true
      const { from, redirect_url } = this.searchForm
      const { current } = this.paginationA2U
      const data = await this.getDataSource({
        limit: 20,
        page: current,
        from,
        redirect_url,
        redirect_type: 1
      })
      const total = data.total
      const items = data.items || []
      this.dataSourceA2U = items
      this.paginationA2U.total = total
      this.tableLoading = false
      this.scrollTable('tableA2U')
    },
    initItems(data = [], type) {
      const items = []
      for (let i = 0; i < data.length; i++) {
        const item = data[i]
        const initData = this.initData(item, type)
        if (!initData) {
          return null
        }
        items.push(initData)
      }

      return items
    },
    initData(item = [], type) {
      let [
        from = '',
        to = '',
        platform_list = '',
        language_list = '',
        app_version = '',
        is_migrate = ''
      ] = item
      from = this.trimString(from)
      to = this.trimString(to)
      app_version = this.trimString(app_version)
      language_list = this.trimString(language_list)
      platform_list = this.trimString(platform_list)
      is_migrate = this.trimString(is_migrate)
      const redirect_type = type
      language_list = language_list ? this.trimItem(language_list.split(',')) : []
      platform_list = platform_list ? this.trimItem(platform_list.toLowerCase().split(',')) : []

      if (language_list.some((item) => item.toLowerCase() === 'all')) {
        language_list = []
      }

      if (platform_list.includes('all')) {
        platform_list = []
      }

      if (app_version === 'all') {
        app_version = ''
      }

      const temp = is_migrate.toLowerCase()
      is_migrate = temp === 'yes' || temp === 'y' || temp === 'true' ? true : false

      const checkLang = language_list.every((item) => {
        const [data] = languageList.filter((it) => it.value === item)
        return !!data
      })
      const checkPlatform = platform_list.every((item) => {
        const [data] = platformList.filter((it) => it.value === item)
        return !!data
      })

      if (!from || !to || !checkLang || !checkPlatform) {
        this.$message.error('Template error !')
        return null
      }

      if (redirect_type === 1 && !checkUrl(to)) {
        this.$message.error(this.$t('79420'))
        return null
      }

      if (redirect_type === 0 && from === to) {
        this.$message.error(this.$t('79429', { AID1: ` ${from}`, AID2: ` ${to}` }))
        return null
      }

      if (app_version && !checkVersion(app_version)) {
        this.$message.error(this.$t('79422'))
        return null
      }

      return {
        from,
        to,
        app_version,
        is_migrate,
        language_list,
        platform_list,
        country_code_list: [],
        redirect_type
      }
    },
    trimItem(list = []) {
      return list.map((item) => {
        return String(item).trim()
      })
    },
    trimString(str = '') {
      return (str + '').trim()
    },
    onFileChange(file) {
      const reader = new FileReader()
      reader.onload = (event) => {
        const { data, type } = readExcel(event.target.result)
        const items = this.initItems(data, type)
        if (!items) {
          return
        }
        if (items.length > 50) {
          return this.$message.error('Max limitation 50 !')
        }
        this.batchCreateRedirect(items)
      }
      reader.readAsBinaryString(file.file.originFileObj)
    },
    exoprtFile(type) {
      exportExcel(type)
    },
    async handleOk() {
      const createForm = this.$refs.createForm
      const validate = await createForm.validateForm()
      if (!validate) {
        return
      }
      const data = createForm.getFromData()
      this.createRedirect(data)
    },
    async createRedirect(data) {
      const result = await ajax.postBody(
        ADMIN_API.act.create_activity_redirect,
        {
          data
        },
        { loading: true }
      )
      if (!result.success) {
        return
      }
      this.visible = false
      this.$refs.createForm.reset()
      this.getData(false, `${data.redirect_type}`)
    },
    handleCancel() {
      this.$refs.createForm.reset()
    },
    getConditions(row = {}) {
      const { platform_list = [], language_list = [], app_version = '' } = row
      let lang = language_list.join(',')
      if (!lang) {
        lang = 'all'
      }
      let platform = platform_list.join(',')
      if (!platform) {
        platform = 'all'
      }
      return `Platform: ${platform}; Language: ${lang}; Versions: ${app_version}`
    },
    getState(status = 0) {
      return statusList[status]
    },
    async getDataSource(data) {
      const result = await ajax.postBody(ADMIN_API.act.get_activity_redirect_page, {
        data
      })
      if (!result.success) {
        return {}
      }
      return result.result || {}
    },
    async deleteRedirect(data) {
      const confirm = await pmsConfirm.call(this, {
        title: this.$t('global_confirm_delete'),
        okText: this.$t('global_confirm'),
        cancelText: this.$t('global_button_cancel')
      })
      if (!confirm) {
        return
      }
      const { from, to, redirect_type = 0 } = data
      const result = await ajax.postBody(
        ADMIN_API.act.delete_activity_redirect,
        {
          data: {
            from,
            to,
            redirect_type
          }
        },
        {
          loading: true
        }
      )
      if (!result.success) {
        // const message = result.error.message
        // this.$message.error(message)
        return
      }
      this.getData()
    },
    async copyReview(data) {
      const confirm = await pmsConfirm.call(this, {
        title: this.$t('84250'),
        okText: this.$t('global_confirm'),
        cancelText: this.$t('global_button_cancel')
      })
      if (!confirm) {
        return
      }
      const { from, to } = data
      const result = await ajax.postBody(
        ADMIN_API.act.copy_activity_participant_and_review,
        {
          data: {
            from,
            to
          }
        },
        {
          loading: true
        }
      )
      if (!result.success) {
        return
      }
      data.migrate_state = 2
      this.$message.success(this.$t('loading_text'))
    },
    async batchCreateRedirect(data) {
      if (!data || !data.length) {
        return
      }
      const result = await ajax.postBody(
        ADMIN_API.act.sync_batch_create_activity_redirect_and_copy_review,
        {
          data: {
            items: data
          }
        },
        {
          loading: true
        }
      )
      if (!result.success) {
        return
      }
      const fails = result.result?.fails || []
      if (fails.length) {
        const content = fails.map((item) => {
          const { from, to, result } = item
          return (
            <div>
              {from} - {to} - {result}
            </div>
          )
        })
        this.$error({
          title: 'Error',
          content: content
        })
      }
      this.getData()
    }
  }
}
</script>
<style lang="scss" scoped>
.pop-text {
  padding: 5px 10px;
  font-size: 16px;
  color: #1890ff;
  cursor: pointer;
}
.tips-icon {
  font-size: 16px;
  margin-left: 2px;
}
.page-wrapper {
  // max-width: 1500px;
  .table-tabs {
    padding: 20px 0;
    display: flex;
    justify-content: space-between;
    .tabs-wrap {
      display: flex;
      border-bottom: 1px solid #e8e8e8;
      height: 42px;

      .tabs {
        height: 100%;
        line-height: 40px;
        text-align: center;
        border-right: 1px solid #e8e8e8;
        border-top: 1px solid #e8e8e8;
        border-left: 1px solid #e8e8e8;
        border-bottom: 1px solid transparent;
        border-radius: 4px 4px 0 0;
        cursor: pointer;
        padding: 0 20px;
        margin-right: 5px;
        &.active {
          color: #1890ff;
        }
        &:hover {
          color: #1890ff;
        }
      }
    }
  }
  .pagination-wrap {
    padding: 20px 0;
    display: flex;
    justify-content: flex-end;
  }
  .operate-wrap {
    display: flex;
    .right-btn-wrap {
      margin-left: 20px;
      .btn {
        display: block;
        min-width: 180px;
        &.link {
          padding: 0;
        }
      }
    }
  }
}
</style>
