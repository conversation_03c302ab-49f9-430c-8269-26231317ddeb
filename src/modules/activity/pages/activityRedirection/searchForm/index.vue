<template>
  <div class="form-wrap">
    <a-form ref="form" layout="inline">
      <a-form-item label="AID">
        <a-input
          v-model="form.from"
          class="input"
          :placeholder="$t('global_please_input')"
          :allow-clear="true"
        ></a-input>
      </a-form-item>
      <a-form-item v-show="searchType === '1'" label="URL">
        <a-input
          v-model="form.redirect_url"
          class="input"
          :placeholder="$t('global_please_input')"
          :allow-clear="true"
        ></a-input>
      </a-form-item>
      <a-form-item>
        <a-button class="btn" type="primary" @click="search">{{ $t('48139') }}</a-button>
        <a-button class="btn" @click="reset">{{ $t('48140') }}</a-button>
      </a-form-item>
    </a-form>
  </div>
</template>
<script>
export default {
  props: {
    searchType: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      form: {
        redirect_url: '',
        from: ''
      }
    }
  },
  methods: {
    reset() {
      this.form = {
        redirect_url: '',
        from: ''
      }
      this.$emit('search', this.form)
    },
    search() {
      const { redirect_url = '', from = '' } = this.form
      this.$emit('search', { from: from.trim(), redirect_url: redirect_url.trim() })
    }
  }
}
</script>
<style lang="scss" scoped>
.form-wrap {
  background: #f5f5f5;
  padding: 30px;
  border-radius: 5px;
  .input {
    width: 300px;
  }
  .btn {
    margin-left: 20px;
  }
}
</style>
