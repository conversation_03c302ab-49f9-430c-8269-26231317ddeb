import XLSX from 'xlsx'

const header = [
  [__('79330'), __('79331'), __('79959'), __('79333'), __('79338'), __('79332')],
  [__('79330'), __('79329'), __('79959'), __('79333'), __('79338')]
]

const tips = [
  [
    'number without any special letters: ex 167, 2115 etc',
    'number without any special letters: ex 167, 2115 etc',
    'Applicable platform: All, App, Mweb, Desktop etc',
    `Applicable language:
    All, en_US, zh_CN, zh_TW, ja_JP, ko_KR, th_TH, vi_VN, id_ID, zh_HK, en_BS, en_AU, en_NZ, en_GB, en_IN, en_SG, en_CA, en_HK, en_PH, en_MY, fr_FR, es_ES, de_DE, ru_RU, it_IT etc`,
    'Applicable version ex: 6.27.0',
    'ex: True. False'
  ],
  [
    'number without any special letters: ex 167, 2115 etc',
    `URL rules as follows：
    1.URL pathname should begin with https://
    2.If multi language mapping is not required for this redirection, just copy&paste the url in the input field
    3. If multi language mapping is required for this redirection, follow the format below when input URL
    {host}:https://klook.com
    {language}:klook language
    Ex: {host}{language}catering/product/77754-seamart`,
    'Applicable platform: All, App, Mweb, Desktop etc',
    `Applicable language:
    All, en_US, zh_CN, zh_TW, ja_JP, ko_KR, th_TH, vi_VN, id_ID, zh_HK, en_BS, en_AU, en_NZ, en_GB, en_IN, en_SG, en_CA, en_HK, en_PH, en_MY, fr_FR, es_ES, de_DE, ru_RU, it_IT etc`,
    'Applicable version ex: 6.27.0 '
  ]
]

const widthList = [{ wpx: 200 }, { wpx: 200 }, { wpx: 200 }, { wpx: 200 }, { wpx: 200 }, { wpx: 200 }]
export const exportExcel = function(type = 0) {
  const data = [tips[type], header[type]]
  const ws = XLSX.utils.aoa_to_sheet(data)
  const wb = XLSX.utils.book_new()
  ws['!cols'] = widthList
  XLSX.utils.book_append_sheet(wb, ws, type === 0 ? 'AID2AID' : 'AID2URL')
  const fileName = type === 0 ? 'AID-TO-AID' : 'AID-TO-URL'
  XLSX.writeFile(wb, `${fileName}-template.xlsx`)
}

export const readExcel = function(data) {
  let workbook = XLSX.read(data, {
    type: 'binary'
  })
  const SheetNames = workbook.SheetNames
  const type = SheetNames[0] === 'AID2AID' ? 0 : 1
  let sheetData = SheetNames.reduce((acc, curr) => {
    const ws = workbook.Sheets[curr]
    const data = XLSX.utils.sheet_to_json(ws, {
      header: 1,
      range: 2
    })
    const realData = data.filter((item) => !item.every((v) => v === ''))
    return [...acc, ...realData]
  }, [])

  return { data: sheetData, type }
}
