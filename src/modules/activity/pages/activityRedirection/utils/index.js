const lang_conf = require('lang_conf')

const lang_array = lang_conf.getAllLangOption().map((v) => ({
  label: v.options,
  value: v.B_LANG
}))

export const languageList = [
  // {
  //   label: 'All',
  //   value: 'all'
  // },
  ...lang_array
]

const columns = [
  { title: __('79330'), dataIndex: 'from', key: 'from', width: 160 },
  { title: __('79331'), dataIndex: 'to', key: 'to', width: 160, isAid: true },
  {
    title: __('79329'),
    dataIndex: 'to',
    key: 'to',
    ellipsis: true,
    isUrl: true,
    scopedSlots: { customRender: 'to' }
  },
  {
    title: __('79332'),
    dataIndex: 'migrate_state',
    key: 'migrate_state',
    width: 230,
    isAid: true,
    scopedSlots: { customRender: 'migrate_state' }
  },
  {
    title: 'Experiment & Group Name',
    key: 'abtest_list',
    dataIndex: 'abtest_list',
    scopedSlots: { customRender: 'abtest_list' },
    ellipsis: true,
    auth: ['experience/act/redirection/abtest']
  },
  {
    title: __('79938'),
    key: 'conditions',
    ellipsis: true,
    scopedSlots: { customRender: 'conditions' }
  },
  {
    title: __('taxonomy_dest_version_create_time'),
    dataIndex: 'update_time',
    key: 'update_time',
    width: 200
  },
  { title: __('package_price_operator'), dataIndex: 'operator', key: 'operator', ellipsis: true },
  {
    title: __('76542'),
    width: 220,
    scopedSlots: { customRender: 'action' },
    customCell() {
      return { style: { padding: 0 } }
    }
  }
]
export const aidColumns = columns.filter((item) => !item.isUrl)
export const urlColumns = columns.filter((item) => !item.isAid)

export const platformList = [
  // {
  //   label: 'All',
  //   value: 'all'
  // },
  {
    label: 'App',
    value: 'app'
  },
  {
    label: 'Mweb',
    value: 'mweb'
  },
  {
    label: 'Desktop',
    value: 'desktop'
  }
]

// 0-未执行，失败也属于未执行；1-成功，2-执行中，3-未知 4-失败
export const statusList = {
  0: __('79942'),
  1: __('79939'),
  2: __('79941'),
  3: __('79943'),
  4: __('79940')
}

export const checkUrl = function(url = '') {
  // const isVariableHost = /^\{host\}\{language\}.*$/g.test(url)
  // const isHttps = /(http|https):\/\/([\w.]+\/?)\S*/.test(url)
  // const isVariableLang = /^\{language\}.*$/g.test(url)
  // return isVariableHost || isHttps || isVariableLang
  return true
}

export const checkVersion = function(url = '') {
  const isVariable = /^\d+\.\d+\.\d+$/g.test(url)
  return isVariable
}

export const checkABTest = function(abtest = '') {
  const arr = abtest.trim().split(',')
  const reg = /\s+/g
  const validate = arr.every((item) => {
    const itemArr = item.split(':')
    if (itemArr.length != 2) {
      return false
    }
    return itemArr.every((it) => {
      const test = reg.test(it)
      return !test && !!it
    })
  })
  return validate
}

export const getColumns = (type, auth = {}) => {
  const list = type === 1 ? aidColumns : urlColumns
  return list.filter((item) => {
    // 权限控制
    if (!Array.isArray(item.auth)) {
      return true
    }
    return item.auth.every((it) => auth[it])
  })
}
