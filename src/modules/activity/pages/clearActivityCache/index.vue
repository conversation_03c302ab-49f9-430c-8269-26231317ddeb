<template>
  <div>
    <a-form ref="form">
      <a-form-item :label="$t('29172')">
        <a-select v-model="cacheType" class="input">
          <a-select-option :value="1">{{ $t('global_activity') }}</a-select-option>
          <a-select-option :value="2">{{ $t('global_package') }}</a-select-option>
          <a-select-option :value="3">Sku</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item :label="$t('identification_info_id.name')">
        <a-input
          v-model="activityId"
          class="input"
          :placeholder="$t('global_please_input')"
          :allow-clear="true"
        ></a-input>
        <a-button type="primary" :loading="loading" @click="clearCache">{{ $t('pm_submit') }}</a-button>
      </a-form-item>
    </a-form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activityId: '',
      loading: false,
      cacheType: 1,
      typeOption: []
    }
  },
  methods: {
    async clearCache() {
      const id = this.activityId.trim()
      const cacheType = this.cacheType
      const reg = /\d/
      const id_list = id.split(',')
      if (id_list.length > 50) {
        return this.$message.info('Up to 50 IDs')
      }
      const validate = id_list.every((item) => {
        const test = reg.test(item)
        return test
      })
      if (!validate) {
        return this.$message.info('ID must be an integer')
      }
      const apiUrl = ADMIN_API.act.cache_clear
      this.loading = true
      const result = await ajax.getBody(apiUrl, {
        params: { cache_type: cacheType, id_list: id_list.join(',') }
      })
      if (result.success) {
        this.$message.success(this.$t('global_success'))
      }
      this.loading = false
    }
  }
}
</script>
<style scoped>
.input {
  width: 240px;
  margin-right: 20px;
}
</style>
