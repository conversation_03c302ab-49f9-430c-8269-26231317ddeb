<template>
  <div v-if="text" class="basic-divider">
    <span class="basic-divider-text">{{ text }}</span>
  </div>
</template>

<script>
export default {
  name: 'BasicDivider',
  props: {
    text: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.basic-divider {
  position: relative;
  margin: 16px 0;
  width: 100%;
  border-bottom: 1px solid #e8e8e8;

  &-text {
    position: absolute;
    left: 0;
    bottom: -7px;
    padding: 0 10px 0 0;
    font-size: 12px;
    line-height: 14px;
    color: rgba(0, 0, 0, 0.45);
    background-color: #fff;
    z-index: 2;
  }
}
</style>
