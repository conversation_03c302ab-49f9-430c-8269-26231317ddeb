<template>
  <div>
    <a-popover v-if="showPopover && message" v-bind="$attrs">
      <div slot="content" v-html="message" style="max-width: 300px;" />
      <slot />
    </a-popover>
    <slot v-else />
  </div>
</template>

<script>
export default {
  name: 'BasicFormPopover',
  props: {
    message: {
      type: String,
      default: ''
    },
    showPopover: {
      type: Boolean,
      default: true
    }
  }
}
</script>
