<template>
  <a-checkbox class="basic-overflow-checkbox" :style="{ width: width }" v-bind="$attrs" v-on="$listeners">
    <OverflowTextTooltip class="text-wrap">
      <slot />
    </OverflowTextTooltip>
  </a-checkbox>
</template>

<script>
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'

export default {
  name: 'BasicOverflowCheckbox',
  components: {
    OverflowTextTooltip
  },
  props: {
    width: {
      type: String,
      default: '100%'
    }
  }
}
</script>

<style lang="scss" scoped>
.basic-overflow-checkbox {
  margin: 8px 0;
  .text-wrap {
    display: inline-block;
    vertical-align: middle;
    width: calc(100% - 40px);
  }
}
</style>
