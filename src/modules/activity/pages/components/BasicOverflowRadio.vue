<template>
  <a-radio class="basic-overflow-radio" v-bind="$attrs" :style="{ width: width }">
    <OverflowTextTooltip class="text-wrap">
      <slot />
    </OverflowTextTooltip>
  </a-radio>
</template>

<script>
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'

export default {
  name: 'BasicOverflowRadio',
  components: {
    OverflowTextTooltip
  },
  props: {
    width: {
      type: String,
      default: '100%'
    }
  }
}
</script>

<style lang="scss" scoped>
.basic-overflow-radio {
  margin-left: 0;

  .text-wrap {
    display: inline-block;
    vertical-align: middle;
    width: calc(100% - 40px);
  }
}
</style>
