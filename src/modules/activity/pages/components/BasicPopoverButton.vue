<template>
  <span>
    <a-button v-if="!hasPopover" v-bind="$attrs" v-on="$listeners">
      <slot />
    </a-button>
    <a-popover v-else :placement="popPlacement">
      <template slot="content">
        <span v-if="popMsg">{{ popMsg }}</span>
        <slot v-else name="tips" />
      </template>
      <a-button v-bind="$attrs" v-on="$listeners">
        <slot />
      </a-button>
    </a-popover>
  </span>
</template>

<script>
export default {
  name: 'BasicPopoverButton',
  props: {
    hasPopover: {
      type: Boolean,
      default: false
    },
    popMsg: {
      type: String,
      default: null
    },
    popPlacement: {
      type: String,
      default: 'top'
    }
  }
}
</script>
