<template>
  <a-form-model
    class="basic-price-form"
    :class="{ 'is-horizontal': isHorizontal }"
    ref="form"
    layout="horizontal"
    :labelCol="{ span: isHorizontal ? 8 : 6 }"
    :wrapperCol="{ span: isHorizontal ? 16 : 18 }"
    :model="form"
    :rules="rules"
  >
    <div>
      <a-form-model-item :label="$t('package_price_cost')" prop="costPrice">
        <a-input
          v-model="form.costPrice"
          :suffix="costCurrency"
          :placeholder="$t('global_please_input')"
          @keyup.native="handleRoundPrice('costPrice')"
          @blur="handleRoundPrice('costPrice')"
        />
      </a-form-model-item>
    </div>
    <div>
      <a-form-model-item :label="$t('package_price_retail')" prop="retailPrice">
        <a-input
          v-model="form.retailPrice"
          :suffix="sellingCurrency"
          :placeholder="$t('global_please_input')"
          @keyup.native="handleRoundPrice('retailPrice')"
          @blur="handleRoundPrice('retailPrice')"
        />
      </a-form-model-item>
    </div>
  </a-form-model>
</template>

<script>
import { round2Decimal } from '@activity/utils'

export default {
  name: 'BasicPriceForm',
  props: {
    layout: {
      type: String,
      default: 'horizontal'
    },
    // 格式：costPrice、retailPrice
    form: {
      type: Object,
      required: true
    },
    costCurrency: {
      type: String,
      required: true
    },
    sellingCurrency: {
      type: String,
      required: true
    },
    isHorizontal: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rules: {
        costPrice: [
          {
            required: true,
            message: this.$t('global_please_input'),
            trigger: 'blur'
          }
        ],
        retailPrice: [
          {
            required: true,
            message: this.$t('global_please_input'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    handleRoundPrice(field) {
      this.form[field] = round2Decimal(this.form[field])
    },
    validateForm() {
      return this.$refs.form.validate().catch(() => false)
    },
    clearValidate() {
      this.$refs.form.clearValidate()
    }
  }
}
</script>

<style lang="scss" scoped>
.basic-price-form {
  &.is-horizontal {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
