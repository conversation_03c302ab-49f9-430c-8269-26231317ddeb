<template>
  <a-form-model class="basic-new-take-form" ref="form" layout="horizontal" :model="form" :rules="rules">
    <div v-if="!isMC2BD" class="form-wrapper">
      <div style="width: 50%">
        <a-form-model-item
          :label="$t('package_price_cost')"
          prop="costPrice"
          :labelCol="{ span: 10 }"
          :wrapperCol="{ span: 13 }"
        >
          <a-input
            v-model="form.costPrice"
            :suffix="costCurrency"
            :placeholder="$t('global_please_input')"
            :disabled="isTripMapped"
            @keyup.native="handleRound('costPrice')"
            @blur="handleCostPriceBlur"
          />
        </a-form-model-item>
      </div>
      <div style="width: 50%">
        <a-form-model-item
          :label="$t('package_price_retail')"
          prop="retailPrice"
          :labelCol="{ span: 10 }"
          :wrapperCol="{ span: 13 }"
        >
          <a-input
            v-model="form.retailPrice"
            :suffix="sellingCurrency"
            :placeholder="$t('global_please_input')"
            @keyup.native="handleRound('retailPrice')"
          />
        </a-form-model-item>
      </div>
    </div>
    <div class="form-wrapper">
      <div style="width: 50%">
        <a-form-model-item
          :label="$t('package_price_selling')"
          prop="sellingPrice"
          :labelCol="{ span: 10 }"
          :wrapperCol="{ span: 13 }"
        >
          <a-input
            v-model="form.sellingPrice"
            :suffix="sellingCurrency"
            :placeholder="$t('global_please_input')"
            :disabled="isPriceProtected"
            @keyup.native="handleRound('sellingPrice')"
            @blur="computeTakeRate"
          />
        </a-form-model-item>
      </div>
      <div style="width: 50%">
        <a-form-model-item
          :label="$t('new_take_rate')"
          prop="takeRate"
          :labelCol="{ span: 10 }"
          :wrapperCol="{ span: 13 }"
        >
          <a-input
            v-model="form.takeRate"
            suffix="%"
            :placeholder="$t('global_please_input')"
            :disabled="isPriceProtected"
            @keyup.native="handleRoundTakeRate"
            @blur="handleTakeRateBlur"
          />
        </a-form-model-item>
      </div>
    </div>
    <a-form-model-item
      :label="$t('package_custom_selling')"
      prop="specialSellingPriceList"
      :labelCol="{ span: 5 }"
      :wrapperCol="{ span: 19 }"
    >
      <SpecialSellingPriceList
        ref="ssp"
        :price="form.sellingPrice"
        :selling-currency="sellingCurrency"
        :special-selling-price-list="form.specialSellingPriceList"
        :disabled="isPriceProtected"
      />
    </a-form-model-item>
    <a-form-model-item
      v-if="formKeys.includes('reason')"
      :label="$t('reason_for_price_changing')"
      prop="reason"
      :labelCol="{ span: 5 }"
      :wrapperCol="{ span: 19 }"
    >
      <a-select v-model="form.reason" @change="handleReasonSelect">
        <a-select-option v-for="reason in priceChangeReasonList" :key="reason.value" :value="reason.value">
          {{ reason.text }}
        </a-select-option>
      </a-select>
    </a-form-model-item>
    <a-form-model-item
      v-if="form.reason === 0 && formKeys.includes('reasonText')"
      label=" "
      prop="reasonText"
      :colon="false"
      :labelCol="{ span: 5 }"
      :wrapperCol="{ span: 19 }"
    >
      <a-textarea
        id="js-reason-text"
        v-model="form.reasonText"
        :placeholder="$t('global_please_input')"
        :rows="4"
        :max-length="100"
      />
    </a-form-model-item>
  </a-form-model>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import {
  round2Decimal,
  provingTakeRate,
  computeTR,
  setTRAndGetBase,
  setCostAndGetBase
} from '@activity/utils'
import SpecialSellingPriceList from '@activity/pages/components/SpecialSellingPriceList.vue'
import { priceChangeReasonList } from '@activity/pages/package/package_const.js'

export default {
  name: 'BasicTakeRateForm',
  components: {
    SpecialSellingPriceList
  },
  props: {
    // sellingPrice、costPrice、retailPrice、takeRate、specialSellingPriceList
    form: {
      type: Object,
      required: true
    },
    sellingCurrency: {
      type: String,
      required: true
    },
    costCurrency: {
      type: String,
      required: true
    },
    exchangeRate: {
      type: [String, Number],
      default: ''
    },
    isPriceProtected: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validateSpecialSellingPriceList = (rule, value, callback) => {
      const isValid = value.every((item) => !!item.price)

      if (isValid) {
        callback()
      } else {
        callback(new Error(this.$t('global_please_input')))
      }
    }

    return {
      priceChangeReasonList,
      defaultSpecialSellingPrice: {
        price: '',
        currency: ''
      },
      rules: {
        costPrice: [
          {
            required: true,
            message: this.$t('global_please_input'),
            trigger: 'blur'
          }
        ],
        retailPrice: [
          {
            required: true,
            message: this.$t('global_please_input'),
            trigger: 'blur'
          }
        ],
        sellingPrice: [
          {
            required: true,
            message: this.$t('global_please_input'),
            trigger: 'blur'
          }
        ],
        takeRate: [
          {
            required: true,
            message: this.$t('global_please_input'),
            trigger: 'blur'
          }
        ],
        specialSellingPriceList: [
          {
            required: true,
            validator: validateSpecialSellingPriceList,
            trigger: 'blur'
          }
        ],
        reason: [
          {
            required: true,
            message: this.$t('global_please_select'),
            trigger: 'blur'
          }
        ],
        reasonText: [
          {
            required: true,
            message: this.$t('global_please_input'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    ...mapState({
      isMC2BD: (state) => state.isMC2BD
    }),
    ...mapGetters(['isTripMapped']),
    formKeys() {
      return Object.keys(this.form)
    }
  },
  methods: {
    handleRound(field) {
      this.form[field] = round2Decimal(this.form[field])
    },
    handleRoundTakeRate() {
      this.form.takeRate = provingTakeRate(this.form.takeRate)
    },
    computeTakeRate() {
      this.form.takeRate = computeTR(this.form.costPrice, this.form.sellingPrice, this.exchangeRate)
    },
    handleTakeRateBlur() {
      const res = setTRAndGetBase(
        this.form.costPrice,
        this.form.sellingPrice,
        this.form.takeRate,
        this.exchangeRate
      )

      this.form.sellingPrice = res.base
      this.form.takeRate = res.take_rate
    },
    handleCostPriceBlur() {
      const res = setCostAndGetBase(
        this.form.costPrice,
        this.form.sellingPrice,
        this.form.takeRate,
        this.exchangeRate
      )

      this.form.costPrice = res.cost
      this.form.sellingPrice = res.base
      this.form.takeRate = res.take_rate
    },
    validateForm() {
      return this.$refs.form.validate().catch(() => false)
    },
    clearValidate() {
      this.$refs.form.clearValidate()
    },
    getSpecialSellingPriceData() {
      return this.$refs.ssp.getSspData()
    },
    getSpecialSellingPriceDataWithoutCache() {
      return this.$refs.ssp.getSspData()
    },
    handleReasonSelect() {
      if (this.form.reason === 0) {
        this.$nextTick(() => {
          const reasonTextDom = document.querySelector('#js-reason-text')
          reasonTextDom && reasonTextDom.scrollIntoView()
        })
      }
    }
  }
}
</script>

<style lang="scss">
.basic-new-take-form {
  .ant-form-item-label > label {
    padding-right: 8px;
    white-space: break-spaces;
    display: inline-block;
    line-height: normal;
  }
}
</style>

<style lang="scss" scoped>
.basic-new-take-form {
  .add-btn-wrap {
    .ant-btn {
      padding: 0;
    }
  }

  .special-selling-price-wrap {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .special-selling-price-item {
    width: 260px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .form-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
}
</style>
