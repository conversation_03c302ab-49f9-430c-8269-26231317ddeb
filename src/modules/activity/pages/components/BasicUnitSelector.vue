<template>
  <a-select
    v-bind="$attrs"
    v-on="$listeners"
    show-search
    style="width: 300px"
    :placeholder="$t('global_please_select')"
    :filter-option="unitFilterOption"
    @change="handleChange"
  >
    <a-select-option
      v-for="unit in unitList"
      :key="unit.sku_id"
      :value="unit.sku_id"
      :class="{ 'is-unpublished': shadowUnpublished && !unit.published }"
    >
      {{ getUnitName(unit) }}
    </a-select-option>
  </a-select>
</template>

<script>
import { mapGetters } from 'vuex'
import { bestMatchLang } from '@activity/utils'

export default {
  name: 'BasicUnitSelector',
  props: {
    shadowUnpublished: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters(['unitList', 'mainUnit'])
  },
  methods: {
    handleChange(skuId) {
      this.$emit('input', skuId)
    },
    getUnitName(unit) {
      const unitName = bestMatchLang('unit_name', 'language', unit.local)
      const mainSkuText =
        this.mainUnit && unit.sku_id === this.mainUnit.sku_id
          ? `(${this.$t('package_unit_main')})`
          : ''
      return `${unitName}${mainSkuText}`
    },
    unitFilterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      )
    }
  }
}
</script>
<style lang="scss">
.ant-select-dropdown-menu-item.is-unpublished {
  color: rgba(0, 0, 0, 0.45);
  // background-color: rgba(0, 0, 0, 0.04);
}
</style>
