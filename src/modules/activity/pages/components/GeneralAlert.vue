<template>
  <a-alert class="general-alert common-alert-style" v-bind="$attrs" v-on="$listeners">
    <slot slot="description" />
  </a-alert>
</template>

<script>
export default {
  name: 'GeneralAlert'
}
</script>

<style lang="scss">
.general-alert {
  &.ant-alert-with-description {
    padding: 8px 15px 8px 35px;

    &.ant-alert-no-icon {
      padding: 8px 15px;
    }

    .ant-alert-icon {
      top: 14px;
      left: 14px;
      font-size: 12px;
      line-height: 14px;
    }
  }

  .ant-alert-message {
    margin-bottom: 0;
    color: #ffab00;
    font-size: 14px;
    line-height: 16px;
  }

  .ant-alert-description {
    font-size: 12px;
    line-height: 14px;
  }

  .ant-alert-description {
    color: #ffab00;
  }

  .ant-alert-description {
    color: #ffab00;
  }
}
</style>
