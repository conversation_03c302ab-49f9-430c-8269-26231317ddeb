<template>
  <div class="number-range-container">
    <a-select v-model="form.min" :disabled="$attrs.disabled" @change="handleChange">
      <a-select-option v-for="type in minimumOpts" :key="type.value" :value="type.value">
        {{ type.label }}
      </a-select-option>
    </a-select>

    <label class="rung"> - </label>

    <a-select v-model="form.max" :disabled="$attrs.disabled" @change="handleChange">
      <a-select-option v-for="type in maximumOpts" :key="type.value" :value="type.value">
        {{ type.label }}
      </a-select-option>
    </a-select>
  </div>
</template>

<script>
export default {
  name: 'NumberRange',
  props: {
    items: {
      type: Array,
      required: true
    },
    value: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      form: {
        min: '',
        max: ''
      },

      minimumOpts: [],
      maximumOpts: []
    }
  },
  watch: {
    items: {
      immediate: true,
      deep: true,
      handler(items) {
        this.minimumOpts = this.numberRangeOpts(items[0].options)
        this.maximumOpts = this.numberRangeOpts(items[1].options)

        if (this.value) {
          this.$set(this, 'form', {
            min: _.isNumber(this.value.min)
              ? this.value.min
              : _.isNumber(items[0].default)
              ? items[0].default
              : 1,
            max: _.isNumber(this.value.max)
              ? this.value.max
              : _.isNumber(items[1].default)
              ? items[1].default
              : 100
          })
          this.$emit('update:value', this.form)
        }
      }
    },
    form: {
      deep: true,
      handler() {
        this.$emit('update:value', this.form)
      }
    }
  },
  methods: {
    numberRangeOpts({ min, max }) {
      return Array(max - min + 1)
        .fill()
        .map((item, index) => ({
          label: String(index + min),
          value: index + min
        }))
    },
    handleChange() {
      this.$emit('change')
    }
  }
}
</script>

<style scoped lang="scss">
.number-range-container {
  display: flex;
  align-items: center;
  // /deep/ .el-select {
  //   width: 100px;
  // }
  .rung {
    display: inline-block;
    margin: 0 10px;
  }
}
</style>
