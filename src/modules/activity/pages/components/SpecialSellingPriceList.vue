<template>
  <div class="special-selling-price-list">
    <div class="special-selling-price-wrap">
      <template v-for="(item, index) in specialSellingPriceList">
        <div
          :class="classMap[item.pricing_rule_type]"
          class="special-selling-price-box"
          :key="`ssp-price-${index}-${item.id}`"
        >
          <a-form-model :model="item" ref="form" :rules="rules">
            <div class="ssp-type-box">
              <a-form-model-item prop="pricing_rule_type">
                <a-radio-group v-model="item.pricing_rule_type" @change="changeSspType(item)">
                  <a-radio
                    :disabled="!hasFixedAuth"
                    :title="!hasFixedAuth ? $t('no_permission') : ''"
                    :value="0"
                    >{{ $t('30133') }}</a-radio
                  >
                  <a-radio :value="1">{{ $t('30134') }}</a-radio>
                </a-radio-group>
              </a-form-model-item>
            </div>
            <div
              :key="`${index}-${item.id}`"
              :data="`${index}-${item.id}`"
              class="special-selling-price-item"
            >
              <a-form-model-item class="ssp-width" prop="currency">
                <a-select
                  show-search
                  v-model="item.currency"
                  :filter-option="filterOption"
                  :disabled="!!item.currency || disabled || getFixedDisabled(item)"
                  @change="changeSellingCurrency(item)"
                >
                  <a-select-option
                    v-for="currency in sortedCurrencyList"
                    :key="currency"
                    :value="currency"
                    :disabled="selectedCurrencyList.includes(currency)"
                  >
                    {{ currency }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
              <a-form-model-item class="ssp-width price-style" prop="price">
                <a-input
                  v-model="item.price"
                  :placeholder="item.pricing_rule_type === 1 ? 'null' : $t('global_please_input')"
                  :disabled="disabled || item.pricing_rule_type === 1 || getFixedDisabled(item)"
                  @keyup.native="handleRoundSpecialSellingPrice(item)"
                >
                </a-input>
              </a-form-model-item>
              <a-form-model-item v-if="item.pricing_rule_type === 1" class="ssp-width" prop="percent">
                <a-input
                  v-model="item.percent"
                  @change="checkRateVal($event, item)"
                  :placeholder="$t('enter')"
                >
                  <template slot="suffix">%</template>
                </a-input>
              </a-form-model-item>
              <a-form-model-item class="flex-auto">
                <UtcDatetime ref="utc" :model="item" :disabled="getFixedDisabled(item)"></UtcDatetime>
              </a-form-model-item>
              <a-form-model-item>
                <span class="btn-delete">
                  <CircleDeleteButton
                    style="margin-left: 8px;"
                    :disabled="disabled || getFixedDisabled(item)"
                    @click="handleDeleteSpecialSellingPrice(index, item)"
                  />
                </span>
              </a-form-model-item>
            </div>
          </a-form-model>
        </div>
      </template>
    </div>
    <div class="add-btn-wrap">
      <a-button type="link" icon="plus" :disabled="disabled" @click="handleAddSpecialSellingPrice">
        {{ $t('global_add') }}
      </a-button>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import { round2Decimal } from '@activity/utils'
import { SUPPORT_CURRENCY_SYMBO } from '@activity/pages/package/package_const.js'
import CircleDeleteButton from '@activity/components/CircleDeleteButton.vue'
import UtcDatetime from '@activity/components/UtcDatetime'
import { checkAuth } from '@/plugins/authDirective'
import actMixins from '@activity/mixins/index.js'
const defRules = [
  {
    required: true,
    trigger: ['blur', 'change']
  }
]
export default {
  name: 'SpecialSellingPriceList',
  mixins: [actMixins.sspPriceMixin],
  components: {
    UtcDatetime,
    CircleDeleteButton
  },
  props: {
    price: {
      type: [Number, String],
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    },
    sellingCurrency: {
      type: String,
      required: true
    },
    specialSellingPriceList: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      hasFixedAuth: checkAuth('sspFixed'),
      classMap: {
        0: 'ssp-fixed-style',
        1: 'ssp-float-style'
      },
      rules: {
        pricing_rule_type: defRules,
        currency: {
          validator: (rule, value, callback) => {
            if (!value) return false
            let arr = this.specialSellingPriceList.filter((o) => o.currency === value)
            return arr.length === 1
          },
          required: true,
          trigger: 'change'
        },
        percent: defRules,
        price: defRules,
        invalid_time_zone: defRules,
        invalid_time_front: { required: true, trigger: 'change' }
      },
      cachedSpecialSellingPriceList: [],
      defaultSpecialSellingPrice: {
        isNewFlag: true,
        id: 0,
        price: '',
        currency: '',
        pricing_rule_type: 1,
        invalid_time_zone: 8 // 默认utc +8
      }
    }
  },
  computed: {
    sortedCurrencyList() {
      return SUPPORT_CURRENCY_SYMBO.map((item) => item[0]).sort()
    },
    selectedCurrencyList() {
      return this.specialSellingPriceList.map((item) => item.currency)
    }
  },
  watch: {
    sellingCurrency: {
      handler(currency) {
        this.updateSspData()
      },
      immediate: true
    },
    price(v) {
      this.updateSspData()
    },
    specialSellingPriceList: {
      immediate: true,
      deep: true,
      handler() {
        this.cachedSpecialSellingPriceList = _.cloneDeep(this.specialSellingPriceList)
      }
    }
  },
  methods: {
    ...mapActions(['getExchangeRate2actions']),
    getCurrencySymbol: klook.getCurrencySymbol,
    updateSspData: _.debounce(async function() {
      let options = {
        price: this.price,
        sellingCurrency: this.sellingCurrency
      }
      let arr = this.specialSellingPriceList
      if (!arr || !arr.length) return
      await this.bindSspList2mixin(arr, options)
    }, 300),
    getFixedDisabled(item) {
      // 没有权限、固定价格
      return !this.hasFixedAuth && item.pricing_rule_type === 0
    },
    changeSspType(item) {
      this.$set(item, 'percent', undefined)
      this.$set(item, 'price', undefined)
      this.checkShowTips(item)
    },
    checkShowTips({ pricing_rule_type, isNewFlag }, type = 'change') {
      let typeMap = {
        change: 1,
        delete: 0
      }
      if (pricing_rule_type === typeMap[type] && !isNewFlag) {
        this.$message.info(__('31139'))
      }
    },
    checkRateVal: _.debounce(function(e, item) {
      let val = item.percent
      let fmtStr = klook.fmtAmountPrecision(val)
      let percent = fmtStr >= 0 ? fmtStr : parseFloat(val) || undefined
      this.$set(item, 'percent', percent > 0 ? parseFloat(percent) : undefined)
      this.updateSspData()
    }, 300),
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    handleDeleteSpecialSellingPrice(index, item) {
      this.specialSellingPriceList.splice(index, 1)
      this.checkShowTips(item, 'delete')
    },
    handleRoundSpecialSellingPrice(item) {
      item.price = round2Decimal(item.price)
    },
    handleAddSpecialSellingPrice() {
      const item = _.cloneDeep(this.defaultSpecialSellingPrice)
      this.specialSellingPriceList.push(item) //新增
    },
    changeSellingCurrency(item) {
      this.updateSspData()
    },
    async validate() {
      let forms = Array.from(this.$refs.form || [])
      let resp = await Promise.all(forms.map(async (o) => await o.validate()))
      return resp.every((o) => o)
    },
    getSspData() {
      return this.specialSellingPriceList.map((item) => ({
        id: item.id,
        expire_soon: item.expire_soon,
        currency: item.currency,
        price: Number(item.price),
        percent: Number(item.percent || 0),
        invalid_time_zone: item.invalid_time_zone,
        invalid_time_front: item.invalid_time_front,
        pricing_rule_type: item.pricing_rule_type
      }))
    }
  }
}
</script>

<style lang="scss" scoped>
.special-selling-price-list {
  .ssp-width {
    width: 100px;
    min-width: 100px;
    margin: 0 12px 0 0;
    &.price-style {
      width: 160px;
      min-width: 160px;
    }
  }
  .ssp-float-style {
    .ssp-width {
    }
  }
  .special-selling-price-box {
    width: 100%;
    padding: 0px 12px 0 12px;
    background-color: #fafafa;
    margin: 12px 0 0 0;
    &:first-child {
      margin: 0;
    }
  }

  .special-selling-price-item {
    display: flex;
    width: 100%;
    margin-bottom: 12px;
    align-items: top;
    justify-content: flex-start;
  }

  .add-btn-wrap {
    .ant-btn {
      padding: 0;
    }
  }
  .btn-delete {
    display: inline-block;
    height: 100%;
  }
}
</style>
