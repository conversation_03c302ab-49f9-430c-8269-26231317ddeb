<template>
  <div class="ssp-block" :class="styleType">
    <div class="ssp-block-box">
      <span v-if="showBtnSsp" class="btn-ssp-open" @click="clickBtnSspOpen">
        <slot name="btn-ssp">
          <span class="ssp-link">{{ $t('28682') }}</span>
          <a-icon type="double-right" />
        </slot>
      </span>
      <div class="ssp-row-wrap" :style="sspStyle">
        <ul class="ssp-row-box">
          <li
            v-for="(item, index) in sspList"
            :key="`${index}-${item.id}`"
            :class="[bgMaps[item.pricing_rule_type]]"
            class="ssp-row-line"
          >
            <span v-if="item.pricing_rule_type === 1" class="percent"
              >{{ item.percent || 0 }}%{{ styleType == 'style-cell' ? ' -' : '' }}</span
            >
            <span class="currency">{{ item.currency }}</span>
            <span class="price fw-bold common-ellipsis-style">{{ item.price }}</span>
            <template v-if="!hideIcon && showIconInfo">
              <a-tooltip overlayClassName="common-tooltip-style" :title="getTips(item)">
                <span :class="[item.expire_soon && 'icon-is-expire']" class="icon-def"
                  ><a-icon theme="filled" type="info-circle"
                /></span>
              </a-tooltip>
            </template>
            <template v-else-if="!hideIcon">
              <span>
                {{ getTips(item) }}
                <span :class="[item.expire_soon && 'icon-is-expire']" class="icon-def">
                  <a-icon theme="filled" type="info-circle" />
                </span>
              </span>
            </template>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    sspStyle: {
      type: [String, Object],
      default: ''
    },
    hideIcon: {
      type: Boolean,
      default: false
    },
    showIconInfo: {
      type: Boolean,
      default: true
    },
    styleType: {
      type: String,
      default: 'style-ssp' // 'style-ssp' | 'style-cell'
    },
    showBtnSsp: {
      type: Boolean,
      default: true
    },
    sspList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      bgMaps: {
        0: 'bg-fixed',
        1: 'bg-float'
      }
    }
  },
  watch: {},
  methods: {
    getCurrencySymbol: klook.getCurrencySymbol,
    getTips(item) {
      // if (item.invalid_time_zone == 0) {
      //   return `${item.invalid_time_front} (GTM)`
      // }
      let prefix = item.invalid_time_zone > 0 ? '+' : ''
      let str = `(GTM ${prefix}${item.invalid_time_zone})`
      return `${item.invalid_time_front} ${str}`
    },
    clickBtnSspOpen() {
      this.$emit('click')
    }
  }
}
</script>

<style lang="scss" scoped>
.ssp-block {
  .ssp-block-box {
    line-height: 0;
    .btn-ssp-open {
      display: inline-block;
      margin-bottom: 5px;
      font-size: 12px;
      line-height: 14px;
      color: #0091ff;
      cursor: pointer;
      .ssp-link {
        font-weight: bold;
        margin-right: 6px;
      }
    }
  }
  .ssp-row-box {
    display: inline-block;
    font-size: 12px;
    line-height: 14px;
    color: #000;
    width: 100%;
    min-width: 184px;
    overflow: hidden;
    .ssp-row-line {
      display: flex;
      border-radius: 2px;
      padding: 2px 6px;
      margin-bottom: 12px;
      &:last-child {
        margin-bottom: 0;
      }
      span {
        vertical-align: middle;
        margin-right: 8px;
        &.icon-def {
          margin: 0;
          &.icon-is-expire {
            color: #ff5630;
          }
        }
      }
      .price {
        flex: 1 1 auto;
      }
    }
    .icon-def {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.25);
    }
  }
}

.ssp-block {
  &.style-ssp {
    .ssp-row-box {
      text-align: left;
      .ssp-row-line {
        &.bg-fixed {
          background-color: rgba(255, 171, 0, 0.1);
        }
        &.bg-float {
          background-color: rgba(0, 145, 255, 0.05);
        }
      }
    }
  }
  &.style-cell {
    .ssp-row-box {
      text-align: right;
      .ssp-row-line {
        margin-bottom: 0;
        display: block;
      }
    }
  }
}
</style>
