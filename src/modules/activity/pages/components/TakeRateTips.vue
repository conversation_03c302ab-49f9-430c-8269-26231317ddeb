<template>
  <GeneralAlert banner :show-icon="false" style="margin-bottom: 10px;">
    <span>
      <span style="color: #000;">{{ $t('package_minimum_take_rate') }}</span>
      <span style="color: #36B37E;"> {{ formatTakeRate(overrideTakeRate) }}% </span>
      <span style="color: #000; margin-left: 50px;">
        {{ $t('package_existing_take_rate') }}
      </span>
      <span style="color: #FFAB00;"> {{ formatTakeRate(lastTakeRate) }}% </span>
    </span>
  </GeneralAlert>
</template>

<script>
import { provingTakeRate } from '@activity/utils'
import GeneralAlert from '@activity/pages/components/GeneralAlert.vue'

export default {
  name: 'TakeRateTips',
  components: {
    GeneralAlert
  },
  props: {
    overrideTakeRate: {
      type: String,
      default: '0'
    },
    lastTakeRate: {
      type: String,
      default: '0'
    }
  },
  methods: {
    formatTakeRate(takeRate) {
      return provingTakeRate(takeRate)
    }
  }
}
</script>

<style lang="scss" scoped></style>
