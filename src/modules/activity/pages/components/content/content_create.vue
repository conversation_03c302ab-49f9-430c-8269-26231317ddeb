<template>
  <div class="seo-content" :data-spm-page="getSpm">
    <div class="seo-content-box">
      <div class="common-basic-box">
        <h2>{{ $t('edit_activity_content') }}</h2>
        <!-- {{seoObj.list}} -->
        <a-form-model
          ref="seoForm"
          :model="form"
          :rules="rules"
          class="common-basic-form-style"
          :colon="false"
        >
          <template v-for="(item, i) in seoObj.list">
            <a-form-model-item
              v-if="!item.isHide"
              :key="item.key + i"
              :prop="item.key"
              :label="item.label"
              :class="item.label ? '' : 'common-form-sub-style'"
              :rules="item.langKey === ref ? [] : undefined"
            >
              <div v-if="showSystemAutoGenerateTip && item.systemAutoGenerateTip" class="form-item-tip">
                {{ item.systemAutoGenerateTip }}
              </div>
              <template v-if="item.typeKey === 'markdown'">
                <SimpleMde
                  :ref="`markdown-ref${i}`"
                  v-model="item.value"
                  :disabled="item.disabled"
                  :title="item.disabled ? $t('no_permission') : ''"
                  :lang_text="item.lang_text"
                  :config="{
                    placeholder: item.placeholder || $t('global_please_input'),
                    extension_ability
                  }"
                  @change="changeValue"
                />
              </template>
              <template v-else>
                <a-input
                  ref="input"
                  v-model="item.value"
                  :placeholder="item.placeholder || $t('global_please_input')"
                  :disabled="item.disabled"
                  @change="changeValue"
                ></a-input>
              </template>
            </a-form-model-item>
          </template>
        </a-form-model>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
const lang_conf = require('lang_conf')
import SimpleMde from '@activity/components/obt-markdown/index.vue'
import { newMarkdownDefaultFormatValue } from '@activity/components/simple-md/utils/index.js'

export default {
  components: {
    SimpleMde
  },
  props: {
    actGlobal: {
      type: Object,
      default() {
        return {
          categoryInfo: {}
        }
      }
    }
  },
  computed: {
    ...mapGetters(['getActStepStatus']),
    currStepStatus() {
      return this.getActStepStatus?.activity_content ?? false
    },
    rules() {
      let arr = ['seo_url', 'seo_keywords', 'seo_overview', 'seo_title', 'seo_meta_desc']
      let obj = {}
      arr.forEach((k) => {
        const index = this.seoObj.list.findIndex((item) => item.key === k)
        const curr = this.seoObj.list[index]
        const required = !curr?.disabled

        if (k === 'seo_url') {
          obj[k] = [
            {
              required,
              pattern: /^[a-z\d-]+$/,
              message: __('seo_url_format_tip'),
              trigger: 'change'
            },
            { max: 100, message: 'max length 100', trigger: 'change' }
          ]
        } else if (k === 'seo_overview') {
          obj[k] = [{ max: 1000, message: 'max length 1000', trigger: 'change' }]
        } else if (['seo_keywords', 'seo_meta_desc'].includes(k)) {
          obj[k] = [
            {
              trigger: ['change', 'blur'],
              validator: (rule, value, callback) => {
                if (!(this.$refs[`markdown-ref${index}`]?.[0]?.validator?.() ?? true)) {
                  return callback(new Error(__('110654')))
                }
                callback()
              }
            }
          ]
        } else {
          obj[k] = [
            {
              required,
              message: __('global_please_input'),
              trigger: ['change', 'blur']
            }
          ]
        }
      })
      return obj
    },
    lang() {
      return klook.getEditLang()
    },
    ref() {
      return this.languages[0] ? this.languages[1] : this.$route.query.ref
    },
    activity_id() {
      return parseInt(this.$route.params.id) || 0
    },
    activityStatus() {
      return this.actGlobal.categoryInfo.activity_status
    },
    activityLangStatus() {
      return this.actGlobal.categoryInfo?.act_sub_category_info?.lang_ever_published
    },
    getSpm() {
      const oid = `activity_${this.activity_id || 0}`
      return `SEOInfo?oid=${oid}`
    },
    vertical() {
      return this.actGlobal.categoryInfo?.act_sub_category_info?.vertical || ''
    }
  },
  data() {
    this.extension_ability = [
      {
        key: 'markdown_support',
        value: {
          format: newMarkdownDefaultFormatValue
        }
      }
    ]

    return {
      showSystemAutoGenerateTip: false,
      languages: [],
      isSPU: this.$root.isSPU,
      form: {
        seo_url: '',
        // seo_top_keywords: '',
        seo_keywords: '',
        seo_overview: '',
        seo_title: '',
        seo_meta_desc: ''
      },
      seoObj: {
        refList: [
          {
            key: 'activity_title',
            label: __('act_activity_title'),
            disabled: true
          },
          // {
          //   key: 'activity_subtitle',
          //   label: __('act_activity_subtitle'),
          //   disabled: true
          // },
          // {
          //   key: 'activity_desc',
          //   label: __('act_short_description'),
          //   disabled: true
          // },
          // {
          //   key: 'seo_top_keywords',
          //   label: __('act_seo_top_keywords'),
          //   placeholder: this.initPlaceholder('act_seo_top_keywords_hint', true)
          // },
          {
            key: 'seo_keywords',
            label: __('act_seo_keywords'),
            placeholder: this.initPlaceholder('act_seo_keywords_hint', true),
            typeKey: 'markdown',
            lang_text: ''
          },
          {
            key: 'seo_overview',
            label: __('107096'),
            typeKey: 'markdown',
            placeholder: 'Please enter overview'
          },
          {
            key: 'seo_title',
            label: __('act_seo_title'),
            placeholder: this.initPlaceholder('act_seo_title_hint', true)
          },
          {
            key: 'seo_meta_desc',
            label: __('act_seo_meta_description'),
            typeKey: 'markdown',
            lang_text: ''
          }
        ],
        list: [
          {
            key: 'note_content',
            label: __('act_product_notes'),
            disabled: true,
            isHide: true
          },
          {
            key: 'activity_title',
            label: __('act_activity_title'),
            disabled: false
          },
          // {
          //   key: 'activity_subtitle',
          //   label: __('act_activity_subtitle'),
          //   disabled: true
          // },
          // {
          //   key: 'activity_desc',
          //   label: __('act_short_description'),
          //   disabled: true
          // },
          {
            key: 'seo_url',
            label: __('act_seo_url'),
            placeholder: __('content_act_seo_url'),
            systemAutoGenerateTip: __('122698')
          },
          // {
          //   key: 'seo_top_keywords',
          //   label: __('act_seo_top_keywords'),
          //   placeholder: this.initPlaceholder('act_seo_top_keywords_hint')
          // },
          {
            key: 'seo_keywords',
            label: __('200594'),
            placeholder: this.initPlaceholder('act_seo_keywords_hint'),
            typeKey: 'markdown',
            lang_text: ''
          },
          {
            key: 'seo_overview',
            label: __('107096'),
            typeKey: 'markdown',
            placeholder: 'Please enter overview'
          },
          {
            key: 'seo_title',
            label: __('act_seo_title'),
            placeholder: this.initPlaceholder('act_seo_title_hint'),
            systemAutoGenerateTip: __('122700')
          },
          {
            key: 'seo_meta_desc',
            label: __('act_seo_meta_description'),
            typeKey: 'markdown',
            lang_text: '',
            systemAutoGenerateTip: __('122700')
          }
        ]
      }
    }
  },
  watch: {
    // 监听全局数据请求完成
    'actGlobal.categoryInfo': {
      deep: true,
      handler() {
        this.handleCheckEditPermission()
      }
    }
  },
  async mounted() {
    klook.bus.$emit('addHandleSave2bus', this.save)
    klook.bus.$emit('floorTimeline2bus', { floorData: {} })

    await this.initData()

    if (!this.currStepStatus) {
      this.showSystemAutoGenerateTip = true
      this.save({ autoSubmitToView: false, isSucMsg: false })
    }
  },
  methods: {
    ...mapActions(['updateActStepStatus2action']),
    async initData() {
      let { activity_id, lang, ref } = this
      let reqData = await ajax.get({
        url: ADMIN_API.act.get_seo_fields,
        params: {
          activity_id,
          language: lang,
          refer_language: ref
        }
      })
      this.statusAll = reqData.language_status.status
      this.languages = klook.initFilterLan(reqData.edit_language.language) || reqData.edit_language.language
      this.initRefLang()
      this.setValue('note_content', reqData.text) //特殊字段：运营信息未填备注则不展示
      this.getValue(reqData.content)
      this.changeValue()
    },
    async getValue(result) {
      this.contentResp = result
      let content = result.content
      this.setValue('seo_url', result.seo_url)

      _.forEach(content, (ele, index) => {
        for (let i in ele) {
          this.setValue(i, ele[i], 'value', ele.language_type)
        }
      })

      // 权限控制
      this.handleCheckEditPermission()
    },
    async updateLangStatus() {
      if (this.isBDpage) {
        return
      }
      let edit_lang = klook.getEditLang()
      let need_update = this.statusAll.find((item) => item.language == edit_lang && parseInt(item.status) < 2)
      if (need_update) {
        await ajax.post({
          url: `/prosrv/activities/${this.activity_id}/${edit_lang}/status/update`,
          data: {
            status: 2
          }
        })
      }
    },
    formatPostData() {
      let { form, lang, activity_id } = this
      let data = {
        activity_id,
        seo_url: form.seo_url,
        language_type: lang,
        // seo_top_keywords: form.seo_top_keywords,
        seo_keywords: form.seo_keywords,
        seo_overview: form.seo_overview,
        seo_title: form.seo_title,
        seo_meta_desc: form.seo_meta_desc
      }
      return data
    },
    async save({ autoSubmitToView = true, isSucMsg = true } = {}) {
      if (!(await this.doTest())) {
        this.$message.warn(__('package_error_mandatory'))
        this.$nextTick(() => {
          klook.scrollElError()
        })
        return
      }

      let data = this.formatPostData()
      let result = await ajax.postBody(
        {
          url: ADMIN_API.act.update_seo_fields,
          data
        },
        {
          loading: true,
          msgOpt: {
            isSucMsg // 保存成功是否提示
          }
        }
      )

      if (!result?.success) {
        return
      }

      await this.updateLangStatus()
      if (!this.currStepStatus) {
        await this.updateActStepStatus2action({
          activity_id: this.activity_id,
          status: 1,
          step: 'activity_content',
          language: new URLSearchParams(location.search).get('lang') || this.published_language
        })
      }
      // $emit initQueryData2bus -> dispatch updateActItemData2actions -> query /activity_service/get_floating_fields
      klook.bus.$emit('initQueryData2bus') //状态流转 submit to preview
      autoSubmitToView &&
        klook.bus.$emit('updateActStatusByEventBus', {
          key: 'submit_to_preview',
          status: 4,
          text: __('submit_to_preview'),
          type: 'primary',
          authTip: __('122701')
        })
      return true
    },
    changeValue() {
      let { lang } = this
      let { form, seoObj } = this
      seoObj.list.forEach((item) => {
        if (item.langKey === lang) {
          this.$set(form, item.key, item.value)
        }
      })
    },
    setValue(key, value, myKey = 'value', language_type) {
      let lang = language_type || this.lang
      let arr = this.seoObj.list.filter((o) => {
        return o.key === key && lang === o.langKey
      })
      if (!arr.length) return
      arr.forEach((obj) => {
        switch (key) {
          case 'note_content':
            this.$set(obj, myKey, value)
            this.$set(obj, 'isHide', !value)
            break
          default:
            this.$set(obj, myKey, value)
        }
      })
    },
    initRefLang() {
      let { lang, ref } = this
      let { refList, list } = this.seoObj
      let text = lang_conf.getLangObj('B_LANG', 'LANG_TITLE')[lang]
      let refText = lang_conf.getLangObj('B_LANG', 'LANG_TITLE')[ref]
      list.forEach((o) => {
        this.$set(o, 'langKey', lang) //第一步初始化langKey，保证this.setValue可正确匹配
      })
      if (lang === ref) {
        let mapKeys = refList.map((o) => o.key)
        list.forEach((o) => {
          mapKeys.includes(o.key) && this.$set(o, 'disabled', true)
        })
      }
      ref &&
        refList.forEach((item) => {
          this.$set(item, 'langKey', ref)
          this.$set(item, 'disabled', true)
          this.$set(item, 'label', '')
          if (item.typeKey === 'markdown') {
            this.setValue(item.key, text, 'lang_text') //list源数据
            // 设置refList数据
            this.$set(item, 'lang_text', refText)
          }
          let index = list.findIndex((o) => o.key === item.key)
          list.splice(index + 1, 0, item) // 插入list源数据
        })
    },
    initPlaceholder(langKey, ref) {
      let langStr = this.$t(langKey)
      let lang = ref ? this.ref : this.lang
      let str = `${langStr} (${klook.getFlang2Blang(lang)})`
      return str
    },
    async doTest() {
      return new Promise((resolve) => {
        this.$refs.seoForm.validate(resolve)
      })
    },

    /**
     * 编辑权限控制
     */
    handleCheckEditPermission() {
      // 没有获取到业务线字段暂不处理
      if (!this.vertical) {
        return
      }

      const checkVertical = ['attractions', 'mobility']
      const permissionCode = 'experience/act/activity_seo_master'

      const isEM = this.$root.roles.is_em
      const isSEO = klook.checkPermission(permissionCode)
      const isVertical = checkVertical.includes(this.vertical)
      // 活动语言状态：0 ｜ 1
      // 代表未发布过该语言，1 代表已发布过该语言
      const isPrePublish = this.activityLangStatus === 0

      console.log('-------- 权限条件 --------')
      console.log('vertical: ', this.vertical)
      console.log('isEM: ', isEM)
      console.log('isSEO: ', isSEO)
      console.log('isVertical: ', isVertical)
      console.log('isPrePublish: ', isPrePublish)
      console.log('-------- 权限条件 --------')

      // 发布后状态且没有 SEO 权限，禁用
      this.setValue('seo_url', !isPrePublish && !isSEO, 'disabled')

      // 针对业务线，发布后状态且没有 SEO 权限，禁用
      this.setValue('seo_title', isVertical && !isPrePublish && !isSEO, 'disabled')

      // 根据业务线判断权限
      this.setValue('seo_keywords', !isEM, 'disabled')
      this.setValue('seo_overview', isVertical ? !isSEO : !isEM, 'disabled')
      this.setValue('seo_meta_desc', isVertical ? !isSEO : !isEM, 'disabled')

      // 特殊处理，seo_overview 只有特定业务线才展示
      this.setValue('seo_overview', !isVertical, 'isHide')
    }
  }
}
</script>

<style lang="scss" scoped>
.seo-content {
  .seo-content-box {
    padding: 0 0 38px 0;
  }

  .form-item-tip {
    margin-bottom: 8px;
    line-height: 1em;
    color: rgb(170, 170, 170);
  }
}
</style>
<style lang="scss">
.seo-content {
  .seo-content-box {
    .CodeMirror-placeholder {
      color: rgba(0, 0, 0, 0.5);
    }
  }
}
</style>
