<template>
  <div v-if="valid" class="guard-group">
    <div class="guard-group-wrapper">
      <h2 class="header">
        {{ $t('81818') }}
      </h2>

      <div class="body">
        <div class="tags-wrapper">
          <div v-for="(tag, index) in marks" :key="index" class="tag-box">
            <Tag :tag="tag" />
            <a-switch v-model="tag.value" :disabled="!tag.editable" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Tag from './tag.vue'

export default {
  components: {
    Tag
  },
  props: {
    marks: {
      type: [Array],
      default: () => []
    }
  },
  data() {
    return {}
  },
  computed: {
    isMerchant() {
      // admin端只可见 不可编辑
      return this.$root.isMerchant
    },
    valid() {
      return !!this.marks?.length && this.isMerchant
    }
  }
}
</script>

<style lang="scss" scoped>
.guard-group {
  .header {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
  }
  .body {
    margin-top: 8px;
    .tags-wrapper {
      .tag-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
  }
}
</style>
