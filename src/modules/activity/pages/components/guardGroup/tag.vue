<template>
  <div
    v-if="showTag"
    class="tag"
    :style="tag.value && getTagStyle(tag)"
    :class="{ 'unchoose-style': !tag.value }"
  >
    <span class="text">{{ tag.text }}</span>
  </div>
</template>

<script>
import { hexToRgba } from '@activity/utils/index.js'

export default {
  props: {
    tag: {
      type: Object,
      default: () => {}
    },
    showTag: {
      type: Boolean,
      default: true
    }
  },

  computed: {
    valid() {
      return this.tag.value
    }
  },
  methods: {
    getTagStyle(tag) {
      const { bg_color: backgroundColor, font_color: color, border_color: borderColor } = tag || {}

      return {
        backgroundColor: hexToRgba(backgroundColor),
        color: hexToRgba(color),
        borderColor: hexToRgba(borderColor)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tag {
  border-width: 1px;
  border-style: solid;
  padding: 1px 8px;
  line-height: 18px;
  border-radius: 2px;
  font-size: 12px;

  &.unchoose-style {
    background-color: #fafafa;
    color: rgba(0, 0, 0, 0.85);
    border-color: #d9d9d9;
  }
}
</style>
