<template>
  <div class="price_input_currenct-container">
    <a-input
      v-bind="$attrs"
      v-model="amount"
      :disabled="disabled"
      :placeholder="placeholder"
      @blur="$emit('blur')"
    ></a-input>
    <span class="currency-footnote" v-if="currency">{{ currency }}</span>
    <slot name="extra"></slot>
  </div>
</template>

<script>
export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: [String, Number],
    currency: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: 'please input'
    },
    formatter: Function
  },
  data() {
    return {
      amount: ''
    }
  },
  watch: {
    value: {
      handler(v) {
        this.amount = v
      },
      immediate: true
    },
    amount(v) {
      if (this.formatter) {
        let val = this.formatter(v)
        if (val !== v) {
          this.$nextTick(() => {
            this.amount = val
          })
        } else {
          this.$emit('change', v)
          this.$emit('input', v)
        }
      } else {
        this.$emit('change', v)
        this.$emit('input', v)
      }
    }
  }
}
</script>

<style lang="scss">
.price_input_currenct-container {
  position: relative;
  .currency-footnote {
    position: absolute;
    right: 10px;
    top: 6px;
    font-size: 13px;
    line-height: 2em;
  }
  .el-input {
    box-sizing: border-box;
    padding: 0;
    input {
      border-radius: 2px;
      border: solid 1px #e0e0e0;
      padding: 0 40px 0 12px;
      &:active,
      &:hover,
      &:focus {
        border-color: #1079fb;
      }
    }
  }
}
</style>
