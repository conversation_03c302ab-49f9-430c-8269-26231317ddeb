<template>
  <div class="container">
    <DynamicForm
      :filter-items="filterItems"
      :option-map="optionMap"
      :support-export-fields="supportExportFields"
      :loading="loading"
      :is-act="true"
      :download-loading="downloadLoading"
      @search="queryOrDownList"
      @exportFieldsChange="exportFieldsChange"
      @exportClick="exportClick"
    >
    </DynamicForm>
    <a-divider dashed class="divider" />
    <a-table
      ref="table"
      :columns="columns"
      :data-source="dataSource"
      :row-key="(record, index) => index"
      :loading="loading"
      table-layout="fixed"
      :pagination="pagination"
      bordered
      :scroll="{ x: scrollX }"
    >
    </a-table>
  </div>
</template>

<script>
import AdminApi from '@/modules/activity/api/exportData'
import DynamicForm from '../components/dynamicForm.vue'
import tableMixins from '../tableMixins'

export default {
  name: 'Activity',
  components: {
    DynamicForm
  },
  mixins: [tableMixins],
  props: {
    userInfor: {
      type: Object,
      required: true,
      default: () => {}
    }
  },
  data() {
    return {
      supportExportFields: [], // 支持导出的字段
      filterItems: [], // 后端返回可以筛选的字段
      exportFields: [], // 导出的字段
      isLoading: false,
      loading: false,
      downloadLoading: false,
      dataSource: [],
      columns: [],
      scrollX: 'max-content',
      pagination: {
        pageSize: 50,
        total: 0
      }
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    // 获取支持导出的字段
    async fetchData() {
      this.isLoading = true
      const data = await ajax.get({
        url: AdminApi.get_all_filter_items
      })

      if (data) {
        this.supportExportFields = data.support_export_fields || []
        this.filterItems = data.filter_items || []
      }
      this.isLoading = false
    },
    // 获取表格数据
    async queryOrDownList(value, action = 0) {
      // Sub Category 必传
      if (!value['sub_category_ids'] && !value['activity_ids']) {
        this.$message.error('AID or Sub Category cannot be empty')
        return
      }

      if (action === 0) {
        this.loading = true
      } else {
        this.downloadLoading = true
      }
      const payload = this.constructPayload(value, action)
      const cacheKey = JSON.stringify(payload)
      if (this.cache[cacheKey] && action === 0) {
        // 使用缓存的结果
        this.handleResult(action, this.cache[cacheKey])
        // 模拟loading
        setTimeout(() => {
          this.setLoading(action)
        }, 400)
      } else {
        const res = await ajax.postBody({
          url: AdminApi.getActivityList,
          data: payload
        })
        if (action === 0) {
          this.cache[cacheKey] = res
        }
        this.handleResult(res, action)
        this.setLoading(action)
      }
    },
    exportFieldsChange(value) {
      this.exportFields = value
    }
  }
}
</script>
