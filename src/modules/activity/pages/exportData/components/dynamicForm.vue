<template>
  <a-row :gutter="10" class="container">
    <a-col :span="16">
      <a-form :form="form" label-align="left" colon>
        <a-row :gutter="5">
          <a-col v-for="field in dynamicFormFields" :key="field.key" :span="12">
            <a-form-item :label="field.name">
              <a-input v-if="field.input_type === 0" v-decorator="[field.key]" allow-clear />
              <a-switch
                v-else-if="[2, 3].includes(field.input_type)"
                v-decorator="[field.key, { valuePropName: 'checked' }]"
              />
              <a-select
                v-else-if="[1, 4].includes(field.input_type)"
                v-decorator="[field.key]"
                :mode="getSelectType(field)"
                allow-clear
                show-search
                option-filter-prop="children"
                :filter-option="filterOption"
                @change="(val) => change(field.key, val)"
              >
                <a-select-option v-for="option in getList(field)" :key="option.value" :value="option.value">
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-empty
            v-if="dynamicFormFields.length === 0"
            class="waring"
            description="Please add a filter first"
            type="warning"
          />
        </a-row>
      </a-form>
    </a-col>
    <a-col :span="8">
      <a-form :form="sbmitform" label-align="left" colon>
        <a-row>
          <a-col :span="24">
            <a-form-item label="Add Filter">
              <a-select
                v-decorator="['filter']"
                placeholder="Select a field to search"
                mode="multiple"
                allow-clear
                show-search
                option-filter-prop="children"
                :filter-option="filterOption"
                @change="addField"
              >
                <a-select-option v-for="field in filterItems" :key="field.name" :value="field.name">
                  {{ field.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="Data fields to export">
              <a-select
                v-decorator="['export']"
                placeholder="Data fields to export"
                mode="multiple"
                allow-clear
                show-search
                option-filter-prop="children"
                :filter-option="filterOption"
                @change="exportFieldsChange"
              >
                <a-select-option :value="'all'"> All </a-select-option>
                <a-select-option v-for="field in supportExportFields" :key="field.key" :value="field.key">
                  {{ field.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <a-space>
        <a-button type="primary" :loading="loading" @click="debouncedSearch">search</a-button>
        <a-button type="primary" :loading="downloadLoading" @click="debouncedDownload">Download</a-button>
        <a-button @click="reset">reset</a-button>
      </a-space>
    </a-col>
  </a-row>
</template>
<script>
import { debounce } from 'lodash'

export default {
  props: {
    filterItems: {
      type: Array,
      required: true,
      default: () => []
    },
    supportExportFields: {
      type: Array,
      required: true,
      default: () => []
    },
    optionMap: {
      type: Object,
      required: true,
      default: () => {}
    },
    loading: {
      type: Boolean,
      required: true,
      default: () => false
    },
    downloadLoading: {
      type: Boolean,
      required: true,
      default: () => false
    },
    isAct: {
      type: Boolean,
      required: false,
      default: () => false
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      sbmitform: this.$form.createForm(this),
      dynamicFormFields: [],
      exportField: [],
      city: [],
      debouncedSearch: debounce(this.search, 500),
      debouncedDownload: debounce(this.downloadData, 500)
    }
  },
  watch: {
    filterItems: {
      handler(newVal) {
        if (newVal.length > 0) {
          const dynamicFormFields = (this.filterItems || [])
            .filter((field) => field.default_filter)
            .map((field) => field.name)
          this.addField(dynamicFormFields)
          setTimeout(() => {
            this.sbmitform.setFieldsValue({
              filter: dynamicFormFields
            })
          }, 300)
        }
      },
      immediate: true
    },
    supportExportFields: {
      handler(newVal) {
        if (newVal.length > 0) {
          const filterfields = (this.supportExportFields || [])
            .filter((field) => field.default_export)
            .map((field) => field.key)
          setTimeout(() => {
            this.exportFieldsChange(filterfields, true)
          }, 300)
        }
      },
      immediate: true
    }
  },
  methods: {
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    addField(newFieldKeys) {
      const dynamicFormFields = this.filterItems.filter((item) => newFieldKeys.includes(item.name))
      this.dynamicFormFields = dynamicFormFields
      this.$nextTick(() => {
        dynamicFormFields.forEach((newField) => {
          if ([2, 3].includes(newField.input_type)) {
            this.form.setFieldsValue({
              [newField.key]: false
            })
          }
        })
      })
    },
    search() {
      if (this.exportField.length === 0) {
        this.$message.error('Please select at least one "Data fields to export" to search!')
        return
      }

      this.form.validateFields((err, values) => {
        if (!err) {
          this.$emit('search', values)
        }
      })
    },
    reset() {
      this.form.resetFields()
    },
    exportFieldsChange(payload, init = false) {
      let value
      if (payload.includes('all')) {
        value = this.supportExportFields.map((field) => field.key)
        setTimeout(() => {
          this.sbmitform.setFieldsValue({
            export: ['all']
          })
        }, 300)
      } else {
        value = payload
        if (init) {
          setTimeout(() => {
            this.sbmitform.setFieldsValue({
              export: payload
            })
          }, 300)
        }
      }

      this.exportField = value
      this.$emit('exportFieldsChange', value)
    },
    async change(key, value) {
      if (key === 'country_ids') {
        let result = await ajax.get(ADMIN_API.act.get_all_cities_under_id, {
          params: {
            id: value
          }
        })
        const data = result.map((v) => ({
          label: v.name,
          value: v.id
        }))
        this.city = data || []
        this.form.setFieldsValue({
          city_ids: undefined
        })
      }
    },
    getList(field) {
      // city 比较特殊
      if (field.key === 'city_ids') {
        return this.city || []
      }
      return field.optional_list || this.optionMap[field.key] || []
    },
    downloadData() {
      if (this.exportField.length === 0) {
        this.$message.error('Please select at least one field ')
        return
      }

      this.form.validateFields((err, values) => {
        if (!err) {
          this.$emit('exportClick', values, this.exportField)
        }
      })
    },
    getSelectType(field) {
      const multipleKeys = this.isAct
        ? ['publish_status', 'city_ids']
        : [
            'package_status',
            'confirmation_time',
            'inventory_type',
            'voucher_get_method',
            'voucher_type',
            'voucher_code_level',
            'ticket_type',
            'cancellation_policy',
            'bookable_to_agent_or_user',
            'inventory_model',
            'selling_currency',
            'financial_model'
          ]

      if (multipleKeys.includes(field.key)) {
        return 'multiple'
      }
      return 'default'
    }
  }
}
</script>
<style>
.container {
  margin-bottom: 16px;
}
.waring {
  margin: 20px auto;
}
</style>
