<template>
  <div class="container">
    <a-tabs v-model="tabType">
      <a-tab-pane key="Activity" tab="Activity List">
        <Activity :user-infor="userInfo" :option-map="optionMap" />
      </a-tab-pane>
      <a-tab-pane key="Packages" tab="Packages List">
        <Packages :user-infor="userInfo" :option-map="optionMap" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script>
import Activity from './activity/index.vue'
import Packages from './packages/index.vue'
import { getUserInfo } from '@/modules/supplyApi/utils'
import { SUPPORT_CURRENCY_SYMBO } from '@activity/pages/package/package_const.js'

const url = '/v1/productadminsrv/toolsapi/export_data_service/get_export_sub_category_list'
export default {
  name: 'ExportData',
  components: {
    Activity,
    Packages
  },
  data() {
    return {
      tabType: 'Activity',
      userInfo: {},
      subCategory: [],
      countries: [],
      leafCategory: []
    }
  },
  computed: {
    // 下拉数据源
    optionMap() {
      const data = (SUPPORT_CURRENCY_SYMBO || []).map((item) => {
        return {
          label: item[0],
          value: item[0]
        }
      })
      return {
        sub_category_ids: this.subCategory,
        country_ids: this.countries,
        leaf_category_ids: this.leafCategory,
        merchant_currency: data,
        selling_currency: data
      }
    }
  },
  mounted() {
    this.getAccountId()
    this.getSubCategory()
    this.allCountries()
    this.getActPublishedCategoryInfo2action()
  },
  methods: {
    click(item, index) {
      this.tabType = item.value
      this.cur_tab = index
    },
    async getAccountId() {
      const result = await getUserInfo()
      if (result) {
        this.userInfo = result
      }
    },
    // 获取二级分类
    async getSubCategory() {
      let response = await ajax.get(url, {
        params: {
          language: window.KLK_LANG || 'en_US'
        }
      })
      if (!response) {
        return
      }
      const [category = {}] = (response.subcategory_list || []).filter((item) => item.id === 101)
      this.subCategory = category.subcategory_list.map((item) => {
        const { id, name } = item
        return {
          value: id,
          label: `${id}-${name}`
        }
      })
    },
    async allCountries() {
      const result = await ajax.get(ADMIN_API.act.get_all_countries, {
        params: {
          language: this.language
        }
      })
      this.countries = (result || []).map((item) => ({
        value: item.id,
        label: item.name
      }))
    },
    async getActPublishedCategoryInfo2action() {
      const result = await ajax.get(ADMIN_API.act.get_ttd_published_category_info)
      this.leafCategory = (result || []).reduce((acc, item) => {
        const { subcategory_list } = item
        acc = acc.concat(
          subcategory_list.map((subItem) => ({
            value: subItem.id,
            label: `${subItem.id}-${subItem.name}`
          }))
        )
        return acc
      }, [])
    }
  }
}
</script>
<style>
.container {
  padding: 10px;
}
</style>
