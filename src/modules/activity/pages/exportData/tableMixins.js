import { debounce } from 'lodash'

const mixins = {
  props: {
    optionMap: {
      type: Object,
      required: true,
      default: () => {}
    }
  },
  data() {
    return {
      scrollY: 0,
      isExporting: false,
      cache: {}
    }
  },
  mounted() {
    this.calculateTableHeight()
    window.addEventListener('resize', debounce(this.calculateTableHeight, 250))
  },
  beforeD<PERSON>roy() {
    window.removeEventListener('resize', debounce(this.calculateTableHeight, 250))
  },
  methods: {
    async exportClick(value) {
      if (this.isExporting) {
        this.$message.warning('Export is in progress, please wait...')
        return
      }
      this.isExporting = true
      await this.queryOrDownList(value, 1)
      this.isExporting = false
    },
    formatData(columns, dataRows) {
      return dataRows.map((row) => {
        const formattedRow = {}
        columns.forEach((column, index) => {
          formattedRow[column] = row[index]
        })
        return formattedRow
      })
    },
    calculateTableHeight() {
      const tableElement = this.$refs.table.$el
      const { top } = tableElement.getBoundingClientRect()
      const windowHeight = window.innerHeight
      const y = windowHeight - top - 150
      this.scrollY = y
    },
    formatValue(payload, name) {
      const value = payload[name]
      if (typeof value === 'boolean') {
        return value ? '1' : '0'
      }
      if (Array.isArray(payload[name])) {
        return value.join(',')
      }
      return value?.toString()
    },
    constructPayload(value, action) {
      const keys = Object.keys(value)
      const payload = {
        field_items: this.filterItems
          .filter((item) => keys.includes(item.key))
          .map((item) => ({
            field_key: item.key,
            value: this.formatValue(value, item.key)
          })),
        export_fields: this.exportFields,
        action
      }

      if (action === 1) {
        payload.to_emails = this.userInfor.account_name
      }

      return payload
    },
    handleResult(res, action) {
      if (res) {
        if (action === 0) {
          this.dataSource = this.formatData(res?.result?.columns || [], res.result?.datas || [])
          this.pagination = {
            total: res?.result?.search_total || 0,
            pageSize: 50,
            showTotal: (total) =>
              `Preview total:  ${total || 0}, downloads Total: ${res?.result?.download_total || 0}`
          }
          this.columns = (res?.result?.columns).map((column) => ({
            title: column,
            dataIndex: column,
            key: column,
            ellipsis: true,
            width: 200,
            customRender: (text) => {
              return {
                children: (
                  <a-tooltip placement="top">
                    <template slot="title">
                      <span>{text}</span>
                    </template>
                    <span>{text}</span>
                  </a-tooltip>
                ),
                attrs: {}
              }
            }
          }))
          if (this.columns.length > 0) {
            this.scrollX = this.columns.length * 200
          }
        } else {
          this.$message.success(
            'Your request is under processing now, it will take time to export the data. Once done, you will receive it through your email'
          )
        }
      }
    },
    setLoading(action) {
      if (action === 0) {
        this.loading = false
      } else {
        this.downloadLoading = false
      }
    }
  }
}

export default mixins
