<template>
  <div class="outer-wrap">
    <a-form layout="inline">
      <a-form-item label="Subcategory ID">
        <a-input
          v-model="subCateId"
          class="search-ipt"
          placeholder="search by Subcategory ID"
          @pressEnter="search"
        ></a-input>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" :loading="loading" @click="search">Search</a-button>
      </a-form-item>
    </a-form>
    <h3 class="cate-num">{{ sub_category_name }}</h3>
    <div v-show="!sub_category_id" class="empty-wrap"><a-empty description="No result" /></div>
    <div v-show="sub_category_id">
      <div class="tabs-head">
        <div class="tabs-head-left">Configuration Fields</div>
        <div class="tabs-head-right">
          <div class="r-text">Properties</div>
          <a-button type="primary" :loading="saveLoading" @click="submitForm">Save</a-button>
        </div>
      </div>
      <a-tabs
        default-active-key="1"
        :active-key="activeKey"
        tab-position="left"
        :tab-bar-style="tabBarStyle"
        @change="tabsChange"
      >
        <a-tab-pane key="1" tab="$0 Price Copywriting">
          <div class="form">
            <div v-for="item in lang" :key="item" class="form-item">
              <div class="form-label-sp">
                <span v-if="item === 'en_US'" class="red-dot">*</span>
                {{ item }}
              </div>
              <a-input v-model="text[item]" class="select" :placeholder="item"></a-input>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="contact_v2" tab="Contact Info">
          <div class="form sp">
            <div v-for="item in contactInfo" :key="item.key">
              <div class="form-label-title">{{ item.name }}</div>
              <div class="form-item">
                <div class="form-label">Ticked by default?</div>
                <a-select v-model="contact[item.key].value" default-value="True" class="select" size="small">
                  <a-select-option :value="1">True</a-select-option>
                  <a-select-option :value="0">False</a-select-option>
                </a-select>
              </div>
              <div class="form-item">
                <div class="form-label">Modifable？</div>
                <a-select
                  v-model="contact[item.key].modifiable"
                  :default-value="item.default"
                  class="select"
                  size="small"
                >
                  <a-select-option :value="1">True</a-select-option>
                  <a-select-option :value="0">False</a-select-option>
                </a-select>
              </div>
            </div>
            <!-- name: 'Language Restriction of name	 key: 'name_restrict_english' -->
            <div>
              <div class="form-label-title">Language Restriction of name</div>
              <div class="form-item">
                <div class="form-label">EN/Local</div>
                <a-select
                  v-model="contact.name_restrict_english.value"
                  default-value="EN"
                  class="select"
                  size="small"
                >
                  <a-select-option :value="1">EN</a-select-option>
                  <a-select-option :value="0">Local</a-select-option>
                </a-select>
              </div>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="pre_pay_asset_type" tab="Pre-pay Asset Type">
          <div class="form">
            <div label="">
              <div class="form-item">
                <a-select v-model="prePay" default-value="NA" class="select">
                  <a-select-option value="Advance">Advanced</a-select-option>
                  <a-select-option value="NA">NA</a-select-option>
                </a-select>
              </div>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="non_sold_separate" tab="Cannot be purchased separately?">
          <div class="form">
            <div class="form-item">
              <a-select v-model="separate" default-value="No" class="select">
                <a-select-option value="1">Yes</a-select-option>
                <a-select-option value="0">No</a-select-option>
              </a-select>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="rebate_credit_rate" tab="Rebate Credit Rate">
          <div class="form">
            <div class="form-item">
              <a-input-number
                v-model="rebate_credit_rate"
                class="select"
                placeholder="Input a number"
                :step="0.01"
                :min="0"
                :max="100"
                :precision="2"
                :formatter="formatter"
                :parser="(value) => value.replace('%', '')"
              />
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="itinerary_support" tab="Itinerary Config">
          <div class="form">
            <div>
              <div class="form-label-title">Itinerary Config</div>
              <div class="form-item">
                <div class="form-label">Support Itinerary?</div>
                <a-select v-model="itinerary_support.support_itinerary" class="select" size="small">
                  <a-select-option :value="true">True</a-select-option>
                  <a-select-option :value="false">False</a-select-option>
                </a-select>
              </div>
              <div class="form-item">
                <div class="form-label">Require?</div>
                <a-select
                  v-model="itinerary_support.is_required"
                  :disabled="!itinerary_support.support_itinerary"
                  class="select"
                  size="small"
                >
                  <a-select-option :value="true">True</a-select-option>
                  <a-select-option :value="false">False</a-select-option>
                </a-select>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script>
import { contactInfo } from './utils'
export default {
  data() {
    return {
      contactInfo,
      lang: [],
      tabBarStyle: {
        width: '260px',
        paddingTop: '16px'
      },
      loading: false,
      saveLoading: false,
      activeKey: '1',
      subCateId: '',
      sub_category_id: '',
      sub_category_name: '',
      text: {},
      prePay: 'NA',
      separate: '0',
      contact: {
        title: {},
        last_name: {},
        first_name: {},
        country: {},
        country_code: {},
        phone_number: {},
        mail_box: {},
        name_restrict_english: {}
      },
      debounce: {
        non_sold_separate: false,
        pre_pay_asset_type: false,
        contact_v2: false,
        rebate_credit_rate: false,
        itinerary_support: false
      },
      rebate_credit_rate: undefined,
      itinerary_support: {
        support_itinerary: false,
        is_required: false
      }
    }
  },
  methods: {
    formatter(value) {
      if (value) {
        return `${value}%`
      }
      return ''
    },
    tabsChange(tabs) {
      let { debounce = {} } = this
      this.activeKey = tabs
      // 只在第一次点击时请求数据
      if (debounce[tabs]) {
        return
      }
      this.debounce[tabs] = true

      switch (tabs) {
        case 'contact_v2':
          this.getContact()
          break
        case 'pre_pay_asset_type':
          this.getPrepay()
          break
        case 'non_sold_separate':
          this.getSseparate()
          break
        case 'rebate_credit_rate':
          this.getRebateCreditRate()
          break
        case 'itinerary_support':
          this.getItinerarySupport()
          break
      }
    },
    checkText() {
      const { text } = this
      const obj = {}
      // 检查用户输入的内容，去除前后空白
      for (let key in text) {
        obj[key] = text[key].trim()
      }
      this.text = obj
      return obj
    },
    async getRebateCreditRate() {
      const result = await this.getConfig('rebate_credit_rate')
      if (!result) {
        return
      }
      const { rebate_credit_rate } = result.config || {}
      this.rebate_credit_rate = rebate_credit_rate
    },
    async getItinerarySupport() {
      const result = await this.getConfig('itinerary_support')
      if (!result) {
        return
      }
      const { support_itinerary, is_required } = result.config || {}
      this.itinerary_support.support_itinerary = !!support_itinerary
      this.itinerary_support.is_required = !!is_required
    },
    // Cannot be purchased separately?
    async getSseparate() {
      const result = await this.getConfig('non_sold_separate')
      if (!result) {
        return
      }
      let { non_sold_separate = '0' } = result.config || {}
      this.separate = String(non_sold_separate)
    },
    // Pre-pay Asset Type
    async getPrepay() {
      const result = await this.getConfig('pre_pay_asset_type')
      if (!result) {
        return
      }
      let { type = 'NA' } = result.config || {}
      this.prePay = type
    },
    // Contact Info
    async getContact() {
      const result = await this.getConfig('contact_v2')
      if (!result) {
        return
      }
      this.contact = result.config || {}
    },
    async getConfig(config_type) {
      let { sub_category_id } = this
      const hide = this.$message.loading('Loading...', 0)
      const result = await ajax.get(ADMIN_API.act.get_category_config_common, {
        params: {
          category_id: sub_category_id,
          config_type
        }
      })
      hide()
      return result
    },
    async saveConfig(data = {}) {
      let { debounce } = this
      // tabs 从未被点击，说明数据未修改
      if (!debounce[data.config_type]) {
        return true
      }
      const result = await ajax.postBody(ADMIN_API.act.store_category_config_common, {
        data
      })
      return result
    },
    saveText() {
      const text = this.checkText()
      const result = ajax.postBody(ADMIN_API.act.save_sub_category_no_price_text, {
        data: {
          sub_category_id: this.sub_category_id,
          text
        }
      })
      return result
    },
    async submitForm() {
      const {
        sub_category_id,
        prePay = 'NA',
        separate = '0',
        contact = {},
        rebate_credit_rate = 1.67,
        itinerary_support
      } = this
      this.saveLoading = true
      const allResult = await Promise.all([
        this.saveText(),
        this.saveConfig({
          category_id: sub_category_id,
          config_type: 'pre_pay_asset_type',
          config: { type: prePay }
        }),
        this.saveConfig({
          category_id: sub_category_id,
          config_type: 'non_sold_separate',
          config: { non_sold_separate: +separate }
        }),
        this.saveConfig({
          category_id: sub_category_id,
          config_type: 'contact_v2',
          config: contact
        }),
        this.saveConfig({
          category_id: sub_category_id,
          config_type: 'itinerary_support',
          config: itinerary_support
        }),
        this.saveConfig({
          category_id: sub_category_id,
          config_type: 'rebate_credit_rate',
          config: {
            rebate_credit_rate
          }
        })
      ])
      this.saveLoading = false
      const success = allResult.every((item) => item || item.success)
      success && this.$message.success('Saved successfully')
    },
    async search() {
      let subcategory_id_list = this.subCateId.trim()
      // 输入的subcategory_id限制为数字
      if (!/^[1-9]\d*$/g.test(subcategory_id_list)) {
        return this.$message.info('Input a number')
      }
      this.loading = true
      const result = await ajax.get(ADMIN_API.act.get_sub_category_no_price_text_by_id, {
        params: {
          subcategory_id_list
        }
      })
      this.loading = false

      if (!result) {
        // this.$message.error('No Result')
        return
      }
      let data = result[0]
      const { sub_category_id, text = {}, sub_category_name = '' } = data
      const lang = Object.keys(text)
      // 数据为空
      if (lang.length === 0) {
        this.$message.info('No Result')
        this.sub_category_id = ''
        this.sub_category_name = ''
        this.text = {}
        return
      }
      // 排序，保证en_US排在第一位
      this.lang = lang.sort((item) => {
        return item === 'en_US' ? -1 : 1
      })
      this.sub_category_id = sub_category_id
      this.sub_category_name = sub_category_id + '-' + sub_category_name
      this.text = text
      this.activeKey = '1'
      this.debounce = {
        non_sold_separate: false,
        pre_pay_asset_type: false,
        contact_v2: false,
        rebate_credit_rate: false
      }
    }
  }
}
</script>

<style lang="scss">
.outer-wrap {
  padding: 20px;

  .search-ipt {
    width: 200px;
  }
  .form {
    padding-top: 24px;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    max-width: 860px;
    &.sp {
      max-width: 620px;
    }
    .form-label-title {
      font-weight: 600;
      font-size: 16px;
      padding-bottom: 6px;
    }
    .select {
      width: 200px;
    }
    .form-item {
      display: flex;
      align-items: center;
      min-width: 100px;
      padding-bottom: 10px;
      margin-right: 20px;
      .form-label-sp {
        min-width: 60px;
        text-align: left;
        position: relative;
        .red-dot {
          color: red;
          position: absolute;
          left: -8px;
          top: 0;
          z-index: 2;
        }
      }
      .form-label {
        min-width: 142px;
        text-align: left;
        padding-left: 16px;
        align-self: flex-start;
      }
    }
  }
  .tabs-head {
    display: flex;
    align-items: center;
    text-align: center;
    background: #eee;
    line-height: 44px;
    margin-top: 10px;
    max-width: 1200px;

    .tabs-head-left {
      width: 260px;
      border-right: 1px solid #ccc;
    }
    .tabs-head-right {
      display: flex;
      align-items: center;
      flex: 1;
      padding-right: 24px;
      .r-text {
        max-width: 780px;
        flex: 1;
      }
    }
  }
  .ant-tabs-tab {
    padding: 8px 24px !important;
    text-align: left !important;
  }

  .cate-num {
    padding: 10px 0;
    margin: 0;
  }
  .empty-wrap {
    padding: 120px 0;
  }
}
</style>
