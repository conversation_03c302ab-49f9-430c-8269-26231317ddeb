<script>
export default {
  data() {
    return {
      guide: {
        visible: true,
        title: (h) => (
          <span
            domPropsInnerHTML={__('28249', {
              MULTILANG_URL_PLACEHOLDER: `${this.$router.options.base}/act/management`
            })}
          />
        ),
        onOk: () => {
          this.$router.push({
            path: '/act/management'
          })
        },
        zIndex: 5,
        centered: true
      },
      modalVm: null
    }
  },
  created() {
    this.modalVm = this.$modal.info(this.guide)
  },
  beforeDestroy() {
    this.modalVm.destroy()
  },
  render() {
    return ''
  }
}
</script>
