<template>
  <div id="IsCPTypeFixedDate">
    <a-form-model-item
      class="form-item"
      prop="IsCPTypeFixedDate"
      :rules="[{ required: !val, message: 'Please select a type', trigger: 'change' }]"
    >
      <div :style="`margin-bottom:${copy ? 16 : 2}px; color: rgba(0, 0, 0, 0.85)`">
        <div class="rules-label">{{ $t('200750') }}</div>
        <a-select v-model="copy" @change="onChange" :disabled="readonly">
          <a-select-option v-for="(option, idx) in options" :key="idx" :value="option.value">
            <span style="vertical-align: middle; display: inline-block">{{ option.label }}</span>
          </a-select-option>
        </a-select>
      </div>
    </a-form-model-item>
    <div>
      <a-alert
        v-if="showAlert"
        message="Please double confirm with the API team whether this API partner supports checking redemption status. If not, please use
        Conditional cancellation - Freetext type to set the rule, otherwise, customers cannot self-refund
        successfully."
        type="info"
      />
    </div>
  </div>
</template>

<script>
import options, {
  cp_by_participate_time_group,
  cp_by_redeem_time_group,
  cp_by_expire_time_group,
  cancellation_policy_fixed_date
} from './options'
import { ticket_type } from '@activity/pages/package/package_const'

export default {
  name: 'IsCPTypeFixedDate',
  model: {
    prop: 'val',
    event: 'update'
  },
  props: {
    val: {
      type: [String, Object],
      default: null
    },
    readonly: {
      type: Boolean,
      default: false
    },

    // 需要根据
    cpStatus: {
      type: Object,
      required: true
    }
  },
  computed: {
    copy: {
      get() {
        return this.val
      },
      set(val) {
        this.$emit('update', val)
      }
    },
    subCategoryId() {
      return this.$store.state.categoryInfo?.sub_category_id ?? 0
    },
    showAlert() {
      return this.copy === cp_by_redeem_time_group && this.cpStatus.inventoryType === 'API'
    },
    options() {
      let data = options.map(({ label, value }) => ({ label, value }))
      const {
        inventoryType,
        isCPValueCondition,
        ticketType,
        voucherGetMethod,
        hasOldFormat,
        isComboPKG,
        isPassProducts,
        supportsIds
      } = this.cpStatus || {}

      const supportsType = supportsIds.includes(this.subCategoryId)
      const isTicketTypeFixedDate = ticketType === ticket_type.fixed_date
      const isInventoryNotAPI = inventoryType !== 'API'
      // 第二期的规则展示条件加上有数据的情况下才展示
      const newBindIf = isCPValueCondition && isTicketTypeFixedDate && isInventoryNotAPI && hasOldFormat
      if (isComboPKG || isPassProducts || !supportsType) {
        // 如果是 ComboPKG 或者是 PassProducts，或者不支持的类目，则只展示最后2种 Freetext和 Old format 规则
        data = data.slice(-2)
      }

      return data.filter((item) => {
        const value = item.value
        // By participation time 出现在 Ticket type = Fixed date，如果不是 Fixed date，则过滤掉 cp_by_participate_time_group
        if (value === cp_by_participate_time_group && ticketType !== ticket_type.fixed_date) {
          return false
        }
        if (value === cp_by_expire_time_group) {
          // By Expiration time 仅出现在 open date or open date w/o calendar
          if (
            ticketType === ticket_type.open_ticket ||
            ticketType === ticket_type.open_ticket_without_calendar
          ) {
            return true
          }
          return false
        }

        if (value === cancellation_policy_fixed_date) {
          // 没有找到tag，则说明没有数据，直接过滤掉，不展示
          if (!newBindIf) {
            return false
          }
          // 有原来的数据,还满足原来的展示条件，则展示
        }
        // cp_by_redeem_time_group 出现在 Inventory type = API或者others ，还有 Voucher retrieval method = Electronic vouche
        if (value === cp_by_redeem_time_group) {
          if ((inventoryType === 'API' || inventoryType === 'OTHERS') && voucherGetMethod === 0) {
            return true
          }
          return false
        }
        return true
      })
    }
  },
  watch: {
    options(newVal) {
      if (newVal) {
        // 数组会被过滤，如果当前值不在options中，则置空
        if (this.options.findIndex((item) => item.value === this.val) === -1) {
          this.copy = null
        }
      }
    }
  },
  methods: {
    onChange(val) {
      this.$emit('update', val)
    }
  }
}
</script>

<style></style>
