// rules的名称
export const cp_by_participate_time_group = 'cp_by_participate_time_group'
export const cp_by_booking_confirm_time_group = 'cp_by_booking_confirm_time_group'
export const cp_by_expire_time_group = 'cp_by_expire_time_group'
export const cp_by_redeem_time_group = 'cp_by_redeem_time_group'
export const cancellation_policy_fixed_date = 'cancellation_policy_fixed_date'

// 代表最早一版本的数据，保持现状态
export const old_cancellation_refund_policy = 'old_cancellation_refund_policy'

// 有条件退改的种类，之前只有2种新旧之分，现在扩展到6种
const options = [
  {
    label: 'Standardized - by participation time',
    // tip: 'Use Standardized settings can calculate refund automatically, short refund process',
    value: cp_by_participate_time_group,
    mapValue: {
      GROUP_TAG: cp_by_participate_time_group,
      WIDGET_TAG: 'cp_by_participate_time_title'
    }
  },
  {
    label: 'Standardized - by booking confirmation time',
    // tip: 'Use “Freetext” settings will stay manual refund process, original refund process',
    value: cp_by_booking_confirm_time_group,
    mapValue: {
      GROUP_TAG: cp_by_booking_confirm_time_group,
      WIDGET_TAG: 'cp_by_booking_confirm_time_title'
    }
  },
  {
    label: 'Standardized - by expiration time',
    // tip: 'Use “Freetext” settings will stay manual refund process, original refund process',
    value: cp_by_expire_time_group,
    mapValue: {
      GROUP_TAG: cp_by_expire_time_group,
      WIDGET_TAG: 'cp_by_expire_time_title'
    }
  },
  {
    label: 'Standardized - by redemption time',
    value: cp_by_redeem_time_group,
    mapValue: {
      GROUP_TAG: cp_by_redeem_time_group,
      WIDGET_TAG: 'cp_by_redeem_time_title'
    }
  },
  // 第二版数据，有数据展示，没数据不展示
  {
    label: 'Standardized - Old format',
    value: cancellation_policy_fixed_date,
    mapValue: {
      GROUP_TAG: cancellation_policy_fixed_date,
      WIDGET_TAG: 'cancellation_policy_fixed_date_title'
    }
  },
  // 最早的数据，无GROUP_TAG
  {
    label: 'Freetext - Conditional cancellation policy',
    value: old_cancellation_refund_policy,
    mapValue: {
      GROUP_TAG: old_cancellation_refund_policy,
      WIDGET_TAG: old_cancellation_refund_policy
    }
  }
]

// 原有的配置raido如下：
// options: [
//   {
//     label: 'Standardized - Conditional Cancellation Policy',
//     tip: 'Use “Standardized” settings can calculate refund automatically, short refund process',
//     value: true
//   },
//   {
//     label: 'Freetext - Conditional Cancellation Policy ',
//     tip: 'Use “Freetext” settings will stay manual refund process, original refund process',
//     value: false
//   }
// ]

export default options
