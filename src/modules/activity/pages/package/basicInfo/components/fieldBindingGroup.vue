<template>
  <div class="field-binding-group-container">
    <ShimAntdTooltip placement="right" :title="title" :rich-tips="richTips" :is-fit-content="false">
      <Content
        ref="content"
        :original-group-data="originalGroupData"
        :ref-data="getRefData()"
        :group-list="groupList"
        :all-variables="allVariables"
        :show-summary="showSummary"
        :order-list="orderList"
        :field="field"
        :field-title="fieldTitle"
        :invalid-tag="current_invalid_tag"
        :auto-checked-to-front="true"
        :required="required"
        :display-title="displayTitle"
        :has-shading="true"
        :display-widget-count="true"
        :has-draft-state="hasDraftState"
        :hide-widget-type="[3]"
        group-item-padding="0"
        :default-required-gourp-items="defaultRequiredGourpItems"
        v-bind="$attrs"
        @changeData="$emit('changeData')"
        @changeGroupSummary="onChangeGroupSummary"
        @unfoldAllGroups="$emit('unfoldAllGroups')"
      >
        <template v-if="$slots.customTitle" #customTitle>
          <slot name="customTitle"></slot>
        </template>
      </Content>
    </ShimAntdTooltip>

    <List
      v-if="list.visible"
      :visible.sync="list.visible"
      :field="field"
      :group-list="groupList"
      :original-group-data="originalGroupData"
    />
  </div>
</template>

<script>
import { mapMutations } from 'vuex'
import common_mixin from '@activity/pages/activityManagement/detailV2/common_mixin'
import cancellation_mixin from '@activity/pages/package/basicInfo/mixins/cancellation/fieldBindingGroup.js'
import { computedGroupSummary } from '@activity/components/groupSummary/utils'
import { getEditLang } from '@activity/utils'
import Content from '@activity/pages/activityManagement/detailV2/components/content'
import List from '@activity/pages/activityManagement/detailV2/components/list/index'
import { genUUID } from '../../../../utils'
import ShimAntdTooltip from '@activity/components/shimAntdTooltip'

export default {
  name: 'FieldBindingGroup',
  components: {
    Content,
    List,
    ShimAntdTooltip
  },
  mixins: [common_mixin, cancellation_mixin],
  inject: ['activity_id'],
  props: {
    schemaConfig: {
      type: Object,
      required: true
    },
    form: {
      type: Object,
      required: true
    },
    field: {
      type: String,
      required: true
    },
    data: {
      type: Object,
      required: true
    },
    allVariables: {
      type: Array,
      required: true
    },
    showSummary: {
      type: Boolean,
      default: true
    },
    orderList: {
      type: Array,
      required: true
    },
    groupList: {
      type: Array,
      required: true
    },
    initCallback: {
      type: Function,
      default: null
    },
    fieldTitle: {
      type: String,
      default: ''
    },
    refForm: {
      type: Object,
      default: () => ({})
    },
    // option binding group 是同数据源，在该情况下前置统一初始化数据，不需要在这里重复处理
    shouldBeInitData: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    },
    richTips: {
      type: String,
      default: ''
    },
    defaultRequiredGourpItems: {
      type: Array,
      default: () => []
    },
    hasDraftState: {
      type: Boolean,
      default: false
    },
    overlayCheckedTitle: {
      type: Boolean,
      default: false
    },
    displayTitle: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      invalid_tags: {},
      current_invalid_tag: {
        invalid_attr_value_tags: [],
        invalid_widget_tags: []
      },
      groupsSummary: [],

      list: {
        visible: false
      }
    }
  },
  computed: {
    required() {
      return this.schemaConfig[this.field]?.required ?? false
    },
    originalGroupData: {
      get() {
        return this.data.group_data
      },
      set() {}
    },
    fieldBindingRel() {
      return this.$store.getters['attr/bindingRelOnFieldGetter']({
        field: this.field
      })
    },
    fieldValue() {
      return this.form[this.field]
    },
    invalidWidgetTags() {
      return this.$store.getters['attr/invalidWidgetTagsByFieldGetter']({
        field: this.field
      })
    },
    filterAttrValOpt() {
      return this.$store.getters['attr/filterAttrValOptByFieldGetter']({
        field: this.field
      })
    },
    disabledVarOptionByField() {
      return this.$store.getters['attr/disabledVarOptionByFieldGetter']({
        field: this.field
      })
    }
  },
  watch: {
    fieldBindingRel: {
      deep: true,
      handler(v) {
        this.isModifiedBindingRel = true
        v && this.initGroupData()
      }
    },
    groupList: {
      deep: true,
      immediate: true,
      handler() {
        this.initGroupData()
      }
    }
  },
  methods: {
    ...mapMutations({
      setGroupSelectionStore: 'attr/setGroupSelectionStore',
      setCheckedTitlesOnGroup: 'attr/setCheckedTitlesOnGroup'
    }),
    computedGroupSummary,
    getGroupPostData(clearWidgetData = false) {
      const package_id = +this.$route.query.package_id

      return {
        activity_id: this.activity_id,
        language: getEditLang(),
        groups: this.groupList.map((group) => {
          let group_id = group.id
          let widgets = this.originalGroupData.groups_instance[group_id]

          return {
            group_id,
            extra_id: package_id,
            extra_type: 'PKG',
            widgets: widgets.map((item, index) => {
              const { id, extra_info, instance_id, draft_id } = item

              let attr_values = item.attr_values || []
              if (item.create) {
                // for backend, only
                attr_values = attr_values.map((attr_value) => {
                  return {
                    ...attr_value,
                    value:
                      '[object Object]' === toString.call(attr_value.value)
                        ? JSON.stringify(attr_value.value)
                        : attr_value.value, // String
                    ...(attr_value.is_free_text
                      ? {
                          create: true,
                          variable_map_value: {
                            ...attr_value.variable_map_value,
                            values: (_.get(attr_value, 'variable_map_value.values') || []).map((value) => ({
                              ...value,
                              create: true
                            }))
                          }
                        }
                      : {})
                  }
                })
              } else {
                attr_values = attr_values.map((attr_value) => {
                  return {
                    ...attr_value,
                    value:
                      '[object Object]' === toString.call(attr_value.value)
                        ? JSON.stringify(attr_value.value)
                        : attr_value.value
                  }
                })
              }

              /**
               * cancellation_policy 手动隐藏的bind,清空值
               */
              return {
                id,
                extra_info,
                instance_id,
                draft_id,
                attr_values:
                  clearWidgetData || this.cpIsHiddenBinding(item, this.fieldValue)
                    ? []
                    : attr_values.map((attr_value) => {
                        attr_value = _.cloneDeep(attr_value)
                        delete attr_value.showText
                        delete attr_value.isInstanceData

                        return attr_value
                      }),
                order: index
              }
            })
          }
        })
      }
    },
    async validateGroup() {
      return await this.$refs.content.validateForm()
    },
    async initGroupData() {
      if (this._debounce) {
        return
      }

      this._debounce = true
      setTimeout(() => {
        this._debounce = false
      }, 1000 / 60)

      let { group_data: originalGroupData = {}, invalid_tags } = this.data

      if (this.shouldBeInitData) {
        originalGroupData = this.formatResponseData(originalGroupData)
      }

      Object.assign(this.originalGroupData, originalGroupData)
      this.invalid_tags = invalid_tags || []

      this.$emit('fieldAttributeGroupVN', {
        field: this.field,
        $vm: this
      })

      this.formatGroupInstanceData()
      this.initCallbackFn()
    },
    initCallbackFn() {
      this.initCallback?.(this)
    },
    // 根据选项绑定关系格式化 widget
    formatGroupInstanceData() {
      this.groupList.forEach((group) => {
        const groupId = group.id
        let widgets = this.originalGroupData.groups_instance?.[groupId] ?? []
        const groupBindingRel = this.$store.getters['attr/bindingRelOnGroupGetter']({
          field: this.field,
          groupId
        })

        let res
        if (groupBindingRel) {
          res = this.formatBindingRel(widgets, groupBindingRel)
        } else {
          res = this.formatRefTagLinkage(widgets)
        }

        const { data, titlesList } = res
        this.originalGroupData.groups_instance[groupId] = data
        this.setCheckedTitlesOnGroup({
          field: this.field,
          groupId,
          data: titlesList,
          overlayCheckedTitle: this.overlayCheckedTitle
        })
      })
    },
    // 原有 open date ref tag 控制联动的逻辑，与 option rel 冲突
    formatRefTagLinkage(widgets) {
      const data = widgets.map((widget) => {
        widget.ui_conf.attr_value_list = widget.ui_conf.attr_value_list.map((item) => {
          if (
            this.filterAttrValOpt.includes(item.ref_field_tag) ||
            this.current_invalid_tag.invalid_attr_value_tags.includes(item.ref_field_tag)
          ) {
            item.hide = true
          } else {
            item.hide = false
          }

          return item
        })

        return widget
      })

      return {
        data,
        titlesList: data.map((widget) => genUUID(widget))
      }
    },
    // 格式化选项和部件的绑定关系
    formatBindingRel(widgets, groupBindingRel) {
      return widgets.reduce(
        (acc, widget) => {
          const { refAttrId } = widget
          const titles = groupBindingRel?.titles ?? {}
          const currTitleBindingRel = titles[refAttrId]
          let { ui_conf, attr_values } = widget

          // 如果当前 option 存在关联
          if (currTitleBindingRel) {
            const { values } = currTitleBindingRel

            ui_conf.attr_value_list = ui_conf.attr_value_list.map((item) => {
              const bindingRel = values[item.id]
              if (bindingRel) {
                return {
                  ...item,
                  access_permission: 2,
                  extendConf: bindingRel
                }
              } else if (
                !this.isModifiedBindingRel &&
                attr_values.find((attrVal) => attrVal.id === item.id)
              ) {
                return {
                  ...item,
                  extendConf: {
                    access_permission: 1,
                    canDelete: true,
                    isTrashData: true // 只能删，不可修改
                  }
                }
              } else {
                return {
                  ...item,
                  extendConf: {
                    access_permission: 0
                  }
                }
              }
            })
          }

          // 切换选项之后，直接走关联关系，虚增的脏数据需要过滤
          if (this.isModifiedBindingRel) {
            ui_conf.attr_value_list = ui_conf.attr_value_list.filter((item) => !item?.extendConf?.isInflate)
            widget.attr_values = widget.attr_values.filter((item) => !item || !item.extendConf?.isTrashData)
          } else {
            const common = {
              extendConf: {
                access_permission: 1,
                canDelete: true,
                isTrashData: true // 只能删，不可修改
              }
            }
            widget.attr_values = attr_values.map((attrValues) => {
              if (ui_conf.attr_value_list.find((item) => item.id === attrValues.id)) {
                return attrValues
              }

              // 关联关系的 attr value 是不支持 free text 的
              // 所以 free text 肯定是脏数据
              if (attrValues.is_free_text) {
                Object.assign(attrValues, common)
              } else {
                // 已删的旧 option 需要虚增
                ui_conf.attr_value_list.push({
                  ...attrValues,
                  isInflate: true,
                  ...common
                })
              }

              return attrValues
            })
          }

          // 如果有关联或者有脏值情况下，需要自动将对应的 widget checked（适用于左边 group titles 列表选择）
          if (currTitleBindingRel || (!this.isModifiedBindingRel && widget.attr_values.length)) {
            acc.titlesList.push(genUUID(widget))
          }

          currTitleBindingRel && acc.data.push(widget)

          return acc
        },
        {
          data: [],
          titlesList: []
        }
      )
    },
    getRefData() {
      let refData = {}

      if (Object.prototype.hasOwnProperty.call(this.refForm, this.field)) {
        refData = _.get(this.refForm[this.field], 'group_data.groups_instance', {})
      }

      return refData
    },
    getGroupsSummary() {
      this.$refs.content?.getGroupsSummaryDebounce?.()
    },
    onChangeGroupSummary(data) {
      this.$set(this, 'groupsSummary', data.groupsSummary)
      this.$emit('changeGroupSummary', data)
    }
  }
}
</script>

<style lang="scss">
.basic-info-attribute {
  .ant-row.ant-form-item {
    min-height: inherit;
  }
  .ant-input {
    width: 100%;
  }
}
</style>

<style lang="scss" scoped>
$spacePixel: 12px;
.group-widget-item {
  background-color: #fafafa;
  margin-top: 8px;
  padding: 12px;
  &:first-child {
    margin-top: 0;
  }
  ::v-deep .compound-select .compound-option {
    background-color: transparent;
  }
}
.group-widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  border-radius: 4px;
  cursor: default;
  transition: background-color 0.3s;
  &:first-child {
    margin-top: 0;
  }
  &:hover {
    background-color: #fafafa;
  }
}
.group-widget-name {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
}
.group-list {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.summary-group-list {
  background-color: #f7f7f9;
  padding: $spacePixel;
  margin-top: $spacePixel + 38; // 50
}
.summary-group-name {
  margin-bottom: $spacePixel;
  font-size: 18px;
}

.has-error {
  color: #f5222d;
}
</style>
