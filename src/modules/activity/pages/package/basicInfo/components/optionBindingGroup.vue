<template>
  <div class="option-binding-titles-container">
    <ShimAntdTooltip placement="right" :title="title" :rich-tips="richTips" :is-fit-content="true">
      <a-form-model-item class="option-binding-form-item" :prop="field" :rules="rules" :colon="colon">
        <template #label>
          <label class="option-binding-form-item-label">
            {{ formItemLabel }}
            <!-- 后续可以通过 content 的 displayCountOnGroup 判断是否存在可展示的group -->
            <a-icon
              v-if="showUnfoldBtn"
              :type="!isEditState ? 'right' : 'down'"
              @click.stop="isEditState = !isEditState"
            />
          </label>
        </template>

        <slot name="strongTips"></slot>

        <slot name="chosen">
          <component
            :is="componentConf.name"
            v-model="form[field]"
            v-check-val="
              () => {
                return options.map((opt) => opt.value).includes(form[field])
              }
            "
            :placeholder="placeholder"
            :disabled="disabledChosen"
          >
            <component
              :is="componentConf.option"
              v-for="option of options"
              :key="option.value"
              :value="option.value"
              :disabled="getOptDisabled(option)"
            >
              {{ option.label }}
            </component>
          </component>
        </slot>
      </a-form-model-item>
    </ShimAntdTooltip>

    <!-- 是否是C -->
    <CancellationType
      v-if="cpStatus && cpStatus.showRulesType"
      v-model="isCPTypeNewBind"
      :readonly="readonly"
      :cp-status="cpStatus"
    />

    <slot name="attr">
      <FieldBindingGroup
        v-if="groupList.length"
        ref="fieldBindingGroup"
        v-bind="$attrs"
        :schema-config="schemaConfig"
        :form="form"
        :field="field"
        :group-list="groupList"
        :data="optionGroup"
        :show-summary="false"
        :should-be-init-data="false"
        :readonly="readonly"
        @changeData="$emit('changeData')"
        @unfoldAllGroups="isEditState = true"
        @changeGroupSummary="(data) => $emit('changeGroupSummary', data)"
        @fieldAttributeGroupVN="fieldAttributeGroupVN"
      />
    </slot>
  </div>
</template>

<script>
import { mapMutations } from 'vuex'
import FieldBindingGroup from './fieldBindingGroup'
import common_mixin from '@activity/pages/activityManagement/detailV2/common_mixin'
import ShimAntdTooltip from '@activity/components/shimAntdTooltip'
import cancellation_mixin from '@activity/pages/package/basicInfo/mixins/cancellation/optionBindingGroup.js'

export default {
  name: 'OptionBindingTitles',
  components: {
    FieldBindingGroup,
    ShimAntdTooltip
  },
  mixins: [common_mixin, cancellation_mixin],
  inheritAttrs: false,
  model: {
    prop: 'value',
    event: 'update'
  },
  props: {
    schemaConfig: {
      type: Object,
      required: true
    },
    field: {
      type: String,
      required: true
    },
    form: {
      type: Object,
      required: true
    },
    optionGroup: {
      type: Object,
      required: true
    },
    formItemLabel: {
      type: String,
      default: ''
    },
    rules: {
      type: Object,
      default: () => ({})
    },
    colon: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: 'select',
      validator(value) {
        return ['select', 'radio'].includes(value)
      }
    },
    placeholder: {
      type: String,
      default: 'Please select'
    },
    disabledChosen: {
      type: Boolean,
      default: false
    },
    calcOptDisabledFunc: {
      type: Function,
      default: null
    },
    defaultOptions: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    },
    richTips: {
      type: [String, Object],
      default: null
    }
  },
  data() {
    return {
      groupList: [],
      isEditState: true
    }
  },
  computed: {
    readonly() {
      return this.$attrs.readonly || this.schemaConfig[this.field]?.access_permission === 1
    },
    options() {
      return this.schemaConfig[this.field].options || this.defaultOptions || []
    },
    componentConf() {
      const dict = {
        select: {
          name: 'a-select',
          option: 'a-select-option'
        },
        radio: {
          name: 'a-radio-group',
          option: 'a-radio'
        }
      }
      return dict[this.type]
    },
    groupsData: {
      get() {
        return this.optionGroup.group_data
      }
    },
    hasBindingRel() {
      return this.options.some((item) => item.binded_titles)
    },
    currFieldValue() {
      return this.form[this.field]
    },
    showUnfoldBtn() {
      return (
        this.groupList.length &&
        !this.groupList.every((group) => group.widget_list.every((widget) => widget.hide))
      )
    }
  },
  watch: {
    isEditState(val) {
      this.handleSwitchEditState(val)
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initOptRelationship()
    })
  },
  methods: {
    ...mapMutations({
      setBindingRelOnField: 'attr/setBindingRelOnField'
    }),
    fieldAttributeGroupVN(vm) {
      this.$listeners.fieldAttributeGroupVN(vm)

      setTimeout(() => {
        this.isEditState = false
      }, 60)
    },
    handleSwitchEditState(val) {
      const content = this.$refs.fieldBindingGroup?.$refs?.content

      if (!content) return

      if (val) {
        // 展开
        content?.unfoldAllGroups?.()
      } else {
        content?.foldAllGroups?.()
      }
    },
    validateGroup() {
      return this.$refs?.fieldBindingGroup?.validateGroup?.() ?? true
    },
    getGroupPostData() {
      return this.$refs.fieldBindingGroup.getGroupPostData()
    },
    initOptRelationship() {
      this.setBindingRelOnField({
        field: this.field,
        data: null
      })

      if (this.currFieldValue !== undefined) {
        this.handleChange(false)
      }

      this.$watch('currFieldValue', this.handleChange, { deep: true })
    },
    async handleChange(switchEditState = true) {
      if (!this.hasBindingRel) {
        return
      }

      const value = this.form[this.field]
      let { groupList, bindingRelationship } = this.getBindingTitlesByValue(value)
      groupList = groupList.filter((group) => {
        return (group.widget_list || []).some((widget) => {
          const bindConf = bindingRelationship?.[group.id]?.titles?.[widget.ref_attr_id] ?? {}
          Object.assign(widget, bindConf)

          return widget.access_permission !== 0
        })
      })
      this.$set(this, 'groupList', groupList)
      this.setBindingRelOnField({
        field: this.field,
        data: bindingRelationship
      })

      if (switchEditState) {
        setTimeout(() => {
          this.isEditState = true
        }, 100)
      }
    },
    getBindingTitlesByValue(value) {
      const currOpt = this.options.find((opt) => opt.value === value)
      const bindingTitles = currOpt?.binded_titles || []
      let { groups_schema: groupsSchema, groups_instance } = this.groupsData

      return bindingTitles.reduce(
        (acc, title) => {
          const { group_id } = title
          let currBindingRel = acc.bindingRelationship[group_id]
          if (!currBindingRel) {
            currBindingRel = {
              group_id,
              titles: {}
            }
            acc.bindingRelationship[group_id] = currBindingRel
            // FieldBindingGroup props
            acc.groupList.push(groupsSchema[group_id])
          }

          currBindingRel.titles[title.attr_id] = Object.assign(
            {
              ...title,
              attr_id: title.attr_id,
              values: {}
            },
            currBindingRel.titles?.[title.attr_id] ?? {}
          )

          const instance = groups_instance[group_id].find((item) => item.refAttrId === title.attr_id)
          if (instance) {
            Object.assign(instance.ui_conf, title)
          }

          for (let attr of title.binded_attr_values) {
            const { attr_value_ids, ...other } = attr

            attr_value_ids.forEach((id) => {
              currBindingRel.titles[title.attr_id].values[id] = other
            })
          }

          return acc
        },
        {
          groupList: [],
          bindingRelationship: {}
        }
      )
    },
    getOptDisabled(option) {
      if (typeof this.calcOptDisabledFunc === 'function') {
        return this.calcOptDisabledFunc({
          option,
          value: this.form[this.field]
        })
      }

      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.option-binding-titles-container {
  .option-binding-form-item {
    min-height: initial;
    margin-bottom: 12px;

    &-label {
      display: inline-flex;
      align-items: center;

      ::v-deep .anticon {
        margin-left: 6px;
      }
    }
  }
}
</style>
