<template>
  <div id="package-basic-info-editable" class="basic-info js-basic-info-vm" :data-spm-page="getSpm">
    <div v-disabled="vDisabled" class="basic-info-box common-basic-form-style">
      <div v-if="!initLoadFinish" class="common-spin-box"><a-spin /></div>
      <div v-show="initLoadFinish">
        <tpl_package_copy
          v-if="showCopyButton"
          url="/prosrv/packages/copy"
          :klk_gvars="klk_gvars"
          :package_id="package_id"
          :field-keys="packageFieldKeys"
          :handle-save="handleSaveCopy"
        >
          {{ $t('104033') }}
        </tpl_package_copy>
        <!-- eslint-disable vue/no-use-v-if-with-v-for,vue/no-confusing-v-for-v-if -->
        <div
          v-for="(item, i) in orderArr"
          v-if="showCurrentCommonFieldFn(item)"
          :key="i"
          class="common-basic-box"
        >
          <common-field
            :key="i"
            ref="ref_common_fields"
            v-model="form"
            v-bind="$props"
            :title="item.title"
            :key-flag="item.keyFlag"
            :pkg="package_info"
            :order-list="item.orderList || []"
            :schema-config="schemaConfig"
            :lock-auto-publish="lockAutoPublish"
            :original-data="reqData"
            :lock-auto-warm-up="lockAutoWarmUp"
            :ref-form="refForm"
            :ref-option-group="refOptionGroup"
            :disabled-open-ticket="disabledOpenTicket"
            :combo-confirmation-time="comboConfirmationTime"
            :combo-cancellation-policy="comboCancellationPolicy"
            :option-group="optionGroup"
            :is-publish-with-ai="isPublishWithAI"
            :is-create="isCreate"
            :support-presale="supportPresale"
            :pkg-basic-info-field-tips="pkgBasicInfoFieldTips"
            v-on="item.on"
            @fieldAttributeGroupVN="handleFieldAttributeGroupVN"
            @changeData="onChangeData"
          ></common-field>
        </div>
        <div v-if="$root.isAdmin && $root.isFNB" class="common-copy-wrap">
          <a-button @click="openFnbTemplate">{{ $t('fnb_use_template') }}</a-button>
        </div>
      </div>
    </div>

    <List
      v-if="groupSelectionData.visible"
      :visible.sync="groupSelectionData.visible"
      v-bind="groupSelectionData"
    />

    <div
      id="save_package_info_spm"
      v-bind="$root.bindIHTrack({ type: 'module', spm: 'SavePackageInfo' })"
    ></div>
    <div id="copy_package_info_spm" v-bind="$root.bindIHTrack({ type: 'module', spm: 'CopyPackage' })"></div>
  </div>
</template>

<script>
import CommonField from '@activity/components/common_field'
import tpl_package_copy from '@activity/components/tpl_package_copy'
import { mapState, mapGetters, mapMutations, mapActions } from 'vuex'
import { validatorDefault, getEditLang, scrollElError } from '@activity/utils'
import {
  FNB_TICKET_TYPE as fnb_ticket_types,
  FNB_TICKET_TYPE,
  cancel_policy,
  SUPPORT_WITHOUT_CALENDAR_SUB_CATEGORY_IDS_V2,
  OPEN_TICKET_WITHOUT_CALENDAR,
  ACCESS_PERMISSION
} from '@activity/pages/package/package_const.js'
import moment from 'moment'
import { pmsConfirm } from '@activity/utils'
import { isAdmin } from '@/env'
import common_mixin from '@activity/pages/activityManagement/detailV2/common_mixin'
import List from '@activity/pages/activityManagement/detailV2/components/list/index'
import { reservationProductsMixin } from '@activity/pages/package/mixins/index'
import { getFieldTips } from '@activity/utils/fieldGuideTipsDict'
import baseMixin from '@activity/pages/package/basicInfo/mixins/base/index.js'

const selection_list_mixin = {
  components: {
    List
  },
  computed: {
    ...mapState({
      groupSelectionData: (state) => state.attr.groupSelectionData
    })
  }
}

const open_ticket_mixin = {
  watch: {
    disabledOpenTicket(v, oldV) {
      if (!oldV && v) {
        let { access_permission = 0 } = this.schemaConfig['ticket_type'] || {}
        if (access_permission !== 1) {
          this.form.ticket_type = undefined
        }
      }
    }
  },
  computed: {
    pkgBasicInfoFieldTips() {
      return getFieldTips('pkgBasicInfo')
    },
    templateId() {
      return this.$store.state.categoryInfo?.template_id ?? 0
    },
    // 修改代码需同步到 @activity/pages/activityManagement/act-create/main/package/basic-info.vue
    disabledOpenTicket() {
      // const validCategoryId =
      //   SUPPORT_WITHOUT_CALENDAR_SUB_CATEGORY_IDS.includes(this.subCategoryId) ||
      //   SUPPORT_WITHOUT_CALENDAR_CATEGORY_IDS.includes(this.categoryId)
      const validSubCategoryId = SUPPORT_WITHOUT_CALENDAR_SUB_CATEGORY_IDS_V2.includes(this.subCategoryId)
      // 检查库存类型是否支持
      let validInvType = false
      const currentInventoryType = this.form.inventory_type

      // 后端下发inventory_open_ticket_without_calendar代表白名单活动，可以进行open_ticket_without_calendar
      if (this?.pkgInfos?.inventory_open_ticket_without_calendar && this.isCreate) {
        validInvType = ['INVENTORY', 'OTHERS', 'API'].includes(currentInventoryType)
      } else {
        // 保持原有逻辑，只允许OTHERS和API类型
        validInvType = ['OTHERS', 'API'].includes(currentInventoryType)
      }

      return !(validInvType && validSubCategoryId)
    }
  }
}

const attribute_mixin = {
  data() {
    return {
      fieldAttributeGroupVN: {}
    }
  },
  beforeDestroy() {
    this.fieldAttributeGroupVN = null
  },
  methods: {
    handleFieldAttributeGroupVN({ field, $vm }) {
      this.$set(this, 'fieldAttributeGroupVN', {
        ...this.fieldAttributeGroupVN,
        [field]: $vm
      })

      this.allAttrGroupVNMountedDebounce()
    },
    allAttrGroupVNMountedDebounce: _.debounce(function () {
      // 避免 attr group 在初始化时触发 setActivateFooterWrapper: true
      this.allAttrGroupVNMounted = true
    }, 200),
    getCountObjectByAttributeFieldKey(key) {
      if (this.form.validity_model !== 1 && key === 'activation_validity') {
        return true
      }

      let instance = _.get(this.fieldAttributeGroupVN, key, null)

      if (!instance) {
        return true
      }

      // # to be confirmed
      // sentry-fix:
      // Invalid attempt to iterate non-iterable instance. In order to be iterable, non-array objects must have a [Symbol.iterator]() method.
      const groupsInstance = Object.values(instance.data.group_data?.groups_instance || {})
      if (!Array.isArray(groupsInstance)) {
        return true
      }

      for (let widgets of groupsInstance) {
        if (!Array.isArray(widgets)) {
          continue
        }

        for (let widget of widgets) {
          const { required, hide } = widget.ui_conf

          if (required && !hide && !widget.attr_values.length) {
            return false
          }
        }
      }

      return true
    }
  }
}

export default {
  name: 'PackageBasicInfo',
  components: {
    tpl_package_copy,
    CommonField
  },
  mixins: [
    selection_list_mixin,
    open_ticket_mixin,
    attribute_mixin,
    common_mixin,
    reservationProductsMixin,
    baseMixin
  ],
  inject: [
    'refreshPkgs',
    'refreshPage',
    'reloadPage2provide',
    'rootChangeLoading',
    'setAlertFixed2provide',
    'setBeforeEachSaveConfirmInterceptor'
  ],
  provide: function () {
    return {
      initFloorTimelineData: this.initFloorTimelineData,
      calcAttrGroupDisplayState: this.calcAttrGroupDisplayState,
      vDisabled: this.vDisabled
    }
  },
  props: {
    activity_id: {
      type: [Number, String],
      required: true
    },
    package_id: {
      type: [Number, String],
      required: true
    },
    package_type: {
      type: Number
    }
  },
  data() {
    return {
      get_packages_basic_info: {},
      cancel_policy,
      initLoadFinish: false,
      reqData: null,
      actGlobal: null,
      cacheForm: null, // form的快照
      form: {},
      schemaConfig: {},
      new_package_id: this.package_id,
      lockAutoPublish: false,
      lockAutoWarmUp: false,
      serverTime: '',
      startTimestamp: 0,
      MAX_AUTO_PUBLISH_TIME: 1000 * 60 * 30,
      refForm: {},
      refOptionGroup: {},
      comboConfirmationTime: -1,
      comboCancellationPolicy: -1,

      optionGroup: {},

      supportPresale: false,
      un_schedule_open_ticket: false
    }
  },
  computed: {
    ...mapState({
      isMerchant: 'isMerchant',
      pkgInfos: 'pkgInfos',
      packages: 'packages',
      lockMerchantActEditing: 'lockMerchantActEditing',
      pkgStepStatus: (state) => state.pkgStepStatus
    }),
    ...mapGetters(['isPublishWithAI', 'saveBeforeStatus2getters', 'lockMerchantEditFreeText2getter']),
    pkgBasicInfoFieldTips() {
      return getFieldTips('pkgBasicInfo')
    },
    vDisabled() {
      const deleteElements = [
        {
          selector: 'compound-extend__btn',
          force: true
        }
      ]

      if (this.lockMerchantActEditing) {
        return {
          lock: true,
          deleteElements,
          scope: 'all'
        }
      }

      const excludeElements = this.$store.state.isMC2BD ? [] : ['exclude-disabled']

      if (this.lockMerchantEditFreeText2getter && !this.isCreate) {
        return {
          lock: true,
          defaultScope: 'freeText',
          excludeElements,
          deleteElements
        }
      } else {
        this.setAlertFixed2provide({
          show: false,
          html: ''
        })

        return {
          lock: false
        }
      }
    },
    refer_language() {
      let { lang, ref } = this.$route.query
      ref = ref || 'en_US'

      return lang === ref ? '' : ref
    },
    subCategoryId() {
      let obj = this.$attrs.actGlobal ? this.$attrs.actGlobal : {}
      return obj.categoryInfo && obj.categoryInfo.sub_category_id
    },
    categoryId() {
      let obj = this.$attrs.actGlobal ? this.$attrs.actGlobal : {}
      return obj.categoryInfo && obj.categoryInfo.category_id
    },
    klk_gvars() {
      const packages = (this.pkgInfos && this.pkgInfos.packages) || []
      const package_all = packages.filter((item) => item.product_type !== 1)
      return {
        package_all: package_all
      }
    },
    serverTimestamp() {
      return this.getUTCTimestamp(this.serverTime)
    },
    package_info() {
      let form = _.cloneDeep(this.form)
      return {
        ...form,
        ...this.get_packages_basic_info
      }
    },
    language() {
      return this.$store.state.edit_lang.map((v) => v.language_type)
    },
    hasCreateFields() {
      return !!this.merchant_id
    },
    orderArr() {
      let list = this.initPageData()
      let { product_type, ticket_type } = this.form
      if (product_type === 1) {
        list.splice(1, 0, {
          keyFlag: 'combo',
          title: this.$t('73096'),
          orderList: ['combo_standalone_pkgs'],
          countObj: {
            combo_standalone_pkgs: {
              mapValue: 'combo_standalone_pkgs',
              callback: (arr) => {
                const validate = (arr || []).every((item) => {
                  const valueValidate =
                    Array.isArray(item.value) && item.value.length > 1 && item.value.every((it) => !!it)
                  return valueValidate
                })
                return validate ? true : ''
              }
            }
          }
        })
      }
      // att的活动无论何时都不需要过滤掉预约模块
      const isAttReservation = _.has(this.form, 'reservation_others_cut_off_time')
      if (ticket_type === 1 && !isAttReservation) {
        return list.filter((item) => !(item.keyFlag === 'reservationPolicySetting'))
      }

      return list
    },
    showCopyButton() {
      // 获取是否显示copy按钮的状态
      const { product_type = 0 } = this.form
      const { inventory_type } = this.cacheForm || {}
      return inventory_type != 'INVENTORY' && product_type != 1
    },
    getSpm() {
      const oid = `activity_${this.activity_id || 0}`
      return `PackageInfo?oid=${oid}`
    },
    isCreate: {
      catch: false,
      get: function () {
        return !this.$route.query.package_id
      }
    },
    packageFieldKeys() {
      const { field_items = [] } = this.get_packages_basic_info || {}
      return field_items?.map((ele) => ele?.field_key) || []
    }
  },
  watch: {
    form: {
      handler(v) {
        this.$nextTick(function () {
          this.initFloorTimelineData()
          if (!this.cacheForm) {
            this.cacheForm = _.cloneDeep(this.form)
          } else if (!_.isEqualWith(this.cacheForm, this.form)) {
            this.setActivateFooterWrapper(true)
          }
        })
      },
      deep: true
    },
    'form.package_type': {
      immediate: false,
      handler(v) {
        if (this.isCreate) {
          if ([6, 7, 8].includes(v)) {
            this.form.ticket_type = fnb_ticket_types.fnb_open_ticket
          } else if ([9, 10, 11].includes(v)) {
            this.form.ticket_type = fnb_ticket_types.fnb_fixed_date
          }
        }
      }
    },
    'form.validity_model': {
      immediate: true,
      handler(v) {
        this.computedUsageValidityTag()

        this.resetGroupDataAndSummary(['activation_validity', 'participation_validity'])

        // validity_model 为 1 的时候才需要填写 activation_validity，所以切换需要清空数据
        if (v !== 1 && this.fieldAttributeGroupVN?.activation_validity) {
          this.$set(this.form.activation_validity.group_data, 'groups_instance', null)
          this.fieldAttributeGroupVN.activation_validity.initGroupData()
        }
      }
    },
    'form.product_type': {
      handler(v) {
        if (v === 1 && this.isCreate) {
          this.setFormData(this._combo_default_package_info)
        } else if (v === 0) {
          this.setFormData(this.get_packages_basic_info)
        }
      }
    },
    'form.pkg_merchant': {
      handler(v, oldV) {
        if (v && oldV) {
          this.$nextTick(() => {
            this.form.merchant_confirm = 0
          })
        }
        // combo 时强制更新merchant
        if (v && this.form.product_type === 1) {
          klook.bus.$emit('updateMerchants', v)
        }
      }
    },
    // handle default value
    'form.cancellation_policy'(v) {
      if (v == -1) {
        this.form.cancellation_policy = undefined
      }
      // 选择了活动开始前无条件退改则不可选open date ticket类型
      if (v === this.cancel_policy.cancel_before_start && this.form.ticket_type === 3) {
        this.form.ticket_type = undefined
      }
    },
    'form.inventory_type'(v, oldV) {
      const canTicketTypeEdit =
        +this.schemaConfig?.ticket_type?.access_permission === +ACCESS_PERMISSION['READ_AND_EDIT']
      if (!!canTicketTypeEdit && oldV !== undefined && v !== 'OTHERS') {
        // 白名单活动 && 新建场景 && 切换为INVENTORY类型时,保留ticket_type
        if (this?.pkgInfos?.inventory_open_ticket_without_calendar && this.isCreate && v === 'INVENTORY') {
          return
        }
        this.form.ticket_type = undefined
      }
    },
    // handle default value
    'form.voucher_usage': {
      immediate: true,
      handler(v) {
        if (v == -1) {
          this.form.voucher_usage = 0
        }
      }
    },
    'form.ticket_type': {
      immediate: true,
      handler(v) {
        if (this.openDate && this.form.confirmation_time !== 1) {
          this.form.cancellation_policy = 9
        }

        switch (v) {
          case 1: {
            this.form.validity_model = 0
            this.computedUsageValidityTag()
            this.resetGroupDataAndSummary(['activation_validity', 'participation_validity'])
            break
          }
          case 3: {
            if (this.isPassProducts && this.form.validity_model === 0) {
              this.form.validity_model = undefined
            }
            // if (this.un_schedule_open_ticket) {
            //   this.form.inventory_type = 'OTHERS'
            // }
            break
          }
        }
        // 新建的时候切换product_type可能出现展示不正确的情况
        // 分析原因是因为组件未渲染出来导致$refs获取不到，所以未执行初始化函数
        // 使用nextTick的时候又会导致选项选中状态丢失、所以做这个逻辑处理
        if (this.isCreate) {
          this.$nextTick(() => {
            this.computedUsageValidityTag()
          })
        } else {
          this.computedUsageValidityTag()
        }

        this.usageValidityLinkageFn()
      }
    },
    'form.combo_standalone_pkgs': {
      handler(v) {
        // 阻止watch，防止死循环。
        if (this._stop_combo_standalone_skus_watch) {
          this._stop_combo_standalone_skus_watch = false
          return
        }
        this.debounceGetComboBaseInfo(v)
      },
      deep: true
    },
    '$route.query': {
      handler(q, oldQuery) {
        if (+q.package_id !== +oldQuery.package_id) {
          this.initPkgId(+q.package_id)
          this.getRefDataByPkgId()
        }
      }
    }
  },
  async mounted() {
    klook.bus.$emit('addHandleSave2bus', this.handleSave)
  },
  async created() {
    if (this.$root.isMerchant) {
      this.setBeforeEachSaveConfirmInterceptor({
        confirmSave: {
          fn: this.handleSave.bind(this)
        }
      })
    }

    await this.initPkgId(this.package_id)
    await this.getRefDataByPkgId()

    this.initFloorTimelineData()

    if (this.$root.isHotel) {
      this.package_info.ticket_type = FNB_TICKET_TYPE.fnb_open_ticket
    }

    if (this.form.auto_pub_unpub) {
      let obj = this.form.auto_pub_unpub
      if (obj.package_published) {
        this.lockAutoPublish = true
      } else {
        this.serverTime = this.getServerTime()
        this.startTimestamp = this.getCurrentTimestamp()
        let { published, published_time, suspended_config } = obj
        const { suspended_start_time } = suspended_config
        if (published) {
          // 自动发布或者预热暂停前30分钟都不能操作
          const diffPub = this.getUTCTimestamp(published_time) - this.getCurrentTimestamp()
          const diffSuspend = this.getUTCTimestamp(suspended_start_time) - this.getCurrentTimestamp()

          this.lockAutoPublish = diffPub > 0 && diffPub < this.MAX_AUTO_PUBLISH_TIME
          this.lockAutoWarmUp = diffSuspend > 0 && diffSuspend < this.MAX_AUTO_PUBLISH_TIME
        }
      }
    }
    this.$nextTick(() => {
      this.initLoadFinish = true
    })
  },
  methods: {
    ...mapGetters(['getPackages', 'getServerTime']),
    ...mapActions(['getPkgInfo2actions', 'updatePkgStepStatus2action']),
    ...mapMutations({
      setPackages: 'setPackages',
      setInvalidWidgetTagsOnField: 'attr/setInvalidWidgetTagsOnField',
      setFilterAttrValOptOnField: 'attr/setFilterAttrValOptOnField',
      setDisabledVarOptionOnField: 'attr/setDisabledVarOptionOnField',
      setActivateFooterWrapper: 'setActivateFooterWrapper'
    }),
    validatorDefault: validatorDefault,
    onChangeData() {
      // attr group change data
      if (this.initLoadFinish && this.allAttrGroupVNMounted) {
        this.setActivateFooterWrapper(true)
      }
    },
    usageValidityLinkageFn() {
      // ticket type 与 group usage_validity 的联动限制
      let res = {
        invalid_attr_value_tags: [],
        invalid_widget_tags: []
      }
      const invalidTags = this.form?.usage_validity?.invalid_tags ?? {}
      if (hasOwnProperty.call(invalidTags, this.form.ticket_type)) {
        res = invalidTags[this.form.ticket_type]
      }
      if (
        this?.pkgInfos?.inventory_open_ticket_without_calendar &&
        this.form.ticket_type === 3 &&
        this.form.inventory_type === 'INVENTORY'
      ) {
        res.invalid_widget_tags.push(
          'title_non_available_date',
          'title_other_notes',
          'title_available_weekdays'
        )
        res.invalid_attr_value_tags.push(
          'valid_time_options_a',
          'valid_time_options_b',
          'valid_time_options_c',
          'valid_time_options_g',
          'valid_time_options_f',
          'valid_time_options_h'
        )
      }
      this.setFilterStore({
        field: 'usage_validity',
        invalidWidgetTags: res.invalid_widget_tags,
        filterAttrValOpt: res.invalid_attr_value_tags
      })
    },
    async resetGroupDataAndSummary(list = []) {
      await this.$nextTick()
      list.forEach((field) => {
        this.orderArr.forEach((item, index) => {
          if (item.orderList.includes(field)) {
            this.$refs?.ref_common_fields?.[index]?.resetGroupDataAndSummary?.([field])
          }
        })
      })
    },
    debounceGetComboBaseInfo: _.debounce(async function (standalone = []) {
      await this.getComboBaseInfo(standalone)
    }, 500),
    computedUsageValidityTag() {
      // 对 ticket type 做联动锁定操作
      // 1  valid_time_options_a 中 {{specific date}} 与 {{specific time}} 联动，锁死specific time，不能编辑
      // 2  如果是 3(no calendar) ，ref_field_tag: "attr_item_specific_date" 不能选择 ref_field_tag: "attr_value_participation_date"
      // x_options_x:   title attr value 选项
      // attr_item_x:   var ref
      // attr_value_x:  var select option ref
      let filterAttrValOpt = []
      let disabledVarOption = [
        'valid_time_options_a-attr_item_specific_time',
        'valid_time_options_a-attr_item_specific_time_same_time'
      ]
      if (this.form.ticket_type === 3) {
        // open ticket without calendar
        filterAttrValOpt.push('valid_time_options_f', 'activation_valid_time_options_d')
        disabledVarOption.push('attr_item_specific_date-attr_value_participation_date')

        if (this.isPassProducts) {
          disabledVarOption.push('attr_item_specific_date_2-attr_value_participation_date_2')
        } else {
          filterAttrValOpt.push('activation_valid_time_options_a')
        }
      }

      if (!this.displayValidityModel) {
        setTimeout(() => {
          klook.bus.$emit('valid_time_options_g', { action: 'autoSelect' })
        })
      }

      if (this.form.validity_model === 1) {
        filterAttrValOpt.push('valid_time_options_d', 'valid_time_options_e', 'valid_time_options_f')
        disabledVarOption.push(
          'attr_item_specific_date-attr_value_participation_date',
          'attr_item_specific_date-attr_value_booking_confirmation_date'
        )
      }

      if (this.form.validity_model === 2) {
        filterAttrValOpt.push(
          'valid_time_options_a',
          'valid_time_options_c',
          'valid_time_options_d',
          'valid_time_options_e',
          'valid_time_options_f',
          'valid_time_options_g',
          'valid_time_options_h'
        )
        disabledVarOption.push(
          'attr_item_specific_date-attr_value_participation_date',
          'attr_item_specific_date-attr_value_redemption_date'
        )

        setTimeout(() => {
          klook.bus.$emit('valid_time_options_b', { action: 'autoSelect' })
        })
      }

      const relatedField = ['usage_validity', 'activation_validity', 'participation_validity']

      for (let field of relatedField) {
        this.setFilterStore({
          field,
          filterAttrValOpt,
          disabledVarOption
        })
      }

      this.resetGroupDataAndSummary(relatedField)
    },
    setFilterStore({ field, invalidWidgetTags, filterAttrValOpt, disabledVarOption }) {
      if (invalidWidgetTags) {
        this.setInvalidWidgetTagsOnField({
          field,
          data: invalidWidgetTags
        })
      }

      if (filterAttrValOpt) {
        this.setFilterAttrValOptOnField({
          field,
          data: filterAttrValOpt
        })
      }

      if (disabledVarOption) {
        this.setDisabledVarOptionOnField({
          field,
          data: disabledVarOption
        })
      }
    },
    getStaticFormData() {
      const staticData = {
        pkg_name: [],
        pkg_subname: [],
        pkg_contact: [],
        package_tips: '',
        price_displaying: 0
      }
      for (const key in staticData) {
        staticData[key] = this.form[key]
      }
      return _.cloneDeep(staticData)
    },
    async getComboBaseInfo(standalone = []) {
      // if (!this.isCreate) {
      //   return
      // }
      // 过滤出有效的 combo_standalone_pkgs
      let list = standalone
        .map((item) => item.value)
        .filter((item) => {
          return Array.isArray(item) && item.length === 2 && item.every((item) => !!item)
        })

      const standalone_pkg_ids = list.map((item) => item[1]).join(',')
      if (!standalone_pkg_ids || standalone_pkg_ids === this._cacheIds) {
        return
      }
      const result = await ajax.getBody(ADMIN_API.act.get_combo_default_basic_info, {
        params: {
          combo_activity_id: this.activity_id,
          standalone_pkg_ids,
          language: klook.getEditLang(),
          ref_language: klook.getRefLang(),
          page_from: klook.getPlatformRoleKey(),
          show_standalone_info: true
        }
      })
      if (!result || !result.success || !result.result) {
        // this.$modal.error({
        //   title: 'Error',
        //   content: result.error && result.error.message
        // })
        return
      }
      let data = result.result
      const form = this.initFormData(data)
      this.comboCancellationPolicy = form.cancellation_policy
      // comboConfirmationTime: combo 后的确认时间，用于筛选出可以选择的项目
      this.comboConfirmationTime = form.confirmation_time
      if (!this.isCreate) {
        return
      }
      // 缓存standalone_pkg_ids
      this._cacheIds = standalone_pkg_ids
      // 接口没有返回这两个字段，在此处加上，并使用form已有的值。
      data.field_items.push(
        {
          access_permission: 2,
          field_key: 'product_type',
          required: true,
          value: this.form.product_type
        },
        {
          access_permission: 2,
          field_key: 'combo_standalone_pkgs',
          required: true,
          value: this.form.combo_standalone_pkgs
        }
      )
      this._staticFormData = this.getStaticFormData()
      this.setFormData(data, true)
    },
    showCurrentCommonFieldFn(item) {
      let orderList = item.orderList || []

      return orderList.some((field) => _.get(this.schemaConfig, `${field}.access_permission`, 0) > 0)
    },
    openFnbTemplate() {
      klook.gotoOldAdmin(
        `/act/package/info${location.search}&activity_id=${this.activity_id}`,
        {},
        { click_func: 'openFnbTemplate' }
      )
    },
    initPageData() {
      let arr = [
        {
          orderList: ['product_type']
        },
        {
          keyFlag: 'basicInfo',
          title: this.$t('activity_basic_page_basic_info'),
          orderList: [
            'package_image',
            'pkg_name',
            'package_tips',
            'package_type',
            'pkg_unique_selling_point',
            'pkg_subname',
            'price_displaying',
            'pkg_merchant',
            'merchant_confirm',
            'pkg_contact'
          ],
          countObj: {
            package_image: 'package_image',
            pkg_name: 'pkg_name',
            // pkg_name: {
            //   mapValue: 'pkg_name',
            //   callback: (arr) => {
            //     let list = arr
            //     if (klook.getRefLang() && klook.getRefLang() !== 'en_US') {
            //       list = list.filter((o) => o.language !== klook.getRefLang())
            //     }
            //     let res = list.map((o) => !!_.trim(o.name))
            //     return res.every((o) => o) ? true : ''
            //   }
            // },
            package_tips: 'package_tips',
            package_type: 'package_type',
            pkg_subname: {
              mapValue: 'pkg_subname',
              callback: (arr) => {
                let obj = arr.find((o) => o.language === klook.getEditLang())
                return obj && obj.name
              }
            },
            price_displaying: 'price_displaying',
            pkg_merchant: 'pkg_merchant',
            merchant_confirm: 'merchant_confirm',
            pkg_contact: 'pkg_contact.0.number'
          }
        },
        {
          keyFlag: 'policiesRules',
          title: this.$t(this.$root.isMerchant ? '82054' : 'pkg.policiesRules'),
          orderList: [
            'inventory_type',
            'confirmation_time',
            'auto_suspend_sale',
            'voucher_get_method',
            'dynamic_voucher_code',
            'voucher_type',
            'voucher_usage',
            'voucher_code_level'
          ],
          countObj: {
            confirmation_time: 'confirmation_time',
            inventory_type: 'inventory_type',
            auto_suspend_sale: 'auto_suspend_sale',
            voucher_get_method: 'voucher_get_method',
            dynamic_voucher_code: 'dynamic_voucher_code',
            voucher_type: 'voucher_type',
            voucher_usage: 'voucher_usage',
            voucher_code_level: 'voucher_code_level'
          }
        },

        {
          keyFlag: 'ValiditySetting',
          title: this.$t('30545'),
          orderList: [
            'ticket_type',
            'validity_model',
            'activation_validity',
            'usage_validity',
            'participation_validity'
            // 'field_group_summary'
          ],
          countObj: {
            ticket_type: 'ticket_type',
            validity_model: 'validity_model',
            activation_validity: {
              mapValue: 'activation_validity',
              callback: () => {
                return this.getCountObjectByAttributeFieldKey('activation_validity')
              }
            },
            usage_validity: {
              mapValue: 'usage_validity',
              callback: () => {
                return this.getCountObjectByAttributeFieldKey('usage_validity')
              }
            },
            participation_validity: {
              mapValue: 'participation_validity',
              callback: () => {
                return this.getCountObjectByAttributeFieldKey('participation_validity')
              }
            }
          }
        },

        {
          keyFlag: 'cancellationPolicySetting',
          title: this.$t('cancellation_policy'),
          orderList: ['cancellation_policy'],
          countObj: {
            cancellation_policy: 'cancellation_policy'
          }
        },
        // spa/att 预约
        {
          keyFlag: 'reservationPolicySetting',
          title: this.$t('121699'),
          orderList: [
            'reservation_required',
            'reservation_method',
            'reservation_others_cut_off_time',
            'reservation_other_content',
            'reservation_no_show_policy',
            'reservation_amendment_policy',
            // 非obt配置，用于下面obt配置分组
            // reservation_week_timeslot、reservation_block_out_date、reservation_cut_off_time
            'available_amendment_group',
            'reservation_week_timeslot',
            'reservation_block_out_date',
            'reservation_cut_off_time'
          ],
          countObj: {
            reservation_required: 'reservation_required'
            // reservation_method: 'reservation_method',
            // reservation_no_show_policy: 'reservation_no_show_policy',
            // reservation_amendment_policy: 'reservation_amendment_policy',
            // reservation_week_timeslot: 'reservation_week_timeslot',
            // reservation_block_out_date: 'reservation_block_out_date',
            // reservation_cut_off_time: 'reservation_cut_off_time',
            // reservation_other_content: 'reservation_other_content'
          }
        },
        {
          keyFlag: 'specialSettings',
          title: this.$t('pkg.specialSettings'),
          orderList: [
            'auto_pub_unpub',
            'min_max_bookings',
            'calendar_day',
            'calendar_extend',
            'guarantee_group'
          ],
          countObj: {
            auto_pub_unpub: 'auto_pub_unpub',
            min_max_bookings: 'min_max_bookings',
            calendar_day: 'calendar_day',
            calendar_extend: 'calendar_extend',
            guarantee_group: 'guarantee_group'
          }
        },
        {
          keyFlag: 'TopUpSettings',
          title: this.$t('172380'),
          orderList: ['esim_topup_info'],
          countObj: {
            esim_topup_info: 'esim_topup_info'
          }
        },
        {
          keyFlag: 'checkItems',
          title: this.$t('pkg.checkItems'),
          orderList: [
            'show_account_type',
            'transferable',
            'escrow_guarantee',
            'does_it_include',
            'sensitive_info',
            'sg_culture_pass_id'
          ],
          countObj: {
            show_account_type: 'show_account_type',
            transferable: 'transferable',
            escrow_guarantee: 'escrow_guarantee',
            does_it_include: {
              mapValue: 'does_it_include',
              callback: (val) => {
                return val === 8 ? undefined : val
              }
            },
            sensitive_info: 'sensitive_info.0',
            sg_culture_pass_id: 'sg_culture_pass_id'
          }
        }
      ]
      return arr
    },
    initFloorTimelineData: _.debounce(function () {
      let { orderArr } = this
      let obj = {
        floorData: {
          list: orderArr.map((item) => {
            let obj = _.merge({}, item, this.getCountObj(item, item.keyFlag))
            return obj
          })
        }
      }

      klook.bus.$emit('scrollToSuggestField2bus', {
        data: obj.floorData.list
      })

      klook.bus.$emit('floorTimeline2bus', obj)
      return obj
    }, 300),
    // 看着脑瓜疼，待重写。。。
    getCountObj(data, key) {
      let { form, schemaConfig } = this
      let { countObj = {} } = data
      let countKeys = Object.entries(countObj)
      let num = 0,
        count = 0
      let requireNum = 0,
        requireCount = 0
      let notFilled = []
      countKeys.forEach((arr) => {
        let basic_attr_config = schemaConfig[arr[0]]
        if (basic_attr_config && basic_attr_config.access_permission) {
          count++
          let val = arr[1]
          if (typeof val === 'string') {
            val = _.get(form, val)
          } else {
            val = val.callback(_.get(form, val.mapValue))
          }
          let isFilled = val || typeof val === 'number' || typeof val === 'boolean'
          isFilled && num++
          if (basic_attr_config.required) {
            requireCount++
            isFilled && requireNum++
          }

          const { field_key, required, config } = basic_attr_config
          if (
            !isFilled ||
            (field_key === 'guarantee_group' &&
              toString.call(val) === '[object Object]' &&
              !Object.values(val).filter((item) => item).length) ||
            (field_key === 'pkg_contact' &&
              Array.isArray(val) &&
              val.every((item) => !(item.media && item.name)))
          ) {
            notFilled.push({
              field: field_key,
              required,
              config
            })
          }
        }
      })
      //特殊处理 start
      if (key == 'policiesRules') {
        // 修复count / num 可能出现负数或不准确的情况
        // 在选择了库存模型的情况下count / num / requireCount / requireNum 可能出现计算两次的的情况
        let mapList = ['voucher_type', 'voucher_code_level', 'voucher_usage']
        switch (form['inventory_type']) {
          case 'API':
            mapList = ['voucher_usage']
            ;['voucher_type', 'voucher_code_level'].forEach((k) => {
              count--
              form[k] !== undefined && num--
              if (schemaConfig[k].required) {
                requireCount--
                form[k] !== undefined && requireNum--
              }
            })
            break
          case 'INVENTORY':
            mapList = ['voucher_code_level', 'voucher_usage']
            ;['voucher_type'].forEach((k) => {
              count--
              form[k] !== undefined && num--
              if (schemaConfig[k].required) {
                requireCount--
                form[k] !== undefined && requireNum--
              }
            })
            break
          default:
          //
        }
        if (form['voucher_get_method'] !== 0) {
          mapList.forEach((k) => {
            if (schemaConfig[k] && schemaConfig[k].access_permission) {
              count--
              form[k] !== undefined && num--
              if (schemaConfig[k].required) {
                requireCount--
                form[k] !== undefined && requireNum--
              }
            }
          })
        }
      }
      //特殊处理 end
      if (key == 'reservationPolicySetting') {
        const needReservation = this.form.reservation_required === 1
        const isAttReservation = _.has(this.form, 'reservation_others_cut_off_time')
        const spaReservationKeys = [
          'reservation_method',
          'reservation_other_content',
          'reservation_no_show_policy',
          'reservation_amendment_policy',
          'reservation_week_timeslot',
          'reservation_block_out_date',
          'reservation_cut_off_time'
        ]
        const attReservationKeys = [
          'reservation_method',
          'reservation_others_cut_off_time',
          'reservation_other_content',
          'reservation_required'
        ]

        const keys = isAttReservation ? attReservationKeys : spaReservationKeys
        if (needReservation) {
          keys.forEach((k) => {
            const [n, c] = this.getCountNumer(k)
            count += c
            num += n

            if (schemaConfig[k]?.required) {
              requireCount += c
              requireNum += n
            }
          })
        }
      }
      let obj = {
        num,
        count,
        requireNum,
        requireCount,
        require: !!requireCount,
        notFilled,
        id: key // id 用于 floorTimeline/index.vue -> a-anchor-link href
      }
      return obj
    },
    getCountNumer(key) {
      const value = _.get(this.form, key)
      const viaKlookAppOther = this.form.reservation_method === 2
      let num = 0
      let count = 0

      switch (key) {
        case 'reservation_method':
          num = value ? 1 : 0
          count = 1
          break
        case 'reservation_other_content':
          num =
            ((value.note || '').trim() ||
              !(value.contact || []).some((item) => {
                const { content = '', type } = item
                return !content.trim() || !type
              })) &&
            viaKlookAppOther
              ? 1
              : 0
          count = viaKlookAppOther ? 1 : 0
          break
        case 'reservation_no_show_policy':
          num = !viaKlookAppOther && value ? 1 : 0
          count = !viaKlookAppOther ? 1 : 0
          break
        case 'reservation_amendment_policy':
          num =
            !viaKlookAppOther &&
            value.time_unit &&
            value.time_num &&
            this.form.reservation_no_show_policy === 1
              ? 1
              : 0
          count = !viaKlookAppOther && this.form.reservation_no_show_policy === 1 ? 1 : 0
          break
        case 'reservation_week_timeslot':
          num =
            !viaKlookAppOther &&
            value.every((item) => {
              const { timeslot, week_day } = item
              const checkTimeslot = timeslot.every((timeslot) => {
                const { start, end } = timeslot
                return start && end
              })

              const checkWeekDay = week_day.every((week_day) => {
                return typeof week_day === 'number'
              })

              return timeslot.length && week_day.length && checkTimeslot && checkWeekDay
            })
              ? 1
              : 0
          count = !viaKlookAppOther ? 1 : 0
          break
        case 'reservation_block_out_date':
          num =
            !viaKlookAppOther &&
            !value.some((item) => {
              const [start, end] = item
              return !start && !end
            })
              ? 1
              : 0
          count = !viaKlookAppOther ? 1 : 0
          break
        case 'reservation_cut_off_time':
          num = !viaKlookAppOther && value.time_unit && value.time_num ? 1 : 0
          count = !viaKlookAppOther ? 1 : 0
          break
        case 'reservation_others_cut_off_time':
          num =
            viaKlookAppOther &&
            ((value?.type === 2 && value.time_config?.time_unit && value.time_config?.time_num) ||
              value?.type === 1)
              ? 1
              : 0
          count = viaKlookAppOther ? 1 : 0
      }

      return [num, count]
    },
    async getPackagesBasicInfo(params = {}) {
      return await ajax.get(ADMIN_API.act.get_packages_basic_info, { params })
    },
    async getRefDataByPkgId() {
      if (this.isMerchant) {
        return
      }

      const { package_id, refer_language } = this
      if (package_id && refer_language) {
        let result = await this.getPackagesBasicInfo({
          package_id: +package_id,
          language: refer_language,
          refer_language: klook.getEditLang(),
          page_from: klook.getPlatformRoleKey()
        })

        let formData = this.initFormData(result || {})
        this.$set(this, 'refForm', { ...formData })
        this.refOptionGroup = result?.option_group || {}
      }
    },
    initFormData({ field_items = [] }) {
      let obj = {}
      field_items.forEach((item) => {
        obj[item.field_key] = item.value
      })
      return _.cloneDeep(obj)
    },
    async initPkgId(package_id) {
      let reqData = await this.getPkgInfo2actions({
        activity_id: this.activity_id,
        refresh: true
      })
      klook.bus.$emit('updatePkgInfos2bus', reqData)
      this.reqData = reqData

      let params = {
        package_id,
        language: klook.getEditLang(),
        refer_language: this.refer_language,
        page_from: klook.getPlatformRoleKey()
      }

      this.defaultPackageInfo = reqData.default_package_info
      let result = package_id ? await this.getPackagesBasicInfo(params) : this.defaultPackageInfo

      this.get_packages_basic_info = result || {}
      this._combo_default_package_info = reqData.combo_default_package_info || {}
      if (result) {
        this.merchant_id = result.merchant_id
        this.supportPresale = result.support_presale
        this.un_schedule_open_ticket = result.un_schedule_open_ticket
        this.initGroupData(result)
        await this.setFormData(this.get_packages_basic_info)
      }
    },
    initGroupData(result) {
      const groupsData = result?.option_group?.group_data ?? null

      if (!groupsData) {
        this.optionGroup = {}
        return
      }

      if (!groupsData.groups_instance) {
        groupsData.groups_instance = {}
      }

      groupsData.groups_summary.forEach((group) => {
        let { widgetsData } = this.getCurrentGroupData({
          group_id: group.id,
          data: groupsData,
          getEditData: false
        })
        groupsData.groups_instance[group.id] = widgetsData
      })

      this.optionGroup = result.option_group
    },
    getCurrentTimestamp() {
      return this.getUTCTimestamp(moment().format('YYYY-MM-DD HH:mm:ss'))
    },
    getUTCTimestamp(date) {
      return +moment.utc(date).valueOf() || 0
    },
    async publishValidator() {
      if (this.lockAutoPublish || !this.package_info.auto_pub_unpub) {
        // If start locking, it cannot update, pass
        return true
      }
      let currentServerTimestamp = this.getCurrentTimestamp() - this.startTimestamp + this.serverTimestamp
      let diff =
        this.getUTCTimestamp(this.package_info.auto_pub_unpub.published_time) - currentServerTimestamp
      if (diff > 0 && diff < this.MAX_AUTO_PUBLISH_TIME) {
        await this.$modal.info({
          title: this.$t('auto_publish_unpublish_tips_3')
        })
        return false
      }
      return true
    },
    async invalidAutoPublish() {
      let autoPublishValid = await this.publishValidator()
      if (!autoPublishValid) {
        await this.refreshPkgs()
        setTimeout(() => {
          this.refreshPage()
        })
        return true
      }
      return false
    },
    async checkReservation(data) {
      const section = data.section
      if (section != 'reservation_config') {
        return true
      }
      const resp = await ajax.postBody(ADMIN_API.act.check_reservation, {
        data
      })

      if (resp && resp.success) {
        const result = resp.result || {}
        const need_user_confirm = result.need_user_confirm
        if (need_user_confirm) {
          const cofirm = await pmsConfirm.call(this, {
            content: result.note
          })
          return cofirm
        }
        return true
      }
      return false
    },
    async postCopyData(data, isCopyFrom) {
      const resp = await ajax.postBody(ADMIN_API.act.copy_package, {
        data,
        noDefaultResponseInterceptor: true
      })
      if (resp.success) {
        if (isCopyFrom) {
          const target = resp.result?.target ?? []
          this.$router.replace({
            path: `/act/package/info/${this.activity_id}`,
            query: {
              ...this.$route.query,
              package_id: target[0]
            }
          })
          await this.refreshPkgs()
          setTimeout(() => {
            this.$store.commit('attr/setGroupSelectionStore')
            this.setActivateFooterWrapper(false)
            this.refreshPage()
          })
        } else {
          this.$message.success(this.$t('global_success'))
        }
      } else {
        this.$root.trackIHEvent('#copy_package_info_spm', {
          err_message: resp?.error?.message ?? 'CopyPackage Error'
        })
      }
    },
    async handleSaveCopy({ type, value, section = 'all' }) {
      if (await this.invalidAutoPublish()) {
        return
      }
      const isCopyFrom = type === 2
      const source = isCopyFrom ? value : this.package_id
      const target = isCopyFrom ? (!this.package_id ? [] : [+this.package_id]) : value
      const data = {
        source,
        activity_id: +this.activity_id,
        edit_language: getEditLang(),
        target,
        section
      }
      const checkReservation = await this.checkReservation(data)

      if (!checkReservation) {
        return
      }
      this.postCopyData(data, isCopyFrom)
    },
    validGuaranteeGroup(rule, value, callback) {
      // 最小值为必填项
      if (this.form.ticket_type === 1) {
        const { min_group = 0, max_group = 0 } = this.form.guarantee_group
        // 允许max_group 为空, 最小为1
        if (_.isNumber(min_group) && min_group > 0) {
          if (_.isNumber(max_group) && max_group > 0 && max_group < min_group) {
            callback(new Error(__('81816')))
          } else {
            callback()
          }
        } else if (_.isNumber(max_group) && min_group === 0 && _.isNumber(max_group) && max_group > 0) {
          callback(new Error(__('85441')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    validMinAndMaxBooking(rule, value, callback) {
      const { min_participants, max_participants } = this.form.min_max_bookings

      if (
        _.isNumber(min_participants) &&
        _.isNumber(max_participants) &&
        min_participants >= 0 &&
        max_participants >= 0
      ) {
        if (max_participants === 0) {
          callback(new Error(__('80128')))
        } else {
          callback()
        }
      } else {
        callback(new Error('Please input number >= 0'))
      }
    },
    validLevy(rule, value, callback) {
      if (this.form.does_it_include === 8) {
        callback(new Error('Please fill this form'))
      } else {
        callback()
      }
    },
    validMerchantContact(rule, value, callback) {
      if (!_.isArray(value)) {
        this.formValid = false
      } else {
        this.formValid = value.every((item, index) => {
          const isCustom = item.media == 'customized'
          if (!rule.required && value.length === 1 && index === 0) {
            // 非必填的时候，对于第一个对象：
            // true: 都填 或 都不填 或 只填了media
            // false: 只填了number
            return !(_.isEmpty(item.media) && !_.isEmpty(item.number))
          }

          if (isCustom) {
            return !(_.isEmpty(item.media) || _.isEmpty(item.number) || _.isEmpty(item.title))
          }

          // 对于其它对象：
          // true: 都填
          // false: 只填了某一个 或 都不填
          return !(_.isEmpty(item.media) || _.isEmpty(item.number))
        })
        if (this.formValid) {
          callback()
        } else {
          callback(new Error('Please input contact'))
        }
      }
    },
    calcAttrGroupDisplayState(field, form) {
      switch (field) {
        case 'activation_validity':
          return form.validity_model === 1
        case 'usage_validity':
          return form.ticket_type !== undefined
        case 'participation_validity':
          return form.validity_model === 2
      }

      return true
    },
    handleValidateTtAndInventory({ ticketType, inventoryType, cancellationPolicy }) {
      if (
        cancellationPolicy === this.cancel_policy.cancel_before_start &&
        inventoryType === 'PIC' &&
        ticketType === OPEN_TICKET_WITHOUT_CALENDAR
      ) {
        this.$error({
          content: 'Do not select like this',
          title: __('global_confirm')
        })
        return false
      }
      return true
    },
    handleSavingPkgValidate() {
      const data = this.fmtPostData()
      const { field_items } = data
      const { value: ticketType } = field_items.find((ele) => ele.field_key === 'ticket_type') || {}
      const { value: inventoryType } = field_items.find((ele) => ele.field_key === 'inventory_type') || {}
      const { value: cancellationPolicy } =
        field_items.find((ele) => ele.field_key === 'cancellation_policy') || {}

      const validateTtAndInventoryRes = this.handleValidateTtAndInventory({
        ticketType,
        inventoryType,
        cancellationPolicy
      })
      if (!validateTtAndInventoryRes) {
        return false
      }
      const { ticket_type: oldTicketType, inventory_type: oldInventoryType } = this.cacheForm || {}
      // 如果没有改动到ticketType和inventoryType，跳过；只要改动了一个，就继续
      if (ticketType === oldTicketType && inventoryType === oldInventoryType) {
        return true
      }
      // 11.02 增加当tt=3，出库方式为API时的提醒校验 且是新od
      // 新建的时候不用校验un_schedule_open_ticket，编辑的时候需要
      if (
        ticketType === OPEN_TICKET_WITHOUT_CALENDAR &&
        inventoryType === 'API' &&
        (this.isCreate || !!this.un_schedule_open_ticket)
      ) {
        return new Promise((resolve) => {
          let secondsToGo = 5
          const title = this.$t('110178')
          const htmlContentString = this.$t('110179')
          const modal = this.$confirm({
            title,
            content: <div domPropsInnerHTML={htmlContentString}></div>,
            okText: `${this.$t('49826')} ${secondsToGo > 0 ? secondsToGo : ''}`,
            cancelText: `${this.$t('73070')} ${secondsToGo > 0 ? secondsToGo : ''}`,
            okButtonProps: {
              props: {
                disabled: !!secondsToGo > 0
              }
            },
            cancelButtonProps: {
              props: {
                disabled: !!secondsToGo > 0
              }
            },
            onOk() {
              setTimeout(() => {
                clearInterval(interval)
                modal.destroy()
                resolve(true)
              })
            },
            onCancel() {
              setTimeout(() => {
                clearInterval(interval)
                modal.destroy()
                resolve(false)
              })
            }
          })
          const interval = setInterval(() => {
            secondsToGo -= 1
            modal.update({
              okText: `${this.$t('49826')} ${secondsToGo > 0 ? `(${secondsToGo})` : ''}`,
              cancelText: `${this.$t('73070')}${secondsToGo > 0 ? `(${secondsToGo})` : ''}`,
              okButtonProps: {
                props: {
                  disabled: !!secondsToGo > 0
                }
              },
              cancelButtonProps: {
                props: {
                  disabled: !!secondsToGo > 0
                }
              }
            })
          }, 1000)
          setTimeout(() => {
            clearInterval(interval)
          }, secondsToGo * 1000)
        })
      } else {
        return true
      }
    },
    async updatePackage(modify_id) {
      let published_time = _.get(this.form, 'auto_pub_unpub.published_time', '')
      if (
        this.form.published &&
        this.form.auto_pub_unpub.is_set &&
        published_time &&
        this.getUTCTimestamp(published_time) - this.getCurrentTimestamp() > 0
      ) {
        this.$modal.info({
          title:
            'Can not set auto-publish for an already published package, please unpublish current package first.'
        })
        klook.abort()
      }
      let data = this.fmtPostData()
      let resp = await ajax.postBody(
        ADMIN_API.act.save_package_basic_info,
        {
          data: {
            ...data,
            modify_id,
            language: getEditLang()
          },
          noDefaultResponseInterceptor: true
        },
        {
          loading: true
        }
      )
      // 特殊错误不刷新页面
      if (resp && !resp?.success && resp?.error?.code === '50085') {
        const errorDom = document.getElementById('IsCPTypeFixedDate')
        errorDom?.scrollIntoView?.({
          behavior: 'smooth',
          block: 'center'
        })
        return
      }
      const extObj = {
        save_type: this.saveBeforeStatus2getters ? 1 : 0,
        err_message: resp?.success ? '' : resp?.error?.message || 'Error: false'
      }
      this.$root.trackIHEvent('.js-edit-save-btn', extObj)
      const success = resp?.success ?? false
      if (!success) {
        await this.refreshPkgs()
        this.setActivateFooterWrapper(false)
        this.routerInfo()
        this.$nextTick(() => {
          this.refreshPage()
        })
        this.$root.trackIHEvent('#save_package_info_spm', {
          err_message: resp?.error?.message ?? 'SavePackageInfo Error'
        })
        return false
      }

      // 更新 store
      this.setPackages(
        this.getPackages().map((item) => {
          if (item.package_id === data.package_id) {
            return {
              ...item,
              ...data
            }
          }
          return item
        })
      )
      return true
    },
    async addPackage(data = null) {
      let confirm = true
      if (this.form.product_type === 1) {
        this.rootChangeLoading(false)
        confirm = await new Promise((resolve) => {
          this.$modal.confirm({
            title: 'Notice',
            content: this.$t('73101'),
            okText: this.$t('73102'),
            cancelText: this.$t('73103'),
            icon: () => <a-icon type="info-circle" style="color: #437DFF" />,
            onOk() {
              resolve(true)
            },
            onCancel() {
              resolve(false)
            }
          })
        })
      }
      if (!confirm) {
        return false
      }
      this.rootChangeLoading(true)
      data = data ? data : this.fmtPostData()
      let res = await ajax.postBody(
        ADMIN_API.act.create_package,
        {
          noDefaultResponseInterceptor: true, // 获取error.message
          data
        },
        {
          msgOpt: {
            isModal: true
          }
        }
      )
      const { result } = res || {}
      const extObj = {
        save_type: this.saveBeforeStatus2getters ? 1 : 0,
        err_message: res?.success ? '' : res?.error?.message || 'Error: false'
      }
      this.$root.trackIHEvent('.js-edit-save-btn', extObj)
      if (result && result.package_id) {
        this.new_package_id = result.package_id
        this.cacheForm = _.cloneDeep(this.form)
        return true
      } else {
        return false
      }
    },
    routerInfo() {
      let package_type = (this.get_packages_basic_info && this.get_packages_basic_info.package_type) || ''
      let query = {
        ...this.$route.query,
        package_id: '' + this.new_package_id, //保证package_id类型一致
        package_type
      }
      if (!_.isEqual(query, this.$route.query)) {
        // 旧数据存在需要重新选 package_type 的情况
        this.$router.replace({
          path: `/act/package/info/${this.activity_id}`,
          query
        })
      }
    },
    async getValidateRes() {
      let ref_common_fields = this.$refs.ref_common_fields
      let validateRes = ref_common_fields
        ? await Promise.all(
            ref_common_fields.map(async (o) => {
              return o.validate(true)
            })
          )
        : [true]

      ref_common_fields = null
      return validateRes
    },
    async handleSave() {
      let modify_id = klook.getModifyId()
      if (await this.invalidAutoPublish()) {
        return
      }
      let validateRes = await this.getValidateRes()
      if (validateRes.every((item) => item)) {
        if (
          isAdmin &&
          !this.isCreate &&
          this.cacheForm.pkg_merchant &&
          this.cacheForm.pkg_merchant !== this.form.pkg_merchant
        ) {
          let temp = await pmsConfirm.call(this, {
            content: this.$t('48159')
          })

          if (!temp) {
            return
          }
        }

        const validateConfirm = await this.handleSavingPkgValidate()
        if (!validateConfirm) {
          return
        }

        this.rootChangeLoading(true)
        if (this.isCreate) {
          let isTrue = await this.addPackage()
          if (!isTrue) {
            this.rootChangeLoading(false)
            return
          } else {
            this.routerInfo()
          }
          // klook.bus.$emit('initQueryData2bus') // 更新活动状态
        }
        if (!this.isCreate && !(await this.updatePackage(modify_id))) {
          return false
        }
        if (this.isCreate || !this.$store.state.pkgStepStatus.package_basic_info) {
          await this.updatePkgStepStatus2action({
            activity_id: this.activity_id,
            package_id: this.new_package_id,
            language: getEditLang(),
            status: 1,
            step: 'package_basic_info'
          })
        }
        // klook.bus.$emit('updatePkgInfos2bus', null, this.new_package_id)
        await ajax.get(
          ADMIN_API.act.modify_completed,
          {
            params: {
              snapshot_modify_id: modify_id
            }
          },
          ajax.sucOptions
        )
        this.rootChangeLoading(false)
        this.setActivateFooterWrapper(false)
        this.refreshPage() //配合后端说强刷，上面bus更新数据就可以不用了
      } else {
        this.$message.warn(__('package_error_mandatory'))
        this.$nextTick(() => {
          scrollElError()
        })
        return
      }
    },
    async autoUpdatePackageStepStatus() {
      const { isMerchant, pkgStepStatus, isCreate, activity_id, package_id } = this
      if (
        !isMerchant ||
        isCreate ||
        _.get(pkgStepStatus, 'package_basic_info', 0) ||
        !this.checkRequiredSchemaConfigValue()
      ) {
        return
      }

      await this.updatePkgStepStatus2action({
        activity_id,
        package_id,
        language: getEditLang(),
        status: 1,
        step: 'package_basic_info'
      })
      klook.bus.$emit('updatePkgInfos2bus', null, package_id)
    }
    // next methods
  }
}
</script>

<style lang="scss" scoped>
.basic-info {
  .basic-info-box {
    padding-bottom: 0;
  }
}
</style>
