import { voucher_usages } from '@activity/pages/package/package_const.js'
import { getComboNameI18ns } from '@activity/utils'

const spaFileds = [
  'reservation_amendment_policy',
  'reservation_week_timeslot',
  'reservation_block_out_date',
  'reservation_cut_off_time',
  'reservation_other_content'
]
const attReservationFields = ['reservation_other_content', 'reservation_others_cut_off_time']
// 区分spa与att reservation的字段
const attReservationUniqueField = 'reservation_others_cut_off_time'
export default {
  beforeDestroy() {
    this._staticFormData = null
  },
  methods: {
    async initObConfig({ field_items = [] }) {
      let result = field_items
      this.schemaConfig = {
        ...result.reduce((acc, curr) => {
          let validator
          switch (curr.field_key) {
            case 'pkg_contact': {
              validator = this.validMerchantContact
              break
            }
            case 'does_it_include': {
              validator = this.validLevy
              break
            }
            case 'min_max_bookings': {
              validator = this.validMinAndMaxBooking
              break
            }
            case 'combo_standalone_pkgs':
              validator = (rule, value, callback) => {
                const validate = value.every((item) => {
                  const valueValidate =
                    Array.isArray(item.value) && item.value.length > 1 && item.value.every((it) => !!it)
                  return valueValidate
                })
                if (!validate && this.isCreate) {
                  callback(new Error('Please fill this form'))
                } else {
                  callback()
                }
              }
              break
            case 'escrow_guarantee_photo':
              validator = (rule, value, callback) => {
                if (!this.form.escrow_guarantee) {
                  return callback()
                }
                if (rule.required && (!Array.isArray(value) || value.length === 0)) {
                  callback(new Error('Please fill this form'))
                } else {
                  callback()
                }
              }
              break
            case 'voucher_usage':
              validator = (rule, value, callback) => {
                const { print_mobile_voucher, 80333: print_mobile_offline_voucher } = voucher_usages
                if (this.form.reservation_method === 1 && value === print_mobile_offline_voucher) {
                  return callback(new Error(__('121732')))
                }
                if ([print_mobile_voucher, print_mobile_offline_voucher].includes(this.form.voucher_usage)) {
                  return callback()
                }

                callback(new Error('Please fill this form'))
              }
              break
            case 'package_image':
              validator = (rule, value, callback) => {
                if (!rule.required || (value && value.url)) {
                  return callback()
                }

                callback(new Error('Please upload images!'))
              }
              break
            case 'guarantee_group':
              validator = this.validGuaranteeGroup
              break
            case 'reservation_method':
              validator = this.reservationMethodValidator
              break
            case 'reservation_no_show_policy':
              validator = this.reservationNoShowPolicyValidator
              break
            case 'reservation_amendment_policy':
            case 'reservation_cut_off_time':
              validator = this.reservationAmendmentPolicyValidator
              break
            case 'reservation_others_cut_off_time':
              validator = this.reservationOthersCutOffTimeValidator
              break
            case 'reservation_block_out_date':
              validator = this.reservationBlockOutDateValidator
              break
            case 'reservation_week_timeslot':
              validator = this.reservationWeekTimeslotValidator
              break
            case 'reservation_other_content':
              validator = this.reservationOtherContentValidator
              break
            case 'voucher_code_level':
              validator = this.voucherCodeLevelValidator
              break
            case 'show_account_type':
              validator = this.showAccountTypeValidator
              break
            default:
              validator = this.validatorDefault
          }

          let richTips = this.pkgBasicInfoFieldTips.find((item) => item.field === curr.field_key) || ''

          return {
            ...acc,
            [curr.field_key]: {
              ...curr,
              config: (curr.config && JSON.parse(curr.config)) || {},
              richTips,
              ...(curr.field_key === 'pkg_name' // 只有套餐名才是多语言组件
                ? {
                    onlyEditLanguage: this.$root.isMerchant,
                    required: curr.required,
                    requiredEn: true
                  }
                : {
                    // 其余的需要有对应的 rules 去判断
                    rules: {
                      trigger: 'blur',
                      required: curr.required,
                      validator
                    }
                  })
            }
          }
        }, {})
      }

      this.autoUpdatePackageStepStatus()
    },
    checkRequiredSchemaConfigValue() {
      const schemaConfig = this.schemaConfig || {}
      for (const key in schemaConfig) {
        const element = schemaConfig[key]
        const { value, required = true, access_permission = 0 } = element || {}
        if (access_permission === 0 || !required) {
          continue
        }
        if (_.isNil(value) || value === '') {
          return false
        }
      }
      return true
    },
    showAccountTypeValidator(rule, value, callback) {
      if (this.form.reservation_method === 1 && value != 1) {
        return callback(new Error(__('121733')))
      }

      if (rule.required && (value === '' || undefined === value)) {
        callback(new Error(__('package_error_mandatory')))
      } else {
        callback()
      }
    },
    voucherCodeLevelValidator(rule, value, callback) {
      if (this.form.reservation_method === 1 && value === 'UnitTypeVoucher') {
        return callback(new Error(__('121731')))
      }

      if (rule.required && (value === '' || undefined === value)) {
        callback(new Error(__('package_error_mandatory')))
      } else {
        callback()
      }
    },
    reservationOtherContentValidator(rule, value, callback) {
      const required = rule.required
      let validate = false
      const { contact, note } = value

      const list = contact.map((contact) => {
        const { content = '', type } = contact
        if (type === 3) {
          return !/^(http|https):\/\/([\w.]+\/?)\S*/.test(content)
        }
        return false
      })

      const hasError = list.some((item) => item)

      if (required) {
        const checkContact = contact.every((contact) => {
          const { content = '', type } = contact
          return content.trim() && type
        })

        validate = hasError ? false : checkContact || note.trim()
      } else {
        validate = true
      }

      if (!validate) {
        const msg = hasError ? ' ' : 'Please fill this form'
        return callback(new Error(msg))
      }
      callback()
    },
    reservationWeekTimeslotValidator(rule, value, callback) {
      const required = rule.required

      let validate = false
      if (required) {
        validate = value.every((item) => {
          const { timeslot, week_day } = item
          const checkTimeslot = timeslot.every((timeslot) => {
            const { start, end } = timeslot
            // moment(start, 'HH:mm').valueOf() <= moment(end, 'HH:mm').valueOf()
            return start && end
          })

          const checkWeekDay = week_day.every((week_day) => {
            return typeof week_day === 'number'
          })

          return timeslot.length && week_day.length && checkTimeslot && checkWeekDay
        })
      } else {
        validate = value.every((item) => {
          const { timeslot, week_day } = item
          const checkTimeslot = timeslot.every((timeslot) => {
            const { start, end } = timeslot
            return start && end
          })

          const checkWeekDay = week_day.every((week_day) => {
            return typeof week_day === 'number'
          })

          if (checkTimeslot) {
            return checkWeekDay && week_day.length
          }

          if (checkWeekDay && week_day.length) {
            return checkTimeslot
          }

          const emptyTimeslot = timeslot.every((timeslot) => {
            const { start, end } = timeslot
            return !start && !end
          })

          const enptyWeekDay = week_day.every((week_day) => {
            return !!week_day
          })
          return emptyTimeslot && enptyWeekDay
        })
      }

      if (!validate) {
        return callback(new Error('Please fill this form'))
      }

      callback()
    },
    reservationBlockOutDateValidator(rule, value, callback) {
      const required = rule.required
      if (
        required &&
        value.some((item) => {
          const [start, end] = item
          return !start && !end
        })
      ) {
        return callback(new Error('Please fill this form'))
      }

      if (!required) {
        if (
          value.every((item) => {
            const [start, end] = item
            return (start && end) || (!start && !end)
          })
        ) {
          return callback()
        } else {
          return callback(new Error('Please fill this form'))
        }
      }
      callback()
    },
    reservationAmendmentPolicyValidator(rule, value, callback) {
      const { time_num, time_unit } = value
      const required = rule.required
      if (required && (!time_num || !time_unit)) {
        return callback(new Error('Please fill this form'))
      }

      if (!required) {
        if ((time_num && time_unit) || (!time_num && !time_unit)) {
          return callback()
        } else {
          return callback(new Error('Please fill this form'))
        }
      }
      callback()
    },
    reservationOthersCutOffTimeValidator(rule, value, callback) {
      const { type, time_config } = value
      const { time_num, time_unit } = time_config || {}
      const dontNeedTimeConfig = type === 1
      const needTimeConfig = type === 2
      const isValid = (needTimeConfig && time_num && time_unit) || dontNeedTimeConfig
      if (isValid) {
        return callback()
      } else {
        return callback(new Error('Please fill this form'))
      }
    },
    reservationMethodValidator(rule, value, callback) {
      if (!rule.required) {
        return callback()
      }
      if (!value) {
        return callback(new Error('Please fill this form'))
      }
      callback()
    },
    reservationNoShowPolicyValidator(rule, value, callback) {
      if (!rule.required) {
        return callback()
      }
      if (!value) {
        return callback(new Error('Please fill this form'))
      }
      callback()
    },
    async setFormData(result, isCombo = false) {
      const {
        print_mobile_voucher,
        80294: evoucher_offline_voucher,
        80333: print_mobile_offline_voucher
      } = voucher_usages
      let formData = this.initFormData(result)
      // 特殊字段处理 start
      let cpForm = _.merge({}, formData, {
        sensitive_info: formData.sensitive_info ? formData.sensitive_info.split(',') : [],
        // - 如果用户没有勾选 offline voucher，使用默认值 E-voucher supported (0)
        // - 如果用户勾选 offline voucher，则使用 E-voucher + offline voucher supported (4)
        voucher_usage: [evoucher_offline_voucher, print_mobile_offline_voucher].includes(
          formData.voucher_usage
        )
          ? print_mobile_offline_voucher
          : print_mobile_voucher
      })
      const packageContact = cpForm.pkg_contact
      if (packageContact) {
        if (packageContact.length) {
          const contactValue = packageContact.map((item) => {
            const { type, media, number } = item
            // 自定义的使用特殊的media
            const realMedia = type == 1 ? 'customized' : media
            return {
              media: realMedia,
              number,
              title: media
            }
          })
          this.$set(cpForm, 'pkg_contact', contactValue)
        } else {
          // obt存在且为空数组
          this.$set(cpForm, 'pkg_contact', [
            {
              media: 'phone',
              number: '',
              title: ''
            }
          ])
        }
      }
      // 给combo_standalone_skus加上默认值，最少1个列表
      // 默认语言 en_US klook.getEditLang()
      if (this.isCreate && !Array.isArray(formData.combo_standalone_pkgs) && !isCombo) {
        this.$set(cpForm, 'combo_standalone_pkgs', [
          {
            package_id: undefined,
            i18ns: [...getComboNameI18ns()]
          }
        ])
      }
      // 如果是combo套餐，初始化value值
      if (!this.isCreate && Array.isArray(formData.combo_standalone_pkgs)) {
        this.$set(
          cpForm,
          'combo_standalone_pkgs',
          formData.combo_standalone_pkgs.map((item) => {
            item.value = [item.activity_id, item.package_id]
            item.i18ns = item.i18ns.sort((item) => (item.language === 'en_US' ? 1 : -1))
            return item
          })
        )
      }

      if (hasOwnProperty.call(cpForm, 'calendar_day') && !cpForm.calendar_day) {
        cpForm.calendar_day =
          this.defaultPackageInfo.field_items.find((item) => item.field_key === 'calendar_day')?.value ??
          undefined
      }

      const pkgImage = cpForm.package_image
      if (pkgImage) {
        cpForm.package_image = Array.isArray(pkgImage) ? pkgImage[0] : null
      }

      // spa/att 预约相关字段处理
      const isAttReservation = _.has(cpForm, attReservationUniqueField)
      const reservationType = isAttReservation ? 'att' : 'spa'
      const initFn = {
        att: this.initAttFields,
        spa: this.initSpaFileds
      }
      cpForm = initFn[reservationType]?.(cpForm)
      // spa 相关字段处理 end
      // 特殊字段处理 end

      /**
       * staticFormData: 保存修改combo_standalone_pkgs前的form数据
       */
      let staticFormData = {}
      if (isCombo) {
        staticFormData = this._staticFormData || {}
      }
      this._stop_combo_standalone_skus_watch = isCombo
      this.$set(this, 'form', {
        ...this.form,
        ...cpForm,
        ...staticFormData
      })
      await this.initObConfig(result)
      this.usageValidityLinkageFn()
    },
    initSpaFileds(form = {}) {
      spaFileds.forEach((item) => {
        if (hasOwnProperty.call(form, item)) {
          let data = form[item]
          let defaultValue = null
          switch (item) {
            case 'reservation_amendment_policy':
            case 'reservation_cut_off_time':
              defaultValue = { time_num: undefined, time_unit: 'hours' }
              break
            case 'reservation_block_out_date':
              data = Array.isArray(data)
                ? data.map((item) => {
                    const [start, end = start] = item.split('-')
                    return [start, end]
                  })
                : null
              defaultValue = [[undefined, undefined]]
              break
            case 'reservation_other_content':
              data = Array.isArray(data?.contact)
                ? data
                : { contact: [{ content: '', type: undefined }], note: data?.note || '' }
              defaultValue = { contact: [{ content: '', type: undefined }], note: '' }
              break
            case 'reservation_week_timeslot':
              data = this.initWeekTimeslot(data)
              defaultValue = [{ week_day: [], timeslot: [{ start: undefined, end: undefined }] }]
              break
          }
          form[item] = data ? data : defaultValue
        }
      })
      return form
    },
    // 处理att fields初始化
    initAttFields(form = {}) {
      attReservationFields.forEach((item) => {
        if (hasOwnProperty.call(form, item)) {
          let data = form[item]
          let defaultValue = null
          switch (item) {
            case 'reservation_others_cut_off_time':
              defaultValue = { type: 1, time_config: { time_num: undefined, time_unit: 'hours' } }
              break
            case 'reservation_other_content':
              data = Array.isArray(data?.contact)
                ? data
                : { contact: [{ content: '', type: undefined }], note: data?.note || '' }
              defaultValue = { contact: [{ content: '', type: undefined }], note: '' }
              break
          }
          form[item] = data ? data : defaultValue
        }
      })
      return form
    },
    initWeekTimeslot(data) {
      if (!Array.isArray(data)) {
        return null
      }
      const list = data.reduce((acc, curr) => {
        const { week_day, timeslot } = curr
        const data = acc.find((item) => {
          return _.isEqual(item.timeslot, timeslot)
        })
        if (data) {
          data.week_day.push(week_day)
        } else {
          acc = [...acc, { week_day: [week_day], timeslot }]
        }
        return acc
      }, [])
      return list.map((item) => {
        const { week_day, timeslot } = item
        const timeSlot = timeslot.map((time) => {
          const [start, end = start] = time.split('-')
          return { start, end }
        })
        return {
          week_day,
          timeslot: timeSlot
        }
      })
    },
    fmtPostData() {
      let data = _.cloneDeep(this.form)
      // 还原数据格式 start
      data.sensitive_info && (data.sensitive_info = data.sensitive_info.join())

      if (Array.isArray(data.pkg_contact)) {
        data.pkg_contact = data.pkg_contact.reduce((acc, item) => {
          const { media, number, title = '' } = item
          const isCustom = media == 'customized'
          // 自定义联系方式
          if (isCustom) {
            if (!(_.isEmpty(title) || _.isEmpty(number))) {
              acc.push({
                media: title,
                type: 1,
                number
              })
            }
          } else {
            if (!(_.isEmpty(media) || _.isEmpty(number))) {
              acc.push({
                media,
                number,
                type: 0
              })
            }
          }
          return acc
        }, [])
      }

      data.package_image = data.package_image ? [data.package_image] : null
      // 还原数据格式 end
      for (let key in data) {
        let obt = this.schemaConfig[key]
        // 去除obt没有的字段
        if (!hasOwnProperty.call(this.schemaConfig, key)) {
          delete data[key]
        } else {
          //非必填字段类型校验
          if (obt && obt.required === false && data[key] === undefined) {
            // 非必填、类型不一致(v-check-val转为undefined)，则不返回给后端作为参数
            delete data[key]
          }
        }
      }

      // combo_standalone_pkgs 格式化
      if (Array.isArray(data.combo_standalone_pkgs)) {
        data.combo_standalone_pkgs = data.combo_standalone_pkgs.map((item) => {
          return {
            i18ns: item.i18ns.map((it) => {
              if (this.isCreate) {
                it.id = item.value[1]
              }
              delete it.lock
              return it
            }),
            package_id: this.isCreate ? item.value[1] : item.package_id
          }
        })
      }

      if (data.voucher_get_method === 1 && hasOwnProperty.call(data, 'voucher_type')) {
        // 无凭证
        data.voucher_type = -1
      }

      if (this.form.voucher_get_method !== 0) {
        // !== e_voucher
        data.voucher_usage = 0
      }

      // 台湾履保证明加上默认值
      if (data.escrow_guarantee === false && hasOwnProperty.call(this.form, 'escrow_guarantee_photo')) {
        data.escrow_guarantee_photo = []
      }

      // spa/att 预约相关字段处理
      const isAttReservation = _.has(data, attReservationUniqueField)
      const reservationType = isAttReservation ? 'att' : 'spa'
      const formatFn = {
        att: this.formatAttReservationFields,
        spa: this.formatSpaFileds
      }
      data = formatFn[reservationType]?.(data)
      // spa/att 预约相关字段处理 end

      let option_groups = []
      let field_items = Object.entries(data).map((arr) => {
        let [field_key, value] = arr

        const fieldAttrCompList = [
          'activation_validity',
          'usage_validity',
          'aid_voucher_type_desc',
          'participation_validity',
          'pkg_unique_selling_point'
        ]
        if (fieldAttrCompList.includes(field_key)) {
          let clearWidgetData
          if (!this.calcAttrGroupDisplayState(field_key, this.form)) {
            clearWidgetData = true
          }

          return {
            field_key,
            value: this.fieldAttributeGroupVN[field_key]?.getGroupPostData?.(clearWidgetData) ?? value
          }
        }

        const optAttrCompList = ['cancellation_policy', 'confirmation_time', 'voucher_get_method']
        if (optAttrCompList.includes(field_key)) {
          const postData = this.fieldAttributeGroupVN?.[field_key]?.getGroupPostData?.()

          if (postData) {
            option_groups = [...option_groups, ...postData.groups]
          }
        }

        if (field_key === 'auto_pub_unpub') {
          if (value.unpublished === 1 && value.unpublished_time === '') {
            value.unpublished = 0
          }
          if (value.published === 1 && value.published_time === '') {
            value.published = 0
          }
        }

        return {
          field_key,
          value
        }
      })

      return {
        activity_id: this.activity_id,
        field_items,
        language: klook.getEditLang(),
        package_id: this.package_id,
        page_from: klook.getPlatformRoleKey(),
        option_groups
      }
    },
    formatAttReservationFields(form) {
      const { reservation_required } = form
      if (reservation_required === 2) {
        form.reservation_others_cut_off_time = null
        form.reservation_other_content = null
        form.reservation_method = null
      }

      attReservationFields.forEach((item) => {
        if (hasOwnProperty.call(form, item)) {
          const data = form[item]
          let value = null
          switch (item) {
            case 'reservation_others_cut_off_time':
              value = data
              break
            case 'reservation_other_content':
              value = this.formatReservationOtherContent(data)
              break
          }
          form[item] = value
        }
      })
      return form
    },
    formatSpaFileds(form) {
      const { reservation_required, reservation_method } = form
      if (reservation_required === 2) {
        form.reservation_amendment_policy = null
        form.reservation_cut_off_time = null
        form.reservation_other_content = null
        form.reservation_block_out_date = null
        form.reservation_week_timeslot = null
        form.reservation_method = null
      }

      if (reservation_method === 2) {
        form.reservation_amendment_policy = null
        form.reservation_cut_off_time = null
        form.reservation_block_out_date = null
        form.reservation_week_timeslot = null
      }

      spaFileds.forEach((item) => {
        if (hasOwnProperty.call(form, item)) {
          const data = form[item]
          let value = null
          switch (item) {
            case 'reservation_amendment_policy':
            case 'reservation_cut_off_time':
              value = data
              break
            case 'reservation_other_content':
              value = this.formatReservationOtherContent(data)
              break
            case 'reservation_block_out_date':
              value = data
                ? data.map((item) => {
                    const [s, e] = item
                    if (s === e) {
                      return s
                    }
                    return item.join('-')
                  })
                : data
              break
            case 'reservation_week_timeslot':
              value = data ? this.formatWeekTimeSlot(data) : data
              break
          }
          form[item] = value
        }
      })
      return form
    },
    formatReservationOtherContent(data) {
      if (!data) {
        return data
      }

      data.contact = (data?.contact || [])
        .filter((item) => JSON.stringify(item) !== '{}')
        .map((item) => {
          const { type, content } = item
          return { type, content }
        })

      return data
    },
    formatWeekTimeSlot(timeSlot = []) {
      return timeSlot.reduce((acc, curr) => {
        const { week_day = [], timeslot } = curr
        const data = week_day.map((item) => {
          return {
            week_day: item,
            timeslot: timeslot.map((time) => {
              const { start, end } = time
              return `${start}-${end}`
            })
          }
        })
        return [...acc, ...data]
      }, [])
    }
  }
}
