import injectMixin from './inject'
import {
  cancellation_policy_fixed_date,
  cp_by_participate_time_group,
  cp_by_booking_confirm_time_group,
  cp_by_expire_time_group
} from '@activity/pages/package/basicInfo/components/cancellation/options'

// 此mixins主要处理在有条件退改规则下，wieget的复制和选择的禁用逻辑
export default {
  mixins: [injectMixin],
  methods: {
    /**
     * cpGetCheckboxDisable 方法是判断是否禁用选择框，如果是新绑定的4种类型，则全部开放可选
     * cancellation_policy_fixed_date 代表原来的new binding，不可以选择
     */
    cpGetCheckboxDisable(option) {
      if (!this.cpStatus) {
        return false
      }

      if (this.cpStatus.isCPTypeNewBind === cancellation_policy_fixed_date) {
        return true
      }

      return false
    },

    /**
     * cpGetCopyDisable 方法是判断是否禁用复制按钮
     * cp_by_participate_time_group 除了cp_by_participate_time_av_wxbp ，cp_by_participate_time_av_fbttsbp 不可以复制
     * cp_by_booking_confirm_time_group 类型中除了 cp_by_booking_confirm_time_av_lxab cp_by_booking_confirm_time_av_ltsab 不可以复制
     */
    cpGetCopyDisable(option) {
      if (!this.cpStatus || !option) {
        return false
      }

      const { isCPTypeNewBind } = this.cpStatus
      // 代表原来的free text，可以复制
      if (option.ref_field_tag === '' && !option?.required) {
        return false
      }

      let result = true
      switch (isCPTypeNewBind) {
        case cp_by_participate_time_group:
          if (
            [
              'cp_by_participate_time_av_wxbp',
              'cp_by_participate_time_av_lxap',
              'cp_by_participate_time_av_wtsbp',
              'cp_by_participate_time_av_ltsap'
            ].includes(option.ref_field_tag)
          )
            result = false
          break
        case cp_by_booking_confirm_time_group:
          if (
            ['cp_by_booking_confirm_time_av_lxab', 'cp_by_booking_confirm_time_av_ltsab'].includes(
              option.ref_field_tag
            )
          )
            result = false
          break
        case cp_by_expire_time_group:
          if (['cp_by_expire_time_av_lxae'].includes(option.ref_field_tag)) result = false
          break
        case cancellation_policy_fixed_date:
          if (option.ref_field_tag === 'cancellation_policy_fixed_date_av_xdhm') {
            result = true
          } else {
            result = false
          }
          break
        default:
          result = false
          break
      }
      return result
    }
  }
}
