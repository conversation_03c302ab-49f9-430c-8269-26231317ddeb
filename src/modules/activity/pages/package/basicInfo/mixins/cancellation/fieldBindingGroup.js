import injectMixin from './inject'

// 此mixins 主要用于在于提交保存的时候，过滤掉隐藏项的数据
export default {
  mixins: [injectMixin],
  methods: {
    cpIsHiddenBinding(widgets, typeValue) {
      if (typeValue !== 7) {
        return false
      }

      if (!this.cpStatus) {
        return false
      }

      const { isCPFileld, widgetTag, isCPTypeNewBind } = this.cpStatus

      if (!isCPFileld) {
        return false
      }

      const tag = widgets.ui_conf.ref_field_tag
      const commonTag = tag === '' && !widgets.ui_conf?.required
      // 只有在有条件退款的时候执行这些逻辑
      if (!isCPTypeNewBind) {
        if (!commonTag) {
          return true
        }
        return false
      }

      // 如果rules存在，过滤掉不属于当前rǔles下的类型
      if (isCPTypeNewBind && widgetTag === tag) {
        return false
      }

      // 通用widget
      if (commonTag) {
        return false
      }

      return true
    }
  }
}
