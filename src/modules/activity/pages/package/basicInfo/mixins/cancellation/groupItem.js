import injectMixin from './inject'

// 此Mixin主要用于判断是否隐藏控件还有隐藏校验的项目需要跳过校验
export default {
  mixins: [injectMixin],
  methods: {
    /**
     * 判断是否需要隐藏,返回true则表示显示
     *
     */
    cpShowWidget(widget) {
      if (!widget) {
        return true
      }

      if (!this.cpStatus) {
        return true
      }

      const { widgetTag, isCPValueCondition } = this.cpStatus

      if (!isCPValueCondition) {
        return true
      }

      return this._isTypeRight(widget, widgetTag)
    },

    /**
     * 隐藏校验的项目需要跳过校验，返回false不会跳过校验，返回true则会跳过校验
     * 如果 widget 既不是新绑定也不是旧绑定，则需要跳过校验。
     * 如果 widget 是新绑定且 newBindShow 为 false，则需要跳过校验。
     * 如果 widget 是旧绑定且 oldBindShow 为 false，则需要跳过校验。
     */
    cpPassValidate(widget) {
      if (!widget) {
        return false
      }
      if (!this.cpStatus) {
        return false
      }

      const { isCPValueCondition, widgetTag } = this.cpStatus
      if (!isCPValueCondition) {
        return true
      }

      return !this._isTypeRight(widget, widgetTag)
    },
    // 进行显示隐藏判断逻辑
    _isTypeRight(widget, groupTags) {
      const tag = widget?.ui_conf?.ref_field_tag
      // 通用的widget，widget的ref_field_tag为空，且不是非必填的时候，每一个type都需要展示
      if (tag === '' && !widget.ui_conf?.required) {
        return true
      }
      return tag === groupTags ? true : false
    }
  }
}
