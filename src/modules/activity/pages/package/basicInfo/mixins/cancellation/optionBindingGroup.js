import { cancel_policy } from '@activity/pages/package/package_const'
import CancellationType from '@activity/pages/package/basicInfo/components/cancellation/CancellationType.vue'
import options, {
  cancellation_policy_fixed_date,
  old_cancellation_refund_policy
} from '@activity/pages/package/basicInfo/components/cancellation/options'

export default {
  components: {
    CancellationType
  },
  props: {
    isComboPKG: {
      type: Boolean,
      default: false
    },
    isPassProducts: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      /*
       *  rules类型，之前是boolean，现在是string
       */
      isCPTypeNewBind: null,
      hasOldFormat: false,
      supportsIds: []
    }
  },
  provide() {
    return {
      cpStatusFn: () => this.cpStatus
    }
  },

  computed: {
    /**
     *  Cancellation Field Bind的状态
     */
    cpStatus() {
      if (this.field !== 'cancellation_policy') {
        return null
      }
      // 是否是取消政策字段
      const isCPFileld = this.field === 'cancellation_policy'

      // 是否取消政策字段值是conditional
      const isCPValueCondition =
        isCPFileld && this.form?.[this.field] === cancel_policy.cancel_with_conditions

      // ticketType的值
      const ticketType = this.form?.ticket_type

      // inventory的值
      const inventoryType = this.form?.inventory_type
      const showRulesType = isCPValueCondition && isCPFileld
      const isCPTypeNewBind = this.isCPTypeNewBind
      const voucherGetMethod = this.form?.voucher_get_method

      // 当前widgetId对应的widgetTag
      const widgetTag = options.find((option) => option.value === isCPTypeNewBind)?.mapValue?.WIDGET_TAG

      return {
        isCPFileld,
        isCPValueCondition,
        isCPTypeNewBind,
        showRulesType,
        widgetTag,
        // 校验相关值
        inventoryType,
        ticketType,
        voucherGetMethod,
        isComboPKG: this.isComboPKG,
        isPassProducts: this.isPassProducts,
        hasOldFormat: this.hasOldFormat,
        supportsIds: this.supportsIds
      }
    }
  },

  watch: {
    // 手动选择发生变化，展开所有的group
    isCPTypeNewBind: {
      immediate: true,
      handler(val) {
        if (val === cancellation_policy_fixed_date) {
          this.isEditState = false
          return
        }
        this.isEditState = true
      }
    },
    /**
     * 当select 出现的时候，需要计算默认值
     * */
    'cpStatus.showRulesType': {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.updateCpBindType()
        } else {
          this.isCPTypeNewBind = null
        }
      }
    },

    groupList(val) {
      if (val) {
        this.updateCpBindType()
      }
    },
    supportsIds(val) {
      if (val && val.length > 0) {
        this.updateCpBindType()
      }
    }
  },
  methods: {
    async getAllIds() {
      const result = await ajax.get(ADMIN_API.act.get_standard_cancel_policy_sub_category_ids)
      if (result) {
        this.supportsIds = result || []
      }
    },
    // 设置isCPTypeNewBind的值
    updateCpBindType() {
      if (!this.cpStatus) {
        return
      }

      if (!this.cpStatus.isCPValueCondition) {
        return
      }
      const type = this.getCpBindType()
      this.isCPTypeNewBind = type
      // 这里强制更新一下，因为isCPTypeNewBind是一个computed属性，不会自动更新
      // this.$forceUpdate()
    },
    // 从groups_instance中获取到有值的widget的groupId，再从groups_schema种拿到ref_field_tag
    getCpBindType() {
      const optionInstance = this.optionGroup?.group_data?.groups_instance || {}
      const result = []
      Object.keys(optionInstance).forEach((key) => {
        const widgets = optionInstance[key]

        // 过滤掉通用的widget
        // 注意：attr_values是当前的值，current_attr_values是历史值，这里用的历史值
        const hasNonEmptyAttrValues = widgets
          .filter((widget) => widget?.ui_conf?.ref_field_tag)
          .some((widget) => widget?.current_attr_values?.length || 0 > 0)
        if (hasNonEmptyAttrValues) {
          result.push(key)
        }
      })
      if (result.length === 0) {
        return undefined
      }

      const optionSchema = this.optionGroup?.group_data?.groups_schema
      const [groupId] = result
      if (!optionSchema || !optionSchema[groupId]) {
        return undefined
      }

      const tags = optionSchema[groupId]?.ref_field_tag

      // 说明有历史数据
      if (tags === cancellation_policy_fixed_date) {
        this.hasOldFormat = true
      }

      // 如果ref_field_tag为空 && 是有条件退改则说明是freeText类型
      if (!tags && this.cpStatus?.isCPValueCondition) {
        return old_cancellation_refund_policy
      }
      return options.map((item) => item.value).includes(tags) ? tags : undefined
    }
  },
  mounted() {
    this.getAllIds()
  }
}
