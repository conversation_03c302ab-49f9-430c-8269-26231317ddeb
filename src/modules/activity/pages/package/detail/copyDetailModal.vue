<template>
  <a-modal
    width="720px"
    class="copy-groups-modal"
    :title="$t('act_copy_groups')"
    :visible.sync="_visible"
    :mask-closable="false"
    @cancel="handleClose"
  >
    <a-form-model
      ref="form"
      :key="_visible"
      :model="form"
      layout="horizontal"
      :label-col="{ span: 7 }"
      :wrapper-col="{ span: 17 }"
      :colon="false"
    >
      <a-form-model-item
        v-if="!isComboPKG"
        prop="type"
        :label="$t('act_copy_direction')"
        :rules="{
          required: true,
          validator: validatorArr
        }"
      >
        <a-radio-group v-model="form.type">
          <a-radio value="to">{{ $t('act_copy_to_other_pkg') }}</a-radio>
          <a-radio value="from">{{ $t('act_copy_from_another_pkg') }}</a-radio>
        </a-radio-group>
      </a-form-model-item>

      <a-form-model-item
        prop="to_package_id_list"
        :label="$t('act_select_to_from')"
        :rules="{
          required: true,
          validator: validatorArr
        }"
      >
        <a-select
          v-model="form.to_package_id_list"
          style="width: 400px"
          show-search
          :mode="form.type === 'to' ? 'multiple' : 'default'"
        >
          <a-select-option v-for="pkg in packageListOpts" :key="pkg.value" :value="pkg.value">
            {{ [-1, 0].includes(pkg.value) ? pkg.label : pkg.value + '-' + pkg.label }}
          </a-select-option>
        </a-select>
      </a-form-model-item>

      <a-form-model-item
        prop="group_list"
        :label="$t('act_groups_to_copy')"
        :rules="{
          required: true,
          validator: validatorArr
        }"
      >
        <a-select v-model="form.group_list" style="width: 400px" show-search mode="multiple">
          <a-select-option v-for="group in groupListOpts" :key="`${form.type}-${group.id}`" :value="group.id">
            {{ `${[-1, 0].includes(group.id) ? '' : group.id + '-'}${group.name}` }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </a-form-model>

    <div slot="footer">
      <a-button @click="handleClose"> {{ $t('global_cancel') }}</a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmitCopy">{{
        $t('global_submit')
      }}</a-button>
    </div>
  </a-modal>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'CopyDetailModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    groupList: {
      type: Array,
      required: true
    },
    allGroups: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      form: {
        type: 'to',
        to_package_id_list: [],
        group_list: []
      },

      loading: false
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(v) {
        this.$emit('update:visible', v)
      }
    },
    approval_status() {
      let { pkgInfo = {} } = this.$attrs.actGlobal
      return pkgInfo.approval_status
    },
    activity_id() {
      return +this.$route.params.id
    },
    package_id() {
      return +this.$route.query.package_id
    },
    packageList() {
      return this.pkgList()
        .filter((item) => item.package_id !== +this.package_id)
        .map((pkg) => ({
          value: pkg.package_id,
          label: this.getName(pkg)
        }))
    },
    groupListOpts() {
      let groupList = this.groupList
      if (this.form.type === 'from') {
        groupList = this.allGroups
      }

      return [
        groupList.length !== this.form.group_list.length
          ? {
              id: -1,
              name: 'All'
            }
          : {
              id: 0,
              name: 'Clear All'
            },
        ...groupList
      ]
    },
    comboPackageList() {
      let {
        combo_info = { standalone_packages: [] },
        category: { sub_category_id }
      } = this.$store.state.currentPackageFloatingField
      return combo_info.standalone_packages
        .filter((item) => item.sub_category_id === sub_category_id)
        .map((item) => {
          return {
            value: item.package_id,
            label: item.package_name
          }
        })
    },
    packageListOpts() {
      if (this.isComboPKG) {
        return this.comboPackageList
      } else if (this.form.type === 'to') {
        return [
          this.packageList.length !== this.form.to_package_id_list.length
            ? {
                value: -1,
                label: 'All'
              }
            : {
                value: 0,
                label: 'Clear All'
              },
          ...this.packageList
        ]
      } else {
        return this.packageList
      }
    },
    isComboPKG() {
      let { product_type = 0 } = this.$store.state.currentPackageFloatingField

      return product_type === 1
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler() {
        const formType = this.isComboPKG ? 'from' : 'to'

        this.$set(this, 'form', { ...this.$options.data().form, type: formType })
      }
    },
    'form.group_list': {
      deep: true,
      immediate: true,
      handler(v) {
        let group_list = (this.groupListOpts || []).reduce(
          (acc, curr) => (-1 === curr.id ? acc : [...acc, curr.id]),
          []
        )
        if (v.some((item) => -1 === item)) {
          // includes All
          this.$set(this.form, 'group_list', group_list)
        }
        if (v.some((item) => 0 === item)) {
          this.$set(this.form, 'group_list', [])
        }
      }
    },
    'form.to_package_id_list': {
      deep: true,
      immediate: true,
      handler(v) {
        let to_package_id_list = _.flattenDepth([v])

        if (to_package_id_list.some((item) => -1 === item)) {
          // includes All
          this.$set(
            this.form,
            'to_package_id_list',
            this.packageList.map((item) => item.value)
          )
        }
        if (to_package_id_list.some((item) => 0 === item)) {
          this.$set(this.form, 'to_package_id_list', [])
        }
      }
    },
    'form.type': {
      immediate: true,
      handler(v) {
        if (v === 'to') {
          this.$set(this.form, 'to_package_id_list', [])
          this.$set(this.form, 'group_list', [])
        } else {
          this.$set(this.form, 'to_package_id_list', '')
        }
      }
    }
  },
  methods: {
    ...mapGetters(['pkgList']),
    ...mapActions(['updatePkgStepStatus2action']),
    validatorArr(rule, value, cb) {
      value = this.form[rule.field]
      if (value === '' || (Array.isArray(value) && !value.length)) {
        cb(new Error('Please select'))
      } else {
        cb()
      }
    },
    getName(data) {
      let current = _.find(data.package_local || [], {
        language_type: lang_conf.getLangObj('F_LANG', 'B_LANG')[KLK_LANG]
      })

      return (current && current.name) || _.get(data, 'package_local[0].name', '')
    },
    handleSubmitCopy() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true
          let data = {
            group_list: this.form.group_list,
            ...(this.form.type === 'to'
              ? {
                  to_package_id_list: this.form.to_package_id_list,
                  from_package_id: this.package_id
                }
              : {
                  to_package_id_list: [this.package_id],
                  from_package_id: this.form.to_package_id_list
                })
          }
          await ajax.postBody(ADMIN_API.act.copy_package_instance_data, {
            data
          })

          data.to_package_id_list.forEach((package_id) => {
            ;(async (package_id) => {
              await this.updatePkgStepStatus2action({
                status: 1,
                activity_id: this.activity_id,
                language: new URLSearchParams(location.search).get('lang') || 'en_US',
                step: 'package_detail_info',
                package_id: package_id
              })

              klook.bus.$emit('updatePkgInfos2bus', null, package_id)
            })(package_id)
          })

          this.loading = false

          if (this.form.type === 'from') {
            this.$emit('refreshWhenFrom')
          }

          this._visible = false
          this.$message.success(this.$t('global_success'))
          this.$set(this, 'form', this.$options.data().form)
        }
      })
    },
    handleClose() {
      this._visible = false
    }
  }
}
</script>
