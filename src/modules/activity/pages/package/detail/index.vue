<template>
  <div id="package-detail-editable" class="package-detail" :data-spm-page="getSpm">
    <Presenter
      ref="detailGroups"
      class="act-detail-box"
      extra-type="PKG"
      :readonly="readonly"
      :is-publish-with-ai="isPublishWithAI"
      @groupListInited="groupListInited"
      @saveError="saveError"
    >
      <template #header>
        <div class="common-copy-wrap" style="flex: 1;">
          <a-button :disabled="disableCopyPkg" @click="handleCopy">
            {{ $t('21857') }}
          </a-button>
        </div>
      </template>
    </Presenter>

    <CopyDetailModal
      :visible.sync="visible"
      :group-list="groupList"
      :all-groups="allGroups"
      @refreshWhenFrom="refreshWhenFrom"
    />

    <!-- 埋点 -->
    <div id="copy_detail_spm" :data-spm-module="getCopySpm" data-spm-virtual-item="__virtual"></div>
    <div id="quick_view_spm" :data-spm-module="getQuickViewSpm" data-spm-virtual-item="__virtual"></div>
    <div
      id="save_package_detail_spm"
      v-bind="$root.bindIHTrack({ type: 'module', spm: 'SavePackageDetail' })"
    ></div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import CopyDetailModal from './copyDetailModal.vue'
import Presenter from '../../activityManagement/detailV2/components/presenter'

export default {
  inject: ['refreshPkgs', 'refreshPage', 'page_from'],
  components: {
    CopyDetailModal,
    Presenter
  },
  data() {
    return {
      readonly: false,
      visible: false,

      groupList: [],
      allGroups: []
    }
  },
  computed: {
    ...mapGetters(['isPublishWithAI']),
    getSpm() {
      const oid = `activity_${this.activity_id || 0}`
      return `PackageDetail?oid=${oid}`
    },
    getCopySpm() {
      const oid = `activity_${this.activity_id || 0}`
      return `CopyPackageDetail?trg=manual&oid=${oid}`
    },
    getQuickViewSpm() {
      const oid = `activity_${this.activity_id || 0}`
      return `PackageDetailQuickView?oid=${oid}&trg=manual`
    },
    disableCopyPkg() {
      let isb = false // admin端都可以复制
      if (this.$store.state.isMC2BD) {
        isb = [1, 3].includes(this.approval_status) // 1:pendingApproval  3:approved
      }
      return isb
    }
  },
  mounted() {
    // this.$root.setMutationObserver('#package-detail-editable', this)
  },
  async created() {
    // 埋点
    klook.bus.$on('QuickViewClick_Spm', () => {
      this.$tracker.track('action', '#quick_view_spm')
    })
  },
  beforeDestroy() {
    klook.bus.$off('QuickViewClick_Spm')
  },
  methods: {
    saveError(res) {
      this.$root.trackIHEvent('#save_package_detail_spm', {
        err_message: res?.error?.message ?? 'SavePackageDetail Error'
      })
    },
    async refreshWhenFrom() {
      await this.refreshPkgs()
      await this.refreshPage()
      this.$refs.detailGroups && this.$refs.detailGroups.getCurrWidgetsData()
    },
    groupListInited({ groupList, data, allGroups }) {
      const required = groupList.some((group) => {
        const widgets = data.groups_schema[group.id]?.widget_list ?? []

        return widgets.some((widget) => !!widget.required)
      })

      this.$set(this, 'allGroups', allGroups)

      klook.bus.$emit('changeNavPackageListDataByKey', [
        {
          step: 'package_detail_info',
          require: required
        }
      ])
    },
    handleCopy() {
      this.$set(this, 'groupList', this.$refs.detailGroups?.groupList ?? [])
      this.visible = true

      // 埋点
      this.$tracker.track('action', '#copy_detail_spm')
    }
  }
}
</script>

<style lang="scss" scoped>
.package-detail {
  .package-detail-box {
    padding-bottom: 8px;
  }
}
</style>

<style lang="scss">
.copy-groups-modal .ant-form-item-label {
  label {
    white-space: pre-wrap;
    line-height: 1.5em;
    display: inline-block;
  }
}
</style>
