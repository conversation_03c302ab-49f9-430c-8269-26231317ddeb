<template>
  <div class="mail-info-group-container">
    <a-radio-group
      ref="radioGroup"
      v-model="need"
      :disabled="!!$attrs.disabled || $attrs.disabledNeedCheckbox"
      style="margin: 8px 0;"
      @change="changeNeed"
    >
      <a-radio v-for="opt in needOptions" :value="opt.value" :key="opt.value">
        {{ opt.label }}
      </a-radio>
    </a-radio-group>

    <div
      v-if="need"
      class="content-other-info triangle"
      :style="{
        '--offsetX': triangleOffsetX + 'px'
      }"
    >
      <other-info-group
        ref="groups"
        v-bind="$attrs"
        v-on="$listeners"
        :originalGroups="originalGroups"
        :group-checked-list.sync="currentGroupCheckedList"
        :disabledCheckbox="true"
        @toViewMerged="(data) => $emit('toViewMerged', data)"
      />
    </div>
  </div>
</template>

<script>
import OtherInfoGroup from './OtherInfoGroup'

export default {
  components: {
    OtherInfoGroup
  },
  props: {
    originalGroups: {
      required: true,
      type: Array
    },
    groupCheckedList: {
      type: Array,
      required: true
    }
  },
  data() {
    this.isInited = false

    return {
      triangleOffsetX: 0,
      need: false
    }
  },
  watch: {
    groupCheckedList: {
      immediate: true,
      deep: true,
      handler() {
        if (!this.isInited) {
          this.isInited = true
          this.initData()
        }
      }
    },
    need: {
      handler(v) {
        let currentGroupCheckedList = []
        let groupIds = this.originalGroups.map((item) => item.group_id)

        if (v) {
          currentGroupCheckedList = [...new Set([...this.currentGroupCheckedList, ...groupIds])]
        } else {
          this.$emit('updateData', [])
          currentGroupCheckedList = this.currentGroupCheckedList.filter((item) => !groupIds.includes(item))
        }

        this.$set(this, 'currentGroupCheckedList', currentGroupCheckedList)
      }
    }
  },
  computed: {
    currentGroupCheckedList: {
      get() {
        return this.groupCheckedList
      },
      set(v) {
        this.$emit('update:groupCheckedList', v)
      }
    },
    needOptions() {
      return [
        {
          label: __('28633'),
          value: false
        },
        {
          label: __('28635'),
          value: true
        }
      ]
    }
  },
  methods: {
    initData() {
      const { originalGroups } = this

      for (let group of originalGroups) {
        if (this.groupCheckedList.includes(group.group_id)) {
          this.need = true
          this.changeNeed()
          break
        }
      }
    },
    async changeNeed() {
      await this.$nextTick()

      const ele = this.$refs.radioGroup.$el

      if (ele) {
        let [left, right] = ele.querySelectorAll('.ant-radio')
        this.triangleOffsetX = right.getBoundingClientRect().left - left.getBoundingClientRect().left
      }
    },
    validateForm() {
      return this.$refs.groups.validateForm()
    }
  }
}
</script>
