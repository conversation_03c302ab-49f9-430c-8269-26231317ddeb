<template>
  <div class="customOtherInfo-container">
    <div class="body">
      <div v-for="(form, index) of items" class="form-item" :key="`${form.type_flag}-${index}`">
        <a-form-model class="form-card" layout="vertical" ref="form" :model="form">
          <a-form-model-item label="" prop="type_flag" required>
            <a-radio-group
              v-model="form.type_flag"
              size="small"
              :disabled="
                !!form.extra_info_id ||
                  onlyFreeText ||
                  xinfo_lock_info_ids.includes(form.extra_info_id) ||
                  readonly
              "
              @change="handleTypeFlag(index)"
            >
              <a-radio-button
                v-for="opt of typeOpts"
                :key="opt.value"
                :value="opt.value"
                :disabled="disabledTypes.includes(opt.value) || readonly"
              >
                {{ opt.label }}
              </a-radio-button>
              <!-- :disabled="disabledTypes.includes(opt.value)" -->
            </a-radio-group>

            <p v-if="xinfo_lock_info_ids.includes(form.extra_info_id)" style="color: red;">
              {{ $t('29043') }}
            </p>
          </a-form-model-item>
          <template v-if="form.type_flag">
            <div class="__info">
              <tpl_special_multi_language_input
                v-model="form.info"
                v-bind="commonAttrs"
                :required="true"
                :class="{
                  __infoInput: [1, 2, 3].includes(form.type_flag)
                }"
                valField="name"
                :label="form.type_flag === 6 ? $t('54043') : $t('package_xinfo_name')"
                :disabled="xinfo_lock_info_ids.includes(form.extra_info_id) || readonly || isPublishWithAi"
                :placeholder="form.type_flag === 6 ? $t('54045') : $t('package_xinfo_name_placeholder')"
                :type-flag="form.type_flag"
                :default-max-input-length="100"
              ></tpl_special_multi_language_input>

              <tpl_special_multi_language_input
                v-if="[1, 2, 3].includes(form.type_flag)"
                v-model="form.info"
                v-bind="commonAttrs"
                class="__infoInput"
                valField="hint"
                :required="true"
                :label="$t('28958')"
                :disabled="xinfo_lock_info_ids.includes(form.extra_info_id) || readonly || isPublishWithAi"
                :placeholder="$t('package_xinfo_hint_placeholder')"
              ></tpl_special_multi_language_input>
            </div>

            <a-form-model-item v-if="form.type_flag === 2" label="" prop="date_option">
              <a-radio-group
                v-model="form.date_option"
                :options="dateOpts"
                :disabled="onlyFreeText || xinfo_lock_info_ids.includes(form.extra_info_id) || readonly"
              >
              </a-radio-group>
            </a-form-model-item>

            <tpl_special_multi_language_input
              v-if="[3, 6].includes(form.type_flag)"
              v-model="form.info"
              v-bind="commonAttrs"
              :valField="form.type_flag === 3 ? 'content' : 'hint'"
              :required="true"
              :label="form.type_flag === 3 ? $t('28959') : $t('21813')"
              type="textarea"
              :disabled="xinfo_lock_info_ids.includes(form.extra_info_id) || readonly || isPublishWithAi"
              :placeholder="
                form.type_flag === 3
                  ? $t('package_xinfo_dropdown_placeholder')
                  : $t('package_xinfo_checkbox_placeholder')
              "
            ></tpl_special_multi_language_input>

            <a-form-model-item
              v-if="form.type_flag !== 6"
              :label="$t('package_xinfo_choose_repeat_rule')"
              prop="ask_option"
            >
              <a-radio-group
                v-model="form.ask_option"
                :options="askOpts"
                :disabled="onlyFreeText || xinfo_lock_info_ids.includes(form.extra_info_id) || readonly"
              >
              </a-radio-group>
            </a-form-model-item>

            <a-form-model-item v-if="[2, 4].includes(form.type_flag)" label="" prop="required">
              <a-checkbox
                :checked="!!form.required"
                :disabled="onlyFreeText || xinfo_lock_info_ids.includes(form.extra_info_id) || readonly"
                @change="(e) => handleChangeRequired(form, e.target.checked)"
              >
                {{ $t('package_xinfo_mandatory_hint') }}
              </a-checkbox>
            </a-form-model-item>

            <p
              v-if="[1, 3, 6].includes(form.type_flag)"
              :style="{
                marginTop: '12px'
              }"
            >
              {{ $t('package_xinfo_ismadatory') }}
            </p>

            <footer class="form-card__footer">
              <a-button
                type="primary"
                size="small"
                :disabled="xinfo_lock_info_ids.includes(form.extra_info_id) || readonly"
                @click="handleSave(form, index)"
              >
                {{ $t('global_save') }}
              </a-button>

              <a-button size="small" class="form-card__footerPreview" @click="handlePreview(form, index)">
                {{ $t('global_preview') }}
              </a-button>
            </footer>
          </template>
        </a-form-model>

        <a-popconfirm
          v-if="!onlyFreeText && !xinfo_lock_info_ids.includes(form.extra_info_id) && !readonly"
          title="Are you sure delete this?"
          ok-text="Yes"
          cancel-text="No"
          @confirm="handleDelete(form, index)"
        >
          <CircleDeleteButton size="small" :alt="$t('global_delete')" />
        </a-popconfirm>
      </div>
    </div>

    <footer class="footer" v-if="!readonly">
      <a-button v-if="!onlyFreeText" type="primary" :disbaled="loading" @click="handleAddAnother">
        {{ $t('package_xinfo_another') }}
      </a-button>
    </footer>

    <a-modal
      v-if="modal.visible"
      v-model="modal.visible"
      :title="modal.isPreview ? 'Preview' : ''"
      :closable="false"
      :width="modal.isPreview ? '520px' : '720px'"
    >
      <template slot="footer">
        <a-button :type="modal.isPreview ? 'primary' : 'default'" @click="modal.visible = false">
          {{ $t('78466') }}
        </a-button>

        <a-button v-if="!modal.isPreview" type="primary" @click="handleSave(modal.form)">
          {{ $t('global_submit') }}
        </a-button>
      </template>

      <template v-if="modal.isPreview">
        <a-form-model layout="vertical">
          <a-form-model-item v-for="(item, index) of modal.form.info" :key="index" :label="item.name">
            <template v-if="modal.form.type_flag === 6">
              <a-checkbox>
                {{ item.hint }}
              </a-checkbox>
            </template>
            <component
              :is="previewComponentDict[modal.form.type_flag]"
              v-else
              :placeholder="item.hint"
              :options="getSelectOpts(item.content || '')"
              :format="previewComponentFmt[modal.form.type_flag]"
              :label="item.name"
              :mode="
                modal.form.mode
                  ? modal.form.mode
                  : previewComponentMode[modal.form.type_flag]
                  ? previewComponentMode[modal.form.type_flag]
                  : 'default'
              "
            >
            </component>
          </a-form-model-item>
        </a-form-model>
      </template>

      <template v-else>
        <p>{{ $t('28973') }}</p>
        <a-table
          :bordered="true"
          :pagination="false"
          :columns="getTableData(modal.form.info).columns"
          :data-source="getTableData(modal.form.info).data"
        ></a-table>
      </template>
    </a-modal>
  </div>
</template>

<script>
import { typeObj, dateObj } from './utils'
import tpl_special_multi_language_input from '@activity/components/tpl_special_multi_language_input'
import AFormModelItem from 'ant-design-vue/es/form-model/FormItem'
import CircleDeleteButton from '../../../components/CircleDeleteButton'

export default {
  name: 'customOtherInfo',
  components: {
    CircleDeleteButton,
    AFormModelItem,
    tpl_special_multi_language_input
  },
  props: {
    allData: {
      type: Array,
      required: true
    },
    package_id: {
      type: [Number, String],
      required: true
    },
    onlyFreeText: {
      type: Boolean,
      default: false
    },
    xinfo_lock_info_ids: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    disabledTypes: {
      type: Array,
      default: () => []
    },
    readonly: {
      type: Boolean,
      default: false
    },
    isPublishWithAi: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      modal: {
        visible: false,
        form: null,
        type: ''
      },
      items: [],
      previewComponentFmt: {
        2: 'YYYY-MM-DD',
        4: 'HH:mm'
      },
      previewComponentMode: {
        2: 'date'
      }
    }
  },
  watch: {
    allData: {
      deep: true,
      handler() {
        let allData = this.allData.map((item) => ({
          ...item,
          info: item.info.map((info) => {
            if ([3].includes(item.type_flag)) {
              let [hint, ...content] = info.hint.split(',')
              content = content.join(',')

              return {
                ...info,
                hint,
                content
              }
            }

            return info
          })
        }))
        this.items = _.cloneDeep(allData)
      }
    }
  },
  computed: {
    previewComponentDict() {
      return {
        3: 'a-select',
        1: 'a-input',
        2: 'a-date-picker',
        4: 'a-time-picker',
        5: 'a-checkbox'
      }
    },
    askOpts() {
      return [
        {
          value: 1,
          label: this.$t('package_xinfo_ask_once')
        },
        {
          value: 2,
          label: this.$t('package_xinfo_ask_each')
        }
      ]
    },
    typeOpts() {
      return Object.entries(typeObj).reduce((acc, curr) => {
        let [value, label] = curr
        value = Number(value)

        if (value === 3) {
          return [{ value, label }, ...acc]
        }

        return [...acc, { value, label }]
      }, [])
    },
    dateOpts() {
      return Object.entries(dateObj).map((item) => ({
        value: +item[0],
        label: item[1]
      }))
    },
    commonAttrs() {
      return {
        requiredEn: true,
        inputStyle: {},
        isFormModel: false,
        dataField: 'info',
        initSchema: {
          name: '',
          hint: '',
          content: ''
        }
      }
    }
  },
  methods: {
    handleTypeFlag(index) {
      this.$nextTick(() => {
        let data = _.cloneDeep(this.items[index])
        if (data.type_flag === 6) {
          data.info = data.info.map((item) => {
            if (!item.name) {
              item.name = __('54044')
            }
            return item
          })
        }
        this.items.splice(index, 1, data)
        this.$emit('update:allData', this.fmtItems2AllData())
      })
    },
    handleChangeRequired(form, v) {
      form.required = Number(v)
      this.$forceUpdate()
    },
    fmtItems2AllData() {
      return this.items.map((item) => this.fmtFormData(item))
    },
    fmtFormData(form) {
      let data = _.cloneDeep({
        ...form,
        package_id: this.package_id
      })

      data.info = data.info.map((item) => {
        let { content, hint, ...other } = item
        hint = [hint, content].filter((item) => item).join(',')

        return {
          ...other,
          hint
        }
      })

      return data
    },
    getTableData(info) {
      let columns = info.map((item, index) => {
        let language = lang_conf.getLangObj('B_LANG', 'LANG_TITLE')[item.language]
        return {
          title: `${__('28959')}(${language})`,
          dataIndex: `col-${index}`,
          rowKey: `col-${index}`,
          width: '250px'
        }
      })

      let data = info.map((item) => item.content.split(','))
      let rowMaxLength = data.reduce((acc, curr) => Math.max(acc, curr.length), 0)
      let colLength = data.length

      return {
        columns,
        data: Array(rowMaxLength)
          .fill('')
          .map((row, rowIndex) => {
            return Array(colLength)
              .fill('')
              .reduce((acc, curr, colIndex) => {
                return {
                  ...acc,
                  [`col-${colIndex}`]: data[colIndex][rowIndex] || ''
                }
              }, {})
          })
      }
    },
    getSelectOpts(data) {
      //  Customized other info preview 支持输入用（,）分隔的内容。
      let arr = Array.isArray(data) ? data : data.split(',')
      return arr.map((item, index) => ({
        label: item,
        value: item + index // 加上index,防止多个value值相同时控制台报错警告
      }))
    },
    handlePreview(form, index) {
      // 复用旧版preview，index小于0 表示 Customized other info 数据preview

      if (index < 0) {
        return this.$set(this, 'modal', {
          visible: true,
          form,
          isPreview: true
        })
      }
      this.$refs.form[index].validate(async (valid) => {
        if (!valid) return

        this.$set(this, 'modal', {
          visible: true,
          form,
          isPreview: true
        })
      })
    },
    async validateForm() {
      let all = await Promise.all(
        (this.$refs.form || []).map(
          (form, index) =>
            new Promise((resolve) => {
              if (this.items[index].extra_info_id) {
                form.validate((valid) => resolve(valid))
              } else {
                resolve(true)
              }
            })
        )
      )

      return all.every((item) => item)
    },
    // @param {number | null} index - null save on secondary confirm
    async handleSave(form, index = null) {
      let valid = await new Promise((resolve, reject) => {
        if (index === null) {
          resolve(true)
        }

        return this.$refs.form[index].validate((valid) => resolve(valid))
      })

      if (!valid) {
        this.$message.warn(__('package_error_mandatory'))
        return
      }

      let key = form.extra_info_id ? 'update_customized_other_info' : 'create_customized_other_info'

      if (index !== null && form.type_flag === 3) {
        this.$set(this, 'modal', {
          visible: true,
          form,
          type: ''
        })

        return
      }

      let data = this.fmtFormData(form)
      let { extra_info_id } = (
        await ajax.postBody(ADMIN_API.act[key], {
          data
        })
      ).result

      this.$set(this, 'modal', this.$options.data().modal)

      if (!form.extra_info_id) {
        form.extra_info_id = extra_info_id
        this.$emit('update:allData', this.fmtItems2AllData())
        this.$emit('addNew', extra_info_id)
      } else {
        this.$message.success(this.$t('global_success'))
        this.$emit('update:allData', this.fmtItems2AllData())
      }
    },
    async handleDelete(data, index) {
      // let differenceItems = _.differenceWith(this.items, data, _.isEqual)

      // let res = await ajax.postBody(ADMIN_API.act.bind_package_old_otherinfos, {
      //   data: {
      //     package_id: this.package_id,
      //     old_otherinfo_id_list: differenceItems.map(
      //       (item) => item.extra_info_id
      //     )
      //   }
      // })

      let delRes = this.items.splice(index, 1)[0]
      this.$emit('deleted', delRes.extra_info_id)
      this.$emit('update:allData', this.fmtItems2AllData())
    },
    handleAddAnother() {
      this.items.push({
        ask_option: 1,
        category_id: '0',
        extra_info_id: 0,
        date_option: 2,
        info: [],
        package_id: this.package_id,
        position: 0,
        required: 1,
        type_flag: ''
      })
      this.$emit('update:allData', this.fmtItems2AllData())
    }
  }
}
</script>

<style lang="scss" scoped>
$commonPX: 12px;
.footer {
  margin-top: $commonPX;
}

.body {
  .form-item {
    display: flex;
    align-items: center;
  }
  .form-card {
    flex: 1;
    margin-right: $commonPX;
    margin-top: $commonPX;
    padding: $commonPX;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }
  ::v-deep {
    .ant-form-item label {
      margin: 8px 0;
    }
    .special_multi_language_input-container {
      width: 100%;
    }
    .ant-radio-group .ant-radio-button-wrapper {
      margin-right: $commonPX;
      border-radius: 3px;
      &.ant-radio-button-wrapper-checked {
        border-color: #1890ff;
        border-bottom-width: 1px;
        box-shadow: -1px 0 0 0 #1890ff;
        &.ant-radio-button-wrapper-disabled {
          background-color: #1890ff;
        }
      }
    }
    .ant-form-item {
      margin-bottom: 0;
    }
  }
  .__info {
    display: flex;
    justify-content: space-between;
    margin-top: $commonPX;
    .__infoInput {
      width: 49%;
    }
  }
  .form-card__footer {
    margin-top: $commonPX;
    &Preview {
      margin-left: $commonPX;
    }
  }
}
</style>
