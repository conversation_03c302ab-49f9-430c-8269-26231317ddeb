<!-- original: klk-admin msp  -->
<template>
  <a-drawer
    class="merge-other-info-container"
    :title="$t('73057')"
    :width="1000"
    :visible="visible"
    :headerStyle="drawerHeaderStyle"
    :bodyStyle="drawerBodyStyle"
    placement="right"
    @close="handleClose"
  >
    <a-spin :spinning="loading" :delay="100">
      <div v-if="group" class="content-box">
        <div class="guide-content" v-html="$t('73060')"></div>
        <h3 style="display: flex; justify-content: space-between;">
          <span>{{ $t('73058') }}</span>
          <a-button v-if="isMerged" type="danger" @click="handleSplit">
            {{ $t('73064') }}
          </a-button>
        </h3>

        <other-info-item
          ref="otherInfoItem"
          class="merge-other-info-item"
          :fields="fields"
          :group="group"
          :validator="validator"
          :inject-field-key="true"
          v-bind="commonAttrs"
          @dragEnding="handleDragEnding"
        >
          <template #groupName="scoped">
            {{ scoped.groupName }}
          </template>
        </other-info-item>
      </div>

      <div class="content-box">
        <div class="transfer-content">
          <SimpleTransfer
            ref="transfer"
            v-if="currentMergeFieldData"
            :key="currentMergeFieldData && currentMergeFieldData.key"
            :lock="!currentMergeKey"
            :field-key="currentMergeFieldData.key"
            :field-name="currentMergeFieldData.name"
            :source-data="currentMergeFieldData.source_value"
            :target-data.sync="currentMergeFieldData.target_value"
            @updateTarget="handleUpdateTarget"
          />
        </div>
      </div>

      <div class="content-box">
        <h3>{{ $t('73059') }}</h3>

        <template v-for="(item, index) in fromOtherInfo">
          <other-info-item
            ref="selectItem"
            :key="index"
            :fields="item.style.fields"
            :group="item"
            :validator="validator"
            :disabledDrag="true"
            v-bind="commonAttrs"
          />
        </template>
      </div>
    </a-spin>

    <div class="footer">
      <a-button @click="handleClose">{{ $t('73074') }}</a-button>
      <a-button type="primary" @click="handleSave">{{ $t('73077') }}</a-button>
    </div>
  </a-drawer>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import OtherInfoItem from './OtherInfoItem'
import { validator, confirm } from './utils'
import SimpleTransfer from './components/Transfer'
import { getEditLang } from '@activity/utils'

export default {
  name: 'MergeOtherInfoDrawer',
  components: {
    SimpleTransfer,
    OtherInfoItem
  },
  inject: ['refreshExtraData'],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mergeInfo: {
      type: Array,
      required: true
    },
    package_id: {
      type: Number,
      required: true
    },
    currentTitleId: {
      type: Number,
      default: 0
    },
    isMerged: {
      type: Boolean,
      default: false
    },
    originalData: {
      type: Object,
      required: true
    }
  },
  data() {
    this.MERGING_CLASS_NAME = '--title-merging'

    return {
      drawerHeaderStyle: {
        borderBottom: 'none',
        paddingLeft: '16px',
        paddingRight: '16px'
      },
      drawerBodyStyle: {
        padding: '60px 16px'
      },

      loading: false,

      group: null,
      mergeFields: [],

      currentMergeFieldData: null,
      currentMergeKey: null, // The transfer box is activated only when it has a value

      fromOtherInfo: []
    }
  },
  watch: {
    visible: {
      immediate: true,
      async handler(v) {
        if (v) {
          this.initData()
        }
      }
    },
    fields: {
      deep: true,
      handler() {
        const fn = _.debounce(() => {
          this.$refs.otherInfoItem?.$refs?.form?.validate?.()
        }, 600)

        fn()
      }
    },
    clickTarget(target) {
      let MAX_LAYER = 8

      while (MAX_LAYER && target && !target.dataset?.fieldKey) {
        target = target?.parentNode
        MAX_LAYER--
      }

      const fieldKey = target?.dataset?.fieldKey

      // reset current merge key only in the field form
      if (!fieldKey) {
        return
      }

      const className = this.MERGING_CLASS_NAME
      const ele = document.querySelector('.' + className)
      ele && ele.classList.toggle(className, false)
      target.classList.toggle(className, true)

      if (this.needMergeKeys.includes(fieldKey)) {
        this.settingCurrentMergeFieldData(fieldKey)
      } else {
        this.currentMergeKey = null
      }
    }
  },
  computed: {
    ...mapState({
      clickTarget: (state) => state.clickTarget
    }),
    ...mapGetters({
      allLanguages: 'getAllLanguagesOpts',
      countries: 'getCountryOpts'
    }),
    commonAttrs() {
      return {
        headmost: true,
        decrease: false,
        closeMergeChoose: true,
        showHeaderOperation: false,
        showAddNewBtn: false,
        showDeleteBtn: false,
        showInputDeleteBtn: false,
        showInputAddNewBtn: false,
        countries: this.countries,
        languages: this.allLanguages
      }
    },
    needMergeKeys() {
      return this.mergeFields.map((item) => item.key)
    },
    fields() {
      return this.group?.style?.fields || []
    },
    mergerPackageInfo() {
      return this.mergeInfo.map((pkg) => `「${pkg.package_id}」 - ${pkg.package_name}`).join(' & ')
    },
    merge_info() {
      return this.mergeInfo.map((pkg) => ({
        package_id: pkg.package_id,
        title_id: pkg.title_id
      }))
    }
  },
  methods: {
    validator(group, field, rule, value, fields, callback) {
      return validator.call(this, group, field, rule, value, fields, callback, true)
    },
    getRequestParams() {
      return {
        package_id: this.package_id,
        title_id: this.currentTitleId,
        merge_info: this.merge_info
      }
    },
    async initData(data = this.originalData) {
      this.loading = true
      const { instance_datas, need_merge_field, from_otherinfo } = data

      instance_datas.style.fields.forEach((field) => {
        const needMergeRes = _.find(need_merge_field, {
          key: field.key
        })

        if (needMergeRes && Array.isArray(field.value)) {
          const { target_value } = needMergeRes

          field.value.forEach((val) => {
            const index = target_value.findIndex((item) => item.id === val.id)
            target_value[index] = Object.assign(val, target_value[index] || {})
          })
        }
      })

      this.$set(this, 'group', instance_datas)
      this.$set(this, 'mergeFields', need_merge_field)
      this.$set(this, 'fromOtherInfo', from_otherinfo)
      this.settingCurrentMergeFieldData(need_merge_field?.[0]?.key, true)
      this.loading = false

      await this.$nextTick()
    },
    // async refreshMergedData() {
    //   this.loading = true
    //
    //   const response = await ajax.post(ADMIN_API.act.get_package_otherinfos_for_manual_merge, {
    //     data: {
    //       package_id: this.package_id,
    //       title_id: this.currentTitleId,
    //       merge_info: this.merge_info
    //     }
    //   })
    //
    //   this.initData(response)
    //   this.loading = false
    // },
    async handleSplit() {
      let flag = await confirm.call(this, {
        content: __('73078'),
        storeKey: 'merge-split'
      })

      if (flag) {
        this.loading = true
        await ajax.post(ADMIN_API.act.delete_combo_package_otherinfos_by_manual_merge, {
          data: this.getRequestParams()
        })

        this.loading = false
        this.$message.success(this.$t('global_success'))
        // this.refreshMergedData()
        this.refreshExtraData()
        this.$emit('update:visible', false)
      }
    },
    async handleSave() {
      const validate = await this.$refs.otherInfoItem.validateForm()

      if (!validate) {
        this.$message.warn(this.$t('package_error_mandatory'))
        return
      }

      let flag = await confirm.call(this, {
        content: __('73076'),
        storeKey: 'merge-confirm',
        cancelText: __('73075')
      })

      if (flag) {
        this.loading = true
        const { title_id } = await ajax.post(ADMIN_API.act.save_combo_package_otherinfos_by_manual_merge, {
          data: this.getPostData()
        })

        this.loading = false
        this.$message.success(this.$t('global_success'))
        this.refreshExtraData({ title_id })
        this.$emit('update:visible', false)
      }
    },
    getPostData() {
      const { group_id, title_id, style } = this.group

      return {
        activity_id: +this.$route.params.id,
        package_id: this.package_id,
        language: getEditLang(),
        otherinfo: {
          group_id,
          title_id,
          fields: style.fields.map((item) => {
            return {
              key: item.key,
              value: item.value
            }
          })
        },
        merge_info: this.merge_info
      }
    },
    async handleClose() {
      let flag = await confirm.call(this, {
        content: __('73072'),
        storeKey: 'merge-leave'
      })

      if (flag) {
        this.$emit('update:visible', false)
      }
    },
    // other info form -> transfer
    handleDragEnding(data) {
      this.$set(this.currentMergeFieldData, 'target_value', data)
    },
    // transfer -> other info form
    handleUpdateTarget(data) {
      if (!this.currentMergeKey) return

      this.group.style.fields.forEach((field) => {
        if (field.key === this.currentMergeKey) {
          field.value = data
        }
      })
    },
    settingCurrentMergeFieldData(fieldKey, isInit = false) {
      if (!fieldKey || fieldKey === this.currentMergeKey) {
        return
      }

      this.group.style.fields.forEach((field) => {
        if (field.key === fieldKey) {
          this.$set(
            this,
            'currentMergeFieldData',
            this.mergeFields.find((item) => item.key === fieldKey)
          )
          if (isInit) {
            this.currentMergeKey = null
          } else {
            this.currentMergeKey = fieldKey
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.content-box {
  margin-bottom: 24px;
}

.guide-content {
  padding: 8px 16px;
  margin-bottom: 24px;
  background: #fffbe6;
  border: 1px solid #ffe58f;
  box-sizing: border-box;
  border-radius: 2px;
}

.footer {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  padding: 10px 22px;
  border: 1px solid #f0f0f0;
  background-color: #fff;
  text-align: right;
  z-index: 999;

  ::v-deep .ant-btn {
    margin-left: 12px;
  }
}

.merge-other-info-container ::v-deep {
  .--is-empty-list {
    width: 100%;
    height: 32px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
  }
  .--title-merging {
    .--is-empty-list {
      position: relative;
      top: 0;
    }
    .ant-input,
    .--is-empty-list {
      border-color: #40a9ff;
    }
  }
}
</style>
