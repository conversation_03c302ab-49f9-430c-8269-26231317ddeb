<template>
  <div class="extra-preview">
    <div v-for="(item, index) of otherInfo" :key="index" class="other-info-preview-item">
      <div class="__title">
        {{ item.title }}
      </div>
      <div v-for="(data, dataIdx) of item.data" :key="dataIdx" class="__content">
        <div class="__tips" v-if="data.tips">
          {{ data.tips }}
        </div>

        <template v-if="(data.fixed && data.fixed.length) || (data.draggable && data.draggable.length)">
          <template v-if="data.fixed && data.fixed.length">
            <div v-for="(fixed, fixedIdx) of data.fixed" :key="fixedIdx" class="fixed-item">
              {{ getName(fixed) }}
              <label>
                <a-button
                  v-if="canBeEditMerged(fixed.data)"
                  type="primary"
                  size="small"
                  style="margin-right: 12px;"
                  @click="handleToMerge({ row: fixed.data })"
                  >View and edit</a-button
                >
              </label>
            </div>
          </template>

          <template v-if="data.draggable && data.draggable.length">
            <DraggableList
              v-model="data.draggable"
              class="package_extra-draggable"
              itemKey="extra_info_id"
              :disabled="disabled"
            >
              <template v-slot:default="slotProps">
                <div class="package_extra-draggable_content">
                  {{ getTitle(slotProps.item) }}
                  <label>
                    <a-button
                      v-if="canBeEditMerged(slotProps.item)"
                      type="primary"
                      size="small"
                      style="margin-right: 12px;"
                      @click="handleToMerge({ row: slotProps.item })"
                      >View and edit</a-button
                    >
                    <svg-icon icon-name="menu" />
                  </label>
                </div>
              </template>
            </DraggableList>
          </template>
        </template>

        <div class="__empty" v-else>
          {{ $t('29158') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DraggableList from '../../../components/DraggableList'
import { bestMatchLang } from '@activity/utils'
import { travellerGroupsSort, infoGroupsSort } from './utils'

export default {
  name: 'OtherInfoFrontPreview',
  components: {
    DraggableList
  },
  props: {
    oldOtherInfoCheckedIds: {
      type: Array,
      required: true
    },
    oldOtherInfoDataCustomData: {
      type: Array,
      required: true
    },
    oldOtherInfoData: {
      type: Array,
      required: true
    },
    contactInfoChecked: {
      type: Array,
      required: true
    },
    contactInfoOptions: {
      type: Array,
      required: true
    },
    updateData: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      otherInfo: [
        {
          title: __('other_info_required_participants'),
          data: [
            {
              tips: __('package_xinfo_ask_once'),
              fixed: [],
              draggable: []
            },
            {
              tips: __('package_xinfo_ask_each'),
              fixed: [],
              draggable: []
            }
          ]
        },
        {
          title: __('package_tag_extra_info'),
          data: [
            {
              fixed: [],
              draggable: []
            }
          ]
        },
        {
          title: __('other_info_contact'),
          data: [
            {
              fixed: [],
              draggable: []
            }
          ]
        }
      ]
    }
  },
  watch: {
    // 联系信息
    contactInfoChecked: {
      deep: true,
      immediate: true,
      handler(v) {
        let fixed = this.contactInfoOptions.reduce((acc, curr) => {
          if (v.includes(curr.value)) {
            return [
              ...acc,
              {
                name: curr.label
              }
            ]
          }

          return acc
        }, [])

        _.set(this.otherInfo, '2.data.0.fixed', fixed)
      }
    },
    // 其他用户信息（未结构化）&& 添加新的用户信息（未结构化）
    tempOldOtherInfoCheckedIdsForWatch: {
      immediate: true,
      deep: true,
      handler(v, oldV) {
        oldV = oldV || []

        let xor = _.xorWith(v, oldV)
        let oldOtherInfoUnitLevel = _.get(this.otherInfo, '0.data.1.draggable', [])
        let oldOtherInfoUnitLevelIds = oldOtherInfoUnitLevel.map((item) => item.extra_info_id)
        let oldOtherInfoBookingLevel = _.get(this.otherInfo, '1.data.0.draggable', [])
        let oldOtherInfoBookingLevelIds = oldOtherInfoBookingLevel.map((item) => item.extra_info_id)

        xor.forEach((id) => {
          let current = _.find(this.oldOtherInfoDataCustomData, {
            extra_info_id: id
          })

          if (!current) {
            current = _.find(this.allOldOtherInfoCheckData, {
              extra_info_id: id
            })
          }

          // ask_option, 1: booking, 2: unit
          if (current.ask_option === 1) {
            if (oldOtherInfoBookingLevelIds.includes(id)) {
              oldOtherInfoBookingLevel = oldOtherInfoBookingLevel.filter((item) => item.extra_info_id !== id)
            } else {
              oldOtherInfoBookingLevel.push(current)
            }
          } else {
            if (oldOtherInfoUnitLevelIds.includes(id)) {
              oldOtherInfoUnitLevel = oldOtherInfoUnitLevel.filter((item) => item.extra_info_id !== id)
            } else {
              oldOtherInfoUnitLevel.push(current)
            }
          }
        })

        _.set(this.otherInfo, '0.data.1.draggable', oldOtherInfoUnitLevel)
        _.set(this.otherInfo, '1.data.0.draggable', oldOtherInfoBookingLevel)
      }
    },
    // 所有结构化的 groups 处理
    updateData: {
      immediate: true,
      deep: true,
      handler() {
        this.debounceSettingPreviewData()
      }
    }
  },
  computed: {
    allOldOtherInfoCheckData() {
      return this.oldOtherInfoData.reduce((acc, curr) => {
        return [...acc, ...curr.content]
      }, [])
    },
    tempOldOtherInfoCheckedIdsForWatch() {
      return JSON.parse(JSON.stringify(this.oldOtherInfoCheckedIds))
    }
  },
  methods: {
    canBeEditMerged(data) {
      // -1，默认值；0, 不可 merge；1，自动 merge，不可 edit；2，手动 merge，可 edit
      const mergeType = _.get(data, 'combo_merge_info.merge_type', -1)

      return _.get(data, 'combo_merge_info.is_merged', 0) === 1 && mergeType !== 1
    },
    handleToMerge({ row }) {
      this.$emit('toViewMerged', row)
    },
    debounceSettingPreviewData: _.debounce(function() {
      let { otherCustom } = this.chunkCustomOtherInfo()
      let { info, postInfo, termAndCondition } = this.updateData

      // 出行人 > 邮寄信息 > 自定义 > 常用附加信息> T&C
      let otherGroups = [
        ...this.sortByArray(postInfo),
        ...this.sortByArray(info, infoGroupsSort), // 常用附加信息
        ...this.sortByArray(otherCustom), // 自定义
        ...this.sortByArray(termAndCondition) // T&C
      ]
      _.set(this.otherInfo, '1.data.0.fixed', otherGroups)
      // 出行人必填信息设置
      this.getTravelerOtherInfo()
    }),
    chunkCustomOtherInfo() {
      return this.updateData.customized.reduce(
        (acc, curr) => {
          // 自定other info 没有 ref_field_tag 但是要展示在other info 模块
          if (curr.ref_field_tag || curr.info_type === -1) {
            acc.otherCustom.push(curr)
          } else {
            // custom other info
            let writeLevel = _.find(curr.fields, {
              key: 'write_level'
            })

            if (writeLevel) {
              let name = _.find(curr.fields, {
                key: 'customized_title_name'
              })

              let customName = `${curr.group_name} - ${_.get(name, 'value.content_en', '')}`

              if (writeLevel.value === 'booking') {
                acc.bookingCustom.push({
                  name: customName,
                  data: curr
                })
              } else {
                acc.unitCustom.push({
                  name: customName,
                  data: curr
                })
              }
            } else {
              acc.otherCustom.push({
                name: curr,
                data: curr
              })
            }
          }

          return acc
        },
        {
          otherCustom: [],
          // 下面只是返回了 fixed name, 而不是对象
          unitCustom: [], // :string[]
          bookingCustom: []
        }
      )
    },
    getCommonOtherInfoIds() {
      return this.otherInfo.reduce(
        (acc, curr) => [
          ...acc,
          ...curr.data
            .reduce((acc2, curr2) => [...acc2, ...(curr2.draggable || [])], [])
            .map((item) => item.extra_info_id)
        ],
        []
      )
    },
    // 出行人必填信息设置
    getTravelerOtherInfo() {
      let { traveller } = this.updateData
      let { unitCustom, bookingCustom } = this.chunkCustomOtherInfo()

      let { booking, unit } = traveller.reduce(
        (acc, curr) => {
          for (let group of curr) {
            if (group.write_level === 0) {
              acc.booking.push(group)
            } else {
              acc.unit.push(group)
            }
          }

          return acc
        },
        {
          booking: [],
          unit: []
        }
      )

      booking = this.sortByArray(booking, travellerGroupsSort)
      unit = this.sortByArray(unit, travellerGroupsSort)

      _.set(this.otherInfo, '0.data.0.fixed', [...booking, ...bookingCustom])

      _.set(this.otherInfo, '0.data.1.fixed', [...unit, ...unitCustom])
    },
    sortByArray(data, arr) {
      if (arr) {
        data = _.sortBy(data, (item) => {
          return _.findIndex(arr, (curr) => curr === item.ref_field_tag)
        })
      }
      return data.map((item) => ({
        name: item.group_name,
        data: item
      }))
    },
    getTitle(item) {
      if (Object.prototype.toString.call(item) !== '[object Object]') {
        return item
      }

      return `${item.extra_info_id} - ${bestMatchLang('name', 'language', item.details || item.info)}`
    },
    getName(data) {
      const title_id = data?.data?.title_id ?? null
      const fields = data?.data?.fields ?? null
      let name = _.find(fields, {
        key: 'customized_title_name'
      })
      if (name) {
        let customName = _.get(name, 'value.content_en', '')
        return `${title_id} - ${data.name} - ${customName}`
      }
      return title_id ? `${title_id} - ${data.name}` : data.name
    }
  }
}
</script>

<style scoped lang="scss">
$chunkMargin: 32px;
$itemMargin: 16px;

.extra-preview {
  margin-top: 36px;
}
.other-info-preview-item {
  color: #000;
  margin: $chunkMargin 0 0 $itemMargin;
  .__rectangle {
    width: 4px;
    height: 20px;
    background-color: #0091ff;
  }
  .__container {
    margin-left: $itemMargin;
  }
  .__title {
    display: flex;
    align-items: center;
    margin-bottom: $itemMargin;
    font-weight: 600;
    font-size: 18px;
    &:before {
      content: '';
      width: 4px;
      height: 20px;
      display: inline-block;
      background-color: #0091ff;
      margin-right: 12px;
    }
  }
  .__content {
    margin-left: $itemMargin;
    margin-bottom: $chunkMargin;
    .__tips {
      margin-bottom: 6px;
    }
    .__empty {
      font-size: 10px;
      color: #b2b2b2;
    }
    .fixed-item {
      display: flex;
      justify-content: space-between;
      width: 100%;
      max-width: 600px;
      margin-top: 4px;
      padding: 8px 12px;
      background: #f2f6fc;
      word-break: break-word;
    }
  }
  .package_extra-draggable {
    margin: 6px 0;
  }
}
</style>
