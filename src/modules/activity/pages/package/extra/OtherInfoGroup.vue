<template>
  <div class="other-info-group-container" :class="styleType">
    <template v-if="!$attrs.polish">
      <template v-if="isCreateGuideType">
        <a-tooltip :title="$t('88705')" placement="right">
          <a-select
            v-model="calcCheckedGroups"
            mode="multiple"
            style="width: 100%; margin: 4px 0 0 0"
            :placeholder="$t('83903')"
            :dropdown-class-name="'merchant-select-style'"
            :filter-option="filterGroupOption"
          >
            <a-select-option
              v-for="group in checkList"
              v-show="showCheckList"
              :key="group.group_id"
              :value="group.group_id"
              :disabled="!!$attrs.disabled || $attrs.disabledCheckbox || !!group.style.disabled"
              :class="{
                'v-disabled-exclude': group.ref_field_tag === 'ticket_type'
              }"
              @click="handleSelectGroupOption(group)"
            >
              {{ group.group_name }}
              <a-tooltip v-if="group.tooltip" placement="left">
                <template slot="title">
                  <span v-html="group.tooltip"></span>
                </template>
                <a-icon style="margin-left: 4px" type="info-circle" />
              </a-tooltip>
            </a-select-option>
          </a-select>
        </a-tooltip>
      </template>
      <a-checkbox-group v-else v-model="checkedGroups" style="width: 100%">
        <component
          :is="group.tooltip ? 'a-tooltip' : 'span'"
          v-for="group in checkList"
          v-show="showCheckList"
          :key="group.group_id"
          placement="left"
        >
          <template slot="title">
            <span v-html="group.tooltip || ''"></span>
          </template>
          <BasicOverflowCheckbox
            :value="group.group_id"
            :disabled="
              !!$attrs.disabled ||
              $attrs.disabledCheckbox ||
              getOtherInfoDisabled(group.info_type) ||
              !!group.style.disabled
            "
            :class="{
              'v-disabled-exclude': group.ref_field_tag === 'ticket_type'
            }"
            width="28%"
            @click="handleSelectGroupOption(group)"
          >
            {{ group.group_name }}
          </BasicOverflowCheckbox>
        </component>
        <!-- 调试注释，记得写回 BasicOverflowCheckbox 组件 -->
        <!-- :disabled="!!$attrs.disabled || $attrs.disabledCheckbox" -->
      </a-checkbox-group>
    </template>

    <DraggableList
      v-model="currGroups"
      class="other-info-group-draggable"
      :disabled="!!$attrs.disabled || !$attrs.canDrag || isChildDragging"
      item-key="uuid"
      :get-key="getDraggableItemKey"
      :handle="true"
    >
      <component
        :is="getOtherInfoDisabled(slotProps.item.info_type) ? 'a-tooltip' : 'div'"
        slot-scope="slotProps"
        placement="right"
      >
        <div v-if="getOtherInfoDisabled(slotProps.item.info_type)" slot="title">{{ $t('48231') }}</div>
        <OtherInfoItemMerchant
          v-if="styleType === 'create-guide-type'"
          ref="otherInfoItem"
          v-bind="$attrs"
          :disabled="getOtherInfoDisabled(slotProps.item.info_type) || !!slotProps.item.style.disabled"
          :fields="slotProps.item.style.fields"
          :group="slotProps.item"
          :index="slotProps.index"
          :countries="countries"
          :headmost="$attrs.headmost || headmostCompute(slotProps.item, slotProps.index)"
          :decrease="decreaseCompute(slotProps.item)"
          :languages="allLanguages"
          :validator="validator"
          :use-itinerary-pick-up-data="!!slotProps.item.style.disabled"
          :filter-option="filterOption"
          @addGroup="handleAddGroup"
          @deleteGroup="handleDelGroup"
          @addItem="handleAddItem"
          @deleteItem="handleDelItem"
          @dragging="handleDragging"
          @preview="handlePreview"
          @toViewMerged="(data) => $emit('toViewMerged', data)"
        ></OtherInfoItemMerchant>
        <other-info-item
          v-else
          ref="otherInfoItem"
          v-bind="$attrs"
          :disabled="getOtherInfoDisabled(slotProps.item.info_type) || !!slotProps.item.style.disabled"
          :fields="slotProps.item.style.fields"
          :group="slotProps.item"
          :index="slotProps.index"
          :countries="countries"
          :headmost="$attrs.headmost || headmostCompute(slotProps.item, slotProps.index)"
          :decrease="decreaseCompute(slotProps.item)"
          :languages="allLanguages"
          :validator="validator"
          :use-itinerary-pick-up-data="!!slotProps.item.style.disabled"
          :filter-option="filterOption"
          @addGroup="handleAddGroup"
          @deleteGroup="handleDelGroup"
          @addItem="handleAddItem"
          @deleteItem="handleDelItem"
          @dragging="handleDragging"
          @preview="handlePreview"
          @toViewMerged="(data) => $emit('toViewMerged', data)"
        />
      </component>
    </DraggableList>

    <!-- <template v-for="(group, index) in groups">
      <other-info-item
        v-if="validateItem(group)"
        ref="otherInfoItem"
        :key="group.uuid"
        v-bind="$attrs"
        :fields="group.style.fields"
        :group="group"
        :index="index"
        :countries="countries"
        :headmost="headmostCompute(group, index)"
        :decrease="decreaseCompute(group)"
        :languages="allLanguages"
        :validator="validator"
        @addGroup="handleAddGroup"
        @deleteGroup="handleDelGroup"
      />
    </template> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import OtherInfoItemMerchant from './OtherInfoItemMerchant.vue'
import OtherInfoItem from './OtherInfoItem.vue'
import { getUuid } from '@activity/utils'
import BasicOverflowCheckbox from '@activity/pages/components/BasicOverflowCheckbox.vue'
import DraggableList from '@activity/components/DraggableList.vue'
import { validator } from './utils'
import { isMerchant } from '@/env'

export default {
  name: 'OtherInfoGroup',
  components: {
    OtherInfoItemMerchant,
    OtherInfoItem,
    BasicOverflowCheckbox,
    DraggableList
  },
  props: {
    styleType: {
      type: String,
      default: '' // create-guide-type
    },
    originalGroups: {
      required: true,
      type: Array
    },
    groupCheckedList: {
      required: true,
      type: Array
    },
    showCheckList: {
      type: Boolean,
      default: true
    },
    packageId: {
      type: [String, Number],
      default: ''
    },
    filterOption: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      groups: [],
      checkList: [],
      otherInfoItemList: [],
      isChildDragging: false,
      groupsInitialized: false
    }
  },
  computed: {
    allLanguages: {
      get() {
        return this.getAllLanguagesOpts()
      },
      set() {}
    },
    countries: {
      get() {
        return this.getCountryOpts()
      },
      set() {}
    },
    originalGroupsIdList() {
      return this.originalGroups.map((item) => item.group_id)
    },
    calcCheckedGroups: {
      set(newV) {
        const differenceCheckedGroups = _.difference(this.groupCheckedList, this.originalGroupsIdList)
        this.$emit('update:groupCheckedList', [...newV, ...differenceCheckedGroups]) // .sync, binding data
        this.$nextTick(() => {
          this.$emit('changeData')
          this.$emit('updateData', this.getPostData())
        })
      },
      get() {
        const arr = this.groupCheckedList?.filter((key) => {
          return this.originalGroups?.find((o) => o.group_id === key)
        })
        return arr
      }
    },
    checkedGroups: {
      set(newV) {
        // antdv 把不在options里面的id删掉了，需要补回来
        const differenceCheckedGroups = _.difference(this.groupCheckedList, this.originalGroupsIdList)
        this.$emit('update:groupCheckedList', [...newV, ...differenceCheckedGroups]) // .sync, binding data
        this.$nextTick(() => {
          this.$emit('changeData')
          this.$emit('updateData', this.getPostData())
        })
      },
      get() {
        return this.groupCheckedList
      }
    },
    currGroups: {
      get() {
        return this.groups.filter((group) => this.validateItem(group))
      },
      set(data) {
        this.groups = data
      }
    },
    isCreateGuideType() {
      return this.styleType === 'create-guide-type'
    }
  },
  watch: {
    groups: {
      deep: true,
      immediate: true,
      handler(newV, oldV) {
        this.$emit('updateData', this.getPostData())

        if (this.groupsInitialized) {
          this.$emit('changeData')
        }

        if ((!oldV || (Array.isArray(oldV) && !oldV.length)) && this.$el) {
          this.groupsInitialized = true
        }
      }
    },
    checkedGroups: {
      deep: true,
      handler(newV, oldV) {
        let xor = _.xorWith(newV, oldV)

        // 能拖拽的时候不打乱原有的顺序
        if (this.$attrs.canDrag) {
          const groups = this.groups.filter((group) => oldV.includes(group.group_id))
          const currentGroupIds = groups.map((group) => group.group_id)
          xor.forEach((groupId) => {
            // 需要新增
            const target = this.originalGroups.find((group) => group.group_id === groupId)
            if (target && newV.includes(groupId) && !currentGroupIds.includes(groupId)) {
              let copy = _.cloneDeep(target)
              this.cleanGroup(copy)
              groups.unshift(this.getGroupDefaultValue(copy))
            }
          })
          this.$set(this, 'groups', groups)
          return
        }

        let groups = _.cloneDeep(this.checkedGroups)
          .sort((a, b) => a - b)
          .reduce((acc, curr) => {
            let current = this.groups.filter((group) => group.group_id === curr)

            if (current.length) {
              if (xor.includes(curr)) {
                return oldV.length > newV.length
                  ? acc
                  : [
                      // add one
                      ...acc,
                      ...current.map((group) => this.getGroupDefaultValue(group))
                    ]
              } else {
                return [...acc, ...current]
              }
            }

            let originIndex = _.findIndex(this.originalGroups, {
              group_id: curr
            })

            // checkedGroups 是共用的，但 originalGroups 是私有的，所以需要排除非当前的 group 的情况
            if (-1 === originIndex) return acc

            let copy = _.cloneDeep(this.originalGroups[originIndex])
            return [...acc, this.getGroupDefaultValue(copy)]
          }, [])

        this.$set(this, 'groups', groups)
      }
    },
    originalGroups: {
      immediate: true,
      deep: true,
      handler(newV, oldV) {
        if (!_.isEqual(newV, oldV)) {
          this.checkList = _.uniqBy(this.originalGroups, 'group_id')
          this.checkList.sort((a, b) => a.group_display_order - b.group_display_order) // 需要使用 group_display_order 排序
          const groups = _.cloneDeep(
            this.originalGroups.map((group) => {
              group.style.fields = group.style.fields.map((field) => {
                if (!field.value) {
                  field.value = this.getFieldDefaultValue(field).value
                }
                return field
              })
              return group
            })
          )

          this.$set(this, 'groups', groups)
        }
      }
    }
  },
  async beforeMount() {
    this.countries = await this.$store.dispatch('actionGetTaxonomyCountries', {
      include_not_show_country: 0,
      language: window.KLK_LANG
    })
    await this.$store.dispatch('actionGetAllLanguage')
  },
  methods: {
    ...mapGetters(['getAllLanguagesOpts', 'getCountryOpts']),
    getOtherInfoDisabled(infoType) {
      const infoTypes = [-1]
      return this.$attrs.disabled || (infoTypes.includes(infoType) && isMerchant)
    },
    validator(group, field, rule, value, fields, callback) {
      return validator.call(this, group, field, rule, value, fields, callback)
    },
    async validateForm() {
      let all = await Promise.all(this.otherInfoItemList.map((item) => item.validateForm()))
      return all.every((item) => item)
    },
    getFieldDefaultValue(field) {
      const fieldType = field.style.type
      const optionType = field.options.type

      if (fieldType === 16) {
        field.value = field.options.default || {
          id: 0,
          content: '',
          content_en: ''
        }
      } else if (fieldType === 17) {
        field.value = field.options.default || [
          {
            id: 0,
            content: '',
            content_en: ''
          }
        ]
      } else if (optionType === 6) {
        field.value = field.options.default || {
          id: 0,
          content: ''
        }
      } else if (optionType === 7) {
        field.value = field.options.default || {
          min: '',
          max: ''
        }
      } else if (field.style.multiple_selection) {
        field.value = !field.options.default
          ? []
          : Array.isArray(field.options.default)
          ? field.options.default
          : [field.options.default]
      } else {
        field.value = field.options.default || ''
      }

      return field
    },
    getGroupDefaultValue(group) {
      return {
        ...group,
        style: {
          ...group.style,
          fields: group.style.fields.map((field) => this.getFieldDefaultValue(field))
        }
      }
    },
    getPostData() {
      return this.groups.reduce((acc, curr) => {
        if (this.checkedGroups.includes(curr.group_id)) {
          return [
            ...acc,
            {
              ...curr,
              fields: curr.style.fields.map((field) => ({
                key: field.key,
                value: field.value
              }))
            }
          ]
        }
        return acc
      }, [])
    },
    validateItem(group) {
      return Boolean(this.checkedGroups.includes(group.group_id) && _.get(group, 'style.fields', []).length)
    },
    handleAddGroup(group_id) {
      let originIndex = _.findIndex(this.originalGroups, { group_id })
      let copy = _.cloneDeep(this.originalGroups[originIndex])
      this.cleanGroup(copy)

      let index = _.findLastIndex(this.groups, { group_id })
      this.groups.splice(index + 1, 0, {
        ...this.getGroupDefaultValue(copy),
        uuid: getUuid()
      })
      this.$forceUpdate()
    },
    cleanGroup(group) {
      // 新建的时候要清掉一些数据：
      // 1.title_id
      if (group.title_id) {
        group.title_id = 0
      }

      // 2.field disabled 状态
      if (group && group.style && group.style.fields) {
        group.style.fields.forEach((field) => {
          if (
            field &&
            field.style &&
            field.style.disabled &&
            !field.key.includes('supply_api_mapping_other_info') // supply api mapping 字段需要遵循配置
          ) {
            field.style.disabled = 0
          }
        })
      }
    },
    decreaseCompute(group) {
      return this.groups.filter((item) => group.group_id === item.group_id).length > 1
    },
    handleDelGroup({ uuid }) {
      this.$confirm({
        title: this.$t('other_info_confirm_del'),
        onOk: () => {
          const index = this.groups.findIndex((group) => group.uuid === uuid)
          this.groups.splice(index, 1)
        }
      })
    },
    headmostCompute(group, index) {
      return _.findIndex(this.currGroups, { title_id: group.title_id }) === index
    },
    handleAddItem(item) {
      this.otherInfoItemList.push(item)
    },
    handleDelItem(item) {
      const index = this.otherInfoItemList.indexOf(item)
      if (index >= 0) {
        this.otherInfoItemList.splice(index, 1)
      }
    },
    handleDragging(val) {
      // 子元素 dragging 的时候，父元素禁止drag
      this.isChildDragging = val
    },
    handlePreview(data) {
      this.$emit('preview', data)
    },
    filterGroupOption(inputValue, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
    },
    handleSelectGroupOption(group) {
      this.$emit('selectGroupOption', { group })
    },
    getDraggableItemKey(item) {
      return item.uuid
    }
  }
}
</script>

<style lang="scss">
.merchant-select-style.ant-select-dropdown {
  display: inline-block;
  width: auto !important;
  min-width: 400px;
  ul.ant-select-dropdown-menu {
    max-height: 284px;
  }
}
.other-info-group-container {
  .ant-checkbox-wrapper + .ant-checkbox-wrapper {
    margin-left: 0;
  }

  .ant-checkbox-wrapper {
    margin-right: 8px;
  }
}
</style>
