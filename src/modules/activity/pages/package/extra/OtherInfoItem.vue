<template>
  <div class="other-info-item-container">
    <a-form-model
      ref="form"
      :model="form"
      class="other-info-item__form"
      :label-col="{ span: 7 }"
      :wrapper-col="{ span: 16 }"
      :data-title-id="group.title_id"
      :label-align="
        [groupRefFieldTagDict.pick_up_location_scope].includes(group.ref_field_tag) ? 'left' : 'right'
      "
    >
      <svg-icon
        v-if="!$attrs.disabled && $attrs.canDrag"
        icon-name="menu"
        class="other-info-item__form-menu handle"
      />
      <!-- preview button group.is_preview ===1 展示"-->
      <div class="other-info-item-header">
        <!-- 能拖拽的时候，需要全部显示名字和增加按钮 -->
        <div v-if="$attrs.headmost || $attrs.canDrag" class="__left">
          <div class="other-info__groupName">
            <a-checkbox
              v-if="canBeSelectedToMerge"
              class="--selected-to-merge"
              :data-info-type="group.info_type"
              :checked="mergeOtherInfoDrawer.mergeInfo.some((curr) => curr.uuid === group.uuid)"
              :disabled="disabledMergedCheckbox"
              @change="(event) => changeChooseMerge({ event, group })"
            />

            <label class="__content">
              <span>
                <slot name="groupName" :group="group" :groupName="groupName">
                  {{ groupName }}
                  <!-- 临时走前端逻辑 -->
                  <a-tooltip
                    v-if="
                      !(
                        useItineraryPickUpData &&
                        groupRefFieldTagDict.pick_up_location_scope === group.ref_field_tag
                      ) && group.hint
                    "
                    placement="right"
                  >
                    <template slot="title">
                      {{ group.hint }}
                    </template>
                    <a-icon type="question-circle" style="margin-left: 4px" />
                  </a-tooltip>
                </slot>
              </span>
              <span v-if="group.tips" class="--tips">
                {{ group.tips }}
              </span>
            </label>
          </div>
        </div>

        <div v-if="showHeaderOperation" class="__right">
          <a-button
            v-if="group.is_preview === 1"
            :data-spm-module="getPreviewSpm"
            data-spm-virtual-item="__virtual"
            size="small"
            class="preview-btn"
            :class="{ polish: ($attrs.headmost || $attrs.canDrag) && group.is_multi && !$attrs.polish }"
            @click="handlePreview"
          >
            {{ $t('global_preview') }}
          </a-button>

          <a-button v-if="canBeEditMerged" type="primary" size="small" @click="handleViewMerged(group)">
            {{ $t('73056') }}
          </a-button>

          <template v-if="group.is_multi">
            <a-button
              v-if="!$attrs.polish && showAddNewBtn"
              type="link"
              icon="plus"
              class="btn-groupAdd"
              :disabled="!!$attrs.disabled"
              @click="$emit('addGroup', group.group_id)"
            >
              {{ $t('global_add') }}
            </a-button>
          </template>
        </div>
      </div>

      <div v-if="group.alert" class="other-info--alert" v-html="group.alert"></div>

      <div class="other-info--formItemContainer">
        <div
          class="--formItem"
          :class="{
            stripe: $attrs.stripe
          }"
          :style="`margin-top: ${$attrs.headmost || $attrs.canDrag ? '42px' : '12px'}`"
        >
          <div v-if="$attrs.decrease && !$attrs.polish && showDeleteBtn" class="group--del-container">
            <a-button
              v-if="showDeleteBtn"
              type="text"
              class="--del"
              :disabled="!!$attrs.disabled"
              @click="$emit('deleteGroup', { uuid: group.uuid })"
            >
              {{ $t('global_delete') }}
            </a-button>
          </div>

          <template v-for="(field, index) in fields">
            <a-form-model-item
              v-if="showField(field)"
              :key="index"
              :prop="field.key"
              :rules="{
                required: getRequired(field),
                trigger: ['blur', 'change'],
                validator: (rule, value, callback) =>
                  validator(group, field, rule, field.value, fields, callback)
              }"
              :class="{
                'form-item-no-label': !field.name,
                'form-item-vertical': field.style.vertical || [23, 24].includes(field.style.type)
              }"
            >
              <template v-if="field.name" #label>
                {{ field.name }}
              </template>
              <BasicFormPopover
                :message="field.hover"
                :auto-adjust-overflow="false"
                trigger="hover"
                placement="right"
                v-bind="getBasicFormPopoverAttrsByField(field)"
              >
                <!-- select -->
                <template v-if="field.style.type === 1">
                  <template v-if="field.options.type === 7">
                    <number-range
                      :items="field.options.items"
                      :value.sync="field.value"
                      :disabled="$attrs.polish || !!$attrs.disabled || getFieldDisabled(field)"
                      :class="{
                        'v-disabled-exclude': disabledExclude
                      }"
                    />
                  </template>
                  <template v-else>
                    <lazy-long-select
                      v-model="field.value"
                      opt-value-key="value"
                      opt-label-key="label"
                      class="lazy-long-select"
                      :class="{
                        '--disabled': $attrs.polish || !!$attrs.disabled || getFieldDisabled(field),
                        'v-disabled-exclude': disabledExclude
                      }"
                      :select-hint="$t('type_to_search')"
                      :ops-length="20"
                      :remote="getItemOpts(field, group).length > 300"
                      :multiple="Boolean(field.style.multiple_selection)"
                      :full-opts="getItemOpts(field, group)"
                      :disabled="
                        $attrs.polish ||
                        !!$attrs.disabled ||
                        getAllOptDisabled(field) ||
                        getFieldDisabled(field)
                      "
                      :allow-clear="field.options.type !== 9"
                      :use-tree-select="isTreeSelect(field)"
                      :all-value="getAllValue(field)"
                      @change="(v) => handleChange(v, field)"
                    >
                      <!-- field.options.type === 9 是有联动锁定关系的下拉项，如果能清除，容易造成联动的 key 被置空 -->
                      <!-- type 0:无ALL选项; 1:ALL的值是 int; 2:ALL的值是字符串 3:ALL后端特殊处理 -->
                      <template v-if="field.options.all_value && field.options.all_value.type" #headerOptions>
                        <a-select-option
                          :disabled="getAllOptDisabled(field)"
                          :value="field.options.all_value.value"
                        >
                          {{ $t('global_button_all') }}
                        </a-select-option>
                      </template>
                    </lazy-long-select>
                  </template>
                </template>

                <!-- text -->
                <template v-else-if="field.style.type === 2">
                  <a-input
                    v-if="field.options.type === 6"
                    v-model="field.value.content"
                    :disabled="!!$attrs.disabled || getFieldDisabled(field)"
                    :class="{
                      'v-disabled-exclude': disabledExclude
                    }"
                  />
                  <a-input
                    v-else
                    v-model="field.value"
                    :disabled="!!$attrs.disabled || getFieldDisabled(field)"
                    :class="{
                      'v-disabled-exclude': disabledExclude
                    }"
                  />

                  <template v-if="$attrs.reference && getReference($attrs.reference, group.group_id, field)">
                    <a-input
                      style="margin-top: 6px"
                      :value="getReference($attrs.reference, group.group_id, field)"
                      :disabled="true"
                      :class="{
                        'v-disabled-exclude': disabledExclude
                      }"
                    />
                  </template>
                </template>

                <!-- checkbox -->
                <template v-else-if="field.style.type === 14">
                  <component
                    :is="field.style.multiple_selection ? 'a-checkbox-group' : 'a-radio-group'"
                    v-model="field.value"
                  >
                    <component
                      :is="field.style.multiple_selection ? 'a-checkbox' : 'a-radio'"
                      v-for="(item, idx) in getItemOpts(field, group)"
                      :key="idx"
                      :value="item.value"
                      :disabled="$attrs.polish || !!$attrs.disabled || getFieldDisabled(field)"
                      :class="{
                        'v-disabled-exclude': disabledExclude
                      }"
                      >{{ item.label }}
                    </component>
                  </component>
                </template>

                <template v-else-if="field.style.type === 15">
                  <a-checkbox-group v-model="field.value">
                    <div
                      v-for="(item, idx) in getItemOpts(field, group)"
                      :key="idx"
                      class="checkbox-block-item"
                    >
                      <a-checkbox
                        :disabled="$attrs.polish || !!$attrs.disabled || getFieldDisabled(field)"
                        :value="item.value"
                      ></a-checkbox>
                      <p class="checkbox-block-content common-scrollbar">{{ item.label }}</p>
                    </div>
                  </a-checkbox-group>
                </template>

                <template v-else-if="field.style.type === 16">
                  <OtherInfoMultiInput
                    :value="field.value"
                    :disabled="!!$attrs.disabled || getFieldDisabled(field)"
                    :disabled-exclude="disabledExclude"
                  />
                </template>

                <template v-else-if="field.style.type === 17">
                  <OtherInfoDragInput
                    :class="{
                      '--is-empty-list': Array.isArray(field.value) && !field.value.length
                    }"
                    :field="field"
                    :disabled="!!$attrs.disabled || getFieldDisabled(field)"
                    :disabled-exclude="disabledExclude"
                    :showDeleteBtn="showInputDeleteBtn"
                    :showAddNewBtn="showInputAddNewBtn"
                    :disabledDrag="!!$attrs.disabledDrag"
                    @dragging="(val) => $emit('dragging', val)"
                    @dragEnding="(val) => $emit('dragEnding', val)"
                  />
                </template>

                <!-- selection_district -->
                <template v-else-if="field.style.type === 23 || useItineraryPickUpData">
                  <District
                    ref="district"
                    v-model="field.value"
                    :field="field"
                    :disabled="!!$attrs.disabled || getFieldDisabled(field) || useItineraryPickUpData"
                    :disabled-exclude="disabledExclude"
                    :use-itinerary-pick-up-data="useItineraryPickUpData"
                  />
                </template>

                <template v-else-if="field.style.type === 24">
                  <FixedPoint
                    ref="fixedPoint"
                    v-model="field.value"
                    :field="field"
                    :disabled="!!$attrs.disabled || getFieldDisabled(field)"
                    :disabled-exclude="disabledExclude"
                  />
                </template>
              </BasicFormPopover>
            </a-form-model-item>
          </template>
        </div>
      </div>
    </a-form-model>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getEditLang } from '@activity/utils'
import LazyLongSelect from '@activity/components/LazyLongSelect.vue'
import NumberRange from '@activity/pages/components/NumberRange.vue'
import BasicFormPopover from '@activity/pages/components/BasicFormPopover.vue'
import OtherInfoMultiInput from './components/MultiInput.vue'
import OtherInfoDragInput from './components/DragInput.vue'
import District from '@activity/pages/package/extra/components/pickUpLocation/district/index.vue'
import FixedPoint from '@activity/pages/package/extra/components/pickUpLocation/fixedPoint/index.vue'
import { groupRefFieldTagDict } from '@activity/pages/package/extra/const.js'

export default {
  name: 'OtherInfoItem',
  components: {
    LazyLongSelect,
    NumberRange,
    BasicFormPopover,
    OtherInfoDragInput,
    OtherInfoMultiInput,
    District,
    FixedPoint
  },
  inheritAttrs: false,
  inject: ['getMergeOtherInfoDrawer'],
  model: {
    value: 'form',
    event: 'change'
  },
  props: {
    group: {
      type: Object,
      required: true
    },
    fields: {
      type: Array,
      required: true
    },
    validator: {
      type: Function,
      required: true
    },
    showHeaderOperation: {
      type: Boolean,
      default: true
    },
    showAddNewBtn: {
      type: Boolean,
      default: true
    },
    showDeleteBtn: {
      type: Boolean,
      default: true
    },
    showInputDeleteBtn: {
      type: Boolean,
      default: true
    },
    showInputAddNewBtn: {
      type: Boolean,
      default: true
    },
    injectFieldKey: {
      type: Boolean,
      default: false
    },
    // itinerary 生成的 pick up location 数据为只读，且无论点还是区域，均使用区域组件兼容显示
    useItineraryPickUpData: {
      type: Boolean,
      default: false
    },
    // 目前仅 field.options.type === 5 有效, 详见 methods.getItemOpts
    filterOption: {
      type: Function,
      default: null
    }
  },
  data() {
    this.groupRefFieldTagDict = groupRefFieldTagDict
    return {
      form: {},
      groupItems: {},
      titleList: {},
      attrValueList: {}
    }
  },
  computed: {
    getPreviewSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `Preview?oid=${oid}&trg=manual`
    },
    ...mapGetters(['isTripMapped']),
    // two-way data binding for the provide data
    mergeOtherInfoDrawer: {
      get() {
        return this.getMergeOtherInfoDrawer()
      },
      set() {}
    },
    groupName() {
      if (this.$attrs.groupName) {
        return this.$attrs.groupName
      }

      const { group } = this
      const groupName = group.group_name
      const comboPkgName = _.get(group.combo_merge_info, 'from_combo_package_name', null)
      const titleCombo = _.get(group.combo_merge_info, 'title_from_combo_info', [])

      if (Array.isArray(titleCombo) && titleCombo.length) {
        return `${groupName} (from ${titleCombo.map((item) => item.package_name).join(' & ')})`
      }

      return comboPkgName ? `${groupName} (from ${comboPkgName})` : groupName
    },
    disabledMergedCheckbox() {
      const { mergeInfo, currentGroupId } = this.mergeOtherInfoDrawer

      // 已勾选的可操作
      if (!mergeInfo.length || mergeInfo.some((item) => item.uuid === this.group.uuid)) {
        return false
      }

      // 需要合并来自不同的套餐
      const currentPackageId = _.get(this.group, 'combo_merge_info.from_combo_package_id')
      const doNotContain = mergeInfo.every((item) => item.package_id !== currentPackageId)

      return !doNotContain || (!!currentGroupId && currentGroupId !== this.group.group_id)
    },
    isMerged() {
      return _.get(this.group, 'combo_merge_info.is_merged', 0) === 1
    },
    canBeSelectedToMerge() {
      if (this.$attrs.closeMergeChoose) {
        return false
      }

      const mergeType = _.get(this.group, 'combo_merge_info.merge_type', -1)

      return !this.isMerged && mergeType === 2 // -1，默认值；0, 不可 merge；1，自动 merge，不可 edit；2，手动 merge，可 edit
    },
    canBeEditMerged() {
      const mergeType = _.get(this.group, 'combo_merge_info.merge_type', -1)

      return this.isMerged && mergeType !== 1
    },
    disabledExclude() {
      return this.group.ref_field_tag === 'ticket_type' && this.isTripMapped
    },
    calcParentSelectedKeys() {
      // 主从联动用，参考下面的 showField 方法
      const parentFields = this.fields.filter((field) => field.style?.is_parent)
      let result = parentFields.reduce(
        (acc, curr) => [
          ...acc,
          ...(Array.isArray(curr?.value)
            ? curr.value
            : [['string', 'number'].includes(typeof curr?.value) ? curr.value : null])
        ],
        []
      )

      // 存在 value 为 number 的特殊情况，如 name, 这里统一为 string
      return result.filter((item) => item !== null).map(String)
    }
  },
  async beforeMount() {
    this.form = new Proxy(
      {},
      {
        get: (target, prop, receiver) => {
          let current = _.find(this.fields, {
            key: prop
          })

          return (current && current.value) || Reflect.get(target, prop)
        }
      }
    )

    this.$on('getGroupItems', async function (group_id) {
      let options = await this.$store.dispatch('actionGetGroupItemById', {
        group_id
      })
      this.$set(this.groupItems, group_id, options)
    })

    this.$on('getTitleList', async function (group_id) {
      let options = await this.$store.dispatch('actionGetTitleListById', {
        group_id
      })
      this.$set(this.titleList, group_id, options)
    })

    this.$on('getAttrValueList', this.getAttrValueList)
  },
  mounted() {
    this.$emit('addItem', this)
  },
  beforeDestroy() {
    this.$emit('deleteItem', this)
    this.$off('getGroupItems').$off('getTitleList').$off('getAttrValueList')
  },
  methods: {
    getBasicFormPopoverAttrsByField(field) {
      return this.injectFieldKey ? { 'data-field-key': field.key } : {}
    },
    isTreeSelect(field) {
      const all_value = field?.options?.all_value?.type ?? undefined
      const multiple = field?.style?.multiple_selection ?? 0
      return !!all_value && !!multiple
    },
    getAllValue(field) {
      return field?.options?.all_value?.value ?? ''
    },
    handleViewMerged(data) {
      this.$emit('toViewMerged', data)
    },
    changeChooseMerge({ event, group }) {
      const { checked } = event.target
      const { mergeOtherInfoDrawer } = this

      if (checked) {
        if (!mergeOtherInfoDrawer.currentGroupId) {
          mergeOtherInfoDrawer.currentGroupId = group.group_id
        }
        mergeOtherInfoDrawer.mergeInfo.push({
          uuid: group.uuid,
          package_id: group.combo_merge_info.from_combo_package_id,
          package_name: group.combo_merge_info.from_combo_package_name,
          title_id: group.title_id
        })
      } else {
        mergeOtherInfoDrawer.mergeInfo = mergeOtherInfoDrawer.mergeInfo.filter(
          (item) => item.uuid !== group.uuid
        )

        if (!mergeOtherInfoDrawer.mergeInfo.length) {
          mergeOtherInfoDrawer.currentGroupId = undefined
        }
      }
    },
    async handlePreview() {
      let validate = await this.validateForm()
      if (!validate) {
        return
      }
      let form = this.getPreviewForm()
      this.$emit('preview', form)
    },
    getPreviewForm() {
      let { fields } = this
      let currentFields = []
      let parentField = {}

      fields.forEach((field) => {
        if (field.style && field.style.is_parent) {
          parentField = field
        }
        if (this.showField(field)) {
          currentFields.push(field)
        }
      })

      let form = {
        info: this.getFormInfo(currentFields),
        mode: 'default'
      }

      switch (parentField.value) {
        case 'text_box':
          form.type_flag = 1
          break
        case 'date':
          form.type_flag = 2
          form.mode = 'date'
          break
        case 'select':
          form.type_flag = 3
          break
        case 'multi_select':
          form.type_flag = 3
          form.mode = 'multiple'
          break
        case 'hh_mm_time':
          form.type_flag = 4
          break
      }
      return form
    },
    getFormInfo(fields = []) {
      let fieldData = {}
      let contentEn = []
      let contentCn = []
      let group = this.group

      fields.forEach((item) => {
        fieldData[item.key] = item.value
      })

      let {
        customized_title_name = {},
        customized_otherinfo_hint = {},
        customized_attr_value = []
      } = fieldData
      // Select 类型 options 选项转换成数组，使用时用','拼接
      customized_attr_value.forEach((attr) => {
        contentCn.push(attr.content)
        contentEn.push(attr.content_en)
      })

      let cn = {
        language: 'zh_CN',
        hint: customized_otherinfo_hint.content,
        name: customized_title_name.content || group.group_name,
        content: contentCn
      }
      let en = {
        language: 'en_US',
        hint: customized_otherinfo_hint.content_en,
        name: customized_title_name.content_en || group.group_name,
        content: contentEn
      }
      let { lang } = this.$route.query
      // 英文状态下只展示英文预览，其他语言展示英文和当前语言预览。
      if (lang === 'en_US') {
        return [en]
      }
      return [cn, en]
    },
    showField(field) {
      if (this.$attrs.showField) {
        return true
      }

      const parentKeys = field.style.parent_keys || []

      return (
        parentKeys.length === 0 || parentKeys.some((key) => this.calcParentSelectedKeys.includes(String(key)))
      )
    },
    getRequired(field) {
      if (_.get(field, 'options.rel_key', null)) {
        let linkageField = this.fields.find((item) => item.key === field.options.rel_key)
        if (linkageField && Array.isArray(linkageField.value) && linkageField.value.length) {
          return false
        }
      }

      return field.style.required && !this.getAllOptDisabled(field)
    },
    getAllOptDisabled(field) {
      if (_.get(field, 'options.rel_key', null)) {
        let allValue = field.options.all_value.value
        let linkageField = this.fields.find((item) => item.key === field.options.rel_key)
        if (linkageField) {
          let ids = linkageField.value || []
          if (ids.includes(allValue)) {
            return true
          }
        }
      }

      return false
    },
    getFieldDisabled(field) {
      return !!_.get(field, 'style.disabled', false)
    },
    getAttrValueList: _.debounce(async function (attr_item_id_list) {
      if (this.attrValueList[attr_item_id_list]) {
        return this.attrValueList[attr_item_id_list]
      }
      let options = (
        await ajax.get(ADMIN_API.act.get_attr_values_by_attr_item_idlist, {
          params: {
            attr_item_id_list,
            language: getEditLang()
          }
        })
      ).map((item) => ({
        value: item.attr_value_id,
        label: item.ref_field_tag + ' - ' + item.attr_value_name
      }))
      this.$set(this, 'attrValueList', {
        ...this.attrValueList,
        [attr_item_id_list]: options
      })
    }, 300),
    getReference(reference, group_id, langField) {
      let group = _.find(reference.groups, {
        group_id
      })
      if (!group) {
        return ''
      }
      let field = _.find(group.style.fields || [], {
        key: langField.key
      })

      return field && field.options.type === 6 ? field.value.content : field.value || ''
    },
    validateForm() {
      return new Promise((resolve) => this.$refs.form.validate((valid) => resolve(valid)))
    },
    handleChange(val, field) {
      let rel_key = field.options.rel_key
      let allValue = field.options.all_value.value
      if (Array.isArray(val)) {
        // multi-select
        if (val.length > 1) {
          if (val[0] === allValue) {
            // Should be delete allValue.
            field.value.splice(0, 1)
          } else if (val[val.length - 1] === allValue) {
            // The newly selected option value is allValue, should clear other values
            field.value = [allValue]
          }
        }
        if (val.length === 0 && !rel_key) {
          // 使用 TreeSelect 组件的时候可以重置为空
          if (this.isTreeSelect(field)) {
            field.value = []
          } else if (field.options.all_value.type && !this.getAllOptDisabled(field)) {
            field.value = [allValue]
          }
        }
      }

      if (rel_key) {
        if (_.isEqual(field.value, [allValue])) {
          this.fields.forEach((item) => {
            if (item.key === rel_key) {
              item.value = []
            }
          })
        }
      }
    },
    getItemOpts(field, group) {
      let { options } = field

      switch (options.type) {
        case 1:
          return this.$attrs.languages
        case 2:
          return this.$attrs.countries
        case 3: {
          let opts = _.get(this.groupItems, group.group_id, null)
          if (!opts) {
            this.$set(this, 'groupItems', {
              ...this.groupItems,
              [group.group_id]: []
            })
            this.$emit('getGroupItems', group.group_id)
          }
          return this.groupItems[group.group_id]
        }
        case 4: {
          let opts = _.get(this.titleList, group.group_id, null)
          if (!opts) {
            this.$set(this, 'titleList', {
              ...this.titleList,
              [group.group_id]: []
            })
            this.$emit('getTitleList', group.group_id)
          }
          return this.titleList[group.group_id]
        }
        case 5: {
          const opts = (options.items || []).map((item) => ({
            write_level: item.write_level,
            value: item.key,
            label: item.name
          }))
          return this.filterOption?.(opts, field) || opts
        }
        case 8: {
          this.$emit('getAttrValueList', options.from_attr_item_id)

          return this.attrValueList[options.from_attr_item_id] || []
        }
        case 9: {
          const countries = this.$attrs.countries
          if (field.options.rel_key) {
            let linkageField = this.fields.find((item) => item.key === field.options.rel_key)
            if (linkageField) {
              let ids = linkageField.value || []
              return countries.map((country) => ({
                ...country,
                disabled: ids.includes(country.value)
              }))
            }
          }
          return countries
        }
      }
    }
  }
}
</script>

<style lang="scss">
.other-info-item__form {
  .ant-form-item-label > label {
    padding-right: 8px;
    white-space: break-spaces;
    display: inline-block;
    line-height: normal;

    &:after {
      display: none;
    }
  }
}
.form-item-no-label .ant-form-item-control-wrapper {
  width: 100%;
  .ant-checkbox-group {
    width: 100%;
  }
}
</style>

<style lang="scss" scoped>
.other-info-item__form {
  position: relative;
  // width: 90%;
  min-width: 500px;
  // max-width: 1080px;
  padding: 30px 40px;
  margin: 10px auto;
  background: #ffffff;
  border: 1px solid #d3dce6;
  box-sizing: border-box;
  border-radius: 4px;
  // .--disabled {
  //   /deep/ i.el-tag__close.el-icon-close {
  //     display: none;
  //   }
  //   /deep/ .el-tag {
  //     color: #c0c0c0;
  //     background-color: #eff2f7;
  //     border-color: #d9e0e8;
  //   }
  // }

  &-menu {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translate(0, -50%);
  }
  .other-info__groupName {
    /*position: absolute;*/
    /*top: 22px;*/
    /*left: 30px;*/
    display: flex;
    font-size: 14px;
    color: #1989fa;
    .--tips {
      display: block;
      font-size: 12px;
      font-weight: normal;
      color: #212121;
      opacity: 0.65;
    }
  }
  .other-info-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    .preview-btn {
      margin-right: 12px;
    }

    .__right {
      display: inline-flex;
      flex-wrap: nowrap;
      margin-left: 12px;
    }
  }
  .btn-groupAdd {
    /* 抵消 antd btn padding, 对齐右端 */
    margin-right: -20px;
  }

  .other-info--alert {
    margin-top: 6px;
    font-weight: 500;
  }

  .other-info--formItemContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    .--formItem {
      position: relative;
      margin-top: 42px;
      width: 100%;
      display: flex;
      flex-direction: column;
      &.stripe {
        padding: 12px 20px;
        background: #fafafa;
      }
      .group--del-container {
        padding: 10px 20px;
        text-align: right;
        .--del {
          z-index: 999;
          color: #ff4949;
        }
      }
    }
  }
  .lazy-long-select {
    width: 100%;
  }
  &-icon {
    position: absolute;
    right: -22px;
    top: 50%;
    margin-top: -8px;
    font-size: 16px;
  }
  .checkbox-block-item {
    display: flex;
    padding: 16px;
    margin-bottom: 20px;
    background-color: #fafafa;
    .checkbox-block-content {
      max-height: 100px;
      overflow: auto;
    }
  }

  .form-item-vertical {
    display: flex;
    flex-direction: column;

    ::v-deep {
      .ant-form-item-label {
        white-space: nowrap;
        width: fit-content;
      }

      .ant-form-item-control-wrapper {
        width: 100%;
      }
    }
  }

  ::v-deep {
    .ant-radio-group {
      position: relative;
      top: -5px;
      width: 100%;

      .ant-radio-wrapper {
        display: inline-flex;
        align-items: flex-start;
        margin-bottom: 8px;
        line-height: 22px;
        white-space: break-spaces;

        &:last-child {
          margin-bottom: 0px;
        }

        .ant-radio {
          margin-top: 3px;
        }
      }
    }
  }
}
</style>
