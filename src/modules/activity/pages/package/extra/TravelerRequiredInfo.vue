<template>
  <div class="traveler-required-info-container">
    <a-tooltip
      :class="$root.isMerchant && 'spec-style'"
      :title="$root.isMerchant ? $t('88706') : ''"
      placement="right"
    >
      <a-radio-group ref="radioGroup" v-model="travelerInfoType" @change="changeTravelerType(true)">
        <a-radio
          v-for="item in NEED_TRAVELER_INFO_CONF"
          :key="item.value"
          :disabled="disabledRadio"
          :style="{
            height: '26px',
            lineHeight: '26px'
          }"
          :value="item.value"
        >
          {{ item.label }}
        </a-radio>
      </a-radio-group>
    </a-tooltip>
    <div v-if="showNonRepeatableSwitch" class="non-repeatable-switch" style="margin-top: 16px">
      <a-switch v-model="isNonRepeatable" :disabled="disabledGroup" @change="handleNonRepeatableChange" />
      <span style="margin-left: 8px">校验出行人唯一性</span>
    </div>

    <div
      v-show="travelerInfoType !== -1"
      class="content-other-info triangle"
      :style="{
        '--offsetX': triangleOffsetX + 'px'
      }"
    >
      <p style="margin-bottom: 10px">
        {{ $t('other_info_less_likely_warn') }}
      </p>

      <other-info-group
        ref="body"
        :original-groups="travellerGroups"
        :group-checked-list.sync="currentGroupCheckedList"
        :disabled="disabledGroup"
        :filter-option="filterOption"
        @changeData="$emit('changeData')"
        @updateData="handelUpdateData"
      />
    </div>
  </div>
</template>

<script>
import OtherInfoGroup from './OtherInfoGroup'
import { Modal } from 'ant-design-vue'

// 字段标识常量
const NAME_REF_FIELD_TAG = 'name_root'
const CERTIFICATE_REF_FIELD_TAG = 'identification_type'

export default {
  name: 'TravelerRequiredInfo',
  components: {
    OtherInfoGroup
  },
  props: {
    // shared all check data
    groupCheckedList: {
      type: Array,
      required: true
    },
    // filtered data through info_type = 1
    travellerGroups: {
      type: Array,
      required: true
    },
    disabledRadio: {
      type: Boolean,
      default: false
    },
    disabledGroup: {
      type: Boolean,
      default: false
    },
    initialNonRepeatable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    this.isInited = false

    return {
      travelerInfoType: -1,
      triangleOffsetX: 160,
      isNonRepeatable: false
    }
  },
  watch: {
    travelerInfoType: {
      handler(v) {
        const data = v === -1 ? [] : this.$refs.body.getPostData()
        this.handelUpdateData(data)
      }
    },
    groupCheckedList: {
      immediate: true,
      deep: true,
      handler() {
        if (!this.isInited) {
          this.isInited = true
          this.initData()
        }
      }
    },
    // 监听初始值变化，同步到本地状态
    initialNonRepeatable: {
      immediate: true,
      handler(newVal) {
        this.isNonRepeatable = newVal
      }
    },
    // 监听开关显示状态，当开关隐藏时重置状态
    showNonRepeatableSwitch: {
      handler(newVal, oldVal) {
        if (oldVal === true && newVal === false && this.isNonRepeatable) {
          this.isNonRepeatable = false
          this.$emit('nonRepeatableSwitchChange', false)
        }
      }
    }
  },
  computed: {
    categoryId() {
      return _.get(this.$store, 'state.categoryInfo.sub_category_id', null)
    },
    currentGroupCheckedList: {
      get() {
        return this.groupCheckedList
      },
      set(v) {
        this.$emit('changeGroupCheckedList', v)
      }
    },
    NEED_TRAVELER_INFO_CONF() {
      return [
        {
          label: this.$t('other_info_no_required'),
          value: -1
        },
        {
          label: this.$t('other_info_1_participant'),
          value: 0
        },
        {
          label: this.$t('other_info_all_participant'),
          value: 1
        }
      ]
    },
    showNonRepeatableSwitch() {
      // 检查是否选择了"info from all participants required" (value: 1)
      if (this.travelerInfoType !== 1) {
        return false
      }

      // 检查sub_category_id是否不等于2
      if (this.categoryId === 2) {
        return false
      }

      // 检查是否包含name或certificate字段
      return this.hasNameOrCertificateFields()
    },
    // 获取当前选中的groups
    selectedGroups() {
      if (!this.travellerGroups || !this.groupCheckedList) {
        return []
      }
      return this.travellerGroups.filter((group) => this.groupCheckedList.includes(group.group_id))
    }
  },
  mounted() {
    this.changeTravelerType()
  },
  methods: {
    handelUpdateData(data) {
      this.$emit(
        'updateData',
        data.map((item) => ({
          ...item,
          write_level: this.travelerInfoType // 0-booking级别, 1-unit级别,
        }))
      )
    },
    async validateForm() {
      return -1 !== this.travelerInfoType ? this.$refs.body.validateForm() : true
    },
    initData() {
      const { travellerGroups } = this

      for (let group of travellerGroups) {
        if (this.groupCheckedList.includes(group.group_id)) {
          this.travelerInfoType = _.get(group, 'write_level', -1)
          this.changeTravelerType()
          break
        }
      }
    },
    changeTravelerType(dispatch = false) {
      this.$nextTick(() => {
        const setting = this.$refs.radioGroup.$el
        if (setting) {
          const isChecked = setting.querySelector('.ant-radio-checked')
          this.triangleOffsetX = isChecked.getBoundingClientRect().x - setting.getBoundingClientRect().x
        }
      })

      dispatch && this.$emit('changeData')
    },
    filterOption(options = []) {
      return options.filter((option) => {
        // supply api mapping option 会返回 write_level 字段, 0: booking; 1: unit;
        if (option.write_level !== undefined) {
          return option.write_level === this.travelerInfoType
        }

        return option
      })
    },

    hasNameOrCertificateFields() {
      if (!this.selectedGroups || this.selectedGroups.length === 0) {
        return false
      }

      // 检查是否有name_root或identification_type字段
      return this.selectedGroups.some(
        (group) =>
          group.ref_field_tag === NAME_REF_FIELD_TAG || group.ref_field_tag === CERTIFICATE_REF_FIELD_TAG
      )
    },

    handleNonRepeatableChange(checked) {
      if (checked) {
        Modal.confirm({
          title: 'Confirmation',
          content: 'The participants info need to be non-repeatable',
          okText: this.$t('global_confirm'),
          cancelText: this.$t('global_cancel'),
          onOk: () => {
            // 用户确认，向父组件传递开关状态变化
            this.$emit('nonRepeatableSwitchChange', this.isNonRepeatable)
          },
          onCancel: () => {
            // 如果用户取消，恢复开关状态
            this.isNonRepeatable = false
          }
        })
      } else {
        // 关闭开关时直接传递状态变化
        this.$emit('nonRepeatableSwitchChange', this.isNonRepeatable)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.traveler-required-info-container {
  margin-bottom: 12px;
  .spec-style {
    .ant-radio-group {
      display: inline;
    }
  }
}
</style>
