<template>
  <a-collapse v-model="currentActiveKey" class="collapse-container">
    <a-collapse-panel
      v-for="item of list"
      :key="String(item[fieldKey])"
      :class="{
        'package-has-chosen': chosenPackageIds.includes(item.package_id)
      }"
    >
      <template #header>
        <slot name="header" :item="item"></slot>
      </template>

      <slot name="body" :item="item"></slot>
    </a-collapse-panel>
  </a-collapse>
</template>

<script>
export default {
  name: 'MergeCollapse',
  props: {
    list: {
      type: Array,
      required: true
    },
    fieldKey: {
      type: String,
      default: 'id'
    },
    activeKey: {
      type: Array,
      default: () => []
    },
    chosenPackageIds: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    currentActiveKey: {
      get() {
        return this.activeKey
      },
      set(v) {
        this.$emit('update:activeKey', v)
      }
    }
  },
  async mounted() {
    await this.$nextTick()

    this.$set(
      this,
      'currentActiveKey',
      this.list.map((item) => String(item[this.fieldKey]))
    )
  }
}
</script>

<style lang="scss" scoped>
@mixin borderNone {
  border: none;
}

$hasKeyBg: #f0f7ff;
$transitionSecond: 0.3s;

.collapse-container {
  @include borderNone;

  .ant-collapse-item {
    @include borderNone;
    background-color: #fff;
  }

  ::v-deep {
    .ant-collapse-content {
      @include borderNone;
    }

    .ant-collapse-header {
      padding: 4px 16px 4px 42px !important;
    }

    .ant-collapse-arrow {
      top: 10px !important;
      left: 14px !important;
    }

    .ant-collapse-content-box {
      padding: 0 !important;
    }
  }

  .package-has-chosen ::v-deep .ant-collapse-header {
    transition: background-color $transitionSecond;
    background-color: $hasKeyBg;
  }
}
</style>
