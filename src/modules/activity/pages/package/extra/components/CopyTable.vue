<template>
  <div>
    <a-table
      :columns="copyColumns"
      :data-source="dataSource"
      :row-selection="rowSelection"
      :pagination="false"
      size="middle"
    />
  </div>
</template>
<script>
const renderContent = (value, row, isName = false) => {
  const obj = {
    children: value,
    attrs: {}
  }
  if (row.isParent) {
    obj.attrs.style = 'font-weight: bold'

    if (isName) {
      obj.attrs.colSpan = 4
    } else {
      obj.attrs.colSpan = 0
    }
  }
  return obj
}
export default {
  props: {
    dataSource: {
      type: Array,
      default: () => []
    },
    selectedRowKeys: {
      type: Array,
      default: () => []
    },
    allSelectedKeys: {
      type: Object,
      default: () => {}
    },
    dataKey: {
      type: [Number, String],
      default: 0
    },
    currentPackageId: {
      type: [Number, String],
      default: ''
    },
    singleSelect: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      copyColumns: [
        {
          title: this.$t('package_xinfo_type'),
          dataIndex: 'type',
          key: 'type',
          className: 'copy-column',
          customRender(value, row) {
            return renderContent(value, row)
          }
        },
        {
          title: this.$t('package_xinfo_name'),
          dataIndex: 'name',
          key: 'name',
          className: 'copy-column',
          customRender(value, row) {
            return renderContent(value, row, true)
          }
        },
        {
          title: this.$t('package_xinfo_repeat_rules'),
          dataIndex: 'rules',
          key: 'rules',
          className: 'copy-column',
          customRender(value, row) {
            return renderContent(value, row)
          }
        },
        {
          title: this.$t('package_xinfo_mandatory'),
          dataIndex: 'mandatory',
          key: 'mandatory',
          className: 'copy-column',
          customRender(value, row) {
            return renderContent(value, row)
          }
        }
      ]
    }
  },
  computed: {
    rowSelection() {
      const { selectedRowKeys, allSelectedKeys, dataKey, singleSelect, currentPackageId } = this
      return {
        selectedRowKeys: selectedRowKeys,
        onChange: this.rowSelectionChange,
        getCheckboxProps(record) {
          const otherSelectKeys = Object.keys(allSelectedKeys).reduce((acc, curr) => {
            const keys = curr === dataKey ? [] : allSelectedKeys[curr]
            return [...acc, ...keys]
          }, [])
          const disabled =
            singleSelect && currentPackageId
              ? currentPackageId != dataKey
              : otherSelectKeys.includes(record.key)
          const style = {}
          if (record.isChild) {
            style.display = 'none'
          }
          return {
            style,
            props: {
              disabled: disabled
            }
          }
        }
      }
    }
  },
  methods: {
    rowSelectionChange(selectedRowKeys) {
      this.$emit('change', {
        id: this.dataKey,
        keys: selectedRowKeys
      })
    }
  }
}
</script>

<style scoped lang="scss"></style>
