<template>
  <div class="other-info-drag-input">
    <DraggableList
      v-model="field.value"
      class="drag-input-list"
      :disabled="!!$attrs.disabled || disabledDrag"
      :handle="true"
      item-key="uuid"
      @start="$emit('dragging', true)"
      @end="handleDragEnd"
    >
      <template v-slot:default="slotProps">
        <div class="drag-input-item">
          <component
            :is="slotProps.item.from_value ? 'a-tooltip' : 'span'"
            placement="right"
            overlayClassName="common-tooltip-style"
          >
            <template slot="title">
              <span style="font-weight: 600;">
                {{ field.name }}
              </span>
              <hr style="margin: 6px;" />
              <p v-for="v in slotProps.item.from_value" :key="v.package_id" style="margin-bottom: 6px;">
                「{{ v.package_id }} - {{ v.package_name }}」 - {{ v.content }}
              </p>
            </template>

            <OtherInfoMultiInput
              :value="slotProps.item"
              :disabled="disabled"
              :disabledExclude="disabledExclude"
              @change="handleChange(slotProps.item)"
            />
          </component>

          <a-popconfirm
            title="Are you sure delete this?"
            ok-text="Yes"
            cancel-text="No"
            @confirm="handleDelete(slotProps.item)"
          >
            <a-button
              v-if="showDeleteBtn && !disabled"
              ghost
              type="link"
              size="small"
              style="padding: 0;"
              class="drag-input-item-delete"
            >
              <svg-icon icon-name="delete-trash" style="font-size: 20px;" />
            </a-button>
          </a-popconfirm>

          <svg-icon v-if="!disabledDrag && !disabled" icon-name="menu" class="drag-input-item-drag handle" />
        </div>
      </template>
    </DraggableList>
    <div class="drag-input-operation">
      <a-button
        v-if="showAddNewBtn"
        type="link"
        icon="plus"
        :disabled="disabled"
        style="padding: 0;"
        @click="handleAdd(field)"
      >
        {{ $t('global_add') }}
      </a-button>
    </div>
  </div>
</template>

<script>
import DraggableList from '@activity/components/DraggableList.vue'
// import CircleDeleteButton from '@activity/components/CircleDeleteButton'
import OtherInfoMultiInput from './MultiInput.vue'

let id = 1

export default {
  name: 'OtherInfoDragInput',
  components: {
    DraggableList,
    // CircleDeleteButton,
    OtherInfoMultiInput
  },
  props: {
    field: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    disabledExclude: {
      type: Boolean,
      default: false
    },
    disabledDrag: {
      type: Boolean,
      default: false
    },
    showDeleteBtn: {
      type: Boolean,
      default: true
    },
    showAddNewBtn: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      defaultItem: {
        id: 0,
        content: '',
        content_en: ''
      }
    }
  },
  watch: {
    field: {
      immediate: true,
      handler(val) {
        if (val) {
          ;(val.value || []).forEach((item) => {
            item.uuid = id++
          })
        }
      }
    }
  },
  methods: {
    handleAdd(field) {
      const newItem = _.cloneDeep(this.defaultItem)
      newItem.uuid = id++
      field.value.push(newItem)
    },
    handleDelete(item) {
      this.field.value.splice(this.field.value.indexOf(item), 1)
    },
    handleChange(item) {
      // 后端说去掉这个逻辑
      // item.id = 0
    },
    handleDragEnd() {
      this.$emit('dragging', false)
      this.$emit('dragEnding', this.field.value)
    }
  }
}
</script>

<style scoped lang="scss">
.other-info-drag-input {
  margin-right: -50px;

  .drag-input-operation {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .drag-input-item {
    position: relative;
    padding-right: 50px;

    &-delete {
      position: absolute;
      right: 25px;
      top: 8px;
    }

    &-drag {
      position: absolute;
      right: 7px;
      top: 12px;
    }
  }
}
</style>
