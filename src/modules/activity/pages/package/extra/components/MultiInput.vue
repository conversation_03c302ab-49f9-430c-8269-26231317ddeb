<template>
  <div class="other-info-multi-input">
    <a-input
      v-if="showOtherLang"
      v-model="value.content"
      :disabled="disabled"
      :placeholder="getPlaceholder(getEditLang())"
      :class="{
        'v-disabled-exclude': disabledExclude
      }"
      @change="handleChange"
    />
    <a-input
      v-model="value.content_en"
      :disabled="disabled"
      :placeholder="getPlaceholder('en_US')"
      :class="{
        'v-disabled-exclude': disabledExclude
      }"
      @change="handleChange"
    />
  </div>
</template>

<script>
import { getEditLang } from '@activity/utils'

export default {
  name: 'OtherInfoMultiInput',
  props: {
    value: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    disabledExclude: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    showOtherLang() {
      return getEditLang() !== 'en_US'
    }
  },
  methods: {
    getEditLang,
    handleChange() {
      this.$emit('change')
    },
    getPlaceholder(lang) {
      const langI18n = this.$t(`act_lan_${lang}`)
      return `${this.$t('global_please_input')}. (${langI18n})`
    }
  }
}
</script>

<style scoped lang="scss"></style>
