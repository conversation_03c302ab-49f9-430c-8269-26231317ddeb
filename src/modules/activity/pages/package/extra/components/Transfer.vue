<template>
  <div class="transfer-container" :class="{ 'is-lock': lock }">
    <SimpleTransferItem class="transfer-item">
      <template #header>
        <span
          :style="{
            cursor: sourceChosenRes.length === sourceData.length ? 'default' : 'pointer'
          }"
          @click="handleGoToNotFilled"
        >
          {{ sourceChosenResItemsText }}
        </span>
        <span>{{ $t('73067') }}</span>
      </template>

      <template #body>
        <OverflowTextTooltip
          class="field-title"
          :class="{
            'has-chosen-key': sourceChosenRes.length
          }"
          overlayClassName="common-tooltip-style"
          @click.native="(event) => handleGoToChosenOption(event, fieldKey)"
        >
          {{ fieldName }}
        </OverflowTextTooltip>

        <mergeCollapse
          class="package-content"
          :list="sourceData"
          :activeKey.sync="activeKey"
          fieldKey="package_id"
          :chosen-package-ids="sourceChosenRes.map((item) => item.package_id)"
        >
          <OverflowTextTooltip
            slot="header"
            slot-scope="scoped"
            class="package-title text-overflow-header"
            :data-package-id="scoped.item.package_id"
            overlayClassName="common-tooltip-style"
          >
            {{ scoped.item.package_name }}
          </OverflowTextTooltip>

          <div slot="body" slot-scope="scoped" class="option-list">
            <template v-if="getOptionsByTargetFilter(scoped.item.value).length">
              <a-checkbox
                v-for="opt in getOptionsByTargetFilter(scoped.item.value)"
                :key="opt.id"
                class="option-item"
                :class="{
                  'ant-checkbox-disabled': getPkgCheckState(scoped.item) && !getOptCheckState(opt),
                  'has-chosen-key': getOptCheckState(opt)
                }"
                :checked="getOptCheckState(opt)"
                :data-package-id="scoped.item.package_id"
                @click="(evt) => handleCheckSource(evt, { opt, pkg: scoped.item })"
              >
                <OverflowTextTooltip class="text-overflow-content" overlayClassName="common-tooltip-style">
                  {{ isContentEn ? opt.content_en : opt.content }}
                </OverflowTextTooltip>
              </a-checkbox>
            </template>

            <a-empty v-else :image="simpleImage" style="margin: 4px;" />
          </div>
        </mergeCollapse>
      </template>
    </SimpleTransferItem>

    <div class="transfer-action">
      <div
        class="action-item is-merge"
        :class="{
          'is-activate': sourceChosenRes.length === sourceData.length
        }"
      >
        <span class="item-text">
          {{ $t('73063') }}
        </span>
        <span class="item-btn" @click="handleMerge"><a-icon type="right"/></span>
      </div>

      <div
        class="action-item is-split"
        :class="{
          'is-activate': targetChosenRes.length
        }"
      >
        <span class="item-btn" @click="handleSplit"><a-icon type="left"/></span>
        <span class="item-text">{{ $t('73064') }}</span>
      </div>
    </div>

    <SimpleTransferItem class="transfer-item">
      <template #header>
        <span>
          {{ targetChosenItemsText }}
        </span>
        <span>{{ $t('73068') }}</span>
      </template>

      <template #body>
        <template v-if="currentTargetData.length">
          <OverflowTextTooltip
            class="field-title"
            :class="{
              'has-chosen-key': targetChosenRes.length
            }"
            style="cursor: default;"
            overlayClassName="common-tooltip-style"
          >
            {{ fieldName }}
          </OverflowTextTooltip>

          <div class="target-content">
            <a-checkbox
              v-for="opt in currentTargetData"
              :key="opt.unique"
              class="target-opt"
              :checked="getTargetCheckState(opt)"
              :class="{
                'has-chosen-key': getTargetCheckState(opt)
              }"
              @click="(evt) => handleCheckTarget(evt, { opt })"
            >
              <a-tooltip placement="topLeft" overlayClassName="common-tooltip-style">
                <template slot="title">
                  <span style="font-weight: 600;">
                    {{ fieldName }}
                  </span>
                  <hr style="margin: 6px;" />
                  <p v-for="v in opt.from_value" :key="v.package_id" style="margin-bottom: 6px;">
                    「{{ v.package_id }} - {{ v.package_name }}」 -
                    {{ isContentEn ? v.content_en : v.content }} <br /><br />
                  </p>
                </template>

                <OverflowTextTooltip
                  class="text-overflow-content"
                  placement="bottomLeft"
                  overlayClassName="common-tooltip-style"
                >
                  {{ isContentEn ? opt.content_en : opt.content }}
                </OverflowTextTooltip>
              </a-tooltip>
            </a-checkbox>
          </div>
        </template>

        <a-empty v-else style="margin-top: 42px;" />
      </template>
    </SimpleTransferItem>
  </div>
</template>

<script>
import SimpleTransferItem from './TransferItem'
import MergeCollapse from './Collapse'
import OverflowTextTooltip from '../../../../components/OverflowTextTooltip'
import { Empty } from 'ant-design-vue'
import { getEditLang } from '@activity/utils'

export default {
  name: 'SimpleTransfer',
  components: {
    OverflowTextTooltip,
    MergeCollapse,
    SimpleTransferItem
  },
  props: {
    lock: {
      type: Boolean,
      default: false
    },
    fieldKey: {
      type: String,
      required: true
    },
    fieldName: {
      type: String,
      required: true
    },
    sourceData: {
      type: Array,
      required: true
    },
    targetData: {
      type: Array,
      required: true
    }
  },
  data() {
    this.currentGoToSourceChosenIndex = 0
    this.currentGoToSourcePackageIndex = 0
    this.UUID_SEPARATOR = '-'

    return {
      simpleImage: Empty.PRESENTED_IMAGE_SIMPLE,
      isEmpty: true,

      activeKey: [],
      sourceChosenRes: [],

      targetChosenRes: []
    }
  },
  computed: {
    isContentEn() {
      return getEditLang() === 'en_US'
    },
    currentTargetData: {
      get() {
        return this.targetData
      },
      set(v) {
        this.$emit('update:targetData', v)
        this.$emit('updateTarget', v)
      }
    },
    currentTargetDataOptIds() {
      return this.currentTargetData.reduce((acc, curr) => {
        return [...acc, ...(curr.from_value || []).map((item) => item.id)]
      }, [])
    },
    sourceChosenResItemsText() {
      const data = this.sourceChosenRes.length
        ? {
            x: this.sourceChosenRes.length,
            y: this.sourceData.length
          }
        : {
            x: 0,
            y: 0
          }

      return klook.parseStr1(this.$t('73065'), data)
    },
    targetChosenItemsText() {
      // `${this.targetChosenRes.length}/${this.currentTargetData.length} items`
      return klook.parseStr1(this.$t('73066'), this.currentTargetData.length)
    }
  },
  async mounted() {
    await this.$nextTick()
    this.initData()
  },
  methods: {
    getOptionsByTargetFilter(options) {
      return options.filter((opt) => !this.currentTargetDataOptIds.includes(opt.id))
    },
    initData() {
      const currentTargetData = (this.currentTargetData || []).map((item) => {
        Object.assign(item, {
          unique: String((item.from_value || []).map((curr) => curr.id).join(this.UUID_SEPARATOR))
        })

        return item
      })

      this.$set(this, 'currentTargetData', currentTargetData)
      this.cachecurrentTargetData = _.cloneDeep(currentTargetData)
    },
    async handleGoToNotFilled() {
      const { sourceChosenRes } = this
      if (sourceChosenRes.length === this.sourceData.length) {
        return
      }

      const sourceChosenPackageIds = sourceChosenRes.map((item) => String(item.package_id))
      const selectorClassName = '.transfer-container .package-title'
      let emptyPackageIds = []
      const emptyPackageDomList = Array.from(document.querySelectorAll(selectorClassName)).filter((ele) => {
        const packageId = ele.dataset.packageId

        if (sourceChosenPackageIds.includes(packageId)) {
          return false
        } else {
          emptyPackageIds.push(packageId)
          return true
        }
      })

      // unfold the collapse panel of empty package
      if (emptyPackageIds.length) {
        this.$set(this, 'activeKey', [...new Set([...this.activeKey, ...emptyPackageIds])])
        await this.$nextTick()
      }

      if (this.currentGoToSourcePackageIndex > emptyPackageDomList.length - 1) {
        this.currentGoToSourcePackageIndex = 0
      }

      let currentEle = emptyPackageDomList[this.currentGoToSourcePackageIndex]

      if (currentEle) {
        const optDom = currentEle?.parentNode?.parentNode?.querySelector?.('.option-item')

        if (optDom) {
          optDom.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })
        } else {
          currentEle.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })
        }

        this.currentGoToSourcePackageIndex++
      }
    },
    async handleGoToChosenOption(event) {
      let chosenPackageId = this.sourceChosenRes.map((item) => String(item.package_id))
      // unfold the collapse panel of empty package
      this.$set(this, 'activeKey', [...new Set([...this.activeKey, ...chosenPackageId])])
      await this.$nextTick()

      const chosenOpts = event.target.nextSibling.querySelectorAll(
        '.option-item.ant-checkbox-wrapper-checked'
      )

      if (this.currentGoToSourceChosenIndex > chosenOpts.length - 1) {
        this.currentGoToSourceChosenIndex = 0
      }

      const currentEle = chosenOpts[this.currentGoToSourceChosenIndex]

      if (currentEle) {
        currentEle.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })

        this.currentGoToSourceChosenIndex++
      }
    },
    getPkgCheckState(pkg) {
      return this.sourceChosenRes.some((item) => pkg.package_id === item.package_id)
    },
    getOptCheckState(opt) {
      return this.sourceChosenRes.some((item) => opt.id === item.id)
    },
    getTargetCheckState(opt) {
      return this.targetChosenRes.some((item) => opt.unique === item.unique)
    },
    handleMerge() {
      if (this.sourceChosenRes.length !== this.sourceData.length) {
        return
      }
      const { sourceChosenRes } = this

      const unique = String(sourceChosenRes.map((curr) => curr.id).join(this.UUID_SEPARATOR))
      const cache = _.find(this.cachecurrentTargetData, { unique })

      if (cache) {
        this.currentTargetData.push(cache)
      } else {
        const { content, content_en } = sourceChosenRes[0]
        this.currentTargetData.push({
          id: 0,
          content,
          content_en,
          from_value: sourceChosenRes,
          unique: String(sourceChosenRes.map((curr) => curr.id).join(this.UUID_SEPARATOR))
        })
      }

      this.$emit('updateTarget', this.currentTargetData)
      this.$set(this, 'sourceChosenRes', [])
      this.currentGoToSourceChosenIndex = 0
      this.currentGoToSourcePackageIndex = 0
    },
    handleSplit() {
      if (this.targetChosenRes.length) {
        const currentTargetData = this.currentTargetData.filter((item) => {
          return !this.targetChosenRes.find((res) => res.unique === item.unique)
        })
        this.$set(this, 'currentTargetData', currentTargetData)
        this.$set(this, 'targetChosenRes', [])
      }
    },
    async handleCheckSource(event, { pkg, opt }) {
      await this.$nextTick()
      const { package_id, package_name, title_id } = pkg
      const { id, content } = opt
      const checked = event.target.checked

      if (checked) {
        const data = {
          id,
          content,
          content_en: this.isContentEn ? content : '',
          package_id,
          title_id,
          package_name
        }
        const index = this.sourceChosenRes.findIndex((item) => item.package_id === package_id)

        if (-1 !== index) {
          this.sourceChosenRes.splice(index, 1, data)
        } else {
          this.currentGoToSourceChosenIndex = 0
          this.currentGoToSourcePackageIndex = 0
          this.sourceChosenRes.push(data)
        }
      } else {
        const index = this.sourceChosenRes.findIndex((item) => item.id === id)
        this.sourceChosenRes.splice(index, 1)
      }
    },
    handleCheckTarget(event, { opt }) {
      const checked = event.target.checked

      if (checked) {
        this.targetChosenRes.push(opt)
      } else {
        const index = this.currentTargetData.findIndex((item) => item.unique === opt.unique)
        this.targetChosenRes.splice(index, 1)
      }
    }
  }
}
</script>

<style scoped lang="scss">
$transitionSecond: 0.3s;
$mergeColor: #437dff;
$splitColor: #ff4d4f;
$hasKeyBg: #f0f7ff;

.transfer-container {
  display: flex;
  align-items: center;

  .transfer-action {
    margin: 0 8px;

    .action-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-bottom: 4px;
      color: rgba(0, 0, 0, 0.25);
      &.is-activate {
        cursor: pointer;

        &.is-merge {
          .item-text {
            color: $mergeColor;
          }
          .item-btn {
            color: #fff;
            background-color: $mergeColor;
          }
        }

        &.is-split {
          .item-text {
            color: $splitColor;
          }
          .item-btn {
            color: #fff;
            background-color: $splitColor;
          }
        }

        .item-btn {
          cursor: pointer;
        }
      }
    }

    .item-btn {
      width: 24px;
      height: 24px;
      line-height: 24px;
      margin: 0 auto;
      background-color: #f5f5f5;
      border-radius: 2px;
      border: 1px solid #d9d9d9;
      text-align: center;
      transition: background-color $transitionSecond;
      cursor: not-allowed;
    }

    .item-text {
      font-weight: 400;
      font-size: 14px;
      color: #b2b2b2;
      text-align: center;
      transition: color $transitionSecond;
    }
  }

  .field-title {
    position: sticky;
    top: 0;
    display: block;
    height: 40px;
    line-height: 40px;
    padding: 0 12px;
    background-color: #fff;
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    z-index: 9;
  }

  .has-chosen-key {
    background-color: $hasKeyBg;
    cursor: pointer;
  }

  .text-overflow-header {
    display: block;
    width: 100%;
  }

  .text-overflow-content {
    display: inline-block;
    width: calc(100% - 36px);
    position: relative;
    top: 5px;
  }

  .package-title,
  .target-opt {
    font-weight: 400;
    font-size: 14px;
  }

  .target-opt {
    width: 100%;
    padding: 4px 12px;
  }

  .option-item {
    display: block;
    padding: 6px 12px 6px 42px;
    margin-left: 0 !important;
    &:hover {
      font-weight: 600;
      background-color: $hasKeyBg;
    }
  }

  .ant-checkbox-wrapper + .ant-checkbox-wrapper {
    margin-left: 0;
  }

  ::v-deep .ant-collapse-header {
    position: sticky;
    top: 40px;
    background-color: #fff;
    z-index: 8;
  }

  &.is-lock {
    .transfer-item::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      width: 100%;
      height: 100%;
      background-color: #eee;
      opacity: 0.5;
      z-index: 99;
    }
  }
}
</style>
