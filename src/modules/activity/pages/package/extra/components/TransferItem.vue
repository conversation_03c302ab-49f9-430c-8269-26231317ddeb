<template>
  <div class="transfer-item-container">
    <header class="header">
      <slot name="header"></slot>
    </header>

    <div class="body">
      <slot name="body"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimpleTransferItem',
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
$borderColor: #d9d9d9;

.transfer-item-container {
  position: relative;
  width: 456px;
  height: 264px;
  border: 1px solid $borderColor;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border-bottom: 1px solid $borderColor;
  }

  .body {
    height: 218px;
    overflow-y: scroll;
  }
}
</style>
