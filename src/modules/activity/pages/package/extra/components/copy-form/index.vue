<template>
  <a-form-model ref="form" :model="form" :label-col="labelCol" :wrapper-col="wrapperCol" :rules="rules">
    <a-form-model-item :label="$t('act_copy_direction')" prop="type" required>
      <a-radio-group v-model="form.type" @change="handleTypeChange">
        <a-radio v-if="showTo && isSimOrWifi" :value="1">{{ $t('act_copy_to_other_pkg') }}</a-radio>
        <a-radio :value="2">{{ $t('act_copy_from_another_pkg') }}</a-radio>
      </a-radio-group>
    </a-form-model-item>
    <a-form-model-item :label="$t('package_xinfo_type')" prop="infoType" required>
      <a-radio-group v-model="form.infoType" @change="handleTypeChange">
        <a-radio v-if="isSimOrWifi" :value="1" :disabled="disabledStandardized">{{ $t('104039') }}</a-radio>
        <a-radio :value="2">{{ $t('30924') }}</a-radio>
      </a-radio-group>
    </a-form-model-item>
    <a-form-model-item v-if="form.type === 1" :label="$t('act_groups_to_copy')" prop="value" required>
      <a-select
        v-model="form.value"
        mode="multiple"
        show-search
        :placeholder="$t('global_select')"
        option-filter-prop="label"
        :max-tag-count="5"
        class="select"
      >
        <a-select-option
          v-for="item in selectOptions"
          :key="item.package_id"
          :value="item.package_id"
          :label="`${item.package_id}-${item.title}`"
        >
          {{ item.package_id }} - {{ item.title }}
        </a-select-option>
      </a-select>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    options: {
      type: Array,
      default: () => []
    },
    showTo: {
      type: Boolean,
      default: true
    },
    packageStatus: {
      type: Number,
      default: -1
    },
    isSimOrWifi: {
      type: Boolean,
      default: false
    },
    maxLength: {
      type: Number,
      default: 20
    }
  },
  data() {
    const validateFun = (r, v, callback) => {
      if (!v) {
        return callback(new Error(this.$t('global_select')))
      }
      if (Array.isArray(v) && !v.length) {
        return callback(new Error(this.$t('global_select')))
      }
      callback()
    }
    const validateValueFun = (r, v, callback) => {
      if (!v) {
        return callback(new Error(this.$t('global_select')))
      }
      if (Array.isArray(v)) {
        const len = v.length
        if (!len) {
          return callback(new Error(this.$t('global_select')))
        }
        const max = this.maxLength
        if (len > max) {
          return callback(new Error(this.$t('79625', { num: max })))
        }
      }
      callback()
    }
    return {
      form: {
        type: undefined,
        value: undefined,
        infoType: undefined
      },
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      rules: {
        type: [
          {
            validator: validateFun
          }
        ],
        value: [
          {
            validator: validateValueFun
          }
        ],
        infoType: [
          {
            validator: validateFun
          }
        ]
      }
    }
  },
  computed: {
    isCopyFrom() {
      return this.form.type === 2
    },
    disabledStandardized() {
      const { packageStatus, isCopyFrom } = this
      const unpublish = packageStatus === 0
      if (isCopyFrom && !unpublish) {
        return true
      }
      return false
    },
    selectOptions() {
      const {
        form: { type, infoType },
        options
      } = this
      if (type === 1) {
        return options.filter((item) => {
          const { publish_status, product_type } = item
          const isCombo = product_type === 1
          const isUnpublish = publish_status === 0
          if (infoType === 1) {
            return !isCombo && isUnpublish
          }
          return !isCombo
        })
      }
      return options
    }
  },
  watch: {
    disabledStandardized(v) {
      if (v) {
        this.form.infoType = 2
      }
    }
  },
  mounted() {
    if (!this.showTo || !this.isSimOrWifi) {
      this.form.type = 2
    }
    if (!this.isSimOrWifi) {
      this.form.infoType = 2
    }
  },
  methods: {
    validateForm() {
      return this.$refs.form.validate().catch(() => false)
    },
    getData() {
      return this.form
    },
    handleTypeChange() {
      this.form.value = undefined
    }
  }
}
</script>

<style lang="scss" scoped>
.select {
  width: 300px;
}
</style>
