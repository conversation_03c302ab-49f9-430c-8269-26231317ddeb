<template>
  <div class="district-container">
    <div class="district-wrap">
      <List
        v-if="!disabled"
        ref="list"
        :value.sync="currValues"
        :checked-values.sync="checkedValues"
        :error-msg="errorMsg"
        :disabled="disabled"
        @check="onCheckItem"
        @reportError="handleOpenReportErrorDrawer"
      />
      <div class="district__map-box">
        <Map v-if="!mapPosterior" ref="map" :value="currValues" />
        <slot name="table" :list="currValues">
          <DistrictTable
            v-if="currValues.length"
            ref="districtTable"
            :value="currValues"
            :disabled="disabled"
            :use-itinerary-pick-up-data="useItineraryPickUpData"
            @delete="onDeleteItem"
            @panTo="onPanToItem"
            @reportError="handleOpenReportErrorDrawer"
          />
        </slot>
      </div>
    </div>

    <Map v-if="mapPosterior" ref="map" :value="currValues" />
  </div>
</template>

<script>
// @note !!!
// 这组件即是 other info 区域编辑组件（读写）
// 同时也是兼容显示 itinerary 生成 other info 数据的组件（只读）
import BaseMixin from '@activity/pages/package/extra/mixins/otherInfoItemBase.js'
import List from '@activity/pages/package/extra/components/pickUpLocation/district/list.vue'
import Map from '@activity/pages/package/extra/components/pickUpLocation/district/map.vue'
import DistrictTable from '@activity/pages/package/extra/components/pickUpLocation/district/table.vue'
import { OPEN_REPORT_ERR_DRAWER_2BUS_KEY } from '@activity/utils/modal-message.js'
import { mapEncodingDict } from '@activity/pages/package/units/utils/map/index.js'
import { transformGeomData } from '@activity/pages/package/itinerary/components/departure-district-area/customized/editor/utils.js'

export default {
  name: 'PickUpLocationDistrict',
  components: { List, Map, DistrictTable },
  mixins: [BaseMixin],
  props: {
    mapPosterior: {
      type: Boolean,
      default: false
    },
    errorMsg: {
      type: String,
      default: ''
    },
    useItineraryPickUpData: {
      type: Boolean,
      default: false
    }
  },
  provide() {
    return {
      getGeomByIds: this.getGeomByIds
    }
  },
  data() {
    return {
      cacheCheckedValues: [],
      checkedValues: []
    }
  },
  mounted() {
    if (this.currValues) {
      this.checkedValues = this.currValues.map((item) => item.content.area_id)
      this.cacheCheckedValues = [...this.checkedValues]
    }
  },
  methods: {
    handleOpenReportErrorDrawer(message) {
      klook.bus.$emit(OPEN_REPORT_ERR_DRAWER_2BUS_KEY, { message })
    },
    onDeleteItem({ record }) {
      this.deleteItemById(record.content.area_id)
    },
    deleteItemById(id, autoUncheck = true) {
      this.$refs.map.instance.removePolygonById(id)
      const index = this.currValues.findIndex((item) => item.content.area_id === id)
      if (~index) {
        this.currValues.splice(index, 1)
        // 自动取消勾选列表项
        autoUncheck && this.$refs.list.uncheckItemById(id)
        const checkedValues = this.checkedValues.filter((curr) => curr !== id)
        this.$set(this, 'checkedValues', checkedValues)
        this.cacheCheckedValues = [...this.checkedValues]
      }
    },
    onPanToItem({ record }) {
      if (record.content.pick_up_type === 1) {
        const { location_original, location } = record.content
        this.$refs.map.instance.setCenterByLocation(location_original || location)
      } else {
        this.onPanToItemById(record.content.area_id)
      }
    },
    onPanToItemById(id) {
      this.$refs.map.instance.setCenter2mapByGeomId(id)
    },
    async onCheckItem({ checkedKeys, checked, dataRef }) {
      this.$refs.list.loading = true

      if (checked) {
        this.checkedItem({ checkedKeys, dataRef })
      } else {
        this.uncheckedItem({ checkedKeys, dataRef })
      }

      this.$refs.list.loading = false
    },
    async checkedItem({ checkedKeys, dataRef }) {
      let { id, path } = dataRef
      let paths = [...path, id]
      this.cacheCheckedValues = [...checkedKeys]

      // 跟进节点路径获取最上级的生效(勾选)节点
      let currEffectiveId
      for (let path of paths) {
        if (checkedKeys.includes(path)) {
          currEffectiveId = path
          break
        }
      }
      // 当前生效的节点
      const currEffectiveNode = this.$refs.list.findNodeByIdWithMap(currEffectiveId)
      const data = this.getNewItemData(currEffectiveNode)
      // 勾选联动可能会触发父级被勾选, 所以需要深度清除其子节点
      this.deepClearChildItems(currEffectiveNode)
      // 画围栏
      const result = await this.getGeomByIds([currEffectiveNode.id])
      const polygons = result?.[0]?.polygons
      if (polygons && polygons.length) {
        const res = this.$refs.map.instance.printPolygon({ data, polygons })
        if (res?.polygons?.length) {
          // 居中围栏到可视区
          this.$refs.map.instance.fitBounds2mapByPolygons(res.polygons)
        }

        this.currValues.push(data)
      } else {
        this.$message.error('There is no fence data in the current area')
      }
    },
    async uncheckedItem({ checkedKeys, dataRef }) {
      let { id, path } = dataRef
      let paths = [...path, id]

      this.$refs.map.instance.setCenter2mapByGeomId(id)

      // 由子级触发父级被取消选中
      let currNoneffectiveParentIds = []
      for (let path of paths) {
        if (this.cacheCheckedValues.includes(path) && !checkedKeys.includes(path) && path !== id) {
          currNoneffectiveParentIds.push(path)
          break
        }
      }
      this.cacheCheckedValues = [...checkedKeys]
      // 当前失效的父级IDs
      if (currNoneffectiveParentIds.length) {
        const effectiveChildren = []
        for (let currNoneffectiveParentId of currNoneffectiveParentIds) {
          // 移除之前父级围栏数据
          this.deleteItemById(currNoneffectiveParentId, false)
          // 获取生效的子级ids
          const currNode = this.$refs.list.findNodeByIdWithMap(currNoneffectiveParentId)
          effectiveChildren.push(
            ...currNode.children.filter(
              (child) => !(child.id === id || currNoneffectiveParentIds.includes(child.id))
            )
          )
        }

        // 将剩余有效的子节点展开并画出对应的围栏
        await Promise.all(
          effectiveChildren.map(async (item) => {
            const result = await this.getGeomByIds([item.id])
            const data = this.getNewItemData(item)
            const polygons = result?.[0]?.polygons ?? []
            this.$refs.map.instance.printPolygon({ data, polygons })
            this.currValues.push(data)
          })
        )
        // const ids = effectiveChildren.map((item) => item.id)
        // const result = await this.getGeomByIds(ids)
        // result.forEach((item) => {
        //   const child = effectiveChildren.find((child) => child.id === item.area_id)
        //   const data = this.getNewItemData(child)
        //   this.$refs.map.instance.printPolygon({ data, polygons: item.polygons })
        //   this.currValues.push(data)
        // })
      } else {
        this.cacheCheckedValues = [...checkedKeys]
        this.deleteItemById(id, false)
      }
    },
    deepClearChildItems(data) {
      ;(data.children || []).forEach((child) => {
        this.deleteItemById(child.id, false)
        if (data.children?.length) {
          this.deepClearChildItems(data.children)
        }
      })
    },
    getLevelByNodeType(type) {
      return {
        2: 1,
        3: 2,
        5: 3,
        '-1': 3,
        '-2': 5
      }[type]
    },
    getNewItemData(data) {
      return {
        id: 0,
        content: {
          pick_up_type: 2, // 1:point 2:scope
          map_type: 1, // 地图类型 1：google 2：高德
          supply_api_mapping_key: '',
          amap_code: '',
          location: data.location,
          parent_name: data.parent_name,
          administrative_level: this.getLevelByNodeType(data.type), // 等级 1为country 2为city 3为行政区，县 4为point
          location_name: data.name,
          area_id: data.id,
          google_place_id: data.google_place_id,
          path: data.path
        }
      }
    },
    formatPolygonCoordinates(polygon) {
      return _.flattenDeep(polygon).reduce((acc, curr, index) => {
        if (index % 2) {
          acc[acc.lastIndex].lat = curr
        } else {
          acc.push({
            lng: curr
          })
        }

        return acc
      }, [])
    },
    async getGeomByIds(ids) {
      ids = ids.filter((id) => !!id)

      if (!ids.length) {
        return []
      }

      const response = await ajax.get(ADMIN_API.act.get_geom_by_area_id_list, {
        params: {
          area_id_list: ids.join(',')
        }
      })

      let polygons = []
      return (response.geom_list || []).reduce((acc, curr) => {
        if (!curr.geom) {
          return acc
        }

        const { is_cn_mainland, map_encoding } = curr.customized_config || {}

        let geom
        if (is_cn_mainland && map_encoding === mapEncodingDict.wgs) {
          geom = transformGeomData(curr.geom, mapEncodingDict.wgs2gcj)
        } else {
          geom = curr.geom
        }

        const { type, coordinates } = geom
        if (type === 'MultiPolygon') {
          polygons = coordinates.reduce((acc, curr) => [...acc, this.formatPolygonCoordinates(curr)], [])
        } else {
          polygons = [this.formatPolygonCoordinates(coordinates)]
        }

        return [
          ...acc,
          {
            area_id: curr.area_id,
            polygons
          }
        ]
      }, [])
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../../../../../../src/assets/css/lib/mixins';

.district-wrap {
  display: flex;
  gap: 12px;
  margin: 10px 0;

  overscroll-behavior: initial !important;
  @include mixin-hover-display-scrollbar;
}

.district-container ::v-deep .common-report-error {
  font-weight: 400;
  color: #2073f9;
  line-height: 1.5em;
  text-decoration-line: underline;
  cursor: pointer;

  &:hover {
    opacity: 0.75;
  }
}

.district__map-box {
  flex-grow: 1;
}
</style>
