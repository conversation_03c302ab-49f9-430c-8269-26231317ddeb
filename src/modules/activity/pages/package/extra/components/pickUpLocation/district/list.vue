<template>
  <div class="district-list-container district-list">
    <div class="district-list__title">{{ $t('161212') }}</div>
    <a-form-model :model="form">
      <a-form-model-item prop="searchValue" :rules="rules">
        <a-input
          v-model="searchValue"
          class="district-list__input exclude-v-disable"
          placeholder="input search text"
          :disabled="disabled"
          @change="searchNode"
        >
          <a-icon slot="addonAfter" type="search" />
        </a-input>

        <div v-if="errorMsg" class="custom-explain ant-form-explain">{{ errorMsg }}</div>
      </a-form-model-item>
    </a-form-model>

    <template v-if="searched">
      <a-spin :spinning="loading" wrapper-class-name="district-list__tree">
        <a-tree
          v-if="treeData.length"
          ref="tree"
          v-model="checkedKeys"
          checkable
          :expanded-keys="expandedKeys"
          :auto-expand-parent="false"
          :default-expand-all="false"
          :tree-data="treeData"
          :replace-fields="{
            children: 'children',
            title: 'name'
          }"
          :load-data="onLoadData"
          @check="onCheck"
          @expand="onExpand"
        />

        <svg-icon v-else icon-name="not-found" class="district-list__not-found" />
      </a-spin>

      <div
        v-if="isMerchant && searchValue && !treeData.length"
        class="district-list__report-enter common-report-error"
        @click="handleEmitReportError"
      >
        {{ $t('119840') }}
      </div>
    </template>
  </div>
</template>

<script>
import { isMerchant } from '@/env'
import { getEditLang } from '@activity/utils'

export default {
  name: 'DistrictList',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    checkedValues: {
      type: Array,
      default: () => []
    },
    errorMsg: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isMerchant,
      searchValue: '',
      expandedKeys: [],
      treeData: [],
      loading: false,
      searched: false
    }
  },
  computed: {
    form: {
      get() {
        return {
          searchValue: this.searchValue
        }
      }
    },
    rules() {
      return {
        searchValue: [
          {
            required: true,
            message: 'Please select',
            validator: (rule, value, callback) => {
              if (this.treeData.length) {
                callback()
              } else {
                callback('Please select')
              }
            }
          }
        ]
      }
    },
    checkedKeys: {
      get() {
        return this.checkedValues
      },
      set(values) {
        this.$emit('update:checkedValues', values)
      }
    }
  },
  methods: {
    async requestNodes(data = {}) {
      const response = await ajax.post(ADMIN_API.act.get_current_and_next_child, {
        data: {
          language: getEditLang(),
          types: [2, 3, 5, -2],
          page: 1,
          limit: 50,
          ...data
        }
      })
      return response?.nodes ?? []
    },
    formatNodeData(data) {
      return {
        ...data,
        key: data.id,
        checkable: true,
        disabled: false,
        isLeaf: !!data.is_leaf
      }
    },
    searchNode: _.debounce(async function searchNode(event) {
      this.searched = true
      const query = event.target.value
      this.requestUuid = query

      if (!query) {
        this.expandedKeys = []
        this.treeData = []
        return
      }

      this.loading = true
      const nodes = await this.requestNodes({
        name: query
      })

      if (query === this.requestUuid) {
        const treeData = nodes.map((node) => {
          node.path = this.formatPath(node.path)
          if (node.children) {
            node.children = node.children.map((child) => {
              return this.formatNodeData({
                ...child,
                path: this.formatPath(child.path)
              })
            })
          }

          return this.formatNodeData(node)
        })
        // 建立 Map 索引
        this.nodeMap = this.buildNodeMap(treeData)
        // 展开第一层级
        let expandedKeys = treeData.map((item) => item.key)
        // 关联子节点自动展开父级
        let addExpandedKeys = []
        for (let node of this.nodeMap) {
          const { isLeaf, children, path, id } = node[1]
          const paths = [...path, id]

          if (!isLeaf && !(Array.isArray(children) && children.length)) {
            this.value.forEach((curr) => {
              const currPath = curr.content.path || []
              if (paths.every((p) => currPath.includes(p))) {
                addExpandedKeys.push(id, ..._.xor(currPath, paths))
              }
            })
          }
        }

        addExpandedKeys = [...new Set(addExpandedKeys)]
        for (let key of addExpandedKeys) {
          const response = await this.requestNodes({
            area_id: key
          })
          const children = (response?.[0]?.children ?? []).map((child) => this.formatNodeData(child))
          this.nodeMap.get(key).children = children.map((child) =>
            Object.assign(child, { path: this.formatPath(child.path) })
          )
        }

        this.expandedKeys = [...new Set([...expandedKeys, ...addExpandedKeys])]
        this.treeData = treeData
        this.nodeMap = this.buildNodeMap(treeData)
      }

      this.loading = false
    }, 200),
    formatPath(path) {
      return path
        .split('/')
        .map((id) => Number(id))
        .filter((id) => id)
    },
    onCheck(checkedKeys, event) {
      this.$emit('check', {
        checkedKeys,
        dataRef: event.node.dataRef,
        checked: event.checked
      })
    },
    uncheckItemById(id, list = this.treeData) {
      let delIds = []
      for (let item of list) {
        const children = item.children || []
        if (item.id === id) {
          delIds = [id, ...children.map((child) => child.id)]
          break
        }

        if (children.length) {
          const flag = this.uncheckItemById(id, children)
          if (flag) {
            break
          }
        }
      }

      if (delIds.length) {
        this.checkedKeys = this.checkedKeys.filter((key) => !delIds.includes(key))
      }

      return !!delIds.length
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys
    },
    onLoadData(treeNode) {
      return new Promise((resolve) => {
        if (treeNode.dataRef.children) {
          resolve()
          return
        }

        const { key } = treeNode.dataRef
        this.requestNodes({
          area_id: key
        }).then((response) => {
          const children = (response?.[0]?.children ?? []).map((child) => this.formatNodeData(child))
          treeNode.dataRef.children = children.map((child) =>
            Object.assign(child, { path: this.formatPath(child.path) })
          )
          this.treeData = [...this.treeData]
          this.nodeMap = this.buildNodeMap(this.treeData)

          resolve()
        })
      })
    },
    buildNodeMap(data) {
      const nodeMap = new Map()

      function buildMap(nodes) {
        for (const node of nodes) {
          nodeMap.set(node.id, node)
          if (node.children && node.children.length > 0) {
            buildMap(node.children)
          }
        }
      }

      buildMap(data)
      return nodeMap
    },
    findNodeByIdWithMap(targetId, nodeMap = this.nodeMap) {
      return nodeMap.get(targetId) || null
    },
    handleEmitReportError() {
      this.$emit('reportError', `${this.$t('119840')}(${this.searchValue})`)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../../../../../../src/assets/css/lib/mixins';
$baseMarginPixel: 16px;

.district-list {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  gap: 10px;
  width: 240px;
  min-height: 300px;
  max-height: 758px;
  padding: $baseMarginPixel;
  border-radius: 4px;
  background-color: #f0f7ff;

  &__title {
    font-size: 16px;
    font-weight: 600;
    color: #000;
    line-height: 1.2em;
  }

  &__tree {
    flex: 1;
    background-color: #fff;
    @include mixin-hover-display-scrollbar;

    &:hover {
      overscroll-behavior: inherit !important;
    }
  }

  &__not-found {
    position: absolute;
    left: 50%;
    top: 50px;
    transform: translateX(-50%);
    font-size: 100px;
  }

  &__report-enter {
    margin-top: 16px;
  }
}
</style>
