<template>
  <div :id="mapId"></div>
</template>

<script>
import Geom from '@activity/pages/package/extra/core/geom.js'

export default {
  name: 'DistrictMap',
  props: {
    value: {
      type: Array,
      required: true
    }
  },
  inject: ['getGeomByIds', 'provideData'],
  data() {
    this.mapId = 'district-map'
    return {}
  },
  async mounted() {
    await this.initMap()
    klook.bus.$on('refreshPickUpMap', this.refreshPickUpMap)
  },
  beforeDestroy() {
    klook.bus.$off('refreshPickUpMap', this.refreshPickUpMap)
    this.refreshTimer && clearTimeout(this.refreshTimer)
    this.instance?.destroy?.()
  },
  methods: {
    refreshPickUpMap() {
      this.refreshTimer && clearTimeout(this.refreshTimer)
      this.refreshTimer = setTimeout(async () => {
        this.$emit('refreshing', true)
        await this.initMap()
        this.$emit('refreshing', false)
      }, 200)
    },
    clearAll() {
      this.instance?.clearAllRecords?.()
    },
    async initMap() {
      const options = {
        options: {
          location: this.provideData?.activityDepartureCity?.location || ''
        }
      }
      this.instance = new Geom(document.getElementById(this.mapId), options)
      await this.instance.build()
      this.$emit('initialized')

      const ids = this.value
        .filter((item) => item.content.pick_up_type === 2)
        .map((item) => item.content.area_id)

      const result = await this.getGeomByIds(ids)
      result.forEach((item) => {
        const data = this.value.find((curr) => curr.content.area_id === item.area_id)
        this.instance.printPolygon({ data, polygons: item.polygons })
      })

      // 兼容 itinerary 的数据
      // fixed points
      this.value
        .filter((item) => item.content.pick_up_type === 1)
        .forEach((item) => {
          this.instance.paintMarker({
            ...item,
            content: {
              ...item.content,
              location: item.content.location_original // itinerary location
            }
          })
        })

      this.showAllRecordOnMap()
    },
    showAllRecordOnMap() {
      const positions = [
        ...this.instance.getBoundsPositionsByPolygons(),
        ...this.instance.getBoundsPositionsByMarkers()
      ]
      // 显示点与区域
      this.instance.fitBoundsByPositions(positions)
    }
  }
}
</script>

<style lang="scss" scoped>
#district-map {
  min-width: 300px;
  height: 300px;
}
</style>
