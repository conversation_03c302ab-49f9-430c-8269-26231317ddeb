<template>
  <div class="district-table">
    <div class="district-table__title">
      <span>{{ title }}</span>
      <a
        v-if="!useItineraryPickUpData && $root.isMerchant"
        class="common-report-error"
        @click="handleEmitReportError"
      >
        {{ $t('119839') }}
      </a>
    </div>
    <a-table :columns="columns" :data-source="value" :pagination="false" :scroll="{ x: 600, y: 360 }">
      <div slot="scope" slot-scope="text, record" class="point-item-location">
        <OverflowTextTooltip
          placement="bottom"
          overlay-class-name="common-tooltip-style"
          style="display: block;"
          :delay="200"
        >
          <span style="cursor: pointer;" @click="handlePanToLocation(record)">
            {{ getLocationName(record) }}
          </span>
        </OverflowTextTooltip>
      </div>

      <div slot="titleFees">
        {{ $t('161215') }}<span v-if="currency">({{ currency }})</span>
      </div>

      <div slot="fee" slot-scope="text, record">
        {{ getPriceValue(record) }}
      </div>

      <div slot="action" slot-scope="text, record, index" class="point-item-action">
        <a-button type="link" style="color: #ff7874;" @click="handleDelItem(record, index)">
          {{ $t('global_delete') }}
        </a-button>
      </div>
    </a-table>
  </div>
</template>

<script>
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'

export default {
  name: 'DistrictTable',
  components: {
    OverflowTextTooltip
  },
  props: {
    value: {
      type: Array,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    useItineraryPickUpData: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    currency() {
      for (let item of this.value || []) {
        let currency = item.content.customized_config?.extra_fee?.price_currency || ''

        if (currency) {
          return currency
        }
      }

      return ''
    },
    columns() {
      const title = this.useItineraryPickUpData ? __('167716') : __('104023')
      const columns = [{ title, width: 200, scopedSlots: { customRender: 'scope' } }]

      // 兼容来自 itinerary 生成的数据
      if (this.useItineraryPickUpData && this.currency) {
        columns.push({
          slots: { title: 'titleFees' },
          width: 200,
          scopedSlots: { customRender: 'fee' }
        })
      }

      if (!this.disabled) {
        columns.push({ title: __('76542'), width: 100, scopedSlots: { customRender: 'action' } })
      }

      return columns
    },
    title() {
      if (this.useItineraryPickUpData) {
        return this.$t('167715')
      }

      return this.$t('104293')
    }
  },
  methods: {
    handlePanToLocation(record) {
      this.$emit('panTo', { record })
    },
    handleDelItem(record, index) {
      this.$emit('delete', { record, index })
    },
    handleEmitReportError() {
      const list = this.value.map((item) => {
        const { location_name, google_place_id } = item.content
        return `${location_name}[${google_place_id}]`
      })
      this.$emit('reportError', `${this.$t('119839')}.(${list.join(', ')})`)
    },
    getLocationName(record) {
      return record.content?.customized_config?.customized_area_name || record.content.location_name
    },
    getPriceValue(record) {
      const value = record.content?.customized_config?.extra_fee?.price_value ?? null

      if (typeof value === 'number') {
        return parseFloat(value / 100)
      }

      return ''
    }
  }
}
</script>

<style lang="scss" scoped>
.district-table {
  margin-top: 16px;

  &__title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .point-item-action {
    margin-left: -15px;
  }

  .point-item-location {
    display: grid;
  }
}
</style>
