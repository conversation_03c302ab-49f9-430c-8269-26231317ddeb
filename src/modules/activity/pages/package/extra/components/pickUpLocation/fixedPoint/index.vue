<template>
  <div class="fixed-point">
    <Map ref="map" v-model="currValues" class="fixed-point__map" :disabled="disabled" />

    <FixedTable
      v-if="currValues.length"
      ref="table"
      v-model="currValues"
      :disabled="disabled"
      @delete="onDeleteItem"
      @panTo="onPanToItem"
      @updateSupplyApiKey="onUpdateSupplyApiKey"
      @updateLocationName="onUpdateLocationName"
    />
  </div>
</template>

<script>
import Map from '@activity/pages/package/extra/components/pickUpLocation/fixedPoint/map.vue'
import FixedTable from '@activity/pages/package/extra/components/pickUpLocation/fixedPoint/table.vue'
import BaseMixin from '@activity/pages/package/extra/mixins/otherInfoItemBase.js'

export default {
  name: 'FixedPoint',
  components: {
    Map,
    FixedTable
  },
  mixins: [BaseMixin],
  methods: {
    onDeleteItem({ record, index }) {
      this.$refs.map.instance.removeMarker(record)
      this.currValues.splice(index, 1)
    },
    onPanToItem({ record }) {
      this.$refs.map.instance.setCenter2map(record)
    },
    onUpdateSupplyApiKey({ record, value }) {
      Object.assign(record.content, {
        supply_api_mapping_key: value
      })
    },
    onUpdateLocationName({ value, index }) {
      Object.assign(this.currValues[index].content, {
        location_name: value
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.fixed-point {
  margin-top: 10px;

  &__map {
    width: 100%;
    height: 348px;
  }
}
</style>
