<template>
  <div class="map-search-box">
    <a-select v-if="showMapSourceSelector" value="google_maps" disabled>
      <a-select-option value="google_maps">
        Google MapSearch
      </a-select-option>
    </a-select>

    <a-select
      ref="select"
      show-search
      class="map-search exclude-v-disable"
      :class="{ 'show-search-icon': showSearchIcon }"
      :dropdown-class-name="
        `fixed-map-select-list ${!currentAddress && options.length === 0 ? 'empty-dropdown' : ''}`
      "
      :value="currentAddress"
      :default-active-first-option="false"
      :show-arrow="showAllow || showSearchIcon"
      :filter-option="false"
      :dropdown-match-select-width="false"
      :disabled="disabled"
      @focus="onFocus"
      @search="changeSearchAddress"
    >
      <span slot="placeholder">
        {{ placeholder }}
      </span>
      <span v-if="showSearchIcon" slot="suffixIcon" class="map-search-icon">
        <a-icon type="search" />
      </span>

      <a-select-option
        v-for="option in options"
        :key="`${option.location}_${option.google_place_id}`"
        class="map-search__result-item"
        @click="handleSelectOption(option)"
      >
        <OverflowTextTooltip>{{ option.location_name }}</OverflowTextTooltip>
      </a-select-option>

      <div slot="notFoundContent" class="map-search__not-result">
        <svg-icon icon-name="not-found" class="map-search__not-result-icon" />
        <span class="map-search__not-result-operator">
          {{ $t('104295') }}
          <span
            v-if="showAddCustomLocationBtn"
            class="map-search__not-result-operator-btn"
            @click="handleAddCustomLocation"
          >
            {{ addBtnTxt }}
          </span>
        </span>
      </div>
    </a-select>
  </div>
</template>

<script>
import { getEditLang } from '@activity/utils'
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'
import SvgIcon from '@activity/components/SvgIcon.vue'

export default {
  name: 'FixedMapSearch',
  components: {
    OverflowTextTooltip,
    SvgIcon
  },
  props: {
    value: {
      type: String,
      default: undefined
    },
    baseLocation: {
      type: String,
      default: ''
    },
    showMapSourceSelector: {
      type: Boolean,
      default: true
    },
    showAddCustomLocationBtn: {
      type: Boolean,
      default: true
    },
    showAllow: {
      type: Boolean,
      default: false
    },
    showSearchIcon: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: __('form.gpsLocation')
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    this.mapId = 'fixed-point-map'

    return {
      options: [],
      customAddPointName: ''
    }
  },
  computed: {
    currentAddress: {
      get() {
        return this.value || undefined
      },
      set(val) {
        this.$emit('update:value', val)
      }
    },
    addBtnTxt() {
      return klook.parseStr1(this.$t('104296'), {
        Add: __('28690')
      })
    }
  },
  methods: {
    handleAddCustomLocation() {
      this.currSearchUuid = ''
      this.blurSearchInput()
      this.$emit('addCustomLocation', this.currentAddress)
    },
    changeSearchAddress: _.debounce(async function changeSearchAddress(query) {
      this.currSearchUuid = query

      if (!query) {
        this.resetSearchData([])
        return
      }

      const response = await ajax.get(ADMIN_API.act.search_text_from_google, {
        params: {
          query,
          language: getEditLang(),
          location: this.baseLocation
        }
      })

      if (this.currSearchUuid === query) {
        this.resetSearchData(response.data || [])
      }
    }, 200),
    onFocus() {
      this.currentAddress = ''
    },
    resetSearchData(options) {
      this.currSearchUuid = ''
      this.customAddPointName = ''
      this.$set(this, 'options', options)
    },
    async handleSelectOption(option) {
      this.$emit('select', option)
      this.currSelectedOption = option
      this.currentAddress = option.location_name

      this.customAddPointName = ''
      setTimeout(() => {
        this.$refs.select.$el.querySelector('input').blur()
      })
    },
    blurSearchInput() {
      setTimeout(() => {
        this.$refs.select.$el.querySelector('input').blur()
        this.currentAddress = undefined
        this.$set(this, 'options', [])
      })
    }
  }
}
</script>

<style lang="scss">
@import '../../../../../../../../../src/assets/css/lib/mixins';

.fixed-map-select-list {
  max-height: 214px;
  @include mixin-hover-display-scrollbar;

  &.empty-dropdown {
    &.ant-select-dropdown {
      display: none;
    }
  }

  &.ant-select-dropdown--empty .ant-select-dropdown-menu-item:hover {
    background: #fff !important;
  }

  .map-search {
    &__result {
      position: absolute;
      min-width: 100%;
      padding: 8px 6px;
      background-color: #fff;
      box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08),
        0px 3px 6px -4px rgba(0, 0, 0, 0.12);
      z-index: 999;

      &-item {
        max-width: calc(100vw - 500px);
        padding: 4px 6px;
        border-radius: 4px;
        cursor: pointer;
        &:hover {
          background-color: #f0f0f0;
        }
      }
    }

    &__not-result {
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: 100%;
      padding: 20px;
      text-align: center;
      font-size: 14px;
      line-height: 22px;
      background-color: #fff;

      &-icon {
        font-size: 100px;
      }

      &-operator {
        display: inline-flex;
        flex-wrap: nowrap;
        gap: 6px;
        white-space: nowrap;
        align-items: center;
        margin-top: 4px;

        &-btn {
          cursor: pointer;
          color: #437dff;
        }
      }
    }
  }
}

.map-search.show-search-icon {
  &.ant-select-open .ant-select-arrow-icon svg {
    transform: rotate(0deg);
  }

  .ant-select-selection__rendered {
    margin-right: 32px;
  }

  .map-search-icon {
    position: absolute;
    top: -9px;
    right: -12px;
    width: 32px;
    height: 30px;
    line-height: 32px;
    border-radius: 0 4px 4px 0;
    border-left: 1px solid #d9d9d9;
  }
}
</style>

<style lang="scss" scoped>
.map-search-box {
  display: flex;
  align-items: center;
  gap: 16px;

  ::v-deep .ant-select {
    width: 136px;
  }

  .map-search {
    position: relative;
    flex: 1;
    height: 32px;
  }
}
</style>
