<template>
  <div class="fixed-map">
    <MapSearch
      :value.sync="searchValue"
      :base-location="actDepartureCityLocation"
      v-bind="$attrs"
      @addCustomLocation="onAddCustomLocation"
      @select="onSelect"
    />

    <div v-if="errorMsg" class="custom-explain ant-form-explain">{{ errorMsg }}</div>

    <div :id="mapId" class="map-container" :class="{ 'custom-add-point': !!customAddPointName }"></div>
  </div>
</template>

<script>
import Marker from '@activity/pages/package/extra/core/marker.js'
import MapSearch from './map-search'
import { getUuid } from '@activity/utils'

export default {
  name: 'FixedMap',
  components: {
    MapSearch
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Array,
      required: true
    },
    // marker 最大数量，超过会先进先出淘汰
    maximum: {
      type: Number,
      default: 999
    },
    errorMsg: {
      type: String,
      default: ''
    }
  },
  inject: ['provideData'],
  data() {
    this.mapId = getUuid()

    return {
      searchValue: '',
      customAddPointName: ''
    }
  },
  computed: {
    list() {
      return this.value
    },
    actDepartureCityLocation() {
      return this.provideData?.activityDepartureCity?.location || ''
    }
  },
  mounted() {
    this.initMap()
    klook.bus.$on('refreshPickUpMap', this.refreshPickUpMap)
  },
  beforeDestroy() {
    klook.bus.$off('refreshPickUpMap', this.refreshPickUpMap)
    this.refreshTimer && clearTimeout(this.refreshTimer)
    this.instance?.destroy?.()
  },
  methods: {
    refreshPickUpMap() {
      this.refreshTimer && clearTimeout(this.refreshTimer)
      this.refreshTimer = setTimeout(() => {
        this.initMap()
      }, 200)
    },
    async initMap() {
      const options = {
        options: {
          location: this.actDepartureCityLocation,
          styles: [
            {
              featureType: 'poi.business',
              stylers: [
                {
                  visibility: 'off'
                }
              ]
            },
            {
              featureType: 'road',
              elementType: 'labels.icon',
              stylers: [
                {
                  visibility: 'off'
                }
              ]
            },
            {
              featureType: 'transit',
              stylers: [
                {
                  visibility: 'off'
                }
              ]
            }
          ]
        },
        clickMapCallback: async (event) => {
          if (this.customAddPointName) {
            const { result, data } = await this.getCustomAddressHandleResultPms(event)
            const option = {
              location_name: this.customAddPointName,
              isCustom: true,
              ...data
            }

            this.setPoint2Center(option, result)
            this.insertNewItem(option)

            this.searchValue = ''
            this.customAddPointName = ''
          }
        },
        markerDragendCallback: async (event, { data }) => {
          Object.assign(data.content, (await this.getCustomAddressHandleResultPms(event)).data)
        }
      }
      this.instance = new Marker(document.getElementById(this.mapId), options)
      this.$emit('initialized', this.instance)
      await this.instance.build()
      // 初始 marker
      this.list.forEach((item) => {
        this.instance.paintMarker(item)
      })
      this.instance.fitBounds2mapByMarkers()
    },
    async getCustomAddressHandleResultPms(event) {
      const location = this.instance.latLng2str(event.latLng.toJSON())
      const results = await this.instance.geocodeMapInfo({
        location: event.latLng
      })

      let result = results[0]
      for (let item of results) {
        if (
          item.types.includes('point_of_interest') ||
          item.types.includes('street_address') ||
          item.types.includes('premise')
        ) {
          result = item
          break
        }
      }

      return {
        result,
        data: {
          location,
          google_place_id: result.google_place_id,
          address_desc: result.address_components.reduce((acc, curr) => {
            if (curr.types.includes('postal_code') || curr.types.includes('plus_code')) {
              return acc
            }

            return `${curr.long_name}${acc}`
          }, '')
        }
      }
    },
    onAddCustomLocation(value) {
      this.customAddPointName = value
    },
    async onSelect(option) {
      // location 可能是经过处理后的 wgs84 数据, location_original 是原始的 Google Map 数据
      option.location = option.location_original || option.location
      delete option.location_original

      const results = await this.instance.geocodeMapInfo({
        placeId: option.google_place_id
      })
      const result = results?.[0] ?? null

      // sentry -> location 为 undefined 是无效值
      if (option.location) {
        this.setPoint2Center(option, result)
        this.insertNewItem(option)
      }
    },
    // @param {object} option - 当前新增或者所选值
    // @param {object} result - 根据 placeID 或 坐标获得的 geocodeMapInfo
    setPoint2Center(option, result) {
      if (!this.instance.map) {
        return
      }

      if (result?.geometry?.viewport) {
        this.instance.map.fitBounds(result.geometry.viewport)
      } else {
        const location = this.instance.formatLatLng(option.location)
        this.instance.map.setCenter(this.instance.getGoogleLatLng(location))
        this.instance.map.setZoom(12)
      }
    },
    // 数据都会是 { id: number, content: object } 的格式
    insertNewItem(option) {
      const data = this.getNewItemData(option)
      const existCurrItem = this.list.find((item) => this.instance.isSameMarker(item, data))

      if (!existCurrItem) {
        if (this.list.length >= this.maximum) {
          const rmItem = this.list.shift()
          this.instance.removeMarker(rmItem)
        }
        this.list.push(data)
        this.instance.paintMarker(data)
      }

      this.$emit('change', this.list)
    },
    getNewItemData(data) {
      return {
        id: 0,
        content: {
          pick_up_type: 1, // 1:point 2:scope
          map_type: 1, // 地图类型 1：google 2：高德
          supply_api_mapping_key: '',
          amap_code: '',
          ...data
        }
      }
    }
  }
}
</script>

<style lang="scss">
@import '../../../../../../../../../src/assets/css/lib/mixins';

.fixed-map-select-list {
  max-height: 214px;
  @include mixin-hover-display-scrollbar;

  &.empty-dropdown {
    &.ant-select-dropdown {
      display: none;
    }
  }

  &.ant-select-dropdown--empty .ant-select-dropdown-menu-item:hover {
    background: #fff !important;
  }

  .map-search {
    &__result {
      position: absolute;
      min-width: 100%;
      padding: 8px 6px;
      background-color: #fff;
      box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08),
        0px 3px 6px -4px rgba(0, 0, 0, 0.12);
      z-index: 999;

      &-item {
        max-width: calc(100vw - 500px);
        padding: 4px 6px;
        border-radius: 4px;
        cursor: pointer;
        &:hover {
          background-color: #f0f0f0;
        }
      }
    }

    &__not-result {
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: 100%;
      padding: 20px;
      text-align: center;
      font-size: 14px;
      line-height: 22px;
      background-color: #fff;

      &-icon {
        font-size: 100px;
      }

      &-operator {
        display: inline-flex;
        flex-wrap: nowrap;
        gap: 6px;
        white-space: nowrap;
        align-items: center;
        margin-top: 4px;

        &-btn {
          cursor: pointer;
          color: #437dff;
        }
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.fixed-map {
  width: 100%;

  .map-container {
    width: 100%;
    height: 300px;
    margin-top: 16px;

    &.custom-add-point ::v-deep .gm-style > :first-child {
      cursor: url(../../../points/img/custom-cursor-point.png), default !important;
    }
  }
}
</style>
