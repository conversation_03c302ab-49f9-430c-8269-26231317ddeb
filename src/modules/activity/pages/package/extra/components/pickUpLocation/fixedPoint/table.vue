<template>
  <div class="fixed-point-table">
    <div class="fixed-point-table__title">{{ $t('103760') }}</div>
    <a-table :columns="columns" :data-source="value" :pagination="false" :scroll="{ x: 700, y: 300 }">
      <div slot="location" slot-scope="text, record, index" class="point-item-location">
        <div v-if="currEdit.index === index" class="point-edit">
          <a-input
            v-model="currEdit.value"
            class="exclude-v-disable"
            autofocus
            size="small"
            @blur="blurNameInput"
          >
            <a-button slot="suffix" type="link" size="small" @click="handleSaveName(index)">
              {{ $t('global_save') }}
            </a-button>
          </a-input>
        </div>
        <span v-else class="point-name">
          <OverflowTextTooltip placement="bottom" overlay-class-name="common-tooltip-style" :delay="200">
            <span style="cursor: pointer;" @click="handlePanToLocation(record)">
              {{ record.content.location_name }}
            </span>
          </OverflowTextTooltip>
          <a-icon
            v-if="record.content.isCustom"
            class="point-edit-icon"
            type="edit"
            @click="handleEditName(record, index)"
          />
        </span>
      </div>

      <div slot="address" slot-scope="text, record">
        <OverflowTextTooltip
          placement="bottom"
          overlay-class-name="common-tooltip-style"
          style="display: block;"
          :delay="200"
        >
          {{ record.content.address_desc }}
        </OverflowTextTooltip>
      </div>

      <div slot="action" slot-scope="text, record, index" class="point-item-action">
        <a-popover
          v-if="isAdmin"
          :visible="currSupplyApiKey.key === getVisibleKey(record)"
          title="API Pick Up Mapping Key"
          trigger="click"
          :get-popup-container="getPopupContainer"
        >
          <div slot="content" class="supply-api-key">
            <div class="supply-api-key__body">
              <div class="supply-api-key__body-location-name">
                {{ record.content.location_name }}
              </div>
              <a-input
                v-model="currSupplyApiKey.value"
                class="supply-api-key__body-input"
                allow-clear
                size="small"
                :max-length="500"
              ></a-input>
            </div>
            <div class="supply-api-key__footer">
              <a-button type="primary" size="small" @click="handleConfirmSupplyApiKey(record)">
                {{ $t('global_confirm') }}
              </a-button>
              <a-button type="default" size="small" @click="handleCancelSupplyApiKey">
                {{ $t('global_cancel') }}
              </a-button>
            </div>
          </div>
          <a-button v-if="isApi" type="link" @click="handleSupplyApiKey(record)">
            API Pick Up Mapping Key
          </a-button>
        </a-popover>
        <a-button type="link" style="color: #ff7874;" @click="handleDelItem(record, index)">
          {{ $t('global_delete') }}
        </a-button>
      </div>
    </a-table>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { isAdmin } from '@/env'
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'

const SupplyApiKeyMixin = {
  inject: ['getPackageStatusById'],
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currSupplyApiKey: {
        value: '',
        key: null
      }
    }
  },
  computed: {
    ...mapState({
      clickTarget: (state) => state.clickTarget
    }),
    stockOutType() {
      return this.getPackageStatusById(this.$route.query.package_id)?.stock_out_type ?? 'unknown'
    },
    isApi() {
      return this.stockOutType === 'API'
    }
  },
  watch: {
    clickTarget(value) {
      this.autoClosePopoverDebounce(value)
    }
  },
  methods: {
    autoClosePopoverDebounce: _.debounce(function autoClosePopoverDebounce(clickTarget) {
      if (!this.$el.contains(clickTarget)) {
        this.handleCancelSupplyApiKey()
      }
    }, 60),
    getPopupContainer() {
      return this.$el
    },
    getVisibleKey(record) {
      return `${record.content.google_place_id}_${record.content.location}`
    },
    handleSupplyApiKey(record) {
      const value = record.content.supply_api_mapping_key || ''
      this.$set(this, 'currSupplyApiKey', {
        value,
        key: this.getVisibleKey(record)
      })
    },
    handleConfirmSupplyApiKey(record) {
      this.$emit('updateSupplyApiKey', { record, value: this.currSupplyApiKey.value })
      this.handleCancelSupplyApiKey()
    },
    handleCancelSupplyApiKey() {
      this.currSupplyApiKey.key = null
    }
  }
}

export default {
  name: 'FixedPointTable',
  components: {
    OverflowTextTooltip
  },
  mixins: [SupplyApiKeyMixin],
  props: {
    value: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      isAdmin,
      currEdit: {
        value: '',
        index: -1
      }
    }
  },
  computed: {
    columns() {
      const columns = [
        { title: __('104020'), width: 300, scopedSlots: { customRender: 'location' } },
        { title: __('104293'), scopedSlots: { customRender: 'address' } }
      ]

      if (!this.disabled) {
        const width = this.isApi ? 280 : 200
        columns.push({ title: __('104294'), width, scopedSlots: { customRender: 'action' } })
      }

      return columns
    }
  },
  methods: {
    handlePanToLocation(record) {
      this.$emit('panTo', { record })
    },
    handleDelItem(record, index) {
      this.$emit('delete', { record, index })
    },
    handleEditName(record, index) {
      this.currEdit.value = record.content.location_name
      this.currEdit.index = index
    },
    blurNameInput() {
      // 需晚于 handleSaveName 执行
      this.nameTimer && clearTimeout(this.nameTimer)
      this.nameTimer = setTimeout(async () => {
        await this.$nextTick()
        this.$set(this, 'currEdit', this.$options.data().currEdit)
        this.nameTimer = null
      }, 200)
    },
    handleSaveName(index) {
      this.$emit('updateLocationName', { value: this.currEdit.value, index })
      this.$set(this, 'currEdit', this.$options.data().currEdit)
    }
  }
}
</script>

<style lang="scss" scoped>
.fixed-point-table {
  margin-top: 16px;

  ::v-deep {
    .ant-popover-title {
      text-align: left;
    }

    .supply-api-key {
      &__body {
        &-location-name {
          font-size: 16px;
          line-height: 22px;
        }
        &-input {
          width: 320px;
          margin-top: 12px;
        }
      }

      &__footer {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
        margin-top: 12px;
      }
    }
  }

  .point-item-location {
    width: 280px;
  }

  .point-edit {
    margin: -1px 0px -1px -7px;
    &-icon:hover {
      color: #437dff;
    }

    ::v-deep .ant-input {
      width: 100%;
      padding-right: 60px;
    }
  }

  .point-name {
    display: flex;
    gap: 16px;
    align-items: center;
    border: 1px solid transparent;
  }

  .point-item-action {
    margin-left: -15px;
  }
}
</style>
