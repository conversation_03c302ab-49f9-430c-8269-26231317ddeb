<template>
  <a-modal
    :visible="visible"
    :title="modal.isPreview ? 'Preview' : ''"
    :closable="false"
    :width="modal.isPreview ? '520px' : '720px'"
  >
    <template slot="footer">
      <a-button :type="modal.isPreview ? 'primary' : 'default'" @click="$emit('close')">
        {{ $t('contentApi.close') }}
      </a-button>
    </template>

    <template v-if="modal.isPreview">
      <a-form-model layout="vertical">
        <a-form-model-item v-for="(item, index) of modal.form.info" :key="index" :label="item.name">
          <template v-if="modal.form.type_flag === 6">
            <a-checkbox>
              {{ item.hint }}
            </a-checkbox>
          </template>
          <component
            :is="previewComponentDict[modal.form.type_flag]"
            v-else
            :placeholder="item.hint"
            :options="getSelectOpts(item.content || '')"
            :format="previewComponentFmt[modal.form.type_flag]"
            :label="item.name"
            :mode="
              modal.form.mode
                ? modal.form.mode
                : previewComponentMode[modal.form.type_flag]
                ? previewComponentMode[modal.form.type_flag]
                : 'default'
            "
          >
          </component>
        </a-form-model-item>
      </a-form-model>
    </template>
  </a-modal>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    modal: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      previewComponentDict: {
        3: 'a-select',
        1: 'a-input',
        2: 'a-date-picker',
        4: 'a-time-picker',
        5: 'a-checkbox'
      },
      previewComponentFmt: {
        2: 'YYYY-MM-DD',
        4: 'HH:mm'
      },
      previewComponentMode: {
        2: 'date'
      }
    }
  },
  methods: {
    getSelectOpts(data) {
      //  Customized other info preview 支持输入用（,）分隔的内容。
      let arr = Array.isArray(data) ? data : data.split(',')
      return arr.map((item, index) => ({
        label: item,
        value: item + index // 加上index,防止多个value值相同时控制台报错警告
      }))
    }
  }
}
</script>
