// https://docs.google.com/spreadsheets/d/1VRBRgWs-Pb9D_NJCBuMnAgvrrVQ9jc-hYVvLoBE00gA/edit#gid=1428169185
// 数值对应的联系人内容 { 0: '称谓', 1: '姓名（英文）', 2: '姓名（本地语言）', 3: '姓氏', 4: '名字', 5: '国家 / 地区', 6: '手机号码', 7: '邮箱' }
// ↓↓↓ category id includes type ↓↓↓
// type1: 可选项有 [3, 4, 5]，勾选中[3, 4, 5]
// type2: 可选项有 [6, 7]: 勾选中[6, 7]
// type3: 可选项有 [0, 1, 2, 5, 6, 7] : 勾选中[5, 6, 7]
// type1 && type2 或 category id 都不在 type 里，则是 [3, 4, 5, 6, 7]
export const type1 = [
  1,
  2,
  4,
  7,
  8,
  9,
  14,
  15,
  16,
  20,
  21,
  22,
  187,
  192,
  206,
  211,
  220,
  223,
  228,
  230,
  232,
  249,
  251,
  254,
  256,
  258,
  260,
  263,
  266,
  275,
  279,
  282,
  287,
  300,
  303,
  306,
  309,
  311,
  313,
  331,
  356,
  360
]

export const type2 = [
  1,
  2,
  4,
  7,
  8,
  9,
  14,
  15,
  16,
  17,
  18,
  19,
  20,
  21,
  22,
  168,
  171,
  177,
  179,
  185,
  187,
  192,
  194,
  198,
  206,
  211,
  220,
  223,
  228,
  230,
  232,
  249,
  251,
  254,
  256,
  258,
  260,
  263,
  266,
  275,
  279,
  282,
  287,
  298,
  300,
  303,
  306,
  309,
  311,
  313,
  317,
  319,
  321,
  323,
  325,
  329,
  331,
  333,
  341,
  343,
  345,
  353,
  356,
  358,
  360,
  364
]

export const type3 = [
  1,
  2,
  4,
  7,
  8,
  9,
  14,
  15,
  16,
  17,
  18,
  19,
  20,
  21,
  22,
  168,
  171,
  177,
  179,
  187,
  194,
  206,
  220,
  223,
  228,
  230,
  232,
  251,
  263,
  266,
  279,
  282,
  287,
  298,
  300,
  303,
  306,
  309,
  311,
  313,
  317,
  319,
  321,
  323,
  325,
  331,
  413,
  415,
  523
]

// 临时前端处理 group 锁定
export const groupRefFieldTagDict = {
  pick_up_location_scope: 'pick_up_location_scope',
  hotel_address: 'hotel_address',
  meet_up_location: 'meet_up_location',
  pick_up_location: 'pick_up_location'
}

// other info pick-up location key
export const attrValueKey = {
  // before itinerary v3
  selection_district: 'selection_district',
  selection_point: 'selection_point',
  // itinerary v3
  customized_attr_value: 'customized_attr_value'
}
