// import { GoogleMap } from '@activity/pages/package/extra/core/map.js'
import Marker from './marker'

export default class Geo<PERSON> extends Marker {
  #geomRecord = []
  zoomTimer = null

  constructor() {
    super(...arguments)

    return this
  }

  // constructor 不支持异步, 实例需执行 build 初始化 Map
  async build(opts = {}) {
    await super.initMap(opts)

    return this
  }

  // 接口节点 id 或者 other info 字段 value content.area_id, 字段名不同但二者等价, 这里用 id 指代二者
  printPolygon({ data, polygons }) {
    if (!this.map) {
      return
    }

    this.map.setCenter(this.formatLatLng(data.content.location))
    const item = {
      ...data,
      polygons: this.printGMapPolygons(polygons)
    }
    this.#geomRecord.push(item)

    return item
  }

  printGMapPolygons(polygons) {
    return polygons.map((coordinates) => {
      const polygon = new google.maps.Polygon({
        paths: coordinates,
        strokeColor: '#FF4D4F',
        strokeOpacity: 0.8,
        strokeWeight: 3,
        fillColor: '#FF4D4F',
        fillOpacity: 0.45
      })

      polygon.setMap(this.map)

      return polygon
    })
  }

  clearAllRecords() {
    this.#geomRecord.forEach((item) => {
      const polygons = item.polygons || []
      polygons.forEach((polygon) => polygon.setMap(null))
    })

    this.#geomRecord = []
  }

  removePolygonById(id) {
    const index = this.#geomRecord.findIndex((item) => item.content.area_id === id)
    if (~index) {
      const polygons = this.#geomRecord.splice(index, 1)?.[0]?.polygons ?? []
      polygons.forEach((polygon) => polygon.setMap(null))
    }
  }

  setCenter2mapByGeomId(id) {
    const curr = this.#geomRecord.find((item) => item.content.area_id === id)
    if (curr) {
      if (curr.polygons.length) {
        this.fitBounds2mapByPolygons(curr.polygons)
      } else {
        const latLng = this.formatLatLng(curr.content.location)
        this.map.setCenter(latLng)
      }
    }
  }

  getGeomRecord() {
    return this.#geomRecord
  }

  fitBounds2mapByPolygons(polygons) {
    const boundsPositions = this.getBoundsPositionsByPolygons(polygons)

    if (!boundsPositions?.length || !this.map) return

    const bounds = new this.mapSdk.LatLngBounds()
    boundsPositions.forEach((item) => {
      bounds.extend(item)
    })
    this.map.fitBounds(bounds.toJSON())

    // this.zoomTimer && clearTimeout(this.zoomTimer)
    // this.zoomTimer = setTimeout(() => {
    //   const currZoom = this.map.getZoom()
    //   this.map.setZoom(Math.min(this.fitBoundsMinZoom, currZoom))
    //   this.zoomTimer = null
    // }, 200)
  }

  getBoundsPositionsByPolygons(polygons) {
    const polyArray = polygons || this.getGeomRecord().reduce((acc, curr) => [...acc, ...curr.polygons], [])
    if (polyArray.length) {
      const positions = []
      let path, paths
      for (let polys = 0; polys < polyArray.length; polys++) {
        paths = polyArray[polys].getPaths()
        for (let i = 0; i < paths.getLength(); i++) {
          path = paths.getAt(i)
          for (let ii = 0; ii < path.getLength(); ii++) {
            positions.push(path.getAt(ii))
          }
        }
      }

      return positions
    }

    return []
  }

  getArrayBounds(polyArray) {
    let bounds = new this.mapSdk.LatLngBounds()
    let path, paths
    for (let polys = 0; polys < polyArray.length; polys++) {
      paths = polyArray[polys].getPaths()
      for (let i = 0; i < paths.getLength(); i++) {
        path = paths.getAt(i)
        for (let ii = 0; ii < path.getLength(); ii++) {
          bounds.extend(path.getAt(ii))
        }
      }
    }

    return bounds
  }

  destroy() {
    this.clearListener()
    this.clearAllRecords()
    super.destroy()
  }
}
