import { initGoogleMaps } from '@activity/components/taxonomy/gmaps.js'
import { defaultMapOptions } from '@activity/pages/poiTool/utils/constant.js'
import eventEmitter from '@activity/pages/poiTool/utils/eventEmitter.js'

let currInitObject = {
  target: null,
  map: null
}

class GoogleMap extends eventEmitter {
  container = null
  mapSdk = null
  map = null
  geocoder = null
  listeners = []
  fitBoundsMinZoom = 11
  data = null
  mapState = null
  commonInfoWindow = null

  defaultMapOptions = {
    ...defaultMapOptions,
    fullscreenControl: true
  }

  constructor(container, config = {}) {
    super({ namespace: 'map' })
    this.container = container
    this.config = config
    this.fitBoundsMinZoom = config.fitBoundsMinZoom || 11

    return this
  }

  async build() {
    await this.initMap(...arguments)
    this.$emit('initialized')

    return this
  }

  async initMap(opts = {}) {
    const options = Object.assign({}, this.defaultMapOptions, this.config.options || {})
    this.mapSdk = (await initGoogleMaps(opts)).maps
    // 当前活动 location
    const { location, ...otherOpts } = options
    if (location) {
      otherOpts.center = this.formatLatLng(location)
    }

    if (!this.container) {
      return
    }

    if (currInitObject.target === this.container) {
      this.map = currInitObject.map
    } else {
      this.map = new this.mapSdk.Map(this.container, otherOpts)
      currInitObject.target = this.container
      currInitObject.map = this.map
      this.addListener(this.map, 'click', (event) => {
        this.config?.clickMapCallback?.(event)
      })
    }

    this.data = new this.mapSdk.Data()
    this.commonInfoWindow = new this.mapSdk.InfoWindow()
  }

  addListener(target, eventName, fn) {
    let listener
    if (this.mapSdk?.event?.addListener) {
      listener = this.mapSdk.event.addListener(target, eventName, fn)
    }

    this.listeners.push({
      target,
      eventName,
      listener
    })

    return listener
  }

  removeListener(target, eventName) {
    const index = this.listeners.findIndex((item) => item.target === target && eventName === item.eventName)
    if (~index) {
      const curr = this.listeners.splice(index, 1)[0]
      curr?.remove?.()
    }
  }

  clearListener() {
    this.listeners.forEach((item) => item.listener?.remove?.())
    this.listeners = []
  }

  getContainer() {
    return this.map.getDiv()
  }

  getCenter() {
    return this.map?.getCenter?.()
  }

  setCenter(latLng) {
    this.map.panTo(latLng)
  }

  setCenterByLocation(location) {
    if (!location || !this.map) return

    const latLng = this.formatLatLng(location)

    this.map.panTo(this.getGoogleLatLng(latLng))
  }

  getMapZoom() {
    return this.map?.getZoom?.() || this.fitBoundsMinZoom
  }

  setZoom(zoom = this.defaultMapOptions.zoom) {
    this.map.setOptions({
      zoom
    })
  }

  stashMapState() {
    this.mapState = {
      zoom: this.getMapZoom(),
      center: this.getCenter()
    }
  }

  recoverMapState() {
    if (!this.mapState) return

    const { zoom, center } = this.mapState
    this.setZoom(zoom)
    center && this.setCenter(center)
  }

  clearMapStash() {
    this.mapState = null
  }

  setMinZoom(minZoom) {
    this.map.setOptions({
      minZoom
    })
  }

  // data: { placeId, location }
  async geocodeMapInfo(data) {
    if (!this.mapSdk) {
      return []
    }

    if (!this.geocoder) {
      this.geocoder = new this.mapSdk.Geocoder()
    }

    const { results } = (await this.geocoder?.geocode?.(data)) || {}

    return results || []
  }

  getGoogleLatLng({ lat, lng }) {
    return new google.maps.LatLng(lat, lng)
  }

  formatDecimalPoint(value, digit = 7) {
    return +Number(value).toFixed(digit)
  }

  formatLatLng(latLng) {
    let lng, lat
    if (latLng.lng && latLng.lat) {
      lat = latLng.lat
      lng = latLng.lng
    } else {
      const lagLatArr = Array.isArray(latLng) ? latLng : String(latLng).split(',')
      lat = lagLatArr[0]
      lng = lagLatArr[1]
    }

    return {
      lng: this.formatDecimalPoint(lng),
      lat: this.formatDecimalPoint(lat)
    }
  }

  latLng2str(latLng) {
    return `${latLng.lat},${latLng.lng}`
  }

  fitBoundsByPositions(positions) {
    if (this.map && Array.isArray(positions) && positions.length) {
      const bounds = new this.mapSdk.LatLngBounds()
      positions.forEach((item) => {
        try {
          bounds.extend(item)
          // eslint-disable-next-line no-empty
        } catch (e) {}
      })

      this.map.fitBounds(bounds)
    }
  }

  destroy() {
    this.clearListener()
    this.data?.setMap?.(null)
    this.data = null
    this.mapSdk = null
    this.map = null
    this.container = null
    this.clearMapStash()
  }
}

export { GoogleMap }
export default GoogleMap
