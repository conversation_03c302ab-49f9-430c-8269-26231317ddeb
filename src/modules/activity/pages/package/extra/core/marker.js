import { GoogleMap } from '@activity/pages/package/extra/core/map.js'
// import customMapPoint from '@activity/pages/package/extra/points/img/custom-map-point.png'
import defaultMapPoint from '@activity/pages/package/extra/points/img/default-map-point.png'

const eventNames = ['click', 'mouseover', 'mouseout']

export default class Marker extends GoogleMap {
  // { marker, id, content }[]
  #makerRecord = []
  zoomTimer = null

  constructor() {
    super(...arguments)

    return this
  }

  // constructor 不支持异步, 实例需执行 build 初始化 Map
  async build(opts = {}) {
    await super.initMap(opts)

    return this
  }

  paintMarker(data, title = null) {
    if (!data.content?.location || !this.mapSdk) {
      return
    }
    const latLng = this.formatLatLng(data.content.location)
    const { isCustom } = data.content

    // https://developers.google.com/maps/documentation/javascript/reference/marker?hl=zh-cn#MarkerOptions.animation
    const marker = new this.mapSdk.Marker({
      position: latLng,
      map: this.map,
      draggable: isCustom,
      icon: {
        url: defaultMapPoint,
        scaledSize: {
          width: 26,
          height: 36
        }
      },
      animation: this.mapSdk.Animation.DROP,
      cursor: isCustom ? 'pointer' : 'not-allowed'
    })

    this.recordMarker(data, marker)

    eventNames.forEach((eventName) => {
      this.addListener(marker, eventName, (event) => {
        this.$emit(eventName + 'Marker', { event, marker })
      })
    })

    if (title) {
      marker._title = title || ''
      this.addListener(marker, 'mouseover', () => {
        this.commonInfoWindow.setContent(marker._title)
        this.commonInfoWindow.open(this.map, marker)
      })

      this.addListener(marker, 'mouseout', () => {
        this.commonInfoWindow.close()
      })
    }

    isCustom &&
      this.addListener(marker, 'dragend', (event) => {
        this.config.markerDragendCallback?.(event, { data, marker })
      })
  }

  isSameMarker(curr, next) {
    return curr.content.location === next.content.location && curr.content.place_id === next.content.place_id
  }

  findMarkerByData(data) {
    return this.#makerRecord.find((item) => this.isSameMarker(data, item))
  }

  recordMarker(data, marker) {
    const item = { ...data, marker }
    const index = this.#makerRecord.findIndex((item) => this.isSameMarker(data, item))
    if (!~index) {
      this.#makerRecord.push(item)
    } else {
      this.#makerRecord.splice(index, 1, item)
    }
  }

  getDataByMarker(marker) {
    return this.#makerRecord.find((item) => item.marker === marker)
  }

  removeMarker(data) {
    const index = this.#makerRecord.findIndex((item) => this.isSameMarker(data, item))
    if (~index) {
      const item = this.#makerRecord.splice(index, 1)[0]

      ;[eventNames, 'dragend'].forEach((eventName) => {
        this.removeListener(item.marker, eventName)
      })
      item?.marker?.setMap?.(null)
    }
  }

  clearAllRecords() {
    this.#makerRecord.forEach(({ marker }) => {
      marker.setMap(null)
    })
    this.#makerRecord = []
  }

  setCenter2map(data) {
    // @note data.location 为 itinerary 格式, data.content.location 为 other info 数据格式
    this.setCenterByLocation(data.location || data.content.location)
  }

  getMarkerRecord() {
    return this.#makerRecord
  }

  fitBounds2mapByMarkers(markers) {
    const boundsPositions = this.getBoundsPositionsByMarkers(markers)

    if (!boundsPositions?.length || !this.map) return

    const bounds = new this.mapSdk.LatLngBounds()
    boundsPositions.forEach((item) => {
      bounds.extend(item)
    })
    this.map.fitBounds(bounds)

    this.zoomTimer && clearTimeout(this.zoomTimer)
    this.zoomTimer = setTimeout(() => {
      if (!this.map) {
        return
      }

      const currZoom = this.map.getZoom()
      this.map.setZoom(Math.min(this.fitBoundsMinZoom, currZoom))
      this.zoomTimer = null
    }, 600)
  }

  getBoundsPositionsByMarkers(markers) {
    const markerArray = markers || this.getMarkerRecord().map((item) => item.marker)

    if (markerArray.length) {
      const positions = []

      for (let i = 0; i < markerArray.length; i++) {
        if (markerArray[i].getVisible()) {
          positions.push(markerArray[i].getPosition())
        }
      }

      return positions
    }

    return []
  }

  scaleMarker(marker, scaledSize = 1) {
    if (!marker?.getIcon || isNaN(scaledSize)) return

    var currentIcon = marker.getIcon()

    if (currentIcon) {
      const newSize = new google.maps.Size(
        currentIcon.scaledSize.width * scaledSize,
        currentIcon.scaledSize.height * scaledSize
      )

      const newIcon = {
        url: currentIcon.url,
        scaledSize: newSize,
        anchor: new google.maps.Point(newSize.width / 2, newSize.height / 2)
      }

      marker.setIcon(newIcon)
    }
  }

  destroy() {
    this.clearListener()
    this.clearAllRecords()
    super.destroy()
  }
}
