<template>
  <div
    id="package-extra-editable"
    v-disabled="{ lock: wholeLock }"
    class="package_extra--container"
    :class="styleType"
    :data-spm-page="isSpuFlow ? undefined : getPageSpm"
  >
    <p v-if="modify_restrict_hint" class="restrict-hint">
      {{ $t('act_modify_restrict_hint') }}
    </p>
    <!-- addon exist_mapped_add_on -->
    <a-alert v-if="exist_mapped_add_on" :message="$t('79699')" banner type="warning" class="top-alert" />

    <a-affix v-if="isComboPackage" :offset-top="64">
      <div class="package_extra--section merge-selected-items-operate">
        <label
          :style="{
            cursor: mergeOtherInfoDrawer.mergeInfo.length ? 'pointer' : 'default'
          }"
          @click="handleGoToSelectedMerged"
        >
          {{ itemSelectedTxt }}
        </label>
        <a-button
          type="primary"
          :disabled="isNewOtherInfoLoading || mergeOtherInfoDrawer.mergeInfo.length < 2"
          @click="handleMergeSelectedItems"
        >
          {{ $t('73115') }}
        </a-button>
      </div>
    </a-affix>

    <!-- 用户信息（已结构化） -->
    <div v-disabled="vDisabled" class="package_extra--section">
      <div :id="$t('83902')" class="merchant-guide-header">
        <div>{{ $t('83902') }}</div>
        <a-button
          v-if="needCopyButton || (!isSpuFlow && showCopyButton)"
          class="copy-btn"
          :data-spm-module="getCopySpm"
          data-spm-virtual-item="__virtual"
          size="small"
          type="primary"
          ghost
          :disabled="
            isGlobalDisabled ||
            isAdminEm ||
            disabledCustomerOtherInfo ||
            exist_mapped_add_on ||
            isPublishWithAI
          "
          @click.stop="handleCopy"
        >
          {{ $t('78220') }}
        </a-button>
      </div>
      <a-spin :spinning="isNewOtherInfoLoading">
        <div v-if="standardizedTips.length" class="standardized-tips">
          <p v-if="standardizedTips.length < 2" v-html="standardizedTips[0]" />
          <template v-else>
            <p style="margin-bottom: 5px">
              {{ $t('other_info_standardized_tips_act_multi') }}
            </p>
            <p v-for="(tip, idx) in standardizedTips" :key="idx">
              -&nbsp;&nbsp;
              <span v-html="tip" />
            </p>
          </template>
        </div>

        <div class="package_extra-block">
          <div :bordered="false">
            <!-- 联系信息 -->
            <div key="contact" class="fields-box">
              <div slot="header">
                <p class="package_extra-block_title">
                  {{ $t('other_info_contact') }}
                </p>
                <p class="package_extra-block_tips">
                  {{ $t('other_info_following_info_collected') }}
                </p>
              </div>
              <div class="collapse-card">
                <div class="package_extra-block_content">
                  <!-- 注释 -->
                  <a-checkbox-group v-model="contactInfoChecked" style="width: 100%">
                    <BasicOverflowCheckbox
                      v-for="item in contactInfoOptions"
                      :key="item.value"
                      :value="item.value"
                      :disabled="isGlobalDisabled || item.disabled"
                      width="25%"
                      @change="handleContactInfoChange"
                    >
                      {{ item.label }}
                    </BasicOverflowCheckbox>
                  </a-checkbox-group>
                </div>
              </div>
            </div>
            <!-- 出行人必填信息设置 -->
            <div key="participants" class="fields-box" :style="{ margin: '8px 0 0 0' }">
              <div slot="header">
                <p class="package_extra-block_title is-required">
                  {{ $t('other_info_required_participants') }}
                </p>
              </div>
              <div class="collapse-card" style="display: block">
                <template v-for="(traveller, idx) in travellerGroups">
                  <p v-if="isComboPackage" style="margin: 8px 0" :key="idx + 'name'">
                    {{ traveller.package_id }} - {{ traveller.package_name }}
                  </p>
                  <TravelerRequiredInfo
                    ref="TravelerRequiredInfo"
                    class="package_extra-block_content"
                    :groupCheckedList="traveller.checked"
                    :travellerGroups="traveller.groups"
                    :polish="isAdminEm"
                    :key="idx"
                    :disabledRadio="isComboPackage || exist_mapped_add_on"
                    :disabledGroup="isGlobalDisabled || disabledInfo || isComboPackage || exist_mapped_add_on"
                    v-bind="commonComboAttrs"
                    @changeGroupCheckedList="(data) => changeGroupCheckedList({ data, traveller })"
                    @toViewMerged="handleViewMerged"
                    @changeData="onChangeData"
                    @updateData="(data) => handleUpdateTravellerData(data, idx)"
                  />
                </template>
              </div>
            </div>
            <!-- 邮寄信息 -->
            <div v-if="postInfoGroup.length" key="postInfo" class="fields-box">
              <div slot="header">
                <p class="package_extra-block_title">
                  {{ $t('28640') }}
                </p>
                <p class="package_extra-block_tips">
                  {{ $t('28632') }}
                </p>
              </div>
              <div class="collapse-card">
                <div class="package_extra-block_content">
                  <choice-2-group
                    ref="postInfoGroup"
                    :original-groups="postInfoGroup"
                    :group-checked-list.sync="groupCheckedList"
                    :polish="isAdminEm"
                    :disabled="isGlobalDisabled || disabledInfo || exist_mapped_add_on"
                    v-bind="commonComboAttrs"
                    @toViewMerged="handleViewMerged"
                    @updateData="(data) => handelUpdateData(data, 'postInfo')"
                  />
                </div>
              </div>
            </div>
            <!-- 附加信息 -->
            <div key="additional" class="fields-box">
              <div slot="header">
                <p class="package_extra-block_title">
                  {{ $t('other_info_additional') }}
                </p>
                <p class="package_extra-block_tips">
                  {{ $t('other_info_less_likely_warn_2') }}
                </p>
              </div>
              <div class="collapse-card">
                <div class="package_extra-block_content">
                  <other-info-group
                    ref="infoGroups"
                    :style-type="styleType"
                    :stripe="true"
                    :disabled="isGlobalDisabled || disabledInfo || exist_mapped_add_on || isPublishWithAI"
                    :original-groups="infoGroups"
                    :polish="isAdminEm"
                    v-bind="commonComboAttrs"
                    :group-checked-list.sync="groupCheckedList"
                    :package-id="package_id"
                    @changeData="onChangeData"
                    @toViewMerged="handleViewMerged"
                    @updateData="(data) => handelUpdateData(data, 'info')"
                    @selectGroupOption="onSelectGroupOption"
                    @preview="customizedPreview"
                  />
                </div>
              </div>
            </div>
            <!-- T&C -->
            <div v-if="termAndConditionGroup.length" key="t&c" class="fields-box">
              <div slot="header">
                <p class="package_extra-block_title">
                  {{ $t('28679') }}
                </p>
                <p class="package_extra-block_tips">{{ $t('28686') }}@toMerge</p>
              </div>
              <div class="collapse-card">
                <div class="package_extra-block_content">
                  <choice-2-group
                    ref="termAndConditionGroup"
                    :original-groups="termAndConditionGroup"
                    :group-checked-list.sync="groupCheckedList"
                    :polish="isAdminEm"
                    :disabled="isGlobalDisabled || disabledInfo || exist_mapped_add_on"
                    :show-check-list="false"
                    v-bind="commonComboAttrs"
                    @toViewMerged="handleViewMerged"
                    @updateData="(data) => handelUpdateData(data, 'termAndCondition')"
                  />
                </div>
              </div>
            </div>
            <!-- 自定义 other info -->
            <div v-if="customizedGroups && customizedGroups.length" key="general" class="fields-box">
              <div slot="header" class="package_extra-title_wrap">
                <p class="package_extra-block_title">
                  {{ $t('30924') }}
                </p>
              </div>
              <div class="collapse-card">
                <div class="package_extra-block_content">
                  <other-info-group
                    ref="customizedGroups"
                    :style-type="styleType"
                    :canDrag="true"
                    :stripe="true"
                    :disabled="
                      isGlobalDisabled ||
                      disabledInfo ||
                      disabledCustomerOtherInfo ||
                      exist_mapped_add_on ||
                      isPublishWithAI
                    "
                    :original-groups="customizedGroups"
                    :polish="isAdminEm"
                    :disabledCheckbox="supportNewOtherinfoType !== 0 || isComboPackage"
                    :group-checked-list.sync="groupCheckedList"
                    :package-id="package_id"
                    v-bind="commonComboAttrs"
                    @changeData="onChangeData"
                    @updateData="(data) => handelUpdateData(data, 'customized')"
                    @preview="customizedPreview"
                    @toViewMerged="handleViewMerged"
                    @selectGroupOption="onSelectGroupOption"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-spin>
    </div>

    <CommonFooter v-if="!isSpuFlow && !calcIsCreateGuide" class="merchant-footer">
      <a-button v-if="!activityIsUnablePreview" class="v-disabled-exclude" @click="handlePreview">
        {{ $t('global_preview') }}
      </a-button>
      <a-button
        v-bind="calcSaveData"
        type="primary"
        class="v-disabled-exclude js-edit-save-btn"
        :disabled="isGlobalDisabled || isNewOtherInfoLoading || isOldOtherInfoLoading"
        @click="handleCheckForm"
      >
        {{ $t('global_save') }}
      </a-button>
    </CommonFooter>

    <!-- 复制弹窗 -->
    <a-modal
      v-model="showCopyForm"
      :width="780"
      :title="$t('78220')"
      @ok="handleFormOk"
      @cancel="showCopyForm = false"
    >
      <CopyForm ref="copyForm" :options="options" :show-to="!isMerchant" />
    </a-modal>
    <a-modal
      v-model="copyModalVisible"
      :title="$t('global_copy')"
      :confirm-loading="copyConformLoading"
      :width="840"
      :ok-button-props="{ props: { disabled: selectedCopyList.length === 0 } }"
      @ok="handleCopyOk"
      @cancel="handleCopyCancel"
    >
      <a-spin :spinning="copyModalLoading">
        <div class="package_extra_copy--container">
          <a-collapse
            v-if="copyCollapseData.length > 0 && !copyModalLoading"
            v-model="copyCollapseActiveKey"
            :bordered="false"
          >
            <a-collapse-panel
              v-for="pkg in copyCollapseData"
              :key="pkg.key"
              :header="pkg.name"
              style="border: 0"
            >
              <a-config-provider>
                <template #renderEmpty>
                  <div style="text-align: center">
                    <p>{{ $t('global_bts_table_formatNoMatches') }}</p>
                  </div>
                </template>
                <div class="config-provider">
                  <CopyTable
                    :data-source="pkg.data"
                    :selected-row-keys="getSelectedRowKeys(pkg.key)"
                    :all-selected-keys="copyModalSelectedData"
                    :data-key="pkg.key"
                    @change="rowSelectedChange"
                  />
                </div>
              </a-config-provider>
            </a-collapse-panel>
          </a-collapse>
          <p v-else-if="!copyModalLoading">
            {{ $t('other_info_copy_no_data') }}
          </p>
        </div>
      </a-spin>
    </a-modal>

    <MergeOtherInfoDrawer
      v-if="mergeOtherInfoDrawer.visible"
      ref="mergeOtherInfoDrawer"
      :visible.sync="mergeOtherInfoDrawer.visible"
      v-bind="mergeOtherInfoDrawer"
    />
    <!-- 预览 -->
    <previewModal :visible="showPreview" :modal="previewData" @close="showPreview = false" />

    <div id="save_other_info_spm" v-bind="$root.bindIHTrack({ type: 'module', spm: 'SaveOtherInfo' })"></div>
    <div id="pick_up_map_spm" v-bind="$root.bindIHTrack({ type: 'module', spm: 'PickUpMap' })" />
    <div id="save_pick_map_spm" v-bind="$root.bindIHTrack({ type: 'module', spm: 'SavePickUpMap_Click' })" />
  </div>
</template>

<script>
import { getUuid, getEditLang, bestMatchLang, scrollFormErrorIntoView } from '@activity/utils'
import { commonInfoFields } from '@activity/utils/const'
import CommonFooter from '@activity/components/CommonFooter.vue'
import OtherInfoGroup from './OtherInfoGroup.vue'
import BasicOverflowCheckbox from '@activity/pages/components/BasicOverflowCheckbox.vue'
import { getPreviewValiditySync } from '@activity/components/preview-validity'
import { disabledTypeIds } from './utils'
import { mapState, mapGetters, mapMutations } from 'vuex'
import Choice2Group from './Choice2Group'
import TravelerRequiredInfo from './TravelerRequiredInfo'
import MergeOtherInfoDrawer from './MergeOtherInfoDrawer'
import { getMerchantEditSaveSpm } from '@activity/pages/activityManagement/dataLib.vue'
import copyOtherInfoMixin from './mixins/copy-other-info'
import { groupRefFieldTagDict, attrValueKey } from '@activity/pages/package/extra/const.js'
import { initGoogleMaps } from '@activity/components/taxonomy/gmaps.js'

import previewModal from '@activity/pages/package/extra/components/previewModal/index.vue'

export default {
  name: 'MerchantPackageExtra',
  components: {
    TravelerRequiredInfo,
    Choice2Group,
    OtherInfoGroup,
    CommonFooter,
    BasicOverflowCheckbox,
    MergeOtherInfoDrawer,
    previewModal
  },
  mixins: [copyOtherInfoMixin],
  props: {
    needCopyButton: {
      type: Boolean, // attraction需要展示copy button
      default: false
    },
    isGuideFlow: {
      type: Boolean,
      default: false
    },
    copyData: {
      type: Object,
      default: null
    },
    styleType: {
      type: String,
      default: '' // create-guide-type
    },
    activity_id: {
      type: [Number, String],
      required: true
    },
    package_id: {
      type: [Number, String],
      required: true
    },
    isSpuFlow: {
      type: Boolean,
      default: false
    }
  },
  provide() {
    return {
      getMergeOtherInfoDrawer: () => {
        return this.mergeOtherInfoDrawer
      },
      refreshExtraData: async ({ title_id = null } = {}) => {
        this.mergeOtherInfoDrawer.mergeInfo = []
        this.mergeOtherInfoDrawer.currentTitleId = 0
        this.mergeOtherInfoDrawer.currentGroupId = 0

        await this.initData()

        if (title_id) {
          setTimeout(async () => {
            await this.$nextTick()
            const currentEle = document.querySelector(`.other-info-item__form[data-title-id="${title_id}"]`)

            currentEle?.scrollIntoView?.({
              behavior: 'smooth',
              block: 'center'
            })
          }, 60)
        }
      },
      getPackageStatusById: (pId) => {
        return this.packageStatusDict[pId] || {}
      },
      provideData: this.provideData
    }
  },
  inject: [
    'rootChangeLoading',
    'page_from',
    'handleGTMBtn',
    'setAlertFixed2provide',
    'setBeforeEachSaveConfirmInterceptor'
  ],
  data() {
    const submitColumns = [
      {
        title: `${this.$t('main_package')}id`,
        dataIndex: 'id',
        key: 'id'
      },
      {
        title: `${this.$t('main_package')}${this.$t('content_name')}`,
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: this.$t('unfinish_field'),
        dataIndex: 'detail',
        key: 'detail'
      }
    ]

    return {
      mergeOtherInfoDrawer: {
        visible: false,
        package_id: +this.package_id,
        currentGroupId: 0,
        currentTitleId: 0,
        mergeInfo: [],
        isMerged: false
      },

      disabledTypeIds,

      xinfo_lock_info_ids: [],
      modify_restrict_hint: false,
      // new
      isNewOtherInfoLoading: false,
      newCollapseActiveKey: ['contact', 'participants', 'additional', 'postInfo', 't&c', 'general'],
      travellerGroups: [],
      infoGroups: [],
      customizedGroups: [],
      groupCheckedList: [],

      disabledInfo: false,
      standardizedTips: [],

      // old
      isOldOtherInfoLoading: false,
      oldCollapseActiveKey: [],
      oldOtherInfoData: [],
      oldOtherInfoDataCheckList: [],
      oldCustomOtherInfo: [],
      allOldCustomOtherInfo: [],
      allOldOtherInfoIds: [],

      // save
      updateData: {
        info: [],
        traveller: [],
        postInfo: [],
        termAndCondition: [],
        customized: []
      },

      // contact info
      contactInfoOptions: [],
      contactInfoChecked: [],
      pkgOtherInfoConfigCache: {},

      // submit
      submitModalColumns: submitColumns,
      packages_finished: true,
      isSPU: !!this.$root.isSPU,
      // 需要在 method newOtherInfoDataCheckList 里加上
      postInfoGroup: [],
      termAndConditionGroup: [],
      supportNewOtherinfoType: 0, // 0-就是新的都支持，旧的不支持, 1-不支持新的，支持旧的
      exist_mapped_add_on: false,

      provideData: {
        activityDepartureCity: {}
      },
      packageStatusDict: {},

      showPreview: false,
      previewData: {}
    }
  },
  computed: {
    calcSaveDisabled() {
      const { isGlobalDisabled, isNewOtherInfoLoading, isOldOtherInfoLoading } = this
      return isGlobalDisabled || isNewOtherInfoLoading || isOldOtherInfoLoading
    },
    isMerchant() {
      return this.$root.isMerchant
    },
    calcSaveData() {
      const { name } = this.$route
      const spm = getMerchantEditSaveSpm(name)
      if (!spm) {
        return {}
      }
      const obj = {
        'data-spm-module': `${spm}`,
        'data-spm-virtual-item': '__virtual?trg=manual'
      }
      return obj
    },
    calcIsCreateGuide() {
      return this.styleType === 'create-guide-type'
    },
    disabledCustomerOtherInfo() {
      const { approval_status } = this.currentPackageInfo || {}
      return approval_status === 3 && this.$root.isMerchant
    },
    showCopyButton() {
      const pkgInfos = this.pkgInfos || {}
      const packages = pkgInfos.packages || []
      const isComboPackage = this.isComboPackage
      return packages.length > 1 && !isComboPackage
    },
    getCopySpm() {
      const oid = `activity_${this.activity_id || 0}`
      return `NewCopyOtherInfo?oid=${oid}`
    },
    getPageSpm() {
      if (this.isGuideFlow) {
        return
      }
      const oid = `activity_${this.activity_id || 0}`
      return `OtherInfo?oid=${oid}`
    },
    ...mapGetters([
      'isTripMapped',
      'isPublishWithAI',
      'saveBeforeStatus2getters',
      'lockMerchantEditFreeText2getter',
      'activityIsUnablePreview'
    ]),
    ...mapState({
      merchantActHaveBeenApproved: 'merchantActHaveBeenApproved',
      isAdminEm: (state) => state.isAdminEm,
      pkgInfos: 'pkgInfos',
      currentPackageInfo: 'currentPackageInfo',
      lockMerchantActEditing: 'lockMerchantActEditing'
    }),
    productType() {
      return _.get(this.$store.state.currentPackageFloatingField, 'product_type', null)
    },
    isComboPackage() {
      return this.productType === 1
    },
    commonComboAttrs() {
      const flag = this.isComboPackage

      return {
        showAddNewBtn: !flag,
        showDeleteBtn: !flag,
        showInputDeleteBtn: !flag,
        showInputAddNewBtn: !flag,
        disabledCheckbox: flag,
        disabledNeedCheckbox: flag,
        headmost: flag, // combo 情况下需要显示所有的 group name, 而不仅仅是第一个
        disabledDrag: flag
      }
    },
    oldOtherInfoDataCustomData() {
      return _.uniqBy([...this.allOldCustomOtherInfo, ...this.oldCustomOtherInfo], 'extra_info_id')
    },
    allGroups() {
      return [
        ...this.travellerGroups.map((item) => item.groups).flat(),
        ...this.infoGroups,
        ...this.postInfoGroup,
        ...this.termAndConditionGroup,
        ...this.customizedGroups
      ]
    },
    wholeLock() {
      if (this.lockMerchantActEditing) {
        return true
      }

      return (this.modify_restrict_hint || this.isTripMapped) && !this.isNewOtherInfoLoading
    },
    vDisabled() {
      const excludeElements = ['exclude-v-disable']

      if (this.lockMerchantActEditing) {
        return {
          lock: true,
          scope: 'all',
          excludeElements
        }
      }

      if (this.lockMerchantEditFreeText2getter) {
        return {
          lock: true,
          extendSelectorList: ['compound-extend'],
          defaultScope: 'freeText',
          excludeElements
        }
      }

      return {
        lock: false
      }
    },
    canEditCustomOtherInfo() {
      return this.page_from === 'admin'
    },
    categoryId() {
      return _.get(this.$store, 'state.categoryInfo.sub_category_id', null)
    },
    // 未填写完毕的主套餐信息列表
    unfinishPackageList() {
      let { position } = this.$store.state
      return _.get(position, 'unfinish_package_info', [])
    },
    // 是否存在未填写完毕的主套餐信息列表
    hasUnfinishPackage() {
      let { unfinishPackageList } = this
      return unfinishPackageList && unfinishPackageList.length
    },
    unfinishPackageTableData() {
      return this.unfinishPackageList.map((pkg) => ({
        key: pkg.id,
        id: pkg.id,
        name: pkg.name,
        detail: (pkg.detail || []).join('、')
      }))
    },
    newOtherInfoDataCheckList() {
      return this.groupCheckedList.reduce((acc, curr) => {
        const current = _.find(this.allGroups, {
          group_id: curr
        })
        acc.push(current)
        return acc
      }, [])
    },
    oldOtherInfoCategoryLength() {
      return this.oldOtherInfoData.length
    },
    oldOtherInfoDataCheckCategoryLength() {
      return this.oldOtherInfoData.reduce((acc, curr) => {
        const isChecked =
          !!curr.content &&
          curr.content.some((item) => this.oldOtherInfoDataCheckList.includes(item.extra_info_id))
        return isChecked ? acc + 1 : acc
      }, 0)
    },
    oldOtherInfoDataCheckData() {
      const list = []
      const idList = []

      this.oldOtherInfoData.forEach((category) => {
        category.content &&
          category.content.forEach((item) => {
            if (
              this.oldOtherInfoDataCheckList.includes(item.extra_info_id) &&
              !idList.includes(item.extra_info_id)
            ) {
              list.push(item)
              idList.push(item.extra_info_id)
            }
          })
      })

      return list
    },
    isGlobalDisabled() {
      // 编辑锁状态锁配置
      return this.$root.nonEdit2act || this.$root.nonEdit2status
    },
    oldCustomOtherInfoIds() {
      return this.oldCustomOtherInfo.map((item) => item.extra_info_id)
    },
    itemSelectedTxt() {
      return klook.parseStr1(this.$t('73116'), {
        xx: this.mergeOtherInfoDrawer.mergeInfo.length
      })
    }
  },
  watch: {
    calcSaveDisabled: {
      handler(isb) {
        this.$emit('updateSaveDisabled', isb)
      }
    },
    'mergeOtherInfoDrawer.mergeInfo': {
      deep: true,
      handler() {
        this.currentGoToSelectedMerged = 0
      }
    },
    oldOtherInfoDataCheckList: {
      deep: true,
      handler(newV) {
        if (!(this.isOldOtherInfoLoading || this.isNewOtherInfoLoading)) {
          let diff = _.difference(newV, this.allOldOtherInfoIds)

          if (diff.length) {
            this.allOldOtherInfoIds.push(diff[0])
          } else {
            this.$set(
              this,
              'allOldOtherInfoIds',
              this.allOldOtherInfoIds.filter(
                (id) => newV.includes(id) || this.oldCustomOtherInfoIds.includes(id)
              )
            )
          }
        }
      }
    },
    oldOtherInfoCategoryLength() {
      this.sendFloorTimelineData()
    },
    oldOtherInfoDataCheckCategoryLength() {
      this.sendFloorTimelineData()
    },
    oldCustomOtherInfo: {
      deep: true,
      handler() {
        this.sendFloorTimelineData()
      }
    },
    productType: {
      immediate: true,
      handler(v) {
        if (v !== null) {
          this.initData()
        }
      }
    }
  },
  created() {
    if (this.$root.isMerchant) {
      this.setBeforeEachSaveConfirmInterceptor({
        confirmSave: {
          fn: this.handleSave.bind(this)
        }
      })
    }

    if (this.calcIsCreateGuide) {
      klook.bus.$emit('addHandleSave2bus', this.handleSave, { stepCount: this.$route.query.step_count })
    }
  },
  mounted() {
    this.$emit('guideMounted')
  },
  methods: {
    ...mapMutations(['setActivateFooterWrapper']),
    onChangeData() {
      if (this.isNewOtherInfoLoading || this.isOldOtherInfoLoading) return

      this.setActivateFooterWrapper(true)
    },
    async initData() {
      this.sendFloorTimelineData()
      await Promise.all([
        this.initExtraInfo(),
        this.getOldCommonExtraInfo(),
        this.$store.dispatch('actionGetPosition', {
          activityId: this.activity_id
        }),
        this.$store.dispatch('getActCategory2action', {
          activity_id: this.activity_id
        }),
        this.getPackageOtherInfoConfig(),
        this.getAddonStatus()
      ])

      if (
        !this.$store.state.position.main_package
        // @dep only check if main packages is finished
        // || klk_gvars.position.add_on_package === 1
      ) {
        // can't create new when there's unfinished packages, will show a msg instead
        this.packages_finished = false
        /* $("#myModal #hint").show().siblings().hide();*/
      }
    },
    async getAddonStatus() {
      const res = await ajax.get(ADMIN_API.act.package_exist_mapped_add_on, {
        params: {
          package_id: +this.package_id
        }
      })
      this.exist_mapped_add_on = res?.exist_mapped_add_on ?? false
    },
    handleGoToSelectedMerged() {
      const { mergeInfo } = this.mergeOtherInfoDrawer

      if (!mergeInfo.length) return

      // newCollapseActiveKey: ['contact', 'participants', 'additional', 'postInfo', 't&c', 'general'],

      if (
        this.currentGoToSelectedMerged === undefined ||
        this.currentGoToSelectedMerged > mergeInfo.length - 1
      ) {
        this.currentGoToSelectedMerged = 0
      }

      const selected = document.querySelectorAll('.--selected-to-merge.ant-checkbox-wrapper-checked')
      const currentEle = selected[this.currentGoToSelectedMerged]

      if (currentEle) {
        currentEle.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })

        this.currentGoToSelectedMerged++
      }
    },
    async preGetMergedData({ title_id, merge_info }) {
      this.isNewOtherInfoLoading = true

      const response = await ajax.post(ADMIN_API.act.get_package_otherinfos_for_manual_merge, {
        data: {
          package_id: +this.package_id,
          title_id,
          language: getEditLang(),
          merge_info: merge_info.map((pkg) => ({
            package_id: pkg.package_id,
            title_id: pkg.title_id
          }))
        }
      })

      this.isNewOtherInfoLoading = false

      return response
    },
    async handleMergeSelectedItems() {
      const currentTitleId = 0
      const response = await this.preGetMergedData({
        title_id: 0,
        merge_info: this.mergeOtherInfoDrawer.mergeInfo
      })

      if (response) {
        this.$set(this, 'mergeOtherInfoDrawer', {
          ...this.mergeOtherInfoDrawer,
          currentTitleId,
          isMerged: false,
          visible: true,
          originalData: response
        })
      }
    },
    async handleViewMerged(data) {
      const { combo_merge_info, group_id, title_id } = data
      const mergeInfo = combo_merge_info.title_from_combo_info || []

      const response = await this.preGetMergedData({
        title_id,
        merge_info: mergeInfo
      })

      if (response) {
        this.$set(this, 'mergeOtherInfoDrawer', {
          ...this.mergeOtherInfoDrawer,
          visible: true,
          currentGroupId: group_id,
          currentTitleId: title_id,
          mergeInfo,
          isMerged: true,
          originalData: response
        })
      }
    },
    customizedPreview(data) {
      this.previewData = {
        visible: true,
        form: data,
        isPreview: true
      }
      this.showPreview = true
    },
    handleContactInfoChange(event) {
      const { value, checked } = event.target
      // First/Last name (English）与 First/Last name Local languge) 两个选项是互斥的
      this.$nextTick(() => {
        if (checked && value === 'name_en') {
          this.contactInfoChecked = this.contactInfoChecked.filter((item) => item !== 'name_local')
        } else if (checked && value === 'name_local') {
          this.contactInfoChecked = this.contactInfoChecked.filter((item) => item !== 'name_en')
        }
      })

      this.onChangeData()
    },
    async getPackageOtherInfoConfig() {
      const contactConfig = {
        title: this.$t('29147'),
        country: this.$t('other_info_country_region'),
        phone_number: this.$t('other_info_phone_number'),
        mail_box: this.$t('other_info_email'),
        name_en: this.$t('29148'),
        name_local: this.$t('29149')
      }
      const res = await ajax.get(ADMIN_API.act.get_package_contact_config_v2, {
        params: {
          package_id: +this.package_id
        }
      })
      let contactInfoOptions = []
      let contactInfoChecked = []
      if (res) {
        for (let key in contactConfig) {
          let item = res[key]
          if (!item) {
            continue
          }
          if (item.value === 1) {
            contactInfoChecked.push(key)
          }
          contactInfoOptions.push({
            label: contactConfig[key],
            value: key,
            disabled: item.modifiable === 1 ? false : true
          })
        }
      }

      let nameChecked = res.first_name.value === 1 && res.last_name.value === 1
      // 先判断 First/Last name (English / Local languge) 选中状态，然后判断选中的是哪一个
      if (nameChecked) {
        if (res.name_restrict_english.value === 1) {
          contactInfoChecked.push('name_en')
        } else {
          contactInfoChecked.push('name_local')
        }
      }
      // 往列表追加 First/Last name (English / Local languge)
      // modifiable 都为 1 为可编辑
      let nameDisabled = res.first_name.modifiable === 1 && res.last_name.modifiable === 1 ? false : true
      contactInfoOptions.splice(
        1,
        0,
        {
          label: contactConfig.name_en,
          value: 'name_en',
          disabled: nameDisabled
        },
        {
          label: contactConfig.name_local,
          value: 'name_local',
          disabled: nameDisabled
        }
      )
      this.contactInfoOptions = contactInfoOptions
      this.contactInfoChecked = contactInfoChecked
      this.pkgOtherInfoConfigCache = res
    },
    async saveContactInfo() {
      let { contactInfoChecked = [], pkgOtherInfoConfigCache = {} } = this
      const flag_name = contactInfoChecked.includes('name_local') || contactInfoChecked.includes('name_en')
      let data = {
        package_id: +this.package_id,
        flag_title: contactInfoChecked.includes('title') ? 1 : 0,
        flag_english: contactInfoChecked.includes('name_en') ? 1 : 0,
        flag_lastname: flag_name ? 1 : 0,
        flag_firstname: flag_name ? 1 : 0,
        flag_mailbox: contactInfoChecked.includes('mail_box') ? 1 : 0,
        flag_country: contactInfoChecked.includes('country') ? 1 : 0,
        flag_country_code: pkgOtherInfoConfigCache.country_code?.value ?? 0,
        flag_phone_number: contactInfoChecked.includes('phone_number') ? 1 : 0
      }
      const res = await ajax.postBody(ADMIN_API.act.update_package_contact_config_v2, {
        data: data
      })
      return res
    },
    async saveOldOtherInfoIds() {
      await ajax.postBody(ADMIN_API.act.bind_package_old_otherinfos, {
        data: {
          package_id: this.package_id,
          old_otherinfo_id_list: this.allOldOtherInfoIds
        }
      })

      this.$message.success(this.$t('global_success'))
    },
    async handleDelOldCustomOtherInfo(delId) {
      this.$set(
        this,
        'allOldOtherInfoIds',
        this.allOldOtherInfoIds.filter((item) => item !== delId)
      )

      await this.saveOldOtherInfoIds()
    },
    async handleAddNewOldCustomOtherInfo(newId) {
      this.allOldOtherInfoIds.push(newId)

      await this.saveOldOtherInfoIds()
      await this.handleCheckForm()
    },
    initReplaceData(target, copyData) {
      for (let k in copyData) {
        switch (k) {
          case 'new_styles':
            copyData[k].forEach((o, i) => {
              let hasGroups = []
              const values = o.instance_datas?.group_id_list
              if (values?.length) {
                const targetGroups = target?.[k]?.[i]?.instance_datas?.groups
                if (targetGroups?.length) {
                  hasGroups = values.filter((gid) => {
                    return targetGroups.find((item) => item.group_id === gid)
                  })
                }
                if (hasGroups?.length) {
                  hasGroups.forEach((gid) => {
                    const copyItem = o.instance_datas?.groups?.find((item) => item.group_id === gid)
                    if (copyItem) {
                      const idx = targetGroups.findIndex((item) => item.group_id === gid)
                      idx !== -1 && this.$set(targetGroups, idx, copyItem)
                    }
                  })
                  const targetInstanceData = target?.[k]?.[i]?.instance_datas || {}
                  const arr = targetInstanceData?.group_id_list || []
                  arr.push(...hasGroups.filter((gid) => !arr?.includes(gid)))
                  this.$set(targetInstanceData, 'group_id_list', arr)
                }
              }
            })
            break
          default:
            break
        }
      }
    },
    async initExtraInfo(hideLoading) {
      this.isNewOtherInfoLoading = !hideLoading

      let params = {
        package_id: this.package_id,
        language: getEditLang(),
        check_combo: 1
      }

      if (klook.getPlatformRoleKey() === 'admin') {
        params.ref_language = this.$route.query.ref || ''
      }

      let data

      if (this.isComboPackage) {
        data = await ajax.get(ADMIN_API.act.get_combo_package_otherinfos, {
          params
        })
      } else {
        data = await ajax.get(ADMIN_API.act.get_package_otherinfos, {
          params
        })
      }

      // if (this.copyData && data) {
      //   this.initReplaceData(data, this.copyData)
      // }

      if (data) {
        await this.initNewOtherInfoForm(data.new_styles)
        this.initPackageStatus(data.new_styles)

        this.updateDisabledStatus(data.support_new_otherinfo)
        const old_styles = data.old_styles || []
        let oldOtherInfo = old_styles.filter((item) => item.package_id === 0)
        this.initOldOtherInfoCheckList(oldOtherInfo)
        // wangliu、tianlong、sky后端要这么改
        let filterIdResult = []
        if (this.$root.isAdmin) {
          filterIdResult = (await ajax.get(ADMIN_API.act.get_all_common_other_info_ids)) || []
        }
        this.oldCustomOtherInfo = old_styles.filter((item) => !filterIdResult.includes(item.extra_info_id))
        this.allOldOtherInfoIds = old_styles.map((item) => item.extra_info_id)
        this.allOldCustomOtherInfo = old_styles
        this.supportNewOtherinfoType = _.get(data, 'support_new_otherinfo.support_new_otherinfo_type', 0)
        this.provideData.activityDepartureCity = data.activity_departure_city

        if (this.canEditCustomOtherInfo) {
          await this.getProtectOtherInfo()
        }

        await this.$nextTick()
        this.cachePostData = _.cloneDeep(this.getPostData())
      }

      this.isNewOtherInfoLoading = false
    },
    initPackageStatus(styles) {
      let data = {}
      for (let style of styles) {
        const { package_id, package_status } = style
        data[package_id] = package_status
      }

      this.packageStatusDict = data
    },
    async getProtectOtherInfo() {
      const res_protect_v2 = await ajax.get(ADMIN_API.act.res_protect_v2, {
        params: {
          act_id: +this.activity_id
        }
      })

      let package_lock_lang = []
      if (this.package_id) {
        let temp = res_protect_v2
          .find((v) => v.level === 'PACKAGE')
          .details.find((v) => v.res_id === this.package_id)

        package_lock_lang = (temp && temp.res_language) || []
      }
      let act_lock_lang = res_protect_v2.find((v) => v.level === 'ACTIVITY').details[0].res_language

      let restricted = act_lock_lang
      if (!act_lock_lang.length && this.package_id) {
        restricted = package_lock_lang
      }

      if (restricted.length) {
        this.modify_restrict_hint = true
      } else {
        this.modify_restrict_hint = false
      }

      const oldStyleInfo =
        (
          await ajax.get(ADMIN_API.act.get_package_otherinfos_old_style, {
            params: {
              package_id: +this.package_id,
              language: getEditLang()
            }
          })
        ).info || []

      if (oldStyleInfo.length) {
        let xinfo_lock_info = _.get(
          res_protect_v2.find((v) => v.level === 'OTHERINFO'),
          'details',
          []
        ).filter((item) => item.res_language.length)

        this.xinfo_lock_info_ids = xinfo_lock_info.map((item) => item.res_id)
      }
    },
    initOldOtherInfoCheckList(data) {
      this.oldOtherInfoDataCheckList = data.map((item) => item.extra_info_id)
    },
    updateDisabledStatus(data) {
      const support_new_otherinfo = _.get(data, 'support_new_otherinfo', true)
      this.disabledInfo = !support_new_otherinfo

      let { is_api, is_combo_package, identity_types, is_combo_alone_package, combo_info } =
        data.package_status
      let tips = []

      if (is_api && !support_new_otherinfo) {
        tips.push(this.$t('other_info_standardized_tips_act_api'))
      }

      if (is_combo_package) {
        tips.push(this.$t('other_info_standardized_tips_act_combo_main'))
      } else if (is_combo_alone_package) {
        let tip = ''
        let { combo_activity_id, combo_package_id } = combo_info
        if (KLK_LANG === 'en') {
          tip = klook.strformat(
            this.$t('other_info_standardized_tips_act_combo_temporarily'),
            combo_package_id,
            combo_activity_id
          )
        } else {
          tip = klook.strformat(
            this.$t('other_info_standardized_tips_act_combo_temporarily'),
            combo_activity_id,
            combo_package_id
          )
        }
        tips.push(tip)
      }

      if (identity_types.length) {
        tips.push(this.$t('other_info_standardized_tips_act_identity'))
      }

      this.standardizedTips = tips
    },
    async initNewOtherInfoForm(data) {
      // traveller other info, In the form of a 2D array
      // compatible with combo(multi pkg) or single pkg info
      let travellerGroups = []

      for (let curr of data) {
        const { package_id, package_name } = curr
        let { group_id_list, groups } = curr.instance_datas
        groups = groups || []
        group_id_list = group_id_list || []
        groups = this.fmtGroupData(groups)

        // product_type === 1 combo package
        if (!this.isComboPackage || curr.product_type === 1) {
          let allGroupIds = groups.map((group) => group.group_id)
          for (let v of _.chunk(allGroupIds, 50).values()) {
            await this.$store.dispatch('actionGetGroupItemByIds', {
              group_id_list: v
            })
            await this.$store.dispatch('actionGetTitleListByIds', {
              group_id_list: v,
              custom_params: this.isComboPackage
                ? {
                    package_id: +this.package_id
                  }
                : {}
            })
          }

          // 公共的 group checked 数据
          this.groupCheckedList = group_id_list || []

          if (!this.isComboPackage) {
            travellerGroups = [
              {
                groups: groups.filter((item) => item.info_type === 1),
                checked: this.groupCheckedList
              }
            ]
          }

          this.customizedGroups = groups.filter((item) => {
            if (this.$root.isMerchant) {
              return item.info_type < -1
            }
            return item.info_type < 0
          }) // general other info
          this.allCustomizedKey = this.customizedGroups.map((item) => {
            return item.group_id
          })

          // 附加信息
          this.infoGroups = groups.filter((item) => {
            const types = [0, -1]
            return types.includes(item.info_type)
          }) // otherinfo
          this.termAndConditionGroup = groups.filter((item) => item.info_type === 3) // T & C
          this.postInfoGroup = groups.filter((item) => item.info_type === 2) // mail info
        } else {
          travellerGroups.push({
            package_id,
            package_name,
            groups: groups,
            checked: group_id_list
          })
        }
      }

      this.$set(this, 'travellerGroups', travellerGroups)
    },
    fmtGroupData(groups) {
      return (groups || []).map((group) => {
        group.style.fields = group.style.fields.map((field) => {
          switch (field.options.type) {
            case 6: {
              !field.value &&
                (field.value = field.options.default || {
                  id: 0,
                  content: ''
                })
              break
            }
            case 7: {
              !field.value &&
                (field.value = field.options.default || {
                  min: '',
                  max: ''
                })
              break
            }
            default: {
              !field.value && (field.value = field.options.default || '')
            }
          }
          return field
        })

        return {
          ...group,
          uuid: group.uuid || getUuid()
        }
      })
    },
    async getOldCommonExtraInfo(hideLoading) {
      this.isOldOtherInfoLoading = !hideLoading

      const data = await ajax.get(ADMIN_API.act.get_old_common_otherinfos, {
        params: {
          package_id: +this.package_id,
          language: klook.getAPILang()
        }
      })

      if (data) {
        this.oldOtherInfoData = data.info
      }

      this.isOldOtherInfoLoading = false
    },
    getCollapseTitle(id) {
      return commonInfoFields[id]
    },
    changeGroupCheckedList({ data, traveller }) {
      traveller.checked = data
      this.$set(this, 'groupCheckedList', [...new Set([...this.groupCheckedList, ...data])])
    },
    handleUpdateTravellerData(data, index) {
      this.updateData.traveller.splice(index, 1, data)
      this.sendFloorTimelineData()
    },
    handelUpdateData(data, type) {
      this.updateData[type] = data
      this.sendFloorTimelineData()
    },
    handleCollapseCardClick(id) {
      if (this.isGlobalDisabled || this.isComboPackage || this.exist_mapped_add_on) {
        return
      }

      const index = this.oldOtherInfoDataCheckList.indexOf(id)

      if (index === -1) {
        this.oldOtherInfoDataCheckList.push(id)
      } else {
        this.oldOtherInfoDataCheckList.splice(index, 1)
      }
    },
    handleCheckboxChange(item) {
      const { value } = item.target
      this.handleCollapseCardClick(value)
    },
    getCollapseCardTitle(item) {
      return `${item.extra_info_id} - ${bestMatchLang('name', 'language', item.details)}`
    },
    getCollapseCardHint(item) {
      return bestMatchLang('hint', 'language', item.details)
    },
    getDraggableTitle(id) {
      let allOldOtherInfo = _.uniqBy(
        [...this.allOldCustomOtherInfo, ...this.oldCustomOtherInfo],
        'extra_info_id'
      )

      let res = _.find(allOldOtherInfo, {
        extra_info_id: id
      })
      if (res) {
        return `${res.extra_info_id} - ${bestMatchLang('name', 'language', res.info)}`
      }

      res = _.find(this.oldOtherInfoDataCheckData, {
        extra_info_id: id
      })
      return res ? this.getCollapseCardTitle(res) : ''
    },
    handlePreview() {
      getPreviewValiditySync(this.activity_id, this.$i18n, (date) => {
        if (date) {
          let url = `${klook.getUrlByEnv('')}/en-US/preview/activity/${
            this.activity_id
          }/?lang=${getEditLang()}&deadline_version=1&deadline=${encodeURIComponent(date)}`
          window.open(url)
        }
      })
    },
    async handleSave() {
      return await this.handleCheckForm()
    },
    async handleCheckForm() {
      let groups = [this.$refs.infoGroups]
      const TravelerRequiredInfo = this.$refs.TravelerRequiredInfo || []

      TravelerRequiredInfo.forEach((group) => {
        if (group.travelerInfoType !== -1) {
          groups.push(group)
        }
      })

      if (this.$refs?.postInfoGroup?.need) {
        groups.push(this.$refs.postInfoGroup)
      }

      if (this.$refs?.termAndConditionGroup?.need) {
        groups.push(this.$refs.termAndConditionGroup)
      }

      // custom
      if (this.$refs.customizedGroups) {
        groups.push(this.$refs.customizedGroups)
      }

      let valid = await Promise.all([
        ...groups.map(async (item) => item.validateForm()),
        this.canEditCustomOtherInfo ? this.$refs.customOtherInfo.validateForm() : true
      ])

      if (valid.some((item) => !item)) {
        scrollFormErrorIntoView()
        return
      }

      return await this.saveData()
    },
    async saveStepStatus() {
      if (this.isSpuFlow) {
        this.$emit('updateStatus')
        return
      }

      if (this.calcIsCreateGuide) {
        klook.bus.$emit('setStepStatusFinished2bus', this.$route.query.step_count, {
          package_id: this.package_id
        })
      } else {
        // 上传保存状态
        await this.$store.dispatch('updatePkgStepStatus2action', {
          activity_id: this.activity_id,
          package_id: this.package_id,
          language: getEditLang(),
          status: 1,
          step: 'extra_information'
        })
      }
      klook.bus.$emit('updatePkgInfos2bus')
    },
    getPostData() {
      return {
        activity_id: this.activity_id,
        package_id: this.package_id,
        language: getEditLang(),
        common_otherinfo_ids: this.$refs?.frontPreview?.getCommonOtherInfoIds?.() || [],
        groups: _.flatMapDeep(this.updateData).reduce((acc, curr) => {
          if (!this.groupCheckedList.includes(curr.group_id)) return acc

          if (this.isComboPackage) {
            const is_merged = _.get(curr, 'combo_merge_info.is_merged', 0)

            if (!is_merged) {
              return acc
            }
          }

          return [
            ...acc,
            {
              group_id: curr.group_id,
              title_id: curr.title_id,
              fields: curr.fields,
              ...(curr.info_type === 1 // traveller
                ? {
                    write_level: curr.write_level
                  }
                : {})
            }
          ]
        }, [])
      }
    },
    getDiffSaveData(data, cache) {
      const allData = _.flatMapDeep(this.updateData)
      let result = {}
      let titleIds = []

      try {
        for (let group of data.groups) {
          const { group_id, fields: updateFields, title_id } = group
          const { group_name, style } = _.find(allData, {
            group_id
          })
          const cacheGroupData = _.find(cache.groups, {
            group_id
          })
          const cacheFieldsData = _.get(cacheGroupData, 'fields', [])
          const groupOriginalFieldsConfig = style.fields

          if (!result[group_id]) {
            result[group_id] = {
              group_id,
              title_id,
              group_name,
              add: [],
              update: [],
              remove: []
            }
          }

          titleIds.push(title_id)

          for (let field of updateFields) {
            let { key: updateFieldKey, value: updateFieldValue } = field
            const originFieldConfig = _.find(groupOriginalFieldsConfig, {
              key: updateFieldKey
            })
            const recordData = {
              name: originFieldConfig.name,
              value: updateFieldValue
            }
            const cacheFieldData = _.find(cacheFieldsData, { key: updateFieldKey })

            if (cacheFieldData) {
              if (!_.isEqual(cacheFieldData.value, updateFieldValue)) {
                result[group_id].update.push(recordData)
              }
            } else {
              result[group_id].add.push(recordData)
            }
          }
        }
      } catch (e) {
        console.error('Diff other info: ', e)
      }

      cache.groups.forEach((cacheGroup) => {
        const { title_id: cacheTitleId, group_id: cacheGroupId } = cacheGroup

        if (!titleIds.includes(cacheTitleId)) {
          const { group_name: cacheGroupName } = _.find(this.allGroups, {
            title_id: cacheTitleId
          })

          if (!result[cacheGroupId]) {
            result[cacheGroupId] = {
              group_id: cacheGroupId,
              title_id: cacheTitleId,
              group_name: cacheGroupName,
              add: [],
              update: [],
              remove: []
            }
          }

          result[cacheGroupId].remove.push({
            name: `${cacheTitleId}-${cacheGroupName}`
          })
        }
      })

      return result
    },
    // use the template ?
    getDiffText(data, cache) {
      // 商户端没有combo
      if (this.$root.isMerchant) {
        return ''
      }
      const result = this.getDiffSaveData(data, cache)

      const message = Object.values(result).reduce((acc, curr) => {
        const { group_id, group_name, add, update, remove } = curr
        if (add.length || update.length || remove.length) {
          let msg = [`<h3>${group_id} - ${group_name}</h3>`]

          if (add.length) {
            msg.push(`
              <p>Add: ${add.map((item) => item.name).join(', ')} </p>
            `)
          }

          if (update.length) {
            msg.push(`
              <p>Updated: ${update.map((item) => item.name).join(', ')} </p>
            `)
          }

          if (remove.length) {
            msg.push(`
              <p>Deleted: ${remove.map((item) => item.name).join(', ')} </p>
            `)
          }

          return [...acc, msg.join('')]
        }

        return acc
      }, [])

      if (message.length) {
        return `
          <div>
            ${message.join('')}
          </div>
        `
      }

      return ''
    },
    async saveData() {
      this.rootChangeLoading(true)

      const data = this.getPostData()

      const diffText = this.getDiffText(data, this.cachePostData)

      if (diffText) {
        ajax.post(ADMIN_API.act.send_standalone_package_otherinfos_for_combo, {
          data: {
            package_id: +this.package_id,
            msg: diffText
          }
        })
      }

      const resArr = await Promise.all([
        ajax.postBody(ADMIN_API.act.save_package_otherinfos, {
          noDefaultResponseInterceptor: true, // 获取error.message
          data
        }),
        this.saveContactInfo()
      ])

      const isb = resArr[0].success && resArr[1].success
      const extObj = {
        save_type: this.saveBeforeStatus2getters ? 1 : 0,
        err_message: isb ? '' : resArr[0]?.error?.message || 'Error: false'
      }

      const el = this.calcIsCreateGuide ? '.js-guide-create-save-btn' : '.js-edit-save-btn'
      this.$root.trackIHEvent(el, extObj)

      const isSuccess = resArr[0].success && resArr[1].success
      if (isSuccess) {
        await this.handleGTMBtn()
        await this.trackSaveSpm()
        // 这里为了不出现双层loading
        await Promise.all([this.saveStepStatus(), this.initExtraInfo(true)])
        this.$message.success(this.$t('global_success'))
        this.setActivateFooterWrapper(false)

        setTimeout(() => {
          // hack 修复复用时 runtime 把地图 div 重置问题
          klook.bus.$emit('refreshPickUpMap')
        }, 600)

        if (this.calcIsCreateGuide) {
          this.rootChangeLoading(false)
          this.$emit('successSave')
          return true
        }

        // aid
        if (isSuccess) {
          this.rootChangeLoading(false)
          return {
            success: true // 抛给调用者
          }
        }
      } else {
        this.$root.trackIHEvent('#save_other_info_spm', {
          err_message: resArr[0]?.error?.message ?? 'SaveOtherInfo Error'
        })
      }

      this.rootChangeLoading(false)
      this.$set(this, 'mergeOtherInfoDrawer', this.$options.data.call(this).mergeOtherInfoDrawer)
    },
    async trackSaveSpm() {
      for (let group of _.flatMapDeep(this.updateData)) {
        if (
          this.groupCheckedList.includes(group.group_id) &&
          group.ref_field_tag === groupRefFieldTagDict.pick_up_location_scope
        ) {
          let data = {}
          const inputType = group.fields.find((field) => field.key === 'input_type')?.value ?? null
          if (inputType === 'scope') {
            const curr = group.fields.find((field) =>
              ['selection_district', attrValueKey.customized_attr_value].includes(field.key)
            )
            data.area_quantity = curr.value.length
            data.pickup_type = 0
          } else if (inputType === 'location') {
            const curr = group.fields.find((field) =>
              ['selection_point', attrValueKey.customized_attr_value].includes(field.key)
            )
            data.area_quantity = curr.value.length
            data.pickup_type = 1
          } else {
            data.pickup_type = 2
          }

          await this.savePickUpMapTrack(data)
        }
      }
    },
    async savePickUpMapTrack(ext) {
      const query = {
        ext
      }
      const dom = document.getElementById('save_pick_map_spm')
      dom && (await this.$tracker.updateBinding(dom, query))
      this.$tracker.track('action', '#save_pick_map_spm')
    },
    async handleCreatePackage() {
      await this.handleGTMBtn({
        isBtnSave: false,
        event: 'trackEventCustom',
        eventAction: 'Create Another Package'
      })
      this.$router.push({
        name: 'packageBasicInfo',
        params: { id: this.activity_id },
        query: {
          ...this.$route.query,
          package_id: ''
        }
      })
    },
    handleOverviewPackage() {
      // TODO: 需要先迁移套餐的overview页面
    },
    getUnfinishPackageDetail(detail) {
      detail = detail || []
      return detail.join('、')
    },
    sendFloorTimelineData: _.debounce(function () {
      let floorData = {
        list: [
          {
            title: this.$t('83902'),
            require: true,
            num: this.updateData.info.length === 0 ? 2 : 3,
            count: 3,
            requireNum: 1,
            requireCount: 1
          }
        ]
      }
      const list = floorData?.list || []
      const isb = list.every((o) => o.requireNum >= o.requireCount)
      this.$emit('updateFinished', isb)
    }),
    onSelectGroupOption({ group }) {
      if (group.ref_field_tag === groupRefFieldTagDict.pick_up_location_scope) {
        this.$tracker.track('action', '#pick_up_map_spm')
        initGoogleMaps()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.create-guide-type {
  .package_extra--container {
    padding: 0;
    margin: 0;
  }
  .package_extra--section {
    padding: 0;
  }
}
.merchant-guide-header {
  font-size: 20px;
  line-height: 26px;
  font-weight: 600;
  color: #000;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.package_extra--section {
  .package_extra-block .collapse-card {
    padding: 0;
  }
  .fields-box {
    margin: 16px 0 0 0;
  }
}

.package_extra {
  &--container {
    padding: 0 0 48px;
    margin-bottom: 20px;
  }

  &--section {
    margin-top: 12px;
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;

    &:first-child {
      margin-top: 0;
    }

    .standardized-tips {
      margin-bottom: 12px;
      font-style: italic;
      font-size: 14px;
      color: rgba(255, 0, 0, 0.7);
    }
  }

  &-title_wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &-block {
    margin-top: 16px;

    &_title {
      position: relative;
      font-size: 14px;
      line-height: 20px;
      padding: 6px 0;
      color: rgba(0, 0, 0, 0.85);

      &.is-required {
        margin-left: 10px;

        &:before {
          content: '*';
          position: absolute;
          left: -10px;
          top: 6px;
          color: #e64340;
        }
      }
    }

    &_tips {
      margin-top: 6px;
      line-height: 16px;
      font-size: 12px;
      font-weight: normal;
      color: #212121;
      opacity: 0.65;
    }

    &_content {
      width: 100%;
    }

    .content-other-info {
      padding: 14px;
      background-color: #f5f5f5;

      &.triangle {
        position: relative;
        padding: 20px 18px;
        margin-top: 6px;
        background: #f5f5f5;
        color: #757575;
        &:before {
          content: ' ';
          position: absolute;
          top: -8px;
          left: var(--offsetX);
          display: block;
          width: 0;
          height: 0;
          border-color: transparent;
          border-style: solid;
          border-width: 8px;
          border-top-width: 0;
          border-bottom-color: whitesmoke;
          transition: all 0.3s;
        }
      }
    }

    .ant-collapse-content-box {
      background-color: #fff;
    }

    .ant-collapse-item {
      background-color: #fff;

      &:hover {
        background-color: #fafafa;
      }

      &.ant-collapse-item-active {
        background-color: #fafafa;
      }
    }

    .collapse-card {
      padding: 12px 0 0 6px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: #fff;

      &-wrap {
        margin-left: 4px;
        width: 100%;
        padding: 8px;
        background-color: #fafafa;
        border-radius: 4px;
      }

      &-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      &-title {
        font-weight: 600;
        font-size: 14px;
        line-height: 18px;
      }

      &-type {
        font-size: 12px;
        line-height: 18px;
      }

      &-tips {
        margin-top: 4px;
        font-size: 12px;
        line-height: 12px;
        max-width: 380px;
        font-weight: normal;
        color: #000;
        opacity: 0.65;

        // 只展示3行，多了用省略号
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
      }
    }
  }

  &-draggable {
    margin: 12px 0;

    .list-group-item {
      width: 100%;
      max-width: 600px;
      margin-top: 4px;
      padding: 8px 12px;
      background-color: #fafafa;

      &:first-child {
        margin-top: 0;
      }
    }

    &_content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>

<style lang="scss">
// 不知道谁引入了 margin-bottom: 1em；这里需要覆盖一下
p {
  margin: 0;
}

.top-alert {
  margin-bottom: 4px;
}

.package_extra_copy--container {
  min-height: 50px;

  .ant-table-selection-column,
  .copy-column {
    background-color: #fff !important;
  }

  .ant-collapse-content-box {
    background-color: #fff;
  }

  .ant-collapse-item {
    background-color: #fff;

    &:hover {
      background-color: #fafafa;
    }

    &.ant-collapse-item-active {
      background-color: #fafafa;
    }
  }

  .ant-table-thead {
    background-color: #fff;
  }
}

.restrict-hint {
  padding: 12px;
  clear: both;
  border-left-width: 5px;
  border-left-style: solid;
  padding-left: 30px;
  background-color: #ffe7e8;
  border-color: #e66465;
}

.package_extra_submit--container {
  .ant-btn-link {
    padding: 0;
  }
}

.merge-selected-items-operate {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.copy-btn:disabled {
  background-color: #f5f5f5 !important;
}

#package-extra-editable {
  .ant-form-horizontal .ant-form-item-label {
    line-height: 22px;
  }
}
</style>
