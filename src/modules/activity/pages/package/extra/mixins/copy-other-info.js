import { getUuid, getEditLang, bestMatchLang, pmsConfirm } from '@activity/utils'
import { otherInfoType, copyOtherInfoType } from '../utils'
import { PAGE_LEVEL_DICT } from '~src/modules/aidRevamp/utils/const.js'

import CopyTable from '../components/CopyTable.vue'
import CopyForm from '../components/copy-form'

export default {
  inject: ['reloadPage2provide'],
  components: {
    CopyTable,
    CopyForm
  },
  props: {
    isSpuFlow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      copyModalVisible: false,
      copyModalLoading: false,
      copyConformLoading: false,
      copyCollapseActiveKey: [],
      copyCollapseData: [],
      copyModalSelectedData: {},

      infoType: '-1,-2',
      allPackages: [],
      showCopyForm: false,
      targets: [],
      copyType: undefined,

      currentPackageId: null,
      isCustomizedCopy: false
    }
  },
  computed: {
    options() {
      return this.allPackages.filter((pkg) => pkg.package_id !== this.package_id)
    },
    isCopyTo() {
      return this.copyType === 1
    },
    selectedCopyList() {
      const list = []
      const copyModalSelectedData = this.copyModalSelectedData
      Object.keys(copyModalSelectedData).forEach((item) => {
        return list.push(...copyModalSelectedData[item])
      })
      return list
    }
  },
  methods: {
    async handleCopy() {
      let confirm = true
      const needConfirm = this.diffCustomizedData()
      if (needConfirm) {
        confirm = await pmsConfirm.call(this, {
          title: this.$t('78221'),
          okText: this.$t('78232'),
          cancelText: this.$t('78223')
        })
      }
      if (!confirm) {
        return
      }
      this.showCopyForm = true
      this.getAllPackages()
    },
    async handleCopyOk() {
      const { selectedCopyList, groupCheckedList } = this
      // 将copy选中的 "Customized other info" 过滤出去只留下 common other info
      const selectedCommonOtherinfo = []
      const groupName = []
      const groupIds = []
      this.copyCollapseData.forEach((item) => {
        const data = item.data || []
        data.forEach((it) => {
          if (selectedCopyList.includes(it.title_id)) {
            groupIds.push(it.group_id)
          }
        })
      })
      this.customizedGroups.forEach((item) => {
        const itemSelected = groupIds.some((it) => it === item.group_id)
        if (item.info_type != -1 && itemSelected && groupCheckedList.includes(item.group_id)) {
          selectedCommonOtherinfo.push(item.group_id)
          groupName.push(item.group_name)
        }
      })
      // 如果copy选中的common other info与当前套餐选中的common other info重复，则弹窗提示
      const needComfirm = selectedCommonOtherinfo.length
      let confirm = true
      if (needComfirm && !this.isCopyTo) {
        confirm = await pmsConfirm.call(this, {
          title: 'Are you sure to overwrite ?',
          okText: this.$t('47526'),
          content: this.$t('47525', {
            common_other_info_name_1_common_other_info_name_2: `"${groupName.join('/')}"`
          })
        })
      }

      if (!confirm) {
        return
      }
      const data = this.getCopyFormData()
      this.copyConformLoading = true
      const res = await ajax.postBody(ADMIN_API.act.copy_otherinfos_by_package, {
        data
      })
      this.copyConformLoading = false
      if (res.success) {
        this.copyModalVisible = false
        const result = res?.result ?? []
        const { failed, success } = result.reduce(
          (acc, curr) => {
            const success = curr.success
            if (success) {
              acc.success.push(curr)
            } else {
              acc.failed.push(curr)
            }
            return acc
          },
          { success: [], failed: [] }
        )
        if (failed.length) {
          if (this.isCopyTo) {
            this.$warning({
              title: this.$t('act_copy_to_other_pkg'),
              content: this.$t('104035', {
                package_id: `"${failed.map((item) => item.package_id).join('/')}"`
              })
            })
          }
        } else {
          if (this.isCopyTo) {
            this.$message.success(this.$t('104034', { number: success.length }))
          } else {
            this.$message.success(this.$t('global_copy_success'))
            this.reloadPage2provide()
          }
        }
      } else {
        this.$message.error(this.$t('global_copy_error'))
      }
    },
    diffCustomizedData() {
      const { cachePostData = {} } = this
      const groups = cachePostData.groups || []
      const currentCustomizedGroups = _.flattenDeep(Object.values(this.updateData))
      const customizedGroups = groups
      if (currentCustomizedGroups.length != customizedGroups.length) {
        return true
      }
      let needConfirm = false
      for (const data of customizedGroups) {
        const currentData = currentCustomizedGroups.find((item) => item.title_id === data.title_id)
        if (!currentData) {
          needConfirm = true
          break
        }
        const isDiff = this.diffField(_.get(data, 'fields', []), _.get(currentData, 'fields', []))

        if (isDiff) {
          needConfirm = true
          break
        }
      }
      return needConfirm
    },
    diffField(data, currentData) {
      let isDiff = false
      for (const field of data) {
        const currentField = currentData.find((item) => field.key === item.key)
        if (!currentField) {
          isDiff = true
          break
        }
        // 不去比较uuid
        let isEqual = _.isEqualWith(field.value, currentField.value, this.deleteUuid)
        if (!isEqual) {
          isDiff = true
          break
        }
      }
      return isDiff
    },
    deleteUuid(objValue, othValue) {
      const cloneFieldValue = _.cloneDeep(objValue)
      const cloneCurrentFieldValue = _.cloneDeep(othValue)
      if (_.isArray(cloneFieldValue)) {
        const mapField = cloneFieldValue.map((item) => {
          delete item.uuid
          return item
        })
        const mapCurrentField = cloneCurrentFieldValue.map((item) => {
          delete item.uuid
          return item
        })
        return _.isEqual(mapField, mapCurrentField)
      }
      return _.isEqual(cloneFieldValue, cloneCurrentFieldValue)
    },
    handleCopyCancel() {
      this.copyModalVisible = false
      this.resetCopyModalSelectedData()
    },
    resetCopyModalSelectedData() {
      Object.keys(this.copyModalSelectedData).forEach((item) => (this.copyModalSelectedData[item] = []))
      this.copyCollapseData = []
    },
    getSelectedRowKeys(id) {
      const copyModalSelectedData = this.copyModalSelectedData
      const keys = copyModalSelectedData[id] || []
      return keys
    },
    rowSelectedChange(data) {
      const { id, keys } = data
      this.currentPackageId = keys.length ? id : null
      this.copyModalSelectedData[id] = keys
      // 必须重新赋值，并且修改数据，才能触发 a-table getCheckboxProps 方法。
      this.copyCollapseData = this.copyCollapseData.map((item) => {
        item.data = _.cloneDeep(item.data)
        return item
      })
    },
    async handleFormOk() {
      const copyForm = this.$refs.copyForm
      const validate = await copyForm.validateForm()
      if (!validate) {
        return
      }
      const { type, value, infoType } = copyForm.getData()
      this.infoType = copyOtherInfoType[infoType]
      this.targets = value
      this.copyType = type
      this.isCustomizedCopy = infoType == 1
      this.showCopyForm = false
      this.copyModalVisible = true
      this.getCollapseData()
    },
    getOtherInfosData(list = [], item, isChild = false) {
      return list.map((info, index) => {
        const fields = info.style?.fields ?? []
        const fieldsObject = fields.reduce((acc, curr) => {
          return {
            ...acc,
            [curr.key]: curr.value
          }
        }, {})
        const customized_title_name = fieldsObject.customized_title_name || {}
        const name =
          getEditLang() === 'en_US' ? customized_title_name.content_en : customized_title_name.content
        const is_required = fieldsObject.is_required || []
        const mandatory = is_required.includes('optional') ? this.$t('global_no') : this.$t('global_yes')
        const write_level = info.write_level
        const rules =
          write_level === 0 ? this.$t('package_xinfo_ask_once') : this.$t('package_xinfo_ask_each')
        return {
          package_id: item.package_id,
          originalData: info,
          title_id: info.title_id,
          group_id: info.group_id,
          key: info.title_id,
          type: otherInfoType[info.otherinfo_type],
          name: name || info.group_name,
          rules,
          mandatory,
          write_level,
          isChild
        }
      })
    },
    async getCollapseData() {
      this.copyModalLoading = true

      const resList = await this.getCopyData()

      if (resList) {
        const allPackages = this.allPackages
        const packageExtraInfos = resList
        const otherPackages = allPackages.filter((pkg) => {
          if (this.isCopyTo) {
            return pkg.package_id === this.package_id
          }
          return pkg.package_id !== this.package_id
        })

        let index = 0 // 这里和老admin不同的是，这里删掉了没有extra_info的套餐
        otherPackages.forEach((item) => {
          const key = String(item.package_id) // a-collapse-panel 的 key 需要使用 string 类型
          const extraInfo = packageExtraInfos.find((pkg) => pkg.package_id === item.package_id)
          // 这里和老admin不同的是，这里删掉了没有extra_info的套餐
          const otherInfos = extraInfo?.instance_datas?.groups ?? []
          if (otherInfos.length > 0) {
            // participantsInfo 这个类型的信息必须全部一起复制，不能只选择其中的几个项目
            const { participantsInfo = [], others = [] } = otherInfos.reduce(
              (acc, curr) => {
                if (curr.info_type === 2) {
                  acc.participantsInfo.push(curr)
                } else {
                  acc.others.push(curr)
                }
                return acc
              },
              {
                participantsInfo: [],
                others: []
              }
            )
            const otherData = this.getOtherInfosData(others, item)
            const participantsInfoData = this.getOtherInfosData(participantsInfo, item, true)
            if (participantsInfoData.length) {
              const children = _.cloneDeep(participantsInfoData)
              const id = getUuid()
              participantsInfoData.unshift({
                title_id: id,
                package_id: +item.package_id,
                name: this.$t('28640'),
                key: id,
                childs: children,
                isParent: true
              })
            }
            this.$set(this.copyModalSelectedData, item.package_id, [])
            this.copyCollapseActiveKey.push(key)
            this.copyCollapseData[index] = {
              key,
              name: `${key} - ${item.title}`,
              package_id: +item.package_id,
              data: [...otherData, ...participantsInfoData]
            }

            index += 1
          }
        })
      }

      this.copyModalLoading = false
    },
    async getAllPackages() {
      if (this.allPackages.length) {
        return
      }
      const { isSpuFlow } = this
      const pageType = isSpuFlow
        ? [PAGE_LEVEL_DICT.spu, PAGE_LEVEL_DICT.att, PAGE_LEVEL_DICT.act]
        : [PAGE_LEVEL_DICT.act]
      const packages = await this.$store.dispatch('actionGetAllPackages', {
        activityId: this.activity_id,
        page_type: pageType?.join(',')
      })
      this.allPackages = packages.map((item) => {
        const title = bestMatchLang('name', 'language_type', item.package_local)
        return {
          ...item,
          title
        }
      })
    },
    getCopyData() {
      let language
      if (this.$root.isMerchant) {
        language = [getEditLang()]
      } else {
        let ref = this.$route.query.ref || 'en_US'
        language = [...new Set([getEditLang(), ref, 'en_US'])]
      }
      const { isSpuFlow } = this
      const params = {
        activity_id: this.activity_id,
        language: language.join(','),
        info_type: this.infoType
      }
      if (isSpuFlow) {
        params.page_type = PAGE_LEVEL_DICT.spu
      }
      return ajax.get(ADMIN_API.act.get_package_otherinfos_by_activity, {
        params
      })
    },
    getFields(data = []) {
      const list = data.map((item) => {
        const { key, value } = item
        return {
          key,
          value
        }
      })
      return list
    },
    getCopyFormData() {
      const { targets } = this
      const copyCollapseData = this.copyCollapseData || []
      const copyModalSelectedData = this.copyModalSelectedData || {}
      const allSelectedData = copyCollapseData.reduce((acc, curr) => {
        const package_id = curr.package_id
        const currentCopyModalSelectedData = copyModalSelectedData[package_id] || []
        const data = curr.data.filter((item) => {
          return currentCopyModalSelectedData.includes(item.title_id)
        })
        return {
          ...acc,
          [package_id]: data
        }
      }, {})
      const { package_id, activity_id } = this
      const formData = {
        target_package_ids: this.isCopyTo ? targets : [package_id],
        activity_id,
        language: getEditLang(),
        copy_otherinfo: []
      }
      const list = Object.entries(allSelectedData)
      const copy_otherinfo = []
      list.forEach((item) => {
        const [package_id, data] = item
        if (data.length) {
          const groups = data.reduce((acc, curr) => {
            const { isParent, childs } = curr
            if (isParent) {
              const data = childs.map((item) => {
                return this.getGroupData(item.originalData)
              })
              return [...acc, ...data]
            } else {
              acc.push(this.getGroupData(curr.originalData))
              return acc
            }
          }, [])
          copy_otherinfo.push({
            from_package_id: +package_id,
            groups
          })
        }
      })
      formData.copy_otherinfo = copy_otherinfo
      return formData
    },
    getGroupData(data = {}) {
      const { group_id, title_id, style, write_level } = data
      const fields = this.getFields(style.fields)
      return {
        group_id,
        title_id,
        fields,
        write_level
      }
    }
  }
}
