import { getEditLang } from '@activity/utils'

export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [Array, String],
      required: true
    },
    field: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    },
    disabledExclude: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currValues: []
    }
  },
  computed: {
    isEnUS() {
      return getEditLang() === 'en_US'
    },
    // 接口 en_US 下 content_en, 其余是 content
    // 这里数据层统一为 content, 保存时在格式化
    currContentField() {
      return this.isEnUS ? 'content_en' : 'content'
    }
  },
  watch: {
    currValues: {
      deep: true,
      handler(values) {
        values = _.cloneDeep(values).map((item) => {
          if (item.content !== 'string') {
            item[this.currContentField] = JSON.stringify(item.content)
          }

          if (this.isEnUS) {
            delete item.content
          } else {
            delete item.content_en
          }

          return item
        })

        this.$emit('change', values)
      }
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      this.currValues = _.cloneDeep(this.value || []).map((item) => {
        const content = this.parseJsonString(item[this.currContentField])

        // location_original 是 Google map 原始值
        // location 分两种，非 CN 与原始值相等, CN 则由原始值 GCJ-02 转换为 wgs84 编码
        if (content.location_original) {
          content.location = content.location_original
          delete content.location_original
        }

        return {
          ...item,
          content: this.parseJsonString(item[this.currContentField])
        }
      })
    },
    parseJsonString(str) {
      try {
        return JSON.parse(str)
      } catch (e) {
        return {}
      }
    }
  }
}
