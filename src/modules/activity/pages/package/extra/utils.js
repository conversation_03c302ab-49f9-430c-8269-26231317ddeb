export const autoCollectContactInfoList = [
  __('29147'),
  __('29148'),
  __('29149'),
  __('other_info_last_name'),
  __('other_info_first_name'),
  __('other_info_country_region'),
  __('other_info_phone_number'),
  __('other_info_email')
]

export const typeObj = {
  3: __('package_xinfo_dropdown_list'),
  1: __('package_xinfo_text_box'),
  2: __('package_xinfo_date'),
  4: __('package_xinfo_time'),
  6: __('package_xinfo_checkbox')
}

export const otherInfoType = {
  1: __('package_xinfo_date'),
  9: __('package_xinfo_time'),
  3: __('package_xinfo_dropdown_list'),
  4: __('package_xinfo_dropdown_list'),
  0: __('package_xinfo_text_box')
}

export const dateObj = {
  1: __('package_xinfo_before_today'),
  2: __('package_xinfo_after_today'),
  3: __('package_xinfo_before_booking'),
  4: __('package_xinfo_after_booking')
}

export const copyOtherInfoType = {
  '2': '-1,-2',
  '1': '0,1,2,3'
}

// other-info p3 自定义 otherinfo 洗成不自定义的，洗了之后就要 disabled 了
export const disabledTypeIds = [1, 2, 3, 4]

export const travellerGroupsSort = [
  'name_root',
  'nationality',
  'person_title',
  'identification_type',
  'birthday',
  'age',
  'mobile',
  'email',
  'contact_way_no'
]

export const infoGroupsSort = ['lang_preference', 'participant_type', 'note_type']

import { getEditLang } from '@activity/utils'

// the validator of other info item
export const validator = function(group, field, rule, value, fields, callback, isCombo = false) {
  let rel_key = field.options.rel_key
  const shouldCheckContent = getEditLang() !== 'en_US'

  if (!rule.required) {
    callback()
    return
  }

  if (rel_key) {
    let linkageField = fields.find((item) => item.key === field.options.rel_key)

    if (linkageField && Array.isArray(linkageField.value) && linkageField.value.length) {
      callback()
      return true
    }
  }

  if (
    field.style.type === 1 && // select
    field.options.type === 7
  ) {
    ;(value &&
    !(isNaN(value.max) || isNaN(value.min) || value.min >= value.max) && // number range
      callback()) ||
      callback(new Error(this.$t('package_price_data_error')))
    return
  }

  // multi-input (content_en必传，content非必传)
  if (field.style.type === 16) {
    const { content, content_en } = value || {}

    if (!content_en || (shouldCheckContent && !content)) {
      callback(new Error(isCombo ? this.$t('73062') : this.$t('can_not_be_empty')))
      return
    }
  }

  // drag-input (content_en必传，content非必传)
  if (field.style.type === 17) {
    const valid =
      Array.isArray(field.value) &&
      field.value.length &&
      !field.value.some((item) => !item.content_en || (shouldCheckContent && !item.content))

    if (!valid) {
      callback(new Error(isCombo ? this.$t('73061') : this.$t('can_not_be_empty')))
      return
    }
  }

  // 23: selection_district 24: fixed_point
  if ([23, 24].includes(field.style.type)) {
    const valid =
      Array.isArray(field.value) &&
      field.value.length &&
      field.value.every((item) => item.content || item.content_en)

    if (!valid) {
      callback(new Error(this.$t('104297')))
      return
    }
  }

  if (field.style.multiple_selection) {
    ;(Array.isArray(value) && value.length && callback()) || callback(new Error(this.$t('can_not_be_empty')))
    return
  }

  if (field.style.is_unique) {
    let currentKeyValues = (this.groups || []) // 兼容：在 combo drawer 下不存在 groups
      .filter((item) => group.group_id === item.group_id)
      .reduce((acc, curr) => {
        curr.style.fields.forEach((f) => {
          if (f.key === field.key) {
            acc = [...acc, f.value]
          }
        })
        return acc
      }, [])

    if (_.uniqWith(currentKeyValues, _.isEqual).length !== currentKeyValues.length) {
      callback(new Error(this.$t('other_info_validate_warn_duplicate')))
      return
    }
  }

  ;(value && callback()) || callback(new Error(this.$t('can_not_be_empty')))
}

export const confirm = function({
  storeKey,
  content,
  title = __('73071'),
  cancelText = __('73070'),
  okText = __('global_confirm')
} = {}) {
  if (!storeKey || !content) {
    throw new TypeError('Lack store key or content')
  }

  storeKey = `no-longer-remind-${storeKey}`

  return new Promise((resolve) => {
    if (sessionStorage.getItem(storeKey)) {
      resolve(true)
    } else {
      let noLongerRemind = false

      this.$modal.confirm({
        title,
        maskClosable: true,
        cancelText,
        okText,
        content: (h) => (
          <div>
            {content}
            <br />
            <br />
            <a-checkbox value={noLongerRemind} onInput={(val) => (noLongerRemind = val)}>
              {__('73073')}
            </a-checkbox>
          </div>
        ),
        onOk: () => {
          if (noLongerRemind) {
            sessionStorage.setItem(storeKey, true)
          }
          resolve(true)
        },
        onCancel() {
          resolve(false)
        }
      })
    }
  })
}
