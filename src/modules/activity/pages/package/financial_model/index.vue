<template>
  <div class="financial_model-container" :data-spm-page="getPageSpm">
    <div class="common-basic-box">
      <h2 class="financial-model-info">
        {{ $t('financial_model_financial_model') }}:
        <label>
          {{ $t('financial_model_status') }}:
          <span :class="financeStatusClassName">{{ financeStatusTxt }}</span>
        </label>
      </h2>

      <div class="financial-model-body">
        <a-form-model-item
          :label="$t('financial_model_financial_model')"
          :colon="false"
          :rules="{
            required: true
          }"
        >
          <a-radio-group
            v-model="answer"
            :disabled="disabledSelection || isCombo"
            class="financial-model-question-list"
          >
            <a-radio
              v-for="(question, i) in questionList"
              :key="i"
              :value="question.value"
              :data-value="question.value"
              class="financial-model-question-item"
            >
              <a-tooltip effect="light" :content="question.tips" placement="right" disabled>
                <span class="financial-model-question-title">
                  {{ question.title }}
                </span>
              </a-tooltip>
            </a-radio>
          </a-radio-group>
        </a-form-model-item>
      </div>
    </div>
    <div class="note-wrap">
      <a-alert closable type="info" show-icon>
        <div slot="message" class="note-box">
          <a
            target="_blank"
            href="https://docs.google.com/forms/d/e/1FAIpQLSdDnYkuj2cqvde9pLe8-d8uNpuaHGu1gfMQyiPtcDL4X7CdsQ/viewform"
          >
            Attached questionnaire
          </a>
          to help you identify financial model:
        </div>
      </a-alert>
    </div>
    <div class="common-basic-box">
      <h2>{{ $t('questionnaire') }}</h2>
      <div class="financial-model-table">
        <table class="table">
          <tr class="header">
            <th class="cell">
              Q1 - Please choose the most appropriate description of the ownership change of ticket / product.
            </th>
            <th class="cell">
              Q2 - When does Klook get ticket / product from merchant?
            </th>
            <th class="cell">
              Q3 - Who keeps ticket / product till customer's order is fulfilled?
            </th>
            <th class="cell">Q4 - What is the nature of the product?</th>
            <th class="cell">Result</th>
          </tr>
          <tr>
            <td class="cell" rowspan="2">
              Merchant --&gt; Customer<br /><br />i.e. Klook purchase product from merchant based on
              customer's order, and then settle with merchant
            </td>
            <td class="cell">
              When / After customer place an order on Klooki.e. CEG / Merchant / System automatically confirm
              order and we pass the voucher to customer
            </td>
            <td class="cell">Merchant</td>
            <td class="cell">N/A</td>
            <td class="cell">
              Sell &amp; Settle--&gt; Later Settlement or Prepayment
            </td>
          </tr>
          <tr>
            <td class="cell">
              Before customer place an order on Klooki.e. Need PIC team's help to pre-load vouchers to our
              inventory system
            </td>
            <td class="cell">Klook</td>
            <td class="cell">N/A</td>
            <td class="cell">Sell &amp; Settle--&gt; Consignment to Klook</td>
          </tr>
          <tr>
            <td class="cell" rowspan="3">
              Merchant --&gt; Klook --&gt; Customer<br /><br />i.e. Klook issue PO and bulk purchase ticket /
              product from merchant first, and fulfill customer's order by inventory
            </td>
            <td class="cell" rowspan="2">
              Before customer place an order on Klooki.e. Merchant send over E-ticket / physical product first
              and Klook issue to customer upon order
            </td>
            <td class="cell">Klook ／ Third Party Redemption Point</td>
            <td class="cell">E-ticket</td>
            <td class="cell">Buy &amp; Sell - E-ticket</td>
          </tr>
          <tr>
            <td class="cell">Klook ／ Third Party Redemption Point</td>
            <td class="cell">Physical Ticket / Product</td>
            <td class="cell">Buy &amp; Sell - Physical Product</td>
          </tr>
          <tr>
            <td class="cell">
              Klook does NOT get ticket / product from merchanti.e. The actual goods stay with merchant till
              customer ask for redemption using a Klook voucher
            </td>
            <td class="cell">N/A</td>
            <td class="cell">N/A</td>
            <td class="cell">Buy &amp; Sell - Consignment to Merchant</td>
          </tr>
        </table>

        <a-modal
          :title="submitDialog.title"
          :visible.sync="submitDialog.visible"
          @cancel="submitDialog.visible = false"
        >
          <div>{{ $t('financial_model_confirm_tips') }}</div>
          <!-- <a href="javascript:void(0)" style="text-decoration: underline;" @click="dialogSubmitFinanceModel">
            {{ $t('financial_model_dialog_save') }}
          </a> -->
          <div slot="footer" class="dialog-footer">
            <a-button type="primary" @click="submitFinanceModelBack">{{
              $t('financial_model_dialog_back_to')
            }}</a-button>
          </div>
        </a-modal>
      </div>
    </div>
    <CommonFooter>
      <footer class="financial-model-footer">
        <a-button
          v-if="[1].includes(financeModelInfo.financial_status)"
          type="primary"
          :disabled="hasSendEmail"
          @click="sendEmailHandle"
        >
          {{ $t('email_to_pic') }}
        </a-button>
        <a-button
          type="primary"
          :disabled="btnType !== 'save' || submitDisabled"
          @click="submitFinanceModelHandle"
        >
          {{ $t('global_button_save') }}
        </a-button>
      </footer>
    </CommonFooter>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import CommonFooter from '@activity/components/CommonFooter.vue'

const submit_dialog_mixin = {
  data() {
    return {
      submitDialog: {
        visible: false,
        title: this.$t('global_tips')
      }
    }
  },
  methods: {
    ...mapActions(['updatePkgStepStatus2action']),
    async submitFinanceModelConfirm() {
      this.submitDialog.visible = true
    },
    submitFinanceModelBack() {
      this.$router.push({
        name: 'packageBasicInfo',
        params: this.$route.params,
        query: this.$route.query
      })
    },
    async dialogSubmitFinanceModel() {
      await this.save()
      this.submitDialog.visible = false
    }
  }
}

export default {
  inject: ['reloadPage2provide'],
  components: { CommonFooter },
  props: ['activity_id', 'package_id', 'package_type'],
  mixins: [submit_dialog_mixin],
  data() {
    return {
      financialStatusOptions: {
        0: this.$t('financial_model_not_set'),
        1: this.$t('financial_model_pending_approve'),
        2: this.$t('financial_model_approved'),
        3: this.$t('financial_model_rejected'),
        4: '待二次修改'
      },

      financeModelInfo: {
        financial_status: 0 // Default value
      },

      hasSendEmail: false,

      questionList: [
        {
          title: 'Settlement',
          value: 1,
          tips: 'Settlement'
        },
        {
          title: 'Bulk Purchase - E-ticket',
          value: 2,
          tips: 'Bulk Purchase - E-ticket'
        },
        {
          title: 'Bulk Purchase - Physical',
          value: 3,
          tips: 'Bulk Purchase - Physica'
        },
        {
          title: 'Bulk Purchase - Consignment to Merchant',
          value: 4,
          tips: 'Bulk Purchase - Consignment to Merchant'
        }
      ],
      answer: 0,

      disableSubmit: false,
      disabledSelection: false,
      btnType: 'save'
    }
  },
  watch: {
    isCombo: {
      immediate: true,
      handler(v) {
        if (v) {
          this.answer = 1
        }
      }
    }
  },
  computed: {
    getPageSpm() {
      const oid = `activity_${this.activity_id || 0}`
      return `FinancialModel?oid=${oid}`
    },
    ...mapGetters(['merchantCurrency', 'packageProductType']),
    // combo
    isCombo() {
      return this.packageProductType === 1
    },
    submitDisabled() {
      return (!this.answer && this.financeModelInfo.financial_status !== 1) || this.disableSubmit
    },
    financeStatusClassName() {
      return ['create', 'reviewing', 'passed ', 'rejected'][this.financeModelInfo.financial_status || 0]
    },
    financeStatusTxt() {
      return this.financialStatusOptions[this.financeModelInfo.financial_status] || 'Default'
    }
  },
  methods: {
    async sendEmailHandle() {
      this.$confirm({
        title: this.$t('act_financial_model_send_email'),
        okText: this.$t('act_financial_model_send_btn'),
        cancelText: this.$t('global_cancel'),
        type: 'warning',
        onOk: async () => {
          let result = await ajax.getBody(
            {
              url: ADMIN_API.act.send_financial_pic_email,
              params: {
                activity_id: this.activity_id
              }
            },
            {
              loading: true,
              msgOpt: {
                isSucMsg: true,
                content: this.$t('send_email_success')
              }
            }
          )
          result.success && (this.hasSendEmail = true)
        }
      })
    },
    submitFinanceModelHandle() {
      let stock_out_type = this.financeModelInfo.stock_out_type && this.financeModelInfo.stock_out_type.trim()
      if (stock_out_type !== 'INVENTORY' && [2, 3, 4].includes(this.answer)) {
        this.submitFinanceModelConfirm()
      } else {
        this.save()
      }
    },
    async save() {
      let result = await ajax.postBody(
        {
          url: ADMIN_API.act.update_financial,
          data: {
            package_id: this.package_id,
            financial: this.answer
          }
        },
        ajax.sucOptions
      )
      if (result && result.success) {
        if (!this.$store.state.pkgStepStatus.financial_model) {
          let isTrue = await this.updatePkgStepStatus2action({
            activity_id: this.activity_id,
            package_id: this.package_id,
            step: 'financial_model',
            language: 'ALL',
            status: 1
          })
          if (isTrue) {
            klook.bus.$emit('updatePkgInfos2bus')
          }
        }
        klook.bus.$emit('initQueryData2bus') //状态流转 submit to em
        setTimeout(() => {
          result && this.reloadPage2provide()
        }, 200)
      }
    },
    async getPkgFinancialModel() {
      this.financeModelInfo = await ajax.get({
        url: ADMIN_API.act.get_financial,
        params: {
          package_id: this.package_id
        }
      })

      this.answer = this.financeModelInfo.financial || 0
      if (this.financeModelInfo.financial_status === 1) {
        this.disabledSelection = true
      }
      if ([0, 4].includes(this.financeModelInfo.financial_status)) {
        this.answer = 0
      }
      // 0：未设置， 1：待审核， 2：已审核通过，3，审核未通过, 4, 待二次修改,
      if ([1].includes(this.financeModelInfo.financial_status)) {
        this.btnType = 'next'
      } else {
        this.btnType = 'save'
      }
      // combo 套餐默认模型为 1
      if (this.isCombo) {
        this.answer = 1
      }

      this.auditPkgPublishedFn()
    },
    auditPkgPublishedFn() {
      if (this.financeModelInfo.package_status === 1) {
        if (this.financeModelInfo.financial_status !== 2) {
          this.$info({
            title: this.$t('act_financial_model_pkg_warn')
          })
          this.disableSubmit = true
        }
        this.btnType = 'next'
        this.disabledSelection = true
      }
    }
  },
  created() {
    this.getPkgFinancialModel()
  }
}
</script>

<style lang="scss" scoped>
.financial_model-container {
  margin-bottom: 50px;
  .financial-model-info {
    label {
      color: rgba(0, 0, 0, 0.65);
      font-size: 12px;
      margin-left: 12px;
    }
    span {
      padding: 2px 10px;
      border-radius: 2px;
      margin-left: 4px;
      &.create {
        color: #4c87e6;
        border: solid 1px #4c87e6;
      }
      &.reviewing {
        color: #ffa628;
        border: solid 1px #ffa628;
      }
      &.passed {
        color: #02ac98;
        border: solid 1px #02ac98;
      }
      &.rejected {
        color: #ff4949;
        border: solid 1px #ff4949;
      }
    }
  }
  .note-wrap {
    margin: 20px 0;
    .note-box {
      font-size: 14px;
    }
  }
}

.financial-model-table {
  padding: 16px 0;
  .table {
    max-width: 100%;
    min-width: 660px;
    border-collapse: collapse;
    border-spacing: 0;
    .header {
      background: #333;
      color: #fff;
    }
    td,
    th {
      font-size: 14px;
      font-weight: normal;
      padding: 10px 5px;
      border-style: solid;
      border-width: 1px;
      overflow: hidden;
      word-break: normal;
      border-color: #666;
    }
    .cell {
      text-align: left;
    }
  }
}
</style>
