export async function getItineraryAudio(params, options) {
  const res = await ajax.get(options?.url || ADMIN_API.act.get_itinerary_audio, {
    params
  })
  return res
}

export async function saveItineraryAudio(data) {
  const res = await ajax.post(ADMIN_API.act.save_itinerary_audio, {
    data
  })
  return res === false ? false : true
}

// 一期用不到
// export async function deleteItineraryAudio(data) {
//   const res = await ajax.post(ADMIN_API.act.delete_itinerary_audio, {
//     data
//   })
//   return res
// }
