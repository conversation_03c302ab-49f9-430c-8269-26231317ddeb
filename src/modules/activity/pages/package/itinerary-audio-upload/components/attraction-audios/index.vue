<template>
  <a-form layout="inline" :model="form" class="audio-form">
    <a-form-item :label="$t(TEXT_ID_MAP['ATTRACTION_SELECT_LABEL'])" :required="true">
      <a-select
        v-model="form.attr_value_id"
        :options="attractionOptions"
        style="width: 400px"
        @change="handleChange"
      />
    </a-form-item>

    <div class="audio-upload">
      <div class="audio-upload-guide">
        <div class="audio-upload-guide-title">{{ $t(TEXT_ID_MAP['GUDIE_TITLE']) }}</div>
        <div class="audio-upload-guide-content" v-html="$t(TEXT_ID_MAP['GUDIE_CONTENT'])"></div>
      </div>
      <div v-for="(audio, index) in form.audios" :key="index" class="audio-upload-block">
        <div
          v-for="({ val: language, required }, langIndex) in audioLanguages"
          :key="langIndex"
          class="upload-view-audio"
        >
          <a-form-item class="full-width-form-item" :label="language" :required="required">
            <AudioUpload
              v-model="form.audios[index].audio_i18ns[langIndex]"
              :default-audio-name="`${currSelectAttractionName} - ${language}`"
              :language="language"
              :time-limit="timeLimit"
              :size-limit="sizeLimit"
            />
          </a-form-item>
        </div>
      </div>
    </div>

    <div v-if="form.attr_value_id" class="bottom-bar">
      <a-button type="primary" @click="handleSave">{{ $t(TEXT_ID_MAP['BTN_CONFIRM']) }}</a-button>
    </div>
  </a-form>
</template>

<script>
import AudioUpload from '../audio-upload/index.vue'
import { saveItineraryAudio } from '../../apis/index.js'
import { getIsAudioListsSame } from '../../utils/index.js'
import {
  audioLanguages,
  defaultAudioFile,
  AUDIO_TIME_LIMIT,
  AUDIO_BYTE_LIMIT,
  defaultAudioConfig,
  TEXT_ID_MAP
} from '../../constant/index.js'

export default {
  name: 'AttractionAudio',
  components: {
    AudioUpload
  },
  props: {
    packageId: {
      type: Number,
      required: true
    },
    itineraryAudios: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      audioLanguages,
      preAttrValueId: undefined,
      timeLimit: AUDIO_TIME_LIMIT,
      sizeLimit: AUDIO_BYTE_LIMIT,
      form: {
        attr_value_id: undefined,
        audios: []
      },
      TEXT_ID_MAP
    }
  },
  created() {
    this.handleAutoSelectFirstAttraction()
  },
  watch: {
    itineraryAudios: {
      deep: true,
      handler(v) {
        this.preAttrValueId && this.handleAttractionChange(this.preAttrValueId)
      }
    }
  },
  computed: {
    attractionOptions() {
      return this.itineraryAudios.map(({ attr_value_id, title_multilang }) => ({
        key: attr_value_id || -1,
        label: title_multilang || '',
        value: attr_value_id || -1
      }))
    },
    currSelectAttractionName() {
      const selectedAttraction = this.itineraryAudios.find(
        ({ attr_value_id }) => +attr_value_id === +this.form.attr_value_id
      )
      const { title_multilang = '' } = selectedAttraction || {}
      return title_multilang
    }
  },
  methods: {
    formatAudioWithI18ns(audio) {
      return {
        ...audio,
        audio_i18ns: this.audioLanguages.map(({ val: language }) => {
          const existingAudio = audio.audio_i18ns.find((item) => item.language === language)
          return existingAudio || { language, ...defaultAudioFile }
        })
      }
    },
    calcHasAudioChangeSaved(attrValueId) {
      const savedAudios =
        this.itineraryAudios.find(({ attr_value_id }) => +attr_value_id === +attrValueId)?.audios || []
      const currAudios = this.form.audios?.filter((ele) => {
        const { audio_i18ns = [] } = ele
        return audio_i18ns?.some((audio_i18n) => !!audio_i18n?.audio_url)
      })
      return getIsAudioListsSame(savedAudios, currAudios)
    },
    handleAutoSelectFirstAttraction() {
      const { attr_value_id = '' } = this.itineraryAudios?.[0] || {}
      this.$set(this.form, 'attr_value_id', attr_value_id)
      this.handleAttractionChange(attr_value_id)
    },
    handleChange(newAttrValueId) {
      if (!this.calcHasAudioChangeSaved(this.preAttrValueId)) {
        this.$confirm({
          title: 'Audio has not been saved, are you sure to change to another attraction?',
          onOk: () => this.handleAttractionChange(newAttrValueId),
          onCancel: () => {
            this.$nextTick(() => {
              this.form.attr_value_id = this.preAttrValueId
            })
          }
        })
      } else {
        this.handleAttractionChange(newAttrValueId)
      }
    },
    handleAttractionChange(attrValueId) {
      const selectedAttraction = this.itineraryAudios.find(
        ({ attr_value_id }) => +attr_value_id === +attrValueId
      )
      if (!selectedAttraction) return

      const audios = selectedAttraction.audios.length ? selectedAttraction.audios : [defaultAudioConfig]
      this.form.audios = audios.map(this.formatAudioWithI18ns)
      this.preAttrValueId = attrValueId
    },
    async handleSave() {
      const { packageId } = this
      const audios = this.form.audios.map((audio) => ({
        ...audio,
        audio_i18ns: audio.audio_i18ns.filter(({ audio_url }) => !!audio_url)
      }))

      const res = await saveItineraryAudio({
        package_id: packageId,
        attr_value_id: this.form.attr_value_id,
        audios
      })

      if (res) {
        this.$message.success('Audio uploaded!')
        this.$emit('saved')
      } else {
        this.$message.error('Audio upload failed!')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.audio-upload {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.audio-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.audio-upload-block {
  background: #fafafa;
  padding: 16px;
}
.full-width-form-item {
  display: inline-flex;
  width: 100%;
  align-items: center;

  ::v-deep .ant-form-item-control-wrapper {
    flex: 1;
  }
}
.audio-upload-guide {
  display: flex;
  flex-direction: column;
  gap: 4px;

  &-title {
    color: black;
  }

  &-content {
    color: gray;
  }
}
.op-button {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-top: 12px;
}
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #ffffff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  padding: 10px;
  text-align: center;
  z-index: 1000; // 确保底部栏在最顶层
}
</style>
