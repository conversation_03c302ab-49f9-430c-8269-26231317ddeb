<template>
  <div class="audio-upload">
    <a-button
      v-if="!uploadedFile.audio_url"
      class="audio-uploader"
      @click="handleFileUpload"
      size="small"
      style="width: 100px"
    >
      <a-icon type="cloud-upload"></a-icon>
      Upload
    </a-button>

    <div v-else class="audio-upload-audio-info">
      <a-tooltip
        placement="right"
        :title="`size: ${formatAuidoSize(uploadedFile.audio_size)}\nduration: ${formatAudioDuration(
          uploadedFile.audio_duration
        )}`"
      >
        <a :href="uploadedFile.audio_url" class="audio-name" target="_blank">
          <a-icon type="link" />
          {{ uploadedFile.audio_name }}
        </a>
      </a-tooltip>
      <div class="audio-upload-operate-block">
        <a-button type="text" class="audio-upload-text-button" size="small" @click="downloadFile">
          <a-icon type="download" style="font-size: 16px" />
        </a-button>
        <a-button type="text" class="audio-upload-text-button" size="small" @click="handleRemoveFile">
          <a-icon type="delete" style="font-size: 16px" />
        </a-button>

        <a-button type="text" class="audio-upload-text-button" size="small" @click="handleReUpload">
          <a-icon type="cloud-upload" style="font-size: 16px" />
        </a-button>
      </div>
    </div>
  </div>
</template>

<script>
import { cloudinaryOptions } from '@activity/components/photo-group/const'
import { TEXT_ID_MAP } from '../../constant/index.js'

export default {
  name: 'AudioUpload',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    timeLimit: {
      type: Number,
      default: 30 * 60
    },
    sizeLimit: {
      type: Number,
      default: 50 * 1024 * 1024
    },
    defaultAudioName: {
      type: String,
      default: ''
    },
    language: {
      type: String,
      default: 'en_US'
    },
    value: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    uploadedFile: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('change', val)
      }
    }
  },
  data() {
    return {
      TEXT_ID_MAP
    }
  },
  methods: {
    async handleFileUpload() {
      this.$message.info('Opening Upload Widget, Please wait……')
      const { timeLimit, sizeLimit } = this
      const [result, error] = await new Promise((resolve, reject) => {
        window?.cloudinary?.openUploadWidget(
          {
            ...cloudinaryOptions(),
            resourceType: 'video', // 音频文件使用 video 类型上传
            clientAllowedFormats: ['mp3'], // 允许的音频格式
            uploadPreset: 'ys9tzolg',
            duration: timeLimit,
            maxFileSize: sizeLimit,
            multiple: false // 只能上传一个
          },
          (error, result) => {
            if (result.failed || result.event === 'success') {
              resolve([[result.info || {}], error])
            }
          }
        )
      })
      if (error) {
        this.$message.error({
          content: error?.statusText || 'Upload Failed',
          getContainer: () => document.body
        })
        return
      }
      const { secure_url: audio_url = '', duration: audio_duration = 0, bytes = 0 } = result?.[0] || {}
      if (audio_duration > 60 * timeLimit) {
        this.$message.error({
          content: this.$t(TEXT_ID_MAP['AUDIO_SIZE_INVALID']),
          getContainer: () => document.body
        })
        return
      }
      const { defaultAudioName = '', language } = this
      this.uploadedFile = {
        audio_name: defaultAudioName,
        audio_url,
        audio_size: bytes,
        audio_duration: `${audio_duration}`,
        language
      }
    },
    formatAuidoSize(bytes) {
      const KB = 1024
      const MB = 1024 * KB
      if (bytes < KB) {
        return `${bytes} bytes`
      } else if (bytes < MB) {
        const kilobytes = (bytes / KB).toFixed(2)
        return `${kilobytes} KB`
      } else {
        const megabytes = (bytes / MB).toFixed(2)
        return `${megabytes} MB`
      }
    },
    formatAudioDuration(seconds) {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = (seconds - minutes * 60).toFixed(2)
      return `${minutes}min${remainingSeconds}s`
    },
    handleReUpload() {
      this.$confirm({
        title: 'Are you sure to replace the current audio?',
        onOk: () => {
          this.removeAudio() // 调用删除方法来重置文件信息
          this.handleFileUpload() // 重新触发文件上传逻辑
        }
      })
    },
    handleRemoveFile() {
      this.$confirm({
        title: 'Are you sure delete this audio?',
        onOk: () => {
          this.removeAudio() // 调用删除方法来重置文件信息
        }
      })
    },
    downloadFile() {
      const { uploadedFile = {} } = this
      const { audio_url = '', audio_name = '' } = uploadedFile || {}
      let x = new XMLHttpRequest()
      x.open('GET', audio_url, true)
      x.responseType = 'blob'
      x.onload = function (e) {
        var url = window.URL.createObjectURL(x.response)
        var a = document.createElement('a')
        a.href = url
        a.download = audio_name
        a.click()
      }
      x.send()
    },
    removeAudio() {
      this.uploadedFile = {
        audio_name: '',
        audio_url: '',
        audio_duration: 0,
        language: this.language
      }
    }
  }
}
</script>

<style lang="scss">
.audio-upload {
  display: flex;
}
.audio-upload-audio-info {
  display: flex;
  justify-content: space-between;
  flex: 1;
}

.audio-upload-operate-block {
  display: flex;
  gap: 8px;
  align-items: center;
}

.audio-upload-text-button {
  border: none;
  background: inherit;
}
iframe[allow='camera'] {
  z-index: 1000 !important;
}
</style>
