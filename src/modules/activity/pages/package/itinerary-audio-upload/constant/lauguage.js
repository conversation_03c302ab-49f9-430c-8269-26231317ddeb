const lang_conf = require('lang_conf')
// 语言下拉
export const _lang_array = lang_conf.getAllLangOption().map((v) => ({
  name: v.options,
  val: v.B_LANG
}))

export const lang_array = _lang_array?.filter((language) => {
  const { val = '' } = language
  const isEN = val?.includes('en') && val !== 'en_US'
  return !isEN
})

export const audioLanguages = lang_array?.map((language) => {
  return {
    ...language,
    required: false
  }
})
// key: B_LANG, value: LANG_TITLE
export const lang_obj = lang_conf.getLangObj('B_LANG', 'LANG_TITLE')
