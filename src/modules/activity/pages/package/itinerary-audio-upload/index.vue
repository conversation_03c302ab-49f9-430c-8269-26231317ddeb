<template>
  <div class="itinerary-audio-upload-container">
    <a-spin :spinning="isLoading" tip="Loading...">
      <div class="upload-header" v-if="packageName">
        <h3 class="upload-header-title">{{ $t(TEXT_ID_MAP['UPLOAD_AUDIO'], { pkg_name: packageName }) }}</h3>
        <a-tooltip placement="right">
          <template slot="title">
            <div v-for="(itinerary, index) in itineraryAudios" :key="index">
              <div>{{ itinerary.title_multilang }}: {{ getItineraryAudioCount(itinerary) }}</div>
            </div>
          </template>
          <a-icon type="info-circle" />
        </a-tooltip>
      </div>
      <div class="upload-content">
        <div class="upload-description" v-html="$t(TEXT_ID_MAP['AUDIO_UPLOAD_DESC'])"></div>
        <div class="upload-main-area">
          <AttractionAudios
            :itinerary-audios="itineraryAudios"
            :package-id="packageId"
            @saved="refreshItineraryAudios"
          />
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import AttractionAudios from './components/attraction-audios/index.vue'
import { getItineraryAudio } from './apis/index.js'
import { lang_array as availableLanguages, TEXT_ID_MAP } from './constant/index.js'

export default {
  name: 'ItineraryAudioUpload',
  components: {
    AttractionAudios
  },
  data() {
    return {
      itineraryAudios: [],
      packageName: '',
      isLoading: false,
      TEXT_ID_MAP
    }
  },
  computed: {
    ...mapGetters(['getCategoryInfo2getters']),
    calcIsV2() {
      return this.getCategoryInfo2getters?.spu_id_list?.includes(this.packageId)
    },
    activityId() {
      return +this.$route.params.id
    },
    packageId() {
      return +this.$route.query.package_id
    },
    language() {
      return this.$route.query.lang
    },
    categoryId() {
      return +this.$route.query.category_id
    }
  },
  created() {
    const isInvalidRouteParams = !this.packageId || !this.categoryId
    if (isInvalidRouteParams) {
      this.$message.error('Invalid Url!')
      const { query } = this.$route || {}
      this.$router.replace({
        path: `/act/activity/basic/${this.activityId}`,
        query: {
          ...query
        }
      })
      return
    }
    this.fetchItineraryAudios()
  },
  methods: {
    refreshItineraryAudios() {
      this.fetchItineraryAudios()
    },
    getItineraryAudioCount(audioItem) {
      const { audios = [] } = audioItem
      return audios
        ?.map((audio) => {
          const { audio_i18ns = [] } = audio
          return `${audio_i18ns.length} / ${availableLanguages.length}`
        })
        ?.join('\n')
    },
    async fetchItineraryAudios() {
      const { packageId, language, categoryId } = this
      this.isLoading = true
      const options = {
        url: this.calcIsV2 ? ADMIN_API.act.get_itinerary_audio_v2 : ADMIN_API.act.get_itinerary_audio
      }
      const res = await getItineraryAudio(
        {
          package_id: packageId,
          language,
          category_id: categoryId
        },
        options
      )
      this.isLoading = false
      if (!res) {
        this.$message.error('This Package cannot configure Audio')
        this.$router.replace({
          path: `/act/package/itinerary/${this.activityId}`,
          query: {
            package_id: this.packageId
          }
        })
        return
      }
      const { package_name: packageName = '', data: itineraryAudios = [] } = res
      this.packageName = packageName
      this.itineraryAudios = itineraryAudios
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@merchant/style/global.scss';

.itinerary-audio-upload-container {
  padding: 10px;
}

.upload-header {
  display: flex;
  gap: 4px;
  align-items: center;

  &-title {
    margin-bottom: 0px;
  }
}

.upload-content {
  background-color: #fff;
  padding: 24px 200px 60px;
  display: flex;
  flex-direction: column;
  gap: 20px;

  &-description {
    font-size: 14px;
    font-weight: 400;
    color: gray;
  }

  &-main-area {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
}
</style>
