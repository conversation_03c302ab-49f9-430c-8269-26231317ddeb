const getIsAudioI18nsSame = (oldAudioI18ns = [], newAudioI18ns = []) => {
  const filteredOldAudioI18ns = oldAudioI18ns.filter((ele) => !!ele.audio_url)
  const filteredNewAudioI18ns = newAudioI18ns.filter((ele) => !!ele.audio_url)

  if (filteredOldAudioI18ns.length !== filteredNewAudioI18ns.length) {
    return false
  }

  return filteredNewAudioI18ns.every(({ language, audio_url }) => {
    const oldAudio = filteredOldAudioI18ns.find((ele) => ele.language === language)
    return oldAudio?.audio_url === audio_url
  })
}

export const getIsAudioListsSame = (savedAudios = [], currAudios = []) => {
  if (savedAudios.length !== currAudios.length) {
    return false
  }

  return currAudios.every(({ audio_id, audio_i18ns }) => {
    const savedAudio = savedAudios.find((ele) => ele.audio_id === audio_id) || {}
    return getIsAudioI18nsSame(savedAudio?.audio_i18ns, audio_i18ns)
  })
}
