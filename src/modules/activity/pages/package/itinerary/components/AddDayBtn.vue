<template>
  <div v-if="!provideData.disabled.all && !!menus.length" class="add-day-btn-container timeline-box">
    <span class="timeline-grid"></span>
    <div class="timeline-content">
      <a-icon class="__icon" type="plus-circle" theme="filled" style="font-size: 24px; color:  #437DFF;" />

      <a-tooltip placement="right" overlay-class-name="add-day-menu common-tooltip-style">
        <template #title>
          <div class="menus">
            <div v-for="menu of menus" :key="menu.ref_field_tag" class="menu" @click="handleAdd(menu)">
              <svg-icon
                v-if="ICON_NAME_DICT[menu.ref_field_tag]"
                class="__icon"
                :icon-name="ICON_NAME_DICT[menu.ref_field_tag]"
              />
              {{ menu.name }}
            </div>
          </div>
        </template>
        <a-button type="primary">{{ btnText }}</a-button>
      </a-tooltip>
    </div>
  </div>
</template>

<script>
import { ICON_NAME_DICT, eventBus, TEMPLATES_REF, CHUNK_REF, STAY_REF_KEY } from '../utils/index'

export default {
  name: 'AddDayBtn',
  inject: {
    provideData: {
      default: {
        templates: [],
        itineraryNums: 1,
        disabled: {
          all: false,
          freeText: false
        }
      }
    }
  },
  props: {
    // 如果是某天最前方的添加按钮，pre 则是按钮下方的 group 数据
    pre: {
      type: Object,
      default: undefined
    },
    dataIndex: {
      type: Number,
      default: 0
    },
    oneDayFront: {
      type: Boolean,
      default: false
    },
    // undefined appear across different blocks(chunk)
    next: {
      type: Object,
      default: undefined
    },
    isEmptyDay: {
      type: Boolean,
      default: false
    },
    onlyOneDepartureAndReturn: {
      type: Boolean,
      default: false
    }
  },
  data() {
    this.ICON_NAME_DICT = ICON_NAME_DICT
    return {}
  },
  computed: {
    itineraryNums() {
      return this.provideData.itineraryNums
    },
    templates() {
      const templates = Object.values(this.provideData.templates).filter(
        (tpl) => ![TEMPLATES_REF.head, TEMPLATES_REF.foot].includes(tpl.ref_field_tag)
      )

      if (this.itineraryNums === 1) {
        return templates.filter((tpl) => tpl.ref_field_tag !== STAY_REF_KEY)
      }

      return templates
    },
    departureMenus() {
      return this.templates.filter((template) => template.ref_field_tag === CHUNK_REF.departure)
    },
    commonMenus() {
      return this.templates.filter(
        (template) => ![CHUNK_REF.departure, CHUNK_REF.return].includes(template.ref_field_tag)
      )
    },
    returnMenus() {
      return this.templates.filter((template) => template.ref_field_tag === CHUNK_REF.return)
    },
    menus() {
      // Itinerary without groups data
      if (this.isEmptyDay) {
        return this.commonMenus
      }

      const { chunkGroupName: preChunkGroupName, isLastDay } = this.pre?.uiConfig ?? {}
      const { chunkGroupName: nextChunkGroupName } = this.next?.uiConfig ?? {}

      if (preChunkGroupName === CHUNK_REF.departure) {
        if (this.onlyOneDepartureAndReturn) {
          return this.commonMenus
        } else if (preChunkGroupName === nextChunkGroupName) {
          // Both before and after are departures
          return this.departureMenus
        } else if (!this.next) {
          // Previous one was departure chunk and next chunk was common
          return [...this.departureMenus, ...this.commonMenus]
        }
      }

      // Common chunk
      if (preChunkGroupName === CHUNK_REF.common) {
        if (
          nextChunkGroupName === CHUNK_REF.common || // Both before and after are common widget
          !isLastDay ||
          this.onlyOneDepartureAndReturn
        ) {
          return this.commonMenus
        }

        // Next chunk was return
        return [...this.commonMenus, ...this.returnMenus]
      }

      // 按钮处于某天的最顶部且关联数据是最后一天
      if (this.oneDayFront && isLastDay) {
        if (this.onlyOneDepartureAndReturn) {
          return this.commonMenus
        }

        return [...this.commonMenus, ...this.returnMenus]
      }

      // Both before and after are return widget
      if (!this.onlyOneDepartureAndReturn && preChunkGroupName === CHUNK_REF.return) {
        return this.returnMenus
      }

      return []
    },
    btnText() {
      return this.$t('global_add') + ' ' + this.menus.map((menu) => menu.name).join('/')
    }
  },
  methods: {
    handleAdd(menu) {
      const group = _.cloneDeep(menu)

      if (this.isEmptyDay) {
        eventBus.$emit('addDayGroup', {
          group,
          position: {
            data_index: this.dataIndex,
            group_index: 0
          }
        })

        return
      }

      let { data_index, group_index } = this.pre.uiConfig
      if (this.oneDayFront) {
        group_index = 0
      } else {
        group_index += 1
      }

      eventBus.$emit('addDayGroup', {
        group,
        position: {
          data_index,
          group_index
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.add-day-btn-container {
  cursor: pointer;

  .timeline-content {
    position: relative;
    top: -6px;
    display: flex;
    align-items: center;
    gap: 16px;
    margin-left: 150px;

    ::v-deep .ant-btn-primary {
      background-color: rgb(67, 125, 255);
      border-color: rgb(67, 125, 255);
    }
  }
}

.add-day-menu {
  .menus {
    padding: 6px;

    .menu {
      display: flex;
      align-items: center;
      height: 32px;
      padding: 4px 12px;
      line-height: 32px;
      color: rgba(0, 0, 0, 0.85);
      cursor: pointer;

      &:hover {
        background-color: #f5f5f5;
      }

      .__icon {
        margin-right: 8px;
        font-size: 24px;
      }
    }
  }
}
</style>
