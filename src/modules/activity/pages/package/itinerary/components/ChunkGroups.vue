<!-- 最初做拖拽交互遗留的拆块方案，对于当前相对简单的交互来说并不友好 -->
<template>
  <div class="chunk-groups-container">
    <template v-for="chunk of chunks">
      <div
        v-for="(group, groupIndex) of chunk.groups"
        :key="`${group.uuid}`"
        class="group-item"
        :class="{
          'is-last-day': group.uiConfig.isLastDay
        }"
        :data-uuid="group.uuid"
        :data-data_index="group.uiConfig.data_index"
        :data-group_index="group.uiConfig.group_index"
      >
        <slot name="title">
          <div v-if="group.uiConfig.title" class="day-title">
            <div class="day-title-content">
              <a-icon
                class="timeline-collapse-icon"
                :type="getNonactivatedStateOnDay(group) ? 'down' : 'up'"
                @click="handleSwitchActive(group)"
              />
              <span class="day-title-text">{{ group.uiConfig.title }}</span>
            </div>
          </div>
        </slot>

        <AddDayBtn
          v-if="displayAddBtn && !group.uiConfig.isFirstDay && group.uiConfig.oneDayFront"
          :pre="group"
          :one-day-front="true"
          :next="chunk.groups[groupIndex + 1]"
          :only-one-departure-and-return="onlyOneDepartureAndReturn"
        />

        <Group
          :key="group.uuid"
          ref="group"
          :schema="group"
          class="group-instance"
          :data-uuid="group.uuid"
          :is-timeline="isTimeline"
          :display-name="isTimeline"
          :is-first-on-chunk="groupIndex === 0"
          :is-last-on-chunk="chunk.groups.length - 1 === groupIndex"
          :can-sort="isTimeline"
          :nonactivated-group-keys.sync="currNonactivatedGroupKeys"
          :schedule-type="scheduleType"
          :itinerary-version="itineraryVersion"
        >
          <template v-if="displayAddBtn && !(group.uiConfig.isLastDay && group.uiConfig.isLastGroup)" #footer>
            <AddDayBtn
              :data-index="index"
              :pre="group"
              :next="chunk.groups[groupIndex + 1]"
              :class="{
                'margin-bottom-40': group.uiConfig.isLastGroup
              }"
              :only-one-departure-and-return="onlyOneDepartureAndReturn"
            />
          </template>

          <template
            v-if="displayAddBtn && group.uiConfig.emptyDays && group.uiConfig.emptyDays.length"
            #extra
          >
            <div
              v-for="emptyDay of group.uiConfig.emptyDays"
              :key="emptyDay.data_index"
              class="day-is-empty margin-bottom-40"
            >
              <div class="day-title" style="padding-left: 32px">{{ emptyDay.title }}</div>
              <AddDayBtn
                :data-index="emptyDay.data_index"
                :is-empty-day="true"
                :pre="group"
                :next="chunk.groups[groupIndex + 1]"
                :only-one-departure-and-return="onlyOneDepartureAndReturn"
              />
            </div>
          </template>
        </Group>
      </div>
    </template>
  </div>
</template>

<script>
import Group from './Group'
import AddDayBtn from './AddDayBtn'
import { INSTANCE_TYPE_DICT } from '../utils/index'
import { itineraryVersionDict } from '@activity/pages/package/itinerary/utils/index.js'

export default {
  name: 'ChunkGroups',
  components: {
    AddDayBtn,
    Group
  },
  props: {
    chunks: {
      type: Array,
      required: true
    },
    displayName: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: 'default'
    },
    index: {
      type: Number,
      default: -1
    },
    isLast: {
      type: Boolean,
      default: false
    },
    templates: {
      type: Object,
      required: true
    },
    displayAddBtn: {
      type: Boolean,
      default: false
    },
    nonactivatedGroupKeys: {
      type: Array,
      default: () => []
    },
    scheduleType: {
      type: String,
      default: ''
    },
    itineraryVersion: {
      type: String,
      default: ''
    }
  },
  data() {
    this.INSTANCE_TYPE_DICT = INSTANCE_TYPE_DICT

    return {
      groupList: []
    }
  },
  computed: {
    isTimeline() {
      return this.INSTANCE_TYPE_DICT.timeline === this.type
    },
    currNonactivatedGroupKeys: {
      get() {
        return this.nonactivatedGroupKeys
      },
      set(v) {
        this.$emit('update:nonactivatedGroupKeys', v)
      }
    },
    onlyOneDepartureAndReturn() {
      // > v1 => sub group
      return this.itineraryVersion !== itineraryVersionDict.v1
    }
  },
  methods: {
    getData() {
      const data = this.$refs.group?.map?.((group) => group.getData()) || []

      return _.sortBy(data, ['data_index', 'group_index']).map((group) => {
        // eslint-disable-next-line no-unused-vars
        const { data_index, group_index, ...data } = group

        return data
      })
    },
    async validateForm() {
      return (
        await Promise.all((this.$refs.group || []).map(async (group) => await group.validateForm()))
      ).every((item) => item)
    },
    handleSwitchActive(group) {
      let nonactivatedGroupKeys = []
      // 所有都折叠起来时
      if (this.getNonactivatedStateOnDay(group)) {
        nonactivatedGroupKeys = this.nonactivatedGroupKeys.filter(
          (uuid) => !group.uiConfig.siblingGroupUUIDList.includes(uuid)
        )
      } else {
        nonactivatedGroupKeys = [
          ...new Set([...this.nonactivatedGroupKeys, ...group.uiConfig.siblingGroupUUIDList])
        ]
      }

      this.$emit('update:nonactivatedGroupKeys', nonactivatedGroupKeys)
    },
    getNonactivatedStateOnDay(group) {
      return group.uiConfig.siblingGroupUUIDList.every((uuid) => this.nonactivatedGroupKeys.includes(uuid))
    }
  }
}
</script>

<style lang="scss">
@import '../mixins.scss';

.itinerary-container {
  margin-bottom: 20px;

  @include mixin-timeline-box;

  .ant-form-item:last-child {
    margin-bottom: 0;
  }
}
</style>

<style lang="scss" scoped>
.chunk-groups-container {
  padding: 20px;
  border-radius: 4px;
  background-color: #fff;

  .day-title {
    padding-bottom: 16px;
    margin-bottom: 32px;
    border-bottom: 1px solid #d9d9d9;
    font-weight: 600;
    font-size: 18px;
    color: #001529;
  }

  .day-title-content {
    display: inline-flex;
    align-items: center;
    color: #001529;

    &.first-chunk-title {
      position: relative;
      top: -6px;
      margin-right: 64px;
      .day-title-text {
        font-size: 24px;
        font-weight: 500;
      }
    }
    i {
      font-size: 16px;
      display: inline-block;
      margin-right: 16px;
    }
  }

  .is-last-group,
  .margin-bottom-40 {
    margin-bottom: 40px;
  }
}
</style>
