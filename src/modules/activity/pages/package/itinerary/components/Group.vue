<template>
  <div class="group-container">
    <div
      class="group-content"
      :class="{
        '--is-nonactivated': isNonactivated,
        'timeline-box': isTimeline,
        '--last-day-last-group': schema.uiConfig.isLastDay && schema.uiConfig.isLastGroup
      }"
    >
      <div v-if="displayName" class="__name timeline-title">
        <slot name="name">
          <a-icon
            class="timeline-collapse-icon"
            :type="isNonactivated ? 'down' : 'up'"
            @click="handleSwitchActive"
          />
          <span v-html="fmtName(schema.name)"></span>
        </slot>
      </div>

      <div v-if="isTimeline" class="timeline-grid">
        <svg-icon
          v-if="ICON_NAME_DICT[schema.ref_field_tag]"
          class="__icon"
          :icon-name="ICON_NAME_DICT[schema.ref_field_tag]"
        />
      </div>

      <div class="timeline-content" :class="{ 'is-timeline': isTimeline }" @click="handleGroupSwitchActive">
        <OverflowTextTooltip v-if="previewHTML" class="preview-list">
          <span v-html="previewHTML"></span>
        </OverflowTextTooltip>

        <div class="timeline-actions">
          <svg-icon
            v-if="!schema.uiConfig.onlyOneAssociatedItem && !lockAll"
            class="delete action-item"
            icon-name="simple-trash"
            @click="handleDelete"
          />
          <!-- !schema.uiConfig.onlyOneAssociatedItem -->
          <template v-if="canSort && !lockAll">
            <a-icon
              v-if="!isFirstOnChunk"
              class="action-item direction"
              type="arrow-up"
              @click.stop="handleSort('up')"
            />
            <a-icon
              v-if="
                (!isLastOnChunk ||
                  (!schema.uiConfig.isLastDay && CHUNK_REF.common === schema.uiConfig.chunkGroupName)) &&
                !(schema.uiConfig.isLastDay && schema.uiConfig.isLastGroup)
              "
              class="action-item direction"
              type="arrow-down"
              @click.stop="handleSort('down')"
            />
          </template>
        </div>
        <div v-for="widget of schema.widget_list" :key="widget.uuid" class="widget-item">
          <Widget
            v-if="isValidateComponet(widget.widget_type)"
            v-show="widget.access_permission !== 0"
            ref="widget"
            :group="schema"
            :schema="widget"
            :data-widget-type="widget.widget_type"
            :only-one-widget="schema.widget_list.length === 1"
            :display-name="widget.config.display_name"
            :group-ref-field-tag="schema.ref_field_tag"
            :is-inline="[TEMPLATES_REF.head].includes(schema.ref_field_tag)"
            :departure-type="departureType"
            :attraction-list="attractionList"
            :schedule-type="scheduleType"
            :is-create="schema.create"
            v-bind="$attrs"
            @hook:mounted="widgetMounted"
          >
          </Widget>
        </div>
      </div>
    </div>

    <slot name="footer" :group="schema"></slot>
    <slot name="extra" :group="schema"></slot>
  </div>
</template>

<script>
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'
import Widget from './Widget'
import {
  eventBus,
  ICON_NAME_DICT,
  TEMPLATES_REF,
  EXTRA_TYPE,
  CHUNK_REF,
  ABBR_NAME_DICT
} from '../utils/index'
import { pmsConfirm } from '@activity/utils'
import { scheduleTypeKeyDict } from '../utils/index'

export default {
  name: 'GroupComp',
  components: {
    Widget,
    OverflowTextTooltip
  },
  provide() {
    return {
      setDepartureType: this.setDepartureType,
      setAttraction: this.setAttraction
    }
  },
  inject: {
    provideData: {
      default: {
        disabled: {
          freeText: false,
          all: false
        }
      }
    }
  },
  props: {
    schema: {
      type: Object,
      required: true
    },
    displayName: {
      type: Boolean,
      default: true
    },
    isTimeline: {
      type: Boolean,
      default: false
    },
    canSort: {
      type: Boolean,
      default: false
    },
    isFirstOnChunk: {
      type: Boolean,
      default: false
    },
    isLastOnChunk: {
      type: Boolean,
      default: false
    },
    nonactivatedGroupKeys: {
      type: Array,
      default: () => []
    },
    scheduleType: {
      type: String,
      default: ''
    }
  },
  data() {
    this.ICON_NAME_DICT = ICON_NAME_DICT
    this.TEMPLATES_REF = TEMPLATES_REF
    this.CHUNK_REF = CHUNK_REF

    return {
      form: {},

      previewHTML: '',
      departureType: undefined,
      attractionList: []
    }
  },
  computed: {
    lockAll() {
      return this.provideData?.disabled?.all
    },
    isNonactivated() {
      return this.nonactivatedGroupKeys.includes(this.schema.uuid)
    }
  },
  watch: {
    isNonactivated(v) {
      if (v) {
        this.getPreviewData()
      } else {
        this.previewHTML = ''
      }
    }
  },
  methods: {
    isValidateComponet(widget_type) {
      const scheduleType = this.scheduleType
      if ([13].includes(widget_type) && scheduleType === scheduleTypeKeyDict.date) {
        return false
      } else {
        return true
      }
    },
    setAttraction(v) {
      this.attractionList = v || []
    },
    setDepartureType(v) {
      this.departureType = v
    },
    widgetMounted: _.debounce(async function widgetMounted() {
      await await this.$nextTick()
      this.cacheData = this.getData()
    }, 100),
    getData() {
      const widgets = (this.$refs.widget || []).reduce((acc, widget) => {
        const data = widget.getData()

        if (Array.isArray(data)) {
          return [...acc, ...data]
        }

        return [...acc, data]
      }, [])

      const { data_index, group_index } = this.schema.uiConfig

      return {
        uuid: this.schema.uuid,
        create: this.schema.create,
        group_id: this.schema.id,
        extra_type: EXTRA_TYPE,
        extra_id: +this.$route.query.package_id,
        data_index,
        group_index,
        widgets: widgets.map((widget, index) => {
          return {
            ...widget,
            order: index,
            extra_info: {
              data_index,
              group_index
            }
          }
        })
      }
    },
    async validateForm() {
      return (
        await Promise.all((this.$refs.widget || []).map(async (widget) => await widget.validateForm()))
      ).every((item) => item)
    },
    getPreviewData() {
      // 当收起时，departure里展示time+departure type+meet up point（如果有的话），attraction里展示time+POI名称（多个请用“/”分隔），dining里时间+dining type（如breakfast），return里展示time+return type+return point
      let displayRefFieldTagList = ['itinerary_departure_time']
      switch (this.schema.ref_field_tag) {
        case ABBR_NAME_DICT.departure: {
          displayRefFieldTagList = ['itinerary_departure_time', 'itinerary_departure_map']
          break
        }
        case ABBR_NAME_DICT.attraction: {
          displayRefFieldTagList = ['itinerary_attraction_time', 'itinerary_attraction_poi']
          break
        }
        case ABBR_NAME_DICT.transport: {
          displayRefFieldTagList = ['itinerary_transport_time', 'itinerary_transport_trtype']
          break
        }
        case ABBR_NAME_DICT.dining: {
          displayRefFieldTagList = ['itinerary_dining_time', 'itinerary_dining_dtype']
          break
        }
        case ABBR_NAME_DICT.return: {
          displayRefFieldTagList = ['itinerary_return_time', 'itinerary_return_map']
          break
        }
        case ABBR_NAME_DICT.accommodation: {
          displayRefFieldTagList = ['itinerary_accommodation_time', 'itinerary_accommodation_poi']
          break
        }
      }

      const data = this.$refs.widget.reduce((acc, curr) => {
        if (curr.$refs.instance?.getPreviewData) {
          return [...acc, curr.$refs.instance.getPreviewData()]
        }

        return acc
      }, [])

      this.previewHTML = data.reduce((acc, curr) => {
        if (displayRefFieldTagList.includes(curr.ref_field_tag)) {
          const html = curr.data.reduce((accHtml, widget) => {
            const { text, title } = widget

            if (text && title) {
              accHtml += `
                <label class="preview-item">
                  <span class="__title">${title}: </span>
                  <span class="__text">${text}; </span>
                </label>
              `
            }

            return accHtml
          }, '')

          return acc + html
        }

        return acc
      }, '')
    },
    fmtName(name) {
      return name.replace(/(\/)/g, ' $1<br />')
    },
    handleSort(direction) {
      const { data_index, group_index } = this.schema.uiConfig

      eventBus.$emit('sortDayGroup', {
        direction,
        uuid: this.schema.uuid,
        position: {
          data_index,
          group_index
        }
      })
    },
    handleGroupSwitchActive() {
      if (this.nonactivatedGroupKeys.includes(this.schema.uuid)) {
        this.handleSwitchActive()
      }
    },
    handleSwitchActive() {
      let data = []
      if (this.nonactivatedGroupKeys.includes(this.schema.uuid)) {
        data = this.nonactivatedGroupKeys.filter((item) => item !== this.schema.uuid)
      } else {
        data = [...this.nonactivatedGroupKeys, this.schema.uuid]
      }

      this.$emit('update:nonactivatedGroupKeys', data)
    },
    async handleDelete() {
      let resp
      if (!this.schema.group_id && _.isEqual(this.cacheData, this.getData())) {
        resp = true
      } else {
        resp = await pmsConfirm.call(this, {
          content: __('78878')
        })
      }

      if (resp) {
        const { data_index, group_index } = this.schema.uiConfig
        eventBus.$emit('deleteDayGroup', {
          position: {
            data_index,
            group_index
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.widget-item:last-child .widget-container {
  margin-bottom: 0;
}

.preview-list {
  position: absolute;
  top: 0;
  display: block;
  width: 100%;
  padding: 0 142px 0 32px;
  line-height: 48px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  ::v-deep .preview-item {
    display: contents;
    margin-right: 4px;

    .__title {
      font-weight: 500;
    }
  }
}
</style>
