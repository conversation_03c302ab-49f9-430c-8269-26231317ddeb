<template>
  <div
    v-show="isValidate"
    class="widget-container"
    :class="{
      '--is-inline': calcIsInline,
      '--only-one-widget': onlyOneWidget,
      '--is-block': !calcIsInline
    }"
    :style="getStyle"
  >
    <div v-if="showName" class="__name" :class="{ '--is-required': isRequired(schema) }">
      <slot name="name">
        {{ schema.name }}
        <span v-if="displayReset && !provideData.disabled.all" class="__icon" @click="handleReset">
          <svg-icon icon-name="reset" />
          {{ $t('global_reset') }}
        </span>
      </slot>
    </div>

    <component
      :is="compName"
      v-if="!!compName"
      ref="instance"
      :group="group"
      :config="config"
      :data="schema"
      :disabled="schema.access_permission === 1"
      :group-ref-field-tag="groupRefFieldTag"
      :custom-attr-form-item-render-map="customAttrFormItemRenderMap"
      :departure-type="departureType"
      :attraction-list="attractionList"
      :is-publish-with-ai="isPublishWithAI"
      :schedule-type="scheduleType"
      :is-create="isCreate"
      :item-order="itemOrder"
      :is-inline="calcIsInline"
      :radio-row-block="calcRadioRowBlock"
      v-bind="$attrs"
      @changeCheckList="changeCheckList"
      @toggle="toggleWidget"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import Attribute from './attribute/index.vue'
import DetailTextarea from './detail-textarea/index.vue'
import DepartureMap from './departure-map/index.vue'
import ReturnMap from './return-map/index.vue'
import AttractionPOI from './attraction-poi/index.vue'
import DinningPOI from './dinning-poi/index.vue'
import Accommodation from './accommodation/index.vue'
import TimeDuration from './time-duration/index.vue'
import AvailableTimePicker from './available-time-picker/index.vue'
import DepartureGroup from './departure-group/index.vue'
import ReturnGroup from './return-group/index.vue'

import { refFieldTagDict } from '@activity/pages/package/itinerary/utils/index.js'

export default {
  name: 'ItineraryWidget',
  components: {
    Attribute,
    DetailTextarea,
    DepartureMap,
    ReturnMap,
    AttractionPOI,
    DinningPOI,
    Accommodation,
    TimeDuration,
    AvailableTimePicker,
    DepartureGroup,
    ReturnGroup
  },
  inject: {
    provideData: {
      default: {
        disabled: {
          freeText: false,
          all: false
        }
      }
    }
  },
  props: {
    schema: {
      type: Object,
      required: true
    },
    displayName: {
      type: Boolean,
      default: false
    },
    isInline: {
      type: Boolean,
      default: true
    },
    onlyOneWidget: {
      type: Boolean,
      default: false
    },
    groupRefFieldTag: {
      type: String,
      default: ''
    },
    group: {
      type: Object,
      required: true
    },
    departureType: {
      type: [Number, undefined],
      default: undefined
    },
    attractionList: {
      type: Array,
      default: () => []
    },
    scheduleType: {
      type: String,
      default: ''
    },
    isCreate: {
      type: Boolean,
      default: false
    },
    // 用于 widget:sub-group 顺序使用
    itemOrder: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      customAttrFormItemRenderMap: {
        itinerary_transport_trtype: {
          render: (h, ctx) => {
            let { data, schema } = ctx.props

            return (
              <a-select v-model={data.checkList} allow-clear={!schema.required} style="width: 400px;">
                {schema.attr_value_list.map((option) => (
                  <a-select-option value={option.id}>{option.value}</a-select-option>
                ))}
              </a-select>
            )
          }
        }
      },

      displayReset: false,

      showWidget: true
    }
  },
  computed: {
    calcRadioRowBlock() {
      return [refFieldTagDict.title.headerInfoTimeSelect].includes(this.refFieldTag)
    },
    calcIsInline() {
      return this.isInline && !this.calcRadioRowBlock
    },
    isValidate() {
      return !this.isInvalidate && this.showWidget
    },
    refFieldTag() {
      return this.schema?.ref_field_tag ?? ''
    },

    isInvalidate() {
      const tags = ['itinerary_dining_expense']
      return tags.includes(this.refFieldTag)
    },

    // 特殊样式 ui要求
    getStyle() {
      const { showName, refFieldTag } = this
      // 隐藏itinerary_dining_expense模块
      if (this.isInvalidate) {
        return {
          overflow: 'hidden',
          height: 0,
          marginBottom: 0
        }
      }

      if (refFieldTag === 'itinerary_attraction_reminder' && showName) {
        return {
          padding: '12px 0'
        }
      }
      return {}
    },
    calcDisplayName() {
      return this.displayName && !this.isSubGroupWidgetType
    },
    showName() {
      const { calcDisplayName, attractionList, refFieldTag, departureType } = this
      if (refFieldTag === 'itinerary_attraction_reminder') {
        return calcDisplayName && attractionList.length > 1
      }
      if (refFieldTag === 'itinerary_departure_reminder') {
        return calcDisplayName && typeof departureType === 'number'
      }
      return calcDisplayName
    },
    ...mapGetters(['isPublishWithAI']),
    config() {
      if (toString.call(this.schema.config) === '[object Object]') {
        return this.schema.config
      }

      return JSON.parse(this.schema?.config ?? '{}')
    },
    mapComponents() {
      return {
        1: 'Attribute',
        2: 'DetailTextarea',
        // WidgetTypeMarkdown   = 3
        // WidgetTypeImage      = 4
        // WidgetTypeAddress    = 5
        // WidgetTypeDuration   = 6
        7: 'TimeDuration',
        8: 'DepartureMap',
        9: 'AttractionPOI',
        10: 'Accommodation',
        11: 'DinningPOI',
        12: 'ReturnMap',
        13: 'AvailableTimePicker',
        14: 'DepartureGroup',
        15: 'ReturnGroup'
      }
    },
    compName() {
      return this.mapComponents[this.schema.widget_type]
    },
    isSubGroupWidgetType() {
      return [14, 15].includes(this.schema.widget_type)
    },
    displayResetState() {
      const { widget_type, required } = this.schema

      return widget_type === 7 && !required
    }
  },
  methods: {
    isRequired(schema) {
      const { widget_type, required } = schema
      // 13 类型 后端不能配置成必填项，所以前端给个必填的样式
      const types = [13]
      return !!required || types.includes(widget_type)
    },
    toggleWidget(v) {
      this.showWidget = v
    },
    getData() {
      const values = this.$refs.instance?.getData?.() ?? []
      // if (!values.length) {
      //   return []
      // }
      const common = {
        id: this.schema.id,
        instance_id: this.schema.instance_id || 0,
        ref_field_tag: this.schema.ref_field_tag,
        widget_type: this.schema.widget_type,
        uuid: this.schema.uuid,
        required: this.schema.required
      }
      // can_add 是在 widget(title) 内部实现，保存的时候需要拆分成独立的 title 给回后端
      if (this.config?.can_add) {
        return values.map((value) => ({
          ...common,
          instance_id: value?.instance_id !== undefined ? value.instance_id : common.instance_id,
          attr_values: [value]
        }))
      }

      return {
        ...common,
        attr_values: values
      }
    },
    async validateForm() {
      if (this.isInvalidate) {
        return true
      }
      return this.$refs.instance.validateForm()
    },
    changeCheckList() {
      const { widget_type, attr_value_list, config, required } = this.schema

      // 属性项单选且非必填的需要有重置的功能
      if (widget_type === 1 && !required && !config.multiple_selection && attr_value_list.length > 1) {
        const instance = this.$refs.instance
        let checkList = instance?.form?.checkList ?? []
        if (!Array.isArray(checkList)) {
          checkList = [checkList]
        }

        checkList = checkList.filter((item) => item)

        this.displayReset = !!checkList.length
        return
      }

      this.displayReset = false
    },
    handleReset() {
      this.$refs.instance?.resetValue?.()
    }
  }
}
</script>

<style lang="scss">
.widget-container {
  margin-bottom: 24px;

  &.--is-inline {
    display: flex;
    align-items: center;

    .__name {
      // 与 ./attribute/Compose 的 general-string 样式对应
      position: relative;
      top: -4px;
      line-height: 40px;

      margin-right: 24px;
      margin-bottom: 0;
    }
  }

  &.--is-block {
    .__name {
      margin-bottom: 24px;
    }
  }

  &.--only-one-widget {
    margin-bottom: 0;
  }

  .__name {
    margin-bottom: 12px;

    &.--is-required::before {
      content: '*';
      position: relative;
      top: 2px;
      margin-right: 2px;
      color: #ff5630;
    }
  }

  .__icon {
    display: inline-block;
    margin-left: 6px;
    font-size: 13px;
    color: #437dff;
    cursor: pointer;

    .svg-icon {
      margin-right: 4px;
      filter: invert(45%) sepia(81%) saturate(3136%) hue-rotate(208deg) brightness(101%) contrast(101%);
    }
  }
}
</style>
