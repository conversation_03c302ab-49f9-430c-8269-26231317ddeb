<template>
  <a-form-model ref="form" :model="form" class="attr-wrapper">
    <a-form-model-item :label="$t('78864')" :colon="false">
      <a-select
        v-model="form.place_id"
        class="form-item__ctrl"
        :placeholder="$t('79397')"
        :show-search="true"
        :filter-option="filterOption"
        :allow-clear="true"
        :dropdown-style="dropdownStyle"
        :dropdown-menu-style="dropdownMenuStyle"
        :disabled="isPublishWithAi"
        @search="poiSearch"
        @change="selectChange"
      >
        <template v-if="multiGroup">
          <a-select-opt-group v-for="group of selectGroupMap" :key="group.key">
            <span slot="label">{{ group.label }}</span>
            <a-select-option
              v-for="option in groupOptionList[group.key]"
              :key="option.value"
              class="poi_option_item"
            >
              <OverflowTextTooltip class="overflow-text">
                <span class="poi_option_item__title">{{ option.label }}</span>
              </OverflowTextTooltip>
              <OverflowTextTooltip v-if="getPOIDescription(option)" class="overflow-text">
                <span class="poi_option_item__description">{{ getPOIDescription(option) }}</span>
              </OverflowTextTooltip>
            </a-select-option>
          </a-select-opt-group>
        </template>

        <a-select-option v-for="option in poiOptions" v-else :key="option.value" class="poi_option_item">
          <OverflowTextTooltip class="overflow-text">
            <span class="poi_option_item__title">{{ option.label }}</span>
          </OverflowTextTooltip>
          <OverflowTextTooltip v-if="getPOIDescription(option)" class="overflow-text">
            <span class="poi_option_item__description">{{ getPOIDescription(option) }}</span>
          </OverflowTextTooltip>
        </a-select-option>

        <div slot="dropdownRender" slot-scope="menu">
          <v-nodes :vnodes="menu" />
          <div v-show="poiOptions.length > 0" class="dropdown-btn-wrap" @click="addNew">
            <a-icon class="icon" type="plus"></a-icon>
            <div class="text">{{ $t('78856', { add_new_one: $t('78857') }) }}</div>
          </div>
        </div>
        <div slot="notFoundContent" class="not-found-wrap">
          <svg-icon class="not-found-icon" icon-name="not-found" />
          <div class="not-found-btn-wrap">
            <a-button class="btn" type="link" @click="addNew">
              {{ $t('78856', { add_new_one: $t('78857') }) }}
            </a-button>
          </div>
        </div>
      </a-select>
      <a-tooltip v-if="currPOIDescription">
        <template slot="title">{{ currPOIDescription }}</template>
        <a-icon class="poi-value-description" type="info-circle" />
      </a-tooltip>
    </a-form-model-item>

    <div v-show="showOpreate" class="operate-wrap">
      <!-- <svg-icon class="icon del" icon-name="delete-default" @click="deleteForm" /> -->
      <a-icon type="delete" class="icon del" @click="deleteForm" />
      <a-icon v-show="!isFirst" class="icon accommodation-handle" type="arrow-up" @click="handleSort(-1)" />
      <a-icon v-show="!isLast" class="icon accommodation-handle" type="arrow-down" @click="handleSort(1)" />
    </div>

    <POIButton
      ref="POIButton"
      :show-map-button="showMapButton"
      :current-poi="currentPOI"
      :photo-list="form.images"
      :drawer-title="$t('79397')"
      :searchable="mapSearchable"
      :need-defalut-map-data="true"
      :check-function="checkFunction"
      :hide-map-component="!showMapComponent"
      :upload-photo-title="$t('167232')"
      @photoChange="photoChange"
      @mapPhotoConfirm="mapPhotoConfirm"
      @mapDrawerConfirm="confirm"
      @cancelMap="cancel"
    >
      <div slot="extendInfoWrap" class="attr-extend-info-wrap">
        <a-button class="map-button" @click="toggleMore">
          {{ showMore ? $t('78871') : $t('79394') }}
          <a-icon :type="showMore ? 'up' : 'down'" />
        </a-button>

        <div v-show="showMore">
          <a-form-model-item :label="$t('78865')" :colon="false">
            <a-select
              v-model="form.ho_type"
              class="form-item__ctrl"
              :placeholder="$t('global_please_select')"
              :options="hotelType"
              :allow-clear="true"
            ></a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('78866')" :colon="false">
            <a-select
              v-model="form.star"
              class="form-item__ctrl"
              :placeholder="$t('global_please_select')"
              :options="starOptions"
              :allow-clear="true"
            ></a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('78867')" :colon="false">
            <a-select
              v-model="form.bed_type"
              class="form-item__ctrl"
              :placeholder="$t('global_please_select')"
              :options="bedOptions"
              :allow-clear="true"
            ></a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('78868')" :colon="false">
            <a-select
              v-model="form.sh_type"
              class="form-item__ctrl"
              :placeholder="$t('global_please_select')"
              :options="bathroomOptions"
              :allow-clear="true"
            ></a-select>
          </a-form-model-item>
          <a-form-model-item :colon="false">
            <div slot="label" style="display: inline">
              <span>{{ $t('78869') }}</span>
              <QuestionIcon :message="$t('78931')" placement="top" />
            </div>
            <a-select
              v-model="form.person_num"
              class="form-item__ctrl sp"
              :placeholder="$t('global_please_select')"
              :options="personOptions"
              :allow-clear="true"
            ></a-select>
            <span class="unit-text">{{ $t('78930', { num: '' }) }}</span>
          </a-form-model-item>
        </div>
      </div>
      <CustomNameForm
        ref="customNameForm"
        v-model="customName"
        :ref-value="refValue"
        :disabled="disabledEdit || isPublishWithAi"
        :show-address="showMapComponent"
        :show-tips="showTips"
        :field-title="$t('78864')"
      />
    </POIButton>
  </a-form-model>
</template>
<script>
import POIButton from '../poi-button/index.vue'
import QuestionIcon from '@activity/pages/package/units/components/QuestionIcon.vue'
import CustomNameForm from '../custom-name-form/index.vue'

import { hotelType, starOptions, bedOptions, bathroomOptions, personOptions } from '../../utils/index'
import poiMixin from '../../mixins/poi-mixin'

export default {
  components: {
    POIButton,
    QuestionIcon,
    CustomNameForm,
    VNodes: {
      functional: true,
      render: (h, ctx) => ctx.props.vnodes
    }
  },
  mixins: [poiMixin],
  data() {
    return {
      bathroomOptions,
      bedOptions,
      starOptions,
      hotelType,
      personOptions,
      showMore: false,
      form: {
        place_id: undefined,
        images: [],
        ho_type: undefined,
        star: undefined,
        bed_type: undefined,
        sh_type: undefined,
        person_num: undefined
      }
    }
  },
  inject: {
    provideData: {
      default: {
        vDisabled: {
          lock: false
        }
      }
    }
  },
  watch: {
    currentPOI: {
      deep: true,
      handler(v) {
        this.customName = v.title || ''
      }
    }
  },
  methods: {
    confirmMap(val) {
      this.mapDrawerConfirm(val, '', false, false)
    },
    initForm(value) {
      const data = this.initData(value)
      const {
        place_id = undefined,
        images = [],
        ho_type = undefined,
        star = undefined,
        bed_type = undefined,
        sh_type = undefined,
        person_num = undefined,
        title_multilang
      } = data
      this.form.place_id = this.getDefaultPlaceId(place_id, title_multilang)
      this.form.images = images
      this.form.ho_type = this.initFormData(ho_type)
      this.form.star = this.initFormData(star)
      this.form.bed_type = this.initFormData(bed_type)
      this.form.sh_type = this.initFormData(sh_type)
      this.form.person_num = this.initFormData(person_num)
      this.getPOIFromValue(data, this.form.place_id)
    },
    initFormData(data) {
      if (!data || data === -1) {
        return undefined
      }
      return data
    },
    getFormData() {
      let {
        place_id = '',
        images = [],
        ho_type = '',
        star = '',
        bed_type = '',
        sh_type = '',
        person_num = ''
      } = this.form
      const map = this.initMap(place_id, true)
      return JSON.stringify({
        ...map,
        images,
        ho_type: ho_type || -1,
        star: star || -1,
        bed_type: bed_type || -1,
        sh_type: sh_type || -1,
        person_num: person_num || -1
      })
    },
    checkForm() {
      const {
        place_id = '',
        images = [],
        ho_type = '',
        star = '',
        bed_type = '',
        sh_type = '',
        person_num = ''
      } = this.form
      return !place_id && !images.length && !ho_type && !star && !bed_type && !sh_type && !person_num
    },
    deleteForm() {
      const isEmpty = this.checkForm()
      this.$emit('delete', { isEmpty, index: this.currentIndex })
    },
    toggleMore() {
      this.showMore = !this.showMore
    }
  }
}
</script>
<style scoped lang="scss">
@import '../../mixins.scss';
@include mixin-poi-option-item;

.attr-wrapper {
  background: #fff;
  padding: 8px 24px 20px 24px;
  margin-bottom: 24px;
  max-width: 780px;
  position: relative;
  .operate-wrap {
    position: absolute;
    z-index: 11;
    top: 18px;
    right: 24px;
    display: flex;
    font-size: 16px;
    color: #979797;
    .icon {
      margin-left: 20px;
      cursor: pointer;
      color: #444;
      &.del:hover {
        color: red;
      }

      &.accommodation-handle:hover {
        color: #1890ff;
      }
    }
  }
}
.form-item__ctrl {
  width: 400px;
  &.sp {
    width: 140px;
  }
}
.placement-text {
  padding: 2px 0;
}
.unit-text {
  padding: 0 8px;
}
.map-button {
  margin-right: 24px;
  &.sp {
    margin-top: 16px;
  }
}

.not-found-wrap {
  cursor: default;
  .not-found-btn-wrap {
    color: rgba(0, 0, 0, 0.85);
    .btn {
      padding: 0 5px;
      white-space: pre-wrap;
    }
  }

  .not-found-icon {
    display: block;
    margin: 0 auto 5px auto;
    width: 100px;
    height: 78px;
  }
}

.dropdown-btn-wrap {
  border-top: 1px solid #d9d9d9;
  cursor: pointer;
  display: flex;
  padding: 4px 10px;
  color: #1890ff;
  .icon {
    margin: 4px 4px 0 0;
  }
  .text {
    flex: 1;
  }
}

.attr-extend-info-wrap {
  margin-top: 12px;
}
</style>
