<template>
  <div>
    <AccommodationForm
      v-for="(item, index) in draggableList"
      :key="item.uuid"
      ref="form"
      :data="item"
      :current-index="index"
      :is-last="draggableList.length === index + 1"
      :is-first="index === 0"
      :show-opreate="draggableList.length > 1"
      :is-publish-with-ai="isPublishWithAi"
      @showPhoto="togglePhoto"
      @delete="deleteItem"
      @sort="handleSort"
      @formChange="formChange"
    />
    <div class="map-button-wrap">
      <a-button class="map-button sp" :disabled="!canAdd" icon="plus" @click="addItem">
        {{ $t('78872') }}
      </a-button>
    </div>
    <PhotoDrawer :visible="showPhoto" @cancel="togglePhoto" />
  </div>
</template>
<script>
import PhotoDrawer from '../photo-drawer/index.vue'
import AccommodationForm from '../accommodation-form/index.vue'
import formMixin from '../../mixins/form-mixin'

export default {
  name: 'Accommodation',
  components: {
    PhotoDrawer,
    AccommodationForm
  },
  mixins: [formMixin],
  methods: {
    getPreviewData() {
      const addressList = this.draggableList.reduce((acc, curr) => {
        let value = curr.attr_value_list?.[0]?.value
        if (value) {
          try {
            value = JSON.parse(value)

            if (value?.title_multilang) {
              acc.push(value.title_multilang)
            }
          } catch (e) {
            //
          }
        }

        return acc
      }, [])

      return {
        widget_type: this.data.widget_type,
        ref_field_tag: this.data.ref_field_tag,
        data: [
          {
            title: this.$t('78864'),
            text: addressList.join('/')
          }
        ]
      }
    }
  }
}
</script>

<style scoped lang="scss">
.map-button {
  display: inline-block;
  margin-right: 24px;
}
</style>
