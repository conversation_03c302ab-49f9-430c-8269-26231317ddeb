<template>
  <a-form-model ref="form" :model="form" class="attr-wrapper" :rules="rules">
    <a-form-model-item :label="$t('78854')" :colon="false" prop="place_id" required>
      <a-select
        v-model="form.place_id"
        class="form-item__ctrl"
        :placeholder="$t('79395')"
        :show-search="true"
        :filter-option="filterOption"
        :title="$t('79395')"
        :allow-clear="true"
        :dropdown-style="dropdownStyle"
        :dropdown-menu-style="dropdownMenuStyle"
        :default-active-first-option="false"
        :dropdown-match-select-width="false"
        :class="[currentPOI.source === 0 ? 'custom-poi' : '']"
        :disabled="isPublishWithAi"
        @search="poiSearch"
        @change="selectChange"
      >
        <template v-if="multiGroup">
          <a-select-opt-group v-for="group of selectGroupMap" :key="group.key">
            <span slot="label">{{ group.label }}</span>
            <a-select-option
              v-for="option in groupOptionList[group.key]"
              :key="option.value"
              class="poi_option_item"
            >
              <OverflowTextTooltip class="overflow-text">
                <span class="poi_option_item__title">{{ option.label }}</span>
              </OverflowTextTooltip>
              <OverflowTextTooltip v-if="getPOIDescription(option)" class="overflow-text">
                <span class="poi_option_item__description">{{ getPOIDescription(option) }}</span>
              </OverflowTextTooltip>
            </a-select-option>
          </a-select-opt-group>
        </template>

        <a-select-option v-for="option in poiOptions" v-else :key="option.value" class="poi_option_item">
          <OverflowTextTooltip class="overflow-text">
            <span class="poi_option_item__title">{{ option.label }}</span>
          </OverflowTextTooltip>
          <OverflowTextTooltip v-if="getPOIDescription(option)" class="overflow-text">
            <span class="poi_option_item__description">{{ getPOIDescription(option) }}</span>
          </OverflowTextTooltip>
        </a-select-option>

        <div slot="dropdownRender" slot-scope="menu">
          <v-nodes :vnodes="menu" />
          <div v-show="poiOptions.length > 0" class="dropdown-btn-wrap" @click="addNew">
            <a-icon class="icon" type="plus"></a-icon>
            <div class="text">{{ $t('78856', { add_new_one: $t('78857') }) }}</div>
          </div>
        </div>
        <div slot="notFoundContent" class="not-found-wrap">
          <svg-icon class="not-found-icon" icon-name="not-found" />
          <div class="not-found-btn-wrap">
            <a-button class="btn" type="link" @click="addNew">
              {{ $t('78856', { add_new_one: $t('78857') }) }}
            </a-button>
          </div>
        </div>
      </a-select>

      <a-tooltip v-if="currPOIDescription">
        <template slot="title">{{ currPOIDescription }}</template>
        <a-icon class="poi-value-description" type="info-circle" />
      </a-tooltip>
    </a-form-model-item>
    <a-input
      v-if="refValue"
      v-model="refValue"
      class="form-item__ctrl"
      style="margin-bottom: 24px;"
      disabled
    />
    <div class="poi-btn-wrap">
      <POIButton
        ref="POIButton"
        :current-poi="currentPOI"
        :photo-list="form.images"
        :drawer-title="'Attraction name (POI)'"
        :map-text="mapText"
        :show-map-button="showMapButton"
        :check-function="checkFunction"
        :searchable="isAdd"
        :hide-map-component="!showMapComponent"
        :upload-photo-title="$t('167231')"
        @photoChange="photoChange"
        @mapDrawerConfirm="confirm"
        @mapPhotoConfirm="mapPhotoConfirm"
        @cancelMap="cancel"
      >
        <CustomNameForm
          ref="customNameForm"
          v-model="customName"
          :ref-value="refValue"
          :disabled="disabledEdit || isPublishWithAi"
          :show-address="showMapComponent"
          :show-tips="showTips"
          :field-title="$t('78854')"
        />
        <!-- v-disabled="provideData.vDisabled" -->
      </POIButton>
    </div>
    <!-- <a-form-model-item :label="$t('78858')" :colon="false" prop="ad_type" required>
      <a-radio-group v-model="form.ad_type">
        <a-radio v-for="item in admissionOptions" :key="item.value" :value="item.value">
          {{ item.label }}
        </a-radio>
      </a-radio-group>
    </a-form-model-item> -->
    <div v-show="showOpreate" class="operate-wrap">
      <a-icon type="delete" class="icon del" @click="deleteForm" />
      <a-icon v-show="!isFirst" class="icon attraction-handle" type="arrow-up" @click="handleSort(-1)" />
      <a-icon v-show="!isLast" class="icon attraction-handle" type="arrow-down" @click="handleSort(1)" />
    </div>
  </a-form-model>
</template>
<script>
import POIButton from '../poi-button/index.vue'
import CustomNameForm from '../custom-name-form/index.vue'
import { admissionOptions } from '../../utils/index'
import poiMixin from '../../mixins/poi-mixin'

export default {
  components: {
    POIButton,
    CustomNameForm,
    VNodes: {
      functional: true,
      render: (h, ctx) => ctx.props.vnodes
    }
  },
  inject: {
    provideData: {
      default: {
        vDisabled: {
          lock: false
        }
      }
    }
  },
  mixins: [poiMixin],
  data() {
    const adTypeFn = (r, value, callback) => {
      if (typeof value === 'number') {
        return callback()
      }
      callback(new Error(this.$t('global_select')))
    }
    return {
      admissionOptions,
      form: {
        ad_type: undefined,
        place_id: undefined,
        images: []
      },
      rules: {
        place_id: [
          {
            validator: this.validatorFn
          }
        ],
        ad_type: [
          {
            validator: adTypeFn
          }
        ]
      },
      namRules: {
        name: [
          {
            validator: this.validatorName
          }
        ]
      },
      nameForm: {
        name: '1'
      }
    }
  },
  computed: {
    mapText() {
      const { source } = this.currentPOI
      return source === 0 ? this.$t('78848') : this.$t('78855')
    }
  },
  watch: {
    currentPOI: {
      immediate: true,
      deep: true,
      handler(v) {
        this.customName = v.title || ''
      }
    }
  },
  methods: {
    initForm(value) {
      const data = this.initData(value)
      const { place_id = undefined, images = [], ad_type = 1, title_multilang } = data
      this.form.place_id = this.getDefaultPlaceId(place_id, title_multilang)
      this.form.images = images
      this.form.ad_type = ad_type
      this.getPOIFromValue(data, this.form.place_id)
    },
    getFormData() {
      const {
        form: { ad_type, place_id = '', images }
      } = this
      const map = this.initMap(place_id, true)
      const adObj = {}
      if (ad_type) {
        adObj.ad_type = ad_type
      }
      return JSON.stringify({ ...map, ...adObj, images })
    },
    validatorName(rule, value, callback) {
      const { customName } = this
      if (!customName.trim()) {
        return callback(new Error(this.$t('global_please_input')))
      }
      callback()
    },
    validatorFn(rule, value, callback) {
      const poi = this.currentPOI
      const title = this.customName.trim()
      // 定义不校验location
      if (!title && poi.source === 0) {
        return callback(new Error(this.$t('global_please_input')))
      } else if (!value) {
        return callback(new Error(this.$t('79395')))
      }
      callback()
    },
    checkForm() {
      const { ad_type, place_id, images } = this.form
      return !ad_type && !place_id && !images.length
    },
    deleteForm() {
      const isEmpty = this.checkForm()
      this.$emit('delete', { isEmpty, index: this.currentIndex })
    }
  }
}
</script>
<style scoped lang="scss">
@import '../../mixins.scss';
@include mixin-poi-option-item;

::v-deep .ant-select-dropdown-menu-item-disabled {
  cursor: default;
}

::v-deep .option-hide {
  display: none;
}

.dropdown-btn-wrap {
  border-top: 1px solid #d9d9d9;
  cursor: pointer;
  display: flex;
  padding: 4px 10px;
  color: #1890ff;
  .icon {
    margin: 4px 4px 0 0;
  }
  .text {
    flex: 1;
  }
}

.poi-btn-wrap {
  padding-bottom: 16px;
}

.attr-wrapper {
  background: #fff;
  padding: 5px 24px;
  margin-bottom: 24px;
  max-width: 780px;
  position: relative;
  .operate-wrap {
    position: absolute;
    z-index: 11;
    top: 18px;
    right: 24px;
    display: flex;
    font-size: 16px;
    color: #979797;
    .icon {
      margin-left: 20px;
      cursor: pointer;
      color: #444;
      &.del:hover {
        color: red;
      }
      &.attraction-handle:hover {
        color: #1890ff;
      }
    }
  }
}
.form-item__ctrl {
  width: 400px;
}
.map-button {
  padding: 0;
  margin-right: 24px;
}
.placement-text {
  padding: 2px 0;
}
.not-found-wrap {
  cursor: default;
  .not-found-btn-wrap {
    color: rgba(0, 0, 0, 0.85);
    .btn {
      padding: 0 5px;
      white-space: pre-wrap;
    }
  }

  .not-found-icon {
    display: block;
    margin: 0 auto 5px auto;
    width: 100px;
    height: 78px;
  }
}
</style>
