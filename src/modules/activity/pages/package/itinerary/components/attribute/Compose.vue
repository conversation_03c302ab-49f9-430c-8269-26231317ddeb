<template>
  <a-form-model ref="form" :model="form" class="compose-container" :layout="layout" label-position="right">
    <template v-for="(item, index) of structData">
      <span v-if="item.type === 'string'" :key="index" class="general-string">{{ item.string }}</span>
      <component
        :is="getComponentNameByType(getVariableType(variableMap[item.string].type))"
        v-else
        :key="index + item.string"
        class="variable"
        :class="{
          '--first-variable': index === 0,
          '--last-variable': index === structData.length - 1
        }"
        :data="variableMap[item.string]"
        :type="variableMap[item.string].type"
        :config="config"
        :form-field-val.sync="form[item.string]"
        v-bind="$attrs"
        @change="(val) => handleChange(item.string, val)"
      ></component>
    </template>
  </a-form-model>
</template>

<script>
import StructInput from './StructInput'
import StructSelect from './StructSelect'

export default {
  name: 'Compose',
  components: {
    StructInput,
    StructSelect
  },
  props: {
    structData: {
      type: Array,
      required: true
    },
    layout: {
      type: String,
      default: 'horizontal'
    },
    needChoose: {
      type: Boolean,
      default: true
    },
    config: {
      type: Object,
      required: true
    },
    variableMap: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      form: {},
      variableData: {}
    }
  },
  methods: {
    handleChange(key, value) {
      this.$set(this.variableMap, key, value)
      this.$forceUpdate()
      this.$nextTick(() => {
        this.$refs.form.validateField(key)
      })
    },
    async validateForm() {
      return await new Promise((resolve) => this.$refs.form.validate((valid) => resolve(valid)))
    },
    getVariableType(type) {
      return {
        1: 'select',
        2: 'input'
        // 3: 'link',
        // 4: 'date',
        // 5: 'datetime',
        // 6: 'country',
        // 7: 'city'
      }[type]
    },
    getComponentNameByType(key) {
      return {
        input: 'StructInput',
        select: 'StructSelect'
        // datetime: 'StructDatepicker',
        // date: 'StructDatepicker',
        // link: 'StructLink'
      }[key]
    }
  }
}
</script>

<style lang="scss">
.compose-container {
  display: contents;

  &.ant-form {
    .ant-form-explain {
      position: absolute;
      top: 100%;
      width: max-content;
    }
  }

  .general-string {
    // line-height 预留了表单报错空隙，所以需要 top -4 对齐文本
    // 与 ../Widget 的 __name 样式对应
    position: relative;
    top: -4px;
    line-height: 40px;

    width: max-content;
    white-space: break-spaces;
  }

  .variable {
    display: inline-block;
    margin: 0 8px;

    &.--first-variable {
      margin-left: 0;
    }

    &.--last-variable {
      margin-right: 0;
    }
  }
}
</style>
