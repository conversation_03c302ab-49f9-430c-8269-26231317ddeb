<!-- eslint-disable vue/no-mutating-props -->
<template>
  <div class="option-container" :class="{ '--is-first': index === 0, '--is-last': isLast }">
    <component
      :is="chooseComponentName"
      v-if="displayCheckbox"
      :id="idName"
      style="margin-right: 8px"
      :value="option.id"
      @change="changeValue"
    ></component>
    <!-- variable string -->
    <Compose
      v-if="option.type === 2"
      ref="compose"
      :config="config"
      :struct-data="structData"
      :variable-map.sync="option.variable_map"
      :required="!!schema.required"
    />
    <!-- string -->
    <label v-else :for="idName" class="option-label-warp" :class="{ '--is-vertical': desc }">
      <span class="option-label-text">{{ option.value }}</span>
      <span v-if="desc" class="option-label-desc">{{ desc }}</span>
    </label>
  </div>
</template>

<script>
import {
  parseAttrStrToRender,
  eventBus,
  INJECT_ATTR_OPT_VALUE_EVENT_KEY,
  ATTR_OPT_VALUE_CHANGE_KEY
} from '../../utils/index'
import Compose from './Compose'

export default {
  name: 'ItineraryOption',
  components: {
    Compose
  },
  props: {
    schema: {
      type: Object,
      required: true
    },
    // Current component data
    option: {
      type: Object,
      required: true
    },
    needChoose: {
      type: Boolean,
      default: true
    },
    config: {
      type: Object,
      required: true
    },
    multi: {
      type: Boolean,
      default: false
    },
    index: {
      type: Number,
      default: -1
    },
    isLast: {
      type: Boolean,
      default: false
    },
    checkList: {
      type: Array,
      default: () => []
    },
    customChooseComponentName: {
      type: String,
      default: ''
    },
    desc: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dataVarMap: {},
      structData: '' // string || []
    }
  },
  computed: {
    chooseComponentName() {
      return this.customChooseComponentName
        ? this.customChooseComponentName
        : this.multi
        ? 'a-checkbox'
        : 'a-radio'
    },
    displayCheckbox() {
      if (!this.needChoose) {
        return false
      }

      return this.multi ? true : this.schema?.attr_value_list?.length ?? 0 > 1
    },
    idName() {
      return `option-${this.option.id}-${this.option.uuid}`
    }
  },
  beforeMount() {
    const { data: structData } = parseAttrStrToRender(this.option.value)
    this.$set(this, 'structData', structData)

    const eventKey = `${INJECT_ATTR_OPT_VALUE_EVENT_KEY}_${this.option.ref_field_tag}`
    eventBus.$off(eventKey).$on(eventKey, this.injectAttrOptionValueFunc)
    this.$once('hook:beforeDestroy', () => {
      eventBus.$off(eventKey)
    })
  },
  methods: {
    injectAttrOptionValueFunc({ action }) {
      if (action === 'autoCheck' && !this.checkList.includes(this.option.id)) {
        this.$el.querySelector(`#${this.idName}`).click()
      }
    },
    async validateForm() {
      if (this.option.type === 2) {
        return await this.$refs.compose.validateForm()
      }

      return true
    },
    changeValue() {
      eventBus.$emit(ATTR_OPT_VALUE_CHANGE_KEY, {
        parentRefFieldTag: this.schema.ref_field_tag,
        optionRefFieldTag: this.option.ref_field_tag,
        value: this.option.id
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.option-container {
  .option-label-warp.--is-vertical {
    display: inline-flex;
    gap: 2px;
    flex-direction: column;

    .option-label-text {
      width: fit-content;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.9);
    }

    .option-label-desc {
      color: #a6a6a6;
    }
  }
}
</style>
