<template>
  <a-form-model-item
    class="struct-input-container common-struct-component"
    :class="{
      '--is-inline': isInline,
      '--is-disabled': disabled
    }"
    label=""
    :prop="data.key"
    :rules="rules"
  >
    <a-input
      v-model="currentValue"
      size="small"
      :type="inputType"
      @blur="blurStructInput"
      @change="handleChange"
    ></a-input>

    <a-input
      v-if="refLang && currentRefValue"
      v-model="currentRefValue"
      size="small"
      :type="inputType"
      :placeholder="`Reference Input (${refLang})`"
      :disabled="true"
    ></a-input>
  </a-form-model-item>
</template>

<script>
import Mixin from './mixin'

const isDecimal = (val) =>
  val &&
  String(val).includes('.') &&
  isInteger(String(val).split('.')[0]) &&
  isInteger(String(val).split('.')[1])

const isInteger = _.isInteger

export default {
  name: 'StructInput',
  mixins: [Mixin],
  props: {
    inputType: {
      type: String,
      default: 'textarea'
    }
  },
  data() {
    return {}
  },
  computed: {
    currentRefValue() {
      return this.refData?.values?.[0]?.value ?? ''
    },
    currentValue: {
      get() {
        const value = this.data?.values?.[0]?.value ?? ''

        this.$emit('changeFormValue', value)

        return value
      },
      set(v) {
        this.$emit(
          'change',
          Object.assign({}, this.data, {
            values: this.getStructValues(v)
          })
        )
      }
    },
    rules() {
      if (!this.required) {
        return {
          required: false
        }
      }

      let rules = [{ required: true }]
      const validation = JSON.parse(this.data?.validation ?? '{}')
      const inputType = validation.input_type

      if (inputType === 'int') {
        rules.push({
          trigger: ['blur', 'change'],
          validator(rule, value, callback) {
            return isInteger(this.currentValue) ? callback() : callback('not integer')
          }
        })
      } else if (inputType === 'float') {
        rules.push({
          trigger: ['blur', 'change'],
          validator(rule, value, callback) {
            return isDecimal(this.currentValue) ? callback() : callback('not decimal')
          }
        })
      }

      return {
        trigger: ['blur', 'change'],
        validator: (rule, value, callback) => {
          if (this.isEmptyValue(this.currentValue)) {
            callback(new Error(`${rule.field} is required`))
          } else {
            callback()
          }
        }
      }
    }
  },
  watch: {
    currentValue: {
      immediate: true,
      handler(newV, oldV) {
        this.eventBus.$emit('changeStructInputValue', {
          value: newV,
          oldV,
          key: this.data.key
        })
      }
    }
  },
  methods: {
    getStructValues(v) {
      let values = []
      const id = this.data?.values?.[0]?.id

      if (id) {
        values = [
          {
            ...this.data.values[0],
            create: false,
            value: v
          }
        ]
      } else {
        values = v
          ? [
              {
                create: true,
                value: v
              }
            ]
          : []
      }

      return values
    },
    blurStructInput() {
      this.currentValue = _.trim(this.currentValue)
    },
    handleChange(value) {
      const { key, ref_field_tag } = this.data
      const data = {
        key,
        ref_field_tag,
        type: this.type,
        value: this.getStructValues(this.currentValue)
      }

      this.eventBus.$emit('changeStructValue', data)
      this.$emit('changeStructValue', data)
      this.$emit('changeFormValue', value)
    }
  }
}
</script>

<style lang="scss"></style>
