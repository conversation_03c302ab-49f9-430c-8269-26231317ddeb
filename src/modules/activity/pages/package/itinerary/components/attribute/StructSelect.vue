<template>
  <a-form-model-item
    class="struct-select-container common-struct-component"
    :class="{
      '--is-inline': isInline,
      '--is-disabled': disabled
    }"
    label=""
    :prop="data.key"
    :rules="rules"
  >
    <!-- <LazyLongSelect
      :value="currentValue"
      v-bind="$attrs"
      opt-value-key="id"
      opt-label-key="value"
      :select-hint="$t('type_to_search')"
      :ops-length="20"
      :multiple="multiple"
      :full-opts="options"
      :allow-clear="false"
      :disabled="
        disabled ||
          $attrs.readonly ||
          disabledRefFieldTagList.includes(`${row.ref_field_tag}-${data.ref_field_tag}`)
      "
      @change="handleChange"
    ></LazyLongSelect> -->
    <a-select
      :value="currentValue"
      v-bind="$attrs"
      placeholder="Please select"
      :mode="multiple ? 'multiple' : 'default'"
      :allow-clear="!required"
      :disabled="
        disabled ||
          $attrs.readonly ||
          disabledRefFieldTagList.includes(`${row.ref_field_tag}-${data.ref_field_tag}`)
      "
      @change="handleChange"
    >
      <a-select-option
        v-for="(item, index) in options"
        :key="`${item.id}-${index}`"
        :value="item.id"
        :disabled="Boolean(item.disabled)"
      >
        {{ item.value }}
      </a-select-option>
    </a-select>
  </a-form-model-item>
</template>

<script>
import Mixin from './mixin'
// import LazyLongSelect from '@activity/components/LazyLongSelect.vue'
import { INJECT_ATTR_OPT_VALUE_EVENT_KEY } from '../../utils/index'

export default {
  name: 'StructSelect',
  components: {
    // LazyLongSelect
  },
  mixins: [Mixin],
  inject: {
    beforeChangeValueOnSelect: {
      default: null
    }
  },
  data() {
    return {}
  },
  computed: {
    options() {
      return (this.data.attr_value_list || []).filter((option) => {
        return !this.disabledRefFieldTagList.includes(`${this.data.ref_field_tag}-${option.ref_field_tag}`)
      })
    },
    multiple() {
      return (this.config?.multiple_selection_variables ?? []).includes(this.data.attr_item_id)
    },
    currentValue: {
      // init and forceUpdate
      get() {
        const { data } = this
        let value
        const defaultVal = this.multiple ? [] : undefined

        if (_.isEmpty(data.values)) {
          value = defaultVal
        } else {
          value = this.multiple ? data.values.map((v) => v.id) : data.values[0].id || ''
        }

        this.$emit('update:formFieldVal', value)

        return value
      },
      set(value) {
        this.$emit(
          'change',
          Object.assign({}, this.data, {
            values: this.getStructValues(value)
          })
        )
      }
    },
    rules() {
      if (!this.required) {
        return {
          required: false
        }
      }

      return {
        required: true,
        trigger: ['blur', 'change'],
        validator: _.debounce((rule, value, callback) => {
          if (this.isEmptyValue(this.currentValue)) {
            callback(new Error(`${rule.field} is required`))
          } else {
            callback()
          }
        })
      }
    }
  },
  watch: {
    currentValue: {
      immediate: true,
      handler(newV, oldV) {
        this.eventBus.$emit('changeStructSelectValue', {
          value: newV,
          oldV,
          options: this.options,
          key: this.data.key
        })
      }
    }
  },
  beforeMount() {
    const key = `${INJECT_ATTR_OPT_VALUE_EVENT_KEY}_${this.data.key}`
    this.eventBus.$off(key).$on(key, this.injectAttrValueFunc)
    this.$once('hook:beforeDestroy', () => {
      this.eventBus.$off(key)
    })
  },
  methods: {
    injectAttrValueFunc({ action, value }) {
      if (action === 'assignValue' && value !== undefined) {
        this.currentValue = value
      }
    },
    getStructValues(v) {
      let values = []

      if (v) {
        if (this.multiple) {
          values = this.options
            .filter((option) => v.includes(option.id))
            .map((option) => ({
              ...option,
              create: false
            }))
        } else {
          values = v
            ? [
                {
                  ...this.options.find((option) => v == option.id),
                  create: false,
                  id: v
                }
              ]
            : []
        }
      }

      return values
    },
    handleChange: _.debounce(async function handleChange(value) {
      const { key, ref_field_tag } = this.data
      const index = this.options.findIndex((item) => item.id === value)
      const data = {
        key,
        ref_field_tag,
        type: this.type,
        options: this.options,
        index
      }

      this.eventBus.$emit('changeStructValue', data)
      this.$emit('changeStructValue', data)

      if (typeof this.beforeChangeValueOnSelect === 'function') {
        const response = await this.beforeChangeValueOnSelect({
          oldVal: this.currentValue,
          newVal: value,
          options: this.options,
          data
        })

        if (response && hasOwnProperty.call(response, 'value')) {
          value = response.value
        }
      }

      this.currentValue = value
    })
  }
}
</script>

<style lang="scss" scoped>
.struct-select-container {
  ::v-deep .ant-select {
    width: 100px;
  }

  &.--first-struct {
    ::v-deep .ant-select {
      margin-left: 0;
    }
  }

  &.--last-struct {
    ::v-deep .ant-select {
      margin-right: 0;
    }
  }
}
</style>
