<!-- Attribute layer: Index >>> Option >>> Compose >>> other  -->
<template>
  <a-form-model
    ref="form"
    class="attribute-container"
    :class="[{ '--need-choose': needChoose, '--is-radio-block': radioRowBlock }, refFieldTag]"
    :model="form"
    :rules="rules"
    :layout="layout"
    label-position="right"
  >
    <a-form-model-item prop="checkList" label="" class="attribute-form-item">
      <component
        :is="chooseGroupComponentName"
        v-model="form.checkList"
        :data="form"
        :schema="data"
        v-bind="customRender"
      >
        <template v-for="(option, index) of attrValueList">
          <a-tooltip
            :key="option.id"
            :title="option.tip"
            :overlay-style="{
              maxWidth: '400px'
            }"
          >
            <Option
              ref="option"
              :key="option.id"
              class="attribute-form-item__option"
              :data-ref_field_tag="option.ref_field_tag"
              :option="option"
              :schema="data"
              :index="index"
              :is-last="index === attrValueList.length - 1"
              :config="config"
              :need-choose="needChoose"
              :multi="multi"
              :check-list="multi ? form.checkList : [form.checkList]"
              :desc="getDescByOption(option)"
            />
          </a-tooltip>
        </template>
      </component>
    </a-form-model-item>
  </a-form-model>
</template>

<script>
import Option from './Option'
import { getUuid } from '@activity/utils'
import CustomRender from './CustomRender'
import { scheduleTypeKeyDict } from '../../utils'
import { itineraryVersionDict, refFieldTagDict } from '@activity/pages/package/itinerary/utils/index.js'

export default {
  name: 'ItineraryAttribute',
  inject: {
    setScheduleType: {
      default: () => {}
    },
    getBasicData: {
      default: () => ({})
    }
  },
  components: {
    Option,
    CustomRender
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    layout: {
      type: String,
      default: 'horizontal'
    },
    config: {
      type: Object,
      default: () => ({})
    },
    customAttrFormItemRenderMap: {
      type: Object,
      default: () => ({})
    },
    departureType: {
      type: [Number, undefined],
      default: undefined
    },
    attractionList: {
      type: Array,
      default: () => []
    },
    isCreate: {
      type: Boolean,
      default: false
    },
    itineraryVersion: {
      type: String,
      default: ''
    },
    radioRowBlock: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        checkList: []
      },
      firstWatch: true
    }
  },
  computed: {
    departureTags() {
      const { departureType } = this
      if (typeof departureType === 'number') {
        const v2Tags =
          itineraryVersionDict.v1 !== this.itineraryVersion ? ['itinerary_departure_reminder_ea_time'] : []

        const departureTag = [
          [
            'itinerary_departure_reminder_enter',
            'itinerary_departure_reminder_opera',
            'itinerary_departure_reminder_detail',
            'itinerary_departure_reminder_share',
            'itinerary_departure_eatime_note',
            ...v2Tags
          ],
          [
            'itinerary_departure_reminder_guide',
            'itinerary_departure_reminder_late',
            'itinerary_departure_eatime_note',
            ...v2Tags
          ]
        ]

        // 特殊值，表示包含了 pick-up and meet-up
        // 详见 @activity/pages/package/itinerary/components/departure-group/index.vue#L104
        departureTag[2] = [...new Set([...departureTag[0], ...departureTag[1]])]
        return departureTag[departureType]
      } else {
        return []
      }
    },
    isItineraryDepartureReminder() {
      return this.refFieldTag === 'itinerary_departure_reminder'
    },
    customRender() {
      return this.customAttrFormItemRenderMap[this.data.ref_field_tag]
    },
    multi() {
      return this.config?.multiple_selection
    },
    chooseGroupComponentName() {
      if (this.customRender) {
        return 'CustomRender'
      }

      return this.multi ? 'a-checkbox-group' : 'a-radio-group'
    },
    onlyOneOption() {
      return this.data.attr_value_list.length === 1
    },
    needChoose() {
      const tags = ['itinerary_attraction_reminder']
      const { onlyOneOption, refFieldTag } = this
      const needChooseTag = tags.includes(refFieldTag)
      return !onlyOneOption || needChooseTag
    },
    refFieldTag() {
      /**
       * itinerary_departure_reminder,itinerary_attraction_reminder
       * 这两个部件有特殊逻辑
       * itinerary_departure_reminder：根据departure类型展示不同的选项
       * itinerary_attraction_reminder：attraction 数量大于等于2才展示
       */
      return this.data?.ref_field_tag ?? ''
    },
    attrValueList() {
      const { departureType, data, refFieldTag, attractionList } = this
      const attr_value_list = data.attr_value_list.map((item) => {
        if (!item.uuid) {
          item.uuid = getUuid()
        }
        if (this.isTimeSelect) {
          const { ref_field_tag } = item
          const tips = {
            [scheduleTypeKeyDict.date]: this.$t('103901'),
            [scheduleTypeKeyDict.time]: this.$t('103902')
          }
          item.tip = tips[ref_field_tag] || ''
        }
        return item
      })
      if (refFieldTag === 'itinerary_departure_reminder') {
        if (typeof departureType === 'number') {
          const tag = this.departureTags
          return attr_value_list.filter((item) => tag.includes(item.ref_field_tag))
        }
        return []
      }
      if (refFieldTag === 'itinerary_attraction_reminder') {
        const list = attractionList.length > 1 ? attr_value_list : []
        return list
      }
      return attr_value_list
    },
    rules() {
      return {
        checkList: [
          {
            required: !!this.data.required,
            trigger: ['blur', 'change'],
            validator: (rule, value, callback) => {
              if (
                !!this.data.required &&
                ((this.multi && !this.form.checkList.length) || !this.form.checkList)
              ) {
                callback(new Error('Please select'))
              } else {
                callback()
              }
            }
          }
        ]
      }
    },
    isTimeSelect() {
      return this.refFieldTag === 'itinerary_headinfo_time_select'
    }
  },
  watch: {
    'form.checkList': {
      deep: true,
      handler(v) {
        this.$emit('changeCheckList', v)
        if (this.isTimeSelect) {
          const value = this.attrValueList.find((item) => item.id === v)
          this.setScheduleType(value?.ref_field_tag ?? undefined)
        }
      }
    },
    departureType(newV, oldV) {
      // 第一次选 pick up 的时候需要自动勾选
      if (oldV === undefined && this.justMount && this.isItineraryDepartureReminder) {
        this.autoCheckReconfirmPickUpTimeDebounce()
      }

      if (this.firstWatch) {
        this.firstWatch = false
        return
      }

      if (this.isItineraryDepartureReminder) {
        let checkList
        const validIds = this.attrValueList.map((item) => item.id)
        if (validIds.length) {
          if (this.multi) {
            checkList = this.form.checkList.filter((value) => validIds.includes(value))
          } else {
            checkList = validIds.includes(this.form.checkList) ? this.form.checkList : undefined
          }
        } else {
          checkList = this.multi ? [] : undefined
        }
        this.$set(this.form, 'checkList', checkList)
        this.autoCheckReconfirmPickUpTimeDebounce()
      }
    },
    attractionList(v) {
      if (this.firstWatch) {
        this.firstWatch = false
        return
      }
      if (this.refFieldTag === 'itinerary_attraction_reminder') {
        if (v.length > 1) {
          return
        }
        this.$set(this.form, 'checkList', this.multi ? [] : undefined)
      }
    }
  },
  mounted() {
    if (!this.multi) {
      this.$set(this.form, 'checkList', undefined)
    }
    // 如果选项只有一个就自动勾选上
    // 如果需要展示选项框，则不默认勾选
    if (this.attrValueList.length === 1 && !this.needChoose) {
      const checkVal = this.attrValueList[0].id

      this.$set(this.form, 'checkList', this.multi ? [checkVal] : checkVal)
    }

    if (Array.isArray(this.data.attr_values) && this.data.attr_values.length) {
      const checkList = this.data.attr_values.map((attr) => attr.id)

      this.$set(this.form, 'checkList', this.multi ? checkList : checkList[0])
    } else if (this.isTimeSelect) {
      const value = this.getTimeSelectValue(this.getBasicData(), this.attrValueList)
      this.$set(this.form, 'checkList', value)
    }

    // 等 attrValueList computed 完后判断是否只是挂载而没显示
    setTimeout(() => {
      this.justMount = !this.attrValueList.length
    })
  },
  methods: {
    getTimeSelectValue(timeSlot, list = []) {
      const defaultValue = this.isCreate ? undefined : 0
      const timeslot_type = timeSlot?.timeslot_model?.timeslot_type ?? defaultValue
      if (typeof timeslot_type === 'number') {
        const data = list.find((item) => {
          if (timeslot_type === 1) {
            return item.ref_field_tag === 'itinerary_header_time_sold_by_time'
          }
          return item.ref_field_tag === scheduleTypeKeyDict.date
        })
        return data?.id ?? undefined
      }
      return undefined
    },
    autoCheckReconfirmPickUpTimeDebounce: _.debounce(async function () {
      await this.$nextTick()
      // departureType 0: pick up; 1: meet up; 2: pick up & meet up
      // 详见 @activity/pages/package/itinerary/components/departure-group/index.vue#L104
      if ([0, 2].includes(this.departureType)) {
        const result = this.attrValueList.find(
          (option) => option.ref_field_tag === 'itinerary_departure_reminder_opera'
        )

        if (result) {
          const { id } = result
          this.$set(this.form, 'checkList', this.multi ? [...new Set([...this.form.checkList, id])] : id)
        }
      }
    }, 60),
    getData() {
      let { checkList } = this.form

      if (!Array.isArray(checkList)) {
        checkList = [checkList]
      }

      return this.attrValueList.reduce((acc, curr) => {
        if (checkList.includes(curr.id)) {
          const variable_map = _.cloneDeep(curr.variable_map)
          if (curr.variable_map) {
            const invalid = Object.values(variable_map).some(
              (item) => !(Array.isArray(item.values) && item.values.length)
            )
            if (invalid) {
              return acc
            }

            Object.keys(variable_map).forEach((key) => {
              variable_map[key] = {
                key,
                attr_item_id: variable_map[key].id,
                values: variable_map[key].values
              }
            })
          }

          acc.push({
            ...curr,
            variable_map,
            widget_type: this.data.widget_type
          })
        }

        return acc
      }, [])
    },
    async validateForm() {
      const that = this
      return (
        await Promise.all([
          (() => {
            return new Promise((resolve) => that.$refs.form.validate((validate) => resolve(validate)))
          })(),
          ...(this.$refs.option || []).map(async (option) => await option.validateForm())
        ])
      ).every((item) => item)
    },
    resetValue() {
      this.$set(this.form, 'checkList', this.multi ? [] : undefined)
    },
    getPreviewData() {
      let checkList = this.form.checkList

      !Array.isArray(checkList) && (checkList = [checkList])

      return {
        widget_type: this.data.widget_type,
        ref_field_tag: this.data.ref_field_tag,
        data: [
          {
            title: this.data.name,
            text: this.attrValueList
              .reduce((acc, curr) => {
                if (checkList.includes(curr.id)) {
                  return [...acc, curr.value]
                }

                return acc
              }, [])
              .join(',')
          }
        ]
      }
    },
    getDescByOption(option) {
      switch (option.ref_field_tag) {
        case refFieldTagDict.attrVal.headerTimeSoldByDate:
          return this.$t('171252')
        case refFieldTagDict.attrVal.headerTimeSoldByTime:
          return this.$t('171254')
        default:
          return option.desc || ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.attribute-container {
  display: flex;

  // struct component inline on the general attribute option string
  .common-struct-component {
    &.--is-inline {
      display: inline-flex;
      margin-bottom: 0;
    }
  }

  .attribute-form-item {
    margin-bottom: 0;

    ::v-deep .ant-form-item-children {
      display: flex;
    }
  }

  &.itinerary_headinfo_time_select {
    .option-container {
      margin-bottom: 0px;
    }
  }

  // attribute option
  .option-container {
    margin-bottom: 16px;

    &.--is-last {
      margin-bottom: 8px;

      &.--is-first {
        margin-bottom: 0;
      }
    }
  }

  &.--need-choose ::v-deep {
    .ant-checkbox-group {
      display: flex;
      flex-direction: column;

      .option-container {
        width: max-content;

        &.--last-option {
          margin-bottom: 0;
        }
      }
    }

    .ant-radio-group {
      display: flex;
      flex-wrap: wrap;
      gap: 24px;

      .option-container {
        margin-right: 0;
        margin-bottom: 0;

        &.--last-option {
          margin-right: 0;
        }
      }
    }
  }

  &.--is-radio-block {
    ::v-deep .ant-radio-group {
      flex-direction: column;
    }

    .option-container {
      display: inline-flex;
      flex: 1;
    }
  }
}
</style>
