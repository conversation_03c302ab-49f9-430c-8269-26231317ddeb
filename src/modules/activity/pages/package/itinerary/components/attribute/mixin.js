import { eventBus } from '../../utils/index'

export default {
  inject: ['refLang'],
  props: {
    isInline: {
      type: Boolean,
      default: true
    },
    // 1: 'select',
    // 2: 'input'
    // 3: 'link',
    // 4: 'date',
    // 5: 'datetime',
    // 6: 'country',
    // 7: 'city'
    type: {
      type: Number,
      required: true
    },
    data: {
      type: Object,
      required: true
    },
    required: {
      type: Boolean,
      default: false
    },
    refData: {
      type: Object,
      default: null
    },
    config: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // form 变量对应的 form 值
    formFieldVal: {
      type: [String, Array, Number, null]
    },
    // 当前变量对应的整个属性值配置
    row: {
      type: Object,
      default: () => ({})
    },
    // 需要被锁定的 struct 变量属性项, 如 `${row.ref_field_tag}-${data.ref_field_tag}`
    // 或被过滤的 变量属性项的属性值 option, 如 `${data.ref_field_tag}-${option.ref_field_tag}`
    disabledRefFieldTagList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    this.eventBus = eventBus
    return {}
  },
  methods: {
    isEmptyValue(value) {
      return [null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)
    }
  }
}
