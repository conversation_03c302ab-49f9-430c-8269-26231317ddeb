<template>
  <div class="audio-guide-block">
    <div class="audio-guide-block-title">{{ $t(TEXT_ID_MAP['AUDIO_UPLOAD']) }}</div>
    <div class="audio-guide-block-content">
      {{ $t(TEXT_ID_MAP['AUDIO_UPLOAD_DESC']) }}
    </div>
    <div class="btn-group">
      <a-button @click="handleClickToUploadPage" :disabled="isBtnDisabled">
        <span>{{ $t(TEXT_ID_MAP['AUDIO_UPLOAD_BTN']) }}</span>
      </a-button>
    </div>
  </div>
</template>

<script>
import { TEXT_ID_MAP } from './constant.js'
export default {
  name: 'AudioUploadBlock',
  components: {},
  props: {
    isBtnDisabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      TEXT_ID_MAP
    }
  },
  computed: {
    activityId() {
      return this.$route.params.id
    },
    categoryId() {
      return this.$store.state.categoryInfo?.sub_category_id
    }
  },
  methods: {
    handleClickToUploadPage() {
      const { package_id, ...query } = this.$route?.query || {}
      this.$router.push({
        path: `/act/itineraryAudioUpload/${this.activityId}`,
        query: {
          ...query,
          lang: klook.getEditLang(),
          package_id,
          category_id: this.categoryId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@merchant/style/global.scss';
.audio-guide-block {
  padding: 20px;
  border-radius: 4px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  gap: 8px;

  &-title {
    font-size: 14px;
    font-weight: 400;
    color: black;
  }

  &-content {
    font-size: 14px;
    font-weight: 400;
    color: $text-color-secondary;
  }
}
</style>
