<template>
  <a-form-model ref="form" class="form" :model="form">
    <div class="form-body">
      <a-checkbox v-model="form.checked" class="check-box" disabled></a-checkbox>
      <span class="label">{{ texts[0] }}</span>
      <div class="timer-group">
        <a-form-model-item
          v-for="(item, index) in form.timerList"
          :key="item.id"
          class="timer-group-item"
          :prop="`timeslot_${item.id}`"
          :rules="getRules(item)"
        >
          <div class="timer-group-item-inner">
            <a-time-picker
              v-model="item.value"
              :default-open-value="defaultOpenValue"
              class="timer"
              format="HH:mm"
            />
            <a-icon
              v-show="form.timerList.length > 1"
              class="delete"
              type="minus-circle"
              theme="filled"
              @click="handleDelete(index)"
            />
          </div>
        </a-form-model-item>
        <a-icon class="add-button" type="plus-circle" @click="handleAdd" />
        <span class="dot">{{ texts[2] }}</span>
      </div>
    </div>
    <div class="text">{{ texts[1] }}</div>
  </a-form-model>
</template>
<script>
import { getUuid } from '@activity/utils'
import moment from 'moment'

export default {
  props: {
    data: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      form: {
        checked: true,
        timerList: []
      },
      defaultOpenValue: moment('00:00', 'HH:mm')
    }
  },
  computed: {
    refFieldTag() {
      return this.data?.ref_field_tag ?? ''
    },
    texts() {
      const refFieldTag = this.refFieldTag
      const textsObject = {
        itinerary_departure_time_select: this.$t('103903'),
        itinerary_return_time_select: this.$t('106537')
      }
      const str = _.get(textsObject, refFieldTag, '')
      const reg = /{(time_select_compoment|time_select_component)}[,.，]*/g
      const sReg = /{(time_select_compoment|time_select_component)}/g
      const s = (str.match(reg)?.[0] || '').replace(sReg, '')
      const texts = str.split(reg).filter((item) => {
        return !['time_select_component', 'time_select_compoment'].includes(item)
      })
      return [...texts, s]
    }
  },
  watch: {
    data: {
      immediate: true,
      deep: true,
      handler(v) {
        this.initForm(v)
      }
    }
  },
  methods: {
    getData() {
      const dataId = this.data?.attr_value_list?.[0]?.id || 0
      return [
        {
          create: !dataId,
          is_free_text: true,
          id: dataId,
          widget_type: this.data.widget_type,
          value: this.getValue()
        }
      ]
    },
    getValue() {
      // 去重
      const data = _.unionBy(this.form.checked ? this.form.timerList : [], (item) => {
        const { value, id } = item
        if (!value) {
          return id
        }
        const h = String(value.hours())
        const m = String(value.minutes())
        return `${h}:${m}`
      })
      const values = _.sortBy(data, (item) => +item.value).map((item) => {
        const { value } = item
        if (!value) {
          return {
            h: '',
            m: ''
          }
        }
        const h = String(value.hours())
        const m = String(value.minutes())
        return {
          h: h[1] ? h : `0${h}`,
          m: m[1] ? m : `0${m}`
        }
      })
      return JSON.stringify(values)
    },
    validateForm() {
      return this.$refs.form.validate().catch(() => false)
    },
    initForm(data) {
      const value = JSON.parse(data?.attr_value_list?.[0]?.value ?? '[]')
      if (!value.length) {
        this.form.timerList = [this.getDefaultItem()]
        return
      }
      this.form.timerList = value.map((item) => {
        const { h, m } = item
        return {
          ...item,
          id: getUuid(),
          value: moment(`${h}:${m}`, 'HH:mm')
        }
      })
    },
    getRules(item) {
      const { checked } = this.form
      const value = item.value
      return {
        validator(r, v, callback) {
          if (!checked) {
            return callback()
          }
          if (!value) {
            return callback(new Error('Please select'))
          }
          callback()
        }
      }
    },
    getDefaultItem() {
      return {
        id: getUuid(),
        h: '',
        m: '',
        value: undefined
      }
    },
    handleAdd() {
      this.$refs.form.validate()
      this.form.timerList.push(this.getDefaultItem())
    },
    handleDelete(index) {
      this.form.timerList.splice(index, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.form {
  padding-bottom: 14px;
}

.dot {
  padding: 14px 0 0 6px;
}

.form-body {
  display: flex;

  .check-box {
    margin-top: 8px;
  }
}

.label {
  padding: 8px;
}

.text {
  padding-left: 24px;
}

.timer-group {
  display: inline-flex;
  max-width: 520px;
  flex-wrap: wrap;
  .add-button {
    font-size: 24px;
    color: #437dff;
    cursor: pointer;
    margin: 8px 0 16px 0;
  }

  &-item {
    position: relative;
    width: 120px;
    margin-bottom: 8px;
    margin-right: 8px;

    &-inner {
      position: relative;
      width: 100%;
      .timer {
        width: 100%;
      }

      .delete {
        font-size: 16px;
        color: #ff4d4f;
        position: absolute;
        z-index: 1;
        right: -3px;
        top: -3px;
        cursor: pointer;
        background: #ffffff;
      }
    }
  }
}
</style>
