<template>
  <a-form-model ref="nameForm" :model="nameForm" :rules="namRules">
    <a-alert v-if="showTips" :message="$t('101664')" banner />
    <a-form-model-item :label="fieldTitle" :colon="false" prop="name" required>
      <a-input
        v-model="customName"
        class="form-item__ctrl"
        :placeholder="$t('global_please_input')"
        :disabled="disabled"
      ></a-input>
    </a-form-model-item>
    <a-input
      v-if="refValue"
      :value="refValue"
      class="form-item__ctrl"
      style="margin-bottom: 16px;"
      disabled
    />
    <a-form-model-item v-show="showAddress" :label="$t('act_address')" :colon="false"></a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  name: 'CustomNameForm',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    refValue: {
      type: String,
      default: ''
    },
    disabled: {
      type: <PERSON>olean,
      default: false
    },
    showAddress: {
      type: Boolean,
      default: false
    },
    showTips: {
      type: Bo<PERSON>an,
      default: false
    },
    fieldTitle: {
      type: String,
      default: __('78854')
    }
  },
  data() {
    return {
      nameForm: {
        name: '1'
      },
      namRules: {
        name: [
          {
            validator: this.validatorName
          }
        ]
      }
    }
  },
  computed: {
    customName: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('change', val)
      }
    }
  },
  methods: {
    validatorName(rule, value, callback) {
      const { customName } = this
      if (!customName.trim()) {
        return callback(new Error(this.$t('global_please_input')))
      }
      callback()
    },
    async checkFunction() {
      const form = this.$refs.nameForm
      const validate = await form.validate().catch(() => false)
      return validate
    },
    resetFields() {
      this.$refs.nameForm.resetFields()
    }
  }
}
</script>
<style lang="scss" scoped>
.form-item__ctrl {
  width: 400px;
}
</style>
