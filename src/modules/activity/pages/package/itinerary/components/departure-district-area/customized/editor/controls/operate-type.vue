<template>
  <a-radio-group v-model="currValue" class="operate-type-control" @change="onChange">
    <a-radio-button v-for="item of types" :key="item.value" :value="item.value">
      {{ item.label }}
    </a-radio-button>
  </a-radio-group>
</template>

<script>
export default {
  name: 'MapOperateTypeControl',
  props: {
    value: {
      type: String,
      default: undefined
    },
    types: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      currValue: undefined
    }
  },
  beforeMount() {
    this.currValue = this.value
  },
  methods: {
    onChange(event) {
      this.$emit('change', event.target.value)
    }
  }
}
</script>

<style lang="scss" scoped>
.operate-type-control {
  margin-top: 24px;

  ::v-deep .ant-radio-button-wrapper {
    border: 2px solid #d9d9d9;
    margin-right: -2px;

    &:last-child {
      margin-right: 0;
    }

    &.ant-radio-button-wrapper-checked {
      border-color: #437dff;
    }
  }
}
</style>
