<template>
  <div class="operate-history-control">
    <a-button class="operate-item" :disabled="!undoStackLen" @click="$emit('type', 'undo')">
      <a-icon type="undo" />
      <span>{{ undoText }}</span>
    </a-button>
    <a-button class="operate-item" :disabled="!redoStackLen" @click="$emit('type', 'redo')">
      <a-icon type="redo" />
      <span>{{ redoText }}</span>
    </a-button>
  </div>
</template>

<script>
export default {
  name: 'MapUndoRedoControl',
  props: {
    undoText: {
      type: String,
      default: 'undo'
    },
    redoText: {
      type: String,
      default: 'redo'
    }
  },
  data() {
    return {
      undoStackLen: 0,
      redoStackLen: 0
    }
  }
}
</script>

<style lang="scss" scoped>
.operate-history-control {
  margin: 24px 8px 0 0;
  display: flex;
  color: rgba(0, 0, 0, 0.85);

  .operate-item {
    border: 1px solid #d9d9d9;
    border-right: none;

    &:first-child {
      border-radius: 2px 0 0 2px;
    }

    &:last-child {
      border-radius: 0 2px 2px 0;
    }
  }
}
</style>
