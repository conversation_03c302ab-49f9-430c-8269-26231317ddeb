<template>
  <CommonDrawer
    v-if="currVisible"
    class="departure-customized-map-drawer"
    :visible.sync="currVisible"
    :width="720"
    :title="title"
    :keyboard="false"
  >
    <a-form-model ref="form" :model="form" :rules="rules">
      <a-form-model-item label="" prop="mapData">
        <div class="customized-editor">
          <MapSearch
            :value.sync="form.searchValue"
            show-search-icon
            :placeholder="$t('161227')"
            :show-map-source-selector="false"
            :show-add-custom-location-btn="false"
            @select="onSelect"
          />
          <Map ref="map" v-model="form.mapData" height="480" :can-draw-map="true" @change="onChangeMapData" />
        </div>
      </a-form-model-item>

      <a-form-model-item prop="name" :label="nameLabel">
        <a-input v-model="form.name" :placeholder="$t('161229')"></a-input>
      </a-form-model-item>
    </a-form-model>
    <template #footer>
      <a-button @click="onCancel">{{ $t('global_cancel') }}</a-button>
      <a-button
        type="primary"
        :disabled="
          !form.name || (isCreate && !isModified) || (cacheFormData.name === form.name && !isModified)
        "
        @click="onConfirm"
      >
        {{ $t('global_confirm') }}
      </a-button>
    </template>
  </CommonDrawer>
</template>

<script>
import Map from './map'
import CommonDrawer from '@activity/components/common-drawer/index.vue'
import MapSearch from '@activity/pages/package/extra/components/pickUpLocation/fixedPoint/map-search.vue'
import { formatToFeature } from './utils'

export default {
  name: 'CustomizedEditorDrawer',
  components: {
    CommonDrawer,
    Map,
    MapSearch
  },
  model: {
    prop: 'visible',
    event: 'change'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: undefined
    },
    title: {
      type: String,
      default: ' '
    },
    isCreate: {
      type: Boolean,
      default: false
    },
    nameLabel: {
      type: String,
      default: __('161228')
    }
  },
  data() {
    const validateMapData = (rule, value, callback) => {
      const coordinates = this.form.mapData.geometry?.coordinates

      if (Array.isArray(coordinates) && coordinates.length) {
        callback()
      } else {
        callback(new Error(__('167234')))
      }
    }

    return {
      rules: {
        mapData: [{ validator: validateMapData, trigger: 'change', required: true }],
        name: [{ trigger: 'change', required: true }]
      },
      form: {
        searchValue: undefined,
        mapData: {},
        name: ''
      },
      cacheFormData: {},
      isModified: false
    }
  },
  computed: {
    currVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('change', val)
      }
    },
    disabled() {
      return (
        !this.form.name ||
        (this.isCreate && !this.isModified) ||
        (this.cacheFormData.name === this.form.name && !this.isModified)
      )
    }
  },
  watch: {
    visible(val) {
      val && this.initData()
    }
  },
  methods: {
    initData() {
      if (this.data) {
        this.form.name = this.data.customized_config.customized_area_name
        // 接口数据结构转化为 feature
        this.form.mapData = _.cloneDeep(formatToFeature(this.data))
        this.cacheFormData = _.cloneDeep({
          mapData: this.form.mapData,
          name: this.form.name
        })

        return
      }
      this.$set(this, 'form', this.$options.data().form)
    },
    async onSelect(option) {
      const instance = this.$refs.map.instance
      const results = await instance.geocodeMapInfo({
        placeId: option.google_place_id
      })
      const result = results?.[0] ?? null
      this.setPoint2Center(option, result)
    },
    setPoint2Center(option, result) {
      const instance = this.$refs.map.instance
      if (result?.geometry?.viewport) {
        instance.map.fitBounds(result.geometry.viewport)
      } else {
        const location = instance.formatLatLng(option.location)
        instance.map.setCenter(instance.getGoogleLatLng(location))
        instance.map.setZoom(12)
      }
    },
    onCancel() {
      this.currVisible = false
    },
    async onConfirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.isCreate) {
            // const isEqual = _.isEqual(this.cacheFormData, {
            //   mapData: this.form.mapData,
            //   name: this.form.name
            // })

            // if (isEqual) {
            //   this.onOk()
            //   return
            // }

            this.$confirm({
              title: this.$t('161230'),
              onOk: () => {
                this.onOk()
              }
            })
          } else {
            this.onOk()
          }
        }
      })
    },
    onOk() {
      this.$emit('confirm', this.form)
      this.onCancel()
    },
    onChangeMapData() {
      this.isModified = true
      this.$refs.form.validateField('mapData')
    }
  }
}
</script>

<style lang="scss">
.customized-editor {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 6px;
}
</style>
