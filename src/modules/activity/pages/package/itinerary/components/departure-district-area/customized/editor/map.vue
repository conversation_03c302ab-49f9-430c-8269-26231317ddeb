<template>
  <div class="customized-editor-map" :style="`--height: ${height}px;`">
    <div id="map" ref="map"></div>
  </div>
</template>

<script>
import Vue from 'vue'
import * as turf from '@spatial/helpers'
import center from '@spatial/center'
import * as clipping from 'polygon-clipping'
import Map from '@activity/pages/package/extra/core/marker.js'
import { keyBy } from 'lodash'
import {
  operateTypeDict,
  getDrawingManagerConf,
  MAX_UNDO_AMOUNT,
  PROPERTIES_AREA_ID_KEY,
  PROPERTIES_FEATURE_NAME_KEY,
  PROPERTY_INFO_WINDOW_POS_KEY,
  overrideStyle,
  fullscreenControlOptions
} from './utils'
import { addEventListener } from '@activity/utils/index.js'
import MapOperateTypeControl from './controls/operate-type.vue'
import MapUndoRedoControl from './controls/undo-redo.vue'

export default {
  name: 'CustomizedEditorMap',
  model: {
    prop: 'feature',
    event: 'change'
  },
  inject: ['provideData'],
  props: {
    // GeoJSON
    // { geometry: { coordinates: number[][], type: 'MultiPolygon' }, properties: { customized_group_name: string, id: number }, type: 'Feature' }
    feature: {
      type: Object,
      default: undefined
    },
    basePolygonData: {
      type: Array,
      default() {
        return []
      }
    },
    showBasePolygonData: {
      type: Boolean,
      default: true
    },
    autoCenterMap: {
      type: Boolean,
      default: true
    },
    // 需要减去排除区域
    exclusionPolygonData: {
      type: Array,
      default() {
        return []
      }
    },
    openDiffExclusionPolygonData: {
      type: Boolean,
      default: false
    },
    height: {
      type: [Number, String],
      default: 360
    },
    canDrawMap: {
      type: Boolean,
      default: true
    },
    showHistoryOperations: {
      type: Boolean,
      default: true
    },
    fullscreenControl: {
      type: Boolean,
      default: true
    },
    areaIdKey: {
      type: String,
      default: PROPERTIES_AREA_ID_KEY
    }
  },
  data() {
    this.instance = null
    this.infoWindowInstance = null
    this.recoverMapStateTimer = null
    this.drawingManager = null
    this.listeners = []
    this.controls = []

    return {
      undoStack: [],
      redoStack: [],

      operateType: operateTypeDict.add.value
    }
  },
  beforeMount() {
    if (this.canDrawMap) {
      const keydownEscapeListener = addEventListener(document, 'keydown', this.onKeydownEscape)
      this.listeners.push(keydownEscapeListener)
    } else {
      klook.bus.$on('feature_highlight_by_id', this.eventFeatureHighlightById)
      klook.bus.$on('feature_reset_normal', this.eventResetFeatureState)
      // 临时 feature 事件
      klook.bus.$on('feature_temp_highlight', this.eventFeatureTempHighlight)
      klook.bus.$on('feature_temp_remove', this.eventRemoveTempFeature)
    }
  },
  mounted() {
    this.initMap()
  },
  beforeDestroy() {
    ;[...this.listeners, ...this.controls].forEach((listener) => {
      listener?.remove?.()
    })
    this.recoverMapStateTimer = null
    this.instance.destroy()
    klook.bus
      .$off('feature_highlight_by_id', this.eventFeatureHighlightById)
      .$off('feature_normal', this.eventResetFeatureState)
      .$off('feature_temp_height', this.eventFeatureTempHighlight)
      .$off('feature_temp_remove', this.eventRemoveTempFeature)
  },
  methods: {
    async initMap() {
      const options = {
        options: {
          location: this.provideData?.activityDepartureCity?.location || '',
          fullscreenControl: this.fullscreenControl,
          fullscreenControlOptions
        }
      }
      this.instance = new Map(this.$refs.map, options)
      await this.instance.build({ libraries: 'drawing' })
      this.$emit('mapInitComplete')

      if (this.canDrawMap) {
        const drawingManager = new google.maps.drawing.DrawingManager(getDrawingManagerConf())
        drawingManager.setMap(this.instance.map)
        this.drawingManager = drawingManager

        this.instance.addListener(drawingManager, 'polygoncomplete', this.onPolygonComplete)
        this.drawingManager.setDrawingMode(this.instance.mapSdk.drawing.OverlayType.POLYGON)

        this.initDrawControl()

        if (this.showHistoryOperations) {
          this.initUndoRedoControl()
        }
      }

      // show polygon name
      this.instance.addListener(this.instance.data, 'mouseover', this.onMouseover)
      this.instance.addListener(this.instance.data, 'mouseout', this.onMouseout)

      if (this.feature?.geometry) {
        this.undoStack.push(this.feature)
        const polygons = await this.refreshMap(this.feature)
        this.centerToMap(polygons)
      }
    },
    initDrawControl() {
      const wrap = document.createElement('div')
      const profile = Vue.extend(MapOperateTypeControl)
      let instance = new profile({
        el: wrap,
        propsData: {
          types: operateTypeDict,
          value: this.operateType
        }
      })
      instance.$on('change', (value) => {
        this.operateType = value
      })

      this.controls.push({
        remove: () => {
          instance.$off('change')
          instance.$destroy()
          instance.$el.remove()
          instance = null
          wrap.remove()
        }
      })
      this.instance.map.controls[google.maps.ControlPosition.TOP_CENTER].push(instance.$el)
    },
    initUndoRedoControl() {
      const wrap = document.createElement('div')
      const profile = Vue.extend(MapUndoRedoControl)
      let instance = new profile({
        el: wrap,
        propsData: {
          undoText: this.$t('167273'),
          redoText: this.$t('167274')
        }
      })

      // 更新按钮 disabled 状态
      this.$watch('undoStack', (data) => (instance.undoStackLen = data.length), { deep: true })
      this.$watch('redoStack', (data) => (instance.redoStackLen = data.length), { deep: true })

      instance.$on('type', (type) => (type === 'undo' ? this.onUndo() : this.onRedo()))
      this.controls.push({
        remove: () => {
          instance.$off('type')
          instance.$destroy()
          instance.$el.remove()
          instance = null
          wrap.remove()
        }
      })

      this.instance.map.controls[google.maps.ControlPosition.TOP_RIGHT].push(instance.$el)
    },
    // clear all polygon
    clearPolygonData() {
      this.instance.data && this.instance.data.forEach((feature) => this.instance.data.remove(feature))
    },
    async drawBasePolygonData() {
      if (!(this.showBasePolygonData && this.basePolygonData?.length)) {
        return
      }

      if (!this.autoCenterMap) {
        return
      }

      const polygons = await this.refreshMap()
      this.centerToMap(polygons)
    },
    centerToMap(polygons) {
      const { instance } = this
      if (!this.instance?.mapSdk) {
        return
      }

      const bounds = new instance.mapSdk.LatLngBounds() || []
      polygons.forEach((item) => {
        var multi = item.getGeometry().getArray()
        for (let k = 0; k < multi.length; k++) {
          var polys = multi[k].getArray()
          for (let i = 0; i < polys.length; i++) {
            for (let j = 0; j < polys[i].getLength(); j++) {
              bounds.extend(polys[i].getAt(j))
            }
          }
        }
      })

      instance?.map?.fitBounds(bounds)
    },
    onPolygonComplete(polygon) {
      // 隐藏画的区域
      polygon.setMap(null)

      if (this.currentKeydownEscape) {
        this.currentKeydownEscape = false
        return
      }

      let newPolygon = this.getGeoJsonByGMapPolygon(polygon)
      let newCoordinates = clipping.intersection(newPolygon.geometry.coordinates)
      if (!newCoordinates || !newCoordinates.length) {
        return
      }

      // 已选择的区域坐标
      let oldCoordinates = []
      if (this.feature && this.feature.geometry) {
        oldCoordinates = this.feature.geometry.coordinates
      }

      // 根据相加、相减模式做相应的运算
      if (this.operateType === 'add') {
        newCoordinates = clipping.union(oldCoordinates, newCoordinates)
      } else {
        newCoordinates = clipping.difference(oldCoordinates, newCoordinates)
      }

      // 去除已被其他自定义区域占用的区域
      if (this.openDiffExclusionPolygonData) {
        let exclusionCoordinates = this.exclusionPolygonData.map((v) => v.geometry.coordinates)
        newCoordinates = clipping.difference(newCoordinates, ...exclusionCoordinates)
      }

      if (newCoordinates && newCoordinates.length) {
        const coordinates = turf.multiPolygon(newCoordinates)
        this.updateValue(coordinates)
      } else {
        this.updateValue(null)
      }
    },
    updateValue(value) {
      this.$emit('change', value)
      this.refreshMap(value)

      this.undoStack.push(value)
      if (this.undoStack.length > MAX_UNDO_AMOUNT) {
        this.undoStack.shift()
      }
      this.redoStack = []
    },
    // 刷新地图与添加 features
    async refreshMap(features) {
      if (!Array.isArray(features)) {
        features = features ? [features] : []
      }

      if (this.showBasePolygonData) {
        features.push(...this.basePolygonData)
      }

      this.features = keyBy(features, (feature) => {
        return feature.properties[this.areaIdKey]
      })

      const { instance } = this
      const data = instance.data

      if (data) {
        // clear all polygon
        data.forEach((feature) => instance.data.remove(feature))

        await this.$nextTick()
        const polygons = data.addGeoJson(turf.featureCollection(features))
        instance.data.setMap(instance.map)

        return polygons
      }

      return []
    },
    getGeoJsonByGMapPolygon(polygon) {
      const path = polygon.getPath()
      const coordinates = path.getArray().map((coord) => [coord.lng(), coord.lat()])

      // 两点不成面
      if (coordinates.length < 3) {
        this.$message.warn(this.$t('global_invalid'))
        return {
          geometry: { coordinates: [] }
        }
      }

      if (coordinates[coordinates.lastIndex] !== coordinates[0]) {
        // 封闭 polygon 数组，第一个元素等于最后一个
        coordinates.push(coordinates[0])
      }

      return turf.polygon([coordinates])
    },
    onUndo() {
      const feature = this.undoStack.pop()
      this.redoStack.push(feature)

      const newFeatures = this.undoStack[this.undoStack.length - 1] || null
      this.refreshMap(newFeatures)
      this.$emit('change', newFeatures)
    },
    onRedo() {
      const feature = this.redoStack.pop()
      this.undoStack.push(feature)
      this.refreshMap(feature)
      this.$emit('change', feature)
    },
    onMouseover(event) {
      if (this.canDrawMap) {
        return
      }

      const content = event.feature.getProperty(PROPERTIES_FEATURE_NAME_KEY)
      const id = event.feature.getProperty(this.areaIdKey)
      if (content && id) {
        this.showInfoWindowByFeatureId(id)
        klook.bus.$emit('feature_mouseover', { id })
      }
    },
    onMouseout(event) {
      if (this.canDrawMap) {
        return
      }

      this.resetFeatureState()

      const id = event.feature.getProperty(this.areaIdKey)
      klook.bus.$emit('feature_mouseout', { id })
    },
    getFeatureById(id) {
      let result = null
      ;(this.instance.data || []).forEach((feature) => {
        if (feature.getProperty(this.areaIdKey) === id) {
          result = feature
        }
      })

      return result
    },
    eventFeatureHighlightById(id) {
      this.recoverMapStateTimer && clearTimeout(this.recoverMapStateTimer)
      this.recoverMapStateTimer = null

      this.featureHighlightById(id)
    },
    featureHighlightById(id) {
      this.resetFeatureState()

      const position = this.showInfoWindowByFeatureId(id)
      this.instance.map?.setCenter?.(position)
    },
    showInfoWindowByFeatureId(id) {
      // feature 原生数据
      const originalFeature = this.features[id]

      if (!originalFeature) {
        console.warn('没有找到对应的 feature')
        return
      }

      // google map feature 对象
      const feature = this.getFeatureById(id)
      if (!feature) {
        return
      }

      let position = feature.getProperty(PROPERTY_INFO_WINDOW_POS_KEY)
      if (!position) {
        position = this.calcPolygonCenterByFeature(originalFeature)
        feature.setProperty(PROPERTY_INFO_WINDOW_POS_KEY, position)
      }

      const content = feature.getProperty(PROPERTIES_FEATURE_NAME_KEY)
      if (!this.infoWindowInstance) {
        this.infoWindowInstance = new this.instance.mapSdk.InfoWindow({
          content
        })
      }

      this.instance.data.overrideStyle(feature, overrideStyle)

      this.infoWindowInstance.setContent(content)
      this.infoWindowInstance.setPosition(position)
      this.infoWindowInstance.open(this.instance.map)

      return position
    },
    eventResetFeatureState() {
      this.resetFeatureState()
      this.instance.recoverMapState()
    },
    resetFeatureState() {
      this.instance.data?.revertStyle?.()
      this.infoWindowInstance && this.infoWindowInstance.close(this.instance.map)
    },
    // 高亮临时 feature
    async eventFeatureTempHighlight(feature) {
      if (this.recoverMapStateTimer) {
        clearTimeout(this.recoverMapStateTimer)
        this.recoverMapStateTimer = null
      }

      const polygons = await this.refreshMap(feature)

      const id = feature.properties[this.areaIdKey]
      this.featureHighlightById(id)

      this.centerToMap(polygons)
    },
    eventRemoveTempFeature({ feature } = {}) {
      if (feature) {
        const id = feature.properties[this.areaIdKey]
        this.removeFeatureById(id)
        this.resetFeatureState()
      }

      this.recoverMapStateTimer = setTimeout(() => {
        this.instance.recoverMapState()
      }, 600)
    },
    removeFeatureById(id) {
      const { instance } = this
      ;(instance.data || []).forEach((feature) => {
        // @note feature.properties[PROPERTIES_AREA_ID_KEY] error
        if (feature.Gg?.id === id) {
          instance.data.remove(feature)
        }
      })
    },
    calcPolygonCenterByFeature(feature) {
      try {
        let centerFeature = center(feature)
        let centerCoordinates = centerFeature.geometry.coordinates

        return new this.instance.mapSdk.LatLng(centerCoordinates[1], centerCoordinates[0])
      } catch (e) {
        console.error(e)
      }
      return null
    },
    onKeydownEscape(event) {
      if (event.code === 'Escape' && this.drawingManager) {
        this.currentKeydownEscape = true
        this.drawingManager.setDrawingMode(null)
        setTimeout(() => {
          this.currentKeydownEscape = false
          this.drawingManager.setDrawingMode(this.instance.mapSdk.drawing.OverlayType.POLYGON)
        }, 10)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.customized-editor-map {
  position: relative;
  margin-top: 12px;
}
</style>

<style lang="scss">
.customized-editor-map #map {
  height: var(--height, 360px);

  .gm-style iframe + div {
    border: none !important;
  }

  .gm-style-iw button[aria-label='Close'],
  .gm-style-iw-tc {
    display: none !important;
  }
}
</style>
