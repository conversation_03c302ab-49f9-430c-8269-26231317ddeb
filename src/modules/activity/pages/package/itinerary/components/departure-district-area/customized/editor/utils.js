import { mapEncodingDict, lngLat, gcj2wgs, wgs2gcj } from '@activity/pages/package/units/utils/map/index.js'

export const MAX_UNDO_AMOUNT = 10

export const PROPERTIES_AREA_ID_KEY = 'id'
export const PROPERTIES_FEATURE_NAME_KEY = 'customized_group_name'
export const PROPERTY_INFO_WINDOW_POS_KEY = 'info_window_pos'

export const operateTypeDict = {
  add: { label: __('167271'), value: 'add' },
  subtract: { label: __('167272'), value: 'subtract' }
}

export const polygonOptions = {
  strokeColor: 'black',
  editable: true,
  draggable: true
}

export const circleOptions = {
  editable: true,
  draggable: true,
  fillColor: '#4985e6',
  fillOpacity: 0.5,
  strokeWeight: 1,
  clickable: true,
  zIndex: 1
}

export const overrideStyle = {
  fillColor: '#cc6666'
}

export const fullscreenControlOptions = {
  // google.maps.ControlPosition.RIGHT_BOTTOM
  position: 9
}

export const getDrawingManagerConf = (config = {}) => {
  const { POLYGON, CIRCLE } = google.maps.drawing.OverlayType

  return {
    drawingMode: null,
    drawingControl: false,
    drawingControlOptions: {
      position: google.maps.ControlPosition.TOP_CENTER,
      drawingModes: [POLYGON, CIRCLE]
    },
    polygonOptions,
    circleOptions,
    ...config
  }
}

export const formatToFeature = (data) => {
  return {
    type: 'Feature',
    geometry: data.geom,
    properties: {
      [PROPERTIES_AREA_ID_KEY]: data.area_id,
      [PROPERTIES_FEATURE_NAME_KEY]: data.customized_config.customized_area_name
    }
  }
}

// from @klook/admin-ui/klkAntAreaMap
export function transformLocation(location, type = '') {
  if (type === mapEncodingDict.gcj2wgs) {
    return gcj2wgs(lngLat(location))
  }

  if (type === mapEncodingDict.wgs2gcj) {
    return wgs2gcj(lngLat(location))
  }

  return location
}

export function transformGeomData(data, type) {
  if (type) {
    data = JSON.parse(JSON.stringify(data))
    data.coordinates = data.coordinates.map((coordinate) => {
      return coordinate.map((points) => {
        return points.map((point) => {
          const { lng, lat } = transformLocation({ lng: point[0], lat: point[1] }, type)

          return [lng, lat]
        })
      })
    })
    console.log('# transform', type, data)
  }

  return data
}
