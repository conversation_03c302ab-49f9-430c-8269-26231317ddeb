<template>
  <div class="itinerary-customized-district-area-container custom-explain-wrap">
    <a-spin :spinning="loading">
      <div class="customized-area-content">
        <ItineraryCustomizedAreaList
          ref="list"
          :list="list"
          @highlight="onHighlightListItem"
          @mouseleave="onMouseleaveListItem"
          @check="onCheckArea"
          @addCustomizedArea="onAddCustomizedArea"
        />
        <ItineraryDistrictAreaTable
          ref="table"
          can-edit
          can-delete
          name-property-path="customized_config.customized_area_name"
          :list="tableData"
          :currency="currency"
          @edit="onEdit"
          @delete="onDelete"
          @panTo="onPanToTableItem"
          @mouseout="onMouseoutTableItem"
          @updateCurrency="onUpdateCurrency"
        />
      </div>
    </a-spin>

    <div v-if="errorMsg" class="custom-explain ant-form-explain">{{ errorMsg }}</div>

    <CustomizedEditorMap
      ref="map"
      :can-draw-map="false"
      :show-history-operations="false"
      :base-polygon-data="currPolygonData"
      @mapInitComplete="initData"
    />

    <CustomizedEditorDrawer v-model="editMap.visible" v-bind="editMap" @confirm="onConfirm" />
  </div>
</template>

<script>
import ItineraryCustomizedAreaList from './list'
import ItineraryDistrictAreaTable from '@activity/pages/package/itinerary/components/departure-district-area/table/index.vue'
import CustomizedEditorMap from '@activity/pages/package/itinerary/components/departure-district-area/customized/editor/map.vue'
import CustomizedEditorDrawer from './editor/drawer.vue'
import { ItineraryMapExtraTypeKey } from '@activity/pages/package/itinerary/utils/index.js'
import mixinFieldCompatible from '@activity/pages/package/itinerary/components/departure-district-area/mixins/field-compatible.js'
import { formatToFeature } from './editor/utils'
import * as clipping from 'polygon-clipping'
import { isCNAreaID } from '@activity/pages/package/package_const.js'
import { transformGeomData } from './editor/utils'
import { mapEncodingDict } from '@activity/pages/package/units/utils/map/index.js'

export default {
  name: 'ItineraryCustomizedDistrictArea',
  components: {
    ItineraryCustomizedAreaList,
    ItineraryDistrictAreaTable,
    CustomizedEditorMap,
    CustomizedEditorDrawer
  },
  mixins: [mixinFieldCompatible],
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    // area_data
    value: {
      type: Array,
      required: true
    },
    field: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      loading: true,
      list: [],
      editMap: {
        visible: false,
        title: '',
        data: undefined,
        isCreate: false
      },
      currPolygonData: []
    }
  },
  computed: {
    pkgId() {
      return +this.$route.query.package_id
    },
    tableData() {
      return this.list.filter((item) => item.customized_config.is_selected)
    },
    departureCountry() {
      return this.provideData.departureCountry
    },
    isCNMainland() {
      return this.provideData?.activityDepartureCity?.country_area_id === isCNAreaID
    }
  },
  watch: {
    list: {
      deep: true,
      handler() {
        this.$emit('change', this.list)
      }
    }
  },
  methods: {
    async initData() {
      this.clearFormValidate()
      this.initCurrency() // 需前置获得 currency, 后面初始化默认数据需要自动赋上

      const list = await ajax.get(ADMIN_API.act.query_customized_area, {
        params: {
          extra_type: ItineraryMapExtraTypeKey,
          package_id: this.pkgId
        }
      })

      // interface item {
      //   geom: { coordinates: number[][], type: 'MultiPolygon' },
      //   customized_config: {
      //     customized_area_name: string, is_cn_mainland: boolean, map_encoding: 'wgs' | 'gcj'
      //   },
      //   extra_type: 'itinerary_area',
      //   extra_id: number,
      //   area_id: number
      // }
      this.list = (list || []).map((item) => {
        const res = this.value.find((curr) => curr.area_id === item.area_id)

        if (res) {
          const { is_cn_mainland, map_encoding } = item.customized_config

          let geom
          if (is_cn_mainland && map_encoding === mapEncodingDict.wgs) {
            geom = transformGeomData(item.geom, mapEncodingDict.wgs2gcj)
          } else {
            geom = item.geom
          }

          return {
            geom,
            ...res
          }
        }

        return this.formatItemData(item)
      })

      this.loading = false
      this.refreshCurrPolygonData()
    },
    getPostData() {
      return this.list
        .filter((item) => item.customized_config.is_selected)
        .map((item) => {
          // eslint-disable-next-line no-unused-vars
          const { geom, ...other } = item
          other.customized_config.extra_fee.price_value = +other.customized_config.extra_fee.price_value

          return other
        })
    },
    formatItemData(data, { is_selected = 0 } = {}) {
      data = _.cloneDeep(data)

      return {
        geom: data.geom,
        pick_up_type: 2,
        customized_config: this.getDefaultCustomizedConfig({
          is_selected,
          customized_area_name: data.customized_config?.customized_area_name || ''
        }),
        area_id: data.area_id || 0
      }
    },
    onHighlightListItem({ item }) {
      if (!item.customized_config.is_selected) {
        this.currTempHighlight = formatToFeature(item)
        klook.bus.$emit('feature_temp_highlight', this.currTempHighlight)
      }
    },
    onMouseleaveListItem() {
      if (this.isChecking) {
        return
      }

      this.clearTempFeature()
    },
    existIntersectionInterceptor(data) {
      this.loading = true
      const isExistIntersection = this.isExistIntersection(data)
      this.loading = false
      if (isExistIntersection) {
        this.$warning({
          title: this.$t('161223'),
          content: this.$t('161224'),
          onText: this.$t('161225'),
          onOk: () => {
            this.clearTempFeature({ updateStash: true })
          }
        })

        return false
      }

      return true
    },
    onCheckArea({ checked, item }) {
      this.clearFormValidate()
      this.errorMsg = ''

      if (checked) {
        this.isChecking = true
        this.$refs.map.instance.clearMapStash()

        const valid = this.existIntersectionInterceptor(item)
        if (!valid) {
          return
        }
      }

      this.clearTempFeature({ updateStash: true })
      Object.assign(item.customized_config, { is_selected: Number(checked) })
      this.refreshCurrPolygonData()
    },
    clearTempFeature({ updateStash = false } = {}) {
      klook.bus.$emit('feature_temp_remove', { feature: this.currTempHighlight, updateStash })
      this.currTempHighlight = null
      this.isChecking = false
    },
    isExistIntersection(data) {
      const newCoordinates = data.geom.coordinates
      for (let item of this.tableData) {
        const intersection = clipping.intersection(newCoordinates, item.geom.coordinates)

        if (intersection.length) {
          return true
        }
      }

      return false
    },
    refreshCurrPolygonData() {
      this.currPolygonData = this.list
        .filter((item) => item.customized_config.is_selected)
        .map((item) => formatToFeature(item))

      if (!this.$refs.map) {
        return
      }

      this.$refs.map.clearPolygonData()
      setTimeout(async () => {
        await this.$refs.map.drawBasePolygonData()
        this.$refs.map.instance.stashMapState()
      }, 180)
    },
    onAddCustomizedArea() {
      Object.assign(this.editMap, {
        visible: true,
        data: undefined,
        title: this.$t('161226'),
        isCreate: true
      })
    },
    async onConfirm(data) {
      const { mapData, name } = data
      let area_id = this.editMap.data?.area_id ?? 0

      let geom = mapData.geometry
      const customized_config = {
        customized_area_name: name,
        is_cn_mainland: this.isCNMainland,
        map_encoding: mapEncodingDict.wgs // wgs || gcj
      }
      const response = await ajax.post(ADMIN_API.act.save_customized_area, {
        data: {
          // 如果是国内数据, 则全部 gcj2wgs, 否则不处理
          geom: this.isCNMainland ? transformGeomData(geom, mapEncodingDict.gcj2wgs) : geom,
          customized_config,
          extra_type: ItineraryMapExtraTypeKey,
          extra_id: this.pkgId,
          area_id
        }
      })

      area_id = response.area_id
      if (this.editMap.isCreate) {
        const newItem = this.formatItemData({
          customized_config,
          area_id,
          geom
        })
        const valid = this.existIntersectionInterceptor(newItem)
        if (valid) {
          newItem.customized_config.is_selected = 1
        }
        this.list.push(newItem)
      } else {
        const res = this.list.find((item) => item.area_id === area_id)
        res.customized_config.customized_area_name = name
        res.geom = geom
      }

      this.refreshCurrPolygonData()
      this.$set(this, 'editMap', this.$options.data().editMap)
      setTimeout(() => {
        this.$refs.list.scrollToViewById(area_id)
      }, 100)
    },
    onEdit({ record }) {
      Object.assign(this.editMap, {
        visible: true,
        data: record,
        title: this.$t('161226'),
        isCreate: false
      })
    },
    onDelete({ record }) {
      this.onCheckArea({ checked: false, item: record })
    },
    onPanToTableItem({ record }) {
      klook.bus.$emit('feature_highlight_by_id', record.area_id)
    },
    onMouseoutTableItem({ record }) {
      setTimeout(() => {
        klook.bus.$emit('feature_reset_normal', record.area_id)
      }, 200)
    }
  }
}
</script>

<style lang="scss" scoped>
.itinerary-customized-district-area-container {
  .customized-area-content {
    display: flex;
    gap: 12px;
    height: 320px;
    margin: 10px 0 4px;
  }

  ::v-deep .ant-table-wrapper {
    height: 100%;
    border: 1px solid #f0f0f0;
  }
}
</style>
