<template>
  <div class="itinerary-customized-area-list">
    <header class="header">{{ $t('161213') }}</header>

    <div ref="list" class="checkbox-group">
      <span
        v-for="(item, index) of list"
        :key="index + '_' + item.area_id"
        class="checkbox-item js-action-checkbox-item"
        :data-id="`${item.area_id}`"
      >
        <a-checkbox
          class="checkbox-item-content"
          :value="index + '_' + item.area_id"
          :checked="!!item.customized_config.is_selected"
          @change="(event) => onChange(event, item)"
          @mouseenter="onMouseenter(item)"
          @mouseleave="onMouseleave(item)"
        >
          <OverflowTextTooltip placement="right">
            {{ item.customized_config.customized_area_name }}
          </OverflowTextTooltip>
        </a-checkbox>
      </span>
    </div>

    <a-button type="primary" @click="$emit('addCustomizedArea')">
      <a-icon type="plus" /> {{ $t('161219') }}
    </a-button>
  </div>
</template>

<script>
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'

export default {
  name: 'ItineraryCustomizedAreaList',
  components: {
    OverflowTextTooltip
  },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    onChange(event, item) {
      this.$emit('check', { event, item, checked: event.target.checked })
    },
    onMouseenter(item) {
      this.timer = setTimeout(() => {
        this.$emit('highlight', { item })
      }, 300)
    },
    onMouseleave(item) {
      this.timer && clearTimeout(this.timer)
      this.$emit('mouseleave', { item })
    },
    scrollToViewById(id) {
      const parentEle = this.$el.querySelector('.checkbox-group')
      const childEle = document.querySelector(`.js-action-checkbox-item[data-id='${id}']`)

      if (!(parentEle && childEle)) {
        return
      }

      let y =
        childEle.offsetTop - parentEle.clientHeight + parentEle.clientHeight / 2 + childEle.clientHeight / 2
      parentEle.scrollTo({
        left: 0,
        top: y,
        behavior: 'smooth'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../../../../../../src/assets/css/lib/mixins';

.itinerary-customized-area-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 240px;
  padding: 16px;
  border-radius: 4px;
  background-color: #f0f7ff;

  .header {
    font-size: 16px;
    font-weight: 600;
    color: #000;
    line-height: 1.2em;
  }

  .checkbox-group {
    flex: 1;
    width: 100%;
    height: 250px;
    max-height: 250px;
    padding: 8px 12px 8px 10px;
    background-color: #fff;
    overflow-x: hidden;

    @include mixin-hover-display-scrollbar;
  }

  .checkbox-item-content {
    display: flex;
    align-items: center;
    margin-left: 0;
    margin-bottom: 10px;
    line-height: 1.2em;

    ::v-deep .ant-checkbox + span {
      max-width: 100%;
    }
  }
}
</style>
