import { getEditLang } from '@activity/utils'

export default {
  inject: {
    clearFormValidate: {
      default: null
    },
    provideData: {
      default: () => ({})
    }
  },
  data() {
    return {
      errorMsg: '',
      currency: '',
      // @note itinerary 版数据结构在 this.list
      list: []
    }
  },
  computed: {
    // 兼容 other info 组件的数据字段
    contentField() {
      return getEditLang() === 'en_US' ? 'content_en' : 'content'
    }
  },
  methods: {
    initCurrency() {
      let currency = this.value?.[0]?.customized_config?.extra_fee?.price_currency ?? ''

      if (currency) {
        this.currency = currency
      } else if (this.provideData?.merchantCurrency) {
        this.currency = this.provideData.merchantCurrency
      }
    },
    validateTableForm() {
      return new Promise((resolve) => {
        const form = this.$refs.table?.$refs?.form
        if (form) {
          form.validate(resolve)
        } else {
          resolve(true)
        }
      })
    },
    async validateForm() {
      if (!this.list.length) {
        this.errorMsg = this.$t('Please select')
        return [this.errorMsg]
      }

      await this.validateTableForm()
      let isSelected = false
      for (let item of this.list) {
        const { is_selected, extra_fee } = item.customized_config
        const { price_currency, price_value } = extra_fee || {}

        if (is_selected) {
          isSelected = true
          if (!price_currency) {
            return [this.$t('44913')]
          }

          if (price_value === undefined || price_value === '') {
            return [this.$t('29271')]
          }
        }
      }

      if (!isSelected) {
        this.errorMsg = this.$t('Please select one')
        return [this.errorMsg]
      }

      this.errorMsg = ''
      return [null, true]
    },
    onUpdateCurrency(currency) {
      this.currency = currency
      this.list.forEach((item) => {
        Object.assign(item.customized_config.extra_fee, {
          price_currency: currency
        })
      })
    },
    getDefaultCustomizedConfig({
      is_selected = 1,
      customized_area_name = undefined,
      price_value = '0',
      currency = this.currency
    } = {}) {
      return {
        extra_fee: {
          price_value,
          price_currency: currency || ''
        },
        is_selected,
        customized_area_name
      }
    }
  }
}
