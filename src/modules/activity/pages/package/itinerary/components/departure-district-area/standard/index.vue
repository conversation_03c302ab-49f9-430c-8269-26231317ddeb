<template>
  <div class="itinerary-standard-district-area-container">
    <District
      ref="district"
      v-model="form.value"
      :field="form"
      map-posterior
      :error-msg="errorMsg"
      @change="onChange"
    >
      <template slot="table">
        <ItineraryDistrictAreaTable
          ref="table"
          class="itinerary-district-table"
          :list="list"
          :scroll="{ x: true, y: 278 }"
          :currency="currency"
          can-delete
          @delete="onDeleteItem"
          @panTo="onPanToItem"
          @updateCurrency="onUpdateCurrency"
        />
      </template>
    </District>
  </div>
</template>

<script>
import District from '@activity/pages/package/extra/components/pickUpLocation/district/index.vue'
import ItineraryDistrictAreaTable from '@activity/pages/package/itinerary/components/departure-district-area/table/index.vue'
import mixinFieldCompatible from '@activity/pages/package/itinerary/components/departure-district-area/mixins/field-compatible.js'

export default {
  name: 'ItineraryStandardDistrictArea',
  components: {
    District,
    ItineraryDistrictAreaTable
  },
  mixins: [mixinFieldCompatible],
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    // area_data
    value: {
      type: Array,
      default: undefined
    }
  },
  data() {
    return {
      form: {
        // @note
        // other info 数据结构, 即 { id: number [contentField]: string }
        // 更新时通过 onChange 同步为 this.list(area_data) 数据
        value: []
      }
    }
  },
  beforeMount() {
    this.initData()
  },
  methods: {
    initData() {
      this.initCurrency()
      this.list = this.value || []

      Object.assign(this.form, {
        value: this.list.map((item) => {
          let { area_id, ...content } = item

          return {
            id: area_id,
            [this.contentField]: JSON.stringify({ area_id, ...content })
          }
        })
      })
    },
    getPostData() {
      return this.list.map((item) => {
        item = _.cloneDeep(item)
        delete item[this.contentField]
        item.customized_config.extra_fee.price_value = +item.customized_config.extra_fee.price_value

        return item
      })
    },
    onChange(data) {
      this.clearFormValidate()
      this.errorMsg = ''

      this.list = data.map((item) => {
        const content = JSON.parse(item[this.contentField])

        const res = this.list.find((curr) => curr.area_id === content.area_id)

        let customized_config
        if (res && res.customized_config) {
          customized_config = res.customized_config
        } else {
          customized_config = this.getDefaultCustomizedConfig()
        }

        return {
          ...item,
          ...content,
          customized_config
        }
      })

      this.$emit('change', this.list)
    },
    onDeleteItem({ record }) {
      this.$refs.district.deleteItemById(record.area_id)
    },
    onPanToItem({ record }) {
      this.$refs.district.onPanToItemById(record.area_id)
    }
  }
}
</script>

<style lang="scss" scoped>
.itinerary-standard-district-area-container {
  .itinerary-district-table {
    border: 1px solid #f0f0f0;
  }

  .itinerary-district-table,
  ::v-deep .district-wrap {
    height: 320px;
  }
}
</style>
