<template>
  <div>
    <span class="change-currency-wrap">
      <span>{{ $t('161215') }}</span>
      <span v-if="currency">({{ currency }})</span>
      <a-icon v-if="canEdit" type="edit" @click="changeCurrency" />
    </span>

    <a-modal
      v-model="visible"
      :title="$t('161220')"
      :ok-text="$t('161221')"
      :cancel-text="$t('161222')"
      @ok="onOk"
    >
      <a-select v-model="currCurrency" class="change-currency-selector" :options="options"></a-select>
    </a-modal>
  </div>
</template>

<script>
import { SUPPORT_CURRENCY_SYMBO } from '@activity/pages/package/package_const.js'

export default {
  name: 'ChangeCurrency',
  model: {
    prop: 'currency',
    event: 'change'
  },
  props: {
    currency: {
      type: String,
      default: ''
    },
    canEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      currCurrency: undefined
    }
  },
  computed: {
    options() {
      return SUPPORT_CURRENCY_SYMBO.map((item) => item[0])
        .sort()
        .map((v) => ({
          label: v,
          value: v
        }))
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.currCurrency = this.currency
      }
    }
  },
  methods: {
    changeCurrency() {
      this.visible = true
    },
    onOk() {
      this.visible = false
      this.$emit('change', this.currCurrency)
    }
  }
}
</script>

<style lang="scss" scoped>
.change-currency-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 6px;
}

.change-currency-selector {
  width: 100%;
}
</style>
