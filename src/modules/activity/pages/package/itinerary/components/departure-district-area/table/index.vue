<template>
  <div class="district-area-table custom-explain-wrap">
    <a-form-model ref="form" :model="form">
      <a-table
        :columns="columns"
        :data-source="list"
        :pagination="false"
        :scroll="scroll"
        :row-key="rowKeyFn"
      >
        <div
          slot="scope"
          slot-scope="text, record"
          class="point-item-location"
          @mouseout="onMouseout(record)"
        >
          <OverflowTextTooltip
            placement="bottom"
            overlay-class-name="common-tooltip-style"
            style="display: block;"
            :delay="200"
          >
            <span style="cursor: pointer;" @click="handlePanToLocation(record)">
              {{ getName(record) }}
            </span>
          </OverflowTextTooltip>
        </div>

        <a-form-model-item
          slot="titleFees"
          prop="currency"
          :rules="[{ required: !!list.length, trigger: 'change', message: $t('44913') }]"
        >
          <ChangeCurrency
            :currency="form.currency"
            class="table-title-fees"
            :can-edit="!!list.length"
            @change="onChangeCurrency"
          />
        </a-form-model-item>

        <div slot="fee" slot-scope="text, record, index">
          <a-form-model-item
            :prop="`list.${index}.customized_config.extra_fee.price_value`"
            :rules="[{ required: true, trigger: ['change', 'blur'], message: $t('29271') }]"
          >
            <a-input
              v-model="record.customized_config.extra_fee.price_value"
              @keyup.native="handleRound(record.customized_config.extra_fee)"
            />
          </a-form-model-item>
        </div>

        <div slot="action" slot-scope="text, record, index" class="point-item-action">
          <a-button
            v-if="canEdit"
            type="link"
            style="color: #437DFF;"
            @click="handleItem('edit', record, index)"
          >
            {{ $t('161217') }}
          </a-button>
          <a-button
            v-if="canDelete"
            type="link"
            style="color: #ff7874;"
            @click="handleItem('delete', record, index)"
          >
            {{ $t('161218') }}
          </a-button>
        </div>
      </a-table>
    </a-form-model>
  </div>
</template>

<script>
import { round2Decimal } from '@activity/utils'
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'
import ChangeCurrency from './change-currency'

export default {
  name: 'ItineraryDistrictAreaTable',
  components: {
    OverflowTextTooltip,
    ChangeCurrency
  },
  inject: {
    refreshWrapCheckStatus: {
      default: null
    },
    clearFormValidate: {
      default: null
    }
  },
  props: {
    list: {
      type: Array,
      required: true
    },
    currency: {
      type: String,
      default: ''
    },
    scroll: {
      type: Object,
      default: () => ({ x: 600, y: 280 })
    },
    canEdit: {
      type: Boolean,
      default: false
    },
    canDelete: {
      type: Boolean,
      default: false
    },
    defaultSelected: {
      type: Boolean,
      default: false
    },
    namePropertyPath: {
      type: String,
      default: 'location_name'
    }
  },
  computed: {
    form: {
      get() {
        return {
          list: this.list,
          currency: this.currency
        }
      }
    },
    columns() {
      const columns = [
        {
          title: __('161214'),
          dataIndex: this.namePropertyPath,
          scopedSlots: { customRender: 'scope' }
        },
        {
          slots: { title: 'titleFees' },
          dataIndex: 'customized_config.extra_fee.price_value',
          width: 200,
          scopedSlots: { customRender: 'fee' }
        }
      ]

      if (this.canEdit || this.canDelete) {
        columns.push({
          title: __('161216'),
          width: this.canEdit && this.canDelete ? 200 : 100,
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }

      return columns
    }
  },
  methods: {
    rowKeyFn(record) {
      return record.area_id
    },
    getName(record) {
      return _.get(record, this.namePropertyPath, '-')
    },
    handleRound(data) {
      this.refreshWrapCheckStatus?.()
      this.clearFormValidate?.()

      Object.assign(data, {
        price_value: round2Decimal(data.price_value)
      })
    },
    handlePanToLocation(record) {
      this.$emit('panTo', { record })
    },
    handleItem(type, record, index) {
      this.$emit(type, { record, index })
    },
    onMouseout(record) {
      this.$emit('mouseout', { record })
    },
    onChangeCurrency(currency) {
      this.$emit('updateCurrency', currency)
      this.clearFormValidate?.()
      setTimeout(() => {
        this.refreshWrapCheckStatus?.()
        this.$refs.form.validateField('currency')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.district-area-table {
  flex: 1;

  ::v-deep .ant-table-placeholder {
    border-bottom: none;
  }

  .point-item-action {
    margin-left: -15px;
  }

  .point-item-location {
    display: grid;
  }
}
</style>
