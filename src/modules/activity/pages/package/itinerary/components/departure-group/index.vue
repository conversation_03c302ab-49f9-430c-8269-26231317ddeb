<template>
  <div class="departure-group-container">
    <a-alert v-if="isItineraryV3" class="departure-group-alert" :message="$t('161206')" type="info" />
    <OrderedList
      ref="orderedList"
      :list="list"
      :add-btn-text="$t('global_button_add')"
      @addNew="addNew"
      @delete="onDelete"
    >
      <template #content="{ item, index }">
        <DepartureMap
          :item-field="itemFieldDict.map"
          :data="item.map"
          :item-order="index"
          :sub-widget-data="item"
          v-bind="$attrs"
          @hook:mounted="onMountedMapDebounce"
        />
        <TimePeriod
          v-if="scheduleType === scheduleTypeKeyDict.date"
          display-reset
          :item-field="itemFieldDict.time"
          :data="item.time"
          :item-order="index"
          :sub-widget-data="item"
          v-bind="$attrs"
        />
        <AvailableTimePicker
          v-if="scheduleType === scheduleTypeKeyDict.time"
          :item-field="itemFieldDict.time_select"
          :data="item.time_select"
          :item-order="index"
          :sub-widget-data="item"
          v-bind="$attrs"
        />
        <DetailTextarea
          :item-field="itemFieldDict.detail_multilang"
          :data="item.detail_multilang"
          :item-order="index"
          :sub-widget-data="item"
          v-bind="$attrs"
        />
      </template>
    </OrderedList>
  </div>
</template>

<script>
import DepartureMap from '../departure-map/index'
import TimePeriod from '../time-period/index'
import AvailableTimePicker from '../available-time-picker/index'
import DetailTextarea from '../detail-textarea/index'
import subGroupMixin from '../../mixins/sub-group-mixin'
import { itineraryVersionDict } from '@activity/pages/package/itinerary/utils/index.js'

export default {
  name: 'DepartureGroup',
  components: {
    DepartureMap,
    TimePeriod,
    AvailableTimePicker,
    DetailTextarea
  },
  inject: ['setDepartureType', 'provideData'],
  provide() {
    return {
      // 劫持重新实现父级的 setDepartureType 方法
      setDepartureType: this.customSetDepartureType
    }
  },
  mixins: [subGroupMixin],
  data() {
    this.widgetSchemaDict = {
      time_select: {
        name: 'Time_Duration',
        ref_field_tag: 'itinerary_departure_time_select'
      },
      map: {
        name: 'Departure_Map',
        ref_field_tag: 'itinerary_departure_map',
        widget_type: 8
      },
      detail_multilang: {
        name: 'Detail',
        ref_field_tag: 'itinerary_departure_detail'
      }
    }
    return {
      initDepartureType: false
    }
  },
  computed: {
    itineraryVersion() {
      return this.provideData.itineraryVersion
    },
    isItineraryV3() {
      return this.itineraryVersion === itineraryVersionDict.v3
    }
  },
  methods: {
    onDelete() {
      setTimeout(this.customSetDepartureType, 100)
    },
    // 当所有 DepartureMap 挂载完需要计算 departure type 值
    onMountedMapDebounce: _.debounce(function onMountedMapDebounce() {
      !this.initDepartureType && this.customSetDepartureType()
      this.initDepartureType = true
    }, 300),
    customSetDepartureType() {
      let types = this.getData().reduce((acc, curr) => {
        const type = JSON.parse(curr.value).map.type

        return type === undefined ? acc : [...acc, type]
      }, [])

      const existPickUp = types.includes(0)
      const existMeetUp = types.includes(1)
      // 在原有逻辑基础上，增加 departureType 2 -> pick up & meet up 类型
      // @activity/pages/package/itinerary/components/attribute/index.vue#L126
      const value = existPickUp && existMeetUp ? 2 : existPickUp ? 0 : existMeetUp ? 1 : undefined
      this.setDepartureType(value)
    }
  }
}
</script>

<style lang="scss" scoped>
.departure-group-alert {
  margin-bottom: 12px;
}
</style>
