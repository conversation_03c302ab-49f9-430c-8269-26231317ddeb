<template>
  <div class="itinerary-departure-map-container">
    <a-form-model ref="form" :model="form" :rules="rules">
      <a-form-model-item class="custom-explain-wrap" :colon="false" prop="type" required>
        <span slot="label">
          {{ $t('78842') }}
        </span>
        <SelectType v-model="form.type" @change="onChangeType" />
      </a-form-model-item>

      <component
        :is="mapCompName"
        v-if="!!mapCompName && showMapComp"
        ref="map"
        :key="mapCompName"
        :value="form.area_data"
        :is-customized-area="form.is_customized_area"
        :is-publish-with-ai="isPublishWithAi"
        :location-name-ref-value="currLocationNameRefValue"
      />

      <a-form-model-item
        v-if="!isItineraryV3 && form.type === 1"
        :label="$t('78843')"
        prop="value"
        :colon="false"
        required
      >
        <a-input
          v-model="customName"
          class="form-item__ctrl"
          :placeholder="$t('global_please_input')"
          :allow-clear="true"
          :disabled="isPublishWithAi"
        ></a-input>
        <div v-if="refValue">
          <a-input v-model="refValue" class="form-item__ctrl" style="margin: -10px 0 16px;" disabled />
        </div>
      </a-form-model-item>

      <a-form-model-item v-show="[0, 1].includes(form.type)" prop="place_id">
        <POIButton
          class="map-poi-wrap"
          :current-poi="currentPOI"
          :photo-list="form.images"
          :drawer-title="$t('78843')"
          :map-text="$t('78848')"
          :custom-name="customName"
          :hide-photo="provideData.disabled.all"
          :disabled-map-button="provideData.disabled.all || provideData.disabled.freeText"
          :invalidate-poi="invalidatePoi"
          :upload-photo-title="$t('167230')"
          :hide-map="hideMap"
          @photoChange="photoChange"
          @mapDrawerConfirm="mapDrawerConfirm"
          @mapPhotoConfirm="mapPhotoConfirm"
        >
          <span slot="mapButton" slot-scope="scope">
            <svg-icon class="poi-button-icon" icon-name="icon-travel-location-xs" />{{ scope.mapButtonText }}
          </span>
        </POIButton>

        <PickUpTable v-if="!isItineraryV3 && form.type === 0" :list="extraPickInfoList" />
      </a-form-model-item>
    </a-form-model>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import POIButton from '../poi-button/index.vue'
import SelectType from './select-type.vue'
import poiMixin from '../../mixins/poi-mixin'
import PickUpTable from '@activity/pages/package/itinerary/components/pickUpTable/index.vue'
import DepartureMeetUp from '@activity/pages/package/itinerary/components/departure-meet-up/index.vue'
import DeparturePickUp from '@activity/pages/package/itinerary/components/departure-pick-up/index.vue'
import { itineraryVersionDict } from '@activity/pages/package/itinerary/utils/index.js'
import { parseJsonString } from '@activity/pages/package/itinerary/utils/index.js'

export default {
  name: 'ItineraryDepartureMap',
  inject: ['setDepartureType', 'provideData'],
  components: {
    POIButton,
    PickUpTable,
    SelectType,
    DepartureMeetUp,
    DeparturePickUp
  },
  mixins: [poiMixin],
  props: {
    subWidgetData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    const typeFn = (r, value, callback) => {
      if (typeof value === 'number') {
        return callback()
      }
      callback(new Error(this.$t('global_select')))
    }
    const validatorPOI = (rule, value, callback) => {
      if (this.$refs.map) {
        return callback()
      }

      if (!this.hideMap && !value && this.form.type === 1) {
        this.invalidatePoi = true
        return callback(new Error(' '))
      }

      this.invalidatePoi = false
      callback()
    }
    return {
      form: {
        type: undefined,
        place_id: undefined,
        images: [],
        // 跳过校验用
        value: 1,
        // @note
        // 仅做初始化用，获取新数据请使用 this.$refs.map.getPostData()
        // 需要兼容 other info 的数据格式，所以这里采用脱离而非双向绑定的形式
        area_data: [],
        is_customized_area: undefined
      },
      rules: {
        place_id: [
          {
            validator: validatorPOI,
            trigger: ['change', 'blur']
          }
        ],
        value: [
          {
            validator: this.validatorFn,
            trigger: ['change', 'blur']
          }
        ],
        type: [
          {
            validator: typeFn
          }
        ]
      },
      invalidatePoi: false,
      v2CompatibilityV3: false
    }
  },
  computed: {
    ...mapGetters(['itineraryInstancesDataByIdGetters']),
    currRefAttrValues() {
      const data = this.itineraryInstancesDataByIdGetters(this.subWidgetData.instance_id)
      if (data) {
        return parseJsonString(data.attr_values?.[0]?.value, null)
      }

      return null
    },
    currLocationNameRefValue() {
      const refData = this.currRefAttrValues?.map || {}

      if (this.v2CompatibilityV3) {
        return refData.title_multilang || ''
      }

      return refData.area_data?.[0]?.location_name || ''
    },
    extraPickInfoList() {
      return this.provideData.extraPickInfoList
    },
    mapCompName() {
      if (this.form.type === undefined) {
        return ''
      }

      return this.form.type === 1 ? 'DepartureMeetUp' : 'DeparturePickUp'
    },
    activityDepartureCity() {
      return this.provideData?.activityDepartureCity || {}
    },
    editMapPermission() {
      // 1 有权限, 2 没权限
      return this.provideData.editMapPermission
    },
    itineraryVersion() {
      return this.provideData.itineraryVersion
    },
    isItineraryV3() {
      return this.itineraryVersion === itineraryVersionDict.v3
    },
    showMapComp() {
      return this.isItineraryV3 && this.editMapPermission === 1
    },
    hideMap() {
      return this.isItineraryV3 || this.form.type === 0
    }
  },
  watch: {
    'form.type': {
      immediate: true,
      handler(v) {
        this.setDepartureType(v)
      }
    },
    customName() {
      this.$refs.form.validateField('value')
    }
  },
  methods: {
    getPreviewData() {
      let data = []
      if (this.form.type) {
        data.push({
          title: this.$t('78842'),
          text: this.$t('78889')
        })

        if (this.customName) {
          data.push({
            title: this.$t('78843'),
            text: this.customName
          })
        }
      } else {
        data.push({
          title: this.$t('78842'),
          text: this.$t('78888')
        })
      }

      return {
        widget_type: this.data.widget_type,
        ref_field_tag: this.data.ref_field_tag,
        data
      }
    },
    validatorFn(rule, value, callback) {
      if (this.form.type === 0) {
        callback()
        return
      }
      const customName = this.customName.trim()
      if (!customName) {
        return callback(new Error(this.$t('global_please_input')))
      }
      callback()
    },
    initForm(value) {
      const data = this.initData(value)
      const {
        type,
        place_id = '',
        images = [],
        title_multilang = '',
        area_data,
        is_customized_area,
        ...other
      } = data
      this.form.type = type
      this.form.place_id = this.getDefaultPlaceId(place_id, title_multilang)
      this.form.images = images

      if (Array.isArray(area_data) && area_data.length) {
        this.form.area_data = area_data
      } else if (this.isItineraryV3 && data.location && type === 1) {
        // 兼容 meet up v3 交互 但为 v2 数据的情况
        const { location, location_original = '', address_desc = '' } = data
        this.form.area_data = [
          {
            area_id: 0,
            pick_up_type: 1,
            map_type: 1,
            supply_api_mapping_key: '',
            amap_code: '',
            google_place_id: place_id,
            location: location_original || location,
            location_name: title_multilang,
            address_desc
          }
        ]
        this.v2CompatibilityV3 = true
      } else {
        this.form.area_data = []
      }

      this.form.is_customized_area = is_customized_area
      this.customName = title_multilang
      this.getPOIFromValue(data, this.form.place_id)

      // 如果是未清洗的数据, 则没有 area_data 字段, 此时需要将旧数据格式为 v2 数据结构
      if (this.isItineraryV2 && !hasOwnProperty.call(data, 'area_data') && type === 0) {
        this.form.area_data = [
          {
            ...other,
            place_id,
            location_name: title_multilang
          }
        ]
      }
    },
    getFormData() {
      const {
        form: { type, place_id = '', images }
      } = this
      const area_data = this.$refs.map?.getPostData?.() ?? []

      if (type === 0) {
        return JSON.stringify({ type, images, ...area_data })
      }

      const map = this.initMap(place_id)
      return JSON.stringify({ ...map, type, images, ...area_data })
    },
    onChangeType() {
      this.form.area_data = []
    }
  }
}
</script>

<style lang="scss">
.itinerary-departure-map-container {
  .ant-form-explain {
    display: none;
  }

  .custom-explain-wrap .ant-form-explain {
    display: block;
  }

  .has-error .ant-input,
  .has-error .ant-input:hover {
    background-color: #fff;
    border-color: #d9d9d9;
  }

  .has-error .ant-input-group-addon {
    border-color: #d9d9d9;
    background-color: #fafafa;
    color: rgba(0, 0, 0, 0.65);
  }

  .has-error .ant-input:focus {
    border-color: #40a9ff;
    border-right-width: 1px !important;
    outline: 0;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  .has-error {
    .custom-explain-wrap {
      .ant-input,
      .ant-input:hover {
        background-color: #fff;
        border-color: #f5222d;
      }

      .ant-input-group-addon {
        color: #f5222d;
        background-color: #fff;
        border-color: #f5222d;
      }

      .ant-input:focus {
        border-color: #ff4d4f;
        border-right-width: 1px !important;
        outline: 0;
        box-shadow: 0 0 0 2px rgba(245, 34, 45, 0.2);
      }
    }
  }
}
</style>

<style scoped lang="scss">
.form-item__ctrl {
  width: 400px;
}
.map-button {
  padding: 0;
  margin-right: 24px;
}
.placement-text {
  padding: 2px 0;
}

.itinerary-departure-map-container {
  .__icon {
    position: relative;
    top: 2px;
    margin-left: 6px;
    color: rgba(0, 0, 0, 0.25);
  }

  .map-poi-wrap {
    margin-top: 0px;

    .poi-button-icon {
      margin-right: 10px;
    }
  }
}
</style>
