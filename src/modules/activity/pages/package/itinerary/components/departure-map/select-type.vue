<template>
  <a-radio-group v-model="currValue" class="select-type-options">
    <a-radio
      v-for="option of options"
      :key="option.value"
      :value="option.value"
      :disabled="getOptionDisabled(option)"
    >
      <div class="select-type-item" :class="{ '--is-disabled': getOptionDisabled(option) }">
        <span class="select-type-item-title">{{ option.title }}</span>
        <span>{{ option.desc }}</span>
      </div>
    </a-radio>
  </a-radio-group>
</template>

<script>
import { mapMutations } from 'vuex'
import { getUuid } from '@activity/utils'

export default {
  name: 'DepartureMapSelectType',
  inject: ['provideData'],
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Number,
      default: undefined
    }
  },
  data() {
    this.uuid = getUuid()
    return {}
  },
  computed: {
    departureTypes() {
      return this.$store.state.itineraryDepartureTypes
    },
    currValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('change', val)
      }
    },
    options() {
      return [
        {
          title: this.$t('78889'),
          value: 1,
          disabled: !_.get(this.provideData, 'is_support_meetup_map', true),
          desc: __('161207')
        },
        {
          title: this.$t('78888'),
          value: 0,
          disabled: Object.values(this.departureTypes).includes(0),
          desc: __('161208')
        }
      ]
    }
  },
  watch: {
    currValue: {
      immediate: true,
      handler(value) {
        this.setItineraryDepartureTypes({ uuid: this.uuid, value })
      }
    }
  },
  beforeDestroy() {
    this.clearItineraryDepartureTypes(this.uuid)
  },
  methods: {
    ...mapMutations(['setItineraryDepartureTypes', 'clearItineraryDepartureTypes']),
    getOptionDisabled(option) {
      return option.disabled && this.currValue !== option.value
    }
  }
}
</script>

<style lang="scss" scoped>
$checkedColor: #437dff;
$disabledColor: rgba(0, 0, 0, 0.25);

.select-type-options {
  display: flex;
  gap: 24px;

  ::v-deep .ant-radio-wrapper {
    width: 50%;
    display: inline-flex;
    padding: 16px;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    transition: border-color 0.3s;
    cursor: pointer;

    &:hover:not(.ant-radio-wrapper-disabled):not(.ant-radio-wrapper-checked) {
      border-style: dashed;
    }

    &.ant-radio-wrapper-checked {
      border-style: solid;
    }

    &:hover:not(.ant-radio-wrapper-disabled),
    &.ant-radio-wrapper-checked {
      border-color: $checkedColor;
    }

    .ant-radio {
      height: fit-content;
      margin-top: 4px;
    }
  }

  .select-type-item {
    display: inline-flex;
    align-items: flex-start;
    flex-direction: column;
    gap: 2px;
    white-space: break-spaces;
    color: #000;

    &-title {
      font-size: 16px;
      font-weight: 700;
    }

    &.--is-disabled {
      color: $disabledColor;
      .select-type-item-title {
        color: $disabledColor;
      }
    }
  }
}
</style>
