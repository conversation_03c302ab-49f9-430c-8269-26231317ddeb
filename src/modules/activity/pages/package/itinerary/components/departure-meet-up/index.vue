<template>
  <div class="itinerary-meet-up-map-container">
    <a-spin :spinning="loading">
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-form-model-item :label="$t('78847')" prop="map" :colon="false">
          <FixedPointMap
            ref="map"
            class="custom-explain-wrap"
            :value="form.map"
            :show-map-source-selector="false"
            :show-search-icon="true"
            :maximum="maximum"
            :error-msg="errorMsg"
            @initialized="onInitialized"
            @change="changeValue"
          />
          <!-- <div v-if="currPoi && currPoi.content">
            {{ poiIdStr }}&nbsp;&nbsp;
            <a-icon type="copy" @click="handleClipboard(currPoi.content.poi_id || currPoi.content.place_id)" />
          </div> -->
        </a-form-model-item>

        <a-form-model-item
          v-if="form.map.length"
          class="custom-explain-wrap location-name"
          :label="$t('167233')"
          prop="locationName"
          :colon="false"
        >
          <a-input v-model="form.locationName" :disabled="isPublishWithAi" />
          <a-input v-if="locationNameRefValue" v-model="locationNameRefValue" class="text-area" disabled />
          <!-- 只会有一个点数据 -->
          <a-popover
            v-if="isAdmin"
            :visible="currSupplyApiKey.visible"
            title="API Pick Up Mapping Key"
            trigger="click"
            placement="top"
            :get-popup-container="getPopupContainer"
          >
            <div slot="content" class="supply-api-key">
              <div class="supply-api-key__body">
                <div class="supply-api-key__body-location-name">
                  {{ form.locationName }}
                </div>
                <a-input
                  v-model="currSupplyApiKey.value"
                  class="supply-api-key__body-input"
                  allow-clear
                  size="small"
                  :max-length="500"
                ></a-input>
              </div>
              <div class="supply-api-key__footer">
                <a-button type="primary" size="small" @click="confirmSupplyApiKey">
                  {{ $t('global_confirm') }}
                </a-button>
                <a-button type="default" size="small" @click="cancelSupplyApiKey">
                  {{ $t('global_cancel') }}
                </a-button>
              </div>
            </div>
            <a-button
              v-if="isApi"
              class="supply-api-key__btn"
              type="link"
              @click="editSupplyApiKey(form.map[0])"
            >
              API Pick Up Mapping Key
            </a-button>
          </a-popover>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </div>
</template>

<script>
// @note subgroup meetup 只能设置一个点，多个点需要多个 subgroup
import FixedPointMap from '@activity/pages/package/extra/components/pickUpLocation/fixedPoint/map.vue'
import { isAdmin } from '@/env'

export default {
  name: 'ItineraryDepartureMeetUp',
  inject: ['provideData'],
  components: {
    FixedPointMap
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    locationNameRefValue: {
      type: String,
      default: null
    },
    isPublishWithAi: {
      type: Boolean,
      default: false
    }
  },
  data() {
    this.maximum = 1
    const mapValidator = (rule, value, callback) => {
      if (this.form.map.length) {
        callback()
        this.errorMsg = ''
      } else {
        callback(new Error(__('Please select')))
        this.errorMsg = __('Please select')
      }
    }
    const locationNameValidator = (rule, value, callback) => {
      if (this.form.locationName) {
        callback()
      } else {
        callback(new Error('Please input the location name'))
      }
    }

    return {
      isAdmin,
      loading: true,
      errorMsg: '',
      form: {
        map: [],
        locationName: undefined
      },
      rules: {
        map: [
          {
            required: true,
            validator: mapValidator
          }
        ],
        locationName: [
          {
            required: true,
            validator: locationNameValidator,
            trigger: ['change', 'blur']
          }
        ]
      },
      currPoi: null,
      currSupplyApiKey: {
        visible: false,
        value: undefined
      }
    }
  },
  computed: {
    poiIdStr() {
      if (this.currPoi?.content?.google_place_id) {
        return klook.parseStr1(this.$t('102292'), {
          ID: this.currPoi.content.google_place_id
        })
      }

      return ''
    },
    isApi() {
      return this.provideData.stockOutType === 'API'
    }
  },
  beforeMount() {
    this.initData()
  },
  beforeDestroy() {
    this.instance?.destroy?.()
    this.$set(this, 'form', this.$options.data().form)
  },
  methods: {
    initData() {
      const curr = this.value?.[0]

      if (curr) {
        const { area_id, ...content } = curr
        this.form.locationName = content.location_name

        if (content.location_original) {
          content.location = content.location_original
          delete content.location_original
        }

        this.form.map = [{ content, id: area_id }]
        this.currPoi = curr
      }
    },
    validateForm() {
      return new Promise((resolve) => this.$refs.form.validate(resolve))
    },
    getPostData() {
      // 只会有一项
      return {
        area_data: this.form.map.map((item) => {
          const { id, content } = item

          return {
            area_id: id,
            ...content,
            location_name: this.form.locationName
          }
        })
      }
    },
    changeValue(value) {
      this.form.locationName = value[0].content.location_name
      this.currPoi = value[0]
      this.$emit('change', this.getPostData())
    },
    onInitialized(instance) {
      this.loading = false
      this.instance = instance
    },
    getPopupContainer() {
      return this.$el
    },
    editSupplyApiKey() {
      Object.assign(this.currSupplyApiKey, {
        visible: true,
        value: this.form.map[0].content.supply_api_mapping_key
      })
    },
    confirmSupplyApiKey() {
      Object.assign(this.form.map[0].content, {
        supply_api_mapping_key: this.currSupplyApiKey.value
      })
      this.cancelSupplyApiKey()
    },
    cancelSupplyApiKey() {
      this.$set(this, 'currSupplyApiKey', this.$options.data().currSupplyApiKey)
    }
    // handleClipboard(content) {
    //   klook.Clipboard.copy(content, (success) => {
    //     success && this.$message.success(this.$t('global_success'))
    //   })
    // }
  }
}
</script>

<style lang="scss" scoped>
.itinerary-meet-up-map-container {
  margin: 12px 0;

  .meet-up-map-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .item-icon-trash {
      font-size: 16px;

      &:hover {
        color: red;
      }
    }
  }

  .supply-api-key {
    &__body {
      &-location-name {
        font-size: 16px;
        line-height: 22px;
      }
      &-input {
        width: 320px;
        margin-top: 12px;
      }
    }

    &__footer {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
      margin-top: 12px;
    }

    &__btn {
      padding: 0;
      color: rgb(67, 125, 255);
    }
  }
}
</style>
