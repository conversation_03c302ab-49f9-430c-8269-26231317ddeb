<template>
  <div class="itinerary-pick-up-container">
    <a-form-model ref="form" :model="form" :rules="rules">
      <a-form-model-item prop="type" :colon="false" class="custom-explain-wrap">
        <span slot="label">
          {{ $t('161209') }}
          <span v-if="form.type !== undefined" class="common__reset-btn" @click="handleResetType">
            <svg-icon icon-name="reset" />
            {{ $t('global_reset') }}
          </span>
        </span>
        <a-radio-group v-model="form.type" :options="options" @change="onChange" />

        <component
          :is="compName"
          v-if="form.type !== undefined"
          ref="wrap"
          :key="compName"
          v-model="form.value"
          class="custom-explain-wrap"
          :field="form"
        ></component>
      </a-form-model-item>
    </a-form-model>
  </div>
</template>

<script>
// @note 两个组件需要各自实现 validateForm & getPostData
import StandardDistrictArea from '@activity/pages/package/itinerary/components/departure-district-area/standard/index.vue'
import CustomizedDistrictArea from '@activity/pages/package/itinerary/components/departure-district-area/customized/index.vue'

const isCustomizedAreaDict = {
  standard: 0,
  customized: 1
}

export default {
  name: 'ItineraryDeparturePickUp',
  components: {
    StandardDistrictArea,
    CustomizedDistrictArea
  },
  provide() {
    return {
      clearFormValidate: this.clearValidate,
      refreshWrapCheckStatus: () => {
        this.hasError && this.$refs.wrap.validateForm()
      }
    }
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    isCustomizedArea: {
      type: Number,
      default: undefined
    }
  },
  data() {
    this.isCustomizedAreaDict = isCustomizedAreaDict

    return {
      hasError: false,
      form: {
        type: undefined,
        value: []
      },
      options: [
        { label: __('161210'), value: isCustomizedAreaDict.standard },
        { label: __('161211'), value: isCustomizedAreaDict.customized }
      ]
    }
  },
  computed: {
    compName() {
      return this.form.type === isCustomizedAreaDict.standard
        ? 'StandardDistrictArea'
        : 'CustomizedDistrictArea'
    },
    rules() {
      return {
        type: [{ required: true, trigger: 'blur', validator: this.validator }]
      }
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    async validateForm() {
      const valid = await new Promise((resolve) => this.$refs.form.validate(resolve))

      this.hasError = valid

      return valid
    },
    initData() {
      Object.assign(this.form, {
        type: this.value.length ? this.isCustomizedArea : undefined,
        value: this.value
      })
    },
    getPostData() {
      return {
        is_customized_area: this.form.type,
        area_data: this.$refs.wrap?.getPostData?.() ?? []
      }
    },
    clearValidate() {
      this.$refs.form?.clearValidate?.()
    },
    async validator(rule, value, callback) {
      if (this.form.type === undefined) {
        return callback(new Error(this.$t('global_select')))
      }

      const [err] = await this.$refs.wrap.validateForm()
      if (err) {
        return callback(new Error(this.$t(err)))
      }

      callback()
    },
    onChange() {
      this.$set(this.form, 'value', [])
    },
    handleResetType() {
      this.$set(this, 'form', this.$options.data().form)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../style/mixins.scss';

.itinerary-pick-up-container {
  @include commonResetBtnMixin;
}
</style>
