<template>
  <a-form-model ref="form" :model="form">
    <a-form-model-item :label="$t('78849')" :colon="false">
      <a-textarea
        v-model="form.text"
        class="text-area"
        :show-count="true"
        :max-length="maxLength"
        :auto-size="{ minRows: 2, maxRows: 5 }"
        placeholder="Please enter"
        :disabled="isPublishWithAi"
      ></a-textarea>
      <div class="text-len">{{ textLength }} / {{ maxLength }}</div>
      <a-textarea v-if="refValue" v-model="refValue" class="text-area" disabled :show-count="true" />
    </a-form-model-item>
  </a-form-model>
</template>

<script>
import { mapGetters } from 'vuex'
import poiMixin from '../../mixins/poi-mixin'
import { getEditLang } from '@activity/utils'
import { parseJsonString } from '@activity/pages/package/itinerary/utils/index.js'

export default {
  name: 'DetailTextarea',
  mixins: [poiMixin],
  data() {
    return {
      form: {
        text: ''
      },
      lang: getEditLang()
    }
  },
  computed: {
    ...mapGetters(['itineraryInstancesDataByIdGetters']),
    textLength() {
      return this.form.text.trim().length
    },
    maxLength() {
      const zh = this.lang.startsWith('zh')
      return zh ? 100 : 200
    },
    currRefAttrValues() {
      const data = this.itineraryInstancesDataByIdGetters(this.subWidgetData.instance_id)
      if (data) {
        return parseJsonString(data.attr_values?.[0]?.value, null)
      }

      return null
    },
    refValue() {
      // 兼容 sub-group 数据
      if (this.currRefAttrValues) {
        return this.currRefAttrValues?.detail_multilang || null
      }

      return this.refItineraryInstancesData?.attr_values?.[0]?.value
    }
  },
  methods: {
    getFormData() {
      return this.form.text.trim()
    },
    initForm(value) {
      this.form.text = value
    }
  }
}
</script>

<style scoped lang="scss">
.text-area {
  width: 400px;
  height: 54px;
  margin-top: 12px;

  &:first-child {
    margin-top: 0;
  }
}
.text-len {
  text-align: right;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.25);
  font-size: 14px;
  width: 400px;
}
</style>
