<template>
  <div>
    <a-form-model ref="form" :model="form">
      <a-form-model-item :label="$t('78860')" :colon="false">
        <a-select
          v-model="form.place_id"
          class="form-item__ctrl"
          :placeholder="$t('79396')"
          :show-search="true"
          :filter-option="filterOption"
          :allow-clear="true"
          :dropdown-style="dropdownStyle"
          :dropdown-menu-style="dropdownMenuStyle"
          :disabled="isPublishWithAi"
          @search="poiSearch"
          @change="selectChange"
        >
          <template v-if="multiGroup">
            <a-select-opt-group v-for="group of selectGroupMap" :key="group.key">
              <span slot="label">{{ group.label }}</span>
              <a-select-option
                v-for="option in groupOptionList[group.key]"
                :key="option.value"
                class="poi_option_item"
              >
                <OverflowTextTooltip class="overflow-text">
                  <span class="poi_option_item__title">{{ option.label }}</span>
                </OverflowTextTooltip>
                <OverflowTextTooltip v-if="getPOIDescription(option)" class="overflow-text">
                  <span class="poi_option_item__description">{{ getPOIDescription(option) }}</span>
                </OverflowTextTooltip>
              </a-select-option>
            </a-select-opt-group>
          </template>

          <a-select-option v-for="option in poiOptions" v-else :key="option.value" class="poi_option_item">
            <OverflowTextTooltip class="overflow-text">
              <span class="poi_option_item__title">{{ option.label }}</span>
            </OverflowTextTooltip>
            <OverflowTextTooltip v-if="getPOIDescription(option)" class="overflow-text">
              <span class="poi_option_item__description">{{ getPOIDescription(option) }}</span>
            </OverflowTextTooltip>
          </a-select-option>

          <div slot="dropdownRender" slot-scope="menu">
            <v-nodes :vnodes="menu" />
            <div v-show="poiOptions.length > 0" class="dropdown-btn-wrap" @click="addNew">
              <a-icon class="icon" type="plus"></a-icon>
              <div class="text">{{ $t('78856', { add_new_one: $t('78857') }) }}</div>
            </div>
          </div>
          <div slot="notFoundContent" class="not-found-wrap">
            <svg-icon class="not-found-icon" icon-name="not-found" />
            <div class="not-found-btn-wrap">
              <a-button class="btn" type="link" @click="addNew">
                {{ $t('78856', { add_new_one: $t('78857') }) }}
              </a-button>
            </div>
          </div>
        </a-select>
        <a-tooltip v-if="currPOIDescription">
          <template slot="title">{{ currPOIDescription }}</template>
          <a-icon class="poi-value-description" type="info-circle" />
        </a-tooltip>
      </a-form-model-item>

      <POIButton
        ref="POIButton"
        :current-poi="currentPOI"
        :photo-list="form.images"
        :drawer-title="$t('78860')"
        :show-map-button="showMapButton"
        :searchable="mapSearchable"
        :need-defalut-map-data="true"
        :check-function="checkFunction"
        :hide-map-component="!showMapComponent"
        :upload-photo-title="$t('167232')"
        @photoChange="photoChange"
        @mapPhotoConfirm="mapPhotoConfirm"
        @mapDrawerConfirm="confirmMap"
        @cancelMap="cancel"
      >
        <CustomNameForm
          ref="customNameForm"
          v-model="customName"
          :ref-value="refValue"
          :disabled="disabledEdit || isPublishWithAi"
          :show-address="showMapComponent"
          :show-tips="showTips"
          :field-title="$t('78860')"
        />
      </POIButton>
    </a-form-model>
  </div>
</template>
<script>
import POIButton from '../poi-button/index.vue'
import poiMixin from '../../mixins/poi-mixin'
import CustomNameForm from '../custom-name-form/index.vue'

export default {
  name: 'DinningPOI',
  components: {
    POIButton,
    CustomNameForm,
    VNodes: {
      functional: true,
      render: (h, ctx) => ctx.props.vnodes
    }
  },
  mixins: [poiMixin],
  data() {
    return {
      form: {
        place_id: undefined,
        images: []
      }
    }
  },
  watch: {
    currentPOI: {
      deep: true,
      handler(v) {
        this.customName = v.title || ''
      }
    }
  },
  methods: {
    confirmMap(val) {
      const map = val?.map ?? null
      this.mapDrawerConfirm({ map }, this.customName, true)
      this.isAdd = false
    },
    getPreviewData() {
      let data = []
      if (this.currentPOI?.label) {
        data.push({
          title: this.$t('78860'),
          text: this.currentPOI.label
        })
      }

      return {
        widget_type: this.data.widget_type,
        ref_field_tag: this.data.ref_field_tag,
        data
      }
    },
    initForm(value) {
      const data = this.initData(value)
      const { place_id = '', images = [], title_multilang } = data
      this.form.images = images
      if (!title_multilang) {
        return
      }
      this.form.place_id = this.getDefaultPlaceId(place_id, title_multilang)
      this.getPOIFromValue(data, this.form.place_id)
    },
    getFormData() {
      const { place_id = '', images = [] } = this.form
      const map = this.initMap(place_id, true)
      return JSON.stringify({ ...map, images })
    },
    toggleMap() {
      this.showMap = !this.showMap
    },
    togglePhoto() {
      this.showPhoto = !this.showPhoto
    }
  }
}
</script>

<style scoped lang="scss">
@import '../../mixins.scss';
@include mixin-poi-option-item;

.form-item__ctrl {
  width: 400px;
}
.map-button {
  padding: 0;
  margin-right: 24px;
}
.placement-text {
  padding: 2px 0;
}
.not-found-wrap {
  cursor: default;
  .not-found-btn-wrap {
    color: rgba(0, 0, 0, 0.85);
    .btn {
      padding: 0 5px;
      white-space: pre-wrap;
    }
  }

  .not-found-icon {
    display: block;
    margin: 0 auto 5px auto;
    width: 100px;
    height: 78px;
  }
}

.dropdown-btn-wrap {
  border-top: 1px solid #d9d9d9;
  cursor: pointer;
  display: flex;
  padding: 4px 10px;
  color: #1890ff;
  .icon {
    margin: 4px 4px 0 0;
  }
  .text {
    flex: 1;
  }
}
</style>
