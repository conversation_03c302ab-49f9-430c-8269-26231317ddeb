<template>
  <a-drawer :visible="visible" width="660px" :z-index="100" :title="title" @close="handleCancel">
    <div class="slot-wrap">
      <slot></slot>
    </div>
    <div class="footer">
      <a-button style="margin-right: 8px;" @click="handleCancel">
        {{ $t('global_button_cancel') }}
      </a-button>
      <a-button type="primary" @click="handleSave">
        {{ $t('global_confirm') }}
      </a-button>
    </div>
  </a-drawer>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    }
  },
  methods: {
    handleCancel() {
      this.$emit('cancel')
    },
    handleSave() {
      this.$emit('save')
    }
  }
}
</script>

<style scoped lang="scss">
.slot-wrap {
  padding-top: 50px;
}

.footer {
  position: absolute;
  padding: 10px 16px;
  right: 0;
  bottom: 0;
  width: 100%;

  border-top: 1px solid #e9e9e9;
  background: #fff;
  text-align: right;
  z-index: 3;
}
</style>
