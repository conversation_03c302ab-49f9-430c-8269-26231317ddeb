<template>
  <Drawer :visible="visible" :title="title" @cancel="cancel" @save="confirm">
    <slot></slot>
    <klk-ant-area-map
      v-if="showMap && !hideMapComponent"
      ref="areaMap"
      style="height: 400px;"
      :initial-map="initialMap"
      :enable-amap="false"
      :searchable="searchable"
      :language="editLanguage"
      v-bind="initialObject"
      @change="getMapInfo"
    />
  </Drawer>
</template>
<script>
import { KlkAntAreaMap } from '@klook/admin-ui'
import Drawer from '../drawer/index.vue'
import { getUuid } from '@activity/utils'

export default {
  components: {
    KlkAntAreaMap,
    Drawer
  },
  props: {
    hideMapComponent: {
      type: Boolean,
      default: false
    },
    currentPoi: {
      type: Object,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: 'Title'
    },
    customName: {
      type: String,
      default: ''
    },
    searchable: {
      type: <PERSON>olean,
      default: true
    },
    needDefalutMapData: {
      type: Boolean,
      default: false
    },
    language: {
      type: String,
      default: 'zh_CN'
    }
  },
  data() {
    return {
      initialMap: 'google_maps',
      mapData: null,
      showMap: true
    }
  },
  computed: {
    initialObject() {
      const df = {
        initialPlaceid: '',
        initialName: ''
      }
      const currentPoi = this.currentPoi || {}
      // const { place_id: initialPlaceid = '', title = '', location = '' } = currentPoi
      const { place_id: initialPlaceid = '', location = '' } = currentPoi
      const [lat = '', lng = ''] = location.split(',')
      const initialLocation = lat && lng ? { lng: Number(lng), lat: Number(lat) } : null
      // const initialName = title || this.customName
      if (!initialPlaceid) {
        return df
      }
      return { ...df, initialPlaceid, initialName: '', initialLocation }
    },
    editLanguage() {
      const lang = this.language
      return klook.getFlang2Blang(lang)
    }
  },
  watch: {
    initialObject: {
      deep: true,
      handler(v) {
        // 触发地图更新
        this.showMap = false
        if (!v.initialPlaceid) {
          this.mapData = null
        }
        this.$nextTick(() => {
          this.showMap = true
        })
      }
    }
  },
  methods: {
    getMapInfo(data) {
      this.mapData = data
    },
    cancel() {
      this.$emit('cancel')
    },
    confirm() {
      let data = null
      const mapData = this.mapData
      if (mapData) {
        data = mapData
      } else if (this.needDefalutMapData) {
        const areaMap = this.$refs.areaMap
        const searchValue = areaMap?.currentAddress || ''
        data = {
          name: searchValue.trim(),
          is_empty: true,
          place_id: getUuid()
        }
      }
      this.$emit('confirm', {
        map: data
      })
    }
  }
}
</script>

<style scoped lang="scss">
.map-wrap {
  padding-top: 50px;
}

.footer {
  position: absolute;
  padding: 10px 16px;
  right: 0;
  bottom: 0;
  width: 100%;

  border-top: 1px solid #e9e9e9;
  background: #fff;
  text-align: right;
  z-index: 3;
}
</style>
