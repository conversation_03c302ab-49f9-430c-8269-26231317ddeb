<template>
  <div class="ordered-list-container">
    <div v-for="(item, index) of list" :key="item.uuid" class="ordered-item">
      <div v-if="list.length > 1" class="ordered-item__index">{{ index + 1 }}</div>
      <div class="ordered-item__actions">
        <a-icon v-if="list.length > 1" type="delete" @click="handleDelete({ index })" />
        <a-icon v-if="index !== 0" type="arrow-up" @click="handleSort({ index }, true)" />
        <a-icon v-if="index < list.length - 1" type="arrow-down" @click="handleSort({ index }, false)" />
      </div>
      <div class="ordered-item__content">
        <slot ref="content" name="content" :item="item" :index="index">
          {{ index }}
        </slot>
      </div>
    </div>

    <footer class="ordered-footer">
      <a-button v-if="list.length < maximum" class="add-new-btn" @click="handleAddNew">
        <a-icon type="plus" />&nbsp;{{ addBtnText }}
      </a-button>
    </footer>
  </div>
</template>

<script>
import { pmsConfirm } from '@activity/utils'

export default {
  name: 'OrderedList',
  model: {
    prop: 'list',
    event: 'change'
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    maximum: {
      type: Number,
      default: Infinity
    },
    addBtnText: {
      type: String,
      default: __('global_button_add')
    }
  },
  methods: {
    async handleDelete({ index }) {
      const confirm = await pmsConfirm.call(this, {
        title: this.$t('78878'),
        content: ''
      })

      if (confirm) {
        this.list.splice(index, 1)
        this.$emit('delete')
      }
    },
    handleSort({ index }, isUp) {
      const step = isUp ? -1 : 1
      const curr = this.list.splice(index, 1)[0]
      this.list.splice(index + step, 0, curr)
    },
    handleAddNew() {
      this.$emit('addNew')
    }
  }
}
</script>

<style lang="scss" scoped>
$marginPixel: 12px;

.ordered-list-container {
  margin-bottom: 24px;
  background-color: #fafafa;

  .ordered-item {
    position: relative;
    padding: 20px 24px 20px 24px;
    margin-top: $marginPixel;
    background-color: #fff;

    &:first-child {
      margin-top: 0;
    }

    &__index {
      position: absolute;
      top: 0;
      left: 8px;
      width: 18px;
      height: 16px;
      line-height: 16px;
      text-align: center;
      border-radius: 0 0 4px 4px;
      background-color: #437dff;
      font-weight: 600;
      color: #fff;
    }

    &__actions {
      position: absolute;
      top: 20px;
      right: 24px;
      display: flex;
      gap: 20px;
      z-index: 999;

      ::v-deep .anticon {
        font-size: 16px;
        cursor: pointer;

        &:hover {
          color: #1890ff;
        }

        &.anticon-delete:hover {
          color: red;
        }
      }
    }
  }

  .ordered-footer {
    margin-top: $marginPixel;
  }
}
</style>
