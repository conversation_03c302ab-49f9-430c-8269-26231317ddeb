<template>
  <div class="image-wrap" :class="{ checked: checked }">
    <img class="image" :src="url" />
    <a-checkbox v-show="checked" :checked="checked" class="check-box"></a-checkbox>
  </div>
</template>
<script>
export default {
  props: {
    url: {
      type: String,
      default: ''
    },
    checked: {
      type: Boolean,
      default: false
    }
  }
}
</script>
<style scoped lang="scss">
.image-wrap {
  width: 104px;
  height: 104px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  margin: 0 16px 16px 0;
  padding: 8px;
  position: relative;
  cursor: pointer;
  &.checked {
    border: 2px solid #437dff;
    padding: 6px;
  }
  .check-box {
    position: absolute;
    bottom: -3px;
    right: -1px;
    z-index: 22;
  }
  .image {
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
  }
}
</style>
