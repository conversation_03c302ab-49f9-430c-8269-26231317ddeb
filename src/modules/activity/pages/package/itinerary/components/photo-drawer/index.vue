<template>
  <Drawer :visible="visible" :title="title" @cancel="cancel" @save="confirm">
    <a-tabs
      v-model="activeKey"
      :tab-bar-style="{
        display: galleryList.length <= 1 ? 'none' : 'block'
      }"
    >
      <a-tab-pane v-for="item in galleryList" :key="item.lang" :tab="`${item.lang} ${$t('49588')}`">
        <div class="image-container">
          <div v-for="image in item.images" :key="image.id" @click="selectImage(image.id)">
            <ImageItem :url="image.url" :checked="getChecked(image.id)" />
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
  </Drawer>
</template>

<script>
import { getUuid, getEditLang } from '@activity/utils'

import Drawer from '../drawer/index.vue'
import ImageItem from './image-item.vue'
export default {
  inject: ['getImageGallery'],
  components: {
    Drawer,
    ImageItem
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    maximum: {
      type: Number,
      default: 5
    },
    title: {
      type: String,
      default: 'Title'
    }
  },
  data() {
    return {
      activeKey: 'en_US',
      enImages: [],
      selectedList: [],
      galleryList: []
    }
  },
  mounted() {
    this.initImageGallery()
  },
  methods: {
    getChecked(id) {
      return this.selectedList.includes(id)
    },
    selectImage(id) {
      const isChecked = this.getChecked(id)
      if (isChecked) {
        this.selectedList = this.selectedList.filter((item) => item !== id)
        return
      }
      if (this.selectedList.length >= this.maximum) {
        this.$message.info(this.$t('79625', { num: 5 }))
        return
      }
      this.selectedList.push(id)
    },
    initImageGallery() {
      const language = [...new Set([getEditLang(), 'en_US'].filter((lang) => lang))].sort((item) =>
        item === 'en_US' ? 1 : -1
      )
      const imageGallery = this.getImageGallery() || []
      const galleryList = []
      language.forEach((lang) => {
        const data = imageGallery.find((item) => item.language === lang)
        const images = (data?.images ?? []).map((item, index) => {
          return {
            url: item,
            id: getUuid()
          }
        })
        galleryList.push({
          lang,
          images
        })
      })
      this.galleryList = galleryList
      this.activeKey = language[0]
    },
    cancel() {
      this.selectedList = []
      this.$emit('cancel')
    },
    confirm() {
      const images = this.galleryList.reduce((acc, curr) => {
        const img = curr.images || []
        return [...acc, ...img]
      }, [])
      const checkedList = this.selectedList
      const data = images.filter((item) => checkedList.includes(item.id)).map((item) => item.url)
      this.$emit('confirm', data)
      this.selectedList = []
    }
  }
}
</script>

<style scoped lang="scss">
.image-container {
  padding-top: 8px;
  display: flex;
  flex-wrap: wrap;
}
</style>
