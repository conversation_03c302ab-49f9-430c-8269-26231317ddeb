<template>
  <div class="pick-up-table">
    <template v-if="list.length">
      <a-table :columns="columns" :data-source="list" :pagination="false" :scroll="{ y: 360 }" bordered>
        <div slot="location" slot-scope="text, record">
          <OverflowTextTooltip
            placement="bottom"
            overlay-class-name="common-tooltip-style"
            style="display: block;"
            :delay="200"
          >
            {{ record.location_name }}
          </OverflowTextTooltip>
        </div>

        <div slot="address" slot-scope="text, record">
          <OverflowTextTooltip
            placement="bottom"
            overlay-class-name="common-tooltip-style"
            style="display: block;"
            :delay="200"
          >
            {{ record.address_desc }}
          </OverflowTextTooltip>
        </div>

        <div slot="city" slot-scope="text, record">
          <OverflowTextTooltip
            placement="bottom"
            overlay-class-name="common-tooltip-style"
            style="display: block;"
            :delay="200"
          >
            {{ record.parent_name }}
          </OverflowTextTooltip>
        </div>
      </a-table>

      <div class="pick-up-table__hint">{{ $t('104019') }}</div>
    </template>

    <div v-else-if="!isCarServices2getter" class="pick-up-table__setting-entrance" @click="handleGoExtra">
      <a-icon type="environment" />
      <span>{{ $t('104018') }}</span>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'

export default {
  name: 'PickUpTable',
  components: { OverflowTextTooltip },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      columns: [
        { title: 'Pick-up location', scopedSlots: { customRender: 'location' } },
        { title: 'Address', scopedSlots: { customRender: 'address' } },
        { title: 'City', scopedSlots: { customRender: 'city' } }
      ]
    }
  },
  computed: {
    ...mapGetters(['isCarServices2getter'])
  },
  methods: {
    handleGoExtra() {
      const { href } = this.$router.resolve({
        name: 'packageExtra',
        params: this.$route.params,
        query: this.$route.query
      })
      setTimeout(() => {
        window.open(href)
      }, 60)
    }
  }
}
</script>

<style lang="scss" scoped>
.pick-up-table {
  &__hint {
    font-size: 14px;
    color: #a6a6a6;
    line-height: 22px;
  }

  &__setting-entrance {
    display: flex;
    gap: 4px;
    align-items: center;
    color: #437dff;
    line-height: 22px;
    cursor: pointer;
  }
}
</style>
