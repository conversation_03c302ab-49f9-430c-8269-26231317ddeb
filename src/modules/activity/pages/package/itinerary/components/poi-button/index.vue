<template>
  <div class="wrapper">
    <!-- poi 信息 -->
    <a-button
      v-if="!hideMap"
      v-show="showMapButton"
      :disabled="disabledMapButton"
      class="map-button"
      :class="{ error: invalidatePoi }"
      :type="buttonType"
      @click="toggleMap"
    >
      <component
        :is="canClipboard ? 'a-tooltip' : 'span'"
        placement="top"
        overlay-class-name="common-tooltip-style"
        :overlay-style="{ maxWidth: '336px' }"
      >
        <template slot="title">
          {{ poiIdStr }}&nbsp;&nbsp;
          <a-icon type="copy" @click="handleClipboard(currentPoi.poi_id || currentPoi.place_id)" />
        </template>
        <slot name="mapButton" :mapButtonText="mapButtonText">
          <div class="poi-button-wrap">
            <svg-icon class="icon" icon-name="icon-travel-location-xs" />
            <span>{{ mapButtonText }}</span>
            <a-icon type="right" />
          </div>
        </slot>
      </component>
    </a-button>
    <!-- poi 报错 -->
    <div v-show="invalidatePoi" class="poi-error-text">{{ $t('global_select') }}</div>
    <slot name="extendInfoWrap"></slot>
    <!-- 上传图片 -->
    <div v-if="!hidePhoto" class="map-upload-wrap">
      <div v-if="uploadPhotoTitle" class="map-upload-title">{{ uploadPhotoTitle }}</div>
      <a-button
        v-if="!hidePhoto && !provideData.disabled.all && !isMaximum"
        :disabled="disabledUploadPhoto"
        @click="chooseImage"
      >
        {{ $t('78846') }}
      </a-button>
      <a-upload
        :file-list="fileList"
        :custom-request="customRequest"
        list-type="picture-card"
        accept=".jpg,.jpeg,.png"
        :before-upload="beforeUpload"
        :remove="removeFile"
        :multiple="true"
        :max-count="maxLength"
        :disabled="disabledUploadPhoto"
        @preview="handlePreview"
      >
        <div v-if="!provideData.disabled.all && !isMaximum" @click="uploadImage">
          <a-icon type="plus" />
          <div class="ant-upload-text">{{ $t('44333') }}(≤5)</div>
        </div>
      </a-upload>
    </div>
    <MapDrawer
      v-if="!hideMap"
      :visible="showMap"
      :current-poi="currentPoi"
      :title="drawerTitle"
      :custom-name="customName"
      :searchable="searchable"
      :need-defalut-map-data="needDefalutMapData"
      :language="editLanguage"
      :hide-map-component="hideMapComponent"
      @cancel="cancelMap"
      @confirm="confirmMap"
    >
      <slot></slot>
    </MapDrawer>
    <PhotoDrawer
      v-if="!hidePhoto"
      :visible="showPhoto"
      :maximum="maximum"
      :title="$t('78846')"
      @cancel="togglePhoto"
      @confirm="confirmPhoto"
    />
    <!-- 图片预览 -->
    <a-modal
      v-if="!hidePhoto"
      v-model="modalObject.show"
      :footer="null"
      :centered="true"
      :closable="false"
      :width="800"
      :z-index="10000"
      @cancel="cancelPreview"
    >
      <a-spin :spinning="showLoading">
        <img :src="modalObject.url" class="pre-img" @load="imageLoad" />
      </a-spin>
    </a-modal>
  </div>
</template>
<script>
import MapDrawer from '../map-drawer/index.vue'
import PhotoDrawer from '../photo-drawer/index.vue'
import { uploadFile } from '@activity/components/Cropper/util/upload.js'

export default {
  inject: {
    provideData: {
      default: {
        disabled: {
          all: false,
          freeText: false
        }
      }
    }
  },
  components: {
    MapDrawer,
    PhotoDrawer
  },
  model: {
    prop: 'photoList',
    event: 'change'
  },
  props: {
    hideMapComponent: {
      type: Boolean,
      default: false
    },
    photoList: {
      type: Array,
      default: () => []
    },
    drawerTitle: {
      type: String,
      default: ''
    },
    currentPoi: {
      type: Object,
      default: () => {}
    },
    // 是否需要map
    hideMap: {
      type: Boolean,
      default: false
    },
    disabledUploadPhoto: {
      type: Boolean,
      default: false
    },
    disabledMapButton: {
      type: Boolean,
      default: false
    },
    // 控制map按钮展示状态
    showMapButton: {
      type: Boolean,
      default: true
    },
    hidePhoto: {
      type: Boolean,
      default: false
    },
    mapText: {
      type: String,
      default: ''
    },
    customName: {
      type: String,
      default: ''
    },
    checkFunction: {
      type: Function,
      default: () => true
    },
    searchable: {
      type: Boolean,
      default: true
    },
    invalidatePoi: {
      type: Boolean,
      default: false
    },
    needDefalutMapData: {
      type: Boolean,
      default: false
    },
    uploadPhotoTitle: {
      type: String,
      default: ''
    },
    buttonType: {
      type: String,
      default: 'link'
    }
  },
  data() {
    return {
      showMap: false,
      showPhoto: false,
      fileList: [],
      modalObject: {
        show: false,
        url: ''
      },
      showLoading: false,
      maxLength: 5
    }
  },
  computed: {
    poiLocation() {
      const location = this.currentPoi?.location ?? ''
      return location
    },
    mapButtonText() {
      // 78847: Link to Google map
      // 78848: Edit Google Map
      // 78855: See POI detail
      const poiLocation = this.poiLocation
      const text = this.mapText && poiLocation ? this.mapText : this.$t('78847')
      return poiLocation ? `${text}(${poiLocation})` : text
    },
    isMaximum() {
      return this.maximum <= 0
    },
    maximum() {
      return this.maxLength - this.fileList.length
    },
    editLanguage() {
      return klook.getEditLang()
    },
    canClipboard() {
      return this.currentPoi.poi_id || this.currentPoi.place_id
    },
    poiIdStr() {
      if (this.currentPoi.poi_id) {
        return klook.parseStr1(this.$t('102292'), {
          ID: this.currentPoi.poi_id
        })
      }

      return klook.parseStr1(this.$t('106049'), {
        id: this.currentPoi.place_id
      })
    }
  },
  watch: {
    photoList: {
      immediate: true,
      handler(v) {
        if (Array.isArray(v)) {
          this.getFileList(v)
        }
      }
    }
  },
  methods: {
    handleClipboard(content) {
      klook.Clipboard.copy(content, (success) => {
        success && this.$message.success(this.$t('global_success'))
      })
    },
    confirmPhoto(data) {
      this.showPhoto = false
      this.$emit('mapPhotoConfirm', data)
    },
    async confirmMap(data) {
      let flag = true
      if (typeof this.checkFunction === 'function') {
        flag = await this.checkFunction()
      }
      if (!flag) {
        return
      }
      this.showMap = false
      this.$emit('mapDrawerConfirm', data)
    },
    getFileList(photoList = []) {
      const fileList = this.fileList
      const list = photoList.map((item, index) => {
        return {
          uid: index,
          name: item,
          url: item,
          status: 'done',
          percent: 100,
          path: item
        }
      })
      const loadingList = fileList.filter((item) => item.status != 'done')
      this.fileList = [...list, ...loadingList]
    },
    handlePreview(file) {
      this.modalObject.url = file.url
      this.modalObject.show = true
      this.showLoading = true
    },
    cancelPreview() {
      this.modalObject.url = ''
      this.modalObject.show = false
      this.showLoading = false
    },
    imageLoad() {
      this.showLoading = false
    },
    removeFile(file) {
      const list = []
      const newList = []
      this.fileList.forEach((item) => {
        if (item.uid !== file.uid) {
          newList.push(item)
          item.path && list.push(item.path)
        }
      })
      this.fileList = newList
      this.$emit('photoChange', [...list])
    },
    beforeUpload(file) {
      const isLt4M = file.size / 1024 / 1024 < 4

      if (!isLt4M) {
        this.$info({
          title: 'Image must smaller than 4MB!',
          okText: this.$t('44915')
        })
        console.error('Image must smaller than 4MB!')
      }

      return isLt4M
    },
    async customRequest(e) {
      if (this.fileList.length >= this.maxLength) {
        return
      }
      const file = e.file
      const newFile = {
        uid: file.uid,
        name: file.name,
        url: URL.createObjectURL(file),
        status: 'uploading',
        percent: 0,
        path: ''
      }
      this.fileList.push(newFile)
      const data = await uploadFile(file, {
        setPercentage(percent) {
          newFile.percent = percent
        }
      })
      if (!data) {
        newFile.status = 'error'
        return
      }
      const { secure_url } = data
      newFile.status = 'done'
      newFile.path = secure_url
      newFile.url = secure_url
      this.$emit('photoChange', [...(this.photoList || []), secure_url])
    },
    uploadImage() {
      if (this.isMaximum) {
        this.$message.info(this.$t('79625', { num: 5 }))
        return
      }

      return true
    },
    chooseImage() {
      if (this.isMaximum) {
        this.$message.info(this.$t('79625', { num: 5 }))
        return
      }
      this.togglePhoto()
    },
    cancelMap() {
      this.showMap = !this.showMap
      this.$emit('cancelMap')
    },
    toggleMap() {
      this.showMap = !this.showMap
    },
    togglePhoto() {
      this.showPhoto = !this.showPhoto
    }
  }
}
</script>
<style lang="scss" scoped>
.pre-img {
  display: block;
  width: 100%;
}

.wrapper {
  margin-top: -10px;
  min-width: 560px;
  display: flex;
  flex-direction: column;
}

.map-button {
  margin-right: 24px;
  display: inline-flex;
  align-items: center;
  width: fit-content;

  &.ant-btn-link {
    padding: 0;
    color: #437dff;
  }

  &.error {
    color: #f5222d;
    .icon {
      color: #f5222d;
    }
  }
  .icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
}

.poi-error-text {
  color: #f5222d;
  height: 22px;
  line-height: 6px;
}

.placement-text {
  padding: 5px 0;
  cursor: pointer;
  font-size: 14px;
}

.map-upload-wrap {
  margin-top: 12px;

  .map-upload-title {
    margin-top: -12px;
    line-height: 40px;
  }

  ::v-deep .ant-upload-picture-card-wrapper {
    /* height: 114px; */
    height: 90px;
    margin-top: 12px;
  }
}

.poi-button-wrap {
  display: flex;
  align-items: center;
  gap: 6px;
}
</style>
