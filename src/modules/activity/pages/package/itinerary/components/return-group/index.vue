<template>
  <div class="return-group-container">
    <OrderedList
      v-if="list.length"
      ref="orderedList"
      :list="list"
      :add-btn-text="$t('global_button_add')"
      @addNew="addNew"
    >
      <template #content="{ item, index }">
        <ReturnMap
          v-model="item.item"
          :item-field="itemFieldDict.map"
          :data="item.map"
          :item-order="index"
          :sub-widget-data="item"
          v-bind="$attrs"
        />
        <TimePeriod
          v-if="scheduleType === scheduleTypeKeyDict.date"
          required
          :item-field="itemFieldDict.time"
          :data="item.time"
          :item-order="index"
          :sub-widget-data="item"
          v-bind="$attrs"
        />
        <AvailableTimePicker
          v-if="scheduleType === scheduleTypeKeyDict.time"
          :item-field="itemFieldDict.time_select"
          :data="item.time_select"
          :item-order="index"
          :sub-widget-data="item"
          v-bind="$attrs"
        />
        <DetailTextarea
          :item-field="itemFieldDict.detail_multilang"
          :data="item.detail_multilang"
          :item-order="index"
          :sub-widget-data="item"
          v-bind="$attrs"
        />
      </template>
    </OrderedList>
  </div>
</template>

<script>
import ReturnMap from '../return-map/index.vue'
import TimePeriod from '../time-period/index'
import AvailableTimePicker from '../available-time-picker/index'
import DetailTextarea from '../detail-textarea/index.vue'
import subGroupMixin from '../../mixins/sub-group-mixin'

export default {
  name: 'ReturnGroup',
  components: {
    ReturnMap,
    TimePeriod,
    AvailableTimePicker,
    DetailTextarea
  },
  mixins: [subGroupMixin],
  data() {
    this.widgetSchemaDict = {
      time_select: {
        name: 'Return_Time_Duration',
        ref_field_tag: 'itinerary_return_time_select'
      },
      map: {
        name: 'Return_Map',
        ref_field_tag: 'itinerary_return_map',
        widget_type: 12
      },
      detail_multilang: {
        name: 'Detail',
        ref_field_tag: 'itinerary_return_detail'
      }
    }
    return {}
  }
}
</script>
