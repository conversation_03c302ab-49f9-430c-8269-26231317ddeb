<template>
  <div>
    <a-form-model ref="form" :model="form" :rules="rules">
      <a-form-model-item :label="$t('78874')" :colon="false" prop="type" required>
        <a-radio-group v-model="form.type">
          <a-radio :value="0">{{ $t('78933') }}</a-radio>
          <a-radio :value="1">{{ $t('78934') }}</a-radio>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item v-if="form.type === 1" :label="$t('78875')" :colon="false" prop="value" required>
        <a-input
          v-model="customName"
          class="form-item__ctrl"
          :placeholder="$t('global_please_input')"
          :allow-clear="true"
          :disabled="isPublishWithAi"
        ></a-input>
        <div v-if="refValue">
          <a-input v-model="refValue" class="form-item__ctrl" style="margin: -10px 0 16px;" disabled />
        </div>
      </a-form-model-item>
      <a-form-model-item prop="place_id">
        <POIButton
          v-if="form.type === 1"
          class="map-poi-wrap"
          :hide-photo="true"
          :current-poi="currentPOI"
          :drawer-title="$t('78875')"
          :map-text="$t('78848')"
          :custom-name="customName"
          :disabled-map-button="provideData.disabled.all || provideData.disabled.freeText"
          :invalidate-poi="invalidatePoi"
          button-type="default"
          @mapDrawerConfirm="mapDrawerConfirm"
        >
          <span slot="mapButton" slot-scope="scope">
            <svg-icon class="poi-button-icon" icon-name="icon-travel-location-xs" />{{ scope.mapButtonText }}
          </span>
        </POIButton>
      </a-form-model-item>
    </a-form-model>
  </div>
</template>
<script>
import POIButton from '../poi-button/index.vue'
import poiMixin from '../../mixins/poi-mixin'

export default {
  name: 'ReturnMap',
  components: {
    POIButton
  },
  mixins: [poiMixin],
  data() {
    const typeFn = (r, value, callback) => {
      if (typeof value === 'number') {
        return callback()
      }
      callback(new Error(this.$t('global_select')))
    }
    const validatorPOI = (rule, value, callback) => {
      // 去掉校验
      // if (!value && this.form.type === 1) {
      //   this.invalidatePoi = true
      //   return callback(new Error(' '))
      // }
      // this.invalidatePoi = false
      callback()
    }
    return {
      form: {
        type: undefined,
        place_id: undefined,
        images: [],
        value: 1
      },
      rules: {
        place_id: [
          {
            validator: validatorPOI,
            trigger: ['change', 'blur']
          }
        ],
        value: [
          {
            validator: this.validatorFn,
            trigger: ['change', 'blur']
          }
        ],
        type: [
          {
            validator: typeFn
          }
        ]
      },
      invalidatePoi: false
    }
  },
  methods: {
    getPreviewData() {
      let data = []
      if (this.form.type) {
        data.push({
          title: this.$t('78874'),
          text: this.$t('78934')
        })

        if (this.customName) {
          data.push({
            title: this.$t('78843'),
            text: this.customName
          })
        }
      } else {
        data.push({
          title: this.$t('78874'),
          text: this.$t('78933')
        })
      }

      return {
        widget_type: this.data.widget_type,
        ref_field_tag: this.data.ref_field_tag,
        data
      }
    },
    initForm(value) {
      const data = this.initData(value)
      const { type, place_id = '', images = [], title_multilang } = data
      this.form.type = type
      this.form.place_id = this.getDefaultPlaceId(place_id, title_multilang)
      this.form.images = images
      this.customName = title_multilang
      this.getPOIFromValue(data, this.form.place_id)
    },
    validatorFn(rule, value, callback) {
      if (this.form.type === 0) {
        callback()
        return
      }
      const customName = this.customName.trim()
      if (!customName) {
        return callback(new Error(this.$t('global_please_input')))
      }
      callback()
    },
    getFormData() {
      const {
        form: { type, place_id = '', images }
      } = this
      if (type === 0) {
        return JSON.stringify({ type, images: [] })
      }
      const map = this.initMap(place_id)
      return JSON.stringify({ ...map, type, images })
    }
  }
}
</script>

<style scoped lang="scss">
.form-item__ctrl {
  width: 400px;
}

.map-poi-wrap {
  margin-top: 0px;

  .poi-button-icon {
    margin-right: 10px;
  }
}
</style>
