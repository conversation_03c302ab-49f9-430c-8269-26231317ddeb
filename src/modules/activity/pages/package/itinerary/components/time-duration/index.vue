<template>
  <a-form-model
    ref="form"
    :model="form"
    label-width="0"
    class="time-duration-container"
    @submit.native.prevent
  >
    <a-form-model-item
      v-if="!hideTime"
      prop="ti"
      :rules="{
        validator: validator,
        trigger: 'blur'
      }"
    >
      <a-checkbox v-if="!onlyInputTime" v-model="form.ti.check" class="checkbox">
        {{ $t('78851') }}
      </a-checkbox>
      <a-select
        v-model="form.ti.hours"
        class="select"
        :allow-clear="form.ti.hours !== ''"
        :show-search="true"
        :options="hourOptions"
        :filter-option="filterOption"
        :disabled="!form.ti.check"
        @change="validateField('ti')"
      />
      <span class="divider">:</span>
      <a-select
        v-model="form.ti.minutes"
        class="select"
        :allow-clear="form.ti.minutes !== ''"
        :show-search="true"
        :options="minuteOptions"
        :filter-option="filterOption"
        :disabled="!form.ti.check"
        @change="validateField('ti')"
      />
    </a-form-model-item>

    <a-form-model-item
      v-if="!onlyInputTime"
      prop="du"
      :rules="{
        validator: validator,
        trigger: 'blur'
      }"
    >
      <a-checkbox v-model="form.du.check" class="checkbox">
        {{ $t('78852') }}
      </a-checkbox>
      <a-select
        v-model="form.du.hours"
        class="select"
        :allow-clear="form.du.hours !== ''"
        :show-search="true"
        :options="hourOptions"
        :filter-option="filterOption"
        :disabled="!form.du.check"
        @change="validateField('du')"
      />
      <span class="text">{{ $t('78839') }}</span>
      <a-select
        v-model="form.du.minutes"
        class="select"
        :allow-clear="form.du.minutes !== ''"
        :show-search="true"
        :options="minuteOptions"
        :filter-option="filterOption"
        :disabled="!form.du.check"
        @change="validateField('du')"
      />
      <span class="text">{{ $t('78840') }}</span>
    </a-form-model-item>
  </a-form-model>
</template>

<script>
import { addZero } from '@activity/utils'
import { getSecond, parseSecond, ABBR_NAME_DICT, eventBus, FORCE_THROW_TI_DU_ERROR } from '../../utils/index'

export default {
  name: 'TimeDuration',
  props: {
    data: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    groupRefFieldTag: {
      type: String,
      default: ''
    },
    group: {
      type: Object,
      default: () => ({})
    },
    scheduleType: {
      type: String,
      default: ''
    }
  },
  data() {
    this.hourOptions = Array(24)
      .fill()
      .map((item, index) => ({
        label: addZero(index),
        value: index
      }))

    this.minuteOptions = Array(60)
      .fill()
      .map((item, index) => ({
        label: addZero(index),
        value: index
      }))

    return {
      form: {
        ti: {
          hours: '',
          minutes: '',
          check: false
        },
        du: {
          hours: '',
          minutes: '',
          check: false
        }
      }
    }
  },
  computed: {
    onlyInputTime() {
      return [ABBR_NAME_DICT.departure, ABBR_NAME_DICT.return, ABBR_NAME_DICT.accommodation].includes(
        this.groupRefFieldTag
      )
    },
    hideTime() {
      const scheduleType = this.scheduleType
      return scheduleType === 'itinerary_header_time_sold_by_time'
    },
    isInvalidate() {
      return this.hideTime && this.onlyInputTime
    }
  },
  watch: {
    'form.ti.check': {
      handler(v) {
        this.resetValueWhenCheck(v, 'ti')
      }
    },
    'form.du.check': {
      handler(v) {
        this.resetValueWhenCheck(v, 'du')
      }
    },
    isInvalidate: {
      immediate: true,
      handler(v) {
        this.$emit('toggle', !v)
      }
    },
    // 切换scheduleType时重置time编辑状态
    hideTime(v) {
      if (!v) {
        this.form.ti.check = this.onlyInputTime
      }
    }
  },
  mounted() {
    this.initData()
    if (this.onlyInputTime) {
      this.form.ti.check = true
    }
    const key = `${FORCE_THROW_TI_DU_ERROR}_${this.group.uuid}`
    eventBus.$off(key).$on(key, this.forceThrowErrorFunc)
    this.$once('hook:beforeDestroy', () => {
      eventBus.$off(key)
    })
  },
  methods: {
    resetValue() {
      this.$set(this, 'form', {
        ti: {
          hours: '',
          minutes: '',
          check: this.onlyInputTime
        },
        du: {
          hours: '',
          minutes: '',
          check: false
        }
      })
    },
    getPreviewData() {
      let data = []
      const { ti, du } = this.form

      if (ti.check && ti.hours !== '' && ti.minutes !== '') {
        data.push({
          title: this.$t('78851'),
          text: `${addZero(ti.hours)}:${addZero(ti.minutes)}`
        })
      }

      if (du.check && du.hours !== '' && du.minutes !== '') {
        data.push({
          title: this.$t('78852'),
          text: `${du.hours} ${this.$t('78839')} ${du.minutes} ${this.$t('78840')}`
        })
      }

      return {
        widget_type: this.data.widget_type,
        ref_field_tag: this.data.ref_field_tag,
        data
      }
    },
    forceThrowErrorFunc({ field, message }) {
      if (field && message) {
        this.recordThrowData = {
          [field]: message
        }

        this.$refs.form.validateField(field)
      }
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    resetValueWhenCheck(value, field) {
      if (!value) {
        Object.assign(this.form[field], {
          hours: '',
          minutes: ''
        })
        this.$refs.form.clearValidate(field)
      } else if (this.chooseAtLeastOne) {
        this.chooseAtLeastOne = false
        this.$refs.form.validate()
      }
    },
    checkData(data) {
      let { hours, minutes } = data
      if (typeof hours === 'number' && typeof minutes === 'number') {
        return true
      }
      return false
    },
    getData() {
      let { ti, du } = _.cloneDeep(this.form)

      // const checkTi = this.checkData(ti)
      // const checkDu = this.checkData(du)

      // if (!checkTi && !checkDu) {
      //   return []
      // }

      if (!ti.check) {
        ti.hours = ti.minutes
      }

      if (!du.check) {
        du.hours = du.minutes
      }

      const value = {
        ti: getSecond(ti),
        du: getSecond(du),
        ti_enable: this.hideTime ? false : ti.check
      }

      return [
        {
          create: !this.dataId,
          is_free_text: true,
          id: this.dataId,
          widget_type: this.data.widget_type,
          value: JSON.stringify(value)
        }
      ]
    },
    validateForm() {
      return new Promise((resolve) => {
        if (this.isInvalidate) {
          resolve(true)
        } else if (this.form.ti.check || this.form.du.check) {
          this.$refs.form.validate(resolve)
        } else if (this.data.required) {
          this.chooseAtLeastOne = true
          this.$refs.form.validate()
          resolve(false)
        } else {
          resolve(true)
        }
      })
    },
    validateField(field) {
      this.$refs.form.validateField(field)
    },
    isValidData(value) {
      return value !== '' && value !== undefined
    },
    validator(rule, value, callback) {
      if (this.recordThrowData?.[rule.field]) {
        callback(new Error(this.recordThrowData[rule.field]) ?? 'error')
        setTimeout(() => {
          this.recordThrowData[rule.field] = null
        })
      } else if (this.chooseAtLeastOne) {
        callback(new Error('Please choose at least one'))
      } else if (value.check) {
        const { required } = this.data
        const validH = this.isValidData(value.hours)
        const validM = this.isValidData(value.minutes)
        if (
          (required && validH && validM) || // 必填且都为有效
          (!required && !(validH ^ validM)) // 非不填，全为空或者全为有效
        ) {
          callback()
        } else {
          callback(new Error('Please select'))
        }
      } else {
        callback()
      }
    },
    async initData() {
      const { attr_values } = this.data
      if (Array.isArray(attr_values) && attr_values?.[0]?.value) {
        this.dataId = attr_values[0].id
        const date = JSON.parse(attr_values[0].value)
        let ti, du

        if (date.ti_enable) {
          ti = parseSecond(date.ti)
        } else {
          ti = {
            hours: '',
            minutes: ''
          }
        }

        if (date.du) {
          du = parseSecond(date.du)
        } else {
          du = {
            hours: '',
            minutes: ''
          }
        }

        this.$set(this, 'form', {
          ti: {
            ...ti,
            check: date.ti_enable
          },
          du: {
            ...du,
            check: date.du >= 0
          }
        })
      } else {
        this.dataId = 0
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.time-duration-container {
  .checkbox {
    width: 100px;
  }
  .select {
    width: 64px;
  }
  .divider,
  .text {
    display: inline-block;
    margin: 0 8px;
  }
  .text {
    margin-right: 16px;
  }
}
</style>
