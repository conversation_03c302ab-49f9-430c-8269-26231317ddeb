<template>
  <a-form-model
    ref="form"
    class="time-period"
    :class="{ '--is-empty': isEmpty, '--is-time': isTime, '--is-time-period': isTimePeriod }"
    :model="form"
  >
    <a-form-model-item
      :colon="false"
      prop="time"
      :rules="[
        {
          required: currRequired,
          validator: validator,
          trigger: 'change'
        }
      ]"
    >
      <template #label>
        {{ label }}
        <span
          v-if="(displayReset || !currRequired) && (isTime || isTimePeriod)"
          class="common__reset-btn"
          @click="handleReset"
        >
          <svg-icon icon-name="reset" />
          {{ $t('global_reset') }}
        </span>
      </template>

      <a-radio-group v-model="radioValue" class="time-period__choice" @change="onChange">
        <div class="time-period__choice-item time">
          <a-radio class="choice-item__radio" :value="radioDict.time">{{ $t('78851') }}</a-radio>
          <TimePicker :key="`from_time_${refresh}`" v-model="form.formTime" :disabled="!isTime" />
        </div>

        <div class="time-period__choice-item time-period">
          <a-radio class="choice-item__radio" :value="radioDict.timePeriod">{{ $t('168075') }}</a-radio>
          <span class="choice-item__picker-box">
            <TimePicker
              ref="periodFrom"
              :key="`period_from_${refresh}`"
              v-model="form.period.from"
              :disabled="!isTimePeriod"
            />
            <span>-</span>
            <TimePicker :key="`period_to_${refresh}`" v-model="form.period.to" :disabled="!isTimePeriod" />
          </span>
        </div>
      </a-radio-group>
    </a-form-model-item>
  </a-form-model>
</template>

<script>
import TimePicker from './time-picker.vue'
import { parseJsonString } from '@activity/pages/package/itinerary/utils/index.js'
import { eventBus, FORCE_THROW_TI_DU_ERROR } from '../../utils/index'

const radioDict = {
  time: 1,
  timePeriod: 2
}

export default {
  name: 'TimePeriod',
  components: {
    TimePicker
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    label: {
      type: String,
      default: __('168076')
    },
    displayReset: {
      type: Boolean,
      default: false
    },
    group: {
      type: Object,
      default: () => ({})
    },
    itemOrder: {
      type: Number,
      default: 0
    },
    subWidgetData: {
      type: Object,
      default: () => ({})
    },
    scheduleType: {
      type: String,
      default: ''
    },
    required: {
      type: Boolean,
      debugger: false
    }
  },
  data() {
    return {
      errorMessage: '',
      radioDict,
      form: {
        formTime: -1,
        period: {
          from: -1,
          to: -1
        }
      },
      radioValue: undefined,
      refresh: 0, // hack 刷新 time-picker 组件以重新触发 initData
      dataId: 0
    }
  },
  computed: {
    isEmpty() {
      return !(this.isTime || this.isTimePeriod)
    },
    isTime() {
      return this.radioValue === radioDict.time
    },
    isTimePeriod() {
      return this.radioValue === radioDict.timePeriod
    },
    currRequired() {
      return !!this.data.required || this.required
    }
  },
  watch: {
    isTime(flag) {
      if (!flag) {
        this.form.formTime = -1
        this.refresh += 1
      }
    },
    isTimePeriod(flag) {
      if (!flag) {
        Object.assign(this.form.period, {
          from: -1,
          to: -1
        })
        this.refresh += 1
      }
    },
    form: {
      deep: true,
      handler() {
        this.timeErrMsg = ''
        this.validateForm()
        // this.$refs.form.resetFields()
      }
    }
  },
  beforeMount() {
    this.initData()

    const uuid = this.subWidgetData.uuid
    const key = `${FORCE_THROW_TI_DU_ERROR}_${this.group.uuid}_${uuid}`
    uuid && eventBus.$off(key).$on(key, this.forceThrowErrorFunc)
    this.$once('hook:beforeDestroy', () => {
      eventBus.$off(key)
    })
  },
  methods: {
    getData() {
      let value

      if (this.isTime) {
        value = { from: this.form.formTime, to: -1 }
      } else if (this.isTimePeriod) {
        const { from, to } = this.form.period
        value = { from, to }
      } else {
        value = { from: -1, to: -1 }
      }

      return [
        {
          create: !this.dataId,
          is_free_text: true,
          id: this.dataId,
          value: JSON.stringify(value)
        }
      ]
    },
    validateForm() {
      return new Promise((resolve) => {
        this.$refs.form.validate((valid) => resolve(valid))
      })
    },
    forceThrowErrorFunc({ message }) {
      this.timeErrMsg = message
      this.validateForm()
    },
    async initData() {
      const value = parseJsonString(this.data.value)

      if (Object.prototype.hasOwnProperty.call(value, 'from')) {
        const { from, to } = value

        if (this.isValidData(to)) {
          this.radioValue = radioDict.timePeriod
          Object.assign(this.form.period, {
            from,
            to
          })
        } else if (this.isValidData(from)) {
          this.radioValue = radioDict.time
          this.form.formTime = from
        }
      }
    },
    // from / to 字段空值为 -1
    isValidData(value) {
      return value !== '' && value !== undefined && value !== -1
    },
    validator(rule, value, callback) {
      if (this.timeErrMsg) {
        callback(new Error(this.timeErrMsg))
        this.timeErrMsg = ''
        return
      }

      const { from, to } = this.form.period

      if (
        (this.currRequired && !(this.isTime || this.isTimePeriod)) ||
        (this.isTime && !this.isValidData(this.form.formTime)) ||
        (this.isTimePeriod && !(this.isValidData(from) && this.isValidData(to)))
      ) {
        callback(new Error(this.$t('global_please_select')))
      } else if (this.isTimePeriod && this.isValidData(from) && this.isValidData(to) && to <= from) {
        callback(new Error(this.$t('78882')))
      } else {
        callback()
      }
    },
    async onChange(event) {
      const value = event.target.value

      if (value === undefined) {
        this.$set(this, 'form', this.$options.data().form)
      } else if (
        value === radioDict.timePeriod &&
        !this.isValidData(this.form.period.from) &&
        this.isValidData(this.form.formTime)
      ) {
        // 当切换到区间时，自动赋值 formTime 到 period.from
        this.form.period.from = this.form.formTime

        await this.$nextTick()
        this.$refs.periodFrom.initData()
      }
    },
    handleReset() {
      this.radioValue = undefined
      this.$set(this, 'form', this.$options.data().form)
      this.refresh += 1
      this.$refs.form.clearValidate()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../style/mixins.scss';

$errColor: #f5222d;

@mixin err-select-mixin {
  .ant-select-selection {
    border-color: $errColor;
  }

  .ant-select-arrow {
    color: $errColor;
  }
}

.time-period {
  @include commonResetBtnMixin;

  &__choice {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &__choice-item {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;

    .choice-item__radio {
      flex: 0 0 100px;
      width: 100px;
      margin-right: 16px;
    }

    .choice-item__picker-box {
      display: inline-flex;
      align-items: center;
      gap: 16px;
    }
  }

  ::v-deep .has-error {
    .ant-select-arrow {
      color: rgba(0, 0, 0, 0.25);
    }

    .ant-select-selection {
      border-color: #d9d9d9;
    }
  }

  &.--is-empty {
    ::v-deep .has-error .ant-radio-inner {
      border-color: $errColor;
    }
  }

  &.--is-time {
    ::v-deep .has-error .time-period__choice-item.time {
      @include err-select-mixin;
    }
  }

  &.--is-time-period {
    ::v-deep .has-error .time-period__choice-item.time-period {
      @include err-select-mixin;
    }
  }
}
</style>
