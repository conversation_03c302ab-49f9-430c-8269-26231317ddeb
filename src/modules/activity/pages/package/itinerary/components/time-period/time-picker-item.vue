<template>
  <a-select
    v-model="currVal"
    class="time-picker-item"
    :show-search="true"
    :options="options"
    :filter-option="filterOption"
    v-bind="$attrs"
  />
</template>

<script>
import { addZero } from '@activity/utils'

export default {
  name: 'TimePickerItem',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [Number, String],
      default: undefined
    },
    isHours: {
      type: Boolean,
      default: true
    }
  },
  data() {
    this.options = Array(this.isHours ? 24 : 60)
      .fill()
      .map((_, index) => ({
        label: addZero(index),
        value: index
      }))
    return {}
  },
  computed: {
    currVal: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('change', value)
      }
    }
  },
  methods: {
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }
  }
}
</script>

<style lang="scss" scoped>
.time-picker-item {
  width: 64px;
}
</style>
