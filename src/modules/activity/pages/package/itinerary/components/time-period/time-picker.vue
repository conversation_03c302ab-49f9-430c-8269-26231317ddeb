<template>
  <div class="time-container">
    <TimePickerItem v-model="time.hours" v-bind="$attrs" />
    <span class="divider">:</span>
    <TimePickerItem v-model="time.minutes" :is-hours="false" v-bind="$attrs" />
  </div>
</template>

<script>
import TimePickerItem from './time-picker-item.vue'
import { getSecond, parseSecond } from '@activity/pages/package/itinerary/utils/index.js'

export default {
  name: 'TimePicker',
  components: {
    TimePickerItem
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [Number, String],
      default: -1
    }
  },
  data() {
    return {
      time: {
        hours: -1,
        minutes: -1
      }
    }
  },
  watch: {
    time: {
      deep: true,
      handler(value) {
        this.$emit('change', getSecond(value))
      }
    }
  },
  beforeMount() {
    this.initData()
  },
  methods: {
    initData() {
      this.$set(this, 'time', parseSecond(this.value))
    }
  }
}
</script>

<style lang="scss" scoped>
.time-container {
  display: inline-flex;
  gap: 8px;
  align-items: center;
}
</style>
