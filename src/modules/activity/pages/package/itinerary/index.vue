<template>
  <div v-disabled="vDisabled" class="itinerary-container">
    <div v-if="copy.options.length >= 1" class="box copy">
      <a-button type="default" @click="handleCopy">{{ $t('78876') }}</a-button>
    </div>

    <a-modal
      :title="copy.title"
      :visible="copy.visible"
      :confirm-loading="copy.confirmLoading"
      :ok-button-props="{ props: { disabled: !copy.value } }"
      @ok="copy.handleOk"
      @cancel="copy.handleCancel"
    >
      <a-spin :spinning="copy.loading">
        <a-radio-group v-model="copy.value" class="copy-radio-group" :options="copy.options" />
      </a-spin>
    </a-modal>

    <ChunkGroups
      v-for="(instance, index) of chunkGroupsData"
      v-show="scheduleType || index === 0 || !hasTimeSelect"
      :key="index"
      ref="instance"
      class="chunk-item"
      :class="{
        'is-first-chunk': index === 0,
        'is-last-chunk': index === chunkGroupsData.length - 1
      }"
      :chunks="instance.chunks"
      :index="index"
      :type="instance.type"
      :templates="provideData.templates"
      :display-add-btn="instance.type === INSTANCE_TYPE_DICT.timeline"
      :nonactivated-group-keys.sync="nonactivatedGroupKeys"
      :schedule-type="scheduleType"
      :itinerary-version="provideData.itineraryVersion"
    >
      <template v-if="index === 0" #title>
        <div class="day-title-content first-chunk-title">
          <a-icon
            class="timeline-collapse-icon"
            :type="nonactivatedGroupKeys.length === allGroupUuidData.length ? 'down' : 'up'"
            @click="handleSwitchAllActive"
          />
          <span class="day-title-text">{{ $t('78937') }}</span>
        </div>
      </template>
    </ChunkGroups>

    <div v-if="isAudioUploadVisible" class="audio-upload-block-container">
      <AudioUploadBlock :isBtnDisabled="isAudioUploadBtnDisabled" />
    </div>

    <div v-if="groupsSummary.some((item) => item.summary.length)" class="group-summary-list box">
      <h2 class="__title">Old itinerary</h2>
      <p class="__tips">Please migrate them with the new itinerary module</p>
      <template v-for="group in groupsSummary">
        <groupSummary
          v-if="group.summary && group.summary.length"
          :key="group.id"
          class="__summary"
          :group="group"
        >
          <p slot="groupName" class="summary-group-name">{{ group.name }}</p>
        </groupSummary>
      </template>
    </div>

    <CommonFooter class="merchant-footer">
      <a-popconfirm
        v-if="!isCreate && !isRequired"
        :title="$t('voucher.delete_note')"
        ok-text="Yes"
        cancel-text="No"
        @confirm="handleClearInstance"
      >
        <a-button type="link" style="color: #ff4d4f">
          {{ $t('act_blocklist_clear_all') }}
        </a-button>
      </a-popconfirm>
      <a-button
        v-if="!activityIsUnablePreview"
        v-bind="calcSaveData"
        class="v-disabled-exclude js-edit-save-btn"
        @click="handlePreview"
      >
        {{ $t('global_preview') }}
      </a-button>
      <a-button type="primary" class="v-disabled-exclude" @click="handleSave">
        {{ $t('global_save') }}
      </a-button>
    </CommonFooter>
  </div>
</template>

<script>
import { mapState, mapActions, mapMutations, mapGetters } from 'vuex'
import {
  INSTANCE_TYPE_DICT,
  CHUNK_REF,
  eventBus,
  STAY_REF_KEY,
  HEAD_NUMS_REF_KEY,
  INJECT_ATTR_OPT_VALUE_EVENT_KEY,
  ATTR_OPT_VALUE_CHANGE_KEY,
  parseSecond,
  FORCE_THROW_TI_DU_ERROR
} from './utils/index'
import ChunkGroups from './components/ChunkGroups'
import AudioUploadBlock from './components/audio-upload-block/index.vue'
import { getUuid, getEditLang, getRefLang } from '@activity/utils'
import { pmsConfirm } from '@activity/utils'
import { addZero } from '@activity/utils'
import { computedGroupSummary } from '@activity/components/groupSummary/utils.js'
import groupSummary from '@activity/components/groupSummary'
import CommonFooter from '@activity/components/CommonFooter.vue'
import { ITINERARY_CATEGORY_IDS } from '@activity/utils/const'
import { getPreviewValiditySync } from '@activity/components/preview-validity'
import { getMerchantEditSaveSpm } from '@activity/pages/activityManagement/dataLib.vue'
import { groupRefFieldTagDict } from '@activity/pages/package/extra/const.js'
import {
  scheduleTypeKeyDict,
  itineraryVersionDict,
  parseJsonString,
  audioPermissionDict
} from '@activity/pages/package/itinerary/utils/index.js'

const copy_mixin = {
  data() {
    return {
      copy: {
        title: __('78877'),
        visible: false,
        loading: false,
        confirmLoading: false,
        value: undefined,
        options: [],
        handleOk: async () => {
          this.copy.confirmLoading = true

          await ajax.postBody(ADMIN_API.act.copy_package_itinerary_instance_data, {
            data: {
              from_package_id: this.copy.value,
              to_package_id_list: [this.packageId],
              category_id: this.categoryId
            }
          })

          this.$message.success(this.$t('global_success'))
          this.copy.value = undefined
          this.copy.confirmLoading = this.copy.visible = false
          this.refreshAllData()
        },
        handleCancel: () => {
          this.copy.visible = false
          this.copy.value = undefined
        }
      }
    }
  },
  computed: {
    ...mapGetters(['saveBeforeStatus2getters']),
    calcSaveData() {
      const { name } = this.$route
      const spm = getMerchantEditSaveSpm(name)
      if (!spm) {
        return {}
      }
      const obj = {
        'data-spm-module': `${spm}`,
        'data-spm-virtual-item': '__virtual?trg=manual'
      }
      return obj
    },
    packageId() {
      return +this.$route.query.package_id
    },
    isAudioUploadVisible() {
      const { audioPermission } = this.provideData || {}
      return [audioPermissionDict['EDITABLE'], audioPermissionDict['READ_ONLY']].includes(audioPermission)
    },
    isAudioUploadBtnDisabled() {
      const { audioPermission } = this.provideData || {}
      const isBtnClickable = [audioPermissionDict['EDITABLE']].includes(audioPermission)
      return !isBtnClickable
    }
  },
  methods: {
    async initCopyOptions() {
      const response = await ajax.get(ADMIN_API.act.get_exist_itinerary_package_ids, {
        params: {
          activity_id: this.activityId,
          page_from: this.page_from,
          language: getEditLang(),
          category_id: this.categoryId
        }
      })
      const packages = response.packages || []
      // 没有实例数据（即初始化的时候）需要自动勾选 行程因天气的影响 选项
      if (!packages.some((pkg) => pkg.id === this.packageId)) {
        await this.$nextTick()
        eventBus.$emit(`${INJECT_ATTR_OPT_VALUE_EVENT_KEY}_itinerary_footinfo_type_weather_change`, {
          action: 'autoCheck'
        })
        this.isCreate = true
      } else {
        this.isCreate = false
      }

      this.$set(
        this.copy,
        'options',
        packages.reduce((acc, curr) => {
          if (curr.id === this.packageId) {
            return acc
          }

          return [
            ...acc,
            {
              value: curr.id,
              label: `${curr.id}-${curr.name}`
            }
          ]
        }, [])
      )
    },
    async handleCopy() {
      this.copy.loading = this.copy.visible = true
      await this.initCopyOptions()
      this.copy.loading = false
    }
  }
}

export default {
  name: 'PackageItinerary',
  inject: ['refreshPkgs', 'refreshPage', 'page_from', 'rootChangeLoading'],
  components: {
    ChunkGroups,
    groupSummary,
    CommonFooter,
    AudioUploadBlock
  },
  mixins: [copy_mixin],
  provide() {
    return {
      editLang: getEditLang(),
      refLang: getRefLang(),
      beforeChangeValueOnSelect: async ({ data, oldVal, newVal, options }) => {
        const { key } = data

        if (key === HEAD_NUMS_REF_KEY) {
          let oldNum = oldVal ? +options.find((item) => item.id === oldVal).value : undefined
          const newNum = +options.find((item) => item.id === newVal)?.value || undefined

          if (!oldNum || oldNum === newNum) {
            return
          }

          let resp
          if (_.isEqual(this.cachePostData, this.getPostData())) {
            resp = true
          } else {
            resp = await pmsConfirm.call(this, {
              content: newNum > oldNum ? __('78879') : __('78880')
            })
          }

          if (resp) {
            let newDaysData = this.allData[1].data
            this.provideData.itineraryNums = newNum
            let { data: allDayGroupsData, returnGroups } = this.formatDayChunkData(newDaysData)

            if (newNum > oldNum) {
              // Fill in the new data
              Array(newNum - oldNum)
                .fill()
                .forEach(() => {
                  allDayGroupsData.push({
                    groups: [],
                    uuid: getUuid(),
                    data_index: oldNum++
                  })
                })

              allDayGroupsData[allDayGroupsData.length - 1].groups.push(...returnGroups)
            } else {
              allDayGroupsData = allDayGroupsData.slice(0, newNum)

              if (newNum === 1) {
                // Needs to be filtered to the accommodation
                allDayGroupsData.forEach((data) => {
                  data.groups = data.groups.filter((item) => item.ref_field_tag !== STAY_REF_KEY)
                })
              }

              // Insert the previous return groups
              allDayGroupsData.lastItem.groups.push(...returnGroups)
            }

            this.$set(this.allData[1], 'data', allDayGroupsData)
          } else {
            return {
              value: oldVal
            }
          }
        }
      },
      provideData: this.provideData,
      getImageGallery: this.getGallery,
      setScheduleType: this.setScheduleType,
      getBasicData: () => {
        return this.basicData
      }
    }
  },
  data() {
    this.INSTANCE_TYPE_DICT = INSTANCE_TYPE_DICT

    return {
      allData: [],
      allVariables: [],
      provideData: {
        templates: {},
        itineraryNums: 1,
        vDisabled: {
          lock: false
        },
        disabled: {
          freeText: false,
          all: false
        },
        extraPickInfoList: [],
        itineraryVersion: itineraryVersionDict.v1,
        editMapPermission: undefined,
        merchantCurrency: undefined,
        audioPermission: undefined
      },
      isCreate: false,
      groupsSummary: [],

      nonactivatedGroupKeys: [],
      scheduleType: '',
      basicData: null,
      hasTimeSelect: false,
      activityDepartureCity: {},
      editMapPermission: undefined,
      stockOutType: undefined
    }
  },
  computed: {
    ...mapState({
      isMerchant: (state) => state.isMerchant,
      lockMerchantActEditing: 'lockMerchantActEditing'
    }),
    ...mapGetters(['lockMerchantEditFreeText2getter', 'activityIsUnablePreview']),
    isRequired() {
      const curr = ITINERARY_CATEGORY_IDS.find((item) => item.id === this.categoryId)

      return !!curr?.required
    },
    vDisabled() {
      // pending
      if (this.isMerchant) {
        const excludeElements = this.$store.state.isMC2BD ? [] : ['exclude-disabled']
        if (this.lockMerchantActEditing) {
          return {
            lock: true,
            scope: 'all',
            excludeElements
          }
        }

        if (this.lockMerchantEditFreeText2getter) {
          return {
            lock: true,
            defaultScope: 'freeText',
            excludeElements,
            scope: 'freeText'
          }
        }
      }

      return {
        lock: false
      }
    },
    activityId() {
      return +this.$route.params.id
    },
    categoryId() {
      return this.$store.state.categoryInfo?.sub_category_id
    },
    mapIdOnAllVariables() {
      return this.allVariables.reduce(
        (acc, curr) => ({
          ...acc,
          [curr.id]: curr
        }),
        {}
      )
    },
    chunkGroupsData() {
      const result = this.allData.map((item) => {
        let chunks = item.data.reduce(
          (acc, day, data_index) => {
            const isTimeline = item.type === 'timeline'
            const { groups } = day
            const title = klook.parseStr1(__('78938'), {
              num: data_index + 1
            })

            const uiConfig = acc[1].groups[acc[1].groups.length - 1]?.uiConfig
            if (item.type === 'timeline' && !day.groups.length && uiConfig) {
              uiConfig.emptyDays.push({
                data_index,
                title
              })
            }

            const siblingGroupUUIDList = groups.map((group) => group.uuid)

            groups.forEach((group, group_index) => {
              // 是否是一天的最上面 group
              const oneDayFront = isTimeline && group_index === 0

              let uiConfig = {
                isTimeline,
                chunkGroupName: group.ref_field_tag,
                title: oneDayFront ? title : '',
                oneDayFront,
                data_index,
                group_index,
                isFirstDay: data_index === 0,
                isLastDay: data_index === item.data.length - 1,
                isFirstGroup: group_index === 0,
                isLastGroup: groups.length - 1 === group_index,
                emptyDays: [],
                siblingGroupUUIDList
              }

              group.widget_list = group.widget_list.map((widget) => {
                if (typeof widget.config === 'string') {
                  widget.config = JSON.parse(widget.config || '{}')
                }

                return widget
              })

              if (group.ref_field_tag === CHUNK_REF.departure) {
                // 只剩下一个管理项的时候不能删除和拖拽
                uiConfig.onlyOneAssociatedItem =
                  groups.filter((group) => group.ref_field_tag === CHUNK_REF.departure).length <= 1

                acc[0].groups.push({
                  ...group,
                  uiConfig
                })
              } else if (group.ref_field_tag === CHUNK_REF.return) {
                uiConfig.onlyOneAssociatedItem =
                  groups.filter((group) => group.ref_field_tag === CHUNK_REF.return).length <= 1

                acc[2].groups.push({
                  ...group,
                  uiConfig
                })
              } else {
                uiConfig.chunkGroupName = CHUNK_REF.common
                uiConfig.onlyOneAssociatedItem = item.type === 'default'

                acc[1].groups.push({
                  ...group,
                  uiConfig
                })
              }
            })

            return acc
          },
          [
            { type: CHUNK_REF.departure, groups: [] },
            { type: CHUNK_REF.common, groups: [] },
            { type: CHUNK_REF.return, groups: [] }
          ]
        )

        return {
          type: item.type,
          // head / foot 只有 common 的数据，所以需要过滤
          chunks: chunks.filter((chunk) => chunk.groups.length)
        }
      })

      return result
    },
    allGroupUuidData() {
      return this.chunkGroupsData.reduce((acc, curr) => {
        const res = curr.chunks.reduce((accChunks, chunk) => {
          return [...accChunks, ...chunk.groups.map((group) => group.uuid)]
        }, [])

        return [...acc, ...res]
      }, [])
    }
  },
  watch: {
    vDisabled: {
      deep: true,
      immediate: true,
      handler(v) {
        const { scope, ...other } = v
        this.provideData.vDisabled = other
        this.provideData.disabled = {
          all: scope === 'all',
          freeText: scope === 'freeText'
        }
      }
    }
  },
  async mounted() {
    eventBus.$off('sortDayGroup').$on('sortDayGroup', this.sortDayGroupFunc)
    eventBus.$off('addDayGroup').$on('addDayGroup', this.addDayGroupFunc)
    eventBus.$off('deleteDayGroup').$on('deleteDayGroup', this.deleteDayGroupFunc)
    eventBus.$off('changeStructSelectValue').$on('changeStructSelectValue', this.changeStructSelectValueFunc)
    eventBus.$off(ATTR_OPT_VALUE_CHANGE_KEY).$on(ATTR_OPT_VALUE_CHANGE_KEY, this.attrOptValueChangeFunc)

    await this.$store.dispatch('getActCategory2action', {
      activity_id: this.activityId
    })

    this.initData()
    this.getImageGallery()
    this.clearItineraryDepartureTypes()
  },
  beforeDestroy() {
    // 清除当前套餐搜索过的POI
    this.setPackageItineraryPOIList([])
    eventBus
      .$off('sortDayGroup')
      .$off('addDayGroup')
      .$off('deleteDayGroup')
      .$off('changeStructSelectValue')
      .$off(ATTR_OPT_VALUE_CHANGE_KEY)
  },
  methods: {
    ...mapActions(['getActVariables2actions', 'updatePkgStepStatus2action']),
    ...mapMutations([
      'setPackageItineraryPOIList',
      'setActivateFooterWrapper',
      'clearItineraryDepartureTypes'
    ]),
    setScheduleType(v) {
      this.scheduleType = v
    },
    getPostData() {
      const allInstanceData = (this.$refs.instance || []).map((instance) => instance.getData())

      return {
        error: this.$route.query.cancelValidateTime ? null : this.validateTime(allInstanceData[1]),
        data: allInstanceData.reduce(
          (acc, curr) => [
            ...acc,
            ...curr.map((group) => {
              // 去掉不需要的前端逻辑字段
              // eslint-disable-next-line no-unused-vars
              const { uuid, ...data } = group

              return data
            })
          ],
          []
        )
      }
    },
    /**
     * 保存校验，如果有填写time的话，同一天内 后面行程的Time必须晚于前面行程的Time
     * @param {*} instanceData 内容区所有天数的 groups 集合数据
     * @returns { uuid: string, position: { data_index: number, group_index: number }, eventBusKey?: string, data: { message: string, [key in string]: any } } | null
     */
    validateTime(instanceData = []) {
      const getTimestampValue = (value) => (value < 0 ? 0 : value)

      let currTimestamp = -1
      let currDayIndex = 0
      let errorTimestamp = null
      let eventBusKey
      // 天数据循环
      for (let group of instanceData) {
        const { uuid } = group
        // group widget 循环
        for (let widget of group.widgets) {
          const { data_index } = widget.extra_info
          const commonErrData = {
            uuid,
            position: widget.extra_info
          }
          // 重置新的一天数据
          if (data_index > currDayIndex) {
            currDayIndex = data_index
            currTimestamp = -1
          }
          // time duration widget
          if (widget.widget_type === 7) {
            const { ti, du, ti_enable } = JSON.parse(widget.attr_values[0].value)
            // 必填但未填
            if (
              scheduleTypeKeyDict.date === this.scheduleType &&
              widget.required &&
              ti === -1 &&
              (ti_enable || du === -1)
            ) {
              return {
                ...commonErrData,
                data: {
                  message: this.$t('global_please_select')
                }
              }
            }
            // 当前填写时间早于当天前面叠加时间总和时
            if (ti_enable && ti !== -1 && currTimestamp > ti) {
              errorTimestamp = currTimestamp
              eventBusKey = `${FORCE_THROW_TI_DU_ERROR}_${uuid}`
            }

            // 叠加更新当天时间节点
            !errorTimestamp &&
              (currTimestamp =
                (ti_enable ? getTimestampValue(ti) : !~currTimestamp ? 0 : currTimestamp) +
                getTimestampValue(du))
          } else if ([14, 15].includes(widget.widget_type)) {
            // 14： DepartureGroup；15：ReturnGroup；
            const { time } = parseJsonString(widget.attr_values[0].value)

            if (!time) {
              continue
            }

            const { from, to } = time || {}
            if (from !== -1) {
              // > 时间可以重复
              if (currTimestamp > from) {
                errorTimestamp = parseSecond(from)
              } else {
                currTimestamp = from
              }
            }

            if (!errorTimestamp && to !== -1) {
              // > 时间可以重复
              if (currTimestamp > to) {
                errorTimestamp = parseSecond(to)
              } else {
                currTimestamp = to
              }
            }

            if (errorTimestamp) {
              const widgetUuid = widget.attr_values?.[0]?.uuid ?? 0
              widgetUuid && (eventBusKey = `${FORCE_THROW_TI_DU_ERROR}_${uuid}_${widgetUuid}`)
            }
          }
          // 存在错误节点时间
          if (errorTimestamp) {
            const { hours, minutes } = parseSecond(currTimestamp)
            const time = `${addZero(hours)}:${addZero(minutes)}`

            return {
              ...commonErrData,
              eventBusKey,
              data: {
                message: `${this.$t('78882')}(${time})`,
                field: 'ti',
                index: widget.order
              }
            }
          }
        }
      }

      return null
    },
    async refreshAllData({ status = 1 } = {}) {
      this.initData()
      await this.updatePkgStepStatus2action({
        activity_id: this.activityId,
        package_id: this.packageId,
        language: getEditLang(),
        status,
        step: 'package_itinerary'
      })

      klook.bus.$emit('updatePkgInfos2bus', null, this.packageId)
      this.refreshPkgs()
    },
    async validateForm() {
      return (
        await Promise.all(this.$refs.instance.map(async (instance) => await instance.validateForm()))
      ).every((item) => item)
    },
    formatDayChunkData(daysData) {
      return _.cloneDeep(daysData).reduce(
        (acc, day, dayIndex) => {
          let groups = []

          day.groups.forEach((group) => {
            if (group.ref_field_tag === CHUNK_REF.return) {
              acc.returnGroups.push(group)
            } else {
              groups.push(group)
            }
          })

          acc.data[dayIndex] = {
            ...day,
            groups
          }

          return acc
        },
        {
          data: [],
          returnGroups: []
        }
      )
    },
    sortDayGroupFunc({ direction, position, uuid }) {
      let allDayGroupsData = _.cloneDeep(this.allData[1].data)
      const { data_index: old_data_index, group_index: old_group_index } = position
      let { data_index, group_index } = position
      let oldPositionData = null

      if (direction === 'up') {
        if (group_index === 0) {
          data_index -= 1
          group_index = allDayGroupsData[data_index].groups.length
        } else {
          group_index -= 1
        }

        oldPositionData = allDayGroupsData[old_data_index].groups.splice(old_group_index, 1)[0]
        allDayGroupsData[data_index].groups.splice(group_index, 0, oldPositionData)
      } else {
        group_index += 1
        // 下一个坑位的 index 超过当天数量，即跨天了
        if (allDayGroupsData[data_index].groups.length === group_index) {
          data_index += 1
          group_index = 0
        } else {
          // 没跨天需要再 +1，让 splice 插入到对应的位置后面
          group_index += 1
        }

        oldPositionData = allDayGroupsData[old_data_index].groups[old_group_index]
        allDayGroupsData[data_index].groups.splice(group_index, 0, oldPositionData)
        allDayGroupsData[old_data_index].groups.splice(old_group_index, 1)
      }

      this.$set(this.allData[1], 'data', allDayGroupsData)
      setTimeout(() => {
        setTimeout(() => {
          this.$nextTick(() => {
            this.scrollToView({
              className: `.group-instance[data-uuid="${oldPositionData.uuid}"]`
            })
          })
        })
      })
    },
    addDayGroupFunc({ group, position }) {
      const { data_index, group_index } = position

      const data = {
        ...group,
        widget_list: group.widget_list.map((widget) => {
          widget = this.formatWidgetSchema(widget)
          // 初始化数据时，属性项的值是直接双向绑定了，并没有像详情那又抽出独立的方法获得默认空值
          // 所以这里需要将变量的 values 手动置空
          if (Array.isArray(widget.attr_value_list)) {
            widget.attr_value_list.forEach((item) => {
              if (toString.call(item.variable_map) === '[object Object]') {
                Object.keys(item.variable_map).forEach((key) => {
                  item.variable_map[key].values = []
                })
              }
            })
          }

          return widget
        }),
        create: true,
        uuid: getUuid()
      }

      this.allData[1].data[data_index].groups.splice(group_index, 0, data)

      this.$nextTick(() => {
        this.$message.success('Add successful.')
        this.scrollToView({
          className: `.group-instance[data-uuid="${data.uuid}"]`
        })
      })
    },
    attrOptValueChangeFunc({ parentRefFieldTag, optionRefFieldTag }) {
      // 如果有 dining 的 group，需要西东勾选 菜单因季节变动 的选项
      // if (
      //   parentRefFieldTag === 'itinerary_dining_expense' &&
      //   ['itinerary_expense_included', 'itinerary_expense_partial_included'].includes(optionRefFieldTag)
      // ) {
      //   eventBus.$emit(`${INJECT_ATTR_OPT_VALUE_EVENT_KEY}_itinerary_footinfo_type_season_change`, {
      //     action: 'autoCheck'
      //   })
      // }
    },
    changeStructSelectValueFunc({ key, options, value, oldVal }) {
      if (key === HEAD_NUMS_REF_KEY) {
        if (value === undefined) {
          // 初始化的时候，需要前端帮忙赋值默认值 1 天
          setTimeout(() => {
            eventBus.$emit(`${INJECT_ATTR_OPT_VALUE_EVENT_KEY}_${HEAD_NUMS_REF_KEY}`, {
              action: 'assignValue',
              value: options.find((item) => item.value === '1').id
            })
          })
        } else {
          this.provideData.itineraryNums = +options.find((item) => item.id === value).value
        }
      }
    },
    async deleteDayGroupFunc({ position }) {
      const { data_index, group_index } = position

      this.allData[1].data[data_index].groups.splice(group_index, 1)
    },
    async handleClearInstance() {
      await ajax.postBody(ADMIN_API.act.clear_package_itinerary_instance_data, {
        data: {
          package_id: this.packageId,
          category_id: this.categoryId
        }
      })
      this.$message.success(this.$t('global_success'))
      this.refreshAllData({
        status: 0
      })
    },
    handlePreview() {
      getPreviewValiditySync(this.activityId, this.$i18n, (date) => {
        if (date) {
          let url = `${klook.getUrlByEnv('')}/en-US/preview/activity/${
            this.activityId
          }/?lang=${getEditLang()}&deadline_version=1&deadline=${encodeURIComponent(date)}`
          window.open(url)
        }
      })
    },
    async handleSave() {
      // 必须要有一个 sub-group
      const shouldBeFill = this.allData[1].data.reduce((acc, curr, dayIndex) => {
        const groups = curr.groups.filter(
          (group) => ![CHUNK_REF.departure, CHUNK_REF.return].includes(group.ref_field_tag)
        )
        if (groups.length) return acc

        return [...acc, dayIndex + 1]
      }, [])

      if (shouldBeFill.length) {
        this.$message.error(
          klook.parseStr1(this.$t('78881'), {
            num: shouldBeFill.join()
          })
        )
        return
      }

      const validate = await this.validateForm()
      if (validate) {
        const { error, data } = this.getPostData()

        if (error) {
          const { position, data, uuid, eventBusKey } = error
          const className = `.group-item[data-data_index="${position.data_index}"][data-group_index="${position.group_index}"]`
          const target = document.querySelector(className)

          if (target && uuid) {
            this.removeNonactivatedUUID(uuid)

            await this.$nextTick()
            eventBusKey && eventBus.$emit(eventBusKey, data)
            setTimeout(() => {
              this.scrollToView({
                target: this.$el.querySelector('.ant-form-explain') || target
              })
            }, 200)
          }
          // this.$message.error(message)

          return
        }

        // this.rootChangeLoading(true)
        const res = await ajax.postBody(
          ADMIN_API.act.save_package_instance_data,
          {
            noDefaultResponseInterceptor: true, // 获取error.message
            data: {
              groups: data,
              language: getEditLang(),
              activity_id: this.activityId
            }
          },
          {
            loading: true
          }
        )

        const extObj = {
          save_type: this.saveBeforeStatus2getters ? 1 : 0,
          err_message: res?.success ? '' : res?.error?.message || 'Error: false'
        }
        this.$root.trackIHEvent('.js-edit-save-btn', extObj)

        if (res) {
          this.$message.success(this.$t('global_success'))
          // this.rootChangeLoading(false)

          this.refreshAllData()
        }
      } else {
        const groupItemDom = this.getParentNode(document.querySelector('.itinerary-container .has-error'))
        this.removeNonactivatedUUID(groupItemDom?.dataset?.uuid)
        await this.$nextTick()
        this.scrollToView({
          target: groupItemDom
        })
      }
    },
    removeNonactivatedUUID(uuid) {
      if (uuid && this.nonactivatedGroupKeys.includes(uuid)) {
        this.$set(
          this,
          'nonactivatedGroupKeys',
          this.nonactivatedGroupKeys.filter((uuid) => uuid !== uuid)
        )
      }
    },
    getParentNode(node, className = 'group-item') {
      let MAX = 20

      while (MAX && node) {
        if (node.classList.contains(className)) {
          return node
        }

        MAX--
        node = node.parentNode
      }

      return null
    },
    scrollToView({ target = null, className = '.ant-form-explain', offsetY = -200 } = {}) {
      target = target || document.querySelector(className)
      if (!target) {
        return
      }

      const elementPosition = target.getBoundingClientRect().top
      const offsetPosition = window.pageYOffset + elementPosition + offsetY

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      })
    },
    getGroupSchemaById(groupId, templates = this.provideData.templates) {
      let group = templates[groupId]

      return {
        ...group,
        widget_list: group.widget_list || [],
        config: JSON.parse(group.config || '{}')
      }
    },
    formatWidgetSchema(widget) {
      widget = _.cloneDeep(widget)
      if (widget.widget_type === 1) {
        widget.attr_values = widget.attr_values || []
        // format the map of variable on attribute
        widget.attr_value_list = widget.attr_value_list.map((attr) => {
          const attrData = widget.attr_values.find((item) => item.id === attr.id)

          if (attrData) {
            Object.assign(attr, attrData, {
              value: attr.value
            })
          }

          // variable_map 对应属性项的配置详细信息
          if (hasOwnProperty.call(attr, 'variable_map') && attr.variable_map) {
            // is saved
            attr.variable_map = Object.entries(attr.variable_map).reduce((acc, curr) => {
              const [key, val] = curr

              return {
                ...acc,
                [key]: {
                  ...this.mapIdOnAllVariables[val.attr_item_id],
                  ...val
                }
              }
            }, {})
          } else if (attr.var_map) {
            attr.variable_map = Object.entries(attr.var_map).reduce((acc, curr) => {
              const [key, val] = curr

              return {
                ...acc,
                [key]: {
                  ...this.mapIdOnAllVariables[val],
                  ...(attr.variable_map || {}),
                  ...val,
                  key
                }
              }
            }, {})
          }

          return attr
        })
      } else {
        widget.attr_value_list = widget?.attr_values ?? []
      }

      return widget
    },
    parseRefData(ref) {
      if (Object.values(ref?.group_instance ?? {}).length) {
        const groupsSummary = Object.values(ref.group_instance).map((group) => {
          const tplGroupSchema = ref.group_schema[group.group_id]

          return {
            summary: computedGroupSummary(group.widgets, tplGroupSchema.widget_list, this.allVariables),
            ...tplGroupSchema
          }
        })

        this.$set(this, 'groupsSummary', groupsSummary)
      }
    },
    formatAllInstanceData(instances) {
      this.allData = instances.map((instance) => ({
        ...instance,
        // days
        data: (instance.data || []).map((data) => ({
          ...data,
          uuid: getUuid(),
          groups: (data.groups || []).map((group) => {
            const tplGroup = this.getGroupSchemaById(group.group_id)
            const widgetList = tplGroup.widget_list.reduce((acc, tplWidget) => {
              const widgetDataList = (group.widgets || []).filter((w) => w.id === tplWidget.id)
              const { ref_field_tag } = tplWidget
              const tags = ['itinerary_headinfo_time_select']
              if (tags.includes(ref_field_tag)) {
                this.hasTimeSelect = true
              }
              if (widgetDataList.length) {
                widgetDataList.forEach((widgetItemData) => {
                  const widgetSchema = this.formatWidgetSchema({
                    ...tplWidget,
                    ...(widgetItemData || {})
                  })

                  acc.push({
                    ...widgetSchema,
                    uuid: getUuid()
                  })
                })
              } else {
                acc.push({
                  ...this.formatWidgetSchema(tplWidget),
                  uuid: getUuid()
                })
              }

              return acc
            }, [])

            return {
              create: false,
              ...tplGroup,
              ...group,
              uuid: getUuid(),
              widget_list: widgetList.reduce((acc, curr) => {
                curr.config = JSON.parse(curr.config || '{}')
                // 需要将新增的同个 widget(title) 数据合并到一起
                // 对应的提交处理在 widget.vue:getData
                if (Array.isArray(curr.attr_value_list)) {
                  curr.attr_value_list = curr.attr_value_list.map((attrVal) => ({
                    ...attrVal,
                    instance_id: curr.instance_id
                  }))
                }

                if (curr.config.can_add) {
                  const preWidgetData = acc.find((item) => item.widget_type === curr.widget_type)

                  if (!preWidgetData) {
                    return [...acc, curr]
                  }

                  if (preWidgetData?.config?.can_add && curr.widget_type === preWidgetData?.widget_type) {
                    preWidgetData.attr_value_list.push(...curr.attr_value_list)

                    return acc
                  }
                }

                return [...acc, curr]
              }, [])
            }
          })
        }))
      }))
    },
    async initData() {
      this.rootChangeLoading(true)
      this.allVariables = await this.getActVariables2actions()
      const commonParams = {
        package_id: this.packageId,
        page_from: this.page_from,
        category_id: this.categoryId,
        display_language: lang_conf.getLangObj('F_LANG', 'B_LANG')[KLK_LANG]
      }

      const {
        instances,
        templates,
        ref = {},
        basic_data,
        itinerary_version,
        activity_departure_city = {},
        edit_map_permission,
        merchant_currency,
        stock_out_type,
        is_support_meetup_map,
        audio_permission
      } = await ajax.get(ADMIN_API.act.get_itinerary_by_package, {
        params: {
          ...commonParams,
          language: getEditLang()
        }
      })

      this.provideData.activityDepartureCity = activity_departure_city
      this.provideData.editMapPermission = edit_map_permission
      this.provideData.stockOutType = stock_out_type
      this.provideData.is_support_meetup_map = is_support_meetup_map
      this.basicData = basic_data
      this.provideData.itineraryVersion = itinerary_version
      this.provideData.merchantCurrency = merchant_currency
      this.provideData.templates = templates || {}
      this.provideData.audioPermission = audio_permission
      this.parseRefData(ref)
      this.formatAllInstanceData(instances)

      const refLang = getRefLang() || (getEditLang() === 'en_US' ? '' : 'en_US')
      if (refLang) {
        this.$store.dispatch('actionGetItineraryInstancesDataByPkgId', {
          ...commonParams,
          language: refLang
        })
      }

      this.rootChangeLoading(false)
      this.setActivateFooterWrapper(true)
      this.getPickUpData()
      await this.initCopyOptions()
      setTimeout(async () => {
        await this.$nextTick()
        this.cachePostData = this.getPostData()
      }, 100)
    },
    async getPickUpData() {
      const response = await ajax.get(ADMIN_API.act.get_package_group_field_value, {
        params: {
          package_id: this.packageId,
          field_key: groupRefFieldTagDict.pick_up_location_scope
        }
      })

      if (response?.value?.length) {
        this.provideData.extraPickInfoList = response.value
      } else {
        this.provideData.extraPickInfoList = []
      }
    },
    getGallery() {
      return this.imageGallery
    },
    async getImageGallery() {
      let language = [getEditLang(), 'en_US'].filter((lang) => lang)
      language = [...new Set(language)].join(',')
      const result = await ajax.getBody(ADMIN_API.act.get_image_list, {
        params: {
          activity_id: this.activityId,
          language: language,
          page_from: this.$route.query.page_from || 'admin'
        }
      })
      if (!result.success) {
        return
      }
      this.imageGallery = result?.result?.results ?? []
    },
    handleSwitchAllActive() {
      let nonactivatedGroupKeys =
        this.nonactivatedGroupKeys.length === this.allGroupUuidData.length ? [] : this.allGroupUuidData
      this.$set(this, 'nonactivatedGroupKeys', nonactivatedGroupKeys)
    }
  }
}
</script>

<style lang="scss">
.itinerary-container {
  padding-bottom: 48px;
  margin-bottom: 20px;

  .box {
    padding: 20px;
    margin-bottom: 10px;
    border-radius: 4px;
    background-color: #fff;

    &.copy {
      padding: 12px 20px;
    }
  }

  .loading {
    position: absolute;
    top: 50vh;
    width: 100%;
  }

  .group-summary-list {
    margin-top: 10px;

    .__title {
      position: relative;
      font-weight: 600;
      font-size: 24px;
      line-height: 29px;
      padding-left: 16px;

      &::before {
        content: '';
        position: absolute;
        z-index: 11;
        left: 0;
        top: 4px;
        height: 20px;
        border-left: 4px solid #0091ff;
      }
    }

    .__tips {
      margin: 8px 0 12px;
      color: #212121;
    }

    .__summary {
      padding: 16px;
      margin-bottom: 16px;
      background-color: #fafafa;
    }
  }

  .chunk-item {
    &.is-first-chunk,
    &.is-last-chunk {
      .group-item {
        margin-bottom: 0;
      }
    }

    &.is-first-chunk .group-item {
      display: flex;
      align-items: center;
      margin-bottom: 0;
    }

    &.is-last-chunk {
      margin-top: 10px;
    }
  }
}

.copy-radio-group {
  display: flex;
  flex-direction: column;

  .ant-radio-wrapper {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    white-space: break-spaces;

    .ant-radio {
      position: relative;
      top: 4px;
    }
  }
}
.audio-upload-block-container {
  margin-top: 10px;
}
</style>
