@mixin mixin-timeline-box {
  .timeline-box {
    position: relative;
    display: flex;
    align-items: flex-start;

    &.--last-day-last-group .timeline-grid {
      background-color: transparent;
    }

    &.--is-nonactivated {
      .timeline-content.is-timeline {
        height: 48px;
        min-height: inherit;
        padding: 20px 0;
        overflow: hidden;
        transition: background-color 0.6s, border-color 0.6s;
        cursor: pointer;

        .widget-item {
          display: none;
        }

        .timeline-actions {
          top: 12px;
        }
      }

      &:hover .timeline-content.is-timeline {
        background-color: rgba(240, 247, 255, 0.9);
        border: 1px solid #94bdff;
      }
    }

    .timeline-title {
      display: inline-flex;
      justify-content: space-between;
      width: 140px;
      flex-shrink: 0;
      margin-right: 23.5px;
      text-align: right;
      font-weight: 600;
      font-size: 16px;
      color: #303133;
      word-break: break-word;
    }

    .timeline-collapse-icon {
      position: relative;
      top: 4px;
      margin-right: 6px;
    }

    .timeline-grid {
      position: absolute;
      top: 0;
      left: 162px;
      width: 1px;
      height: 100%;
      display: inline-block;
      background-color: #d9d9d9;

      .__icon {
        position: absolute;
        top: 0;
        left: -12px;
        width: 24px;
        height: 24px;
        background-color: #fff;
      }
    }

    .timeline-content {
      flex: 1;
      min-height: 72px;
      border: 1px solid transparent;

      &.is-timeline {
        position: relative;
        padding: 32px;
        margin-bottom: 24px;
        margin-left: 27.5px;
        background-color: #fafafa;
        border-radius: 8px;
      }

      .timeline-actions {
        position: absolute;
        right: 32px;
        font-size: 16px;

        .action-item {
          margin-left: 20px;
          cursor: pointer;
          position: relative;
          z-index: 1;

          &.delete {
            transition: color 0.6s;
            color: #444;

            &:hover {
              filter: invert(18%) sepia(90%) saturate(3216%) hue-rotate(336deg)
                brightness(95%) contrast(125%);
            }
          }

          &.direction {
            &:hover {
              color: #437dff;
            }
          }
        }
      }
    }
  }
}

@mixin mixin-poi-option-item {
  ::v-deep {
    .poi-value-description {
      margin-left: 8px;
    }

    // 国家和城市 description 隐藏
    .ant-select-selection-selected-value span.overflow-text:nth-child(2) {
      display: none;
    }

    .poi_option_item {
      display: flex;
      gap: 8px;

      span.overflow-text:nth-child(1) {
        display: inline-flex;
        flex-shrink: 0;
        max-width: 80%;
        width: fit-content;
      }

      &__description {
        font-size: 11px;
        color: #a6a6a6;
      }
    }
  }
}
