import { pmsConfirm, getUuid } from '@activity/utils'

export default {
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    config: {
      type: Object,
      default: () => {}
    },
    isPublishWithAi: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showPhoto: false,
      draggableList: []
    }
  },
  computed: {
    canAdd() {
      return this.config.can_add
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler(val) {
        this.initForm(val)
      }
    }
  },
  methods: {
    formChange: _.debounce(function(data) {
      const { index, value } = data
      const list = this.data.attr_value_list
      if (list.length) {
        this.$set(this.data.attr_value_list[index], 'value', value)
      } else {
        this.data.attr_value_list.push({
          instance_id: 0,
          value,
          uuid: getUuid()
        })
      }
    }, 30),
    handleSort({ from, to }) {
      const list = _.cloneDeep(this.data.attr_value_list)
      const toData = list[to]
      const fromData = list[from]
      this.$set(this.data.attr_value_list, from, toData)
      this.$set(this.data.attr_value_list, to, fromData)
    },
    initForm(val) {
      const data = val ? _.cloneDeep(val) : {}
      const attr_value_list = data.attr_value_list || []
      const newData = []
      attr_value_list.forEach((item) => {
        const instance_id = item.instance_id
        const uuid = item.uuid
        newData.push({ ...data, attr_value_list: [item], uuid: instance_id || uuid, instance_id })
      })
      if (newData.length) {
        this.draggableList = newData
        if (!this.cacheList) {
          this.cacheList = _.cloneDeep(newData)
        }
        return
      }
      const it = {
        instance_id: 0,
        value: '',
        uuid: getUuid()
      }
      this.data.attr_value_list.push(it)
    },
    async validateForm() {
      const allForm = this.$refs.form
      const promiseList = []
      allForm.forEach((item) => {
        promiseList.push(item.validateForm())
      })
      const validate = await Promise.all(promiseList)
      return validate.every((item) => item)
    },
    isCreate(id, value = '') {
      const data = (this.cacheList || []).find((item) => id === item.instance_id)
      if (data) {
        const cacheValue = JSON.parse(data?.attr_value_list[0]?.value || '{}')
        const currentValue = JSON.parse(value || '{}')
        return !(cacheValue.title_multilang === currentValue.title_multilang)
      }
      return false
    },
    getData() {
      const dataList = []
      const { widget_type } = this.data
      const defaultData = {
        is_free_text: true,
        widget_type
      }
      const draggableList = this.draggableList
      draggableList.forEach((item) => {
        const value = item?.attr_value_list[0]?.value ?? ''
        const dataId = item?.attr_value_list[0]?.id ?? 0
        const instance_id = item.instance_id
        // const isCreate = this.isCreate(instance_id, value)
        dataList.push({
          ...defaultData,
          create: !dataId,
          // create: !dataId || isCreate,
          id: dataId,
          instance_id,
          value
        })
      })
      return dataList
    },
    addItem() {
      const data = {
        instance_id: 0,
        uuid: getUuid(),
        value: ''
      }
      this.data.attr_value_list.push(data)
    },
    async deleteItem(data) {
      const { isEmpty, index } = data
      let confirm = true
      if (!isEmpty) {
        confirm = await pmsConfirm.call(this, {
          title: this.$t('78878'),
          content: ''
        })
      }
      if (confirm) {
        this.data.attr_value_list.splice(index, 1)
      }
    },
    togglePhoto() {
      this.showPhoto = !this.showPhoto
    }
  }
}
