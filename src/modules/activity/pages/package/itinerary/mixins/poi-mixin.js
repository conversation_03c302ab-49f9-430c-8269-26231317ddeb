import { mapState, mapGetters, mapMutations } from 'vuex'
import { getEditLang, getUuid } from '@activity/utils'
import { SELECT_GROUP_MAP_DICT, getPOIDescription } from '@activity/pages/package/itinerary/utils/index.js'
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'

export default {
  components: {
    OverflowTextTooltip
  },
  inject: {
    provideData: {
      default: {
        vDisabled: {
          lock: false
        },
        disabled: {
          all: false,
          freeText: false
        }
      }
    }
  },
  props: {
    // 表单初始数据，用于初始化form
    data: {
      type: Object,
      default: () => {}
    },
    config: {
      type: Object,
      default: () => {}
    },
    currentIndex: {
      type: Number,
      default: 0
    },
    isLast: {
      type: Boolean,
      default: false
    },
    isFirst: {
      type: Boolean,
      default: false
    },
    showOpreate: {
      type: Boolean,
      default: false
    },
    isPublishWithAi: {
      type: <PERSON>olean,
      default: false
    },
    subWidgetData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    this.selectGroupMap = Object.values(SELECT_GROUP_MAP_DICT)

    return {
      activityPoiOptions: [],
      poiOptions: [],
      query: '',
      isAdd: false,
      customName: ''
    }
  },
  computed: {
    ...mapState({
      packageItineraryPOIList: 'packageItineraryPOIList',
      packageItineraryMapList: 'packageItineraryMapList'
    }),
    ...mapGetters(['itineraryInstancesDataByIdGetters']),
    actId() {
      return +this.$route.params.id
    },
    groupOptionList() {
      // 自定义和非分组 poi 接口都不存在 group 分组情况
      const { valid, data } = this.poiOptions.reduce(
        (acc, curr) => {
          if (!curr.group) {
            acc.valid = false
          }

          if (!acc.valid) return {}

          if (!Object.prototype.hasOwnProperty.call(acc.data, curr.group)) {
            acc.data[curr.group] = []
          }

          acc.data[curr.group].push(curr)

          return acc
        },
        {
          valid: true,
          data: {}
        }
      )

      return valid ? data : {}
    },

    multiGroup() {
      return Object.keys(this.groupOptionList).length > 1
    },

    showMapButton() {
      const { form, currentPOI } = this
      return Boolean(form.place_id && currentPOI && currentPOI.place_id && currentPOI.location)
    },

    showMapComponent() {
      const { currentPOI, isAdd } = this

      if (isAdd) {
        return false
      }

      if (currentPOI.location) {
        return true
      }

      return false
    },
    refValue() {
      // 兼容 sub-group 数据
      if (this.currRefValue) {
        return this.currRefValue
      }

      const value = this.refItineraryInstancesData?.attr_values?.[0]?.value

      if (value) {
        try {
          const data = JSON.parse(value)

          return data?.title_multilang
        } catch (e) {
          return ''
        }
      }

      return ''
    },
    isMap() {
      const refs = ['itinerary_return_map', 'itinerary_departure_map']
      const ref_field_tag = this.data?.ref_field_tag ?? ''
      return refs.includes(ref_field_tag)
    },
    currentList() {
      return this.isMap ? this.packageItineraryMapList : this.packageItineraryPOIList
    },
    currentPOI() {
      const poi = this.getPOIById(this.form.place_id)
      return poi || {}
    },
    currPOIDescription() {
      return this.getPOIDescription(this.currentPOI)
    },
    disabledEdit() {
      // const { isAdd, currentPOI = {} } = this
      // return !isAdd && currentPOI.source === 1
      return !this.isAdd
    },
    isCustomPOI() {
      const source = this.currentPOI?.source ?? -1
      return source === 0
    },
    showTips() {
      return this.isCustomPOI && !this.isAdd
    },
    refItineraryInstancesData() {
      const instanceId = this.data.attr_value_list?.[0]?.instance_id

      return instanceId ? this.itineraryInstancesDataByIdGetters(instanceId) : null
    },
    dropdownMenuStyle() {
      const { poiOptions, query } = this
      const showMenu = poiOptions.length || query
      return showMenu ? {} : { display: 'none' }
    },
    dropdownStyle() {
      const { poiOptions, query } = this
      const showMenu = poiOptions.length || query
      return showMenu
        ? { width: '520px', zIndex: 99 }
        : { width: '520px', border: 'none', boxShadow: 'none', zIndex: 99 }
    },
    mapSearchable() {
      // const { isAdd, currentPOI = {} } = this
      // return isAdd || currentPOI.source === 0
      const { isAdd } = this

      return isAdd
    },
    // 指有标题的POI，用于下拉选项
    validatePOI() {
      const list = this.currentList.reduce((acc, curr) => {
        // 过滤掉无标题的poi
        const { title = '', value } = curr
        // 去重
        const data = acc.find((item) => item.value === value)
        if (!title.trim() || !value || data) {
          return [...acc]
        }
        return [curr, ...acc]
      }, [])
      return _.cloneDeep(list)
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler(val) {
        const { attr_value_list = [] } = val || {}
        const value = attr_value_list[0]?.value ?? ''
        if (value) {
          this.initForm(value)
        }
      }
    },
    form: {
      deep: true,
      immediate: true,
      handler(val) {
        if (this.stopWatch) {
          this.stopWatch = false
          return
        }
        this.formChange(val)
      }
    },
    validatePOI: {
      deep: true,
      immediate: true,
      handler(val) {
        clearTimeout(this.watchTimer)
        this.watchTimer = setTimeout(() => {
          const poiOptions = _.cloneDeep([...val, ...this.activityPoiOptions])
          this.poiOptions = this.uniqByOption(poiOptions)
        }, 100)
      }
    },
    currentPOI: {
      deep: true,
      handler(val, oVal) {
        if (!this.cachePoi) {
          this.cachePoi = oVal
        }
      }
    }
  },
  mounted() {
    this.initActPoiOptions()
  },
  methods: {
    getPOIDescription,
    async initActPoiOptions() {
      this.activityPoiOptions = (await this.getPOIData('', true)) || []
      const poiOptions = _.cloneDeep([...this.validatePOI, ...this.activityPoiOptions])
      this.poiOptions = this.uniqByOption(poiOptions)
    },
    uniqByOption(options) {
      return _.uniqBy(options, 'value').filter(
        (item) =>
          !item.location || // add freetext 时 location 是空
          !options.some((opt) => opt.value !== item.value && opt.location === item.location)
      )
    },
    async checkFunction() {
      const form = this.$refs.customNameForm
      const validate = await form.checkFunction().catch(() => false)
      return validate
    },
    cancel() {
      this.$refs.customNameForm.resetFields()
      this.cancelMap()
    },
    async confirm(val) {
      // location, place_id, poi_id = 0, name
      const { currentPOI, customName } = this
      const { location, place_id } = currentPOI
      let defalutData = {
        place_id: getUuid(),
        is_empty: true
      }
      // 如果currentPOI 存在
      if (location || place_id) {
        const [lat = '', lng = ''] = location.split(',')
        defalutData = {
          place_id,
          location: { lat, lng }
        }
      }
      const map = val?.map ?? { ...defalutData, name: customName }

      this.mapDrawerConfirm({ map }, this.customName, true)
      this.isAdd = false
      this.formChange()
    },
    setStateList(list = []) {
      this.isMap ? this.setPackageItineraryMapList(list) : this.setPackageItineraryPOIList(list)
    },
    //
    ...mapMutations(['setPackageItineraryPOIList', 'setPackageItineraryMapList']),
    getDefaultPlaceId(place_id, title = '') {
      if (!title.trim()) {
        return undefined
      }

      // 自定义 poi place id 是空字符串
      place_id = place_id || 0
      const data = this.currentList.find((it) => {
        if (!place_id) {
          return !it.place_id && title === it.title
        }
        return it.place_id === place_id && title === it.title
      })
      if (data) {
        return data.value
      }
      return getUuid()
    },
    selectChange(v) {
      // const poi = this.getPOIById(v)
      const poi = this.poiOptions.find((item) => item.value === v)
      if (!poi) {
        return
      }
      // 如果选中项已存在，则直接更新place_id
      const hasData = this.currentList.find((item) => {
        return poi.place_id === item.place_id && item.title === poi.title
      })
      if (hasData) {
        this.form.place_id = hasData.value
        return
      }
      const newList = this.initPOI([poi])
      const comboList = [...newList, ...this.currentList]
      this.setStateList(_.cloneDeep(comboList))
    },
    formChange: _.debounce(function (val) {
      const value = this.getFormData()
      const list = this.data.attr_value_list
      if (list.length) {
        this.$set(this.data.attr_value_list[0], 'value', value)
      } else {
        this.data.attr_value_list.push({
          value
        })
      }
      this.$emit('formChange', {
        index: this.currentIndex,
        value
      })
    }, 30),
    // 表单移动
    handleSort(direction) {
      const from = this.currentIndex
      const to = from + direction
      this.$emit('sort', { from, to })
    },
    initData(val) {
      return JSON.parse(val)
    },
    checkNewPOI(title) {
      return this.currentList.find((item) => {
        return !item.place_id && title === item.title
      })
    },
    checkData(data) {
      const { place_id = '', location = '', title_multilang = '' } = data
      return !place_id && !location && !title_multilang
    },
    getPOIFromValue(data, value) {
      if (this.checkData(data)) {
        return null
      }
      const {
        place_id = 0,
        title_multilang = '',
        city_name = '',
        country_name = '',
        address_desc = '',
        poi_id = 0,
        source = 1
      } = data
      let { location = '' } = data

      // 自定义没有place_id会导致下拉项出现重复的数据，在这里过滤
      // 如果没有place_id、title相同则认为是同一个数据
      let hasData = null
      if (!place_id) {
        hasData = this.checkNewPOI(title_multilang)
      }
      // const val = hasData?.value || place_id || value || getUuid()
      const val = hasData?.value || value || getUuid()

      if (data.location_original) {
        location = data.location_original
      }

      const poi = {
        poi_id,
        location,
        city_name,
        country_name,
        address_desc,
        place_id,
        title: title_multilang,
        value: val,
        label: title_multilang,
        source
      }
      if (!this.form.place_id) {
        this.form.place_id = val
      }
      const newList = this.initPOI([poi])
      this.setStateList([...newList, ...this.currentList])
      return poi
    },
    isCreate() {
      const { currentPOI, cachePoi, data } = this
      const ref_field_tag = data?.ref_field_tag ?? ''
      const refFields = ['itinerary_dining_poi']
      if (cachePoi && currentPOI && refFields.includes(ref_field_tag)) {
        return !(currentPOI.title === cachePoi.title)
      }
      return false
    },
    getData() {
      const value = this.getFormData()
      const { widget_type, attr_value_list } = this.data
      const dataId = attr_value_list?.[0]?.id ?? 0
      // const isCreate = this.isCreate()
      return [
        {
          is_free_text: true,
          value,
          create: !dataId,
          // create: !dataId || isCreate,
          id: dataId,
          widget_type
        }
      ]
    },
    mapPhotoConfirm(data) {
      const images = this.form.images
      this.form.images = [...images, ...data]
    },
    cancelMap() {
      this.isAdd = false
    },
    addNew() {
      const POIButton = this.$refs.POIButton
      POIButton.toggleMap()
      this.isAdd = true
      this.form.place_id = undefined
    },
    mapDrawerConfirm(val, customName = '', needCheck = false, stopWatch = true) {
      // const isAdd = typeof this.isAdd === 'boolean' && !this.isAdd
      const disabledEdit = this.disabledEdit

      if (needCheck && disabledEdit) {
        return
      }
      // || !val.map.place_id
      // 自定义poi可以没有place_id
      if (!val || !val.map) {
        return
      }
      const packageItineraryPOIList = this.currentList
      const map = val.map
      const { location, place_id, poi_id = 0, name, is_empty, formatted_address: address_desc = '' } = map
      const poi = packageItineraryPOIList.find((item) => {
        if (is_empty) {
          return name.trim() === item.title
        }
        return place_id === item.place_id && name.trim() === item.title
      })
      const title = (customName || name).trim()
      if (!title) {
        return
      }
      this.stopWatch = stopWatch
      if (poi) {
        poi.title = title
        poi.label = title
        poi.address_desc = address_desc
        this.form.place_id = poi.value
        this.setStateList([...this.currentList])

        // derparture return 自动填充名称
        if (!this.customName.trim()) {
          this.customName = title
        }
        this.validateForm()
        return
      }
      const locationString = location ? `${location.lat},${location.lng}` : ''
      const value = place_id
      const data = {
        poi_id,
        location: locationString,
        title,
        place_id: is_empty ? 0 : place_id,
        value,
        label: title,
        source: 0,
        address_desc
      }
      this.isAdd = false
      this.form.place_id = value
      const newList = this.initPOI([data])
      this.poiOptions = [...newList, ...this.currentList]
      this.setStateList([...newList, ...this.currentList])

      // derparture return 自动填充名称
      if (!this.customName.trim()) {
        this.customName = name
      }
      this.validateForm()
    },
    photoChange(val) {
      this.form.images = val
    },
    initMap(place_id, needSource = false) {
      const poi = this.getPOIById(place_id)
      const {
        location = '',
        title = '',
        city_name = '',
        country_name = '',
        address_desc = '',
        poi_id = 0,
        source = 1,
        place_id: placeId
      } = poi
      const title_multilang = this.customName ? this.customName : title
      const sourceObj = needSource ? { source } : {}
      return {
        location,
        city_name,
        country_name,
        address_desc,
        title_multilang,
        poi_id,
        place_id: placeId || '',
        ...sourceObj
      }
    },
    getPOIById(place_id) {
      const poi = this.currentList.find((item) => item.value === place_id)
      return poi || {}
    },
    async validateForm() {
      const validate = await this.$refs.form.validate().catch(() => false)
      const mapValid = this.$refs.map ? await this.$refs.map.validateForm() : true

      return validate && mapValid
    },
    filterOption() {
      return true
    },
    initPOI(list = []) {
      const packageItineraryPOIList = this.currentList
      // 去重
      const newList = []
      list.forEach((item) => {
        const data = packageItineraryPOIList.find((it) => {
          return it.place_id === item.place_id && item.title === it.title
          // return it.value === item.value
        })
        if (!data) {
          newList.push({
            ...item,
            value: item.value,
            label: item.title
          })
        }
      })
      return newList
    },
    poiSearch: _.debounce(async function (v) {
      const query = v.trim()
      if (!query) {
        this.poiOptions = _.cloneDeep(this.validatePOI)
        this.query = query
        return
      }
      const list = await this.getPOIData(query)
      this.poiOptions = list
    }, 350),
    async getPOIData(query = '', force = false) {
      if (!(query || force)) {
        return []
      }
      // query = 'Market in Barcelona'
      const result = await ajax.getBody(ADMIN_API.act.get_poi_list, {
        params: {
          query,
          activity_id: this.actId,
          language: getEditLang()
        }
      })
      if (!result || !result.success) {
        return []
      }

      const list = (result?.result?.results ?? []).reduce((acc, item) => {
        const { title } = item
        if (!title) {
          return acc
        }

        if (item.location_original) {
          item.location = item.location_original
          delete item.location_original
        }

        const data = {
          ...item,
          value: getUuid(),
          label: title,
          source: 1
        }
        return [...acc, data]
      }, [])
      this.query = query
      return list
    }
  }
}
