// widget_type: 14 | 15 公共 mixin
// 区别在于 this.widgetSchemaDict 不同
import OrderedList from '@activity/pages/package/itinerary/components/ordered-list/index.vue'
import { scheduleTypeKeyDict } from '@activity/pages/package/itinerary/utils/index.js'
import { parseJsonString } from '@activity/pages/package/itinerary/utils/index.js'
import { getUuid } from '@activity/utils'
import { mapGetters } from 'vuex'

export default {
  components: {
    OrderedList
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    scheduleType: {
      type: String,
      default: ''
    }
  },
  data() {
    this.cacheList = null
    this.scheduleTypeKeyDict = scheduleTypeKeyDict
    this.itemFieldDict = {
      map: 'map',
      time: 'time',
      time_select: 'time_select',
      detail_multilang: 'detail_multilang'
    }

    return {
      list: []
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler(val) {
        this.initForm(val)
      }
    }
  },
  computed: {
    ...mapGetters(['itineraryInstancesDataByIdGetters']),
    attrValueList() {
      return this.data.attr_value_list || []
    },
    currRefItineraryInstancesData() {
      const instanceId = this.data.instance_id

      return instanceId ? this.itineraryInstancesDataByIdGetters(instanceId) : null
    }
  },
  methods: {
    async validateForm() {
      const result = await Promise.all(
        this.$refs.orderedList.$children.map((item) => {
          return item?.validateForm?.() ?? true
        })
      )
      return result.every((flag) => !!flag)
    },
    getData() {
      let data = []
      const { ref_field_tag, widget_type } = this.data
      const children = this.$refs.orderedList?.$children ?? []
      children.forEach((widget) => {
        const field = widget.itemField || widget.$attrs['item-field']

        if (field) {
          const order = widget.itemOrder !== undefined ? widget.itemOrder : widget.$attrs['item-order']
          const subWidgetData = widget.subWidgetData || widget.$attrs['sub-widget-data']
          const { uuid, instance_id = 0, id = 0 } = subWidgetData

          if (!data[order]) {
            data[order] = {
              id,
              create: instance_id === 0,
              ref_field_tag,
              widget_type,
              instance_id,
              uuid,
              is_free_text: true,
              value: {}
            }
          }

          const value = widget.getData()?.[0]?.value ?? ''
          // 分字段赋值 value
          data[order].value[field] =
            [this.itemFieldDict.detail_multilang].includes(field) || !value ? value : parseJsonString(value)
        }
      })

      return data.map((item) => {
        item.value = JSON.stringify(item.value)

        return item
      })
    },
    initForm(val) {
      const { attr_value_list = [], ...other } = _.cloneDeep(val || {})
      const list = attr_value_list.map((item) => {
        const { id, instance_id, value } = item
        const data = this.getSubGroupItemData(parseJsonString(value))

        return {
          id,
          uuid: String(instance_id || other.uuid),
          instance_id: instance_id || 0,
          ...data
        }
      })

      if (list.length) {
        this.list = list
        if (!this.cacheList) {
          this.cacheList = _.cloneDeep(list)
        }
        return
      }

      !this.list.length && this.addNew()
    },
    addNew() {
      this.list.push({
        id: 0,
        uuid: getUuid(),
        instance_id: 0,
        ...this.getSubGroupItemData()
      })
    },
    getSubGroupItemData(data = {}) {
      const { time, time_select, map, detail_multilang } = _.cloneDeep(data)
      const common = {
        create: false,
        is_free_text: true
      }

      return {
        time: {
          value: time ? JSON.stringify(time) : ''
        },
        time_select: {
          ...this.widgetSchemaDict.time_select,
          widget_type: 7,
          required: 1,
          attr_value_list: time_select
            ? [
                {
                  ...common,
                  widget_type: 7,
                  value: JSON.stringify(time_select)
                }
              ]
            : []
        },
        map: {
          ...this.widgetSchemaDict.map,
          required: 1,
          attr_value_list: map
            ? [
                {
                  ...common,
                  widget_type: this.widgetSchemaDict.map.widget_type,
                  value: JSON.stringify(map)
                }
              ]
            : []
        },
        detail_multilang: {
          ...this.widgetSchemaDict.detail_multilang,
          widget_type: 2,
          required: 1,
          attr_value_list: detail_multilang
            ? [
                {
                  ...common,
                  widget_type: 2,
                  value: detail_multilang
                }
              ]
            : []
        }
      }
    }
  }
}
