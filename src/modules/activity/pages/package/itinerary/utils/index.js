import Vue from 'vue'

export const SELECT_GROUP_MAP_DICT = {
  most_recommended: { label: __('102291'), key: 'most_recommended' },
  others: { label: __('ob_others'), key: 'others' }
}

export const refFieldTagDict = {
  title: {
    headerInfoTimeSelect: 'itinerary_headinfo_time_select'
  },
  attrVal: {
    headerTimeSoldByDate: 'itinerary_header_time_sold_by_date',
    headerTimeSoldByTime: 'itinerary_header_time_sold_by_time'
  }
}

export const scheduleTypeKeyDict = {
  date: refFieldTagDict.attrVal.headerTimeSoldByDate,
  time: refFieldTagDict.attrVal.headerTimeSoldByTime
}

export const itineraryVersionDict = {
  v1: 'itinerary_v1', // 没有 sub group 概念, departure & return 可以多个
  v2: 'itinerary_v2', // 可多个 sub group, departure & return 仅有一个
  v3: 'itinerary_v3' // 新增 area_data 字段保存 departure pick-up & meet-up 数据
}

// 用于 Group / widget / attr value 间的联动
// event name: changeStructValue
export const eventBus = new Vue()

// 拼接 _${ref_field_tag}
export const INJECT_ATTR_OPT_VALUE_EVENT_KEY = 'injectAttrOptionValue'

export const ATTR_OPT_VALUE_CHANGE_KEY = 'attrOptionValueChange'

export const FORCE_THROW_TI_DU_ERROR = 'forceThrowTimeDurationError'

export const INSTANCE_TYPE_DICT = {
  default: 'default',
  timeline: 'timeline'
}

export const HEAD_NUMS_REF_KEY = '{{Itinerary_Head_Nums}}'

export const STAY_REF_KEY = 'itinerary_accommodation'
export const DINING_REF_KEY = 'itinerary_dining'

export const EXTRA_TYPE = 'PKG_ITINERARY'

export const ABBR_NAME_DICT = {
  departure: 'itinerary_departure',
  attraction: 'itinerary_attraction',
  dining: 'itinerary_dining',
  transport: 'itinerary_transport',
  accommodation: 'itinerary_accommodation',
  return: 'itinerary_return'
}

// ref tag -> icon name
export const ICON_NAME_DICT = Object.entries(ABBR_NAME_DICT).reduce(
  (acc, curr) => ({
    ...acc,
    [curr[1]]: curr[0]
  }),
  {}
)

export const TEMPLATES_REF = {
  head: 'itinerary_headinfo',
  foot: 'itinerary_footinfo'
}

export const CHUNK_REF = {
  departure: 'itinerary_departure',
  common: 'itinerary_common',
  return: 'itinerary_return'
}

import { getUuid } from '@activity/utils'

export const parseAttrStrToRender = (str) => {
  const variableList = str.match(/{{([^}])+}}/g)

  if (!variableList) {
    return {
      isStructure: false,
      data: str
    }
  }

  const data = variableList.reduce((acc, curr, index) => {
    const idx = str.indexOf(curr)
    const preStr = str.substring(0, idx)
    const remainingStr = str.substring(preStr.length + curr.length)

    if (preStr) {
      acc.push({
        type: 'string',
        string: preStr
      })
    }

    acc.push({
      type: 'variable',
      string: curr,
      uuid: getUuid()
    })

    if (index === variableList.length - 1) {
      acc.push({
        type: 'string',
        string: remainingStr
      })
    } else {
      str = remainingStr
    }

    return acc
  }, [])

  return {
    isStructure: true,
    data: data.filter((item) => item.string)
  }
}

export const admissionOptions = [
  {
    label: __('78893'),
    value: 1
  },
  {
    label: __('78894'),
    value: 2
  },
  {
    label: __('78895'),
    value: 3
  }
]

export const hotelType = [
  {
    label: __('78914'),
    value: 1
  },
  {
    label: __('78915'),
    value: 2
  },
  {
    label: __('78916'),
    value: 3
  },
  {
    label: __('78917'),
    value: 4
  },
  {
    label: __('78918'),
    value: 5
  },
  {
    label: __('78919'),
    value: 6
  }
]

export const starOptions = [
  {
    label: __('78920'),
    value: 2
  },
  {
    label: __('78921'),
    value: 3
  },
  {
    label: __('78922'),
    value: 4
  },
  {
    label: __('78923'),
    value: 5
  }
]

export const bedOptions = [
  {
    label: __('78924'),
    value: 1
  },
  {
    label: __('78925'),
    value: 2
  },
  {
    label: __('78926'),
    value: 3
  },
  {
    label: __('78927'),
    value: 4
  }
]

export const bathroomOptions = [
  {
    label: __('78929'),
    value: 1
  },
  {
    label: __('78928'),
    value: 2
  }
]

export const personOptions = [
  {
    label: '1',
    value: 1
  },
  {
    label: '2',
    value: 2
  },
  {
    label: '3',
    value: 3
  },
  {
    label: '4',
    value: 4
  },
  {
    label: '5',
    value: 5
  },
  {
    label: '6',
    value: 6
  },
  {
    label: '7',
    value: 7
  },
  {
    label: '8',
    value: 8
  },
  {
    label: '9',
    value: 9
  },
  {
    label: '10',
    value: 10
  }
]

export const getSecond = (v) => {
  let { hours, minutes } = v

  if (typeof hours === 'number' && typeof minutes === 'number') {
    return (hours || 0) * 60 * 60 + (minutes || 0) * 60
  }

  return -1
}

export const parseSecond = (time) => {
  if (time >= 0) {
    let hours = Math.floor(time / 3600)
    let minutes = Math.floor((time - hours * 3600) / 60)

    return {
      hours: hours,
      minutes: minutes
    }
  }
  return {
    hours: '',
    minutes: ''
  }
}

export function parseJsonString(str, defaultValue = {}) {
  try {
    return JSON.parse(str)
  } catch (e) {
    return defaultValue
  }
}

export const getPOIDescription = (data) => {
  const isKlookPlace = data?.poi_id > 0

  let description = ''
  const { country_name, city_name, address_desc } = data
  if (isKlookPlace) {
    const address = [city_name, country_name].filter((item) => item)
    if (address.length) {
      description = address.join(', ')
    }
  } else if (address_desc) {
    description = address_desc
  }

  return description
}

export const ItineraryMapExtraTypeKey = 'itinerary_area'

export const audioPermissionDict = {
  INVISIBLE: 0,
  READ_ONLY: 2,
  EDITABLE: 1
}
