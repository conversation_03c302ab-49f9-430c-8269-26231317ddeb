import {
  PASS_PRODUCT_SUBCATEGORY_IDS,
  RESERVATION_PRODUCT_SUBCATEGORY_IDS
} from '@activity/pages/package/package_const.js'
import { mapState } from 'vuex'

export const reservationProductsMixin = {
  props: {
    pkgSectionData: {
      type: Object,
      default: null
    }
  },
  computed: {
    ...mapState({
      categoryInfo: (state) => state.categoryInfo
    }),
    isPassProducts() {
      const { sub_category_id } = this.categoryInfo || this.pkgSectionData || {}

      return PASS_PRODUCT_SUBCATEGORY_IDS.includes(sub_category_id)
    },
    // 预约商品需要置灰
    isReservationProducts() {
      const { sub_category_id } = this.categoryInfo || this.pkgSectionData || {}

      return RESERVATION_PRODUCT_SUBCATEGORY_IDS.includes(sub_category_id)
    },
    reservationCountOpts() {
      return Array(100)
        .fill()
        .map((item, index) => ({
          label: index + 1,
          value: index + 1
        }))
    }
  }
}
