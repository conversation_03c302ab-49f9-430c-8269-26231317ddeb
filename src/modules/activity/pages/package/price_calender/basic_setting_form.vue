<template>
  <div>
    <div class="note-box row1">
      <div>
        <span class="text"
          >{{ init || batch_edit || time_slot ? $t('override_take_rate') : $t('target_take_rate') }}
          :
        </span>
        <span class="rate">{{ refer_tr }}%</span>
      </div>
      <div v-if="init || batch_edit || time_slot">
        <span class="text">{{ $t('last_take_rate') }} : </span>
        <span class="rate">{{ ltr || timeslotLtr || actLtr }}%</span>
        <a-button
          v-if="batch_edit"
          size="small"
          type="primary"
          @click="refreshTR"
          style="margin-left: 15px;"
          :loading="refresh_loading"
        >
          Refresh Take Rate
        </a-button>
      </div>
    </div>
    <a-form-model
      layout="horizontal"
      class="common-form-horizontal-style row2"
      :model="form"
      :rules="rules"
      ref="settingForm"
      v-bind="formItemLayout"
    >
      <a-form-model-item v-if="!is_hidden" :label="$t('pm_cost')" prop="cost_amount">
        <a-input
          type="number"
          v-model="form.cost_amount"
          @change="changeForm"
          @mousewheel.native.prevent
          :disabled="disable_cost_price || !canEdit"
          @keyup.native="proving('cost_amount')"
          @blur="setCostAndRate"
          :placeholder="$t('global_please_input')"
          :suffix="`${cost_currency || $attrs.cost_currency}`"
        ></a-input>
      </a-form-model-item>
      <a-form-model-item v-if="!is_hidden" :label="$t('pm_market_price')" prop="market_amount">
        <a-input
          type="number"
          v-model="form.market_amount"
          @change="changeForm"
          @mousewheel.native.prevent
          :disabled="disable_cost_price || !canEdit"
          @keyup.native="proving('market_amount')"
          :placeholder="$t('global_please_input')"
          :suffix="`${base_currency || $attrs.base_currency}`"
        ></a-input>
      </a-form-model-item>
      <a-form-model-item :label="$t('pm_selling_price')" prop="base_amount" v-if="!hidden_sell_price">
        <a-input
          type="number"
          v-model="form.base_amount"
          @change="changeForm"
          @mousewheel.native.prevent
          :disabled="disable_sell_price || !canEdit"
          @keyup.native="proving('base_amount')"
          @blur="getTakeRate"
          :placeholder="$t('global_please_input')"
          :suffix="`${base_currency || $attrs.base_currency}`"
        ></a-input>
      </a-form-model-item>
      <a-form-model-item
        v-if="!hidden_sell_price && this.$route.name !== 'price_audit_detail'"
        :label="$t('new_take_rate')"
        prop="take_rate"
      >
        <a-input
          v-model="form.take_rate"
          @blur="setTakeRate"
          :disabled="disable_sell_price || !canEdit"
          @keyup.native="proving('take_rate')"
          :placeholder="$t('global_please_input')"
          :suffix="'%'"
        >
        </a-input>
      </a-form-model-item>
      <a-form-model-item
        :label="$t('pm_special_selling_price')"
        prop="custom_price"
        v-if="!hidden_sell_price"
        class="curr-amount-wrap"
      >
        <div v-for="(price, index) in form.custom_price" :key="price.index" class="curr-amount">
          <a-select
            class="short-input"
            v-model="price.currency"
            :placeholder="$t('msp_currency_plh')"
            filterable
            showSearch
            @change="changeForm"
            :disabled="disable_sell_price || !canEdit"
          >
            <a-select-option
              v-for="curr in currencyOptions"
              :label="curr.label"
              :value="curr.value"
              :key="curr.value"
              :disabled="currencies.includes(curr.value)"
              >{{ curr.label }}</a-select-option
            >
          </a-select>
          <a-input
            class="short-input"
            type="number"
            v-model="price.amount"
            :placeholder="$t('msp_amount_plh')"
            @change="changeForm"
            @mousewheel.native.prevent
            :disabled="disable_sell_price || !canEdit"
            @keyup.native="proving('amount', index)"
          ></a-input>
          <div class="remove-buttons" v-if="!hidden_sell_price && !disable_sell_price && canEdit">
            <i class="common-svg-delete" @click="deletePrice(price, index)">
              <svg-icon icon-name="trash" style="width: 10px; height: 10px; color: #fff" />
            </i>
          </div>
        </div>
      </a-form-model-item>
      <div class="btn-box">
        <a-button
          type="link"
          icon="plus"
          v-if="!hidden_sell_price && !disable_sell_price && canEdit"
          @click="addPrice"
          >{{ $t('global_add') }}</a-button
        >
      </div>
      <tpl_modify_reason_mark
        v-if="needNote"
        v-model="form.note"
        @change="changeForm"
        class="my-note"
      ></tpl_modify_reason_mark>
    </a-form-model>
  </div>
</template>

<script>
const admin_const = require('@/admin_const')
import tpl_modify_reason_mark from '../../price/tpl_modify_reason_mark'
import {
  computeTR,
  setTRAndGetBase,
  setCostAndGetBase,
  provingTakeRate
} from '../../revenue/take_rate_tool/utils.js'

export default {
  name: 'basic_setting_form.vue',
  components: { tpl_modify_reason_mark },
  model: {
    prop: 'form',
    event: 'change'
  },
  props: {
    cost_currency: {
      type: String,
      default: ''
    },
    base_currency: {
      type: String,
      default: ''
    },
    sku_id: {
      type: Number
    },
    time_slot: {
      type: Boolean,
      default: false
    },
    batch_edit: {
      type: Boolean,
      default: false
    },
    form: {
      type: Object,
      default: () => {
        return {
          cost_amount: '',
          market_amount: '',
          base_amount: '',
          custom_price: [{ amount: undefined, currency: undefined }],
          take_rate: '',
          note: ''
        }
      }
    },
    init: {
      type: Boolean,
      default: false
    },
    needNote: {
      type: Boolean,
      default: false
    },
    activity_id: {
      type: Number
    },
    canEdit: {
      type: Boolean,
      default: true
    },
    timeslotOtr: {
      type: [Number, String]
    },
    timeslotLtr: {
      type: [Number, String]
    },
    calenderPrice: {
      type: Object,
      default: () => {
        return {
          currency: '',
          cost_price: '',
          market_price: '',
          sell_price: '',
          special_price_list: [
            {
              currency: '',
              cost_price: '',
              market_price: '',
              sell_price: ''
            }
          ]
        }
      }
    }
  },
  computed: {
    formItemLayout() {
      const { layout } = this.form
      return layout === 'horizontal'
        ? {
            labelCol: { span: 4 },
            wrapperCol: { span: 5 }
          }
        : {}
    },
    currencies() {
      return this.form.custom_price.map((i) => i.currency)
    },
    is_loss_leader() {
      return [1, 2].includes(this.tier)
    },
    //价格审核页面，修改单个timeslot时不可以修改成本价和市场价
    disable_cost_price() {
      return this.time_slot && this.$route.name === 'price_audit_detail'
    },
    //价格审核页面，批量修改基本设置，不展示成本价和市场价
    is_hidden() {
      return this.$route.name === 'price_audit_detail' && this.batch_edit
    },
    //sku修改价格页面，lossleader单个timeslot不可以修改售价和特殊售卖价
    disable_sell_price() {
      return this.is_loss_leader && this.$route.name === 'pkg_price_modify' && this.time_slot
    },
    //修改sku价格，如果是lossleader,批量修改不展示售价和特殊售价
    hidden_sell_price() {
      return this.is_loss_leader && this.$route.name === 'pkg_price_modify' && this.batch_edit
    },
    //lossleader 修改sku价格时，调用的接口不同
    loss_leader_modify() {
      return this.is_loss_leader && this.$route.name === 'pkg_price_modify'
    },
    refer_tr() {
      return this.otr || this.timeslotOtr || this.actOtr
    }
  },
  watch: {
    sku_id(v) {
      if (this.init && v) {
        this.getPricesBySkuId()
      }
    },
    async base_currency(v) {
      if (v) {
        this.getExchangeRate()
      }
    },
    async form(v) {
      if (v.base_amount && v.cost_amount) {
        if (this.exchangeRate) {
          this.getTakeRate()
        } else {
          this.getExchangeRate()
        }
      }
    }
  },
  data() {
    return {
      refresh_loading: false,
      currencyOptions: [],
      otr: '',
      ltr: '',
      actOtr: '',
      actLtr: '', //活动的last take rate
      exchangeRate: '',
      rules: {
        cost_amount: [{ required: true, message: 'Cost price is invalid' }],
        market_amount: [{ required: true, message: 'Market price is invalid' }],
        base_amount: [{ required: true, message: 'Selling price is invalid' }],
        custom_price: [
          {
            validator: (rule, value, callback) => {
              console.log('value :', value)
              let prices = value.filter((i) => (!!i.currency && !!i.amount) || (!i.currency && !i.amount))
              if (prices.length < value.length) {
                callback(new Error('Invalid'))
              } else {
                callback()
              }
            }
          }
        ],
        take_rate: [{ required: true, message: 'New take rate is invalid' }]
      },
      costom_price_copy: [],
      tier: ''
    }
  },
  async mounted() {
    this.currencyOptions = admin_const.SUPPORT_CURRENCY_SYMBO.map((v) => ({
      label: v[0],
      value: v[0]
    }))
    //是否loss-leader [1,2]
    this.tier = (
      await ajax.get(
        {
          url: ADMIN_API.act.get_activity_tier,
          params: {
            activity_id: +this.activity_id
          }
        },
        { loading: true }
      )
    ).tier
    //this.$store.commit("save_tier", this.tier);
    if (this.cost_currency && this.base_currency) {
      await this.getExchangeRate()
    }
    this.getActivityTakeRate()
    this.setCalenderPrice()
  },
  methods: {
    async getActivityTakeRate() {
      let res = await ajax.get({
        url: ADMIN_API.act.query_activity_take_rate,
        params: {
          activity_id: this.activity_id
        }
      })
      this.actOtr = res.override_take_rate
      this.actLtr = res.last_take_rate
    },
    setCalenderPrice() {
      if (!(this.init || this.batch_edit || this.time_slot)) {
        if (this.calenderPrice) {
          this.form.cost_amount = this.calenderPrice.cost_price
          this.form.base_amount = this.calenderPrice.sell_price
          this.form.market_amount = this.calenderPrice.market_price
          if (this.calenderPrice.special_price_list) {
            this.form.custom_price = this.calenderPrice.special_price_list.map((item) => {
              return {
                amount: item.amount || undefined,
                currency: item.currency || undefined
              }
            })
          }
          this.getTakeRate()
        }
      }
    },
    async getExchangeRate() {
      //成本汇率转售价汇率
      this.exchangeRate = (
        await ajax.get({
          url: ADMIN_API.act.get_exchange_rate,
          params: {
            currency_from: this.cost_currency,
            currency_to_list: this.base_currency
          }
        })
      )[0].to_value
      this.getTakeRate()
    },
    getTakeRate() {
      let res = computeTR(this.form.cost_amount, this.form.base_amount, this.exchangeRate)
      this.$set(this.form, 'take_rate', res)
    },
    setTakeRate() {
      let res = setTRAndGetBase(
        this.form.cost_amount,
        this.form.base_amount,
        this.form.take_rate,
        this.exchangeRate
      )
      this.form.cost_amount = res.cost
      this.form.base_amount = res.base
      this.$set(this.form, 'take_rate', res.take_rate)
    },
    setCostAndRate() {
      if (this.disable_sell_price) {
        return
      }
      let res = setCostAndGetBase(
        this.form.cost_amount,
        this.form.base_amount,
        this.form.take_rate,
        this.exchangeRate
      )
      this.form.cost_amount = res.cost
      this.form.base_amount = res.base
      this.$set(this.form, 'take_rate', res.take_rate)
    },
    async getPricesBySkuId() {
      let res = (await ADMIN_API.getSKUBasicSetting({ sku_id: this.sku_id })).result
      if (!res.custom_price || !res.custom_price.length) {
        res.custom_price = [{ currency: '', amount: '' }]
      }
      let { cost_amount, market_amount, base_amount, custom_price, override_take_rate, last_take_rate } = res
      this.form = {
        cost_amount,
        market_amount,
        base_amount,
        custom_price
      }
      this.otr = override_take_rate
      this.ltr = last_take_rate
      this.cost_currency = res.cost_currency
      this.base_currency = res.base_currency
      this.costom_price_copy = _.cloneDeep(custom_price)
      if (this.exchangeRate) {
        this.getTakeRate()
      } else {
        this.getExchangeRate()
      }
    },
    addPrice() {
      this.form.custom_price.push({ currency: undefined, amount: '' })
    },
    deletePrice(price, index) {
      if (this.form.custom_price.length > 1) {
        this.form.custom_price.splice(index, 1)
      } else {
        this.form.custom_price = [{ currency: undefined, amount: '' }]
      }
    },
    changeForm() {
      this.$emit('change', this.form)
    },
    resetForm() {
      this.$refs.settingForm.resetFields()

      this.form.custom_price = [{ currency: '', amount: '' }]
    },
    refreshTR() {
      this.refresh_loading = true
      this.$emit('refreshTR')
    },
    proving(field, index) {
      let obj = this.form
      if (index !== undefined) {
        obj = this.form.custom_price[index]
      }
      if (typeof obj[field] !== 'string') {
        obj[field] = obj[field].toString()
      }
      if (field === 'take_rate') {
        obj[field] = provingTakeRate(obj[field])
      } else {
        obj[field] = obj[field].replace(/[^.\d]/g, '') //清除"数字"和"."以外的字符
        obj[field] = obj[field].replace(/^\./g, '') //验证第一个字符是数字而不是.
        obj[field] = obj[field].replace(/\.{2,}/g, '.') //只保留第一个. 清除多余的.
        obj[field] = obj[field]
          .replace('.', '$#$')
          .replace(/\./g, '')
          .replace('$#$', '.') //只允许输入一个小数点
        obj[field] = obj[field].replace(/^(-)*(\d+)\.(\d\d).*$/, '$1$2.$3') //只能输入两个小数
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.btn-box {
  padding: 0 0 12px 102px;
}
.curr-amount-wrap {
  ::v-deep &.ant-row.ant-form-item {
    width: 100%;
    .ant-col.ant-form-item-label {
      vertical-align: top;
      top: 3px;
    }
    .ant-col.ant-form-item-control-wrapper {
      width: calc(100% - 122px);
    }
  }
}
.curr-amount {
  display: inline-block;
  box-sizing: border-box;
  width: 50%;
  .short-input {
    width: 116px;
    margin-right: 10px;
  }
  .remove-buttons {
    display: inline-block;
  }
}
.note-box {
  display: flex;
  padding: 8px;
  margin-bottom: 12px;
  font-size: 12px;
  line-height: 14px;
  color: #ffab00;
  background: rgba(255, 171, 0, 0.05);
  div {
    width: 50%;
  }
  .text {
    color: rgba(0, 0, 0, 0.65);
  }
  .rate {
    margin-left: 10px;
    color: #36b37e;
  }
}
</style>
