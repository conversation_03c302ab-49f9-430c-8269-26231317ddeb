<template>
  <div>
    <a-modal
      class="common-line-style"
      width="790px"
      :visible.sync="dialogVisible"
      :title="
        pricingModel === 1 ? $t('pm_default_setting') : $t('pm_edit_cost')
      "
      :closable="false"
      :close-on-click-modal="false"
    >
      <a-tabs
        v-model="activeUnit"
        @click.native.stop="changeUnit"
        class="common-line-style"
      >
        <a-tab-pane
          v-for="unit in unit_ids"
          :tab="getUnitName(unit)"
          :key="unit.price_value.sku_id + ''"
        >
        </a-tab-pane>
      </a-tabs>
      <basic_setting_form
        style="max-width: 100%;"
        :base_currency="base_currency"
        :cost_currency="cost_currency"
        :calender-price="calenderPrice"
        v-model="priceData"
        ref="editForm"
        :init="false"
        :needNote="false"
        :activity_id="activity_id"
      ></basic_setting_form>
      <div slot="footer" class="dialog-footer">
        <a-button @click="cancelSetting">{{ $t('pm_cancel') }}</a-button>
        <a-button type="primary" @click="submitSetting">{{
          $t('pm_submit')
        }}</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script>
import basic_setting_form from './basic_setting_form'
import { formatPrices, price_backend_message } from './util'
import { warn_message } from '../../price/utils'
import { checkMultiTRWarning } from '../../revenue/take_rate_tool/utils.js'

export default {
  name: 'unit_price.vue',
  components: { basic_setting_form },
  props: {
    activity_id: {
      type: Number
    },
    package_id: {
      type: Number
    },
    base_currency: {
      type: String,
      default: ''
    },
    cost_currency: {
      type: String,
      default: ''
    },
    unit_ids: {
      type: Array,
      default: () => []
    },
    change_type: {
      type: String,
      default: 'merchant'
    },
    pricingModel: {
      type: Number,
      default: 1
    },
    calenderPrice: {
      type: Object,
      default: () => {
        return {
          currency: '',
          cost_price: '',
          market_price: '',
          sell_price: '',
          special_price_list: [
            {
              currency: '',
              cost_price: '',
              market_price: '',
              sell_price: ''
            }
          ]
        }
      }
    }
  },
  data() {
    return {
      activeUnit: null,
      formData: {},
      dialogVisible: false,
      priceData: null,
      currentUnit: '',
      finished: false
    }
  },
  watch: {
    dialogVisible(v) {
      if (v) {
        this.initForm()
      } else {
        if (!this.finished) {
          this.$emit('cancel')
        }
      }
    }
  },
  methods: {
    initForm() {
      this.activeUnit = this.unit_ids[0].price_value.sku_id + ''
      this.currentUnit = this.activeUnit
      this.formData = { package_id: this.package_id, sku_price_list: [] }
      this.unit_ids.forEach((u) => {
        this.formData.sku_price_list.push({
          sku_id: u.price_value.sku_id,
          price: {
            base_amount: '', // 基准价金额,
            cost_amount: '', // 成本价金额,
            custom_price: [
              // 货币定价,
              {
                amount: undefined, // 金额,
                currency: undefined // 货币,
              }
            ],
            take_rate: '',
            market_amount: '', // 市场价金额,
            note: '' // 价格备注,
          }
        })
      })
      this.priceData = this.formData.sku_price_list.find(
        (i) => i.sku_id === +this.activeUnit
      ).price
    },
    getUnitName(unit) {
      let lang = klook.getEditLang()
      let local = unit.price_local.find((i) => lang === i.language_type)
      if (local) {
        return local.unit_name
      } else {
        return unit.price_local.find((i) => 'en_US' === i.language_type)
          .unit_name
      }
    },
    cancelSetting() {
      this.$emit('cancel')
      this.$refs.editForm.resetForm()
      this.dialogVisible = false
    },
    changeUnit() {
      this.$refs.editForm.$refs.settingForm.validate((valid) => {
        if (!valid) {
          this.activeUnit = this.currentUnit
        } else {
          this.currentUnit = this.activeUnit
        }
        this.priceData = this.formData.sku_price_list.find(
          (i) => i.sku_id === +this.activeUnit
        ).price
      })
    },
    checkValid(arr) {
      return arr.every((i) => {
        return ['cost_amount', 'base_amount', 'market_amount'].every(
          (j) => !!i.price[j]
        )
      })
    },
    submitSetting() {
      this.$refs.editForm.$refs.settingForm.validate(async (valid) => {
        if (!valid) {
          return
        }
        if (!this.checkValid(this.formData.sku_price_list)) {
          this.$message.warning('Please fill in all units')
          return
        }
        if (!this.$refs.editForm.is_loss_leader) {
          let allUnitAndRates = []
          this.unit_ids.forEach((i) => {
            allUnitAndRates.push({
              unit_name: this.getUnitName(i),
              unit_id: i.price_value.sku_id,
              oldRate: this.$refs.editForm.actOtr,
              newRate: this.formData.sku_price_list.find(
                (j) => j.sku_id === i.price_value.sku_id
              ).price.take_rate
            })
          })
          let takeRateWarning = await checkMultiTRWarning(this, allUnitAndRates)
          if (takeRateWarning === 'cancel') {
            return
          }
        }
        this.$confirm({
          title: __('pm_reminder'),
          content: __('pm_change_merchant_warn'),
          onOk: () => {
            this.tipComfirm()
          }
        })
      })
    },
    async tipComfirm() {
      let data = {
        activity_id: this.activity_id,
        package_id: this.package_id
      }
      data.sku_price_list = this.formData.sku_price_list.map((i) => {
        return {
          activity_id: this.activity_id,
          sku_id: i.sku_id,
          package_id: this.package_id,
          note: i.price.note,
          price: {
            base_amount: i.price.base_amount,
            base_currency: this.base_currency,
            cost_amount: i.price.cost_amount,
            cost_currency: this.cost_currency,
            custom_price: i.price.custom_price.filter(
              (j) => !!j.currency && !!j.amount
            ),
            market_amount: i.price.market_amount
          }
        }
      })
      data.sku_price_list.map((i) => formatPrices(i.price))
      let ajaxOpt = {
        msgOpt: {
          isErrMsg: false
        }
      }
      let res
      if (this.change_type === 'pricing_model') {
        let url = this.pricingModel
          ? '/v1/pricingadminsrv/price/calendar_service/switch_pricing_model_to_calendar'
          : '/v1/pricingadminsrv/price/calendar_service/switch_pricing_model_to_constant'
        res = await ajax.postBody({ url, data }, ajaxOpt)
      } else {
        res = await ajax.postBody(
          {
            url: ADMIN_API.act.reset_price_calendar,
            data
          },
          ajaxOpt
        )
      }
      if (res.success) {
        await warn_message(this, res.result)
        this.$emit('done')
        this.finished = true
        this.$refs.editForm.resetForm()
        this.dialogVisible = false
      } else {
        price_backend_message(this, res.error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.note-box {
  padding: 8px;
  margin-bottom: 12px;
  font-size: 12px;
  line-height: 14px;
  color: #ffab00;
  background: rgba(255, 171, 0, 0.05);
  .text {
    color: rgba(0, 0, 0, 0.65);
  }
  .rate {
    margin-left: 10px;
    color: #36b37e;
  }
}
.dialog-footer {
  text-align: right;
}
</style>
