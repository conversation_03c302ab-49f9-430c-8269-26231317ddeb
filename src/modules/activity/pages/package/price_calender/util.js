export const formatcustomPriceFlag = function(before, after) {
  let res = []
  before.forEach((i) => {
    let pr = after.find((j) => j.currency === i.currency)
    if (pr) {
      res.push({
        currency: i.currency,
        amount: pr.amount,
        flag: 0,
        id: i.id
      })
    } else {
      res.push(Object.assign(i, { flag: 1 }))
    }
  })
  after.forEach((i) => {
    let pr = before.find((j) => j.currency === i.currency)
    if (!pr) {
      res.push({
        currency: i.currency,
        amount: i.amount,
        flag: 0
      })
    }
  })
  return res
}
export const formatPrices = function(obj) {
  let keys = ['cost_amount', 'base_amount', 'market_amount']
  keys.forEach((k) => {
    if (obj[k] && typeof obj[k] === 'string') {
      obj[k] = +obj[k]
    }
  })
  if (obj.custom_price && obj.custom_price.length) {
    obj.custom_price.map((p) => {
      if (p.amount && typeof p.amount === 'string') p.amount = +p.amount
      return p
    })
  }
}
export const price_backend_message = async ($vm, result, suc) => {
  if (
    toString.call(result) === '[object Object]' &&
    Object.prototype.hasOwnProperty.call(result, 'message') &&
    result.message
  ) {
    let h = $vm.$createElement
    $vm.$warning({
      title: '[Backend Response]',
      content: h('div', {
        style: 'max-height: calc(100vh - 200px); overflow-y: auto;',
        domProps: {
          innerHTML: result.message.replace(/\n/gi, '<br /><br />')
        }
      }),
      onText: $vm.$t('global_confirm'),
      onOk() {
        suc && suc(result)
      }
    })
  }
}
