<template>
  <component
    ref="component"
    :is="
      isBdAudit
        ? 'BulkModificationFooterAdminBd'
        : 'BulkModificationFooterAdminAdmin'
    "
    v-bind="$attrs"
    v-on="$listeners"
  />
</template>

<script>
import { mapState } from 'vuex'
import BulkModificationFooterAdminBd from './BulkModificationFooterAdminBd.vue'
import BulkModificationFooterAdminAdmin from './BulkModificationFooterAdminAdmin.vue'

export default {
  name: 'BulkModificationFooterAdmin',
  components: {
    BulkModificationFooterAdminBd,
    BulkModificationFooterAdminAdmin
  },
  computed: {
    ...mapState({
      isBdAudit: (state) => state.isBdAudit
    })
  },
  methods: {
    reset() {
      this.$refs.component.reset()
    }
  }
}
</script>
