<template>
  <div ref="footer" class="bulk-modification-footer-admin-admin">
    <GeneralAlert
      v-if="alertMsg"
      banner
      :show-icon="false"
      :message="alertMsg"
      style="margin-bottom: 10px;"
    />
    <a-spin :spinning="isTakeRateLoading">
      <TakeRateTips
        v-if="type === 'price' && (overrideTakeRate || lastTakeRate)"
        style="margin: 0 0 10px;"
        :override-take-rate="overrideTakeRate"
        :last-take-rate="lastTakeRate"
      />
    </a-spin>
    <div v-show="type === 'button'" class="align-right">
      <a-button
        v-if="!isFixedPriceModel"
        :style="{ marginRight: '12px', padding: 0 }"
        type="link"
        @click="handleOpenDefaultPriceSetting"
      >
        <a-icon type="edit" />
        {{ $t('package_bulk_modification_default_price_setting') }}
      </a-button>
      <BasicPopoverButton
        type="primary"
        pop-placement="topRight"
        :disabled="disablePrice || isAdminEm"
        :has-popover="disablePrice"
        :style="{ marginRight: '12px' }"
        @click="type = 'price'"
      >
        <a-icon type="edit" />
        {{ $t(`package_bulk_modification_edit_price`) }}

        <template slot="tips">
          <span>
            {{ $t('package_bulk_modification_cost_price_tips') }}
          </span>
          <a-button
            type="link"
            style="margin: 0; padding: 0;"
            @click="handleOpenFilter"
          >
            {{ $t('global_filter') }}
          </a-button>
        </template>
      </BasicPopoverButton>
      <BasicPopoverButton
        type="primary"
        :disabled="!isMainUnit || isAdminEm || isTripMapped"
        :has-popover="!isMainUnit"
        :style="{ marginRight: '12px' }"
        @click="type = 'inventory'"
      >
        <a-icon type="edit" />
        {{ $t('package_bulk_modification_edit_inventory') }}

        <template slot="tips">
          <span>
            {{ $t('package_bulk_modification_edit_warning') }}
          </span>
          <a-button
            type="link"
            style="margin: 0; padding: 0;"
            @click="handleSelectMainUnit"
          >
            {{ mainUnitName }}
          </a-button>
        </template>
      </BasicPopoverButton>
      <BasicPopoverButton
        type="primary"
        :disabled="!isMainUnit || isAdminEm || isTripMapped"
        :has-popover="!isMainUnit"
        pop-placement="topRight"
        @click="type = 'cutoff'"
      >
        <a-icon type="edit" />
        {{ $t('package_bulk_modification_edit_cutoff') }}

        <template slot="tips">
          <span>{{ $t('package_bulk_modification_edit_warning') }}</span>
          <a-button
            type="link"
            style="margin: 0; padding: 0;"
            @click="handleSelectMainUnit"
          >
            {{ mainUnitName }}
          </a-button>
        </template>
      </BasicPopoverButton>
    </div>
    <div v-show="type !== 'button'">
      <a-spin :spinning="loading">
        <div style="max-height: 300px; overflow: auto;">
          <PriceForm
            v-if="type === 'price'"
            ref="form"
            type="admin"
            :form="form"
            :costCurrency="costCurrency"
            :sellingCurrency="sellingCurrency"
            :exchangeRate="exchangeRate"
            :isPriceProtected="isPriceProtected"
          />
          <InventoryForm
            v-else-if="type === 'inventory'"
            ref="form"
            :form="form"
          />
          <CutoffForm v-else-if="type === 'cutoff'" ref="form" :form="form" />
        </div>

        <div class="align-right">
          <a-button :style="{ marginRight: '8px' }" @click="handleCancel">
            {{ $t('global_button_cancel') }}
          </a-button>
          <BasicPopoverButton
            v-if="selectedRowKeys.length > 0"
            type="primary"
            pop-placement="topRight"
            :disabled="disablePrice"
            :has-popover="disablePrice"
            :loading="saveLoading"
            @click="handleSave"
          >
            {{ $t('global_save') }}

            <template slot="tips">
              <span>
                {{ $t('package_bulk_modification_cost_price_tips') }}
              </span>
              <a-button
                type="link"
                style="margin: 0; padding: 0;"
                @click="handleOpenFilter"
              >
                {{ $t('global_filter') }}
              </a-button>
            </template>
          </BasicPopoverButton>
          <BasicPopoverButton
            v-else
            type="primary"
            disabled
            has-popover
            pop-placement="topRight"
            :pop-msg="$t('package_bulk_modification_save_tips')"
            :loading="saveLoading"
          >
            {{ $t('global_save') }}
          </BasicPopoverButton>
        </div>
      </a-spin>
    </div>
    <DefaultPriceSettingAdmin
      ref="defaultPriceSetting"
      :isPriceProtected="isPriceProtected"
    />
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { getTimeOffset, computeTR, bestMatchLang } from '@activity/utils'
import {
  priceChangeReasonList,
  MAX_INVENTORY
} from '@activity/pages/package/package_const.js'
import TakeRateTips from '@activity/pages/components/TakeRateTips.vue'
import DefaultPriceSettingAdmin from './DefaultPriceSettingAdmin.vue'
import GeneralAlert from '@activity/pages/components/GeneralAlert.vue'
import BasicPopoverButton from '@activity/pages/components/BasicPopoverButton.vue'
import { warn_message } from '@activity/pages/price/utils'
import PriceForm from './forms/PriceForm.vue'
import InventoryForm from './forms/InventoryForm.vue'
import CutoffForm from './forms/CutoffForm.vue'

export default {
  name: 'BulkModificationFooterAdminAdmin',
  components: {
    TakeRateTips,
    DefaultPriceSettingAdmin,
    GeneralAlert,
    BasicPopoverButton,
    PriceForm,
    InventoryForm,
    CutoffForm
  },
  inject: ['packageId', 'bulkModification'],
  props: {
    data: {
      type: Object,
      required: true
    },
    selectedUnit: {
      type: Number,
      default: 0
    },
    selectedRowKeys: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      MAX_INVENTORY,
      priceChangeReasonList,

      type: 'button',
      loading: false,
      isTakeRateLoading: false,
      saveLoading: false,
      overrideTakeRate: '',
      lastTakeRate: '',
      exchangeRate: '',
      form: {
        costPrice: '',
        retailPrice: '',
        sellingPrice: '',
        takeRate: '',
        specialSellingPriceList: [],
        reason: undefined,
        reasonText: '',
        cutoffType: 0,
        cutoffTime: '00:00',
        isLimited: false,
        inventory: ''
      }
    }
  },
  computed: {
    ...mapState({
      isAdminEm: (state) => state.isAdminEm,
      packageSkuData: (state) => state.packageSkuData,
      priceModel: (state) => state.priceModel
    }),
    ...mapGetters(['unitList', 'mainUnit', 'isTripMapped']),
    isMainUnit() {
      return this.mainUnit && this.mainUnit.sku_id === this.selectedUnit
    },
    isFixedPriceModel() {
      return this.priceModel === 0
    },
    mainUnitName() {
      if (!this.mainUnit) {
        return ''
      }

      return bestMatchLang('unit_name', 'language', this.mainUnit.local)
    },
    alertMsg() {
      if (this.type === 'price' && this.isFixedPriceModel) {
        return this.$t('package_bulk_modification_fixed_price_tips')
      }

      if (this.type === 'inventory') {
        return this.$t('package_bulk_modification_inventory_tips')
      }

      if (this.type === 'cutoff') {
        return this.$t('package_bulk_modification_cutoff_tips')
      }

      return ''
    },
    costCurrency() {
      const { currency } = this.packageSkuData
      return (currency || {}).merchant_currency || ''
    },
    sellingCurrency() {
      const { currency } = this.packageSkuData
      return (currency || {}).selling_currency || ''
    },
    selectedList() {
      return (this.data.calendar || []).filter((item) =>
        this.selectedRowKeys.includes(item.schedule_id)
      )
    },
    disablePrice() {
      // 携程api要校验成本价
      if (this.isTripMapped) {
        return this.selectedList.length > 0
          ? !this.selectedList.every(
              (item) => item.cost_price === this.selectedList[0].cost_price
            )
          : false
      }
      return false
    },
    // 携程api 价格保护
    isPriceProtected() {
      if (!this.isTripMapped) {
        return false
      }

      // 携程的价格保护
      const target = this.unitList.find(
        (item) => item.sku_id === this.selectedUnit
      )
      return target && target.private_price_status === 1
    }
  },
  watch: {
    async type(val) {
      // 初始化：
      // 固定价格模型需要请求价格数据
      // 其它模型只在携程api的时候取列表的第一个
      if (val === 'price') {
        if (this.isFixedPriceModel) {
          await this.initFixedPrice()
          this.$emit('toggleFrozen', true)
        } else {
          this.initCostPrice()
          await this.getTakeRate()
        }
      }

      // 重新设置父容器的下间距
      this.$nextTick(() => {
        const footerDom = this.$refs.footer
        this.$emit('setMargin', footerDom ? footerDom.offsetHeight : 0)
      })
    },
    async selectedList() {
      if (this.type === 'price' && !this.isFixedPriceModel) {
        this.initCostPrice()
        await this.getTakeRate()
      }
    }
  },
  async created() {
    await this.getExchangeRate()
  },
  methods: {
    initCostPrice() {
      const { isTripMapped, selectedList } = this

      if (isTripMapped) {
        this.form.costPrice =
          selectedList.length > 0 ? selectedList[0].cost_price : ''
        this.computeTakeRate()
      }
    },
    async getExchangeRate() {
      this.loading = true
      const res = await ajax.get(ADMIN_API.act.get_exchange_rate, {
        params: {
          currency_from: this.costCurrency,
          currency_to_list: this.sellingCurrency
        }
      })
      this.exchangeRate = res[0].to_value
      this.loading = false
    },
    async initFixedPrice() {
      this.loading = true
      this.isTakeRateLoading = true
      const res = await ajax.get(ADMIN_API.act.query_fixed_price, {
        params: {
          sku_id: this.selectedUnit
        }
      })

      this.overrideTakeRate = res.override_take_rate
      this.lastTakeRate = res.last_take_rate
      this.form.costPrice = res.cost_price
      this.form.retailPrice = res.retail_price
      this.form.sellingPrice = res.selling_price
      this.form.takeRate = computeTR(
        res.cost_price,
        res.selling_price,
        this.exchangeRate
      )
      this.form.specialSellingPriceList = res.special_selling_price
      this.form.reason = undefined
      this.form.reasonText = ''

      this.loading = false
      this.isTakeRateLoading = false
    },
    async getTakeRate() {
      this.isTakeRateLoading = true
      const res = await ajax.post(
        ADMIN_API.act.get_take_rate_for_update_calendar_price,
        {
          data: {
            sku_id: this.selectedUnit,
            time_point: this.selectedList.map((item) => item.start_time)
          }
        }
      )
      this.isTakeRateLoading = false

      this.lastTakeRate = res.last_take_rate
      this.overrideTakeRate = res.override_take_rate
    },
    handleSelectMainUnit() {
      this.$emit('selectUnit', this.mainUnit.sku_id)
    },
    handleOpenDefaultPriceSetting() {
      this.$refs.defaultPriceSetting &&
        this.$refs.defaultPriceSetting.open(this.selectedUnit)
    },
    handleCancel() {
      this.type = 'button'
      this.form = _.cloneDeep(this.$options.data().form)
      this.$emit('toggleFrozen', false)
    },
    handleOpenFilter() {
      this.$emit('openFilter')
    },
    reset() {
      this.handleCancel()
    },
    computeTakeRate() {
      this.form.takeRate = computeTR(
        this.form.costPrice,
        this.form.sellingPrice,
        this.exchangeRate
      )
    },
    async handleSave() {
      const valid = await this.$refs.form.validate().catch(() => false)

      if (!valid) {
        return
      }

      this.saveLoading = true
      const res =
        this.type === 'price'
          ? await this.savePrice()
          : await this.saveInventoryOrCutoff()
      this.saveLoading = false

      if (res.success) {
        this.type = 'button'
        this.bulkModification.update()
        this.$message.success(this.$t('global_modify_success'))
        await warn_message(this, res.result)
      } else {
        this.$message.error(this.$t('global_modify_error'))
      }
    },
    async savePrice() {
      const apiUrl = this.isFixedPriceModel
        ? ADMIN_API.act.update_fixed_price
        : ADMIN_API.act.update_calendar_price

      let note = this.form.reasonText
      if (this.form.reason !== 0) {
        const target = this.priceChangeReasonList.find(
          (item) => item.value === Number(this.form.reason)
        )
        note = target ? target.text : ''
      }

      const specialSellingPriceData = this.$refs.form.getSpecialSellingPriceData()
      const res =
        (await ajax.postBody(apiUrl, {
          data: {
            note,
            sku_id: this.selectedUnit,
            selling_currency: this.sellingCurrency,
            cost_currency: this.costCurrency,
            selling_price: Number(this.form.sellingPrice),
            cost_price: Number(this.form.costPrice),
            retail_price: Number(this.form.retailPrice),
            time_point: this.selectedList.map((item) => item.start_time),
            special_selling_price: specialSellingPriceData.map((item) => ({
              ...item,
              price: Number(item.price)
            }))
          }
        })) || {}

      return res
    },
    async saveInventoryOrCutoff() {
      const cutoffTime =
        this.form.cutoffType * 24 * 60 * 60 +
        getTimeOffset(this.form.cutoffTime)
      const res =
        (await ajax.postBody(ADMIN_API.act.update_schedule, {
          data: {
            sku_id: this.selectedUnit,
            package_id: this.packageId,
            cut_off_time: this.type === 'cutoff' ? cutoffTime : -1,
            inventory: this.type === 'inventory' ? this.getInventory() : -1,
            schedule_id_list: this.selectedRowKeys
          }
        })) || {}

      return res
    },
    getInventory() {
      return this.form.isLimited ? Number(this.form.inventory) : MAX_INVENTORY
    }
  }
}
</script>

<style lang="scss">
.bulk-modification-footer-admin-admin {
  .ant-form-item-label > label {
    padding-right: 8px;
    white-space: break-spaces;
    display: inline-block;
    line-height: normal;
  }
}
</style>

<style lang="scss" scoped>
.bulk-modification-footer-admin-admin {
  position: absolute;
  padding: 10px 16px;
  right: 0;
  bottom: 0;
  width: 100%;

  border-top: 1px solid #e9e9e9;
  background: #fff;
  z-index: 3;

  .align-right {
    text-align: right;
  }

  .w-800 {
    width: 800px;
  }

  .form-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
</style>
