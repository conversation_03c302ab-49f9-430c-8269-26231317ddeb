<template>
  <div ref="footer" class="bulk-modification-footer-merchant">
    <GeneralAlert
      v-if="alertMsg"
      banner
      :show-icon="false"
      :message="alertMsg"
      style="margin-bottom: 10px;"
    />
    <div v-show="type === 'button'" class="align-right">
      <a-button
        v-if="!isFixedPriceModel"
        :style="{ marginRight: '12px', padding: 0 }"
        :disabled="$root.nonEdit2status"
        type="link"
        @click="handleOpenDefaultPriceSetting"
      >
        <a-icon type="edit" />
        {{ $t('package_bulk_modification_default_price_setting') }}
      </a-button>
      <a-button
        type="primary"
        :style="{ marginRight: '12px' }"
        :disabled="$root.nonEdit2status"
        @click="type = 'price'"
      >
        <a-icon type="edit" />
        {{ $t('package_bulk_modification_edit_price') }}
      </a-button>
      <BasicPopoverButton
        type="primary"
        :disabled="!isMainUnit"
        :has-popover="!isMainUnit"
        :style="{ marginRight: '12px' }"
        @click="type = 'inventory'"
      >
        <a-icon type="edit" />
        {{ $t('package_bulk_modification_edit_inventory') }}

        <template slot="tips">
          <span>
            {{ $t('package_bulk_modification_edit_warning') }}
          </span>
          <a-button type="link" style="margin: 0; padding: 0;" @click="handleSelectMainUnit">
            {{ mainUnitName }}
          </a-button>
        </template>
      </BasicPopoverButton>
      <BasicPopoverButton
        type="primary"
        :disabled="!isMainUnit"
        :has-popover="!isMainUnit"
        pop-placement="topRight"
        @click="type = 'cutoff'"
      >
        <a-icon type="edit" />
        {{ $t('package_bulk_modification_edit_cutoff') }}

        <template slot="tips">
          <span>{{ $t('package_bulk_modification_edit_warning') }}</span>
          <a-button type="link" style="margin: 0; padding: 0;" @click="handleSelectMainUnit">
            {{ mainUnitName }}
          </a-button>
        </template>
      </BasicPopoverButton>
    </div>
    <div v-show="type !== 'button'">
      <a-spin :spinning="loading">
        <div style="max-height: 300px; overflow: auto;">
          <InlinePriceForm
            v-if="type === 'price'"
            ref="form"
            :form="form"
            :costCurrency="costCurrency"
            :sellingCurrency="sellingCurrency"
          />
          <InventoryForm v-else-if="type === 'inventory'" ref="form" :form="form" />
          <CutoffForm v-else-if="type === 'cutoff'" ref="form" :form="form" />
        </div>

        <div class="align-right">
          <a-button :style="{ marginRight: '8px' }" @click="handleCancel">
            {{ $t('global_button_cancel') }}
          </a-button>
          <BasicPopoverButton
            type="primary"
            pop-placement="topRight"
            :disabled="selectedRowKeys.length === 0"
            :has-popover="selectedRowKeys.length === 0"
            :pop-msg="$t('package_bulk_modification_save_tips')"
            :loading="saveLoading"
            @click="handleSave"
          >
            {{ $t('global_save') }}

            <template slot="tips">
              <span>{{ $t('package_bulk_modification_edit_warning') }}</span>
              <a-button type="link" style="margin: 0; padding: 0;" @click="handleSelectMainUnit">
                {{ mainUnitName }}
              </a-button>
            </template>
          </BasicPopoverButton>
        </div>
      </a-spin>
    </div>
    <DefaultPriceSettingMerchant ref="defaultPriceSetting" />
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { getTimeOffset, bestMatchLang } from '@activity/utils'
import { MAX_INVENTORY, cutoffDayList } from '@activity/pages/package/package_const.js'
import GeneralAlert from '@activity/pages/components/GeneralAlert.vue'
import DefaultPriceSettingMerchant from './DefaultPriceSettingMerchant.vue'
import BasicPopoverButton from '@activity/pages/components/BasicPopoverButton.vue'
import { warn_message } from '@activity/pages/price/utils'
import InlinePriceForm from './forms/InlinePriceForm.vue'
import InventoryForm from './forms/InventoryForm.vue'
import CutoffForm from './forms/CutoffForm.vue'

export default {
  name: 'BulkModificationFooterMerchant',
  components: {
    GeneralAlert,
    DefaultPriceSettingMerchant,
    BasicPopoverButton,
    InlinePriceForm,
    InventoryForm,
    CutoffForm
  },
  inject: ['packageId', 'bulkModification'],
  props: {
    data: {
      type: Object,
      required: true
    },
    selectedUnit: {
      type: Number,
      default: 0
    },
    selectedRowKeys: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      MAX_INVENTORY,
      cutoffDayList,

      type: 'button',
      loading: false,
      saveLoading: false,
      form: {
        costPrice: '',
        retailPrice: '',
        cutoffType: 0,
        cutoffTime: '00:00',
        isLimited: false,
        inventory: ''
      }
    }
  },
  computed: {
    ...mapState({
      packageSkuData: (state) => state.packageSkuData,
      priceModel: (state) => state.priceModel
    }),
    ...mapGetters(['mainUnit']),
    isMainUnit() {
      return this.mainUnit && this.mainUnit.sku_id === this.selectedUnit
    },
    isFixedPriceModel() {
      return this.priceModel === 0
    },
    mainUnitName() {
      if (!this.mainUnit) {
        return ''
      }

      return bestMatchLang('unit_name', 'language', this.mainUnit.local)
    },
    alertMsg() {
      if (this.type === 'price' && this.isFixedPriceModel) {
        return this.$t('package_bulk_modification_fixed_price_tips')
      }

      if (this.type === 'inventory') {
        return this.$t('package_bulk_modification_inventory_tips')
      }

      if (this.type === 'cutoff') {
        return this.$t('package_bulk_modification_cutoff_tips')
      }

      return ''
    },
    costCurrency() {
      const { currency } = this.packageSkuData
      return (currency || {}).merchant_currency || ''
    },
    sellingCurrency() {
      const { currency } = this.packageSkuData
      return (currency || {}).selling_currency || ''
    },
    selectedList() {
      return (this.data.calendar || []).filter((item) => this.selectedRowKeys.includes(item.schedule_id))
    }
  },
  watch: {
    async type(val) {
      // 固定价格模型要先请求价格数据
      if (this.isFixedPriceModel && val === 'price') {
        await this.initFixedPrice()
        this.$emit('toggleFrozen', true)
      }

      // 选择price的时候，table中的price要高亮，其它同理
      if (val === 'button') {
        this.$emit('modifyHighlightList', null)
      } else if (val === 'price') {
        this.$emit('modifyHighlightList', ['costPrice', 'retailPrice'])
      } else if (val === 'inventory') {
        this.$emit('modifyHighlightList', ['inventory'])
      }

      // 重新设置父容器的下间距
      this.$nextTick(() => {
        const footerDom = this.$refs.footer
        this.$emit('setMargin', footerDom ? footerDom.offsetHeight : 0)
      })
    }
  },
  methods: {
    async initFixedPrice() {
      this.loading = true
      const res = ajax.get(ADMIN_API.act.query_fixed_price, {
        params: {
          sku_id: this.selectedUnit
        }
      })

      this.form.costPrice = res.cost_price
      this.form.retailPrice = res.retail_price
      this.loading = false
    },
    handleOpenDefaultPriceSetting() {
      this.$refs.defaultPriceSetting && this.$refs.defaultPriceSetting.open(this.selectedUnit)
    },
    handleCancel() {
      this.type = 'button'
      this.form = _.cloneDeep(this.$options.data().form)
      this.$emit('toggleFrozen', false)
    },
    handleSelectMainUnit() {
      this.$emit('selectUnit', this.mainUnit.sku_id)
    },
    reset() {
      this.handleCancel()
    },
    getInventory() {
      return this.form.isLimited ? Number(this.form.inventory) : MAX_INVENTORY
    },
    async handleSave() {
      const valid = await this.$refs.form.validate().catch(() => false)

      if (!valid) {
        return
      }

      this.saveLoading = true
      const res = this.type === 'price' ? await this.savePrice() : await this.saveInventoryOrCutoff()

      if (res.success) {
        this.saveLoading = false
        this.type = 'button'
        this.bulkModification.update()
        this.$message.success(this.$t('global_modify_success'))
        await warn_message(this, res.result)
      } else {
        this.saveLoading = false
        this.$message.error(this.$t('global_modify_error'))
      }
    },
    async savePrice() {
      const apiUrl = this.isFixedPriceModel
        ? ADMIN_API.act.update_fixed_price
        : ADMIN_API.act.update_calendar_price
      const res =
        (await ajax.postBody(apiUrl, {
          data: {
            sku_id: this.selectedUnit,
            cost_currency: this.costCurrency,
            cost_price: Number(this.form.costPrice),
            retail_price: Number(this.form.retailPrice),
            selling_currency: this.sellingCurrency,
            time_point: this.selectedList.map((item) => item.start_time)
          }
        })) || {}

      return res
    },
    async saveInventoryOrCutoff() {
      const cutoffTime = this.form.cutoffType * 24 * 60 * 60 + getTimeOffset(this.form.cutoffTime)
      const res =
        (await ajax.postBody(ADMIN_API.act.update_schedule, {
          data: {
            sku_id: this.selectedUnit,
            package_id: this.packageId,
            cut_off_time: this.type === 'cutoff' ? cutoffTime : -1,
            inventory: this.type === 'inventory' ? this.getInventory() : -1,
            schedule_id_list: this.selectedRowKeys
          }
        })) || {}

      return res
    }
  }
}
</script>

<style lang="scss">
.bulk-modification-footer-merchant {
  .ant-form-item-label > label {
    padding-right: 8px;
    white-space: break-spaces;
    display: inline-block;
    line-height: normal;
  }
}
</style>

<style lang="scss" scoped>
.bulk-modification-footer-merchant {
  position: absolute;
  padding: 10px 16px;
  right: 0;
  bottom: 0;
  width: 100%;

  border-top: 1px solid #e9e9e9;
  background: #fff;
  z-index: 3;

  .align-right {
    text-align: right;
  }
}
</style>
