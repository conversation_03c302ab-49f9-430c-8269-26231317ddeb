<template>
  <a-spin
    :spinning="loading"
    class="bulk-modification-table"
    :class="{
      'hide-checkall': isFrozen,
      'is-admin': isAdmin,
      'is-merchant': !isAdmin
    }"
  >
    <div class="bulk-modification-table_header">
      <div style="margin-left: 10px;">
        <a-checkbox :checked="checkAll" :indeterminate="indeterminate" @change="handleCheckAll">
          {{ $t('global_button_all') }}
        </a-checkbox>
        <span>
          ({{ $t('global_total') }}:
          <a-button
            type="link"
            style="padding: 4px;"
            class="number-btn"
            :disabled="!isFilterSelected"
            @click="isFilterSelected = false"
          >
            {{ total }}
          </a-button>
          )
        </span>
        <span style="margin-left: 12px;">
          {{ $t('global_selected') }}:
          <a-button
            type="link"
            style="padding: 4px;"
            class="number-btn"
            :disabled="isFilterSelected || selectedRowKeys.length === 0"
            @click="isFilterSelected = true"
          >
            {{ selected }}
          </a-button>
        </span>
      </div>
      <a-popconfirm
        :title="$t('package_bulk_modification_delete_warning')"
        :ok-text="$t('global_button_ok')"
        :cancel-text="$t('global_button_cancel')"
        :disabled="disableDelete"
        placement="topRight"
        @confirm="handleDelete"
      >
        <a-button type="danger" size="small" :ghost="!disableDelete" :disabled="disableDelete">
          {{ $t('global_delete') }}
        </a-button>
      </a-popconfirm>
    </div>
    <a-table
      class="bulk-modification-table_table"
      :data-source="tableData"
      :scroll="{ x: isAdmin ? 1300 : false, y: 600 }"
      :columns="columns"
      :pagination="showPagination ? pagination : false"
      :row-selection="rowSelection"
      rowKey="id"
      bordered
      @change="handlePageChange"
    >
      <template slot="costPriceTitle">
        <span>{{ $t('package_price_cost') }}</span>
        <a-popover v-model="isPriceSelectorVisible" placement="bottom" trigger="click">
          <template slot="content">
            <a-radio-group v-model="priceSelectorValue">
              <a-radio
                v-for="price in costPriceList"
                :key="price"
                :value="price"
                style="display: block; line-height: 20px; width: 130px;"
              >
                <OverflowTextTooltip style="width: 120px; display: inline-block; vertical-align: middle;">
                  {{ price }}
                </OverflowTextTooltip>
              </a-radio>
            </a-radio-group>
            <div style="margin-top: 8px; display: flex; justify-content: flex-end;">
              <a-button size="small" style="margin-right: 8px;" @click="handlePriceSelectorReset">
                {{ $t('package_price_modify_reset') }}
              </a-button>
              <a-button type="primary" size="small" @click="handlePriceSelectorOk">
                {{ $t('global_confirm') }}
              </a-button>
            </div>
          </template>
          <svg-icon
            v-if="isAdmin"
            class="filter-btn"
            :icon-name="isFilteredPrice === null ? 'filter' : 'filter-on'"
          />
        </a-popover>
      </template>
      <template slot="timeslotText" slot-scope="text">
        <span :class="{ 'grey-text': checkGrey('timeslot') }">
          {{ text }}
        </span>
      </template>
      <template slot="costPriceText" slot-scope="text">
        <OverflowTextTooltip :class="{ 'grey-text': checkGrey('costPrice') }">
          {{ text }}
        </OverflowTextTooltip>
      </template>
      <template slot="retailPriceText" slot-scope="text">
        <OverflowTextTooltip :class="{ 'grey-text': checkGrey('retailPrice') }">
          {{ text }}
        </OverflowTextTooltip>
      </template>
      <template slot="sellingPriceText" slot-scope="text">
        <OverflowTextTooltip :class="{ 'grey-text': checkGrey('sellingPrice') }">
          {{ text }}
        </OverflowTextTooltip>
      </template>
      <template slot="takeRateText" slot-scope="text">
        <span :class="{ 'grey-text': checkGrey('takeRate') }">
          {{ text }}
        </span>
      </template>
      <template slot="specialSellingPrice" slot-scope="text">
        <span v-if="!text || text.length === 0"> - </span>
        <div v-for="(item, index) in text" :key="index">
          {{ item.currency }}
          {{ item.price | formatThousands }}
        </div>
      </template>
      <template slot="inventoryText" slot-scope="text">
        <span :class="{ 'grey-text': checkGrey('inventory') }">
          {{ text }}
        </span>
      </template>
      <template slot="cutoffText" slot-scope="text">
        <span :class="{ 'grey-text': checkGrey('cutoff') }">
          {{ text }}
        </span>
      </template>
    </a-table>
  </a-spin>
</template>

<script>
import { isAdmin, isMerchant } from '@/env'
import { mapState } from 'vuex'
import { checkAuth } from '@/plugins/authDirective'
import { MAX_INVENTORY } from '@activity/pages/package/package_const.js'
import { addZero, formatThousands } from '@activity/utils'
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'

const adminColumns = [
  {
    title: __('package_timeslot'),
    dataIndex: 'timeslot',
    width: '170px',
    fixed: 'left',
    scopedSlots: { customRender: 'timeslotText' }
  },
  {
    dataIndex: 'costPriceText',
    align: 'right',
    slots: { title: 'costPriceTitle' },
    scopedSlots: { customRender: 'costPriceText' }
  },
  {
    title: __('package_price_retail'),
    dataIndex: 'retailPriceText',
    align: 'right',
    scopedSlots: { customRender: 'retailPriceText' }
  },
  {
    title: __('package_price_selling'),
    dataIndex: 'sellingPriceText',
    align: 'right',
    scopedSlots: { customRender: 'sellingPriceText' }
  },
  {
    title: __('act_list_take_rate'),
    dataIndex: 'takeRate',
    align: 'right',
    scopedSlots: { customRender: 'takeRateText' }
  },
  {
    title: __('package_custom_selling'),
    dataIndex: 'specialSellingPrice',
    align: 'right',
    scopedSlots: { customRender: 'specialSellingPrice' }
  },
  {
    title: __('package_inventory'),
    dataIndex: 'inventory',
    align: 'right',
    scopedSlots: { customRender: 'inventoryText' }
  },
  {
    title: __('package_cut_off_time'),
    dataIndex: 'cutoffTime',
    scopedSlots: { customRender: 'cutoffText' }
  }
]

const merchantColumns = [
  {
    title: __('package_timeslot'),
    dataIndex: 'timeslot',
    scopedSlots: { customRender: 'timeslotText' }
  },
  {
    dataIndex: 'costPriceText',
    align: 'right',
    slots: { title: 'costPriceTitle' },
    scopedSlots: { customRender: 'costPriceText' }
  },
  {
    title: __('package_price_retail'),
    dataIndex: 'retailPriceText',
    align: 'right',
    scopedSlots: { customRender: 'retailPriceText' }
  },
  {
    title: __('package_inventory'),
    dataIndex: 'inventory',
    align: 'right',
    scopedSlots: { customRender: 'inventoryText' }
  },
  {
    title: __('package_cut_off_time'),
    dataIndex: 'cutoffTime',
    scopedSlots: { customRender: 'cutoffText' }
  }
]

export default {
  name: 'BulkModificationTable',
  components: {
    OverflowTextTooltip
  },
  inject: ['packageId', 'bulkModification'],
  filters: {
    formatThousands
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    highlightList: {
      type: Array,
      default: null
    },
    isFrozen: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isAdmin,
      isMerchant,

      columns: isAdmin ? adminColumns : merchantColumns,
      selectedRowKeys: [],
      pagination: {
        current: 1,
        pageSize: 20,
        showSizeChanger: true,
        pageSizeOptions: ['20', '50', '100'],
        showSizeChange: this.handleSizeChange,
        input: (page) => (this.pagination.current = page)
      },

      isFilterSelected: false,
      isFilteredPrice: null,
      isPriceSelectorVisible: false,
      priceSelectorValue: null
    }
  },
  computed: {
    ...mapState({
      isAdminEm: (state) => state.isAdminEm,
      isBdAudit: (state) => state.isBdAudit,
      isPkgApproved: (state) => state.isPkgApproved
    }),
    transformedData() {
      return (this.data.calendar || []).map((item) => {
        const result = {
          id: item.schedule_id,
          disabled: false,
          timeslot: item.start_time.substring(0, 16),
          costPrice: item.cost_price,
          costPriceText: `${item.cost_currency} ${formatThousands(item.cost_price)}`,
          retailPrice: item.retail_price,
          retailPriceText: `${item.selling_currency} ${formatThousands(item.retail_price)}`,
          inventory:
            item.inventory >= MAX_INVENTORY
              ? this.$t('package_inventory_unlimited')
              : formatThousands(item.inventory),
          cutoffTime:
            parseInt(item.cut_off_time / (60 * 60 * 24)) +
            ` ${this.$t('global_day')} ` +
            addZero(parseInt((item.cut_off_time / (60 * 60)) % 24)) +
            ':' +
            addZero(parseInt((item.cut_off_time / 60) % 60))
        }

        if (this.isAdmin) {
          result.sellingPrice = item.selling_price
          result.sellingPriceText = `${item.selling_currency} ${formatThousands(item.selling_price)}`
          result.takeRate = `${item.take_rate}%`
          result.specialSellingPrice = item.custom_price
        }

        return result
      })
    },
    costPriceList() {
      const list = []
      this.transformedData.forEach((item) => {
        const { costPrice } = item

        if (!list.includes(costPrice)) {
          list.push(costPrice)
        }
      })

      return list
    },
    costPriceFilteredData() {
      // 筛选价格状态
      return this.isFilteredPrice
        ? this.transformedData.filter((item) => item.costPrice === this.isFilteredPrice)
        : this.transformedData
    },
    tableData() {
      let list = [...this.costPriceFilteredData]

      // 筛选出选中的
      if (this.isFilterSelected) {
        list = list.filter((item) => this.selectedRowKeys.includes(item.id))
      }

      // frozen的时候要disable
      if (this.isFrozen) {
        list.forEach((item) => (item.disabled = true))
      } else {
        list.forEach((item) => (item.disabled = false))
      }

      return list
    },
    rowSelection() {
      return {
        fixed: true,
        columnWidth: 40,
        hideDefaultSelections: true,
        selectedRowKeys: this.selectedRowKeys,
        getCheckboxProps: (record) => {
          return {
            props: {
              name: record.key,
              disabled: record.disabled
            }
          }
        },
        onChange: (selectedRowKeys) => {
          this.selectedRowKeys = selectedRowKeys

          // if (this.selectedRowKeys.length === 0) {
          //   this.selectedPrice = null
          // }
        }
        // onSelect: (record, selected) => {
        //   if (this.isAdmin && selected) {
        //     this.selectedPrice = record.costPrice
        //   } else {
        //     this.selectedPrice = null
        //   }
        // }
      }
    },
    showPagination() {
      return this.tableData.length > 20
    },
    total() {
      return this.costPriceFilteredData.length
    },
    selected() {
      return this.selectedRowKeys.length
    },
    checkAll() {
      return this.selectedRowKeys.length > 0 && this.selectedRowKeys.length === this.total
    },
    indeterminate() {
      return this.selectedRowKeys.length > 0 && this.selectedRowKeys.length !== this.total
    },
    disableDelete() {
      // merchant 且 approved 且有 编辑库存权限或编辑权限 的时候开放
      const isMerchantAndApproved =
        this.isMerchant && this.isPkgApproved && (checkAuth('editInventory') || !this.$root.nonEdit2status)

      return (
        this.selectedRowKeys.length === 0 ||
        this.isBdAudit ||
        this.isAdminEm ||
        (this.$root.nonEdit2act && !isMerchantAndApproved)
      )
    }
  },
  watch: {
    selectedRowKeys(val) {
      this.$emit('selectedRowKeys', val)
    },
    isFrozen(val) {
      // 在frozen的时候要全选
      if (val) {
        this.selectedRowKeys = this.transformedData.map((item) => item.id)
        this.isFilterSelected = false
      }
    }
  },
  methods: {
    checkGrey(field) {
      return this.highlightList && !this.highlightList.includes(field)
    },
    async handleDelete() {
      const res = await ajax.postBody(ADMIN_API.act.destroy_schedule, {
        data: {
          package_id: this.packageId,
          schedule_id_list: this.selectedRowKeys
        }
      })

      if (res.success) {
        this.$message.success(this.$t('global_delete_success'))
        this.bulkModification.update()
        this.reset()
      } else {
        this.$message.error(this.$t('global_delete_error'))
      }
    },
    handleCheckAll() {
      this.selectedRowKeys = this.checkAll ? [] : this.costPriceFilteredData.map((item) => item.id)
    },
    handleSizeChange(current, pageSize) {
      this.pagination.pageSize = pageSize
    },
    handlePageChange(pagination) {
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
    },
    handlePriceSelectorReset() {
      this.isPriceSelectorVisible = false
      this.priceSelectorValue = null
      this.isFilteredPrice = null
    },
    openFilter() {
      this.isPriceSelectorVisible = true
    },
    handlePriceSelectorOk() {
      if (this.priceSelectorValue) {
        // 先重置
        this.isFilterSelected = false
        this.selectedRowKeys = []
        this.isFilteredPrice = this.priceSelectorValue
      }

      this.isPriceSelectorVisible = false
    },
    reset() {
      this.selectedRowKeys = []
      this.isFilterSelected = false
    }
  }
}
</script>

<style lang="scss">
.bulk-modification-table {
  &.hide-checkall {
    .ant-table-header-column .ant-checkbox-wrapper {
      display: none;
    }
  }

  .number-btn:not(:disabled) span {
    text-decoration: underline;
  }

  &.is-admin {
    .ant-table td {
      white-space: nowrap;
    }
  }
}
</style>

<style lang="scss" scoped>
.bulk-modification-table {
  &_header {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .overflow-text {
    width: 130px;
    display: inline-block;
    vertical-align: middle;
  }

  .filter-btn {
    margin-left: 8px;
    width: 16px;
    height: 16px;
    color: transparent;
    vertical-align: middle;
  }

  .grey-text {
    color: rgba(0, 0, 0, 0.3);
  }
}
</style>
