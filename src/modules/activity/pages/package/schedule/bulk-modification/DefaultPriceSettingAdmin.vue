<template>
  <a-modal
    :title="$t('package_bulk_modification_default_price_setting')"
    :visible="visible"
    :width="700"
    :ok-text="$t('global_button_ok')"
    :cancel-text="$t('global_button_cancel')"
    :confirmLoading="saveLoading"
    :okButtonProps="{ props: { disabled: isAdminEm } }"
    @ok="modifyDefaultPrice"
    @cancel="visible = false"
  >
    <GeneralAlert
      :show-icon="false"
      :message="$t('package_price_default_setting_warning')"
      banner
    />

    <div style="margin-top: 20px;">
      <label style="margin-right: 6px; padding-left: 15px;">Unit :</label>
      <BasicUnitSelector v-model="selectedUnit" @change="handleUnitChange" />
    </div>

    <TakeRateTips
      style="margin: 20px 0;"
      :override-take-rate="overrideTakeRate"
      :last-take-rate="lastTakeRate"
    />

    <a-spin :spinning="loading">
      <BasicTakeRateForm
        ref="form"
        :form="form"
        :selling-currency="sellingCurrency"
        :cost-currency="costCurrency"
        :exchange-rate="exchangeRate"
        :isPriceProtected="isPriceProtected"
      />
    </a-spin>
  </a-modal>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { computeTR } from '@activity/utils'
import GeneralAlert from '@activity/pages/components/GeneralAlert.vue'
import TakeRateTips from '@activity/pages/components/TakeRateTips.vue'
import BasicUnitSelector from '@activity/pages/components/BasicUnitSelector.vue'
import BasicTakeRateForm from '@activity/pages/components/BasicTakeRateForm.vue'
import { warn_message } from '@activity/pages/price/utils'

export default {
  name: 'DefaultPriceSettingAdmin',
  components: {
    TakeRateTips,
    GeneralAlert,
    BasicUnitSelector,
    BasicTakeRateForm
  },
  inject: ['bulkModification'],
  props: {
    isPriceProtected: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      saveLoading: false,

      selectedUnit: null,
      costCurrency: '',
      sellingCurrency: '',

      overrideTakeRate: '0',
      lastTakeRate: '0',
      exchangeRate: '',
      form: {
        sellingPrice: '',
        costPrice: '',
        retailPrice: '',
        takeRate: '',
        specialSellingPriceList: []
      }
    }
  },
  computed: {
    ...mapState({
      isBdAudit: (state) => state.isBdAudit,
      isAdminEm: (state) => state.isAdminEm
    }),
    ...mapGetters(['unitList'])
  },
  methods: {
    open(skuId) {
      this.visible = true
      this.selectedUnit = skuId
      this.getDefaultPrice()
      this.$refs.form && this.$refs.form.clearValidate()
    },
    handleUnitChange(skuId) {
      this.getDefaultPrice()
    },
    async getDefaultPrice() {
      this.loading = true

      const res = await ajax.get(ADMIN_API.act.get_calendar_basic_setting, {
        params: {
          sku_id: this.selectedUnit
        }
      })

      this.costCurrency = res.cost_currency
      this.sellingCurrency = res.selling_currency

      await this.getExchangeRate()

      this.overrideTakeRate = res.override_take_rate || '0'
      this.lastTakeRate = res.last_take_rate || '0'
      this.form = {
        costPrice: res.cost_price,
        sellingPrice: res.selling_price,
        retailPrice: res.retail_price,
        takeRate: computeTR(
          res.cost_price,
          res.selling_price,
          this.exchangeRate
        ),
        specialSellingPriceList: res.special_selling_price
      }

      this.loading = false
    },
    async getExchangeRate() {
      const res = await ajax.get(ADMIN_API.act.get_exchange_rate, {
        params: {
          currency_from: this.costCurrency,
          currency_to_list: this.sellingCurrency
        }
      })
      this.exchangeRate = res[0].to_value
    },
    async modifyDefaultPrice() {
      const valid = await this.$refs.form.validateForm()

      if (!valid) {
        return
      }

      this.saveLoading = true
      const specialSellingPriceData = this.$refs.form.getSpecialSellingPriceData()
      const apiUrl = this.isBdAudit
        ? ADMIN_API.act.audit_update_calendar_basic_setting
        : ADMIN_API.act.update_calendar_basic_setting
      const res = await ajax.postBody(apiUrl, {
        data: {
          sku_id: this.selectedUnit,
          note: '',
          selling_currency: this.sellingCurrency,
          cost_currency: this.costCurrency,
          selling_price: Number(this.form.sellingPrice),
          cost_price: Number(this.form.costPrice),
          retail_price: Number(this.form.retailPrice),
          special_selling_price: specialSellingPriceData.map((item) => ({
            ...item,
            price: Number(item.price)
          }))
        }
      })
      this.saveLoading = false

      if (res.success) {
        this.visible = false
        this.bulkModification.update()
        this.$message.success(this.$t('global_modify_success'))
        await warn_message(this, res.result)
      } else {
        this.$message.error(this.$t('global_modify_error'))
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
