<template>
  <a-modal
    :title="$t('package_bulk_modification_default_price_setting')"
    :visible="visible"
    :width="640"
    :ok-text="$t('global_button_ok')"
    :cancel-text="$t('global_button_cancel')"
    :confirmLoading="saveLoading"
    @ok="modifyDefaultPrice"
    @cancel="visible = false"
  >
    <GeneralAlert
      :show-icon="false"
      :message="$t('package_price_default_setting_warning')"
      banner
    />

    <div style="margin-top: 20px;">
      <label style="margin-right: 6px; padding-left: 15px;">Unit :</label>
      <BasicUnitSelector v-model="selectedUnit" @change="handleUnitChange" />
    </div>

    <a-spin :spinning="loading">
      <BasicPriceForm
        ref="form"
        is-horizontal
        :form="form"
        :cost-currency="costCurrency"
        :selling-currency="sellingCurrency"
      />
    </a-spin>
  </a-modal>
</template>

<script>
import { mapGetters } from 'vuex'
import GeneralAlert from '@activity/pages/components/GeneralAlert.vue'
import BasicUnitSelector from '@activity/pages/components/BasicUnitSelector.vue'
import BasicPriceForm from '@activity/pages/components/BasicPriceForm.vue'
import { warn_message } from '@activity/pages/price/utils'

export default {
  name: 'DefaultPriceSettingMerchant',
  components: {
    GeneralAlert,
    BasicUnitSelector,
    BasicPriceForm
  },
  inject: ['bulkModification'],
  data() {
    return {
      visible: false,
      loading: false,
      saveLoading: false,

      selectedUnit: null,
      costCurrency: 'VND',
      sellingCurrency: 'VND',

      form: {
        costPrice: '',
        retailPrice: ''
      }
    }
  },
  computed: {
    ...mapGetters(['unitList'])
  },
  methods: {
    open(skuId) {
      this.visible = true
      this.selectedUnit = skuId
      this.getDefaultPrice()
      this.$refs.form && this.$refs.form.clearValidate()
    },
    handleUnitChange(skuId) {
      this.getDefaultPrice()
    },
    async getDefaultPrice() {
      this.loading = true

      const res = await ajax.get(ADMIN_API.act.get_calendar_basic_setting, {
        params: {
          sku_id: this.selectedUnit
        }
      })

      this.costCurrency = res.cost_currency
      this.sellingCurrency = res.selling_currency
      this.form = {
        costPrice: res.cost_price,
        retailPrice: res.retail_price
      }

      this.loading = false
    },
    async getExchangeRate() {
      const res = await ajax.get(ADMIN_API.act.get_exchange_rate, {
        params: {
          currency_from: this.costCurrency,
          currency_to_list: this.sellingCurrency
        }
      })
      this.exchangeRate = res[0].to_value
    },
    async modifyDefaultPrice() {
      const valid = await this.$refs.form.validateForm()

      if (!valid) {
        return
      }

      this.saveLoading = true
      const res = await ajax.postBody(
        ADMIN_API.act.update_calendar_basic_setting,
        {
          data: {
            sku_id: this.selectedUnit,
            cost_currency: this.costCurrency,
            selling_currency: this.sellingCurrency,
            cost_price: Number(this.form.costPrice),
            retail_price: Number(this.form.retailPrice)
          }
        }
      )
      this.saveLoading = false

      if (res.success) {
        this.visible = false
        this.bulkModification.update()
        this.$message.success(this.$t('global_modify_success'))
        await warn_message(this, res.result)
      } else {
        this.$message.error(this.$t('global_modify_error'))
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
