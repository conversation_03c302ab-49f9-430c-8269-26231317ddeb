<template>
  <a-form-model ref="form" layout="inline" :model="form">
    <a-form-model-item
      :label="$t('package_cut_off_time')"
      prop="cutoffType"
      required
    >
      <a-select
        v-model="form.cutoffType"
        style="width: 200px;margin-right: 12px;"
      >
        <a-select-option
          v-for="cutoffOption in cutoffTypeList"
          :key="cutoffOption.value"
          :value="cutoffOption.value"
        >
          {{ cutoffOption.text }}
        </a-select-option>
      </a-select>
      <a-time-picker
        :value="getTimeslot(form.cutoffTime)"
        format="HH:mm"
        style="width: 200px;margin-bottom: 0;"
        @change="(time, timeString) => (form.cutoffTime = timeString)"
      />
    </a-form-model-item>
  </a-form-model>
</template>

<script>
import { cutoffTypeList } from '@activity/pages/package/package_const.js'

export default {
  name: 'CutoffForm',
  props: {
    form: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      cutoffTypeList
    }
  },
  methods: {
    getTimeslot(str) {
      return str ? moment(str, 'HH:mm') : ''
    },
    validate() {
      return this.$refs.form.validate().catch(() => false)
    }
  }
}
</script>

<style lang="scss" scoped></style>
