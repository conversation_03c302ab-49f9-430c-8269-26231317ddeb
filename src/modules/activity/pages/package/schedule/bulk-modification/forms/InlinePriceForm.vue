<template>
  <a-form-model ref="form" layout="inline" :model="form" :rules="rules">
    <a-form-model-item
      :label="$t('package_price_cost')"
      style="margin-right: 60px;"
      prop="costPrice"
      :colon="false"
    >
      <a-input
        v-model="form.costPrice"
        :suffix="costCurrency"
        placeholder="Please input"
        @keyup.native="handleRound('costPrice')"
        @blur="handleRound('costPrice')"
      />
    </a-form-model-item>
    <a-form-model-item
      :label="$t('package_price_retail')"
      prop="retailPrice"
      :colon="false"
    >
      <a-input
        v-model="form.retailPrice"
        :suffix="sellingCurrency"
        placeholder="Please input"
        @keyup.native="handleRound('retailPrice')"
        @blur="handleRound('retailPrice')"
      />
    </a-form-model-item>
  </a-form-model>
</template>

<script>
import { round2Decimal } from '@activity/utils'

export default {
  name: 'InlinePriceForm',
  props: {
    form: {
      type: Object,
      default: null
    },
    costCurrency: {
      type: String,
      default: ''
    },
    sellingCurrency: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      rules: {
        costPrice: [
          {
            required: true,
            message: __('global_please_input'),
            trigger: 'blur'
          }
        ],
        retailPrice: [
          {
            required: true,
            message: __('global_please_input'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    handleRound(field) {
      this.form[field] = round2Decimal(this.form[field])
    },
    validate() {
      return this.$refs.form.validate().catch(() => false)
    }
  }
}
</script>

<style lang="scss" scoped></style>
