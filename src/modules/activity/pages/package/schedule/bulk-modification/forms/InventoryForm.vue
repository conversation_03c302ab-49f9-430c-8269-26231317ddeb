<template>
  <a-form-model
    v-if="form"
    ref="form"
    layout="inline"
    :model="form"
    :rules="rules"
  >
    <a-form-model-item
      :label="$t('package_inventory')"
      prop="isLimited"
      required
    >
      <a-radio-group v-model="form.isLimited">
        <a-radio :value="false" :style="{ lineHeight: '33px' }">
          {{ $t('package_inventory_unlimited') }}
        </a-radio>
        <a-radio :value="true" :style="{ lineHeight: '33px' }">
          {{ $t('package_inventory_limited_per_day') }}
        </a-radio>
      </a-radio-group>
      <a-input-number
        v-model="form.inventory"
        style="width: 200px;"
        :max="MAX_INVENTORY - 1"
      />
    </a-form-model-item>
  </a-form-model>
</template>

<script>
import { MAX_INVENTORY } from '@activity/pages/package/package_const.js'

export default {
  name: 'InventoryForm',
  props: {
    form: {
      type: Object,
      default: null
    }
  },
  data() {
    const validateInventory = (rule, value, callback) => {
      const { isLimited, inventory } = this.form
      if (isLimited && !inventory && ![0, '0'].includes(inventory)) {
        callback(new Error(this.$t('global_please_input')))
      } else {
        callback()
      }
    }

    return {
      MAX_INVENTORY,
      rules: {
        isLimited: [
          { validator: validateInventory, trigger: 'blur' },
          {
            required: true,
            message: __('global_please_select'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    validate() {
      return this.$refs.form.validate().catch(() => false)
    }
  }
}
</script>

<style lang="scss" scoped></style>
