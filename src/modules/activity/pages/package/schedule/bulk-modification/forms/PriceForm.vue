<template>
  <a-form-model v-if="form" ref="form" layout="horizontal" :model="form" :rules="rules">
    <div v-if="['admin'].includes(type)" class="form-wrapper">
      <div style="width: 40%">
        <a-form-model-item
          :label="$t('package_price_cost')"
          prop="costPrice"
          :labelCol="{ span: 10 }"
          :wrapperCol="{ span: 13 }"
        >
          <a-input
            v-model="form.costPrice"
            :suffix="costCurrency"
            :placeholder="$t('global_please_input')"
            :disabled="isTripMapped"
            @keyup.native="handleRound('costPrice')"
            @blur="handleCostPriceBlur"
          />
        </a-form-model-item>
      </div>
      <div style="width: 40%">
        <a-form-model-item
          :label="$t('package_price_retail')"
          prop="retailPrice"
          :labelCol="{ span: 10 }"
          :wrapperCol="{ span: 13 }"
        >
          <a-input
            v-model="form.retailPrice"
            :suffix="sellingCurrency"
            :placeholder="$t('global_please_input')"
            @keyup.native="handleRound('retailPrice')"
          />
        </a-form-model-item>
      </div>
    </div>
    <div v-if="['bd', 'admin'].includes(type)" class="form-wrapper">
      <div style="width: 40%">
        <a-form-model-item
          :label="$t('package_price_selling')"
          prop="sellingPrice"
          :labelCol="{ span: 10 }"
          :wrapperCol="{ span: 13 }"
        >
          <a-input
            v-model="form.sellingPrice"
            :suffix="sellingCurrency"
            :placeholder="$t('global_please_input')"
            :disabled="isPriceProtected"
            @keyup.native="handleRound('sellingPrice')"
            @blur="computeTakeRate"
          />
        </a-form-model-item>
      </div>
      <div style="width: 40%">
        <a-form-model-item
          :label="$t('new_take_rate')"
          prop="takeRate"
          :labelCol="{ span: 10 }"
          :wrapperCol="{ span: 13 }"
        >
          <a-input
            v-model="form.takeRate"
            suffix="%"
            :placeholder="$t('global_please_input')"
            :disabled="isPriceProtected"
            @keyup.native="handleRoundTakeRate"
            @blur="handleTakeRateBlur"
          />
        </a-form-model-item>
      </div>
    </div>
    <div style="width: 80%;">
      <a-form-model-item
        v-if="['bd', 'admin'].includes(type)"
        :label="$t('package_custom_selling')"
        prop="specialSellingPriceList"
        :labelCol="{ span: 5 }"
        :wrapperCol="{ span: 19 }"
      >
        <SpecialSellingPriceList
          ref="ssp"
          :price="form.sellingPrice"
          :disabled="isPriceProtected"
          :selling-currency="sellingCurrency"
          :special-selling-price-list="form.specialSellingPriceList"
        />
      </a-form-model-item>
    </div>
    <div style="width: 80%;">
      <a-form-model-item
        v-if="['bd', 'admin'].includes(type)"
        :label="$t('reason_for_price_changing')"
        prop="reason"
        :labelCol="{ span: 5 }"
        :wrapperCol="{ span: 19 }"
      >
        <a-select v-model="form.reason" style="width: 240px;" @change="handleReasonSelect">
          <a-select-option v-for="reason in priceChangeReasonList" :key="reason.value" :value="reason.value">
            {{ reason.text }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
    </div>
    <div style="width: 80%;">
      <a-form-model-item
        v-if="['bd', 'admin'].includes(type) && form.reason === 0"
        label=" "
        prop="reasonText"
        :colon="false"
        :labelCol="{ span: 5 }"
        :wrapperCol="{ span: 19 }"
      >
        <a-textarea
          id="js-reason-text"
          v-model="form.reasonText"
          :placeholder="$t('global_please_input')"
          :rows="4"
          :max-length="100"
        />
      </a-form-model-item>
    </div>
  </a-form-model>
</template>

<script>
import { mapGetters } from 'vuex'
import {
  round2Decimal,
  provingTakeRate,
  computeTR,
  setTRAndGetBase,
  setCostAndGetBase
} from '@activity/utils'
import { priceChangeReasonList } from '@activity/pages/package/package_const.js'
import SpecialSellingPriceList from '@activity/pages/components/SpecialSellingPriceList.vue'

export default {
  name: 'PriceForm',
  components: {
    SpecialSellingPriceList
  },
  props: {
    form: {
      type: Object,
      default: null
    },
    costCurrency: {
      type: String,
      default: ''
    },
    sellingCurrency: {
      type: String,
      default: ''
    },
    exchangeRate: {
      type: [String, Number],
      default: ''
    },
    // 可选值为：admin、bd
    type: {
      type: String,
      default: 'bd'
    },
    isPriceProtected: {
      type: Boolean,
      defualt: false
    }
  },
  data() {
    const validateSpecialSellingPriceList = (rule, value, callback) => {
      const isValid = value.every((item) => !!item.price)

      if (isValid) {
        callback()
      } else {
        callback(new Error(this.$t('global_please_input')))
      }
    }

    return {
      priceChangeReasonList,

      rules: {
        costPrice: [
          {
            required: true,
            message: __('global_please_input'),
            trigger: 'blur'
          }
        ],
        retailPrice: [
          {
            required: true,
            message: __('global_please_input'),
            trigger: 'blur'
          }
        ],
        sellingPrice: [
          {
            required: true,
            message: __('global_please_input'),
            trigger: 'blur'
          }
        ],
        takeRate: [
          {
            required: true,
            message: __('global_please_input'),
            trigger: 'blur'
          }
        ],
        specialSellingPriceList: [
          {
            validator: validateSpecialSellingPriceList,
            trigger: 'blur'
          }
        ],
        reason: [
          {
            required: true,
            message: __('global_please_select'),
            trigger: 'blur'
          }
        ],
        reasonText: [
          {
            required: true,
            message: __('global_please_input'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['isTripMapped'])
  },
  methods: {
    // 这里约定，form和ssp都存在才会调用这个方法
    validate() {
      return this.$refs.form.validate().catch(() => false)
    },
    getSpecialSellingPriceData() {
      return this.$refs.ssp.getSspData()
    },
    handleRound(field) {
      this.form[field] = round2Decimal(this.form[field])
    },
    handleCostPriceBlur() {
      const res = setCostAndGetBase(
        this.form.costPrice,
        this.form.sellingPrice,
        this.form.takeRate,
        this.exchangeRate
      )

      this.form.costPrice = res.cost
      this.form.sellingPrice = res.base
      this.form.takeRate = res.take_rate
    },
    computeTakeRate() {
      this.form.takeRate = computeTR(this.form.costPrice, this.form.sellingPrice, this.exchangeRate)
    },
    handleTakeRateBlur() {
      const res = setTRAndGetBase(
        this.form.costPrice,
        this.form.sellingPrice,
        this.form.takeRate,
        this.exchangeRate
      )

      this.form.sellingPrice = res.base
      this.form.takeRate = res.take_rate
    },
    handleRoundTakeRate() {
      this.form.takeRate = provingTakeRate(this.form.takeRate)
    },
    handleReasonSelect() {
      if (this.form.reason === 0) {
        this.$nextTick(() => {
          document.querySelector('#js-reason-text').scrollIntoView()
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.form-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
</style>
