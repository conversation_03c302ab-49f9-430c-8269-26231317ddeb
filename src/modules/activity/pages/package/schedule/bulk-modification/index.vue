<template>
  <div class="bulk-modification" :style="{ 'margin-bottom': bottomMargin }">
    <div class="bulk-modification_unit">
      <label style="margin-right: 6px;">{{
        $t('package_unit_selector')
      }}</label>
      <BasicUnitSelector
        disableUnpublished
        v-model="selectedUnit"
        @change="handleUnitChange"
      />
    </div>
    <a-form-model
      class="bulk-modification_search"
      ref="form"
      layout="horizontal"
      label-align="left"
      :model="form"
      :rules="rules"
      :label-col="{ span: 3 }"
      :wrapper-col="{ span: 21 }"
    >
      <a-form-model-item
        :label="$t('package_timeslot_date_range')"
        prop="dateRange"
        style="margin-bottom: 8px;"
      >
        <a-range-picker
          v-model="form.dateRange"
          size="default"
          style="width: 260px;"
          :placeholder="[
            $t('package_start_date'),
            $t('package_schedule_end_date')
          ]"
          :disabled-date="disabledDateTime"
          valueFormat="YYYY-MM-DD"
          @openChange="handleOpenChange"
          @calendarChange="handleCalendarChange"
        >
          <a-icon slot="suffixIcon" type="calendar" />
        </a-range-picker>
      </a-form-model-item>
      <a-form-model-item
        :label="$t('package_timeslot')"
        prop="timeslotType"
        style="margin-bottom: 8px;"
      >
        <a-radio-group v-model="form.timeslotType">
          <a-radio value="ALL">
            {{ $t('global_button_all') }}
          </a-radio>
          <a-radio value="DAY">
            {{ $t('package_timeslot_by_day') }}
          </a-radio>
          <a-radio value="TIME">
            <a-time-picker
              :value="getTimeslot(form.timeslot)"
              format="HH:mm"
              style="width: 130px;"
              @change="(time, timeString) => (form.timeslot = timeString)"
            />
          </a-radio>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item
        :label="$t('timeslotlist_weekly')"
        prop="weekdays"
        style="margin-bottom: 8px;"
      >
        <a-checkbox
          :indeterminate="weekdayIndeterminate"
          :checked="weekdayCheckAll"
          style="margin: 0;"
          @change="handleWeekdayCheckAllChange"
        >
          {{ $t('global_button_all') }}
        </a-checkbox>
        <a-checkbox-group
          v-model="form.weekdays"
          @change="handleWeekdaysChange"
        >
          <a-checkbox
            v-for="(week, index) in weekList"
            style="margin: 0;"
            :key="week"
            :value="1 + index"
          >
            {{ $t(week) }}
          </a-checkbox>
        </a-checkbox-group>
      </a-form-model-item>
      <a-form-model-item style="margin-bottom: 0;">
        <a-button type="primary" @click="handleSearch">
          {{ $t('package_price_modify_search') }}
        </a-button>
        <a-button type="link" @click="handleReset">
          {{ $t('package_price_modify_reset') }}
        </a-button>
      </a-form-model-item>
    </a-form-model>

    <BulkModificationTable
      ref="bulkModificationTable"
      class="bulk-modification_table"
      :data="data"
      :loading="loading"
      :highlight-list="highlightList"
      :is-frozen="isFrozen"
      @selectedRowKeys="handleSelectedRowKeys"
      @reset="reset"
    />

    <component
      ref="bulkModificationFooter"
      :is="
        isAdmin
          ? 'BulkModificationFooterAdmin'
          : 'BulkModificationFooterMerchant'
      "
      :data="data"
      :selected-unit="selectedUnit"
      :selected-row-keys="selectedRowKeys"
      @selectUnit="handleSelectUnit"
      @toggleFrozen="toggleFrozen"
      @modifyHighlightList="modifyHighlightList"
      @openFilter="openTableFilter"
      @setMargin="setMargin"
    />
  </div>
</template>

<script>
import { isAdmin } from '@/env'
import { mapState, mapGetters } from 'vuex'
import { weekList } from '@activity/pages/package/package_const.js'
import { getTimeOffset } from '@activity/utils'
import BasicUnitSelector from '@activity/pages/components/BasicUnitSelector.vue'
import BulkModificationTable from './BulkModificationTable.vue'

export default {
  name: 'BulkModification',
  components: {
    BasicUnitSelector,
    BulkModificationTable,
    BulkModificationFooterAdmin: () =>
      import(
        /* webpackChunkName: "BulkModificationFooterAdmin" */ './BulkModificationFooterAdmin'
      ),
    BulkModificationFooterMerchant: () =>
      import(
        /* webpackChunkName: "BulkModificationFooterMerchant" */ './BulkModificationFooterMerchant'
      )
  },
  inject: ['packageId'],
  provide() {
    return {
      bulkModification: this
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    defaultSetting: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    const validateDateRange = (rule, value, callback) => {
      if (!value || !value[0] || !value[1]) {
        callback(new Error(this.$t('global_please_select')))
      } else {
        callback()
      }
    }
    const validateTimeslot = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('global_please_select')))
      } else if (value === 'TIME' && !this.form.timeslot) {
        callback(new Error(this.$t('global_please_input')))
      } else {
        callback()
      }
    }
    const validateWeekdays = (rule, value, callback) => {
      if (!value || value.length === 0) {
        callback(new Error(this.$t('global_please_select')))
      } else {
        callback()
      }
    }

    return {
      isAdmin,
      weekList,
      defaultWeekdays: [1, 2, 3, 4, 5, 6, 7],

      data: {},
      loading: false,
      selectedUnit: null,
      form: {
        dateRange: [null, null],
        timeslotType: 'ALL',
        timeslot: '00:00',
        weekdays: []
      },
      rules: {
        dateRange: [{ validator: validateDateRange, trigger: 'blur' }],
        timeslotType: [{ validator: validateTimeslot, trigger: 'blur' }],
        weekdays: [{ validator: validateWeekdays, trigger: 'blur' }]
      },
      weekdayIndeterminate: false,
      weekdayCheckAll: true,
      startDate: null,

      bottomMargin: 0,
      highlightList: null,
      selectedRowKeys: [], // 这里的改变不会引发table里面selectedRowKeys的改变
      isFrozen: false
      // isInited: false
    }
  },
  computed: {
    ...mapState({
      calendarDate: (state) => state.calendarDate,
      calendarUnit: (state) => state.calendarUnit,
      destinationToday: (state) => state.destinationToday
    }),
    ...mapGetters(['unitList']),
    defaultDateRange() {
      // 逻辑为当前价格日历的那个月，如果有过期的日期则减掉
      let start = moment(this.calendarDate)
        .startOf('month')
        .format('YYYY-MM-DD')
      let end = moment(this.calendarDate)
        .endOf('month')
        .format('YYYY-MM-DD')

      if (moment(start).diff(this.destinationToday, 'days') < 0) {
        start = moment(this.destinationToday).format('YYYY-MM-DD')
      }

      if (moment(end).diff(this.destinationToday, 'days') < 0) {
        end = moment(this.destinationToday).format('YYYY-MM-DD')
      }

      return [start, end]
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        if (val) {
          this.init()
        } else {
          this.reset()
        }
      }
    }
  },
  methods: {
    init() {
      // if (this.isInited && Object.keys(this.defaultSetting).length === 0) {
      //   return
      // }

      this.selectedUnit = this.calendarUnit

      this.form = {
        dateRange: this.defaultSetting.dateRange || this.defaultDateRange,
        timeslotType: this.defaultSetting.timeslotType || 'ALL',
        timeslot: this.defaultSetting.timeslot || '00:00',
        weekdays: this.defaultSetting.weekdays || [...this.defaultWeekdays]
      }

      this.handleWeekdaysChange()
      this.getCalendarData()
      // this.isInited = true

      // 这里的交互很奇怪，只有在点击搜索和从日历组件进来才会reset
      this.$refs.bulkModificationTable &&
        this.$refs.bulkModificationTable.reset()
    },
    update() {
      this.getCalendarData()
      // 数据is dirty,外部数据需要更新
      this.$emit('update')
    },
    handleUnitChange() {
      this.getCalendarData()
    },
    handleCalendarChange(val) {
      this.startDate = val[0]
    },
    handleOpenChange() {
      this.startDate = null
    },
    disabledDateTime(current) {
      // 只能选目的地当天以后的数据
      if (current.isBefore(this.destinationToday, 'day')) {
        return true
      }

      // 间隔不能大于3个月
      if (this.startDate && current.diff(this.startDate, 'month') >= 3) {
        return true
      }

      if (this.startDate && current.diff(this.startDate, 'month') <= -3) {
        return true
      }

      return false
    },
    getTimeslot(str) {
      return str ? moment(str, 'HH:mm') : ''
    },
    handleWeekdayCheckAllChange() {
      this.weekdayCheckAll = !this.weekdayCheckAll
      this.weekdayIndeterminate = false
      this.form.weekdays = this.weekdayCheckAll ? [...this.defaultWeekdays] : []
    },
    handleWeekdaysChange() {
      if (
        this.form.weekdays
          .slice()
          .sort()
          .join('') === this.defaultWeekdays.join('')
      ) {
        this.weekdayCheckAll = true
        this.weekdayIndeterminate = false
      } else if (this.form.weekdays.length > 0) {
        this.weekdayCheckAll = false
        this.weekdayIndeterminate = true
      } else {
        this.weekdayCheckAll = false
        this.weekdayIndeterminate = false
      }
    },
    async getTakeRate() {
      this.loading = true
    },
    async getCalendarData() {
      this.loading = true
      this.data =
        (await ajax.post(ADMIN_API.act.query_sku_price_inventory_calendar, {
          data: {
            package_id: this.packageId,
            sku_id: this.selectedUnit,
            time_point:
              this.form.timeslotType === 'ALL'
                ? -1
                : this.form.timeslotType === 'DAY'
                ? 0
                : getTimeOffset(this.form.timeslot),
            start_date: this.form.dateRange[0],
            end_date: this.form.dateRange[1],
            weekly: this.form.weekdays,
            is_page_ing: false
          }
        })) || {}
      this.$refs.bulkModificationFooter &&
        this.$refs.bulkModificationFooter.reset()
      this.loading = false
    },
    debounceGetCalendarData: _.debounce(async function() {
      await this.getCalendarData()
    }, 300),
    async handleSearch() {
      const valid = await this.$refs.form.validate().catch(() => false)

      if (!valid) {
        return
      }

      await this.debounceGetCalendarData()

      // 这里的交互很奇怪，只有在点击搜索和从日历组件进来才会reset
      this.$refs.bulkModificationTable &&
        this.$refs.bulkModificationTable.reset()
    },
    handleReset() {
      this.form.dateRange = [null, null]
      this.form.timeslotType = 'ALL'
      this.form.timeslot = '00:00'
      this.form.weekdays = [...this.defaultWeekdays]
      this.handleWeekdaysChange()
    },
    handleSelectUnit(skuId) {
      this.selectedUnit = skuId
      this.getCalendarData()
    },
    modifyHighlightList(list) {
      this.highlightList = list ? [...list] : list
    },
    reset() {
      this.$refs.bulkModificationTable &&
        this.$refs.bulkModificationTable.reset()
      this.$refs.bulkModificationFooter &&
        this.$refs.bulkModificationFooter.reset()
    },
    handleSelectedRowKeys(val) {
      this.selectedRowKeys = [...val]
    },
    toggleFrozen(val) {
      this.isFrozen = val
    },
    openTableFilter() {
      this.$refs.bulkModificationTable &&
        this.$refs.bulkModificationTable.openFilter()
    },
    setMargin(val) {
      this.bottomMargin = `${val > 50 ? val - 50 : val}px`
    }
  }
}
</script>

<style lang="scss">
.bulk-modification {
  .ant-checkbox-wrapper .ant-checkbox + span {
    padding-left: 2px;
  }
}
</style>

<style lang="scss" scoped>
.bulk-modification {
  // padding-bottom: 80px;

  label {
    margin-right: 6px;
  }

  &_search {
    margin-top: 24px;
    padding: 12px;
    background-color: #fafafa;
    border-radius: 4px;

    &-row {
      margin-top: 8px;

      &:first-child {
        margin-top: 0;
      }
    }
  }

  &_table {
    margin-top: 24px;
  }
}
</style>
