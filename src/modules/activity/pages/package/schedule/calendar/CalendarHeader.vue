<template>
  <div class="calendar-header">
    <div v-if="unitList.length > 0" class="calendar-header-select">
      <a-form-model layout="inline">
        <a-form-model-item :label="$t('package_unit_selector')">
          <BasicUnitSelector shadow-unpublished :value="calendarUnit" @change="handleUnitChange" />
        </a-form-model-item>
        <a-form-model-item :label="$t('package_timeslot_selector')">
          <a-select
            show-search
            :value="selectedTimeslot"
            mode="multiple"
            style="width: 300px"
            :maxTagCount="2"
            :placeholder="$t('global_please_select')"
            @change="handleTimeslotChange"
          >
            <div slot="maxTagPlaceholder">
              ...
            </div>
            <a-select-option key="ALL" value="ALL">
              {{ $t('global_button_all') }}
            </a-select-option>
            <a-select-option v-for="timeslot in timeslotList" :key="timeslot" :value="timeslot">
              {{ timeslot === '00:00' ? $t('package_timeslot_by_day') : timeslot }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </div>
    <div class="calendar-header-year">
      <div class="calendar-header-year_legend">
        <div
          v-for="(color, index) in legendColors"
          :key="color.label"
          class="calendar-header-year_legend-item"
          :class="{ disabled: color.disabled }"
          @click="handleLegendClick(index)"
        >
          <!-- <i
            class="calendar-header-year_legend-item_paint"
            :style="{
              'background-color': color.disabled
                ? 'rgba(0, 0, 0, 0.45)'
                : color.color
            }"
          /> -->
          <svg-icon class="calendar-header-year_legend-item_icon" :icon-name="color.icon" />
          <span class="calendar-header-year_legend-item_text" :style="{ color: color.color }">
            {{ color.label }}
          </span>
        </div>
      </div>
      <div class="calendar-header-year_select">
        <a-icon type="double-left" class="mr-25" @click="handleTimeChange('prev-year')" />
        <a-icon type="left" class="mr-25" @click="handleTimeChange('prev-month')" />
        <span class="calendar-header-year_select-text mr-25">
          {{ dateText }}
        </span>
        <a-icon type="right" class="mr-25" @click="handleTimeChange('next-month')" />
        <a-icon type="double-right" @click="handleTimeChange('next-year')" />
      </div>
    </div>
    <div class="calendar-header-week">
      <div v-for="text in weekName" :key="text" class="calendar-header-week_text">
        {{ text }}
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { getTimeslotFromStartTime } from './utils'
import { weekAbbrList } from '@activity/pages/package/package_const.js'
import BasicUnitSelector from '@activity/pages/components/BasicUnitSelector.vue'

export default {
  name: 'CalendarHeader',
  inject: ['legendColors'],
  components: {
    BasicUnitSelector
  },
  props: {
    data: {
      type: Array,
      default: null
    },
    selectedTimeslot: {
      type: Array,
      default: () => ['ALL']
    }
  },
  computed: {
    ...mapState({
      calendarDate: (state) => state.calendarDate,
      calendarUnit: (state) => state.calendarUnit
    }),
    ...mapGetters(['unitList']),
    dateText() {
      return moment(this.calendarDate).format('MMMM YYYY')
    },
    timeslotList() {
      if (!this.data) {
        return []
      }

      return this.data.reduce((accu, curr) => {
        let timeslot = getTimeslotFromStartTime(curr.start_time)

        if (timeslot && !accu.includes(timeslot)) {
          accu.push(timeslot)
        }

        return accu
      }, [])
    },
    weekName() {
      return weekAbbrList
    }
  },
  methods: {
    handleLegendClick(index) {
      this.$emit('toggleLegend', index)
    },
    handleTimeChange(type) {
      let date = this.calendarDate

      if (type === 'prev-year') {
        date = moment(this.calendarDate)
          .subtract(1, 'years')
          .toDate()
      } else if (type === 'prev-month') {
        date = moment(this.calendarDate)
          .subtract(1, 'months')
          .toDate()
      } else if (type === 'next-year') {
        date = moment(this.calendarDate)
          .add(1, 'years')
          .toDate()
      } else if (type === 'next-month') {
        date = moment(this.calendarDate)
          .add(1, 'months')
          .toDate()
      }

      this.$store.commit('setCalendarDate', date)
    },
    handleUnitChange(unitSku) {
      this.$store.commit('setCalendarUnit', unitSku)
    },
    handleTimeslotChange(changedList) {
      let newList = [...changedList]

      // 已选ALL的情况下，选其它值需要删除ALL
      if (this.selectedTimeslot.includes('ALL') && newList.includes('ALL')) {
        newList = newList.filter((item) => item !== 'ALL')
      }

      // 只剩一个的时候，删了会塞一个 ALL 进去
      // if (newList.length === 0 || newList.includes('ALL')) {
      //   newList = ['ALL']
      // }

      // 只剩一个的时候不能删
      // if (newList.length === 0) {
      //   newList = [...this.selectedTimeslot]
      // }

      if (newList.includes('ALL')) {
        newList = ['ALL']
      }

      this.$emit('update:selectedTimeslot', newList)
    }
  }
}
</script>

<style lang="scss" scoped>
.calendar-header {
  padding: 5px 0;
  background-color: #fafafa;

  &-select {
    padding: 10px 10px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  &-year {
    position: absolute;
    top: 0;
    right: 0;
    padding: 10px 0;
    display: flex;
    justify-content: space-between;

    &_legend {
      display: flex;

      &-item {
        display: flex;
        margin-right: 30px;
        align-items: center;
        color: #000;
        cursor: pointer;

        &_icon {
          font-size: 14px;
          margin-right: 6px;
        }

        &_paint {
          width: 10px;
          height: 4px;
          margin-right: 6px;
        }

        &_text {
          font-size: 10px;
          line-height: 12px;
        }

        &.disabled {
          color: rgba(0, 0, 0, 0.45);

          .calendar-header-year_legend-item_text {
            color: rgba(0, 0, 0, 0.45) !important;
          }
        }
      }
    }

    &_select {
      display: flex;
      align-items: center;

      &-text {
        width: 200px;

        text-align: center;
        font-weight: 600;
        font-size: 24px;
        line-height: 30px;
        color: #333;
      }

      .mr-25 {
        margin-right: 25px;
      }
    }
  }

  &-week {
    padding: 10px 0 0;
    display: flex;
    justify-content: space-between;

    &_text {
      width: calc(100% / 7 - 8px);
      color: #000;

      font-weight: 600;
      font-size: 12px;
      line-height: 14px;
      text-align: right;
    }
  }
}
</style>
