<template>
  <section class="calendar-table">
    <div
      v-for="(dateRow, index) in filteredDataList"
      :key="index"
      class="calendar-table_wrap"
    >
      <div class="calendar-table_row">
        <div
          v-for="item in dateRow"
          :key="`${item.date}-${item.data.length}`"
          class="calendar-table_item"
        >
          <div
            class="calendar-table_item-header"
            :class="{ 'is-today': item.isToday }"
          >
            <a-icon
              v-if="!hideEdit(item)"
              type="edit"
              class="calendar-table_item-header_edit"
              @click="handleEdit(item)"
            />
            {{ item.text }}
          </div>
          <CalendarTableSlot :data="item" :more="moreArr[index]" />
        </div>
      </div>

      <!-- 更多 -->
      <div
        v-if="showMore(dateRow)"
        class="calendar-more"
        @click="handleMore(index)"
      >
        <svg-icon
          icon-name="page-down"
          class="calendar-more-icon"
          :class="{ rotate: !moreArr[index] }"
        />
      </div>
    </div>
  </section>
</template>

<script>
import { mapState } from 'vuex'
import { getDateFromStartTime, getTimeslotFromStartTime } from './utils'
import CalendarTableSlot from './CalendarTableSlot'

export default {
  name: 'CalendarTable',
  inject: ['calendar'],
  components: {
    CalendarTableSlot
  },
  props: {
    data: {
      type: Array,
      default: null
    },
    dateList: {
      type: Array,
      default: () => []
    },
    selectedTimeslot: {
      type: Array,
      default: null
    }
  },
  data() {
    return {
      moreNum: 4, // 超过 4 个就显示 more
      moreArr: [false, false, false, false, false, false, false, false]
    }
  },
  computed: {
    ...mapState({
      isBdAudit: (state) => state.isBdAudit,
      calendarDate: (state) => state.calendarDate,
      calendarUnit: (state) => state.calendarUnit,
      destinationToday: (state) => state.destinationToday,
      isPkgPendingApproval: (state) => state.isPkgPendingApproval
    }),
    dataList() {
      if (!this.data) {
        return this.dateList
      }

      // 给 dateList 填入数据
      const list = _.cloneDeep(this.dateList)
      list.forEach((weekItem) => {
        weekItem.forEach((item) => {
          for (let i = 0; i < this.data.length; ++i) {
            const dataItem = this.data[i]
            const date = getDateFromStartTime(dataItem.start_time)

            if (date && date === item.date) {
              item.originalData.push(dataItem)
              item.data.push(dataItem)
            }
          }

          item.isOutdate = moment(this.destinationToday).isAfter(
            item.date,
            'day'
          )
          item.isToday = moment(this.destinationToday).isSame(item.date, 'day')
        })
      })
      return list
    },
    filteredDataList() {
      const list = [...this.dataList]
      list.forEach((weekItem) => {
        weekItem.forEach((item) => {
          if (this.selectedTimeslot.includes('ALL')) {
            item.data = [...item.originalData]
          } else {
            item.data = item.originalData.filter((dataItem) => {
              const timeslot = getTimeslotFromStartTime(dataItem.start_time)
              return this.selectedTimeslot.includes(timeslot)
            })
          }
        })
      })
      return list
    }
  },
  watch: {
    filteredDataList() {
      this.moreArr = this.moreArr.map(() => false)
    }
  },
  methods: {
    hideEdit(item) {
      // 没有unit的时候也应该不能编辑
      // bdAudit 在pending approval的时候应该能编辑
      const {
        calendarUnit,
        $root,
        isBdAudit,
        isPkgPendingApproval,
        destinationToday
      } = this
      return (
        !calendarUnit ||
        $root.nonEdit2act ||
        (!isBdAudit && $root.nonEdit2status) ||
        (isBdAudit && !isPkgPendingApproval) ||
        moment(item.date).isBefore(destinationToday, 'day')
      )
    },
    handleEdit(item) {
      this.calendar.$emit('edit', {
        item,
        unit: this.calendar.selectedUnit
      })
    },
    showMore(row) {
      // 这里不能根据 more 来判断显示
      return row.some((item) => item.data.length > this.moreNum)
    },
    handleMore(index) {
      this.$set(this.moreArr, index, !this.moreArr[index])
    }
  }
}
</script>

<style lang="scss" scoped>
.calendar-table {
  width: 100%;

  &_wrap {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &_row {
    display: flex;
    justify-content: space-between;
  }

  &_item {
    width: calc(100% / 7 - 8px);

    &:last-child {
      margin-right: 0;
    }

    &-header {
      position: relative;
      width: 100%;
      height: 27px;
      padding: 4px 6px;
      margin: 0;

      font-size: 16px;
      font-weight: 600;
      line-height: 20px;
      color: #000;
      text-align: right;

      border-radius: 4px;
      background-color: rgba(0, 0, 0, 0.04);

      &.is-today {
        background-color: rgba(0, 145, 255, 0.1);
      }

      &_edit {
        display: none;
        position: absolute;
        right: 33px;
        top: 6px;
        font-size: 16px;
        color: #0091ff;
        cursor: pointer;
      }

      &:hover {
        .calendar-table_item-header_edit {
          display: block;
        }
      }
    }
  }

  .calendar-more {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 8px;

    width: 100%;
    height: 20px;
    text-align: center;
    background-color: #fff;
    border-radius: 4px;
    cursor: pointer;

    &-icon.rotate {
      transform: rotate(180deg);
    }
  }
}
</style>
