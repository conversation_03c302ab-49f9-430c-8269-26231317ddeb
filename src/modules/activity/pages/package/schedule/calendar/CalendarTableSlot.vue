<template>
  <div class="calendar-table-slot" :class="{ 'is-outdate': data.isOutdate }">
    <!-- 内容块 -->
    <div v-for="item in slicedData" :key="item.time" class="calendar-item">
      <p class="calendar-item_title align-right one-line">
        {{ formatTimeslot(item) }}
      </p>
      <div class="calendar-item_content">
        <template v-for="color in legendColors">
          <div
            v-show="!color.disabled"
            :key="color.label"
            class="calendar-item_content-row"
          >
            <div class="calendar-item_content-icon">
              <svg-icon :icon-name="color.icon" />
            </div>
            <OverflowTextTooltip
              class="calendar-item_content-text one-line"
              :style="{ color: color.color }"
            >
              {{ getCalendarContentText(item, color) }}
            </OverflowTextTooltip>
          </div>
        </template>
        <div v-if="isAdmin" class="calendar-item_content-row flex-end">
          <a-popover v-if="item.custom_price.length > 0" placement="top">
            <template slot="content">
              <div class="calendar-item_content-ssp">
                <p v-for="(price, index) in item.custom_price" :key="index">
                  <span style="color: #757575;">
                    {{ price.currency }}
                  </span>
                  <span style="color: #212121; font-weight: 500;">
                    {{ (price.price || 0) | formatThousands }}
                  </span>
                </p>
              </div>
            </template>
            <template slot="title">
              <span style="font-size: 14px;">
                {{ $t('package_special_selling_price') }}
              </span>
            </template>
            <a-button
              type="link"
              size="small"
              class="calendar-item-ssp"
              style="font-size: 12px; line-height: 18px;padding: 0;"
            >
              SSP >>
            </a-button>
          </a-popover>
          <a-button
            v-else
            type="link"
            size="small"
            class="calendar-item-ssp"
            style="font-size: 12px; line-height: 18px;padding: 0; color: #fff;"
          >
            SSP >>
          </a-button>
        </div>
      </div>
    </div>

    <!-- 占位块 -->
    <div v-if="slicedData.length === 0" class="calendar-item blank">
      <p class="calendar-item_title align-right">
        null
      </p>
      <div class="calendar-item_content">
        <template v-for="color in legendColors">
          <div
            v-show="!color.disabled"
            :key="color.label"
            class="calendar-item_content-row"
          >
            <span
              class="calendar-item_content-text"
              :style="{ color: color.color }"
            >
              0
            </span>
          </div>
        </template>
        <div v-if="isAdmin" class="calendar-item_content-row flex-end">
          <a-button
            type="link"
            size="small"
            class="calendar-item-ssp"
            style="font-size: 12px; line-height: 18px; padding: 0; color: #fff;"
          >
            SSP >>
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { isAdmin } from '@/env'
import { formatThousands } from '@activity/utils'
import { MAX_INVENTORY } from '@activity/pages/package/package_const.js'
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'

export default {
  name: 'CalendarTableSlot',
  components: {
    OverflowTextTooltip
  },
  inject: ['legendColors'],
  filters: {
    formatThousands
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    more: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isAdmin: isAdmin
    }
  },
  computed: {
    slicedData() {
      return this.more ? this.data.data.slice(0, 4) : this.data.data
    }
  },
  methods: {
    getCalendarContentText(item, color) {
      if (color.attr === 'inventory') {
        return this.getInventoryText(item)
      }

      return `${item[color.currencyAttr]} ${formatThousands(
        item[color.attr] || 0
      )}`
    },
    showTimeslot(item) {
      const splitedTime = item.start_time.split(' ')
      return splitedTime[1] && splitedTime[1] !== '00:00:00'
    },
    formatTimeslot(item) {
      const splitedTime = item.start_time.split(' ')
      return !splitedTime[1] || splitedTime[1] === '00:00:00'
        ? this.$t('package_timeslot_by_day')
        : splitedTime[1].substring(0, 5)
    },
    getInventoryText(item) {
      const { confirmed_count, inventory } = item
      let str = this.$store.state.isMC2BD ? `${confirmed_count || 0} / ` : ''
      str +=
        inventory === 0
          ? this.$t('global_sold_out')
          : inventory >= MAX_INVENTORY
          ? this.$t('package_unlimited')
          : formatThousands(inventory)
      return str
    },
    chunkCustomPriceList(list) {
      return _.chunk(list, 2)
    }
  }
}
</script>

<style lang="scss">
.calendar-table-slot {
  p {
    margin: 0;
  }

  .one-line {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .align-right {
    text-align: right;
  }

  .calendar-item_content-row.flex-end {
    justify-content: flex-end;
  }

  .calendar-item {
    width: 100%;
    padding: 8px;
    margin-top: 8px;
    position: relative;

    background-color: #fff;
    border-radius: 4px;

    &.blank {
      .calendar-item_title,
      .calendar-item_content-block,
      .calendar-item_content-title,
      .calendar-item_content-text,
      .calendar-item_content-row .ant-btn {
        color: #fff !important;
      }
    }

    &_title {
      margin-bottom: 4px;

      color: rgba(0, 0, 0, 0.45);
      font-size: 12px;
      line-height: 20px;
      font-weight: 600;

      // 只展示一行
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; //溢出不换行
    }

    &_content {
      // display: flex;
      // justify-content: space-between;
      // align-items: flex-start;

      &-left {
        width: 44%;
      }

      &-right {
        width: 55%;
      }

      &-block {
        min-height: 28px;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      &-row {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 12px;
      }

      &-title {
        font-size: 10px;
        line-height: 12px;
        color: rgba(0, 0, 0, 0.45);
      }

      &-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        margin-right: 8px;
      }

      &-text {
        font-size: 12px;
        line-height: 20px;
        color: #000;
        font-weight: 600;

        &.sold-out {
          color: #ff5630;
        }
      }
    }
  }

  &.is-outdate {
    .calendar-item {
      &_title,
      &_content-text {
        color: rgba(0, 0, 0, 0.25) !important;
      }

      &-ssp {
        color: rgba(0, 0, 0, 0.25);
      }
    }
  }
}

.ant-popover-content {
  .calendar-item_content-ssp {
    max-height: 300px;
    overflow-y: auto;

    p {
      margin: 0;
      font-size: 14px;
      line-height: 26px;
    }
  }
}
</style>
