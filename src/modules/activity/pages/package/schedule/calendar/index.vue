<template>
  <div class="activity-calendar">
    <a-affix :offset-top="isAdmin ? 0 : 64">
      <div style="background-color: #fafafa">
        <!-- 首次打开的提示 -->
        <GeneralAlert v-if="showGuideAlert || (isAdmin && isBdAudit && takeRateAlert)" banner type="warning">
          <span v-if="isAdmin && isBdAudit && takeRateAlert" v-html="takeRateAlert" />
          <span v-if="showGuideAlert">
            <span>{{ $t('package_price_default_price_tips_1') }}</span>
            <a-button
              type="link"
              size="small"
              style="margin: 0; padding: 0;"
              @click="() => $emit('openUnit')"
            >
              {{ $t('package_unit_subtitle') }}
            </a-button>
            <span>{{ $t('package_price_default_price_tips_2') }}</span>
            <a-button
              type="link"
              size="small"
              style="margin: 0; padding: 0;"
              @click="() => $emit('openTimeslot')"
            >
              {{ $t('package_timeslot_subtitle') }}
            </a-button>
            <span>{{ $t('package_price_default_price_tips_3') }}</span>
          </span>
        </GeneralAlert>

        <CalendarHeader
          :data="data"
          :selectedTimeslot.sync="selectedTimeslot"
          @toggleLegend="handleToggleLegend"
        />
      </div>
    </a-affix>
    <a-spin :spinning="loading">
      <CalendarTable :data="data" :date-list="dateList" :selectedTimeslot="selectedTimeslot" />
    </a-spin>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { isAdmin } from '@/env'
import GeneralAlert from '@activity/pages/components/GeneralAlert.vue'
import { range as rangeArr, getFirstDayOfMonth, getPrevMonthLastDays, getMonthDays } from './utils'
import { provingTakeRate } from '@activity/utils'
import CalendarHeader from './CalendarHeader.vue'
import CalendarTable from './CalendarTable.vue'
let isMC2BD = klook.getPlatformRoleKey() !== 'admin'
const colors = [
  {
    label: isMC2BD ? __('28248') : __('package_inventory'),
    attr: 'inventory',
    icon: 'inventory',
    currencyAttr: '',
    color: '#000',
    disabled: false,
    message: __('49363')
  },
  {
    label: __('package_price_cost'),
    attr: 'cost_price',
    icon: 'finance',
    currencyAttr: 'cost_currency',
    color: '#6554C0',
    disabled: false,
    message: __('49362')
  }
]

const otherColors = [
  {
    label: __('package_price_retail'),
    attr: 'retail_price',
    icon: 'finance',
    currencyAttr: 'selling_currency',
    color: '#00B8D9',
    disabled: false,
    message: __('49364')
  },
  {
    label: __('package_price_selling'),
    attr: 'selling_price',
    icon: 'finance',
    currencyAttr: 'selling_currency',
    color: '#FF5630',
    disabled: false
  }
]

if (isAdmin) {
  colors.push(...otherColors)
}

export default {
  name: 'Calendar',
  components: {
    GeneralAlert,
    CalendarHeader,
    CalendarTable
  },
  inject: ['activityId', 'packageId'],
  provide() {
    return {
      calendar: this,
      legendColors: this.colors
    }
  },
  data() {
    return {
      isAdmin,
      loading: false,
      isTakeRateLoading: false,
      lastTakeRate: '0',
      data: null,
      selectedTimeslot: ['ALL'],
      firstDayOfWeek: 0 // 日历从周几开始
    }
  },
  computed: {
    ...mapState({
      isBdAudit: (state) => state.isBdAudit,
      calendarDate: (state) => state.calendarDate,
      calendarUnit: (state) => state.calendarUnit,
      pkgStepStatusMap: (state) => state.pkgStepStatusMap
    }),
    ...mapGetters(['unitList', 'isTour']),
    colors() {
      const exclude = this.isTour ? ['retail_price'] : []
      return colors.filter((item) => !exclude.includes(item.attr))
    },
    yearAndLastMonth() {
      return moment(this.calendarDate)
        .subtract(1, 'months')
        .format('YYYY-MM')
    },
    yearAndMonth() {
      return moment(this.calendarDate).format('YYYY-MM')
    },
    yearAndNextMonth() {
      return moment(this.calendarDate)
        .add(1, 'months')
        .format('YYYY-MM')
    },
    dateList() {
      let days = []
      let firstDay = getFirstDayOfMonth(this.calendarDate) // 本月第一天是周几

      const prevMonthDays = getPrevMonthLastDays(this.calendarDate, firstDay - this.firstDayOfWeek).map(
        (day) => ({
          date: `${this.yearAndLastMonth}-${this.formatDay(day)}`,
          text: this.formatDay(day),
          type: 'prev',
          originalData: [], // 备份数据，用来执行timslot筛选
          data: []
        })
      )
      const currentMonthDays = getMonthDays(this.calendarDate).map((day) => ({
        date: `${this.yearAndMonth}-${this.formatDay(day)}`,
        text: this.formatDay(day),
        type: 'current',
        originalData: [], // 备份数据，用来执行timslot筛选
        data: []
      }))
      days = [...prevMonthDays, ...currentMonthDays]

      const nextMonthDays = rangeArr((7 - (days.length % 7)) % 7).map((_, index) => ({
        date: `${this.yearAndNextMonth}-${this.formatDay(index + 1)}`,
        text: this.formatDay(index + 1),
        type: 'next',
        originalData: [], // 备份数据，用来执行timslot筛选
        data: []
      }))
      days = days.concat(nextMonthDays)

      return this.toNestedArr(days)
    },
    showGuideAlert() {
      const { price, timeslot } = this.pkgStepStatusMap[this.packageId] || {}

      if ((this.isAdmin && this.isBdAudit) || (price && price !== 0 && timeslot && timeslot !== 0)) {
        return false
      }

      return true
    },
    takeRateAlert() {
      if (this.isAdmin && this.lastTakeRate) {
        const text = this.$t('package_calendar_take_rate_tips')
        const takeRate = this.formatTakeRate(this.lastTakeRate)
        return text.replace('{0}', `<span style="color: #FFAB00;">${takeRate}%</span>`)
      }

      return ''
    }
  },
  watch: {
    unitList() {
      // 如果unitList存在，但calendarUnit不存在，则需要初始化
      // 如果unitList存在，但calendarUnit不在unitList里面（被删了），则也需要初始化
      if (
        this.unitList.length > 0 &&
        (!this.calendarUnit || !this.unitList.find((item) => item.sku_id === this.calendarUnit))
      ) {
        this.$store.commit('setCalendarUnit', this.unitList[0].sku_id)
      }
    },
    calendarUnit() {
      // 为了用户体验，使用 debounce 会有延迟，所以不用 debounce
      this.getCalendarData()
    },
    dateList: {
      immediate: true,
      handler() {
        if (this.dateList.length > 0) {
          // 使用 debounce 防止频繁切换
          this.debounceGetCalendarData()
          // 重置timeslot
          this.selectedTimeslot = ['ALL']
        }
      }
    }
  },
  async created() {
    if (this.isAdmin) {
      await this.getTakeRate()
    }
  },
  methods: {
    formatTakeRate(takeRate) {
      return provingTakeRate(takeRate)
    },
    toNestedArr(days) {
      return rangeArr(days.length / 7).map((_, index) => {
        const start = index * 7
        return days.slice(start, start + 7)
      })
    },
    formatDay(day) {
      return day > 9 ? String(day) : `0${day}`
    },
    handleToggleLegend(index) {
      this.colors[index].disabled = !this.colors[index].disabled
    },
    async getTakeRate() {
      this.isTakeRateLoading = true

      const res = await ajax.get(ADMIN_API.act.query_activity_take_rate, {
        params: {
          activity_id: this.activityId
        }
      })

      this.isTakeRateLoading = false
      this.lastTakeRate = res.last_take_rate
    },
    async getCalendarData() {
      if (!this.calendarUnit || this.dateList.length === 0) {
        return
      }

      this.loading = true
      const res = await ajax.post(ADMIN_API.act.query_sku_price_inventory_calendar, {
        data: {
          package_id: this.packageId,
          sku_id: this.calendarUnit,
          time_point: -1,
          is_page_ing: false,
          start_date: this.dateList[0][0].date,
          end_date: this.dateList[this.dateList.length - 1][6].date
        }
      })
      this.loading = false

      this.data = res.calendar
      this.$store.commit('setDestinationToday', res.destination_today)
      this.$store.commit('setPriceModel', res.package_pricing_model)
    },
    debounceGetCalendarData: _.debounce(function() {
      this.getCalendarData()
    }, 500)
  }
}
</script>

<style lang="scss">
.activity-calendar {
  background-color: #fafafa;
}
</style>
