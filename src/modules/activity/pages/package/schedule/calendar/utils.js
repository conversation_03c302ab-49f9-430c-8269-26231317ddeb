/**
 * 日历相关
 * Date
 */
export const range = function(n) {
  // see https://stackoverflow.com/questions/3746725/create-a-javascript-array-containing-1-n
  return Array.apply(null, { length: n }).map((_, n) => n)
}

export const getFirstDayOfMonth = function(date) {
  const temp = new Date(date.getTime())
  temp.setDate(1)
  return temp.getDay()
}

export const getPrevMonthLastDays = (date, amount) => {
  if (amount <= 0) return []
  const temp = new Date(date.getTime())
  temp.setDate(0)
  const lastDay = temp.getDate()
  return range(amount).map((_, index) => lastDay - (amount - index - 1))
}

export const getMonthDays = (date) => {
  const temp = new Date(date.getFullYear(), date.getMonth() + 1, 0)
  const days = temp.getDate()
  return range(days).map((_, index) => index + 1)
}

export const validateRangeInOneMonth = function(start, end) {
  return (
    start.getMonth() === end.getMonth() &&
    start.getFullYear() === end.getFullYear()
  )
}

/**
 * 日期相关
 * price
 */
export function getDateFromStartTime(startTime) {
  if (!startTime) return ''
  const timeArr = startTime.split(' ')
  return timeArr[0] ? timeArr[0] : ''
}

export function getTimeslotFromStartTime(startTime) {
  if (!startTime) return ''
  const timeArr = startTime.split(' ')
  return timeArr[1] ? timeArr[1].substring(0, 5) : '00:00'
}
