<template>
  <div id="package-schedule-editable" class="package_schedule--container">
    <a-spin :spinning="loading">
      <Calendar
        ref="calendar"
        @edit="handleEdit"
        @openUnit="handleAddUnit"
        @openTimeslot="handleAddTimeslot"
      />
    </a-spin>

    <a-drawer
      :title="$t('package_unit_title')"
      :width="960"
      placement="right"
      :visible="unitPriceVisible"
      :headerStyle="drawerHeaderStyle"
      :bodyStyle="drawerBodyStyle"
      @close="handleAddUnitPriceClose"
    >
      <UnitPrice :visible="unitPriceVisible" @close="handleAddUnitPriceClose" @update="handleLazyUpdate" />
    </a-drawer>

    <a-drawer
      :title="$t('package_timeslot_title')"
      :width="960"
      placement="right"
      :visible="timeslotInventoryVisible"
      :headerStyle="drawerHeaderStyle"
      :bodyStyle="drawerBodyStyle"
      @close="handleAddTimeslotInventoryClose"
    >
      <TimeslotInventory @close="handleAddTimeslotInventoryClose" @update="handleLazyUpdate" />
    </a-drawer>

    <a-drawer
      :title="$t('package_bulk_modification_title')"
      :width="960"
      placement="right"
      :visible="bulkModificationVisible"
      :headerStyle="drawerHeaderStyle"
      :bodyStyle="drawerBodyStyle"
      @close="handleBulkModificationClose"
    >
      <BulkModification
        :visible="bulkModificationVisible"
        :default-setting="defaultSetting"
        @close="handleBulkModificationClose"
        @update="handleLazyUpdate"
      />
    </a-drawer>

    <CommonFooter>
      <!-- admin bd 在pending approval的时候应该不能编辑 -->
      <a-button
        v-if="calendarUnit"
        :loading="loading"
        :disabled="bulkModificationDisabled"
        @click="handleBulkModification"
      >
        <a-icon type="edit" />
        {{ $t('package_bulk_modification_title') }}
      </a-button>
      <a-popover v-else placement="top">
        <template slot="content">
          <span>{{ $t('package_bulk_modification_disable_tips') }}</span>
        </template>
        <a-button disabled>
          <a-icon type="edit" />
          {{ $t('package_bulk_modification_title') }}
        </a-button>
      </a-popover>
      <a-button
        v-if="!isBdAudit"
        type="primary"
        :loading="loading"
        :disabled="$root.nonEdit2act || $root.nonEdit2status"
        @click="handleAddUnit"
      >
        <a-icon type="plus" />
        {{ $t('package_unit_title') }}
      </a-button>
      <a-button v-if="!isBdAudit" type="primary" :disabled="addTimeslotDisabled" @click="handleAddTimeslot">
        <a-icon type="plus" />
        {{ $t('package_timeslot_title') }}
      </a-button>
    </CommonFooter>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import CommonFooter from '@activity/components/CommonFooter.vue'
import { checkAuth } from '@/plugins/authDirective'
import { isMerchant } from '@/env'
import Calendar from './calendar'
import UnitPrice from './unit-price'
import TimeslotInventory from './timeslot-inventory'
import BulkModification from './bulk-modification'

export default {
  name: 'PackageSchedule',
  components: {
    Calendar,
    UnitPrice,
    TimeslotInventory,
    BulkModification,
    CommonFooter
  },
  props: {
    activity_id: {
      type: [Number, String],
      required: true
    },
    package_id: {
      type: [Number, String],
      required: true
    },
    actGlobal: {
      type: Object,
      default: () => ({})
    }
  },
  provide() {
    return {
      activityId: this.activity_id,
      packageId: this.package_id
    }
  },
  data() {
    return {
      isMerchant,

      loading: false,
      // activity_id: Number(this.activity_id),
      // package_id: 74454,
      drawerHeaderStyle: {
        borderBottom: 'none',
        paddingLeft: '16px',
        paddingRight: '16px'
      },
      drawerBodyStyle: {
        padding: '60px 16px'
      },
      unitPriceVisible: false,
      timeslotInventoryVisible: false,
      bulkModificationVisible: false,
      defaultSetting: {},
      needUpdate: false
    }
  },
  computed: {
    ...mapState({
      isBdAudit: (state) => state.isBdAudit,
      calendarUnit: (state) => state.calendarUnit,
      isPkgPendingApproval: (state) => state.isPkgPendingApproval,
      isPkgApproved: (state) => state.isPkgApproved
    }),
    ...mapGetters(['isTripMapped']),
    bulkModificationDisabled() {
      const { isBdAudit, isMerchant, isPkgPendingApproval, isPkgApproved } = this
      const { nonEdit2act, nonEdit2status } = this.$root

      // bd_audit 且 pending 的时候开放
      const isBdAndPending = isBdAudit && isPkgPendingApproval
      // merchant 且 approved 且有 编辑库存权限或编辑权限 的时候开放
      const isMerchantAndApproved =
        isMerchant && isPkgApproved && (checkAuth('editInventory') || !this.$root.nonEdit2status)

      if (!isMerchantAndApproved && (nonEdit2act || (nonEdit2status && !isBdAndPending))) {
        return true
      }

      return false
    },
    addTimeslotDisabled() {
      const { isTripMapped, isMerchant, isPkgApproved } = this
      const { nonEdit2act, nonEdit2status } = this.$root

      // 携程 api 的时候锁住
      if (isTripMapped) {
        return true
      }

      // merchant 且 approved 且有 编辑库存权限或编辑权限 的时候开放
      const isMerchantAndApproved =
        isMerchant && isPkgApproved && (checkAuth('editInventory') || !this.$root.nonEdit2status)
      if (!isMerchantAndApproved && (nonEdit2act || nonEdit2status)) {
        return true
      }

      return false
    }
  },
  watch: {
    package_id: {
      immediate: true,
      async handler(val, oldVal) {
        // 套餐不同则先清空价格日历模块
        if (val && val !== oldVal) {
          this.$store.commit('resetCalendar')
        }

        if (val) {
          await this.getPackageSkuData()
        }
      }
    },
    actGlobal: {
      immediate: true,
      deep: true,
      handler() {
        const isPending = this.actGlobal.pkgInfo && this.actGlobal.pkgInfo.approval_status === 1
        const isApproved = this.actGlobal.pkgInfo && this.actGlobal.pkgInfo.approval_status === 3
        this.$store.commit('setPkgPendingApproval', isPending)
        this.$store.commit('setPkgApproved', isApproved)
      }
    }
  },
  methods: {
    async getPackageSkuData() {
      this.loading = true
      this.data2skus = await this.$store.dispatch('actionGetPackageSkuData', {
        packageId: this.package_id
      })
      this.loading = false
      this.checkStatus2set()
    },
    checkStatus2set() {
      if (!this.data2skus.units || !this.data2skus.units.length) {
        this.checkOnceFlag = true // 跳过无数据
      }
      if (this.checkOnceFlag) return
      this.checkOnceFlag = true
      setTimeout(async () => {
        // 步骤状态清洗
        let isb = false
        if (this.$store.state.pkgStepStatus.price !== 1) {
          // 更新step
          await this.$store.dispatch('updatePkgStepStatus2action', {
            activity_id: this.activity_id,
            package_id: this.package_id,
            language: 'ALL',
            status: 1,
            step: 'price'
          })
          isb = true
        }
        if (this.$store.state.pkgStepStatus.timeslot !== 1) {
          // 更新step
          let res = await ajax.get(ADMIN_API.act.get_package_position, {
            params: {
              package_id: this.package_id
            }
          })
          if (klook.getType(res) === 'Object') {
            if (res.timeslot) {
              await this.$store.dispatch('updatePkgStepStatus2action', {
                activity_id: this.activity_id,
                package_id: this.package_id,
                language: 'ALL',
                status: 1,
                step: 'timeslot'
              })
              isb = true
            }
          }
        }
        isb && klook.bus.$emit('updatePkgInfos2bus')
      }, 60)
    },
    handleEdit(data) {
      this.defaultSetting = {
        dateRange: [moment(data.item.date).format('YYYY-MM-DD'), moment(data.item.date).format('YYYY-MM-DD')]
      }
      this.bulkModificationVisible = true
    },
    handleBulkModification() {
      this.bulkModificationVisible = true
    },
    handleAddUnit() {
      this.unitPriceVisible = true
    },
    handleAddTimeslot() {
      this.timeslotInventoryVisible = true
    },
    handleAddUnitPriceClose() {
      this.unitPriceVisible = false
      this.checkUpdate()
    },
    handleAddTimeslotInventoryClose() {
      this.timeslotInventoryVisible = false
      // 这里只更新日历数据即可
      this.updateCalendar()
    },
    handleBulkModificationClose() {
      this.bulkModificationVisible = false
      this.defaultSetting = {}
      this.checkUpdate()
    },
    handleLazyUpdate() {
      this.needUpdate = true
    },
    async checkUpdate() {
      // 这里需要更新unit数据 + 日历数据
      if (this.needUpdate) {
        const calendarUnitCached = this.calendarUnit

        this.loading = true
        await this.$store.dispatch('actionGetPackageSkuData', {
          packageId: this.package_id
        })
        this.loading = false

        // 如果更新后calendarUnit没变，则需要手动更新calendar
        // 什么时候calendarUnit会变：在unitPrice界面把之前选择的unit删掉了
        if (calendarUnitCached === this.calendarUnit) {
          await this.updateCalendar()
        }
      }
    },
    async updateCalendar() {
      if (this.needUpdate) {
        this.$refs.calendar && this.$refs.calendar.getCalendarData()
        this.needUpdate = false
      }
    }
  }
}
</script>

<style lang="scss">
.package_schedule--container {
  padding-bottom: 60px;
}
</style>
