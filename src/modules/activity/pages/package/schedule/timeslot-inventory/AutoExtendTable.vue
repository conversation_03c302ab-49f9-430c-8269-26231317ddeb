<template>
  <a-spin :spinning="loading">
    <a-table
      :pagination="false"
      :columns="columns"
      :data-source="data"
      :scroll="{ x: true }"
      class="auto-extend-table"
      rowKey="key"
      bordered
    />
  </a-spin>
</template>

<script>
import { MAX_INVENTORY } from '@activity/pages/package/package_const.js'
import { addZero, formatThousands } from '@activity/utils'
import AutoExtendTableBlockOutDate from './AutoExtendTableBlockOutDate.vue'
import AutoExtendTableOperations from './AutoExtendTableOperations.vue'

export default {
  name: 'AutoExtendTable',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  inject: ['packageId'],
  data() {
    const columns = [
      {
        title: this.$t('package_schedule_rule'),
        key: 'weekly',
        dataIndex: 'weekly',
        customRender: (value, row, index) => {
          const obj = {
            children: value,
            attrs: {
              rowSpan: row.rowSpan
            }
          }
          return obj
        }
      },
      {
        title: this.$t('package_timeslot'),
        key: 'start_time',
        dataIndex: 'start_time'
      },
      {
        title: this.$t('package_cut_off_time'),
        key: 'cut_off_time',
        dataIndex: 'cut_off_time'
      },
      {
        title: this.$t('block_out_date'),
        key: 'block_out_date',
        dataIndex: 'block_out_date',
        customRender: (value, row, index) => {
          const obj = {
            children: <AutoExtendTableBlockOutDate data={row} />,
            attrs: {
              rowSpan: row.rowSpan
            }
          }
          return obj
        }
      },
      {
        title: this.$t('package_inventory'),
        key: 'inventory',
        dataIndex: 'inventory'
      },
      {
        title: this.$t('package_schedule_end_date'),
        key: 'end_date',
        dataIndex: 'end_date',
        customRender: (value, row, index) => {
          const obj = {
            children: value,
            attrs: {
              rowSpan: row.rowSpan
            }
          }
          return obj
        }
      },
      {
        title: this.$t('package_list_status'),
        key: 'status',
        dataIndex: 'status',
        customRender: (value, row, index) => {
          const obj = {
            children: value,
            attrs: {
              rowSpan: row.rowSpan
            }
          }
          return obj
        }
      },
      {
        title: this.$t('global_operation'),
        key: 'operation',
        dataIndex: 'operation',
        fixed: 'right',
        customRender: (value, row, index) => {
          const obj = {
            children: (
              <AutoExtendTableOperations
                data={row}
                onDelete={this.handleDelete}
              />
            ),
            attrs: {
              rowSpan: row.rowSpan
            }
          }
          return obj
        }
      }
    ]
    return {
      columns,
      loading: false,
      data: []
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getData()
        }
      }
    }
  },
  methods: {
    async getData() {
      this.loading = true
      const data =
        (await ajax.get(ADMIN_API.act.query_schedule_rule, {
          params: {
            package_id: this.packageId
          }
        })) || []
      this.data = this.handleData(data)
      this.loading = false
    },
    async handleDelete(id) {
      const res = await ajax.postBody(ADMIN_API.act.delete_schedule_rule, {
        data: {
          package_id: this.packageId,
          rule_id: id
        }
      })

      if (res.success) {
        this.$message.success(this.$t('global_delete_success'))
        await this.getData()
      } else {
        this.$message.error(this.$t('global_delete_error'))
      }
    },
    handleData(data) {
      const list = []
      const notDeletedData = data.filter((item) => item.flag !== 1) // not deleted

      notDeletedData.forEach((item, i) => {
        item.time_point.forEach((timePoint, j) => {
          // 这里是迁移的老的代码: extendlist_timeslotlist.js
          const listItem = {
            id: item.id,
            key: `${i}-${j}`,
            show: j === 0 || '',
            disable: item.disable,
            rowSpan: j === 0 ? item.time_point.length : 0,
            end_date: j === 0 ? item.end_date : '',
            originalInventory: timePoint.inventory, // 这里重命名为 originalInventory 了
            cut_off_time:
              parseInt(timePoint.cut_off_time / (60 * 60 * 24)) +
              ` ${this.$t('global_day')} ` +
              addZero(parseInt((timePoint.cut_off_time / (60 * 60)) % 24)) +
              ':' +
              addZero((timePoint.cut_off_time / 60) % 60),
            start_time:
              timePoint.start_time === 0
                ? this.$t('package_timeslot_all_day')
                : addZero(parseInt((timePoint.start_time / (60 * 60)) % 24)) +
                  ':' +
                  addZero((timePoint.start_time / 60) % 60),
            weekly:
              j === 0
                ? item.weekly &&
                  item.weekly
                    .join('/')
                    .replace('1', this.$t('global_week_abbr_mon'))
                    .replace('2', this.$t('global_week_abbr_tue'))
                    .replace('3', this.$t('global_week_abbr_wed'))
                    .replace('4', this.$t('global_week_abbr_thu'))
                    .replace('5', this.$t('global_week_abbr_fri'))
                    .replace('6', this.$t('global_week_abbr_sat'))
                    .replace('7', this.$t('global_week_abbr_sun'))
                : '',
            block_out_date: item.block_out_date
          }

          // 根据上面计算的额外属性
          listItem.status =
            listItem.show &&
            (listItem.disable === 0 ? __('global_valid') : __('global_invalid'))
          ;(listItem.inventory =
            listItem.originalInventory >= MAX_INVENTORY
              ? this.$t('package_unlimited')
              : formatThousands(listItem.originalInventory)),
            list.push(listItem)
        })
      })

      return list
    }
  }
}
</script>

<style lang="scss">
.auto-extend-table {
  .ant-table td {
    white-space: nowrap;
  }
}
</style>
