<template>
  <div>
    <a-popover
      v-if="
        Array.isArray(data.block_out_date) &&
          data.block_out_date.length &&
          data.show
      "
    >
      <template slot="content">
        <a-table
          bordered
          rowKey="id"
          :pagination="false"
          :columns="dateColumns"
          :data-source="formatBlockoutDate(data.block_out_date)"
        />
      </template>
      <a-button type="link" size="small">
        {{ $t('global_read') }}
      </a-button>
    </a-popover>
    <span v-else>
      -
    </span>
  </div>
</template>

<script>
export default {
  name: 'AutoExtendTableBlockOutDate',
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    const dateColumns = [
      {
        title: 'ID',
        key: 'id',
        dataIndex: 'id'
      },
      {
        title: __('package_xinfo_date'),
        key: 'date',
        dataIndex: 'date'
      }
    ]
    return {
      dateColumns
    }
  },
  methods: {
    formatBlockoutDate(list) {
      return list.map((item, index) => ({
        id: index + 1,
        date: item
      }))
    }
  }
}
</script>

<style lang="scss"></style>
