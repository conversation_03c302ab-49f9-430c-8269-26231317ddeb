<template>
  <a-popconfirm
    :title="$t('package_price_schedule_delete_tips')"
    :ok-text="$t('global_button_ok')"
    :cancel-text="$t('global_button_cancel')"
    @confirm="handleDelete(data.id)"
  >
    <a-button
      type="link"
      size="small"
      :disabled="isAdminEm"
      style="color: #ff4d4f"
    >
      {{ $t('global_delete') }}
    </a-button>
  </a-popconfirm>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'AutoExtendTableOperations',
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    ...mapState({
      isAdminEm: (state) => state.isAdminEm
    })
  },
  methods: {
    handleDelete(id) {
      this.$emit('delete', id)
    }
  }
}
</script>

<style lang="scss"></style>
