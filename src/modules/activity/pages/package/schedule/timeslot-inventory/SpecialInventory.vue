<template>
  <div class="special-inventory">
    <div class="special-inventory-box">
      <tableAntd v-bind="{ tableData, hideScroll }"></tableAntd>
    </div>
  </div>
</template>

<script>
import tableAntd from '@activity/components/tableAntd'
const skuColumns = [
  {
    title: __('SKU_ID'),
    dataIndex: 'sku_id',
    width: 160
  },
  {
    title: __('SKU_Name'),
    dataIndex: 'sku_name',
    ellipsis: true,
    width: 300
  },
  {
    title: __('global_status'),
    dataIndex: 'sku_published',
    customRender: (value, record, index) => {
      if (value === 1) {
        return 'published'
      } else {
        return 'unpublished'
      }
    },
    width: 160
  },
  {
    title: __('quantity_special_inventory'),
    dataIndex: 'inventory_count',
    width: 304
  }
]
export default {
  inject: ['packageId'],
  components: {
    tableAntd
  },
  data() {
    return {
      hideScroll: true,
      tableData: {
        show: true,
        loading: false,
        dataSource: [],
        columns: skuColumns,
        rowKey: (r) => {
          return `${r.sku_id}${r.sku_name || ''}`
        }
      }
    }
  },
  async created() {
    await this.getData()
  },
  methods: {
    async getData() {
      let { tableData } = this
      tableData.loading = true
      const res = await ajax.get(
        ADMIN_API.act.get_package_special_inventory_info,
        {
          params: {
            package_id: this.packageId
          }
        }
      )
      tableData.dataSource = res || []
      tableData.loading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.special-inventory {
  .special-inventory-box {
    padding: 24px 0 0 0;
  }
}
</style>
