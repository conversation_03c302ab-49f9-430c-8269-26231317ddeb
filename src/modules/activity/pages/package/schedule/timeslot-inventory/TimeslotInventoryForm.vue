<template>
  <a-form-model class="timeslot-inventory-form" ref="form" layout="vertical" :model="form" :rules="rules">
    <a-form-model-item
      :label="$t('package_timeslot_date_range')"
      style="margin-bottom: 16px;"
      prop="dateRange"
    >
      <a-range-picker
        v-model="form.dateRange"
        :placeholder="[$t('package_start_date'), $t('package_schedule_end_date')]"
        :disabled-date="disabledDateTime"
        valueFormat="YYYY-MM-DD"
        @change="handleDateRangeChange"
      >
        <a-icon slot="suffixIcon" type="calendar" />
      </a-range-picker>
    </a-form-model-item>
    <a-form-model-item
      v-if="showRepeat"
      :label="$t('package_timeslot_repeat')"
      style="margin-bottom: 16px;"
      prop="repeat"
      required
    >
      <a-radio-group v-model="form.repeat">
        <a-radio value="repeat-daily">
          {{ $t('package_timeslot_by_daily') }}
        </a-radio>
        <a-radio value="repeat-week">
          {{ $t('package_timeslot_by_weekly') }}
        </a-radio>
      </a-radio-group>
    </a-form-model-item>
    <a-form-model-item
      v-if="showRepeat && form.repeat === 'repeat-week'"
      :label="$t('package_timeslot_by_weekdays')"
      style="margin-bottom: 16px;"
      prop="weekdays"
    >
      <a-checkbox-group v-model="form.weekdays">
        <a-checkbox v-for="(week, index) in weekList" :key="week" :value="1 + index">
          {{ $t(week) }}
        </a-checkbox>
      </a-checkbox-group>
    </a-form-model-item>
    <a-form-model-item :label="$t('package_timeslot')" style="margin-bottom: 16px;" prop="type" required>
      <a-radio-group v-model="form.type">
        <a-radio :value="1">
          {{ $t('package_timeslot_by_day') }}
        </a-radio>
        <a-radio :value="2">
          {{ $t('package_timeslot_by_timeslot') }}
        </a-radio>
      </a-radio-group>
    </a-form-model-item>
    <template v-if="form.type === 1">
      <a-form-model-item
        :label="$t('package_cut_off_time')"
        prop="cutoffTime"
        style="margin-bottom: 16px;"
        required
      >
        <BasicFormPopover
          :message="$t('package_cut_off_time_hint')"
          :auto-adjust-overflow="false"
          trigger="hover"
          placement="right"
        >
          <a-select v-model="form.cutoffType" style="width: 300px;margin-right: 12px;">
            <a-select-option
              v-for="cutoffOption in cutoffDayList"
              :key="cutoffOption.value"
              :value="cutoffOption.value"
            >
              {{ cutoffOption.text }}
            </a-select-option>
          </a-select>
          <a-time-picker
            :value="getTimeslot(form.cutoffTime)"
            format="HH:mm"
            style="width: 300px;margin-bottom: 0;"
            @change="(time, timeString) => (form.cutoffTime = timeString)"
          />
        </BasicFormPopover>
      </a-form-model-item>
      <a-form-model-item
        :label="$t('package_inventory_limited')"
        prop="isLimited"
        style="margin-bottom: 16px;"
        required
      >
        <a-radio-group v-model="form.isLimited">
          <a-radio :value="false" :style="{ lineHeight: '33px' }">
            {{ $t('package_unlimited') }}
          </a-radio>
          <a-radio :value="true" :style="{ lineHeight: '33px' }">
            {{ $t('package_inventory_limited_per_day') }}
          </a-radio>
        </a-radio-group>
        <a-input-number
          v-show="form.isLimited"
          v-model="form.inventory"
          style="width: 300px;"
          :max="MAX_INVENTORY - 1"
        />
      </a-form-model-item>
    </template>
    <div v-else-if="form.type === 2" class="timeslot-list">
      <div v-for="(timeslot, index) in form.timeslotList" :key="index" class="timeslot-list_item">
        <div class="timeslot-list_item-content">
          <div style="display: flex; align-items: center; justify-content: flex-start;">
            <a-form-model-item
              :label="$t('package_timeslot_start_time')"
              :prop="`timeslotList[${index}].startTime`"
              :rules="generateTimeslotRules()"
              style="margin: 0 12px 16px 0;"
            >
              <a-time-picker
                :value="getTimeslot(timeslot.startTime)"
                format="HH:mm"
                style="width: 200px;"
                @change="(time, timeString) => (timeslot.startTime = timeString)"
              />
            </a-form-model-item>
            <a-form-model-item
              :label="$t('package_cut_off_time')"
              :prop="`timeslotList[${index}].cutoffTime`"
              :rules="generateTimeslotRules()"
              style="margin: 0 12px 16px 0;"
            >
              <BasicFormPopover
                :message="$t('package_cut_off_time_hint')"
                :auto-adjust-overflow="false"
                trigger="hover"
                placement="top"
              >
                <div>
                  <a-select v-model="timeslot.cutoffType" style="width: 200px;margin-right: 12px;">
                    <a-select-option
                      v-for="cutoffOption in cutoffDayList"
                      :key="cutoffOption.value"
                      :value="cutoffOption.value"
                    >
                      {{ cutoffOption.text }}
                    </a-select-option>
                  </a-select>
                  <a-time-picker
                    :value="getTimeslot(timeslot.cutoffTime)"
                    format="HH:mm"
                    style="width: 200px;margin-bottom: 0;"
                    @change="(time, timeString) => (timeslot.cutoffTime = timeString)"
                  />
                </div>
              </BasicFormPopover>
            </a-form-model-item>
          </div>
          <a-form-model-item
            :label="$t('package_inventory_limited')"
            :prop="`timeslotList[${index}].isLimited`"
            :rules="generateInventoryRules(timeslot)"
            style="margin-bottom: 0; padding-bottom: 0;"
          >
            <a-radio-group v-model="timeslot.isLimited">
              <a-radio :value="false" :style="{ lineHeight: '33px' }">
                {{ $t('package_unlimited') }}
              </a-radio>
              <a-radio :value="true" :style="{ lineHeight: '33px' }">
                {{ $t('package_inventory_limited_per_day') }}
              </a-radio>
            </a-radio-group>
            <a-input-number
              v-show="timeslot.isLimited"
              v-model="timeslot.inventory"
              style="width: 200px;"
              :max="MAX_INVENTORY - 1"
            />
          </a-form-model-item>
        </div>
        <div class="timeslot-list_item-operation">
          <CircleDeleteButton
            :disabled="form.timeslotList.length === 1"
            @click="handleTimeslotDelete(index)"
          />
        </div>
      </div>
      <div class="add-btn-wrap">
        <a-button type="link" icon="plus" style="padding: 0" @click="handleAddTimeslot">
          {{ $t('global_add') }}
        </a-button>
      </div>
    </div>
    <a-form-model-item prop="isAutoExtend" required style="margin-bottom: 16px;">
      <template #label>
        <span>{{ $t('package_schedule_autoextend') }}</span>
        <a-button type="link" @click="handleExtendListClick">
          {{ $t('package_timeslotlist_extend') }}
        </a-button>
      </template>
      <BasicFormPopover
        :message="$t('package_schedule_autoextend_hint')"
        :auto-adjust-overflow="false"
        trigger="hover"
        placement="right"
      >
        <a-switch v-model="form.isAutoExtend" />
      </BasicFormPopover>
    </a-form-model-item>
    <a-form-model-item
      v-if="form.isAutoExtend"
      :label="$t('block_out_date')"
      style="margin-bottom: 16px;"
      prop="isBlockOut"
      required
    >
      <a-radio-group v-model="form.isBlockOut">
        <a-radio :value="true">
          {{ $t('global_yes') }}
        </a-radio>
        <a-radio :value="false">
          {{ $t('global_no') }}
        </a-radio>
      </a-radio-group>
    </a-form-model-item>
    <a-form-model-item
      v-if="form.isBlockOut"
      :label="$t('package_block_out_date')"
      style="margin-bottom: 16px;"
      prop="blockoutDateList"
    >
      <div class="block-out-date" v-for="(blockoutDate, index) in form.blockoutDateList" :key="index">
        <a-date-picker v-model="form.blockoutDateList[index]" :disabled-date="disabledDateTime" />
        <CircleDeleteButton
          :disabled="form.blockoutDateList.length === 1"
          style="margin-left: 8px;"
          @click="handleBlockoutDateDelete(index)"
        />
      </div>
      <div class="add-btn-wrap">
        <a-button type="link" icon="plus" @click="handleAddBlockoutDate">
          {{ $t('global_add') }}
        </a-button>
      </div>
    </a-form-model-item>
  </a-form-model>
</template>

<script>
import { MAX_INVENTORY, weekList, cutoffDayList } from '@activity/pages/package/package_const.js'
import CircleDeleteButton from '@activity/components/CircleDeleteButton.vue'
import BasicFormPopover from '@activity/pages/components/BasicFormPopover.vue'

export default {
  name: 'TimeslotInventoryForm',
  components: {
    CircleDeleteButton,
    BasicFormPopover
  },
  data() {
    const validateDateRange = (rule, value, callback) => {
      if (!value || !value[0] || !value[1]) {
        callback(new Error(this.$t('global_please_select')))
      } else {
        callback()
      }
    }
    const validateInventory = (rule, value, callback) => {
      const { isLimited, inventory } = this.form
      if (isLimited && !inventory && ![0, '0'].includes(inventory)) {
        callback(new Error(this.$t('global_please_input')))
      } else {
        callback()
      }
    }
    const validateWeekdays = (rule, value, callback) => {
      if (!value || value.length === 0) {
        callback(new Error(this.$t('global_please_select')))
      } else {
        callback()
      }
    }
    const validateBlockoutDate = (rule, value, callback) => {
      const hasBlank = value.some((item) => !item)
      if (!value || value.length === 0 || hasBlank) {
        callback(new Error(this.$t('global_please_select')))
      } else {
        callback()
      }
    }
    return {
      MAX_INVENTORY,
      weekList,
      cutoffDayList,
      showRepeat: false,
      form: {
        dateRange: [null, null],
        repeat: 'repeat-daily',
        weekdays: [1, 2, 3, 4, 5, 6, 7],
        type: 1,
        cutoffType: 0,
        cutoffTime: '00:00',
        isLimited: false,
        inventory: '',
        timeslotList: [
          {
            startTime: '00:00',
            cutoffType: 0,
            cutoffTime: '00:00',
            isLimited: false,
            inventory: ''
          }
        ],
        isAutoExtend: false,
        isBlockOut: false,
        blockoutDateList: ['']
      },
      rules: {
        dateRange: [
          { validator: validateDateRange, trigger: 'blur' },
          {
            required: true,
            message: __('global_please_select'),
            trigger: 'blur'
          }
        ],
        isLimited: [
          { validator: validateInventory, trigger: 'blur' },
          {
            required: true,
            message: __('global_please_select'),
            trigger: 'blur'
          }
        ],
        weekdays: [
          { validator: validateWeekdays, trigger: 'blur' },
          {
            required: true,
            message: __('global_please_select'),
            trigger: 'blur'
          }
        ],
        cutoffTime: [
          {
            required: true,
            message: __('global_please_select'),
            trigger: 'blur'
          }
        ],
        blockoutDateList: [
          { validator: validateBlockoutDate, trigger: 'blur' },
          {
            required: true,
            message: __('global_please_select'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    disabledDateTime(current) {
      // 只能选目的地当天以后的数据
      if (current.isBefore(new Date(), 'day')) {
        return true
      }

      return false
    },
    generateInventoryRules(timeslot) {
      const validateInventory = (rule, value, callback) => {
        if (timeslot.isLimited && !timeslot.inventory) {
          callback(new Error(this.$t('global_please_input')))
        } else {
          callback()
        }
      }

      return [
        { validator: validateInventory, trigger: 'blur' },
        {
          required: true,
          message: this.$t('global_please_select'),
          trigger: 'blur'
        }
      ]
    },
    generateTimeslotRules() {
      return [
        {
          required: true,
          message: this.$t('global_please_select'),
          trigger: 'blur'
        }
      ]
    },
    handleDateRangeChange() {
      const [startDate, endDate] = this.form.dateRange

      if (startDate && endDate && moment(endDate).diff(startDate, 'days') >= 6) {
        this.showRepeat = true
      } else {
        this.showRepeat = false
      }
    },
    handleAddTimeslot() {
      this.form.timeslotList.push(_.cloneDeep(this.$options.data().form.timeslotList[0]))
    },
    handleTimeslotDelete(index) {
      this.form.timeslotList.splice(index, 1)
    },
    getTimeslot(str) {
      return str ? moment(str, 'HH:mm') : ''
    },
    handleExtendListClick() {
      this.$emit('openAutoExtendList')
    },
    handleAddBlockoutDate() {
      this.form.blockoutDateList.push('')
    },
    handleBlockoutDateDelete(index) {
      this.form.blockoutDateList.splice(index, 1)
    },
    validateForm() {
      return this.$refs.form.validate().catch(() => false)
    },
    reset() {
      this.form = _.cloneDeep(this.$options.data().form)
    },
    getFormData() {
      return this.form
    }
  }
}
</script>

<style lang="scss">
.timeslot-inventory-form {
  .timeslot-list {
    margin-bottom: 16px;

    &_item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 16px;

      &:first-child {
        margin-top: 0;
      }

      &-content {
        width: 100%;
        padding: 8px;
        margin-right: 12px;

        background: #fafafa;
        border-radius: 4px;
      }

      .delete-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #ffab00;
      }
    }
  }

  .add-btn-wrap {
    margin-top: 10px;

    .ant-btn {
      padding: 0;
    }
  }

  .block-out-date {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-top: 16px;

    &:first-child {
      margin-top: 0;
    }
  }
}
</style>
