<template>
  <div class="package_schedule-timeslot">
    <div class="package_schedule-timeslot_header">
      <h3>{{ $t('package_timeslot_subtitle') }}</h3>
      <div>
        <a-button
          size="small"
          style="margin-right: 10px"
          :disabled="isAdminEm"
          @click="handleCopyPackageSchedule"
        >
          {{ $t('package_timeslot_copy') }}
        </a-button>
        <a-tooltip placement="left" :title="$t('special_inventory_tips')">
          <a-icon
            type="question-circle"
            theme="filled"
            style="margin-right: 10px; color: rgba(0, 0, 0, 0.25)"
          />
        </a-tooltip>
      </div>
    </div>

    <div class="package_schedule-unit_content">
      <TimeslotInventoryForm ref="form" @openAutoExtendList="openAutoExtendList" />
    </div>

    <div v-if="!isMC2BD" class="special-inventory-wrap">
      <div class="package_schedule-timeslot_header">
        <h3>{{ $t('activity_special_inventory') }}</h3>
        <div>
          <a-button @click="gotoSpecialInventory" size="small" style="margin-right: 10px">
            {{ $t('activity_special_inventory') }}
          </a-button>
          <a-tooltip placement="left" :title="$t('28337')">
            <a-icon
              type="question-circle"
              theme="filled"
              style="margin-right: 10px; color: rgba(0, 0, 0, 0.25)"
            />
          </a-tooltip>
        </div>
      </div>
      <SpecialInventory />
    </div>

    <div class="package_schedule-timeslot_footer">
      <a-button :style="{ marginRight: '8px' }" @click="handleCancel">
        {{ $t('global_button_cancel') }}
      </a-button>
      <a-button :disabled="isAdminEm" type="primary" @click="handleSave">
        {{ $t('global_save') }}
      </a-button>
    </div>

    <!-- <a-modal
      title="Auto Extend List"
      :visible="isAutoExtendListVisible"
      :footer="null"
      :width="800"
      :ok-text="$t('global_button_ok')"
      :cancel-text="$t('global_button_cancel')"
      @cancel="handleAutoExtendListClose"
      :bodyStyle="{ paddingBottom: '20px' }"
    >
      <AutoExtendTable :visible="isAutoExtendListVisible" />
    </a-modal> -->

    <a-drawer
      :title="$t('package_timeslotlist_extend')"
      :width="800"
      :visible="isAutoExtendListVisible"
      :headerStyle="drawerHeaderStyle"
      :bodyStyle="drawerBodyStyle"
      placement="right"
      @close="handleAutoExtendListClose"
    >
      <AutoExtendTable :visible="isAutoExtendListVisible" />
    </a-drawer>

    <a-modal
      :title="$t('package_copy_schedule_dialog_title')"
      :visible="isCopyScheduleVisible"
      :width="800"
      :confirmLoading="isCopyScheduleLoading"
      :okButtonProps="{ props: { disabled: !copySchedule } }"
      :bodyStyle="{ paddingBottom: '20px' }"
      :ok-text="$t('global_button_ok')"
      :cancel-text="$t('global_button_cancel')"
      @cancel="handleCopyScheduleClose"
      @ok="handleCopyScheduleOk"
    >
      <a-spin :spinning="loading">
        <a-alert type="info" :message="$t('package_copy_schedule_dialog_tips')" banner />
        <a-radio-group v-model="copySchedule" style="margin-top: 20px">
          <a-radio v-for="pkg in otherPkgList" :key="pkg.package_id" :value="pkg.package_id">
            {{ getPkgName(pkg) }}
          </a-radio>
        </a-radio-group>
      </a-spin>
    </a-modal>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { bestMatchLang, scrollFormErrorIntoView, getTimeOffset } from '@activity/utils'
import { MAX_INVENTORY } from '@activity/pages/package/package_const.js'
import TimeslotInventoryForm from './TimeslotInventoryForm.vue'
import SpecialInventory from './SpecialInventory.vue'
import AutoExtendTable from './AutoExtendTable.vue'

export default {
  name: 'TimeslotInventory',
  inject: ['rootChangeLoading', 'activityId', 'packageId', 'handleGTMBtn'],
  components: {
    SpecialInventory,
    TimeslotInventoryForm,
    AutoExtendTable
  },
  data() {
    return {
      loading: false,
      isAutoExtendListVisible: false,
      isCopyScheduleVisible: false,
      isCopyScheduleLoading: false,
      copySchedule: null,

      drawerHeaderStyle: {
        borderBottom: 'none',
        paddingLeft: '16px',
        paddingRight: '16px'
      },
      drawerBodyStyle: {
        padding: '60px 16px'
      }
    }
  },
  computed: {
    ...mapState({
      isMC2BD: (state) => state.isMC2BD,
      isAdminEm: (state) => state.isAdminEm
    }),
    ...mapGetters(['pkgList']),
    otherPkgList() {
      return this.pkgList.filter((pkg) => pkg.package_id !== this.packageId)
    }
  },
  methods: {
    gotoSpecialInventory() {
      klook.gotoOldAdmin('/act/spinv/list')
    },
    openAutoExtendList() {
      this.isAutoExtendListVisible = true
    },
    handleAutoExtendListClose() {
      this.isAutoExtendListVisible = false
    },
    getPkgName(pkg) {
      return bestMatchLang('name', 'language_type', pkg.package_local)
    },
    async handleCopyPackageSchedule() {
      this.copySchedule = null
      this.isCopyScheduleVisible = true

      this.loading = true
      await this.$store.dispatch('getPkgInfo2actions', {
        activity_id: this.activityId
      })
      this.loading = false
    },
    handleCopyScheduleClose() {
      this.isCopyScheduleVisible = false
    },
    handleCancel() {
      this.$emit('close')
    },
    async handleCopyScheduleOk() {
      this.isCopyScheduleLoading = true
      const res = await ajax.postBody(ADMIN_API.act.copy_package_schedule, {
        data: {
          source: this.copySchedule,
          target: [this.packageId]
        }
      })
      this.isCopyScheduleLoading = false

      if (res.success) {
        this.isCopyScheduleVisible = false
        this.$emit('update')
        this.$emit('close')
        this.$message.success(this.$t('global_copy_success'))
        await this.saveStepStatus()
      } else {
        this.$message.error(this.$t('global_copy_error'))
      }
    },
    getWeekly(weekly, dateRange) {
      const [startDate, endDate] = dateRange

      // 如果超过6天，则直接传选择的weekly
      if (startDate && endDate && moment(endDate).diff(startDate, 'days') >= 6) {
        return weekly
      }

      // 如果没有超过6天，则从 startDate 和 endDate 里面解析出时间段
      let startDay = moment(startDate).day()
      let endDay = moment(endDate).day()

      const res = []
      for (let i = 0; i <= 6; i += 1) {
        const day = (startDay + i) % 7
        res.push(day)

        if (day === endDay) {
          break
        }
      }
      return res.map((item) => (item === 0 ? 7 : item))
    },
    async handleSave() {
      const valid = await this.$refs.form.validateForm()

      if (!valid) {
        scrollFormErrorIntoView()
        return
      }

      const formData = this.$refs.form.getFormData()
      this.rootChangeLoading(true)

      let timePoint = [
        {
          start_time: 0,
          cut_off_time: formData.cutoffType * 24 * 60 * 60 + getTimeOffset(formData.cutoffTime),
          inventory: formData.isLimited ? formData.inventory : MAX_INVENTORY
        }
      ]
      if (formData.type === 2) {
        timePoint = formData.timeslotList.map((item) => ({
          start_time: getTimeOffset(item.startTime),
          cut_off_time: item.cutoffType * 24 * 60 * 60 + getTimeOffset(item.cutoffTime),
          inventory: item.isLimited ? item.inventory : MAX_INVENTORY
        }))
      }

      const res = await ajax.postBody(ADMIN_API.act.create_schedule, {
        data: {
          package_id: this.packageId,
          start_date: formData.dateRange[0],
          end_date: formData.dateRange[1],
          is_auto_extend: formData.isAutoExtend,
          block_out_date: formData.isBlockOut
            ? formData.blockoutDateList.map((item) => moment(item).format('YYYY-MM-DD'))
            : [],
          weekly: this.getWeekly(formData.weekdays, formData.dateRange),
          time_point: timePoint
        }
      })

      if (res.success) {
        this.$message.success(this.$t('global_create_success'))
        this.$emit('update')
        this.$emit('close')
        this.$refs.form.reset()
        this.handleGTMBtn({
          customPath: 'package_extra_time'
        })
        await this.saveStepStatus()
      } else {
        this.$message.error(this.$t('global_create_error'))
      }

      this.rootChangeLoading(false)
    },
    async saveStepStatus() {
      await this.$store.dispatch('updatePkgStepStatus2action', {
        activity_id: this.activityId,
        package_id: this.packageId,
        language: 'ALL',
        status: 1,
        step: 'timeslot'
      })
      klook.bus.$emit('updatePkgInfos2bus')
    }
  }
}
</script>

<style lang="scss">
.package_schedule-timeslot {
  padding-bottom: 60px;

  &_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(0, 0, 0, 0.09);

    h3 {
      margin: 0;
      font-size: 16px;
      line-height: 36px;
    }
  }

  &_footer {
    position: absolute;
    padding: 10px 16px;
    right: 0;
    bottom: 0;
    width: 100%;

    border-top: 1px solid #e9e9e9;
    background: #fff;
    text-align: right;
    z-index: 3;
  }
}
</style>
