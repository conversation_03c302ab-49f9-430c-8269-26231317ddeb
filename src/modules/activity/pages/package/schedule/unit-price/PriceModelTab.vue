<template>
  <div>
    <a-spin :spinning="isTakeRateLoading">
      <TakeRateTips
        v-if="overrideTakeRate || lastTakeRate"
        style="margin: 0 0 10px;"
        :override-take-rate="overrideTakeRate"
        :last-take-rate="lastTakeRate"
      />
    </a-spin>
    <a-tabs v-model="proxyActiveUnit">
      <a-tab-pane
        v-for="unit in unitList"
        :key="unit.sku_id"
        :tab="getName(unit)"
      />
    </a-tabs>
    <component
      ref="tabForm"
      :form="form"
      :is="isAdmin ? 'BasicTakeRateForm' : 'BasicPriceForm'"
      :cost-currency="globalForm.merchantCurrency"
      :selling-currency="globalForm.sellingCurrency"
      :exchange-rate="exchangeRate"
    />
  </div>
</template>

<script>
import { isAdmin } from '@/env'
import { bestMatchLang } from '@activity/utils'
import TakeRateTips from '@activity/pages/components/TakeRateTips.vue'
// import BasicPriceForm from '@activity/pages/components/BasicPriceForm.vue'
// import BasicTakeRateForm from '@activity/pages/components/BasicTakeRateForm.vue'

export default {
  name: 'PriceModelTab',
  components: {
    TakeRateTips,
    BasicPriceForm: () =>
      import(
        /* webpackChunkName: "BasicPriceForm" */ '@activity/pages/components/BasicPriceForm.vue'
      ),
    BasicTakeRateForm: () =>
      import(
        /* webpackChunkName: "BasicTakeRateForm" */ '@activity/pages/components/BasicTakeRateForm.vue'
      )
  },
  inject: ['globalForm', 'activityId'],
  props: {
    unitList: {
      type: Array,
      required: true
    },
    visible: {
      type: Boolean,
      default: false
    },
    exchangeRate: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      isAdmin,

      activeUnit: null,
      form: {
        costPrice: '',
        retailPrice: '',
        sellingPrice: '',
        specialSellingPriceList: [],
        takeRate: ''
      },
      data: [],
      lastTakeRate: '',
      overrideTakeRate: '',
      isTakeRateLoading: false
    }
  },
  computed: {
    proxyActiveUnit: {
      get() {
        return this.activeUnit
      },
      async set(val) {
        // 这里参照老admin的设计，在切换之前先检查一下表单
        const valid = await this.$refs.tabForm.validateForm()

        if (!valid) {
          return
        }

        this.collectData()
        this.setForm(val)
        this.activeUnit = val
      }
    }
  },
  watch: {
    visible: {
      immediate: true,
      async handler(val) {
        if (val) {
          // 打开的时候设置默认tab
          this.unitList.length > 0 &&
            (this.activeUnit = this.unitList[0].sku_id)

          // admin的时候需要展示 take rate
          if (this.isAdmin) {
            await this.getTakeRate()
          }
        } else {
          // 关闭的时候重置表单,
          this.$nextTick(() => {
            this.reset()
          })
        }
      }
    }
  },
  methods: {
    async getTakeRate() {
      this.isTakeRateLoading = true

      const res = await ajax.get(ADMIN_API.act.query_activity_take_rate, {
        params: {
          activity_id: this.activityId
        }
      })

      this.isTakeRateLoading = false
      this.lastTakeRate = res.last_take_rate
      this.overrideTakeRate = res.override_take_rate
    },
    getName(unit) {
      return bestMatchLang('unit_name', 'language', unit.local)
    },
    collectData() {
      const specialSellingPrice = this.isAdmin
        ? this.$refs.tabForm.getSpecialSellingPriceDataWithoutCache()
        : []
      this.data = this.data.filter((item) => item.sku_id !== this.activeUnit)
      this.data.push({
        sku_id: this.activeUnit,
        cost_price: this.form.costPrice,
        retail_price: this.form.retailPrice,
        selling_price: this.form.sellingPrice,
        cost_currency: this.globalForm.merchantCurrency,
        selling_currency: this.globalForm.sellingCurrency,
        special_selling_price: _.cloneDeep(specialSellingPrice)
      })
    },
    setForm(skuId) {
      // 回填数据，如果没找到则置空
      const target = this.data.find((item) => item.sku_id === skuId)

      if (target) {
        this.form.costPrice = target.cost_price
        this.form.retailPrice = target.retail_price
        this.form.sellingPrice = target.selling_price
        this.form.specialSellingPriceList = target.special_selling_price
        this.isAdmin && this.$refs.tabForm.computeTakeRate()
      } else {
        this.form = _.cloneDeep(this.$options.data().form)
      }
    },
    checkValid() {
      this.collectData()

      // 检查是否所有的unit都填了，如果每填，则自动跳到那一页，并且检查表单
      const validSkuIdList = this.data.map((item) => item.sku_id)
      const invalidUnitList = this.unitList.filter(
        (item) => !validSkuIdList.includes(item.sku_id)
      )

      if (invalidUnitList.length === 0) {
        return true
      }

      this.proxyActiveUnit = invalidUnitList[0].sku_id
    },
    reset() {
      this.activeUnit = null
      this.form = _.cloneDeep(this.$options.data().form)
      this.data = []
    },
    getFormData() {
      return this.data
    }
  }
}
</script>
