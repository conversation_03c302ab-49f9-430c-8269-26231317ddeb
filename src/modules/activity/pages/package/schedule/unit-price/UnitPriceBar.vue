<template>
  <div class="unit-price-bar" :class="{ expand: tempId === id }">
    <div
      class="unit-price-bar_header"
      :class="{ modified: modified }"
      @click="handleHeaderClick"
    >
      <UnitPriceBarSlot style="width: 120px">
        <template #title>
          <span>
            {{ skuTitle }}
          </span>
          <a-tooltip v-if="isMainSku" placement="top" arrow-point-at-center>
            <template slot="title">
              <span>{{ $t('package_unit_main_sku_tips') }}</span>
            </template>
            <a-icon
              type="question-circle"
              theme="filled"
              style="margin-left: 4px; color: rgba(0, 0, 0, 0.25);"
            />
          </a-tooltip>
        </template>
        <template #content>
          {{ unitName }}
        </template>
      </UnitPriceBarSlot>
      <UnitPriceBarSlot style="width: 120px;">
        <template #title>
          <span>Sku id:</span>
        </template>
        <template #content>
          {{ data.sku_id || '-' }}
        </template>
      </UnitPriceBarSlot>
      <UnitPriceBarSlot style="width: 100px;">
        <template #title> {{ $t('package_list_status') }}: </template>
        <template #content>
          <span :style="{ color: data.published ? '#36B37E' : '#FFAB00' }">
            {{ getStatus(data) }}
          </span>
        </template>
      </UnitPriceBarSlot>
      <UnitPriceBarSlot style="width: 100px;">
        <template #title> {{ $t('package_price_cost') }}: </template>
        <template #content>
          {{
            getPrice('cost_price') === '-' ? '' : globalForm.merchantCurrency
          }}
          {{ getPrice('cost_price') | formatThousands }}
        </template>
      </UnitPriceBarSlot>
      <UnitPriceBarSlot style="width: 100px;">
        <template #title> {{ $t('package_price_retail') }}: </template>
        <template #content>
          {{
            getPrice('retail_price') === '-' ? '' : globalForm.sellingCurrency
          }}
          {{ getPrice('retail_price') | formatThousands }}
        </template>
      </UnitPriceBarSlot>
      <div style="width: 125px; text-align: right">
        <a-popconfirm
          v-if="data.sku_id && !isMainSku"
          :title="$t('package_unit_publish_warning', publishText)"
          :ok-text="$t('global_button_ok')"
          :cancel-text="$t('global_button_cancel')"
          placement="topRight"
          @confirm="handlePublish(data.sku_id)"
        >
          <a-button
            type="primary"
            size="small"
            :loading="isPublishLoading"
            :disabled="isAdminEm"
            style="min-width: 90px"
            @click.stop
          >
            {{ publishText }}
          </a-button>
        </a-popconfirm>
      </div>
      <div style="width: 16px;">
        <div v-show="showDrag" class="handle" style="width: 100%;">
          <svg-icon icon-name="menu" />
        </div>
      </div>
    </div>

    <!-- 下面的框 -->
    <div class="unit-price-bar_content">
      <div v-if="!data.published" class="unit-price-bar_content-operation">
        <a-popconfirm
          v-if="data.sku_id"
          :title="$t('js_confirm_delete')"
          :ok-text="$t('global_button_ok')"
          :cancel-text="$t('global_button_cancel')"
          placement="topRight"
          @confirm="handleDelete(data.sku_id)"
        >
          <a-button ghost type="danger" size="small">
            {{ $t('global_delete') }}
          </a-button>
        </a-popconfirm>
        <a-button
          v-else
          ghost
          type="danger"
          size="small"
          @click="handleDelete(data.sku_id)"
        >
          {{ $t('global_delete') }}
        </a-button>
      </div>
      <div class="unit-price-bar_content-form">
        <UnitPriceForm
          ref="form"
          :default-value="data"
          :exchange-rate="exchangeRate"
          @updateModified="handleUpdateModified"
        />
      </div>
      <div class="unit-price-bar_content-operation">
        <a-button style="margin-right: 12px;" @click="handleCancel">
          {{ $t('global_button_cancel') }}
        </a-button>
        <a-button type="primary" @click="handleSave">
          {{ $t('global_save') }}
        </a-button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import {
  getEditLang,
  getRefLang,
  bestMatchLang,
  formatThousands,
  scrollFormErrorIntoView
} from '@activity/utils'
import { MAX_UNIT } from '@activity/pages/package/package_const.js'
import UnitPriceBarSlot from './UnitPriceBarSlot.vue'
import UnitPriceForm from './UnitPriceForm.vue'

export default {
  name: 'UnitPriceBar',
  inject: [
    'rootChangeLoading',
    'globalForm',
    'packageId',
    'activityId',
    'unitPrice'
  ],
  components: {
    UnitPriceBarSlot,
    UnitPriceForm
  },
  filters: {
    formatThousands
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    tempId: {
      type: [Number, String],
      default: null
    },
    showDrag: {
      type: Boolean,
      default: true
    },
    mainUnit: {
      type: Object,
      default: null
    },
    exchangeRate: {
      type: [String, Number],
      default: ''
    },
    forbidPublish: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isPublishLoading: false,
      modified: false
    }
  },
  computed: {
    ...mapState({
      isAdminEm: (state) => state.isAdminEm
    }),
    unitName() {
      if (!this.data || !this.data.local) {
        return '-'
      }
      return bestMatchLang('unit_name', 'language', this.data.local)
    },
    id() {
      // 对于已保存的，取sku_id，未保存的，取id
      return this.data.sku_id || this.data.id
    },
    isMainSku() {
      return (
        this.mainUnit &&
        this.mainUnit.sku_id &&
        this.data.sku_id === this.mainUnit.sku_id
      )
    },
    skuTitle() {
      return this.isMainSku
        ? `${this.$t('package_unit_main')} ${this.$t('package_unit_selector')}`
        : this.$t('package_unit_selector')
    },
    publishText() {
      return this.data.published
        ? this.$t('package_unit_unpublish')
        : this.$t('package_unit_publish')
    }
  },
  watch: {
    tempId(newVal, oldVal) {
      // 当收起的时候检查是否显示小红点
      if (newVal !== this.id && oldVal === this.id) {
        this.modified = this.$refs.form.checkModified()
      }
    }
  },
  methods: {
    handleHeaderClick() {
      this.$emit('changeTempId', this.tempId === this.id ? null : this.id)
    },
    getPrice(field) {
      return _.get(this.data, ['price', field], '-')
    },
    getStatus(data) {
      if (!this.data || !this.data.sku_id) {
        return '-'
      }
      return this.data.published
        ? this.$t('package_list_unit_published')
        : this.$t('package_list_unit_unpublished')
    },
    handleUpdateModified(val) {
      this.modified = val
    },
    async handlePublish(skuId) {
      if (this.forbidPublish && !this.data.published) {
        this.$error({
          title: 'Error',
          content: this.$t('28993')
        })
        return
      }

      this.isPublishLoading = true

      const res = await ajax.postBody(ADMIN_API.act.published_sku, {
        data: {
          sku_id: skuId,
          language: getEditLang(),
          published: !this.data.published
        }
      })

      this.isPublishLoading = false

      if (res.success) {
        this.$message.success(this.$t('global_success'))
        this.$emit('getData')

        // 更新外部价格日历
        this.unitPrice.$emit('update')
      } else {
        this.$message.error(this.$t('global_failure'))
      }
    },
    async handleDelete(skuId) {
      // 如果没有skuId，则表示是新增的，直接删除即可
      if (!skuId) {
        this.$emit('delete', this.id)
        this.$emit('changeTempId', null)
        return
      }

      this.rootChangeLoading(true)
      const res = await ajax.postBody(ADMIN_API.act.destroy_sku, {
        data: {
          sku_id: skuId
        }
      })
      this.rootChangeLoading(false)

      if (res.success) {
        this.$message.success(this.$t('global_delete_success'))
        this.$emit('getData')

        // 更新外部价格日历
        this.unitPrice.$emit('update')
      } else {
        this.$message.error(this.$t('global_delete_error'))
      }
    },
    handleCancel() {
      this.$refs.form.cancelEdit()
      this.$emit('changeTempId', null)
    },
    async handleSave() {
      const valid = await this.$refs.form.validateForm()

      if (!valid) {
        scrollFormErrorIntoView()
        return
      }

      if (this.data.sku_id) {
        await this.updateUnit()
      } else {
        await this.createUnit()
      }
    },
    async updateUnit() {
      const formData = this.$refs.form.getFormData()
      const params = {
        package_id: this.packageId,
        sku_id: this.data.sku_id,
        unit_type: formData.unitType,
        local: [
          {
            language: getEditLang(),
            unit_name: formData.unitName
          }
        ],
        use_customized_name: formData.useCustomizedName ? 1 : 0,
        min_age_range: Number(formData.minAge),
        max_age_range:
          formData.unitType === 4 && !formData.isAgeLimited
            ? 0
            : Number(formData.maxAge),
        required: formData.required ? 1 : 0,
        min_num: formData.isNumLimited ? Number(formData.minNum) : 0,
        max_num: formData.isNumLimited ? Number(formData.maxNum) : MAX_UNIT
      }

      if (formData.useCustomizedName || formData.unitType === 5) {
        const editLang = getEditLang()
        const refLang = getRefLang()

        if (formData.unitNameRef && editLang !== refLang) {
          params.local.push({
            language: refLang,
            unit_name: formData.unitNameRef
          })
        }

        if (formData.unitNameAdditional && editLang !== 'en_US') {
          params.local.push({
            language: 'en_US',
            unit_name: formData.unitNameAdditional
          })
        }
      }

      this.rootChangeLoading(true)
      const res = await ajax.postBody(ADMIN_API.act.update_sku, {
        data: params
      })
      this.rootChangeLoading(false)

      if (res.success) {
        this.$message.success(this.$t('act_save_success'))
        this.$emit('getData')

        // 更新外部价格日历
        this.unitPrice.$emit('update')
      } else {
        this.$message.error(this.$t('act_save_fail'))
      }
    },
    async createUnit() {
      const formData = this.$refs.form.getFormData()
      const params = {
        activity_id: this.activityId,
        package_id: this.packageId,
        unit_type: formData.unitType,
        local: [
          {
            language: getEditLang(),
            unit_name: formData.unitName
          }
        ],
        min_age_range: Number(formData.minAge),
        max_age_range:
          formData.unitType === 4 && !formData.isAgeLimited
            ? 0
            : Number(formData.maxAge),
        required: formData.required ? 1 : 0,
        min_num: formData.isNumLimited ? Number(formData.minNum) : 0,
        max_num: formData.isNumLimited ? Number(formData.maxNum) : MAX_UNIT,
        package_pricing_model: this.globalForm.priceModel,
        use_customized_name: formData.useCustomizedName ? 1 : 0,
        price: {
          cost_currency: this.globalForm.merchantCurrency,
          cost_price: Number(formData.costPrice),
          retail_price: Number(formData.retailPrice),
          selling_price: Number(formData.sellingPrice),
          selling_currency: this.globalForm.sellingCurrency,
          special_selling_price: formData.specialSellingPriceList.map(
            (item) => ({
              ...item,
              price: Number(item.price)
            })
          )
        }
      }

      // 自定义unitName的时候，如果编辑语言不是英文，则需要输入英文
      if (formData.useCustomizedName || formData.unitType === 5) {
        const editLang = getEditLang()
        const refLang = getRefLang()

        if (formData.unitNameRef && editLang !== refLang) {
          params.local.push({
            language: refLang,
            unit_name: formData.unitNameRef
          })
        }

        if (formData.unitNameAdditional && editLang !== 'en_US') {
          params.local.push({
            language: 'en_US',
            unit_name: formData.unitNameAdditional
          })
        }
      }

      this.rootChangeLoading(true)
      const res = await ajax.postBody(ADMIN_API.act.create_sku_and_price, {
        data: params
      })
      this.rootChangeLoading(false)

      if (res.success) {
        this.$message.success(this.$t('act_save_success'))
        // 这里后端返回不了unit信息
        this.$emit('getData')

        // 需要删除newUnit
        this.$emit('delete', this.id)
        this.$emit('changeTempId', this.data.sku_id)

        // 更新外部价格日历
        this.unitPrice.$emit('update')

        // 更新step
        await this.$store.dispatch('updatePkgStepStatus2action', {
          activity_id: this.activityId,
          package_id: this.packageId,
          language: 'ALL',
          status: 1,
          step: 'price'
        })
        klook.bus.$emit('updatePkgInfos2bus')
      } else {
        this.$message.error(this.$t('act_save_fail'))
      }
    }
  }
}
</script>

<style lang="scss">
.unit-price-bar {
  position: relative;

  p {
    margin: 0;
  }
  &_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;

    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 4px;

    &:hover {
      background: rgba(0, 145, 255, 0.05);
    }

    &.modified {
      border-color: rgba(255, 171, 0, 0.45);

      &:hover {
        background: rgba(255, 171, 0, 0.05);
      }
    }
  }
  &_content {
    display: none;
    padding: 12px;
    background: #fafafa;
    border-radius: 4px;

    &-operation {
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }

    &-form {
      margin: 10px 0 20px;
    }
  }

  &.expand {
    .unit-price-bar_header {
      background: rgba(0, 145, 255, 0.05);

      &.modified {
        background: rgba(255, 171, 0, 0.05);
      }
    }
    .unit-price-bar_content {
      display: block;
    }
  }
}
</style>
