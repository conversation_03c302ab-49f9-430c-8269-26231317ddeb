<template>
  <div class="unit-price-bar-slot">
    <h4>
      <slot name="title" />
    </h4>
    <OverflowTextTooltip>
      <slot name="content" />
    </OverflowTextTooltip>
  </div>
</template>

<script>
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'

export default {
  name: 'UnitPriceBarSlot',
  components: {
    OverflowTextTooltip
  }
}
</script>

<style lang="scss">
.unit-price-bar-slot {
  h4 {
    margin: 4px 0;

    font-size: 10px;
    line-height: 12px;
    font-weight: normal;
    color: rgba(0, 0, 0, 0.45);

    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .overflow-text {
    display: block;
    margin: 0;
    font-size: 12px;
    line-height: 12px;
    color: rgba(0, 0, 0, 0.85);

    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
