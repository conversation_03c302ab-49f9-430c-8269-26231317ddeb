<template>
  <a-form-model class="unit-price-form" ref="form" layout="vertical" :model="form" :rules="rules">
    <a-form-model-item :label="$t('package_unit_type')" style="margin-bottom: 10px;" prop="unitType">
      <a-button
        v-for="unit in unitOptions"
        :key="unit.value"
        :disabled="isUnitSaved || isAdminEm"
        :class="unit.value === form.unitType ? 'is-active' : ''"
        class="unit-price-form-unit-type"
        @click="handleUnitClick(unit.value, unit.text)"
      >
        {{ unit.text }}
      </a-button>
    </a-form-model-item>
    <a-form-model-item
      v-if="isAdmin && canCustomizedUnitName"
      style="margin-bottom: 6px;"
      prop="useCustomizedName"
    >
      <div>
        <label>
          <span style="color: rgba(0, 0, 0, 0.85);">
            {{ $t('package_customized_unit_name') }}
            <a-tooltip placement="top" arrow-point-at-center>
              <template slot="title">
                {{ $t('23123') }}
              </template>
              <a-icon
                type="question-circle"
                theme="filled"
                style="margin-left: 4px; color: rgba(0, 0, 0, 0.25);"
              />
            </a-tooltip>
          </span>
          <a-switch v-model="form.useCustomizedName" style="margin-left: 12px;" />
        </label>
      </div>
    </a-form-model-item>
    <a-form-model-item
      v-if="form.useCustomizedName || form.unitType === 5"
      :label="$t('package_unit_name')"
      style="margin-bottom: 16px;"
      prop="unitName"
    >
      <a-input
        v-model="form.unitName"
        style="width: 500px; display: block;"
        :placeholder="getUnitNamePlaceholder(getEditLang())"
      />
      <a-input
        v-if="useUnitNameRef"
        v-model="form.unitNameRef"
        style="width: 500px; display: block; margin-top: 12px;"
        :disabled="isUnitNameRefDisabled"
        :placeholder="getUnitNamePlaceholder(getRefLang())"
      />
      <a-input
        v-if="useUnitNameAdditional"
        v-model="form.unitNameAdditional"
        style="width: 500px; display: block; margin-top: 12px;"
        :placeholder="getUnitNamePlaceholder('en_US')"
      />
    </a-form-model-item>
    <!-- <a-form-model-item
      v-if="[1, 2, 3, 4].includes(form.unitType)"
      :label="$t('package_unit_age_number')"
      style="margin-bottom: 16px;"
      prop="isAgeLimited"
      required
    >
      <a-radio-group :disabled="isAdminEm" v-model="form.isAgeLimited">
        <a-radio :value="false">
          {{ $t('package_inventory_unlimited') }}
        </a-radio>
        <a-radio :value="true">
          {{ $t('package_unit_limited') }}
        </a-radio>
      </a-radio-group>
    </a-form-model-item> -->
    <a-form-model-item
      v-if="canShowAgeList.includes(form.unitType)"
      :label="$t('package_min_max_age')"
      style="margin-bottom: 16px;"
      prop="minAge"
    >
      <a-input-number
        v-model="form.minAge"
        :disabled="isAdminEm"
        :min="0"
        :max="MAX_AGE"
        style="width: 180px;"
      />
      -
      <a-select
        v-if="form.unitType === 4"
        :value="form.isAgeLimited ? 1 : 0"
        style="width: 180px; margin-right: 10px"
        @change="handleAgeLimitedChange"
      >
        <a-select-option v-for="ageOption in ageOptions" :key="ageOption.label" :value="ageOption.value">
          {{ ageOption.label }}
        </a-select-option>
      </a-select>
      <a-input-number
        v-if="showMaxAge"
        v-model="form.maxAge"
        :min="0"
        :max="MAX_AGE"
        :disabled="isAdminEm"
        style="width: 180px;"
        @blur="handleMaxAgeblur"
      />
      <span class="widespan">&nbsp;Age</span>
    </a-form-model-item>
    <a-form-model-item :label="$t('package_unit_buy')" style="margin-bottom: 16px;" prop="required" required>
      <BasicFormPopover
        :message="$t('package_unit_requirement_tips')"
        :auto-adjust-overflow="false"
        trigger="hover"
        placement="right"
      >
        <a-radio-group v-model="form.required" :disabled="isAdminEm" @change="handleRequiredChange">
          <a-radio :value="true">
            {{ $t('global_yes') }}
          </a-radio>
          <a-radio :value="false">
            {{ $t('global_no') }}
          </a-radio>
        </a-radio-group>
      </BasicFormPopover>
    </a-form-model-item>
    <a-form-model-item
      :label="$t('package_unit_number')"
      style="margin-bottom: 16px;"
      prop="isNumLimited"
      required
    >
      <a-radio-group
        v-model="form.isNumLimited"
        :disabled="isTripMapped || isAdminEm"
        @change="handleIsNumLimitedChange"
      >
        <a-radio :value="false">
          {{ $t('package_unlimited') }}
        </a-radio>
        <a-radio :value="true">
          {{ $t('package_unit_limited') }}
        </a-radio>
      </a-radio-group>
    </a-form-model-item>
    <a-form-model-item
      v-if="form.isNumLimited"
      :label="$t('package_min_max_units')"
      style="margin-bottom: 16px;"
      prop="minNum"
    >
      <a-input-number
        v-model="form.minNum"
        :min="0"
        :disabled="isTripMapped || isAdminEm"
        style="width: 300px;"
      />
      -
      <a-input-number
        v-model="form.maxNum"
        :max="MAX_UNIT - 1"
        :disabled="isTripMapped || isAdminEm"
        style="width: 300px;"
        @blur="handleMaxNumblur"
      />
      <span class="widespan">&nbsp;{{ $t('package_price_units') }}</span>
    </a-form-model-item>
    <!-- 先不要这个表单 -->
    <!-- <a-form-model-item
      label="Voucher Type"
      style="margin-bottom: 16px;"
      prop="zeroPriceSelect"
      required
    >
      <a-radio-group v-model="form.zeroPriceSelect">
        <a-radio :value="0">
          Regular
        </a-radio>
        <a-radio :value="2">
          Free Voucher
        </a-radio>
        <a-radio :value="1">
          Reservation Service
        </a-radio>
      </a-radio-group>
    </a-form-model-item> -->
    <div v-if="globalForm.priceModel !== null && !isAdmin" style="display: flex;">
      <a-form-model-item
        :label="globalForm.priceModel === 0 ? $t('package_price_cost') : $t('package_default_price_cost')"
        style="width: 300px; margin-bottom: 16px; margin-right: 24px;"
        prop="costPrice"
      >
        <a-input
          v-model="form.costPrice"
          :placeholder="$t('global_please_input')"
          :suffix="globalForm.merchantCurrency"
          style="width: 100%;"
          :disabled="isUnitSaved || isAdminEm"
          @keyup.native="handleRoundPrice('costPrice')"
          @blur="handleBlur('costPrice')"
        />
      </a-form-model-item>
      <a-form-model-item
        :label="globalForm.priceModel === 0 ? $t('package_price_retail') : $t('package_default_price_retail')"
        style="width: 300px; margin-bottom: 16px;"
        prop="retailPrice"
      >
        <a-input
          v-model="form.retailPrice"
          :placeholder="$t('global_please_input')"
          :suffix="globalForm.sellingCurrency"
          style="width: 100%;"
          :disabled="isUnitSaved || isAdminEm"
          @keyup.native="handleRoundPrice('retailPrice')"
          @blur="handleBlur('retailPrice')"
        />
      </a-form-model-item>
    </div>

    <template v-else-if="globalForm.priceModel !== null && isAdmin">
      <div style="display: flex;">
        <a-form-model-item
          :label="globalForm.priceModel === 0 ? $t('package_price_cost') : $t('package_default_price_cost')"
          style="width: 300px; margin-bottom: 16px; margin-right: 24px;"
          prop="costPrice"
        >
          <a-input
            v-model="form.costPrice"
            :placeholder="$t('global_please_input')"
            :suffix="globalForm.merchantCurrency"
            style="width: 100%;"
            :disabled="isUnitSaved || isAdminEm"
            @keyup.native="handleRoundPrice('costPrice')"
            @blur="handleAdminCostPriceBlur"
          />
        </a-form-model-item>
        <a-form-model-item
          :label="
            globalForm.priceModel === 0 ? $t('package_price_retail') : $t('package_default_price_retail')
          "
          style="width: 300px; margin-bottom: 16px;"
          prop="retailPrice"
        >
          <a-input
            v-model="form.retailPrice"
            :placeholder="$t('global_please_input')"
            :suffix="globalForm.sellingCurrency"
            style="width: 100%;"
            :disabled="isUnitSaved || isAdminEm"
            @keyup.native="handleRoundPrice('retailPrice')"
            @blur="handleBlur('retailPrice')"
          />
        </a-form-model-item>
      </div>
      <div style="display: flex;">
        <a-form-model-item
          :label="
            globalForm.priceModel === 0 ? $t('package_price_selling') : $t('package_default_price_selling')
          "
          prop="sellingPrice"
          style="width: 300px; margin-bottom: 16px; margin-right: 24px;"
        >
          <a-input
            v-model="form.sellingPrice"
            :suffix="globalForm.sellingCurrency"
            :placeholder="$t('global_please_input')"
            style="width: 100%;"
            :disabled="isUnitSaved || isAdminEm"
            @keyup.native="handleRoundPrice('sellingPrice')"
            @blur="computeTakeRate"
          />
        </a-form-model-item>
        <a-form-model-item
          :label="$t('new_take_rate')"
          prop="takeRate"
          style="width: 300px; margin-bottom: 16px;"
        >
          <a-input
            v-model="form.takeRate"
            suffix="%"
            style="width: 100%;"
            :placeholder="$t('global_please_input')"
            :disabled="isUnitSaved || isAdminEm"
            @keyup.native="handleRoundTakeRate"
            @blur="handleTakeRateBlur"
          />
        </a-form-model-item>
      </div>
      <a-form-model-item
        :label="$t('package_custom_selling')"
        prop="specialSellingPriceList"
        style="width: 625px; margin-bottom: 16px;"
      >
        <SpecialSellingPriceList
          ref="ssp"
          :price="form.sellingPrice"
          :disabled="isUnitSaved || isAdminEm"
          :selling-currency="globalForm.sellingCurrency"
          :special-selling-price-list="form.specialSellingPriceList"
        />
      </a-form-model-item>
    </template>
    <GeneralAlert
      v-if="globalForm.priceModel === 1"
      :show-icon="false"
      :message="$t('package_unit_type_price_warning')"
      banner
    />
  </a-form-model>
</template>

<script>
import { isAdmin } from '@/env'
import { mapState, mapGetters } from 'vuex'
import {
  matchLang,
  getEditLang,
  getRefLang,
  round2Decimal,
  provingTakeRate,
  computeTR,
  setTRAndGetBase,
  setCostAndGetBase
} from '@activity/utils'
import {
  MAX_AGE,
  MAX_UNIT,
  isSPUType,
  isHotelType,
  isNotSPUType
} from '@activity/pages/package/package_const.js'
import GeneralAlert from '@activity/pages/components/GeneralAlert.vue'
import BasicFormPopover from '@activity/pages/components/BasicFormPopover.vue'
import SpecialSellingPriceList from '@activity/pages/components/SpecialSellingPriceList.vue'

export default {
  name: 'UnitPriceForm',
  inject: ['globalForm'],
  components: {
    GeneralAlert,
    BasicFormPopover,
    SpecialSellingPriceList
  },
  props: {
    defaultValue: {
      type: Object,
      default: null
    },
    exchangeRate: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    const validateUnitType = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('global_please_input')))
      } else {
        callback()
      }
    }
    const validateUnitName = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('global_please_input')))
      } else if (this.useUnitNameRef && !this.isUnitNameRefDisabled && !this.form.unitNameRef) {
        callback(new Error(this.$t('global_please_input')))
      } else if (this.useUnitNameAdditional && !this.form.unitNameAdditional) {
        callback(new Error(this.$t('global_please_input')))
      } else {
        callback()
      }
    }
    const validateMinAge = (rule, value, callback) => {
      if (!this.form.minAge && this.form.minAge !== 0) {
        callback(new Error(this.$t('global_please_input')))
      } else if (!_.isInteger(this.form.minAge)) {
        callback(new Error(this.$t('package_unit_age_integer')))
      } else if (this.showMaxAge && !this.form.maxAge) {
        callback(new Error(this.$t('global_please_input')))
      } else if (this.showMaxAge && !_.isInteger(this.form.maxAge)) {
        callback(new Error(this.$t('package_unit_age_integer')))
      } else if (this.showMaxAge && this.form.minAge > this.form.maxAge) {
        callback(new Error(this.$t('package_unit_age_comparison')))
      } else {
        callback()
      }
    }
    const validateNumLimited = (rule, value, callback) => {
      if (this.form.required && !this.form.isNumLimited) {
        callback(new Error(this.$t('package_unit_limited_when_required')))
      } else {
        callback()
      }
    }
    const validateMinNum = (rule, value, callback) => {
      if (this.form.required && this.form.minNum < 1) {
        callback(new Error(this.$t('package_unit_min_num_when_required')))
      } else if (!this.form.isNumLimited) {
        callback()
      } else if (!this.form.minNum && this.form.minNum !== 0) {
        callback(new Error(this.$t('global_please_input')))
      } else if (!_.isInteger(this.form.minNum)) {
        callback(new Error(this.$t('package_unit_num_integer')))
      } else if (!this.form.maxNum && this.form.maxNum !== 0) {
        callback(new Error(this.$t('global_please_input')))
      } else if (!_.isInteger(this.form.maxNum)) {
        callback(new Error(this.$t('package_unit_num_integer')))
      } else if (this.form.minNum > this.form.maxNum) {
        callback(new Error(this.$t('package_unit_num_comparison')))
      } else {
        callback()
      }
    }
    const validatePrice = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('global_please_input')))
      } else {
        callback()
      }
    }
    const validateSpecialSellingPriceList = (rule, value, callback) => {
      const isValid = value.every((item) => !!item.price)

      if (isValid) {
        callback()
      } else {
        callback(new Error(this.$t('global_please_input')))
      }
    }
    return {
      isAdmin,
      MAX_AGE,
      MAX_UNIT,

      isInited: false,
      defaultEditLang: 'en_US',
      canShowAgeList: [1, 2, 3, 4, 17],
      canCustomizeUnitNameList: [0, 1, 2, 3, 4, 6, 16, 17],
      // 这里的表单要和unitPrice里面的表单一致
      form: {
        unitType: '',
        unitName: '',
        unitNameRef: '',
        unitNameAdditional: '',
        useCustomizedName: false,
        isAgeLimited: false,
        minAge: 0,
        maxAge: MAX_AGE,
        required: false,
        isNumLimited: false,
        minNum: 0,
        maxNum: MAX_UNIT - 1,
        // zeroPriceSelect: 0,
        costPrice: '',
        retailPrice: '',
        sellingPrice: '',
        takeRate: '',
        specialSellingPriceList: []
      },
      rules: {
        unitType: [
          { validator: validateUnitType, trigger: 'blur' },
          {
            required: true,
            message: __('global_please_input'),
            trigger: 'blur'
          }
        ],
        unitName: [
          { validator: validateUnitName, trigger: 'blur' },
          {
            required: true,
            message: __('global_please_input'),
            trigger: 'blur'
          }
        ],
        minAge: [
          { validator: validateMinAge, trigger: 'blur' },
          {
            required: true,
            message: __('global_please_input'),
            trigger: 'blur'
          }
        ],
        isNumLimited: [
          { validator: validateNumLimited, trigger: 'blur' },
          {
            required: true,
            message: __('global_please_select'),
            trigger: 'blur'
          }
        ],
        minNum: [
          { validator: validateMinNum, trigger: 'blur' },
          {
            required: true,
            message: __('global_please_input'),
            trigger: 'blur'
          }
        ],
        costPrice: [
          { validator: validatePrice, trigger: 'change' },
          {
            required: true,
            message: __('global_please_input'),
            trigger: 'blur'
          }
        ],
        retailPrice: [
          { validator: validatePrice, trigger: 'change' },
          {
            required: true,
            message: __('global_please_input'),
            trigger: 'blur'
          }
        ],
        sellingPrice: [
          { validator: validatePrice, trigger: 'change' },
          {
            required: true,
            message: __('global_please_input'),
            trigger: 'blur'
          }
        ],
        takeRate: [
          { validator: validatePrice, trigger: 'change' },
          {
            required: true,
            message: __('global_please_input'),
            trigger: 'blur'
          }
        ],
        specialSellingPriceList: [
          {
            validator: validateSpecialSellingPriceList,
            trigger: 'blur'
          }
        ]
      },
      ageOptions: [
        {
          label: __('27793'),
          value: 0
        },
        {
          label: 'Maximum',
          value: 1
        }
      ]
    }
  },
  computed: {
    ...mapState({
      isAdminEm: (state) => state.isAdminEm,
      categoryInfo: (state) => state.categoryInfo
    }),
    ...mapGetters(['isTripMapped']),
    isUnitSaved() {
      return !!this.defaultValue && !!this.defaultValue.sku_id
    },
    unitOptions() {
      // isSPUType、isHotelType
      const { sub_category_id } = this.categoryInfo
      const isSpu = [331, 7, 8, 9, 206, 14].includes(sub_category_id)
      const isHotel = sub_category_id === 14

      // not spu type
      if (!isSpu && sub_category_id !== 22) {
        return isNotSPUType
      }

      // hotel
      if (isHotel || sub_category_id === 22) {
        return [...isHotelType, ...isNotSPUType]
      }

      // spu type
      const filteredList = isSPUType.filter((item) => item.sub_category_id.includes(sub_category_id))

      return [
        ...filteredList,
        {
          text: __('28984'),
          value: 7
        }
      ]
    },
    canCustomizedUnitName() {
      return this.canCustomizeUnitNameList.includes(this.form.unitType)
    },
    isUnitNameRefDisabled() {
      return getRefLang() !== this.defaultEditLang
    },
    useUnitNameRef() {
      const { form, defaultEditLang } = this
      const refLang = getRefLang()
      return (!!form.useCustomizedName || this.form.unitType === 5) && refLang && refLang !== defaultEditLang
    },
    useUnitNameAdditional() {
      const { form, defaultEditLang } = this
      return (
        (!!form.useCustomizedName || this.form.unitType === 5) &&
        getEditLang() !== defaultEditLang &&
        getRefLang() !== defaultEditLang
      )
    },
    showMaxAge() {
      const { unitType, isAgeLimited } = this.form
      return this.canShowAgeList.includes(unitType) && (isAgeLimited || unitType !== 4)
    }
  },
  watch: {
    defaultValue: {
      immediate: true,
      handler() {
        // 这里为了保留用户编辑的内容，在刷新数据之后只初始化部分内容
        if (!this.isInited) {
          this.form = this.init()
          this.isInited = true
        } else {
          this.partialInit()
          this.$emit('updateModified', this.checkModified())
        }
      }
    },
    'globalForm.priceModel'() {
      // 如果没有保存的话，需要清空price
      if (!this.isUnitSaved) {
        this.form.costPrice = ''
        this.form.retailPrice = ''
        this.form.sellingPrice = ''
        this.form.takeRate = ''
      }
    },
    exchangeRate() {
      this.computeTakeRate()
    }
  },
  methods: {
    getEditLang,
    getRefLang,
    getUnitNamePlaceholder(lang) {
      const langI18n = this.$t(`act_lan_${lang}`)
      return `${this.$t('global_please_input')} ${langI18n}`
    },
    init() {
      let form = {}

      if (this.isUnitSaved) {
        // 初始化表单
        form.unitType = this.defaultValue.unit_type
        form.unitName = matchLang('unit_name', 'language', this.defaultValue.local, getEditLang()) || ''
        form.unitNameRef = matchLang('unit_name', 'language', this.defaultValue.local, getRefLang()) || ''
        form.unitNameAdditional =
          matchLang('unit_name', 'language', this.defaultValue.local, this.defaultEditLang) || ''
        form.isAgeLimited = this.defaultValue.max_age_range !== 0
        form.minAge = this.defaultValue.min_age_range
        form.maxAge = form.isAgeLimited ? this.defaultValue.max_age_range : MAX_AGE
        form.required = !!this.defaultValue.required
        form.isNumLimited = this.defaultValue.min_num !== 0 || this.defaultValue.max_num !== MAX_UNIT
        form.minNum = form.isNumLimited ? this.defaultValue.min_num : 0
        form.maxNum = form.isNumLimited ? this.defaultValue.max_num : MAX_UNIT - 1
        form.costPrice = this.defaultValue.price.cost_price
        form.retailPrice = this.defaultValue.price.retail_price
        form.sellingPrice = this.defaultValue.price.selling_price
        form.specialSellingPriceList = this.defaultValue.price.special_selling_price
        form.useCustomizedName = !!this.defaultValue.use_customized_name
        form.takeRate = computeTR(form.costPrice, form.sellingPrice, this.exchangeRate)

        // 如果本身没有自定义，则把值都清空
        if (!form.useCustomizedName && this.canCustomizedUnitName) {
          form.unitName = ''
          form.unitNameRef = ''
          form.unitNameAdditional = ''
        }
      } else {
        form = _.cloneDeep(this.$options.data().form)
        form.unitType = this.unitOptions[0].value
        // 清空里面的值
        // form.unitName = this.unitOptions[0].value === 5 ? '' : this.unitOptions[0].text
      }

      return form
    },
    partialInit() {
      // 部分初始化，只初始化price
      this.form.unitType = this.defaultValue.unit_type
      this.form.costPrice = this.defaultValue.price.cost_price
      this.form.retailPrice = this.defaultValue.price.retail_price
      this.form.sellingPrice = this.defaultValue.price.selling_price
      this.computeTakeRate()
      this.form.specialSellingPriceList = this.defaultValue.price.special_selling_price
    },
    cancelEdit() {
      this.form = this.init()
    },
    checkModified() {
      const defaultForm = this.init()

      // 这里不能用 JSON.stringify 进行比较，因为在unlimited的时候，min和max要忽略掉
      // 这里只比较能改动的
      if (defaultForm.unitType !== this.form.unitType) {
        return true
      }

      if (defaultForm.useCustomizedName !== this.form.useCustomizedName) {
        return true
      }

      if (
        defaultForm.useCustomizedName &&
        (defaultForm.unitName !== this.form.unitName ||
          defaultForm.unitNameRef !== this.form.unitNameRef ||
          defaultForm.unitNameAdditional !== this.form.unitNameAdditional)
      ) {
        return true
      }

      if (defaultForm.isAgeLimited !== this.form.isAgeLimited) {
        return true
      }

      if (defaultForm.isAgeLimited && this.form.isAgeLimited) {
        return defaultForm.minAge !== this.form.minAge || defaultForm.maxAge !== this.form.maxAge
      }

      if (defaultForm.required !== this.form.required) {
        return true
      }

      if (defaultForm.isNumLimited !== this.form.isNumLimited) {
        return true
      }

      if (defaultForm.isNumLimited && this.form.isNumLimited) {
        return defaultForm.minNum !== this.form.minNum || defaultForm.maxNum !== this.form.maxNum
      }

      return false
    },
    handleUnitClick(unitType, unitName) {
      this.form.unitType = unitType
      // unit_type === 5 is other, so should set empty string
      // this.form.unitName = unitType === 5 ? '' : unitName
      this.form.unitName = ''
      this.form.unitNameRef = ''
      this.form.unitNameAdditional = ''

      if (!this.canCustomizeUnitNameList.includes(unitType) && unitType !== 5) {
        this.form.useCustomizedName = false
      }
    },
    handleMaxNumblur() {
      this.$refs.form.validateField('minNum')
    },
    handleMaxAgeblur() {
      this.$refs.form.validateField('minAge')
    },
    validateForm() {
      return this.$refs.form.validate().catch(() => false)
    },
    handleRoundPrice(field) {
      this.form[field] = round2Decimal(this.form[field])
    },
    handleBlur(field) {
      this.handleRoundPrice(field)
      this.$refs.form.validateField(field)
    },
    getFormData() {
      console.log('this.form', this.form)
      return this.form
    },
    handleRequiredChange() {
      if (this.form.required) {
        this.$refs.form.validateField('isNumLimited')
      }
    },
    handleIsNumLimitedChange() {
      if (this.form.required && this.form.isNumLimited) {
        this.$nextTick(() => {
          this.$refs.form.validateField('minNum')
        })
      }
    },
    handleAdminCostPriceBlur() {
      const res = setCostAndGetBase(
        this.form.costPrice,
        this.form.sellingPrice,
        this.form.takeRate,
        this.exchangeRate
      )

      this.form.costPrice = res.cost
      this.form.sellingPrice = res.base
      this.form.takeRate = res.take_rate
    },
    computeTakeRate() {
      this.form.takeRate = computeTR(this.form.costPrice, this.form.sellingPrice, this.exchangeRate)
    },
    handleRoundTakeRate() {
      this.form.takeRate = provingTakeRate(this.form.takeRate)
    },
    handleTakeRateBlur() {
      const res = setTRAndGetBase(
        this.form.costPrice,
        this.form.sellingPrice,
        this.form.takeRate,
        this.exchangeRate
      )

      this.form.sellingPrice = res.base
      this.form.takeRate = res.take_rate
    },
    handleAgeLimitedChange(val) {
      this.form.isAgeLimited = !!val
      this.$refs.form.validateField('minAge')
    }
  }
}
</script>

<style lang="scss">
.unit-price-form {
  &-unit-type {
    margin-right: 16px;
    margin-bottom: 6px;
    height: 24px;
    padding: 0 8px;

    font-size: 12px;
    line-height: 12px;
    color: rgba(0, 0, 0, 0.65);

    &.is-active,
    &.is-active[disabled]:hover {
      color: #40a9ff;
      border-color: #40a9ff;
    }
  }
}
</style>
