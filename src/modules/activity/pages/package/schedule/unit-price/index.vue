<template>
  <div class="package_schedule-unit">
    <a-spin :spinning="loading">
      <a-form-model ref="globalForm" layout="vertical" :model="globalForm">
        <div style="display: flex">
          <a-form-model-item
            prop="merchantCurrency"
            :label="$t('package_merchant_currency')"
            style="width: 300px; margin-right: 24px; margin-bottom: 16px"
          >
            <a-select
              v-model="globalForm.merchantCurrency"
              disabled
              style="width: 100%"
            >
              <a-select-option
                v-for="currency in SUPPORT_CURRENCY_SYMBO"
                :key="currency[0]"
                :value="currency[0]"
              >
                {{ currency[0] }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item
            :label="$t('package_retail_selling_currency')"
            :rules="{
              required: true,
              message: this.$t('global_please_select'),
              trigger: 'change'
            }"
            prop="sellingCurrency"
            style="width: 300px; margin-bottom: 16px"
          >
            <a-select
              v-model="globalForm.sellingCurrency"
              show-search
              :disabled="isSellingCurrencyDisabled || isAdminEm"
              style="width: 100%"
              @change="getExchangeRate"
            >
              <a-select-option
                v-for="currency in SUPPORT_CURRENCY_SYMBO"
                :key="currency[0]"
                :value="currency[0]"
              >
                {{ currency[0] }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </div>
        <a-form-model-item
          :label="$t('pm_price_model')"
          prop="priceModel"
          style="margin-bottom: 16px"
          :rules="{
            required: true,
            message: this.$t('global_please_select'),
            trigger: 'change'
          }"
          required
        >
          <a-radio-group
            :value="targetPriceModel"
            :disabled="isActivityPublished || isTripMapped || isAdminEm"
            @change="handlePriceModelChange"
          >
            <a-radio :value="0">
              {{ $t('pm_fixed_price') }}
            </a-radio>
            <a-radio :value="1">
              {{ $t('pm_price_calendar') }}
            </a-radio>
          </a-radio-group>
        </a-form-model-item>
      </a-form-model>

      <div class="package_schedule-unit_header">
        <h3>{{ $t('package_unit_subtitle') }}</h3>
        <a-button
          type="link"
          icon="plus"
          :disabled="!isUnitSaved || isTripMapped || isAdminEm"
          @click="handleAddUnit"
        >
          {{ $t('global_add') }}
        </a-button>
      </div>

      <div v-if="isUnitSaved" class="package_schedule-unit_content">
        <DraggableList
          v-model="unitList"
          handle
          itemKey="sku_id"
          :disabled="isDragDisabled"
          @start="handleDragStart"
          @change="handleDragChange"
        >
          <template v-slot:default="slotProps">
            <UnitPriceBar
              :data="slotProps.item"
              :temp-id="tempId"
              :show-drag="!isDragDisabled"
              :main-unit="mainUnit"
              :exchange-rate="exchangeRate"
              :forbid-publish="forbidPublish"
              @getData="getData"
              @changeTempId="handleChangeTempId"
              @delete="handleDeleteNewUnit"
            />
          </template>
        </DraggableList>
        <UnitPriceBar
          v-for="item in newUnitList"
          :key="item.id"
          :data="item"
          :temp-id="tempId"
          :show-drag="false"
          :main-unit="mainUnit"
          :exchange-rate="exchangeRate"
          style="margin-top: 20px"
          @getData="getData"
          @changeTempId="handleChangeTempId"
          @delete="handleDeleteNewUnit"
        />
      </div>

      <div v-else class="package_schedule-unit_content type-add">
        <UnitPriceForm ref="unitForm" :exchange-rate="exchangeRate" />
      </div>
    </a-spin>

    <div v-if="!isUnitSaved" class="package_schedule-unit_footer">
      <a-button :style="{ marginRight: '8px' }" @click="handleCancel">
        {{ $t('global_button_cancel') }}
      </a-button>
      <a-button :disabled="isAdminEm" type="primary" @click="handleSave">
        {{ $t('global_button_save') }}
      </a-button>
    </div>

    <!-- 修改 priceModel 的弹窗 -->
    <a-modal
      :title="$t('pm_edit_cost')"
      :visible="isPriceModelVisible"
      :confirm-loading="isPriceModelLoading"
      :width="isAdmin ? 700 : 520"
      @ok="handleSavePriceModel"
      @cancel="handleCancelPriceModel"
    >
      <!-- <component
        ref="tabForm"
        :is="isAdmin ? 'PriceModelTabAdmin' : 'PriceModelTabMerchant'"
        :visible="isPriceModelVisible"
        :unit-list="unitList"
      /> -->
      <PriceModelTab
        ref="tabForm"
        :visible="isPriceModelVisible"
        :unit-list="unitList"
        :exchange-rate="exchangeRate"
      />
    </a-modal>
  </div>
</template>

<script>
import { isAdmin } from '@/env'
import { mapState, mapGetters } from 'vuex'
import {
  scrollFormErrorIntoView,
  getEditLang,
  getRefLang
} from '@activity/utils'
import { MAX_UNIT } from '@activity/pages/package/package_const.js'
import { SUPPORT_CURRENCY_SYMBO } from '@activity/common/const'
import DraggableList from '@activity/components/DraggableList.vue'
import { warn_message } from '@activity/pages/price/utils'
import PriceModelTab from './PriceModelTab.vue'
import UnitPriceForm from './UnitPriceForm.vue'
import UnitPriceBar from './UnitPriceBar.vue'

let id = 1

export default {
  name: 'UnitPrice',
  components: {
    // PriceModelTabAdmin: () => import(/* webpackChunkName: "PriceModelTabAdmin" */ './PriceModelTabAdmin.vue'),
    // PriceModelTabMerchant: () => import(/* webpackChunkName: "PriceModelTabMerchant" */ './PriceModelTabMerchant.vue'),
    PriceModelTab,
    UnitPriceForm,
    DraggableList,
    UnitPriceBar
  },
  inject: ['rootChangeLoading', 'packageId', 'activityId', 'handleGTMBtn'],
  provide() {
    return {
      globalForm: this.globalForm,
      unitPrice: this
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isAdmin,
      SUPPORT_CURRENCY_SYMBO,

      loading: false,
      data: {},

      exchangeRate: '',
      isSellingCurrencyDisabled: true,
      globalForm: {
        merchantCurrency: '',
        sellingCurrency: '',
        priceModel: null // 注意这里的priceModel不等于表单中的priceModel
      },

      // 各种状态
      isActivityPublished: false,

      // pricemodel 弹窗
      targetPriceModel: null,
      isPriceModelVisible: false,
      isPriceModelLoading: false,

      // unitList
      tempId: null, // 展开的那个unit的id
      newUnitList: [],
      unitListCached: []
    }
  },
  computed: {
    ...mapState({
      isAdminEm: (state) => state.isAdminEm,
      packageSkuData: (state) => state.packageSkuData
    }),
    ...mapGetters(['isTripMapped']),
    isDragDisabled() {
      return !!this.tempId || this.isAdminEm
    },
    unitList: {
      get() {
        return this.data.units || []
      },
      set(val) {
        this.data.units = val
      }
    },
    isUnitSaved() {
      // 是否有保存过unit
      return this.unitList.length > 0
    },
    mainUnit() {
      return (this.data.units || [])[0] || null
    },
    forbidPublish() {
      // 当发布了8个以上的时候，就不能再发布了
      return (
        this.unitList.reduce((accu, curr) => {
          if (curr.published) {
            accu += 1
          }
          return accu
        }, 0) >= 8
      )
    }
  },
  watch: {
    visible: {
      immediate: true,
      async handler(val) {
        if (val) {
          this.data = _.cloneDeep(this.packageSkuData)
          await this.init()
        }
      }
    }
  },
  methods: {
    async init() {
      if (this.data.package_id) {
        this.tempId = null
        // 如果后端返回-1，则表示没有被设置，此时设置默认值0
        this.globalForm.priceModel =
          this.data.package_pricing_model !== -1
            ? this.data.package_pricing_model
            : null
        this.globalForm.merchantCurrency = this.data.currency.merchant_currency
        this.globalForm.sellingCurrency = this.data.currency.selling_currency
        // 如果返回的 selling_currency 为空，则表单的 selling_currency 可编辑
        this.isSellingCurrencyDisabled = !!this.data.currency.selling_currency

        this.targetPriceModel = this.globalForm.priceModel
        await this.getExchangeRate()
      }
    },
    async getExchangeRate() {
      const { merchantCurrency, sellingCurrency } = this.globalForm

      if (merchantCurrency && sellingCurrency) {
        const res = await ajax.get(ADMIN_API.act.get_exchange_rate, {
          params: {
            currency_from: merchantCurrency,
            currency_to_list: sellingCurrency
          }
        })
        this.exchangeRate = res[0].to_value
      }
    },
    async getData() {
      this.loading = true
      this.data =
        (await ajax.get(ADMIN_API.act.query_package_sku_list, {
          params: {
            package_id: this.packageId,
            language: getEditLang()
          }
        })) || {}
      this.loading = false
      this.init()
    },
    async handleSavePriceModel() {
      const valid = await this.$refs.tabForm.checkValid()

      if (!valid) {
        return
      }

      this.isPriceModelLoading = true
      const api =
        this.targetPriceModel === 0
          ? ADMIN_API.act.switch_pricing_model_to_fixed
          : ADMIN_API.act.switch_pricing_model_to_calendar
      const formData = this.$refs.tabForm.getFormData()

      const res = await ajax.postBody(api, {
        data: {
          activity_id: this.activityId,
          package_id: this.packageId,
          sku_price_list: formData.map((item) => ({
            sku_id: item.sku_id,
            cost_price: Number(item.cost_price),
            retail_price: Number(item.retail_price),
            selling_price: Number(item.selling_price),
            cost_currency: item.cost_currency,
            selling_currency: item.selling_currency,
            special_selling_price: item.special_selling_price.map((item) => ({
              ...item,
              price: Number(item.price)
            }))
          }))
        }
      })

      if (res.success) {
        this.globalForm.priceModel = this.targetPriceModel
        this.isPriceModelVisible = false
        this.$message.success(this.$t('global_success'))
        await warn_message(this, res.result)
        await this.getData()
        this.$emit('update')
      } else {
        this.$message.error(this.$t('global_failure'))
      }

      this.isPriceModelLoading = false
    },
    handleCancelPriceModel() {
      this.targetPriceModel = this.globalForm.priceModel
      this.isPriceModelVisible = false
    },
    handlePriceModelChange(e) {
      this.targetPriceModel = e.target.value

      if (this.isUnitSaved) {
        // 如果unit已保存，则需要出弹窗，并且不能立刻改变 priceModel
        this.isPriceModelVisible = true
      } else {
        // 如果unit未保存，则不需弹窗，立即改变 priceModel
        this.globalForm.priceModel = this.targetPriceModel
      }
    },
    handleChangeTempId(id) {
      this.tempId = id
    },
    handleDragStart(val) {
      this.unitListCached = [...this.unitList]
    },
    async handleDragChange() {
      // 拖拽后的第一个unit必须是发布过的
      if (!this.unitList[0].published) {
        this.$message.error(this.$t('package_unit_drag_main_sku_warning'))
        this.unitList = [...this.unitListCached]
        return
      }

      this.rootChangeLoading(true)
      const res = await ajax.postBody(ADMIN_API.act.update_sku_priority, {
        data: {
          package_id: this.packageId,
          priority_list: this.unitList.map((item, index) => ({
            priority: index,
            sku_id: item.sku_id
          }))
        }
      })
      await this.getData()
      this.rootChangeLoading(false)

      if (res.success) {
        this.$message.success(this.$t('global_success'))
        this.$emit('update')
        // 这里有一个坑，就是没有全量刷新，可能和后端里面的数据不一致
      } else {
        this.unitList = [...this.unitListCached]
        this.$message.error(this.$t('global_failure'))
      }
    },
    handleCancel() {
      this.$emit('close')
    },
    async handleSave() {
      const isGlobalFormValid = await this.$refs.globalForm
        .validate()
        .catch(() => false)
      const isUnitFormValid = await this.$refs.unitForm.validateForm()

      if (!isGlobalFormValid || !isUnitFormValid) {
        scrollFormErrorIntoView()
        return
      }

      const formData = this.$refs.unitForm.getFormData()
      const params = {
        activity_id: this.activityId,
        package_id: this.packageId,
        unit_type: formData.unitType,
        local: [
          {
            language: getEditLang(),
            unit_name: formData.unitName
          }
        ],
        min_age_range: Number(formData.minAge),
        max_age_range:
          formData.unitType === 4 && !formData.isAgeLimited
            ? 0
            : Number(formData.maxAge),
        required: formData.required ? 1 : 0,
        min_num: formData.isNumLimited ? Number(formData.minNum) : 0,
        max_num: formData.isNumLimited ? Number(formData.maxNum) : MAX_UNIT,
        package_pricing_model: this.globalForm.priceModel,
        use_customized_name: formData.useCustomizedName ? 1 : 0,
        price: {
          cost_currency: this.globalForm.merchantCurrency,
          cost_price: Number(formData.costPrice),
          retail_price: Number(formData.retailPrice),
          selling_price: Number(formData.sellingPrice),
          selling_currency: this.globalForm.sellingCurrency,
          special_selling_price: formData.specialSellingPriceList.map(
            (item) => ({
              ...item,
              price: Number(item.price)
            })
          )
        }
      }

      if (formData.useCustomizedName || formData.unitType === 5) {
        const editLang = getEditLang()
        const refLang = getRefLang()

        if (formData.unitNameRef && editLang !== refLang) {
          params.local.push({
            language: refLang,
            unit_name: formData.unitNameRef
          })
        }

        if (formData.unitNameAdditional && editLang !== 'en_US') {
          params.local.push({
            language: 'en_US',
            unit_name: formData.unitNameAdditional
          })
        }
      }

      this.rootChangeLoading(true)
      const res = await ajax.postBody(ADMIN_API.act.create_sku_and_price, {
        data: params
      })
      this.rootChangeLoading(false)
      if (res.success) {
        this.$message.success(this.$t('act_save_success'))
        this.isSellingCurrencyDisabled = true
        await this.getData()
        this.$emit('update')

        await this.$store.dispatch('updatePkgStepStatus2action', {
          activity_id: this.activityId,
          package_id: this.packageId,
          language: 'ALL',
          status: 1,
          step: 'price'
        })
        await this.handleGTMBtn({
          customPath: 'package_price'
        })
        klook.bus.$emit('updatePkgInfos2bus')
      } else {
        this.$message.error(this.$t('act_save_fail'))
      }
    },
    handleAddUnit() {
      if (this.forbidPublish) {
        this.$error({
          title: 'Error',
          content: this.$t('28993')
        })
        return
      }

      const newId = `new-${++id}`
      this.newUnitList.push({ id: newId })
      this.tempId = newId
    },
    handleDeleteNewUnit(id) {
      // 这里只有delete newUnit 的功能了
      const index = this.newUnitList.map((item) => item.id).indexOf(id)
      index > -1 && this.newUnitList.splice(index, 1)
    }
  }
}
</script>

<style lang="scss">
.package_schedule-unit {
  padding-bottom: 60px;

  h3,
  p {
    margin: 0;
  }

  &_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(0, 0, 0, 0.09);

    h3 {
      margin: 0;
      font-size: 16px;
      line-height: 36px;
    }
  }

  &_content {
    margin-top: 20px;

    &.type-add {
      padding: 20px;
      background-color: #fafafa;
    }

    .draggable-list {
      .flip-list-move {
        transition: transform 0s;
      }
    }

    .list-group-item {
      margin-top: 20px;

      &:first-child {
        margin-top: 0;
      }
    }
  }

  &_footer {
    position: absolute;
    padding: 10px 16px;
    right: 0;
    bottom: 0;
    width: 100%;

    border-top: 1px solid #e9e9e9;
    background: #fff;
    text-align: right;
    z-index: 3;
  }
}
</style>
