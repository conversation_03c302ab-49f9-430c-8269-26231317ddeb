<template>
  <a-input v-model="inputValue" class="input-float-wrap" :placeholder="placeholder" @blur="onBlur" />
</template>

<script>
export default {
  name: 'InputNumber',
  model: {
    event: 'input',
    model: 'value'
  },
  props: {
    value: {
      type: [String, Number],
      default: undefined
    },
    placeholder: {
      type: String,
      default: ''
    },
    defaultValue: {
      type: [String, Number],
      default: undefined
    },
    onlyInteger: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    inputValue: {
      get() {
        if (this.value === undefined) return ''

        return this.value.toString()
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  watch: {
    value(newVal) {
      this.inputValue = newVal?.toString?.()
    }
  },
  beforeMount() {
    if (this.value === undefined || this.value === '') {
      this.inputValue = this.defaultValue
    }
  },
  methods: {
    onBlur() {
      let regex
      if (this.onlyInteger) {
        regex = /-?\d+/
      } else {
        regex = /-?\d*\.?\d+/
      }

      const matches = this.inputValue.match(regex)
      if (matches) {
        this.inputValue = matches[0]
      } else {
        this.inputValue = this.defaultValue
      }
    }
  }
}
</script>
