<template>
  <div class="supply-api-mapping-wrap">
    <a-alert v-if="!isMerchant" type="info" show-icon>
      <span slot="message" v-html="calcAdminAlertMsg"></span>
    </a-alert>

    <header v-if="showTitle" class="header">{{ $t('172677') }}</header>

    <a-form-model ref="form" :model="form" class="main" :class="{ 'is-guide-model': isGuideModel }">
      <div class="main-top">
        <a-form-model-item
          :label="$t('172678')"
          prop="selected_supply_api_partner"
          :rules="[
            {
              required: true,
              message: $t('global_select')
            }
          ]"
        >
          <a-select
            v-model="form.selected_supply_api_partner"
            class="common-select"
            :options="apiPartnerOptions"
            :disabled="calcDisabled || calcApiPartnerDisabled"
          ></a-select>
          <a-tooltip v-if="calcApiPartnerDisabled">
            <template slot="title">
              {{ $t('172689') }}
            </template>
            <a-icon class="common-icon" type="info-circle" theme="filled" />
          </a-tooltip>
        </a-form-model-item>

        <a-alert v-if="isMerchant && form.selected_supply_api_partner" :type="calcPartnerAlertType" show-icon>
          <span slot="message" v-html="calcMappingAlert"></span>
        </a-alert>
      </div>

      <div
        v-if="form.selected_supply_api_partner && form.mapping_data.package_level_mapping.length"
        class="package-wrap"
      >
        <header class="package-header common-title">
          <span>{{ $t('172682') }}</span>
          <a-button v-if="displayResetBtn && isMerchant" :disabled="calcDisabled" @click="handleReset">
            {{ $t('global_reset') }}
          </a-button>
        </header>

        <a-form-model-item
          v-for="(pkgItem, index) of form.mapping_data.package_level_mapping"
          :key="`${pkgItem.key}_${index}`"
          :label="`${pkgItem.name}${$t('172683')}`"
          :prop="`mapping_data.package_level_mapping.${index}.value`"
          :rules="getRule(pkgItem)"
        >
          <component
            :is="getCompName(pkgItem)"
            v-model="pkgItem.value"
            class="common-select"
            :options="pkgItem.options"
            :default-value="pkgItem.default_value"
            :disabled="calcDisabled"
          ></component>
        </a-form-model-item>

        <div class="sku-wrap">
          <div
            v-for="(skuItem, skuIndex) of form.mapping_data.sku_level_mapping"
            :key="skuItem.sku_id"
            class="sku-item"
          >
            <div class="sku-item-title common-title">
              <span>{{ skuItem.sku_id }} - {{ skuItem.sku_name }}</span>
              <span v-if="skuItem.is_connected" class="sku-item--isConnected">
                <a-icon type="check-circle" />
                {{ $t('172687') }}
              </span>
            </div>
            <a-row
              v-for="(productItem, productIndex) of skuItem.products"
              :key="`${skuItem.sku_id}_${productItem.id}_${productIndex}`"
              type="flex"
              class="sku-item-products"
              :gutter="12"
            >
              <a-col
                v-for="(fieldItem, fieldIndex) of productItem.fields"
                :key="`${skuItem.sku_id}_${fieldItem.id}_${fieldIndex}`"
                :span="8"
              >
                <a-form-model-item
                  :label="fieldItem.name"
                  :prop="`mapping_data.sku_level_mapping.${skuIndex}.products.${productIndex}.fields.${fieldIndex}.value`"
                  :rules="getRule(fieldItem)"
                >
                  <component
                    :is="getCompName(fieldItem)"
                    v-model="fieldItem.value"
                    :placeholder="fieldItem.placeholder"
                    :options="fieldItem.options"
                    :default-value="fieldItem.default_value"
                    :disabled="calcDisabled"
                  ></component>
                </a-form-model-item>
              </a-col>
            </a-row>
          </div>
        </div>
      </div>

      <a-empty v-else class="supply-api-mapping-empty" />
    </a-form-model>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex'
import { isMerchant } from '@/env'
import { mapMutations } from 'vuex'
import { InputInteger, InputFloat } from './components/index'
import { getEditLang } from '@activity/utils'

export default {
  name: 'PackageSupplyApiMapping',
  components: {
    InputInteger,
    InputFloat
  },
  inject: ['rootChangeLoading', 'refreshPage'],
  props: {
    showTitle: {
      type: Boolean,
      default: false
    },
    partition: {
      type: Boolean,
      default: true
    },
    // 兼容 merchant 创建模式
    isGuideModel: {
      type: Boolean,
      default: false
    },
    packageId: {
      type: [String, Number],
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isMerchant,
      displayResetBtn: false,
      apiPartnerOptions: [],
      form: {
        template_id: undefined,
        selected_supply_api_partner: undefined,
        mapping_data: {
          package_level_mapping: [],
          sku_level_mapping: []
        }
      },
      isConnected: false
    }
  },
  computed: {
    ...mapState(['lockMerchantActEditing']),
    ...mapGetters({
      currentPkg: 'currentPkg',
      apiMappingSupportConfig: 'currSupplyApiMappingSupportConfigGetter'
    }),
    calcAdminAlertMsg() {
      const url = `${window.location.origin}/mspa/experiencesadmincommon/api/api_list_am`

      return this.$t('173707', {
        Supply_API_backend: `<a href="${url}" target="_blank"><b>${this.$t('173708')}</b></a>`
      })
    },
    calcPublishStatus() {
      return this.currentPkg?.publish_status
    },
    calcApiPartnerDisabled() {
      // published
      return isMerchant && this.calcPublishStatus === 1
    },
    calcActId() {
      if (this.isGuideModel) {
        return +this.$route.query.activity_id
      }

      return +this.$route.params.id
    },
    calcPackageId() {
      if (this.isGuideModel) {
        return +this.packageId
      }

      return +this.$route.query.package_id
    },
    calcDisabled() {
      return (
        this.disabled ||
        this.lockMerchantActEditing ||
        !isMerchant ||
        this.apiMappingSupportConfig?.support_supply_api_mapping === 1 // @note support_supply_api_mapping - 0 隐藏, 1 只读, 2 读写
      )
    },
    calcCurrPartner() {
      return this.apiPartnerOptions.find((item) => item.value === this.form.selected_supply_api_partner)
    },
    calcPartnerAlertType() {
      const isSupport = this.calcCurrPartner?.isSupport || false

      return isSupport ? 'info' : 'warning'
    },
    calcMappingAlert() {
      const curr = this.calcCurrPartner

      if (curr) {
        if (curr.isSupport) {
          return this.$t('172680', {
            partner_name: `<b>${curr.label}</b>`,
            Guidebook: `<a href="${curr.doc}" target="_blank"><b>${this.$t('172681')}</b></a>`
          })
        }

        return this.$t('172685', {
          partner_name: `<b>${curr.label}</b>`,
          form: `<a href="${curr.doc}" target="_blank"><b>${this.$t('172686')}</b></a>`
        })
      }

      return ''
    }
  },
  watch: {
    form: {
      deep: true,
      handler() {
        !this.isGuideModel && this.setActivateFooterWrapper(true)
      }
    },
    'form.selected_supply_api_partner': {
      immediate: true,
      handler(val) {
        if (val) {
          Object.assign(this.form.mapping_data, {
            package_level_mapping: [],
            sku_level_mapping: []
          })
          this.initApiMappingData()
        }
      }
    }
  },
  created() {
    klook.bus.$emit('addHandleSave2bus', this.handleSave, { stepCount: this.$route.query.step_count })
    this.initData()
  },
  methods: {
    ...mapMutations(['setActivateFooterWrapper']),
    ...mapActions(['updatePkgStepStatus2action']),
    async initData() {
      const { selected_supply_api_apiconn_type, supply_api_option_list = [] } = await ajax.get(
        ADMIN_API.act.get_supply_api_partner,
        {
          params: { package_id: this.calcPackageId }
        }
      )

      this.apiPartnerOptions = supply_api_option_list.map((option) => ({
        doc: option.doc,
        label: option.supply_api_partner,
        value: option.supply_api_apiconn_type,
        isSupport: option.support_merchant_self_mapping
      }))

      this.form.selected_supply_api_partner = selected_supply_api_apiconn_type
      this.displayResetBtn = !!selected_supply_api_apiconn_type
      this.$emit('inited')
    },
    async initApiMappingData() {
      this.rootChangeLoading(true)
      const {
        mapping_data = {},
        template_id,
        is_connected
      } = await ajax.get(ADMIN_API.act.get_supply_api_mapping, {
        params: { package_id: this.calcPackageId, partner: this.form.selected_supply_api_partner }
      })

      Object.assign(this.form.mapping_data, {
        package_level_mapping: mapping_data?.package_level_mapping || [],
        sku_level_mapping: mapping_data?.sku_level_mapping || []
      })
      this.form.template_id = template_id
      this.isConnected = is_connected
      this.rootChangeLoading(false)
    },
    async handleSave({ clearData = false } = {}) {
      this.rootChangeLoading(true)

      const valid = await this.validateForm()
      if (!valid) {
        this.$message.warn(this.$t('package_error_mandatory'))
        this.rootChangeLoading(false)
        return false
      }

      const resp = await ajax.postBody(ADMIN_API.act.save_supply_api_mapping, {
        data: {
          ...this.form,
          package_id: this.calcPackageId,
          mapping_data: clearData
            ? {
                package_level_mapping: [],
                sku_level_mapping: []
              }
            : this.form.mapping_data
        }
      })

      if (!resp) {
        this.rootChangeLoading(false)
        return false
      }

      if (this.isGuideModel) {
        this.rootChangeLoading(false)
        klook.bus.$emit('updateBasicInfoByPid2bus')
        klook.bus.$emit('sendGuideCreateSave2bus', this.$route.query.step_count, resp) // spm

        if (resp?.success && !clearData) {
          this.$emit('successSave')
          klook.bus.$emit('setStepStatusFinished2bus', this.$route.query.step_count, {
            package_id: this.calcPackageId
          })
        }
      } else {
        await this.updatePkgStepStatus2action({
          activity_id: this.calcActId,
          package_id: this.calcPackageId,
          language: getEditLang(),
          status: 1,
          step: 'supply_api_mapping'
        })

        this.setActivateFooterWrapper(false)
        this.rootChangeLoading(false)
        this.refreshPage()
      }
      return true
    },
    validateForm() {
      return new Promise((resolve) => this.$refs.form.validate(resolve))
    },
    getCompName({ type }) {
      switch (type) {
        case 'select':
          return 'a-select'
        case 'text':
          return 'a-input'
        case 'date':
          return 'a-date-picker'
        case 'integer':
          return 'input-integer'
        case 'float':
          return 'input-float'
        default:
          return 'a-input'
      }
    },
    validate(rule, value, callback, fieldItem) {
      const required = fieldItem.required

      if (required && (value === undefined || value === '' || value === null)) {
        return callback(new Error(rule.message))
      }

      let valid = true
      if (fieldItem.pattern) {
        valid = String(value).test(fieldItem.pattern)
      }

      if (valid) {
        return callback()
      }

      return callback(new Error(rule.message))
    },
    getRule(item) {
      const key = item.type === 'text' ? 'global_please_input' : 'global_select'
      const message = item.message || this.$t(key)

      return [
        {
          required: item.required,
          trigger: 'change',
          message,
          validate: (rule, value, callback) => this.validate(rule, item.value, callback, item)
        }
      ]
    },
    handleReset() {
      let { package_level_mapping, sku_level_mapping } = this.form.mapping_data
      package_level_mapping = package_level_mapping.map((pkg) => ({
        ...pkg,
        value: hasOwnProperty.call(pkg, 'default_value') ? pkg.default_value : undefined
      }))
      sku_level_mapping = sku_level_mapping.map((sku) => {
        sku.products = sku.products.map((product) => {
          product.fields = product.fields.map((field) => ({
            ...field,
            value: hasOwnProperty.call(field, 'default_value') ? field.default_value : undefined
          }))

          return product
        })

        return sku
      })
      Object.assign(this.form.mapping_data, {
        package_level_mapping,
        sku_level_mapping
      })
    }
  }
}
</script>

<style lang="scss" scoped>
$offsetPixel: 20px;

.supply-api-mapping-wrap {
  margin-bottom: 50px;

  .common-title {
    font-size: 16px;
    font-weight: 600;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.85);
  }

  .common-select {
    max-width: 336px;
  }

  .common-icon {
    margin-left: 8px;
  }

  .header {
    font-size: 24px;
    font-weight: 600;
    color: #000;
  }

  .main {
    display: flex;
    gap: $offsetPixel;
    flex-direction: column;

    ::v-deep {
      .ant-alert {
        margin-top: 8px;
      }
    }

    .main-top {
      padding: $offsetPixel;
      background-color: #fff;
    }

    &.is-guide-model {
      gap: 1px;

      .main-top {
        padding: $offsetPixel 0;
        border-bottom: 1px solid #f0f0f0;
      }

      .package-wrap {
        padding: $offsetPixel 0;
      }
    }

    .package-wrap {
      padding: $offsetPixel;
      background-color: #fff;

      .package-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .sku-wrap {
        display: flex;
        gap: 12px;
        flex-direction: column;
        margin-top: 12px;

        .sku-item {
          padding: 12px 16px;
          border-radius: 4px;
          background-color: #f0f7ff;

          &-title {
            font-size: 14px;
          }

          &-products {
            margin-top: 12px;
            padding-top: 16px;
            border-top: 1px #d9d9d9 dashed;
          }

          &--isConnected {
            padding: 2px 8px;
            margin-left: 8px;
            border-radius: 16px;
            border: 1px solid #b7eb8f;
            background-color: #f6ffed;
            font-size: 12px;
            color: #389e0d;
          }
        }
      }
    }
  }

  .supply-api-mapping-empty {
    margin-top: 36px;
  }
}
</style>
