<template>
  <div>
    <a-form-model :model="form" ref="form" layout="vertical">
      <a-form-model-item prop="is_auto_extend" class="row-inline">
        <div slot="label">
          {{ $t('package_schedule_autoextend') }}
          <a-tooltip placement="right">
            <template slot="title">
              {{ $t('27791') }}
            </template>
            <a-icon type="question-circle" />
          </a-tooltip>
        </div>

        <a-switch
          v-model="form.is_auto_extend"
          :disabled="isChildSku || isBdAudit || disabledByMerchantApproveStatus"
        ></a-switch>
      </a-form-model-item>

      <div v-if="form.is_auto_extend">
        <a-form-model-item
          :label="$t('27792')"
          prop="end_date"
          :rules="{
            required: true,
            trigger: ['blur', 'change'],
            validator: endDateValidator
          }"
        >
          <a-radio-group
            v-model="form.is_limit_end_date"
            name="radioGroup"
            :disabled="isChildSku || isBdAudit"
          >
            <!-- unlimited -->
            <a-radio :value="false">
              {{ $t('27793') }}
            </a-radio>
            <!-- limited -->
            <a-radio :value="true">
              {{ $t('27794') }}
            </a-radio>
          </a-radio-group>

          <a-date-picker
            v-if="form.is_limit_end_date"
            v-model="form.end_date"
            class="row--marginTop"
            :placeholder="$t('27985')"
            :disabled="isChildSku || isBdAudit"
            :disabled-date="disabledDate"
            :style="{
              width: '336px'
            }"
          ></a-date-picker>
        </a-form-model-item>

        <a-form-model-item prop="block_out_date" :required="false">
          <span slot="label">
            {{ $t('package_block_out_date') }}
            <span class="label--tips">({{ $t('27795') }})</span>
          </span>
          <div class="block-out-date-list row--marginTop">
            <div v-for="(block_out_date, index) in form.block_out_date" :key="index" class="__item">
              <a-range-picker
                v-model="form.block_out_date[index]"
                :placeholder="[$t('package_start_date'), $t('package_schedule_end_date')]"
                :disabled="isBdAudit || isChildSku"
                :disabled-date="disabledRangePicker"
              ></a-range-picker>

              <circle-delete-button
                v-if="form.block_out_date.length > 1 && !isBdAudit && !isChildSku"
                class="__item--del"
                @click="handleBlockOutDateDelete(index)"
              />
            </div>
          </div>
          <a-button v-if="!isBdAudit || isChildSku" type="link" @click="handleAddBlockOutDate">
            + {{ $t('global_add') }}
          </a-button>
          <br />
          <GeneralAlert :show-icon="false" banner style="margin-bottom: 12px;">
            <div v-html="$t('29441')" />
          </GeneralAlert>
        </a-form-model-item>
      </div>
    </a-form-model>
  </div>
</template>

<script>
import CircleDeleteButton from '@activity/components/CircleDeleteButton.vue'
import GeneralAlert from '@activity/pages/components/GeneralAlert.vue'

export default {
  name: 'AutoExtendForm',
  model: {
    prop: 'value',
    event: 'update'
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    existAutoExtendRule: {
      type: Boolean,
      default: false
    },
    isChildSku: {
      type: Boolean,
      default: false
    },
    disabledByMerchantApproveStatus: {
      type: Boolean,
      default: false
    }
  },
  components: {
    GeneralAlert,
    CircleDeleteButton
  },
  watch: {
    'form.is_auto_extend': {
      handler(v) {
        if (v && !this.form.block_out_date.length) {
          this.form.block_out_date = [[]]
        }
      }
    },
    'form.is_limit_end_date': {
      handler(v) {
        if (!v) {
          this.form.end_date = ''
        }
      }
    },
    'form.end_date': {
      handler() {
        this.$refs.form.validateField('end_date')
      }
    }
  },
  data() {
    return {}
  },
  computed: {
    form: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('update', v)
      }
    },
    isBdAudit() {
      return klook.getPlatformRoleKey() === 'bd_audit'
    }
  },
  methods: {
    validateForm() {
      return new Promise((resolve) => this.$refs.form.validate((valid) => resolve(valid)))
    },
    handleAddBlockOutDate() {
      this.form.block_out_date.push([])
    },
    handleBlockOutDateDelete(index) {
      this.form.block_out_date.splice(index, 1)
    },
    disabledRangePicker(current) {
      let { block_out_date } = this.form

      if (block_out_date.length) {
        for (let date of block_out_date) {
          if (date.length === 2) {
            let [start, end] = date
            start = moment(start).startOf('day')
            end = moment(end).endOf('day')

            if (current >= moment(start) && current <= moment(end)) {
              return true
            }
          }
        }
      }

      return current < moment().endOf('day')
    },
    disabledDate(current) {
      return current < moment().endOf('day')
    },
    endDateValidator(rule, value, callback) {
      if (!this.form.is_limit_end_date || (this.form.is_limit_end_date && !!this.form.end_date)) {
        callback()
      } else {
        callback(new Error(this.$t('global_please_select')))
      }
    }
  }
}
</script>

<style lang="scss" scoped>
$pixel: 12px;

.row-inline {
  display: flex;
  ::v-deep .ant-form-item-label {
    margin-right: $pixel;
  }
}
.row--marginTop {
  display: block;
  margin-top: $pixel;
}
.block-out-date-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .__item {
    display: inline-flex;
    width: 48%;
    align-items: center;
    margin-bottom: $pixel;
  }
  .__item--del {
    margin-left: 8px;
  }
}
</style>
