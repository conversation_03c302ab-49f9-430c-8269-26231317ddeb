<template>
  <div>
    <a-drawer
      class="autoExtend-container"
      :title="$t('package_schedule_autoextend')"
      :width="drawerWidth"
      :visible="_visible"
      :body-style="{ padding: '80px 20px' }"
      @close="onClose"
    >
      <ApprovalOperatorBar v-if="showApprovalOperatorBar" :unitData="unitData" style="margin-bottom: 32px" />

      <a-spin :spinning="spinning" :delay="300" v-cloak>
        <auto-extend-form
          ref="form"
          v-model="form"
          :existAutoExtendRule="existAutoExtendRule"
          :isChildSku="isChildSku"
          :disabledByMerchantApproveStatus="disabledByMerchantApproveStatus"
        ></auto-extend-form>

        <costDataSourceTip v-if="form.is_auto_extend" style="margin: 12px 0" />

        <auto-extend-table
          v-if="form.is_auto_extend || renderTableCompleted"
          v-show="form.is_auto_extend"
          ref="table"
          :key="`${_visible}-${form.is_auto_extend}`"
          :unitData="unitData"
          :table-data="tableData"
          :cache-table-data="cacheTableData"
          :sku_id="skuId"
          :selling_currency="selling_currency"
          :cost_currency="cost_currency"
          :exchange_rate="exchange_rate"
          :invalidRecordList.sync="invalidRecordList"
          :isChildSku="isChildSku"
          :skuModel="skuModel"
          :disabledByMerchantApproveStatus="disabledByMerchantApproveStatus"
          @hook:mounted="tableMounted"
          @computedInvalidData="computedInvalidData"
        />
      </a-spin>

      <div class="drawer-footer">
        <a-button @click="onClose">
          {{ $t('global_button_cancel') }}
        </a-button>
        <a-button v-if="!hideSaveBtn" type="primary" @click="handleSave">
          {{ $t('global_submit') }}
        </a-button>
      </div>
    </a-drawer>
  </div>
</template>

<script>
import AutoExtendForm from './form.vue'
import AutoExtendTable from './table.vue'
import { getTableFieldStateList } from './utils'
import moment from 'moment'
import { mapState, mapGetters } from 'vuex'
import ApprovalOperatorBar from '../unit-list/calendar-setting/components/approvalOperatorBar'
import { table_mixin } from './mixins'
import costDataSourceTip from '../unit-list/calendar-setting/components/costDataSourceTip'
import { getApprovalOperatorBarFlag } from '../utils/index'

export default {
  name: 'AutoExtend',
  components: {
    costDataSourceTip,
    ApprovalOperatorBar,
    AutoExtendForm,
    AutoExtendTable
  },
  provide() {
    return {
      refreshAutoExtend: this
    }
  },
  inject: {
    calendarSetting: {
      default: undefined
    }
  },
  mixins: [table_mixin],
  model: {
    prop: 'visible',
    event: 'change'
  },
  props: {
    unitData: {
      type: Object,
      required: true
    },
    visible: {
      type: Boolean,
      default: false
      // required: true
    },
    skuId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      renderTableCompleted: false,

      form: {
        is_auto_extend: false,
        is_limit_end_date: false,
        end_date: '',
        block_out_date: []
      },

      tableData: [],
      cacheTableData: [],
      spinning: false,

      selling_currency: '',
      cost_currency: '',

      existAutoExtendRule: false,

      invalidRecordList: [],

      exchange_rate: [],

      skuModel: {}
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(v) {
        if (v) {
          this.refresh()
        }
      }
    }
  },
  computed: {
    showApprovalOperatorBar() {
      return this.isMerchant && getApprovalOperatorBarFlag(this.unitData.ticket_status)
    },
    drawerWidth() {
      return klook.getPlatformRoleKey() === 'merchant' ? '960px' : '75%'
    },
    ...mapState(['isMerchant']),
    // approveStatus: 0: none, 1: pending, 2: reject, 3: resolve
    ...mapGetters(['getCurrentPackageApproveStatus']),
    disabledByMerchantApproveStatus() {
      // 1: pending approval
      return this.isMerchant && [1].includes(this.getCurrentPackageApproveStatus)
    },
    isChildSku() {
      return this.form.share_rel_ship === 1
    },
    isBdAudit() {
      return klook.getPlatformRoleKey() === 'bd_audit'
    },
    _visible: {
      get() {
        return this.visible
      },
      set(v) {
        this.$emit('change', v)
      }
    },
    tableFiledList() {
      return getTableFieldStateList()
    },
    hideSaveBtn() {
      // merchant平台
      // 活动为待审核状态时，所有内容均只可查看不可编辑
      // 活动为审核通过的状态时，主sku可以编辑库存相关（cut off time，激活状态，库存数量）的数据，不能编辑价格数据；子sku均只可查看不可编辑
      // 活动状态为空或拒绝时，可以编辑库存和价格
      return (
        klook.getPlatformRoleKey() === 'merchant' &&
        (this.getCurrentPackageApproveStatus === 1 ||
          (this.getCurrentPackageApproveStatus === 3 && this.isChildSku))
      )
    }
  },
  methods: {
    refresh() {
      this.invalidRecordList = []
      this.initData()
    },
    tableMounted() {
      this.renderTableCompleted = true
    },
    computedInvalidData(rowIndex) {
      const { invalidRecordList } = this.getInvalidRecordByIndex({
        rowIndex,
        tableData: this.tableData,
        skuId: this.skuId,
        tableFiledList: this.tableFiledList,
        skuFieldDisabledStateObj: this.$refs.table.skuFieldDisabledStateObj
      })

      this.$set(
        this,
        'invalidRecordList',
        _.uniqWith([...this.invalidRecordList, ...invalidRecordList], _.isEqual)
      )
    },
    async initData() {
      this.spinning = true

      this.$set(this, 'form', this.$options.data().form)
      let apiKey = this.isBdAudit ? 'audit_get_auto_extend_rule' : 'get_auto_extend_rule'
      let response = await ajax.get(ADMIN_API.act[apiKey], {
        params: {
          sku_id: this.skuId
        }
      })

      if (!response) {
        this.spinning = false
        return
      }

      // eslint-disable-next-line no-unused-vars
      let { weekly, ...form } = response
      let { cost_currency, selling_currency, inv_model, pricing_model, exchange_rate, tableData } =
        await this.fmtInitData(response, { ticket_status: this.unitData.ticket_status })

      this.$set(this, 'skuModel', { inv_model, pricing_model })
      this.cost_currency = cost_currency
      this.selling_currency = selling_currency
      this.exchange_rate = exchange_rate

      this.$refs.table && this.$refs.table.clearEditTableCell()
      this.tableData = tableData.map((item, index) => ({
        ...item,
        uuid: '_' + index // row index
      }))
      this.cacheTableData = _.cloneDeep(this.tableData)
      form.block_out_date = (form.block_out_date || []).map((item) => {
        return [item.start_time, item.end_time]
      })

      this.form = _.cloneDeep(form)
      this.existAutoExtendRule = form.is_auto_extend
      this.spinning = false
    },
    async handleSave() {
      if (this.form.is_auto_extend) {
        let valid = await this.$refs.form.validateForm()

        if (!valid) {
          this.$message.error(__('package_error_mandatory'))
          return
        }
      }

      // save form
      let res
      if (!this.form.is_auto_extend) {
        if (this.existAutoExtendRule) {
          await this.pmsConfirm()
        }
        res = await ajax.post(ADMIN_API.act.destroy_auto_extend_rule, {
          data: {
            sku_id: this.skuId
          }
        })
      } else {
        this.invalidRecordList = []

        let { weekly, invalidRecordList, notEmptyCount } = this.getInvalidData({
          tableData: this.tableData,
          skuId: this.skuId,
          tableFiledList: this.tableFiledList,
          skuFieldDisabledStateObj: this.$refs.table.skuFieldDisabledStateObj
        })

        if (invalidRecordList.length) {
          this.invalidRecordList = invalidRecordList
          this.$refs.table.goToTableByUUID(invalidRecordList[0].uuid)
          this.$message.error(__('package_error_mandatory'))
          return
        }

        if (notEmptyCount === 0) {
          this.$message.warn('Please fill in at least one')
          return
        }

        let block_out_date = this.form.block_out_date.filter((item) => item.length === 2)
        let postData = {
          ...this.form,
          end_date: this.form.end_date ? moment(this.form.end_date).format('YYYY-MM-DD') : '',
          block_out_date: block_out_date.map((item) => ({
            start_time: moment(moment(item[0]).startOf('day')).format('YYYY-MM-DD HH:mm:ss'),
            end_time: moment(moment(item[1]).endOf('day')).format('YYYY-MM-DD HH:mm:ss')
          })),
          weekly: Object.values(weekly)
            .filter((item) => item.schedules.length) // empty 不需要传给后端
            .map((item) => ({
              ...item,
              schedules: item.schedules.map((schedule) => {
                if (!isNaN(schedule.price?.take_rate)) {
                  schedule.price.take_rate = String(schedule.price.take_rate)
                }

                return schedule
              })
            }))
        }

        // save table
        let apiKey = this.isBdAudit
          ? 'audit_create_or_update_auto_extend_rule'
          : 'create_or_update_auto_extend_rule'
        res = await ajax.post(ADMIN_API.act[apiKey], {
          data: postData
        })
      }

      if (res === null) {
        // success
        this.onClose()
        this.calendarSetting?.refresh?.()
        klook.bus.$emit('updatePkgInfo2bus')
        klook.bus.$emit('refreshUnitList', { clearActiveId: false })
        this.$message.success(this.$t('global_success'))
      }
    },
    pmsConfirm() {
      return new Promise((resolve, reject) => {
        this.$confirm({
          content: __('27979'),
          okText: __('global_confirm'),
          onOk() {
            return resolve(true)
          },
          onCancel() {
            return reject()
          }
        })
      })
    },
    onClose() {
      this._visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.autoExtend-container {
  .drawer-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    left: 0;
    background: #fff;
    border-radius: 0 0 4px 4px;
    z-index: 9;
    ::v-deep .ant-btn {
      margin-left: 8px;
    }
  }
  ::v-deep {
    .ant-table-layout-fixed tr.ant-table-row {
      text-align: center;
    }
    .gg-undo {
      box-sizing: border-box;
      position: relative;
      display: block;
      width: 14px;
      height: 14px;
      border: 2px solid;
      border-left-color: transparent;
      border-radius: 100px;
      transform: scale(0.75);
      margin-left: 6px;
    }

    .gg-undo::before {
      content: '';
      display: block;
      box-sizing: border-box;
      position: absolute;
      width: 6px;
      height: 6px;
      border-top: 2px solid;
      border-left: 2px solid;
      top: -3px;
      left: -1px;
      transform: rotate(-68deg);
    }
  }
}

.table {
  display: table;
  .__row {
    display: flex;
    align-items: center;
    border-left: 1px solid #d6d6d6;
    border-top: 1px solid #d6d6d6;
  }
  .__rowCell {
  }
}
.table-body {
  display: table;
  .table-row {
    display: table-row;
    align-items: center;
    border-left: 1px solid #d6d6d6;
    border-top: 1px solid #d6d6d6;
    .table-row-cell {
      min-width: 120px;
      height: 40px;
      display: table-cell;
      flex-grow: 1;
      flex-shrink: 0;
      align-items: center;
      justify-content: space-around;
      padding: 4px 8px;
      border-right: 1px solid #d6d6d6;
      border-bottom: 1px solid #d6d6d6;
      transition: background-color 1s ease, border-color 1s ease;
    }
  }
}
</style>
