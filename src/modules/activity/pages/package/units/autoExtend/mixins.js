import {
  weeklyOpts,
  getDateByTimestamp,
  isOnPriceField,
  isValidTableCellData,
  isEmptyValue,
  priceLinkageUpdate
} from '../bulkEdit/utils'

export const table_mixin = {
  computed: {
    weeklyOptsObject() {
      return weeklyOpts.reduce((acc, curr) => {
        return {
          ...acc,
          [curr.value]: curr.label.replace(/Pre /g, '')
        }
      }, {})
    }
  },
  methods: {
    async fmtInitData(
      { weekly, inv_model, pricing_model, share_rel_ship, cost_currency, selling_currency },
      injectVal = {},
      injectFunc = {}
    ) {
      const exchange_rate = await ajax.get(ADMIN_API.act.get_exchange_rate, {
        params: {
          currency_from: cost_currency,
          currency_to_list: selling_currency
        }
      })

      let commonSkuData = {
        inv_model,
        pricing_model,
        share_rel_ship
      }

      let tableData = (weekly || []).reduce((acc, curr) => {
        let { week_day, schedules } = curr
        return [
          ...acc,
          ...schedules.map((schedule, index) => {
            let rowSpan = index === 0 ? schedules.length : 0

            // auto extend 内需要注入 ticket status
            Object.assign(schedule, injectVal)

            let { auto_fill_schedule_item } = schedule

            if (auto_fill_schedule_item && auto_fill_schedule_item.fix_price) {
              let fixedLinkageData = priceLinkageUpdate({
                ...auto_fill_schedule_item.fix_price,
                originField: 'cost_price',
                exchange_rate
              })

              if (fixedLinkageData.updateField) {
                auto_fill_schedule_item.fix_price[fixedLinkageData.updateField] =
                  fixedLinkageData[fixedLinkageData.updateField]
              } else {
                auto_fill_schedule_item.fix_price.take_rate = undefined
              }

              if (!auto_fill_schedule_item.fix_price.special_selling_price) {
                auto_fill_schedule_item.fix_price.special_selling_price = []
              }

              schedule.auto_fill_schedule_item = auto_fill_schedule_item
            }

            if (!schedule.price) {
              schedule.price = {
                cost_price: '',
                retail_price: '',
                selling_price: '',
                cost_currency,
                selling_currency,
                retail_currency: selling_currency,
                take_rate: undefined,
                special_selling_price: []
              }
            } else {
              if ('[object Function]' === toString.call(injectFunc?.fmtPrice)) {
                schedule.price = injectFunc.fmtPrice({ price: schedule.price })
              }

              let linkageData = priceLinkageUpdate({
                ...schedule.price,
                originField: 'cost_price',
                exchange_rate
              })

              if (linkageData.updateField) {
                schedule.price[linkageData.updateField] = linkageData[linkageData.updateField]
              } else {
                schedule.price.take_rate = undefined
              }
            }

            if (schedule.price.special_selling_price === null) {
              schedule.price.special_selling_price = []
            }

            let { days, time } = getDateByTimestamp(schedule.cut_off_time)
            let { start_time, ...other } = schedule

            return {
              ...commonSkuData,
              rowSpan,
              start_time,
              sku_id: this.skuId,
              isDelete: false,
              week_day,
              week_day_text: this.weeklyOptsObject[week_day],
              [this.skuId]: {
                ...other,
                days,
                time
              }
            }
          })
        ]
      }, [])

      return {
        ...arguments[0],
        exchange_rate,
        tableData
      }
    },
    getInvalidData({
      tableData,
      skuId,
      tableFiledList,
      priceShouldBeNull = false,
      skuFieldDisabledStateObj = {}
    } = {}) {
      return tableData.reduce(
        (acc, curr, rowIndex) => {
          let rowSkuData = curr[this.skuId]

          if (curr.isDelete) {
            return acc
          }

          let { invalidRecordList, emptyList } = this.getInvalidRecordByIndex({
            rowIndex,
            skuId,
            tableFiledList,
            priceShouldBeNull,
            tableData,
            skuFieldDisabledStateObj
          })

          acc.invalidRecordList = [...acc.invalidRecordList, ...invalidRecordList]

          let { week_day } = curr
          if (!hasOwnProperty.call(acc.weekly, week_day)) {
            acc.weekly[week_day] = {
              week_day,
              schedules: []
            }
          }

          if (!emptyList.every((item) => item)) {
            acc.weekly[week_day].schedules.push({
              start_time: curr.start_time,
              ...rowSkuData,
              ...(this.priceShouldBeNull ? { price: null } : {})
            })
            acc.notEmptyCount += 1
          }

          return acc
        },
        {
          weekly: {},
          invalidRecordList: [],
          notEmptyCount: 0
        }
      )
    },
    getInvalidRecordByIndex({
      rowIndex,
      tableData,
      skuId,
      tableFiledList,
      priceShouldBeNull = false,
      skuFieldDisabledStateObj
    } = {}) {
      let currentRowData = _.cloneDeep(tableData[rowIndex])
      let rowSkuData = currentRowData[skuId]
      let emptyList = []
      let validList = []
      let invalidRecordList = []

      for (let tableCol of tableFiledList) {
        let tableField = tableCol.key

        if (tableField === 'cut_off_time') {
          emptyList.push(true)
          validList.push(true)
        } else {
          let currentFieldValue

          if (isOnPriceField(tableField)) {
            if (priceShouldBeNull) {
              continue
            }
            currentFieldValue = rowSkuData.price[tableField]
          } else {
            currentFieldValue = rowSkuData[tableField]
          }

          let isEmpty = isEmptyValue({ value: currentFieldValue, field: tableField })
          // 当该字段是不可编辑的，空值也可以
          if (
            hasOwnProperty.call(skuFieldDisabledStateObj, tableField) &&
            skuFieldDisabledStateObj[tableField]
          ) {
            isEmpty = false
          }

          emptyList.push(isEmpty)

          // 这里后端接口不会传 take rate 的数据，所以会出现 undefined 的情况
          if (tableField === 'take_rate' && currentFieldValue === undefined) {
            validList.push(true)
          } else {
            validList.push(isValidTableCellData(currentFieldValue, tableField))
          }
        }
      }

      let allIsEmpty = emptyList.every((item) => item)
      let allIsValid = false
      if (!allIsEmpty) {
        allIsValid = validList.every((item) => item)
      }

      if (!(allIsEmpty || allIsValid)) {
        validList.forEach((item, idx) => {
          if (!item) {
            invalidRecordList.push({
              uuid: '_' + rowIndex,
              field: tableFiledList[idx].key
            })
          }
        })
      }

      return {
        invalidRecordList,
        emptyList,
        validList
      }
    }
  }
}
