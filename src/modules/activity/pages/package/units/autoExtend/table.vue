<template>
  <div class="auto-extend-table">
    <!-- table 卡顿问题 https://github.com/vueComponent/ant-design-vue/issues/3531#issuecomment-786999015 -->
    <a-table
      bordered
      rowKey="uuid"
      :columns="viewColumnField"
      :data-source="currentTableData"
      :rowClassName="rowClassName"
      :scroll="{ x: '660px', y: '500px' }"
      :pagination="pagination"
      @change="handleTableChange"
    >
      <template slot="timeslot" slot-scope="text, record">
        <div class="table-row-cell">
          <!-- undo -->
          <template v-if="record.isDelete">
            <a-button size="small" @click="handleDeleteRow(record)">{{ $t('29194') }}</a-button>
          </template>
          <span v-else class="cell-actions">
            {{ record.start_time }}
            <span class="cell-btn-list" v-if="showBtnList">
              <a-popover
                v-if="showCopy(record)"
                :visible.sync="currentCopyView[record.uuid]"
                trigger="click"
                overlay-class-name="copy-popover"
                placement="topRight"
              >
                <div class="copy-popover-content" slot="content">
                  <a-tree
                    v-model="copyCheckList"
                    checkable
                    :auto-expand-parent="true"
                    :defaultExpandAll="true"
                    :tree-data="currentCopyTreeData"
                  />
                  <footer class="footer">
                    <a-button type="link" size="small" @click="resetCopyData">{{
                      $t('global_reset')
                    }}</a-button>
                    <a-button
                      type="primary"
                      size="small"
                      :disabled="!copyCheckList.length"
                      @click="handleCopyToOther(record)"
                    >
                      {{ $t('voucher.copy') }}
                    </a-button>
                  </footer>
                </div>
                <a-tooltip placement="left">
                  <template slot="title">
                    <span>{{ $t('voucher.copy') }}</span>
                  </template>
                  <svg-icon
                    icon-name="copy"
                    style="margin-left: 4px; cursor: pointer; font-size: 24px;"
                    @click.stop.prevent="handleCopyView(record)"
                  />
                </a-tooltip>
              </a-popover>

              <a-tooltip placement="right">
                <template slot="title">
                  <span>{{ $t('global_delete') }}</span>
                </template>
                <circle-delete-button v-if="!isChildSku && !isBdAudit" @click="handleDeleteRow(record)" />
              </a-tooltip>
            </span>
          </span>
        </div>
      </template>
    </a-table>

    <SpecialSellingPriceDrawer
      :price="specialSellingPrice.selling_price"
      :visible.sync="specialSellingPrice.visible"
      :initial-value="specialSellingPrice.list"
      v-bind="specialSellingPrice"
      @confirm="handleSSPDrawerConfirm"
    />
  </div>
</template>

<script>
import {
  isOnPriceField,
  parseRowIndexByUUID,
  priceLinkageUpdate,
  getCellDataByPosition,
  getFixedValueByRecord,
  isEmptyValue,
  priceFieldList,
  isValidTableCellData
} from '../bulkEdit/utils'

import { getTableFieldStateList } from './utils'

import CircleDeleteButton from '../../../../components/CircleDeleteButton'
import EditableCell from '../bulkEdit/EditableCell'
import SpecialSellingPriceDrawer from '../components/SpecialSellingPriceDrawer'
import actMixins from '@activity/mixins/index.js'
import { mapState, mapGetters } from 'vuex'
import ApprovalDetailPopover from '../unit-list/calendar-setting/components/approvalDetailPopover'

export default {
  name: 'AutoExtendTable',
  mixins: [actMixins.sspPriceMixin],
  components: {
    /* eslint-disable vue/no-unused-components */
    ApprovalDetailPopover,
    SpecialSellingPriceDrawer,
    /* eslint-disable vue/no-unused-components */
    EditableCell,
    CircleDeleteButton
  },
  props: {
    tableData: {
      type: Array,
      required: true
    },
    cacheTableData: {
      type: Array,
      required: true
    },
    sku_id: {
      type: Number,
      required: true
    },
    selling_currency: {
      type: String,
      required: true
    },
    cost_currency: {
      type: String,
      required: true
    },
    invalidRecordList: {
      type: Array,
      required: true
    },
    exchange_rate: {
      type: Array,
      required: true
    },
    isChildSku: {
      type: Boolean,
      default: false
    },
    skuModel: {
      type: Object,
      required: true
    },
    disabledByMerchantApproveStatus: {
      type: Boolean,
      default: false
    },
    customColumnFieldList: {
      type: Array,
      default: undefined
    },
    showBtnList: {
      type: Boolean,
      default: true
    },
    viewField: {
      type: Array,
      default: () => []
    },
    openApprovalDetailPopover: {
      type: Boolean,
      default: true
    },
    showCostInputSuffixData: {
      type: Boolean,
      default: true
    },
    unitData: {
      type: Object,
      required: true
    }
  },
  data() {
    this.skuFieldDisabledStateObj = {}

    return {
      currentEditCell: {},

      specialSellingPrice: {
        selling_price: '',
        list: [],
        visible: false,
        sellingCurrency: '',
        position: null,
        skuId: null,
        timePoints: []
      },

      currentTableData: [],
      pagination: {
        pageSizeOptions: ['10', '20', '50'],
        total: 0,
        current: 1,
        pageSize: 10,
        showSizeChanger: true
      },

      copyCheckList: [],
      currentCopyView: {},

      copyTreeData: [],
      currentCopyTreeData: []
    }
  },
  watch: {
    clickTarget(target) {
      if (target) {
        if (![...document.querySelectorAll('.copy-popover')].some((item) => item.contains(target))) {
          this.$set(
            this,
            'currentCopyView',
            Object.keys(this.currentCopyView).reduce(
              (acc, curr) => ({
                ...acc,
                [curr]: null
              }),
              {}
            )
          )
        }
      }
    },
    tableData: {
      deep: true,
      immediate: true,
      handler() {
        let { data: copyTreeData } = this.tableData.reduce(
          (acc, curr) => {
            let data = {
              title: curr.start_time,
              key: curr.uuid
            }

            const { week_day, week_day_text } = curr

            if (acc.preWeekDay !== week_day) {
              acc.preWeekDay = week_day
              acc.data.push({
                title: week_day_text,
                key: week_day,
                children: [data]
              })
            } else {
              acc.data[acc.data.length - 1].children.push(data)
            }

            return acc
          },
          {
            data: [],
            preWeekDay: null
          }
        )

        this.copyTreeData = copyTreeData
        this.setResize()
      }
    },
    'specialSellingPrice.visible'(v) {
      if (!v) {
        this.currentEditCell = {}
      }
    }
  },
  computed: {
    ...mapState({
      clickTarget: (state) => state.clickTarget,
      merchantActHaveBeenApproved: 'merchantActHaveBeenApproved'
    }),
    ...mapGetters(['getCurrentPackageApproveStatus']),
    tableFiledList() {
      return this.customColumnFieldList || getTableFieldStateList()
    },
    isBdAudit() {
      return klook.getPlatformRoleKey() === 'bd_audit'
    },
    originalColumns() {
      return [
        {
          title: '',
          dataIndex: 'week_day_text',
          width: 100,
          fixed: 'left',
          customRender: (text, row) => {
            return {
              children: row.week_day_text,
              attrs: {
                rowSpan: row.rowSpan || 0
              },
              style: { textAlign: 'center' }
            }
          }
        },
        {
          title: __('package_timeslot'),
          dataIndex: 'start_time',
          width: 120,
          fixed: 'left',
          scopedSlots: { customRender: 'timeslot' }
        },
        ...this.tableFiledList.map((field) => {
          return {
            title: () => {
              if (['cost_price'].some((item) => field.key.includes(item))) {
                return `${field.title}(${this.cost_currency})`
              } else if (['selling_price', 'retail_price'].some((item) => field.key.includes(item))) {
                return `${field.title}(${this.selling_currency})`
              } else {
                return field.title
              }
            },
            dataIndex: field.key,
            width: field.width || 170,
            customRender: (text, record) => {
              let { uuid } = record
              let index = parseRowIndexByUUID(record.uuid)

              let position = {
                sku_id: this.sku_id,
                field: field.key,
                uuid
              }
              let currentSku = record[this.sku_id]
              let editable = this.getCellEditable({
                record,
                position,
                fieldInfo: field
              })

              let tips = this.isChildSku && !isOnPriceField(field.key) ? this.$t('27815') : ''

              if (field.key === 'cut_off_time') {
                return `${currentSku.days} ${this.$t('package_day_s')} ${currentSku.time}`
              }

              let cacheData
              let currentCache = this.cacheTableData[index][this.sku_id]
              if (isOnPriceField(field.key)) {
                cacheData = _.cloneDeep(currentCache.price[field.key])
                // 审核通过不可编辑价格
                editable = !this.disabledByMerchantApproveStatus && editable
              } else {
                cacheData = currentCache[field.key]
              }

              let { inv_model } = this.skuModel
              inv_model = inv_model === undefined ? 1 : inv_model

              const className = this.getCellClassName(position, editable).join(' ')

              this.skuFieldDisabledStateObj[field.key] = !editable

              const editableCell = (
                <editable-cell
                  record={record}
                  class={className}
                  sku_id={+this.sku_id}
                  field={field.key}
                  editable={editable}
                  isDelete={record.isDelete}
                  currentEditCell={this.currentEditCell}
                  hasTips={Boolean(tips)}
                  cacheData={cacheData}
                  invModel={inv_model}
                  showCostInputSuffixData={this.showCostInputSuffixData}
                  tips={tips}
                  on={{
                    blurTableCell: this.blurTableCellEdit,
                    clearFixedValue: this.handleClearFixedValue,
                    updateTableCell: this.updateTableCell,
                    clearEditTableCell: this.clearEditTableCell
                  }}
                />
              )

              const { ticket_status, ticket_id } = this.unitData

              if (
                this.openApprovalDetailPopover &&
                field.key === 'cost_price' &&
                ticket_status === 'PendingApproval'
              ) {
                return <approvalDetailPopover ticket-id={ticket_id}>{editableCell}</approvalDetailPopover>
              }

              return (
                <span
                  onClick={() => {
                    this.handleEditCell(position)
                  }}
                >
                  {editableCell}
                </span>
              )
            }
          }
        })
        // {
        //   title: () => {
        //     return <div class="cell-header-title-custom">
        //       <span class="is-required">*</span>Cost Price
        //     </div>
        //   },
        //   dataIndex: 'cost_price',
        //   scopedSlots: { customRender: 'inputNumber' }
        // },
      ]
    },
    viewColumnField() {
      if (this.viewField.length) {
        const viewField = [...this.viewField, 'start_time', 'week_day_text']

        return this.originalColumns.filter((item) => viewField.includes(item.dataIndex))
      }

      return this.originalColumns
    }
  },
  beforeMount() {
    this.pagination.total = this.tableData.length
    this.getCurrentTableData()
  },
  methods: {
    isOnPriceField,
    parseRowIndexByUUID,
    isEmptyValue,
    setResize() {
      //重绘，解决固定列高度对不齐
      this.$nextTick(() => {
        const myEvent = new Event('resize')
        window.dispatchEvent(myEvent)
      })
    },
    updateSspData: _.debounce(async function(options, arr) {
      if (!arr || !arr.length) return
      await this.bindSspList2mixin(arr, options)
    }, 300),
    goToTableByUUID(uuid) {
      let index = parseRowIndexByUUID(uuid) + 1
      let { pageSize } = this.pagination
      this.pagination.current = Math.ceil(index / pageSize)

      this.getCurrentTableData()
    },
    getCurrentTableData() {
      let { pageSize, current } = this.pagination
      let chunkTableData = _.chunk(this.tableData, pageSize)

      current = current - 1
      let { data } = (chunkTableData[current] || []).reduce(
        (acc, curr, index, origin) => {
          if (acc.preWeekDay === null) {
            acc.weekIndex = index
            acc.preWeekDay = curr.week_day
            acc.weekCount += 1
          } else {
            if (acc.preWeekDay === curr.week_day) {
              acc.weekCount += 1
            } else {
              origin[acc.weekIndex].rowSpan = acc.weekCount
              // init
              acc.weekIndex = index
              acc.preWeekDay = curr.week_day
              acc.weekCount = 1
            }
          }

          if (index === origin.length - 1) {
            if (acc.preWeekDay === curr.week_day) {
              origin[acc.weekIndex].rowSpan = acc.weekCount
            } else {
              origin[index].rowSpan = 1
            }
          }

          acc.data.push(curr)

          return acc
        },
        {
          data: [],
          weekIndex: 0,
          preWeekDay: null,
          weekCount: 0
        }
      )

      this.$set(this, 'currentTableData', data)
    },
    resetCopyData() {
      this.copyCheckList = this.currentCopyTreeData.reduce((acc, curr) => {
        return [...acc, curr.key, ...(curr.children || []).map((item) => item.key)]
      }, [])
    },
    handleCopyView(record) {
      let uuid = record.uuid
      this.currentCopyTreeData = this.copyTreeData
        .map((item) => {
          item.children = item.children.filter((c) => c.key !== uuid)

          return item
        })
        .filter((item) => item.children.length)

      setTimeout(() => {
        this.resetCopyData()
        this.$set(this, 'currentCopyView', {
          ...Object.keys(this.currentCopyView).reduce(
            (acc, curr) => ({
              ...acc,
              [curr]: false
            }),
            {}
          ),
          [uuid]: true
        })
      }, 60)
    },
    isInValidDOM(ele) {
      document.querySelector('.auto-extend-table .ant-table-body-inner .ant-table-fixed').contains(ele)
    },
    handleCopyToOther(record) {
      let { uuid } = record
      let currentSkuData = record[this.sku_id]
      let { special_selling_price } = currentSkuData.price

      this.copyCheckList
        .filter((item) => String(item).indexOf('_') === 0)
        .forEach((item) => {
          let updateIndex = parseRowIndexByUUID(item)
          let updateRowSkuData = this.tableData[updateIndex][this.sku_id]
          updateRowSkuData.inv_quantity = currentSkuData.inv_quantity
          Object.assign(updateRowSkuData.price, {
            ...currentSkuData.price,
            special_selling_price: _.cloneDeep(special_selling_price || [])
          })
        })

      this.$set(this.currentCopyView, uuid, false)
      this.$message.success(this.$t('global_copy_success'))
    },
    showCopy(record) {
      if (this.isChildSku || this.isBdAudit || this.disabledByMerchantApproveStatus) {
        return false
      }

      let { uuid } = record
      let index = parseRowIndexByUUID(uuid)

      if (index === this.tableData.length - 1) {
        return false
      }

      if (this.tableData[index + 1].isDelete) {
        return false
      }

      let currentSkuData = record[this.sku_id]
      let { cost_price, selling_price, take_rate, special_selling_price } = currentSkuData.price

      return (
        isValidTableCellData(currentSkuData.inv_quantity, 'inv_quantity') ||
        isValidTableCellData(cost_price, 'cost_price') ||
        isValidTableCellData(selling_price, 'selling_price') ||
        isValidTableCellData(take_rate, 'take_rate') ||
        special_selling_price.length
      )
    },
    handleTableChange(pagination) {
      if (pagination.pageSize !== this.pagination.pageSize) {
        this.pagination.current = 1
        this.pagination.pageSize = pagination.pageSize
      } else {
        this.pagination.current = pagination.current
      }

      this.getCurrentTableData()
    },
    getCellClassName({ uuid, field }, editable) {
      let className = ['table-row-cell']

      let invalid = _.find(this.invalidRecordList, {
        uuid,
        field
      })

      if (invalid) {
        className.push('cell-is-invalid')
      }

      if (isOnPriceField(field)) {
        className.push('cell-flex-end')
      }

      if (!editable) {
        className.push('cell-is-disabled')
      }

      let { inv_model, pricing_model } = this.skuModel
      inv_model = inv_model === undefined ? 1 : inv_model
      pricing_model = pricing_model === undefined ? -1 : pricing_model

      if ((field === 'inv_quantity' && inv_model === 3) || (isOnPriceField(field) && pricing_model === 0)) {
        className.push('cell-is-fixed')
      }

      return className
    },
    getCellEditable({ record, position, fieldInfo }) {
      if (record.isDelete) {
        return false
      }

      let { sku_id, field, uuid } = position
      let current = record[sku_id]
      let { skuData } = this.getCellDataByPosition({ sku_id, field, uuid })
      let { share_rel_ship, ticket_status } = skuData
      let { inv_model, pricing_model } = this.skuModel

      let disabled
      if (fieldInfo.disabledFn) {
        disabled = fieldInfo.disabledFn({
          share_rel_ship,
          pricing_model,
          inv_model,
          role: klook.getPlatformRoleKey(),
          // approveStatus: 0: none, 1: pending, 2: reject, 3: resolve
          approveStatus: this.getCurrentPackageApproveStatus,
          ticketStatus: ticket_status,
          merchantActHaveBeenApproved: this.merchantActHaveBeenApproved
        })
      } else {
        disabled = fieldInfo.disabled !== undefined ? fieldInfo.disabled : true
      }

      if (
        share_rel_ship === 1 || // sub sku
        disabled ||
        (this.isBdAudit && current.is_empty)
      ) {
        return false
      }

      if (this.isOnPriceField(field)) {
        return current.price_can_edit === undefined || current.price_can_edit
      } else if (field === 'inv_quantity') {
        return current.quantity_can_edit
      } else {
        return true
      }
    },
    getCellDataByPosition({ sku_id, field, uuid, source = this.tableData } = {}) {
      return getCellDataByPosition({ sku_id, field, uuid, source })
    },
    updateTableCell({
      sku_id,
      field,
      value,
      type,
      uuid = null // single
    } = {}) {
      let currentCellData = this.getCellDataByPosition({
        sku_id,
        field,
        uuid
      })
      if (field === 'selling_price') {
        let sspList = _.get(currentCellData, 'skuData.price.special_selling_price', [])
        sspList.length &&
          this.updateSspData(
            {
              price: value,
              sellingCurrency: this.specialSellingPrice.sellingCurrency
            },
            sspList
          )
      }
      if (!_.isEqual(currentCellData.value, value)) {
        currentCellData.data[field] = value
        if (['cost_price', 'selling_price', 'take_rate'].includes(field)) {
          let linkageData = priceLinkageUpdate({
            ...currentCellData.data,
            originField: field,
            exchange_rate: this.exchange_rate
          })

          if (linkageData.updateField) {
            currentCellData.data[linkageData.updateField] = linkageData[linkageData.updateField]
          }
        }
      }

      this.$forceUpdate()
      this.$emit('computedInvalidData', parseRowIndexByUUID(uuid))
    },
    handleClearFixedValue({ field, sku_id, uuid }) {
      let position = { field, sku_id, uuid }
      let index = this.parseRowIndexByUUID(uuid)
      let { data } = this.getCellDataByPosition({
        ...position,
        source: this.cacheTableData
      })

      if (this.isOnPriceField(field)) {
        for (let i of priceFieldList) {
          this.tableData[index][sku_id].price[i] = _.cloneDeep(data[i])
        }
      } else {
        this.tableData[index][sku_id]['inv_quantity'] = data['inv_quantity']
      }

      this.$emit('computedInvalidData', parseRowIndexByUUID(uuid))
    },
    blurTableCellEdit({ position, shouldReset }) {
      if (shouldReset) {
        let { uuid, sku_id, field } = position
        let index = this.parseRowIndexByUUID(uuid)

        let { value } = this.getCellDataByPosition({
          ...position,
          source: this.cacheTableData
        })

        if (this.isOnPriceField(field)) {
          this.tableData[index][sku_id].price[field] = value
        } else {
          this.tableData[index][sku_id][field] = value
        }
      }
    },
    clearEditTableCell() {
      this.currentEditCell = {}
    },
    getTips({ sku_id, field, uuid }) {
      let { skuData } = this.getCellDataByPosition({ sku_id, field, uuid })

      if (skuData.share_rel_ship === 1) {
        // sub sku
        return this.$t('27815')
      }

      return ''
    },
    handleEditCell({ sku_id, field, uuid }) {
      let index = this.parseRowIndexByUUID(uuid)
      let currentRowData = this.tableData[index]

      if (currentRowData.isDelete) {
        return
      }

      let position = { sku_id, field, uuid }

      if (position) {
        let skuRowData = currentRowData[sku_id]

        let { fixedData, fixedVal, isValid } = getFixedValueByRecord({
          currentSkuData: this.tableData[index][sku_id],
          field
        })

        if (isValid) {
          if (isOnPriceField(field)) {
            for (let field of priceFieldList) {
              skuRowData.price[field] = _.cloneDeep(fixedData[field])
            }
          } else {
            skuRowData['inv_quantity'] = fixedVal
          }
          this.$emit('computedInvalidData', parseRowIndexByUUID(uuid))
          this.$forceUpdate()
          return
        }

        this.currentEditCell = position
        if (
          field === 'special_selling_price' &&
          (skuRowData.price_can_edit || skuRowData.price_can_edit === undefined)
        ) {
          let { selling_price, selling_currency, special_selling_price } = skuRowData.price

          this.$set(this, 'specialSellingPrice', {
            selling_price,
            list: _.cloneDeep(special_selling_price),
            position,
            visible: true,
            sellingCurrency: selling_currency,
            skuId: sku_id,
            timePoints: [this.$store.state.destinationToday]
          })
        }
      }
    },
    handleDeleteRow(record) {
      record.isDelete = !record.isDelete

      if (record.isDelete) {
        let { uuid } = record

        this.$emit(
          'update:invalidRecordList',
          this.invalidRecordList.filter((item) => item.uuid !== uuid)
        )
      }
    },
    rowClassName(record) {
      let className = []
      if (record.isDelete) {
        className.push('row-is-delete')
      }

      return className.join(' ')
    },
    handleSSPDrawerConfirm(data) {
      let { uuid, sku_id, field } = this.specialSellingPrice.position
      let index = this.parseRowIndexByUUID(uuid)

      this.tableData[index][sku_id].price[field] = data

      this.currentEditCell = {}
      this.specialSellingPrice.visible = false

      this.$emit('computedInvalidData', parseRowIndexByUUID(uuid))
    }
  }
}
</script>

<style lang="scss">
.copy-popover-content .ant-tree {
  max-height: 360px;
  overflow-y: auto;
}

.auto-extend-table .ant-table-scroll table .ant-table-fixed-columns-in-body:not([colspan]) > * {
  display: none;
}
</style>

<style lang="scss" scoped>
.cell-header-title-custom {
  display: flex;
  align-items: center;
}
.is-required {
  display: inline-block;
  margin-right: 4px;
  color: #ff572f;
}
::v-deep {
  .ant-table .ant-table-tbody > tr > td {
    padding: 0 !important;
  }
  .row-is-delete {
    background-color: #fff0ec;
  }
  table {
    height: 1px;
  }
  td.ant-table-row-cell-break-word {
    height: 56px;
    line-height: 1.2em;
  }
}

.cell-actions {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .cell-btn-list {
    display: inline-flex;
    align-items: center;
  }
  .cell-copy-btn {
    margin-right: 4px;
    transform: scale(1.4);
  }
}

.table-row-cell {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border: 1px solid transparent;
  height: 100%;
  justify-content: space-between;
  transition: background-color 1s ease, border-color 1s ease;
  ::v-deep .delete-btn {
    transform: scale(0.75);
  }
  &.cell-is-invalid {
    border: 1px solid #ff5630;
  }
  &.cell-flex-end {
    justify-content: flex-end;
  }
  &.cell-is-fixed {
    background-color: rgba(255, 171, 0, 0.1);
  }
  &.cell-is-disabled {
    background-color: #f0f0f0;
    cursor: not-allowed;
  }
}
</style>
