import { checkAuth } from '@/plugins/authDirective'

export const autoExtendFields = [
  {
    key: 'cut_off_time',
    title: __('package_cut_off_time'),
    disabled: ['bd_audit', 'admin', 'merchant'],
    disabledFn({ role }) {
      return ['bd_audit', 'admin', 'merchant'].includes(role)
    }
  },
  {
    key: 'inv_quantity',
    title: __('package_inventory'),
    disabled: ['bd_audit'],
    disabledFn({ role }) {
      return ['bd_audit'].includes(role)
    }
  },
  {
    key: 'cost_price',
    title: __('package_price_cost'),
    disabled: ['bd_audit'],
    disabledFn({ role, merchantActHaveBeenApproved, ticketStatus }) {
      return (
        ['bd_audit'].includes(role) ||
        ticketStatus === 'PendingApproval' ||
        (merchantActHaveBeenApproved && !checkAuth('superPriceEdit'))
      ) // 商户活动或套餐被 BD approved 后，需要有 superPriceEdit 才能修改
    }
  },
  {
    key: 'retail_price',
    title: __('package_price_retail'),
    disabled: ['bd_audit'],
    disabledFn({ role }) {
      return ['bd_audit'].includes(role)
    }
  },
  {
    key: 'selling_price',
    title: __('package_price_selling'),
    hidden: ['merchant'],
    disabledFn({ role }) {
      return ['merchant'].includes(role)
    }
  },
  {
    key: 'take_rate',
    title: __('act_list_take_rate'),
    hidden: ['merchant'],
    disabledFn({ role }) {
      return ['merchant'].includes(role)
    }
  },
  {
    width: 250,
    key: 'special_selling_price',
    title: __('28682'),
    hidden: ['merchant'],
    disabledFn({ role }) {
      return ['merchant'].includes(role)
    }
  }
]

export const getTableFieldStateList = () => {
  let role = klook.getPlatformRoleKey()

  return autoExtendFields.reduce((acc, curr) => {
    if (Array.isArray(curr.hidden) && curr.hidden.includes(role)) {
      return acc
    }

    return [
      ...acc,
      {
        ...curr,
        disabled: Array.isArray(curr.disabled) ? curr.disabled.includes(role) : Boolean(curr.disabled)
      }
    ]
  }, [])
}
