<template>
  <div class="actionBar-container">
    <ul class="actionBar-explain-list actionBar-inline-center">
      <li v-if="showFixedPriceBar || showFixedInvBar" class="explain--item">
        <span
          class="rect content"
          :style="{
            backgroundColor: 'rgba(255, 171, 0, 0.1)'
          }"
        ></span>

        <a-tooltip v-if="showFixedPriceBar" placement="right">
          <template slot="title">
            {{ $t('27739') }}
          </template>
          <span class="fixed-tips"> {{ $t('pm_fixed_price') }} <a-icon type="question-circle" /> </span>
        </a-tooltip>

        <span v-if="showFixedPriceBar && showFixedInvBar" class="fixed-split">
          /
        </span>

        <a-tooltip v-if="showFixedInvBar" placement="right">
          <template slot="title">
            {{ $t('27734') }}
          </template>
          <span class="fixed-tips"> {{ $t('27733') }} <a-icon type="question-circle" /> </span>
        </a-tooltip>
      </li>
      <li v-if="showSubSkuBar" class="explain--item">
        <span
          class="rect content"
          :style="{
            backgroundColor: 'rgba(0, 0, 0, 0.09)'
          }"
        ></span>
        {{ $t('27756') }}
      </li>
      <li class="explain--item">
        <span class="eye-content">
          <i class="gg-eye"></i>
          &nbsp;/&nbsp;
          <span class="gg-eye-off">
            <span class="eye-off">/</span>
            <i class="gg-eye"></i>
          </span>
        </span>

        <a-tooltip placement="right">
          <template slot="title">
            {{ $t('29095') }}
          </template>
          <span class="fixed-tips"> {{ $t('27798') }} <a-icon type="question-circle" /> </span>
        </a-tooltip>
      </li>
      <slot name="other"></slot>
      <!--<li class="explain&#45;&#45;item">-->
      <!--<span class="content">-->
      <!--0 / 999-->
      <!--</span>-->
      <!--{{ $t('28158') }}-->
      <!--</li>-->
    </ul>

    <div class="actionBar-inline-center">
      <slot name="fullscreen"></slot>
      <a-dropdown v-model="visible">
        <div slot="overlay">
          <a-checkbox-group v-model="viewField" class="view-field-checkbox" @click.prevent.stop>
            <a-checkbox v-for="option in options" :key="option.key" :value="option.key">{{
              option.title
            }}</a-checkbox>
          </a-checkbox-group>
        </div>
        <a-button
          :data-spm-module="getFilterColumSpm"
          data-spm-virtual-item="__virtual"
          @click="filterButton($event)"
        >
          {{ $t('29257') }}
          <a-icon class="__icon" :type="visible ? 'up' : 'down'" />
        </a-button>
      </a-dropdown>
    </div>
  </div>
</template>

<script>
import { getBulkViewColumnsByPlatform } from './utils'

export default {
  name: 'ActionBar',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Array,
      required: true
    },
    showFixedPriceBar: {
      type: Boolean,
      default: true
    },
    showFixedInvBar: {
      type: Boolean,
      default: true
    },
    showSubSkuBar: {
      type: Boolean,
      default: true
    },
    exclude: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      fullscreen: false,
      visible: false
    }
  },
  computed: {
    getFilterColumSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `FilterColumn?oid=${oid}&trg=manual`
    },
    options() {
      return getBulkViewColumnsByPlatform(this.exclude)
    },
    viewField: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('change', v)
      }
    }
  },
  mounted() {
    this.handleFullscreen()
  },
  methods: {
    filterButton(e) {
      e.preventDefault()
    },
    handleFullscreen() {
      this.fullscreen = !this.fullscreen
      this.$emit('fullscreen', this.fullscreen)
    },
    getFixedTips() {
      let content = []

      if (this.showFixedPriceBar) {
        content.push(this.$t('27739'))
      }

      if (this.showFixedInvBar) {
        content.push(this.$t('27734'))
      }

      return content.join('<br />')
    }
  }
}
</script>

<style lang="scss" scoped>
.actionBar-container {
  display: flex;
  justify-content: space-between;
  margin: 24px 0 12px 0;
}
.actionBar-inline-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.actionBar-explain-list {
  flex-wrap: wrap;
  margin-right: 12px;
  .explain--item {
    display: inline-flex;
    align-items: center;
    margin-right: 24px;
    &:last-child {
      margin-right: 0;
    }
    .content {
      display: inline-block;
      min-width: 20px;
      margin-right: 8px;
    }
    .eye-content {
      display: flex;
      align-items: center;
    }
  }
  .rect {
    display: inline-block;
    width: 32px;
    height: 16px;
    line-height: 16px;
    border-radius: 2px;
  }
  .fixed-tips {
    display: inline-flex;
    align-items: center;
    ::v-deep .anticon {
      margin-left: 6px;
    }
  }
  .fixed-split {
    display: inline-block;
    margin: 0 6px;
  }
}
.view-field-checkbox {
  display: flex;
  flex-direction: column;
  padding: 12px;
  background-color: #fff;
  ::v-deep .ant-checkbox-wrapper {
    line-height: 2;
    margin-left: 0;
  }
}
.actionBar-container {
  ::v-deep {
    .__icon {
      font-size: 12px;
      transform: scale(0.83333333) rotate(0deg);
    }
  }
}
</style>
