<template>
  <div class="allCheckbox-container">
    <a-checkbox
      v-bind="$attrs"
      :indeterminate="indeterminate"
      :checked="checkAll"
      :style="$attrs.checkAllStyle || {}"
      @change="onCheckAllChange"
      @click="handleGTM"
    >
      {{ $t('act_lan_all') }}
    </a-checkbox>
    <a-checkbox-group v-model="checkedList" v-bind="$attrs">
      <a-checkbox v-for="opt of options" :key="opt[valueKey]" :value="opt[valueKey]" @click="handleGTM">
        <a-tooltip v-if="opt.tooltip">
          <template slot="title">
            {{ opt.tooltip }}
          </template>
          {{ opt[labelKey] }}
        </a-tooltip>

        <template v-else>
          {{ opt[labelKey] }}
        </template>
      </a-checkbox>
    </a-checkbox-group>
  </div>
</template>
<script>
export default {
  name: 'AllCheckbox',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Array,
      required: true
    },
    options: {
      type: Array,
      required: true
    },
    labelKey: {
      type: String,
      default: 'label'
    },
    valueKey: {
      type: String,
      default: 'value'
    },
    GTMKey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      indeterminate: false,
      checkAll: false
    }
  },
  watch: {
    checkedList: {
      immediate: true,
      deep: true,
      handler(v) {
        this.onChange(v)
      }
    }
  },
  computed: {
    checkedList: {
      set(v) {
        this.$emit('change', v)
      },
      get() {
        return this.value
      }
    }
  },
  methods: {
    handleGTM() {
      this.GTMKey && this.GTMHookItem(this.GTMKey)
    },
    onChange(checkedList) {
      this.indeterminate = !!checkedList.length && checkedList.length < this.options.length
      this.checkAll = checkedList.length === this.options.length
    },
    onCheckAllChange(e) {
      Object.assign(this, {
        checkedList: e.target.checked ? this.options.map((opt) => opt[this.valueKey]) : [],
        indeterminate: false,
        checkAll: e.target.checked
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.allCheckbox-container {
  display: flex;
  flex-wrap: wrap;
  ::v-deep {
    .ant-checkbox-group {
      display: contents;
    }
    .ant-checkbox-wrapper {
      line-height: 42px;
    }
  }
}
</style>
