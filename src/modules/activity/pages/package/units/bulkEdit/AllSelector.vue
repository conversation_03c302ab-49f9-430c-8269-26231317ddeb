<template>
  <div class="allSelector-container">
    <a-select
      show-search
      mode="multiple"
      v-bind="$attrs"
      v-model="currentValue"
      placeholder="please select"
      :filter-option="filterOption"
      v-on="$listeners"
      @change="changeSelect"
    >
      <a-select-option v-for="opt of currentOpts" :key="opt[valueKey]" :value="opt[valueKey]">
        {{ opt[labelKey] }}
      </a-select-option>
    </a-select>
    <div
      v-if="shouldSpm"
      id="time_slot_spm"
      :data-spm-module="getTimeSlotSpm"
      data-spm-virtual-item="__virtual"
    ></div>
  </div>
</template>

<script>
export default {
  name: 'AllSelector',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Array,
      required: true
    },
    options: {
      type: Array,
      required: true
    },
    labelKey: {
      type: String,
      default: 'label'
    },
    valueKey: {
      type: String,
      default: 'value'
    },
    showBulkOpt: {
      type: <PERSON>olean,
      default: true
    },
    shouldSpm: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      ALL_VALUE: 'selectAll',
      CLEAR_VALUE: 'clearAll'
    }
  },
  watch: {
    currentValue: {
      deep: true,
      handler(v) {
        if (v.includes(this.ALL_VALUE)) {
          this.$emit(
            'change',
            this.options.map((opt) => opt[this.valueKey])
          )
        } else if (v.includes(this.CLEAR_VALUE)) {
          this.$emit('change', [])
        } else {
          this.$emit('change', v)
        }
      }
    }
  },
  computed: {
    currentValue: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('change', v)
      }
    },
    currentOpts() {
      if (this.value.length && !this.value.includes(this.ALL_VALUE)) {
        return [
          {
            [this.labelKey]: __('act_blocklist_clear_all'),
            [this.valueKey]: this.CLEAR_VALUE
          },
          ...this.options
        ]
      }

      return [
        {
          [this.labelKey]: __('act_lan_all'),
          [this.valueKey]: this.ALL_VALUE
        },
        ...this.options
      ]
    },
    getTimeSlotSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `TimeslotFilter?oid=${oid}&trg=manual`
    }
  },
  methods: {
    changeSelect() {
      this.$tracker.track('action', '#time_slot_spm')
    },
    filterOption(input, option) {
      let { componentOptions } = option
      let label = componentOptions.children[0].text.trim().toLowerCase()
      let value = componentOptions.propsData.value

      input = input.toLowerCase()

      return label.indexOf(input) >= 0 || value.indexOf(input) >= 0
    }
  }
}
</script>

<style lang="scss" scoped></style>
