export default class BulkEditWorker {
  constructor(conf = {}) {
    return this.createWorker(this.init, {
      name: 'bulkEditWorker',
      ...conf
    })
  }
  init() {
    // copy from './utils.js' or bulkTable.vue
    const priceFieldList = [
      'cost_price',
      'retail_price',
      'selling_price',
      'take_rate',
      'special_selling_price'
    ]

    const isOnPriceField = (field) => {
      return priceFieldList.includes(field)
    }

    const priceLinkageUpdate = ({
      cost_price = '',
      selling_price = '',
      take_rate = '',
      toFixed = 2,
      cost_currency = '',
      selling_currency = '',
      originField,
      exchange_rate = []
    } = {}) => {
      let result = {
        cost_price,
        selling_price,
        take_rate,
        updateField: ''
      }
      // linkage need cost
      if (cost_price === '' || cost_price === undefined || cost_price === null) {
        return result
      }
      // 特殊场景 worker
      if (['selling_price', 'cost_price'].includes(originField)) {
        if (cost_price === 0) {
          result.updateField = 'take_rate'
          result.take_rate = 100

          return result
        } else {
          if (selling_price === 0) {
            result.updateField = 'take_rate'
            result.take_rate = undefined

            return result
          }
        }
      }

      // to_value: cost to selling rate
      // from_value: selling to cost rate
      let { to_value, from_value } = exchange_rate.reduce(
        (acc, curr) => {
          if (curr.from_currency === cost_currency && curr.to_currency === selling_currency) {
            return curr
          }

          return acc
        },
        {
          to_value: 1,
          from_value: 1
        }
      )

      if (originField === 'take_rate') {
        if (cost_price !== '') {
          // take_rate + cost_price => selling_price
          result.selling_price = ((cost_price * to_value * 100) / (100 - take_rate)).toFixed(toFixed)
          result.updateField = 'selling_price'
        } else if (selling_price !== '') {
          // take_rate + selling_price => cost_price
          result.cost_price = ((selling_price * from_value * (100 - take_rate)) / 100).toFixed(toFixed)
          result.updateField = 'cost_price'
        }
      } else {
        if (selling_price !== '' && cost_price !== '') {
          // selling_price + cost_price => take_rate
          result.take_rate = +(((selling_price - cost_price * to_value) / selling_price) * 100).toFixed(
            toFixed
          )
          result.updateField = 'take_rate'
        } else if (take_rate !== '' && selling_price !== '') {
          // selling_price + take_rate => cost_price
          result.cost_price = ((selling_price * from_value * (100 - take_rate)) / 100).toFixed(toFixed)
          result.updateField = 'cost_price'
        } else if (take_rate !== '' && cost_price !== '') {
          // cost_price + take_rate => selling_price
          result.selling_price = ((cost_price * to_value * 100) / (100 - take_rate)).toFixed(toFixed)
          result.updateField = 'selling_price'
        }
      }

      let value = result[result.updateField]
      if (result.updateField) {
        // denominator === 0， e.g. (100 - take_rate) === 0
        if (typeof value === 'string') {
          if (value.indexOf('Infinity') !== -1) {
            result[result.updateField] = 0
          } else {
            result[result.updateField] = +result[result.updateField]
          }
        } else if (isNaN(value)) {
          result[result.updateField] = ''
        }
      }

      return result
    }

    const isEqualSimple = (data, other) => {
      return JSON.stringify(data) === JSON.stringify(other)
    }

    const cloneDeepSimple = (data) => {
      return (data && JSON.parse(JSON.stringify(data))) || data
    }

    const isValidTableCellData = (value, field) => {
      let commonValid = !(
        value === null ||
        value === '' ||
        value === undefined ||
        value === -Infinity ||
        'NaN' === String(value)
      )

      let customValid = true
      if (['cost_price', 'retail_price', 'selling_price', 'inv_quantity'].includes(field)) {
        customValid = value >= 0
      }

      return commonValid && customValid
    }

    const getRate = (sellingCurrency, currency, rates) => {
      let arr = rates[sellingCurrency] || []
      let obj = arr.find((o) => o.to_currency === currency) || {}
      return obj.to_value || 0
    }

    const getComboDisabled = (key, sku = {}) => {
      const { combo_selling_price_type, is_combo } = sku
      if (!is_combo) {
        return false
      }
      if (key === 'publish_status') {
        return false
      }
      if (key === 'selling_price' && combo_selling_price_type === 3) {
        return false
      }
      return true
    }

    const getFieldEditableBySkuId = ({ field, data }) => {
      let { price_can_edit, quantity_can_edit, timeslot_can_edit } = data

      if (
        (isOnPriceField(field) && price_can_edit === false) ||
        (field === 'inv_quantity' && quantity_can_edit === false) ||
        (field === 'cut_off_time' && timeslot_can_edit === false)
      ) {
        return false
      }

      return true
    }

    const getCellEditableFlag = ({ skuFieldDisabledStateObj, sku, field, rowData }) => {
      return (
        !skuFieldDisabledStateObj[sku.sku_id][field] &&
        !getComboDisabled(field, sku) &&
        getFieldEditableBySkuId({ field, data: rowData[sku.sku_id] })
      )
    }

    // date1 < date2 则为 true
    const isDateAfter = (date1, date2) => {
      if (!date1 || !date2) {
        return false
      }

      return new Date(date1).getTime() < new Date(date2).getTime()
    }

    // 在一些场景下，值不能被修改
    const guardModify = (value, field, config = {}) => {
      if (field === 'publish_status') {
        const { isPresale, packageFresherLastDate, start_date } = config

        if (isPresale && packageFresherLastDate && start_date) {
          // 如果早于 last_date，则屏蔽 presale; 如果晚于 last_date，则屏蔽 publish
          if (isDateAfter(packageFresherLastDate, start_date)) {
            return value === 1
          } else {
            return value === 2
          }
        }
      }

      return false
    }

    // copy end

    self.onmessage = (evt) => {
      let {
        rates,
        bulkFormBySku,
        field,
        sku_id,
        allData,
        updateData,
        workerUUID,
        isFromFixed,
        shouldUpdateUUIDList,
        exchange_rate,
        cacheAllData,
        updateTableCellRecordListOnSku,
        packageFresherLastDate,
        lockEmptyDataFlag,
        skuFieldDisabledStateObj
      } = evt.data
      let isPriceFiled = isOnPriceField(field)
      // 缓存联动计算的结果
      let cacheLinkageResult = []
      // 更新了的字段 uuid 记录
      let currentUpdateTableCellRecordListOnSku = {}
      // 删除失效的更新字段 uuid 记录
      let deleteUpdateTableCellRecordListOnSku = {}

      let newAllData = allData.map((rowData, rowIndex) => {
        let { uuid, start_date, isPresale } = rowData
        let currentSkuData = rowData[sku_id]
        // 记录需要被更新的字段（因为有联动，所以更新字段可能会有多个）
        let currentUpdateFieldRecord = []

        // 如果是 presale 并且 date 晚于 last_date 则取消 lock
        let shouldLockEmptyDataFlag = lockEmptyDataFlag && currentSkuData.is_empty
        if (
          isPresale &&
          packageFresherLastDate &&
          start_date &&
          isDateAfter(packageFresherLastDate, start_date)
        ) {
          shouldLockEmptyDataFlag = false
        }

        const guardModifyConfig = {
          isPresale,
          start_date,
          packageFresherLastDate
        }

        if (
          currentSkuData.is_self_point && // 仅仅处理自己的点
          !shouldLockEmptyDataFlag &&
          shouldUpdateUUIDList.includes(uuid) // 仅处理被勾选的行数据
        ) {
          // 获取实际需要更新的字段，若值无效，则置为缓存的数值
          const currentCacheRowData = cacheAllData[rowIndex]
          const currentCacheSkuData = currentCacheRowData[sku_id]

          Object.keys(updateData).forEach((key) => {
            if (!hasOwnProperty.call(deleteUpdateTableCellRecordListOnSku, key)) {
              deleteUpdateTableCellRecordListOnSku[key] = []
            }

            // 跟表格 sku 行数据可编辑逻辑同步
            const editable = getCellEditableFlag({
              skuFieldDisabledStateObj,
              sku: currentSkuData,
              field: key,
              rowData
            })

            // 目前 fixed 的优先级大于 editable
            if (!isFromFixed && !editable) {
              deleteUpdateTableCellRecordListOnSku[key].push(uuid)

              return
            }

            let value = updateData[key]
            let cache = isPriceFiled ? currentCacheSkuData.price[key] : currentCacheSkuData[key]

            if (!isValidTableCellData(value, key) || guardModify(value, key, guardModifyConfig)) {
              value = cloneDeepSimple(cache)
            }

            if (isPriceFiled) {
              if (key === 'special_selling_price') {
                let { selling_price: newSp } = bulkFormBySku
                let newValue = cloneDeepSimple(value)
                newValue.forEach((item) => {
                  let oldSp = currentSkuData.price.selling_price
                  if (newSp != oldSp && item.pricing_rule_type === 1 && item.percent) {
                    let rate = getRate(currentSkuData.price.selling_currency, item.currency, rates)
                    let calcPrice = ((oldSp * item.percent) / 100) * rate
                    item.price = parseFloat(calcPrice.toFixed(2))
                  }
                })
                currentSkuData.price[key] = newValue
              } else {
                currentSkuData.price[key] = value
              }
            } else {
              currentSkuData[key] = value
            }

            if (isEqualSimple(cache, value)) {
              deleteUpdateTableCellRecordListOnSku[key].push(uuid)
            } else {
              currentUpdateFieldRecord.push(key)
            }
          })

          if (!currentUpdateFieldRecord.length) {
            Object.keys(updateData).forEach((key) => {
              deleteUpdateTableCellRecordListOnSku[key].push(uuid)
            })

            return currentSkuData
          }

          let priceLinkageField = ['cost_price', 'selling_price', 'take_rate']
          // 如果是价格字段，需要判断是否是自动填充的，若不是，需要计算是否触发了 价格/利率 联动
          if (isPriceFiled && !isFromFixed && priceLinkageField.includes(field)) {
            let linkageData
            let linkageParams = cloneDeepSimple(currentSkuData.price)
            delete linkageParams.retail_price
            delete linkageParams.special_selling_price
            // 如果找到就直接放回当前找到的对象，若没有则返回 null，类似 lodash: _.find
            let findLinkageData = cacheLinkageResult.reduce(
              (acc, curr) => (!acc && isEqualSimple(curr.key, linkageParams) ? curr : acc),
              null
            )

            if (findLinkageData) {
              linkageData = findLinkageData.linkageData
            } else {
              // not find the cache
              linkageData = priceLinkageUpdate(
                Object.assign({}, linkageParams, {
                  originField: field,
                  exchange_rate
                })
              )
              cacheLinkageResult.push({
                key: linkageParams,
                linkageData
              })
            }

            if (linkageData.updateField) {
              currentUpdateFieldRecord.push(linkageData.updateField)
              currentSkuData.price[linkageData.updateField] = linkageData[linkageData.updateField]
            }
          }

          currentUpdateFieldRecord = Array.from(new Set(currentUpdateFieldRecord)) // 去重
          currentUpdateFieldRecord.forEach((key) => {
            if (!hasOwnProperty.call(currentUpdateTableCellRecordListOnSku, key)) {
              currentUpdateTableCellRecordListOnSku[key] = []
            }
            currentUpdateTableCellRecordListOnSku[key].push(uuid)
          })
          if (['cost_price', 'selling_price', 'take_rate', 'retail_price'].includes(field)) {
            // 重新计算 ssp
            let sspList = currentSkuData.price.special_selling_price
            let { selling_currency, selling_price } = currentSkuData.price
            if (sspList && sspList.length) {
              sspList.forEach((item) => {
                if (item.pricing_rule_type === 1 && item.percent) {
                  let rate = getRate(selling_currency, item.currency, rates)
                  let calcPrice = ((selling_price * item.percent) / 100) * rate
                  item.price = parseFloat(calcPrice.toFixed(2))
                }
              })
            }
          }
          return isPriceFiled
            ? {
                price: currentSkuData.price
              }
            : currentUpdateFieldRecord.reduce((acc, curr) => {
                // 只返回当前被更新的字段数据
                acc[curr] = currentSkuData[curr]

                return acc
              }, {})
        }

        return {}
      })

      updateTableCellRecordListOnSku = Object.keys(updateTableCellRecordListOnSku).reduce((acc, key) => {
        // 回填更新记录
        if (hasOwnProperty.call(currentUpdateTableCellRecordListOnSku, key)) {
          acc[key] = Array.from(new Set(currentUpdateTableCellRecordListOnSku[key]))
        } else {
          acc[key] = updateTableCellRecordListOnSku[key] || []
        }
        // 删除被重置初始值的记录
        if (hasOwnProperty.call(deleteUpdateTableCellRecordListOnSku, key)) {
          acc[key] = acc[key].filter((_uuid) => !deleteUpdateTableCellRecordListOnSku[key].includes(_uuid))
        }

        return acc
      }, {})

      postMessage({
        workerUUID,
        updateTableCellRecordListOnSku,
        newAllData
      })
      self.close()
    }
  }
  createWorker(
    fn,
    conf = {
      type: 'module'
    }
  ) {
    let blob = new Blob([';(function ' + fn.toString() + ')()'])
    let url = window.URL.createObjectURL(blob)

    return new Worker(url, conf)
  }
}
