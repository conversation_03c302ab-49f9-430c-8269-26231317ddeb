<template>
  <div class="bulkTable-container" @click="handelEditCell">
    <div class="tips" v-if="tooLargeResText">
      {{ tooLargeResText }}
    </div>

    <a-spin :spinning="spinning" :delay="100">
      <div class="table-container common-scrollbar">
        <!-- left fix table -->
        <bulk-edit-table-fix
          v-model="selectAll"
          v-if="showTableFix && viewItemSizeList.length"
          ref="fixTable"
          :class="{
            'table-scroll-fixed': tableScrollFixedFlag
          }"
          :scroll-top="scrollTop"
          :transform-y="transformY"
          :phantom-height="phantomHeight"
          :height="realTableHeight"
          :visible-data="visibleData"
          :viewItemSizeList="viewItemSizeList"
          :hasChecked="hasChecked"
          :indeterminate="indeterminate"
          :custom-checked="customChecked"
          :deleteTableRowUUIDList="deleteTableRowUUIDList"
          :is-deletable="isDeletable"
          :lock-delete-btn="lockDeleteBtn"
          :shouldOpenHeight="!!shouldOperatorSkuList.length && isMerchant"
          v-bind="$attrs"
          @confirmSelectAll="confirmSelectAll"
          @undoDeleteRow="undoDeleteRow"
          @deleteSelectedRow="deleteSelectedRow"
          @changeCustomChecked="changeCustomChecked"
        ></bulk-edit-table-fix>

        <div class="table">
          <!-- table head and contain bulk form  -->
          <bulk-edit-thead
            v-model="bulkForm"
            ref="thead"
            :all-sku="allSku"
            :disabled="!hasChecked"
            :view-fields="viewBulkColumnField"
            :exchange_rate="exchange_rate"
            :custom-checked="customChecked"
            :skuFieldDisabledStateObj="skuFieldDisabledStateObj"
            :skuFieldLockStateObj="skuFieldLockStateObj"
            :shouldOperatorSkuList="shouldOperatorSkuList"
            @changeBulkEditForm="changeBulkEditForm"
            @bulkEditSSP="handleBulkEditSSP"
            @clearBulkFixedValue="clearBulkFixedValue"
            :bindSspList2mixin="bindSspList2mixin"
            v-bind="$attrs"
          ></bulk-edit-thead>

          <!-- bulk edit scroll table -->
          <bulk-edit-tbody
            ref="tbody"
            class="table-body common-scrollbar"
            :viewItemSizeList="viewItemSizeList"
            :visibleData="visibleData"
            :estimatedItemSize="estimatedItemSize"
            :height="realTableHeight"
            v-slot="slotProps"
            v-model="scrollEventCalculating"
            v-bind="$attrs"
            :key="viewItemSizeList.length"
            :class="{
              'exist-scroll-bar': existScrollBar
            }"
            @changeScrollTop="changeScrollTop"
            @changePhantomHeight="changePhantomHeight"
            @changeTransformVal="changeTransformVal"
            @changeViewIndex="changeViewIndex"
            @clearEditTableCell="clearEditTableCell"
          >
            <div
              class="table-row"
              :style="{
                height: CellHeight + 'px'
              }"
              :key="slotProps.item.uuid"
              :class="{
                'row-is-delete': deleteTableRowUUIDList.includes(slotProps.item.uuid),
                'row-is-calm': !slotProps.scrollEventCalculating,
                'row-is-checked': customChecked.includes(slotProps.item.uuid)
              }"
            >
              <div class="table-row-cell cell-fixed-width">
                {{ slotProps.item.start_date }}
              </div>
              <div class="table-row-cell cell-fixed-width">
                {{ slotProps.item.start_time }}
              </div>

              <template v-for="sku of allSku">
                <template v-for="column of viewBulkColumnField">
                  <div
                    v-if="slotProps.item[sku.sku_id] && !slotProps.item[sku.sku_id].is_self_point"
                    :key="`${slotProps.item.uuid}-${sku.sku_id}-${column.key}`"
                    class="table-row-cell"
                  ></div>

                  <template v-else-if="getFieldShow(column.key, sku)">
                    <!-- 因为 component is 会有样式问题，所以暂时这么处理，待优化。 -->
                    <approvalDetailPopover
                      v-if="
                        slotProps.item[sku.sku_id] &&
                        getNameByTicketStatusOnCostField({
                          field: column.key,
                          ticket_status: slotProps.item[sku.sku_id].ticket_status
                        })
                      "
                      :key="`component-${slotProps.item.uuid}-${sku.sku_id}-${column.key}`"
                      :ticket-id="slotProps.item[sku.sku_id].ticket_id"
                    >
                      <!-- self timeslot point -->
                      <editable-cell
                        class="table-row-cell"
                        :style="column.style"
                        :key="`${slotProps.item.uuid}-${sku.sku_id}-${column.key}`"
                        :class="
                          getTableCellClassName({
                            uuid: slotProps.item.uuid,
                            sku_id: sku.sku_id,
                            field: column.key
                          })
                        "
                        :data-uuid="slotProps.item.uuid"
                        :data-sku_id="sku.sku_id"
                        :data-field="column.key"
                        :record="slotProps.item"
                        :sku_id="sku.sku_id"
                        :is-presale="sku.allowed_presale"
                        :field="column.key"
                        :editable="
                          getCellEditableFlag({
                            sku: sku,
                            field: column.key,
                            rowData: slotProps.item
                          })
                        "
                        :field-lock="skuFieldLockStateObj[sku.sku_id][column.key]"
                        :data-is_combo="getComboDisabled(column.key, sku)"
                        :currentEditCell="currentEditCell"
                        :invModel="allSkuModelObject[sku.sku_id].inv_model"
                        :cache-data="getEditableCellCache(slotProps.index, column.key, sku.sku_id)"
                        :showCostInputSuffixData="showCostInputSuffixData"
                        :lockEmptyDataFlag="
                          getEditLockValueByPath({
                            sku_id: sku.sku_id,
                            path: 'inventory.add_timeslot'
                          }).status
                        "
                        @clearFixedValue="clearFixedValue"
                        @blurTableCell="blurTableCellEdit"
                        @updateTableCell="updateTableCell"
                        @clearEditTableCell="clearEditTableCell"
                      ></editable-cell>
                    </approvalDetailPopover>

                    <!-- self timeslot point -->
                    <editable-cell
                      v-else
                      class="table-row-cell"
                      :style="column.style"
                      :key="`${slotProps.item.uuid}-${sku.sku_id}-${column.key}`"
                      :class="
                        getTableCellClassName({
                          uuid: slotProps.item.uuid,
                          sku_id: sku.sku_id,
                          field: column.key
                        })
                      "
                      :data-uuid="slotProps.item.uuid"
                      :data-sku_id="sku.sku_id"
                      :data-field="column.key"
                      :record="slotProps.item"
                      :sku_id="sku.sku_id"
                      :is-presale="sku.allowed_presale"
                      :field="column.key"
                      :editable="
                        getCellEditableFlag({
                          sku: sku,
                          field: column.key,
                          rowData: slotProps.item
                        })
                      "
                      :field-lock="skuFieldLockStateObj[sku.sku_id][column.key]"
                      :data-is_combo="getComboDisabled(column.key, sku)"
                      :currentEditCell="currentEditCell"
                      :invModel="allSkuModelObject[sku.sku_id].inv_model"
                      :cache-data="getEditableCellCache(slotProps.index, column.key, sku.sku_id)"
                      :showCostInputSuffixData="showCostInputSuffixData"
                      :lockEmptyDataFlag="
                        getEditLockValueByPath({
                          sku_id: sku.sku_id,
                          path: 'inventory.add_timeslot'
                        }).status
                      "
                      @clearFixedValue="clearFixedValue"
                      @blurTableCell="blurTableCellEdit"
                      @updateTableCell="updateTableCell"
                      @clearEditTableCell="clearEditTableCell"
                    ></editable-cell>
                  </template>
                </template>
              </template>
            </div>
          </bulk-edit-tbody>
          <!-- end bulk edit scroll table -->
        </div>
      </div>
    </a-spin>

    <SpecialSellingPriceDrawer
      :price="specialSellingPrice.selling_price"
      :visible.sync="specialSellingPrice.visible"
      :initial-value="specialSellingPrice.list"
      v-bind="specialSellingPrice"
      @confirm="handleSSPDrawerConfirm"
    />

    <PriceReasonModal :visible.sync="priceReason.visible" @confirm="handleConfirmToSubmit" />

    <!-- 埋点 -->
    <div id="select_all_spm" :data-spm-module="getSelectAllSpm" data-spm-virtual-item="__virtual"></div>
  </div>
</template>

<script>
import moment from 'moment'
import {
  getBulkViewColumnsByPlatform,
  publishStatusOpts,
  parseRowIndexByUUID,
  cutOffTimeOpts,
  isValidTableCellData,
  fmtTimeBitFill,
  getPriceFieldBind,
  isOnPriceField,
  getCellDataByPosition,
  getTimestampByDate,
  getDateByTimestamp,
  priceLinkageUpdate,
  getFixedValueByRecord,
  setFixedValueByRecord,
  priceFieldList,
  fmtI18nKey,
  weeklyOpts,
  getComboDisabled
} from './utils'
// import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'
import SpecialSellingPriceDrawer from '@activity/pages/package/units/components/SpecialSellingPriceDrawer.vue'
import PriceReasonModal from '@activity/pages/package/units/components/PriceReasonModal.vue'
import BulkEditTbody from './Tbody.vue'
import EditableCell from './EditableCell.vue'
import BulkEditThead from './Thead'
import BulkEditTableFix from './TableFix'
import BulkEditWorker from './BulkEditWorker.js'
import { mapState, mapGetters } from 'vuex'
import actMixins from '@activity/mixins/index.js'
import maps from '@activity/utils/maps.js'
import ApprovalDetailPopover from '../unit-list/calendar-setting/components/approvalDetailPopover'
import { getEditLockValueByPath, getPackageFresherLastDate } from '@activity/utils'
import { isStatusPublished, isStatusPresale } from '@activity/pages/package/units/bulkEdit/utils'

const worker_mixin = {
  data() {
    this.validWorkerQueue = []

    return {
      currentWorkerUpdateData: null,
      maximum_cell: 3000 * 8 // 最大单元格
    }
  },
  computed: {
    getSelectAllSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `SelectAll?oid=${oid}&trg=manual`
    }
  },
  methods: {
    getUUID() {
      const temp_url = URL.createObjectURL(new Blob())
      const uuid = temp_url.toString()
      URL.revokeObjectURL(temp_url)

      return uuid.substr(uuid.lastIndexOf('/') + 1)
    },
    /**
     * 发送数据到 worker 计算，等待 worker message
     * @param {string} field
     * @param {number} sku_id
     * @param {object} updateData - 需要被批量更新的对象 { [${field}]: ${value} }
     * @param {boolean} isFromFixed - 是否来自批量处理固定数值
     * @param {array} shouldUpdateUUIDList - 需要更新的 UUID 列表
     */
    sendToWorker({
      field,
      sku_id,
      isFromFixed = false,
      updateData,
      shouldUpdateUUIDList = this.customChecked
    } = {}) {
      // 清除当前 sku 下字段的重复结果
      let { validWorkerQueue } = this

      const priceLinkageField = ['selling_price', 'cost_price', 'take_rate', 'retail_price']
      if (!isFromFixed && priceLinkageField.includes(field)) {
        updateData = priceLinkageField.reduce((acc, curr) => {
          let value = this.bulkForm[sku_id][curr]

          if (
            curr === field || // 当前修改的字段，无论数值什么都算数
            isValidTableCellData(value, curr) // 当修改的是价格的时候，当前 sku 的批量修改价格字段的数值，如果有效，也需要一起带入去更新，计算联动
          ) {
            return {
              ...acc,
              [curr]: value
            }
          }

          return acc
        }, {})
      }

      if (_.isEqual(this.currentWorkerUpdateData, arguments)) {
        // 拦截 ant-input-number onBlur 的重复触发
        this.currentWorkerUpdateData = null
        return
      }
      this.currentWorkerUpdateData = _.cloneDeep(arguments)

      let currentWorker = new BulkEditWorker()
      currentWorker.onmessage = (response) => {
        this.workerCallback(response.data)
        this.forceUpdateViewData()
      }

      validWorkerQueue = validWorkerQueue.filter((item) => {
        if (isOnPriceField(field)) {
          return !(item.sku_id === sku_id && isOnPriceField(item.field))
        }

        return !(item.sku_id === sku_id && item.field === 'inv_quantity')
      })
      let workerUUID = this.getUUID()
      validWorkerQueue.push({
        workerUUID,
        sku_id,
        field,
        isFromFixed,
        updateData
      })
      this.validWorkerQueue = validWorkerQueue
      let rates = this.sspPriceMixin.rates
      currentWorker.postMessage({
        rates,
        bulkFormBySku: this.bulkForm[sku_id],
        field,
        sku_id,
        workerUUID,
        updateData,
        isFromFixed,
        allData: this.allData,
        shouldUpdateUUIDList: this.customChecked,
        exchange_rate: this.exchange_rate,
        cacheAllData: this.localCacheAllData,
        updateTableCellRecordListOnSku: this.updateTableCellRecordList[sku_id],
        packageFresherLastDate: this.getPackageFresherLastDate(sku_id),
        lockEmptyDataFlag: this.getEditLockValueByPath({
          sku_id,
          path: 'inventory.add_timeslot'
        }).status,
        skuFieldDisabledStateObj: this.skuFieldDisabledStateObj
      })
      console.log('allData', this.allData)
    },
    workerCallback(data) {
      let { newAllData, workerUUID, updateTableCellRecordListOnSku } = data

      let { validWorkerQueue } = this
      let validWorkerResponse = _.find(validWorkerQueue, { workerUUID })
      if (validWorkerResponse) {
        let { sku_id, field, isFromFixed } = validWorkerResponse
        let isPriceField = isOnPriceField(field)
        if (field === 'selling_price' && !isFromFixed) {
          newAllData.forEach((o) => {
            let sspList = o.price && o.price.special_selling_price
            let updatePrice = _.get(validWorkerResponse, 'updateData.selling_price', 0)
            let oldPrice = _.get(o, 'price.selling_price', 0)
            if (sspList && sspList.length) {
              this.bindSspList2mixin(sspList, {
                price: updatePrice == oldPrice ? updatePrice : oldPrice,
                sellingCurrency: _.get(o, 'price.selling_currency', '')
              })
            }
          })
        }

        this.validWorkerQueue = validWorkerQueue.filter((item) => {
          let isAssociateField = false
          if (isPriceField) {
            isAssociateField = isOnPriceField(item.field)
          } else {
            isAssociateField = item.field === field
          }

          return !(item.sku_id === sku_id && isAssociateField)
        })

        this.allData = this.allData.map((item, index) => {
          return {
            ...item,
            [sku_id]: {
              ...item[sku_id],
              ...newAllData[index]
            }
          }
        })

        // if (Object.keys(updateData).includes('special_selling_price')) {
        //   this.spinning = true
        //   setTimeout(() => {
        //     this.$refs.tbody && this.$refs.tbody.resetTableState()
        //     this.spinning = false
        //   })
        // }

        console.log('当前数据: ', workerUUID)
        this.updateTableCellRecordList[sku_id] = updateTableCellRecordListOnSku
        // this.setUpdateTableCellRecordList(updateTableCellRecordList)
        // this.chunkToSendUpdateRecord(updateTableCellRecordList)
      } else {
        console.log('当前数据废弃: ', workerUUID)
      }
      this.emitTableState()
    },
    chunkToSendUpdateRecord(data, definition = 20) {
      let chunkData = _.chunk(data, definition)
      let index = 0

      const run = () => {
        requestAnimationFrame(() => {
          this.setUpdateTableCellRecordList(chunkData[index])
          index++

          if (index < chunkData.length - 1) {
            run()
          }
        })
      }

      run()
    }
  }
}

export default {
  name: 'BulkTable',
  inject: ['activity_id', 'sendGuide'],
  mixins: [worker_mixin, actMixins.sspPriceMixin],
  provide() {
    return {
      activityId: +this.activity_id
    }
  },
  components: {
    ApprovalDetailPopover,
    BulkEditTableFix,
    BulkEditThead,
    EditableCell,
    BulkEditTbody,
    // OverflowTextTooltip,
    SpecialSellingPriceDrawer,
    PriceReasonModal
  },
  props: {
    // 预估的行高度，用于初始化虚拟滚动高度
    estimatedItemSize: {
      type: Number,
      default: 40
    },
    list: {
      type: Array,
      required: true
    },
    // 当前表格展示的字段，与表格界面 filter 操作关联
    viewField: {
      type: Array,
      default: () => []
    },
    excludeField: {
      type: Array,
      default: () => []
    },
    skuData: {
      type: Array,
      required: true
    },
    // 可自定义需要显示的字段
    customColumnFieldList: {
      type: Array,
      default: undefined
    },
    // 需要工单系统处理的 sku
    shouldOperatorSkuList: {
      type: Array,
      default: () => []
    },
    // 是否需要左侧的固定列
    showTableFix: {
      type: Boolean,
      default: true
    },
    // 用于支持工单系统里的本地搜索过滤功能
    // 需要搭配 state.displayTableIndexByFilter 使用
    activateDisplayTableIndexByFilter: {
      type: Boolean,
      default: false
    },
    // 初次打开的编辑操作指南
    openGuide: {
      type: Boolean,
      default: true
    },
    // 工单系统 approval 操作
    openApprovalDetailPopover: {
      type: Boolean,
      default: true
    },
    // 展示成本的货币后缀
    showCostInputSuffixData: {
      type: Boolean,
      default: true
    }
  },
  data() {
    this.cacheBulkForm = {}
    // 价格小数点
    this.toFixedConstant = 2
    // cache
    this.localCacheAllData = []
    // 当前可是数据的原数据
    this.cacheVisibleData = []
    // 跟踪更新表格，用于显示标亮
    this.updateTableCellRecordList = {}
    this.allData = []

    return {
      CellHeight: 40, // 40 px
      exchange_rate: [],
      scrollEventCalculating: false,
      spinning: false,

      // allData: [],
      viewItemSizeList: [],
      invTotal: {},

      // 价格修改需要弹窗填写原因
      priceReason: {
        visible: false,
        data: null
      },

      // 编辑 SSP 的数据结构
      specialSellingPrice: {
        selling_price: '',
        list: [],
        visible: false,
        sellingCurrency: '',
        position: null,
        isFromBulk: false,
        skuId: null,
        timePoints: []
      },

      selectAll: false,
      customChecked: [],
      bulkForm: {},

      publishStatusOpts,
      cutOffTimeOpts,

      allSku: [],
      // 记录被删除的行
      deleteTableRowUUIDList: [],

      currentEditCell: {},

      scrollTop: 0,
      phantomHeight: 0,
      transformY: 0,
      visibleData: [],

      tableLength: 0,

      // 数据过多提示
      tooLargeResText: '',

      tableScrollFixedFlag: false,

      skuFresherSettingData: null
    }
  },
  watch: {
    // 表格搜索过滤后展示
    displayTableIndexByFilter: {
      deep: true,
      handler() {
        this.changeViewIndex(this.viewIndexPosition)
        this.initViewItemSizeList()
      }
    },
    'specialSellingPrice.visible': {
      handler(v) {
        if (!v) {
          this.currentEditCell = {}
        }
      }
    },
    tableState: {
      deep: true,
      immediate: true,
      handler() {
        this.$emit('tableState', this.tableState)
      }
    },
    'priceReason.visible': {
      handler(v) {
        this.spinning = v
      }
    },

    customChecked: {
      deep: true,
      handler() {
        this.$set(this, 'bulkForm', _.cloneDeep(this.cacheBulkForm || {}))
      }
    },
    list: {
      immediate: true,
      deep: true,
      async handler(v, oldV) {
        if (_.isEqual(v, oldV)) {
          return
        }

        if (this.list.length) {
          const resp = await ajax.get(ADMIN_API.act.mget_inv_price_page_setting, {
            params: {
              sku_ids: this.list.map((sku) => sku.sku_id).join()
            }
          })

          const skuFresherSettingData = resp.reduce(
            (acc, curr) => {
              return {
                skus: {
                  ...acc.skus,
                  ...(curr.skus || [])
                },
                tips_map: {
                  ...acc.tips_map,
                  ...(curr.tips_map || [])
                }
              }
            },
            {
              skus: [],
              tips_map: []
            }
          )

          this.$set(this, 'skuFresherSettingData', skuFresherSettingData)
        } else {
          this.$set(this, 'skuFresherSettingData', null)
        }

        // fmt all data
        let allSku = this.list.reduce((acc, curr) => {
          // eslint-disable-next-line no-unused-vars
          let { calendar, ...skuInfo } = curr // for delete calendar field
          skuInfo.retail_currency = skuInfo.selling_currency

          let currentSkuInfo = _.find(this.skuData, {
            sku_id: skuInfo.sku_id
          })

          acc.push({
            ...(currentSkuInfo || {}),
            ...skuInfo
          }) // col data

          return acc
        }, [])
        await this.getAllRates2mixin(allSku)
        await this.getAllExchangeRate(allSku)

        this.updateTableCellRecordList = allSku.reduce(
          (acc, curr) => ({
            ...acc,
            [curr.sku_id]: this.platformBulkViewColumns.reduce(
              (acc2, curr2) => ({
                ...acc2,
                [curr2.key]: []
              }),
              {
                days: [],
                time: []
              }
            )
          }),
          {}
        )

        let { allData, bulkForm } = this.list.reduce(
          (acc, curr) => {
            let { calendar, ...skuInfo } = curr

            // init bulk form data
            Object.assign(acc.bulkForm, {
              [curr.sku_id]: this.getInitBulkFormDataBySku(calendar, skuInfo)
            })

            // init the row data of table key operation
            calendar = calendar || []

            calendar.forEach((currentCalendar, idx) => {
              let [start_date, start_time] = currentCalendar.start_time.split(' ')
              start_time = start_time.slice(0, 5)

              if (!acc.allData[idx]) {
                acc.allData[idx] = {
                  uuid: '_' + idx, // row index
                  start_date,
                  start_time,
                  week_day: this.weeklyLabelObject[moment(start_date).day()],
                  isPresale: !!curr?.allowed_presale,
                  isDelete: false,
                  isChange: false
                }
              }

              // Initialize price data
              if (currentCalendar.price) {
                // calculate rate
                let linkageData = priceLinkageUpdate({
                  ...currentCalendar.price,
                  originField: 'cost_price',
                  exchange_rate: this.exchange_rate
                })

                if (linkageData.updateField) {
                  currentCalendar.price[linkageData.updateField] = linkageData[linkageData.updateField]
                  if (!~linkageData.updateField.indexOf('take_rate')) {
                    this.setUpdateTableCellRecordList([
                      {
                        sku_id: curr.sku_id,
                        uuid: '_' + idx,
                        field: linkageData.updateField
                      }
                    ])
                  }
                } else {
                  currentCalendar.price.take_rate = undefined
                }

                if (currentCalendar.price.special_selling_price === null) {
                  currentCalendar.price.special_selling_price = []
                }
              } else {
                currentCalendar.price = {
                  // when price -> null
                  cost_price: '',
                  retail_price: '',
                  selling_price: '',
                  cost_currency: skuInfo.cost_currency,
                  selling_currency: skuInfo.selling_currency,
                  retail_currency: skuInfo.selling_currency,
                  take_rate: undefined,
                  special_selling_price: []
                }
              }

              // 后端返回存在空和值无效的问题，所以需要洗，可以跟 Bulk 的共用
              if (currentCalendar.auto_fill_schedule_item) {
                currentCalendar.auto_fill_schedule_item = Object.freeze(
                  acc.bulkForm[curr.sku_id].auto_fill_schedule_item
                )
              }

              // calculate time to show only
              let { days, time } = getDateByTimestamp(currentCalendar.cut_off_time)
              // 需要外层数据同步到 sku 列的数据
              const { ticket_id, ticket_status, combo_selling_price_type, is_combo, sku_id } = skuInfo
              // concat all init data
              Object.assign(acc.allData[idx], {
                [curr.sku_id]: {
                  ...currentCalendar,
                  // cut_off_time
                  ticket_id,
                  ticket_status,
                  sku_id,
                  combo_selling_price_type,
                  is_combo,
                  days,
                  time,
                  isChildSku: _.get(skuInfo, 'share_rel_ship.share_rel_ship', 0) === 1
                }
              })
            })

            return acc
          },
          {
            allData: [], // all row data on table
            bulkForm: {} // bulk edit form on HTML<thead>
          }
        )

        // the number of rows * sku.length * 8 field col
        if (allSku.length * 8 * allData.length > this.maximum_cell) {
          this.tooLargeResText = fmtI18nKey(this.$t('29251'), {
            num1: allData.length * allSku.length,
            num2: Math.ceil(this.maximum_cell / 8)
          })
        } else {
          this.tooLargeResText = ''

          this.allSku = allSku
          this.bulkForm = _.cloneDeep(bulkForm)
          this.cacheBulkForm = _.cloneDeep(bulkForm)

          this.allData = allData
          // 初始化可看视图的高度集，用于虚拟滚动撑高滚动条
          this.initViewItemSizeList()

          this.localCacheAllData = Object.freeze(_.cloneDeep(allData))
          this.tableLength = this.allData.length
        }

        this.resetAllData()
        this.$nextTick(() => {
          this.$refs.tbody && this.$refs.tbody.resetTableState()
          if (allData.length && this.openGuide) {
            this.sendGuide([
              {
                uuid: 'thead-edit',
                ele: document.querySelector('.thead-row-field'),
                style: {
                  bottom: '100%',
                  left: '300px'
                },
                onlyForFirstTime: true,
                content: this.$t('30289')
              },
              {
                uuid: 'tbody-cell',
                ele: document.querySelector('.tbody'),
                style: {
                  bottom: 'inherit',
                  top: '108px',
                  right: 'inherit',
                  left: '600px'
                },
                onlyForFirstTime: true,
                content: this.$t('30301')
              }
            ])
          }
        })
      }
    } // end, list
  },
  computed: {
    ...mapState(['isMerchant', 'displayTableIndexByFilter', 'merchantActHaveBeenApproved']),
    ...mapGetters(['getCurrentPackageApproveStatus', 'isTour']),
    lockDeleteBtn() {
      for (let sku of this.list) {
        const res = this.getEditLockValueByPath({
          sku_id: sku.sku_id,
          path: 'inventory.del_timeslot'
        })

        if (res.status) {
          return res
        }
      }

      return {
        status: false,
        tips: ''
      }
    },
    ticket_status() {
      return maps.ticket_status
    },
    weeklyLabelObject() {
      return weeklyOpts.reduce((acc, curr) => {
        return {
          ...acc,
          [curr.value]: curr.short
        }
      }, {})
    },
    skuFieldLockStateObj() {
      return this.allSku.reduce((acc, curr) => {
        let sku_id = curr.sku_id
        let allowed_presale = curr.allowed_presale

        return {
          ...acc,
          [sku_id]: this.platformBulkViewColumns.reduce((accField, currField) => {
            return {
              ...accField,
              [currField.key]: this.getEditLockByField({ field: currField.key, sku_id, allowed_presale })
            }
          }, {})
        }
      }, {})
    },
    skuFieldDisabledStateObj() {
      return this.allSku.reduce((acc, curr) => {
        let sku_id = curr.sku_id
        let { share_rel_ship, pricing_model, inv_model, ticket_status } = this.allSkuModelObject[sku_id]

        return {
          ...acc,
          [sku_id]: this.platformBulkViewColumns.reduce((accField, currField) => {
            let disabled

            if (currField.disabledFn) {
              disabled = currField.disabledFn({
                share_rel_ship: share_rel_ship.share_rel_ship,
                pricing_model,
                inv_model,
                role: this.pageRole,
                // approveStatus: 0: none, 1: pending, 2: reject, 3: resolve
                approveStatus: this.getCurrentPackageApproveStatus,
                ticketStatus: ticket_status,
                merchantActHaveBeenApproved: this.merchantActHaveBeenApproved
              })
            } else {
              disabled = currField.disabled !== undefined ? currField.disabled : true
            }

            return {
              ...accField,
              [currField.key]: disabled
            }
          }, {})
        }
      }, {})
    },
    allSkuModelObject() {
      return this.allSku.reduce((acc, curr) => {
        return {
          ...acc,
          [curr.sku_id]: curr
        }
      }, {})
    },
    pageRole() {
      return klook.getPlatformRoleKey()
    },
    isBdAudit() {
      return this.pageRole === 'bd_audit'
    },
    disabledByApproveStatus() {
      return this.pageRole === 'merchant' && this.getCurrentPackageApproveStatus === 1 // merchant 套餐待审核状态不可编辑
    },
    tableState() {
      if (this.disabledByApproveStatus) {
        return false
      }

      return {
        updated: this.hasUpdateRecord(),
        deleted: this.deleteTableRowUUIDList.length
      }
    },
    platformBulkViewColumns() {
      return this.customColumnFieldList || getBulkViewColumnsByPlatform(this.excludeField)
    },
    // For the field of view filter
    viewBulkColumnField() {
      if (this.viewField.length) {
        return this.platformBulkViewColumns.filter((item) => this.viewField.includes(item.key))
      }

      return this.platformBulkViewColumns
    },
    hasChecked() {
      return Boolean(this.customChecked.length)
    },
    // for a-checkbox-group
    indeterminate() {
      let { tableLength } = this
      if (this.activateDisplayTableIndexByFilter) {
        tableLength = this.displayTableIndexByFilter.length
      }

      return !!(this.customChecked.length && this.customChecked.length !== tableLength)
    },
    // Table or offset
    maxTableHeight() {
      return document.body.clientHeight - 340
    },
    realTableHeight() {
      return this.realTableHeightNumber + 'px'
    },
    realTableHeightNumber() {
      if (!this.viewItemSizeList.length) {
        return '200px'
      }

      let last = this.viewItemSizeList.slice(-1)[0]

      return Math.min((last && last.bottom + 2) || this.maxTableHeight, this.maxTableHeight)
    },
    existScrollBar() {
      return this.realTableHeightNumber < this.phantomHeight
    },
    // table fix 删除按钮状态
    isDeletable() {
      const isPic = this.allSku.some((item) => item.inv_model === 4)
      if (isPic) {
        return {
          deletable: false,
          title: this.$t('48468')
        }
      }
      const isCombo = this.allSku.some((item) => item.is_combo)
      if (isCombo) {
        return {
          deletable: false,
          title: this.$t('73053')
        }
      }
      return {
        deletable: true,
        title: __('global_delete')
      }
    }
  },
  mounted() {
    document.querySelector('.table-container').addEventListener('scroll', this.horizontalScroll)
  },
  methods: {
    isOnPriceField,
    priceLinkageUpdate,
    parseRowIndexByUUID,
    isValidTableCellData,
    fmtTimeBitFill,
    getPriceFieldBind,
    getDateByTimestamp,
    getCellEditableFlag({ sku, field, rowData }) {
      return (
        !this.skuFieldDisabledStateObj[sku.sku_id][field] &&
        !this.getComboDisabled(field, sku) &&
        this.getFieldEditableBySkuId({ field, data: rowData[sku.sku_id] })
      )
    },
    getFieldEditableBySkuId({ field, data }) {
      let { price_can_edit = false, quantity_can_edit = false, timeslot_can_edit = false } = data || {}

      if (
        (isOnPriceField(field) && price_can_edit === false) ||
        (field === 'inv_quantity' && quantity_can_edit === false) ||
        (field === 'cut_off_time' && timeslot_can_edit === false)
      ) {
        return false
      }

      return true
    },
    getEditLockByField({ field, sku_id, allowed_presale }) {
      let path

      switch (field) {
        case 'cut_off_time':
          path = 'inventory.update_cut_off_time'
          break
        case 'inv_quantity':
          path = 'inventory.update_inv_quantity'
          break
        case 'cost_price':
          path = 'price.update_cost_price'
          break
        case 'retail_price':
          path = 'price.update_retail_price'
          break
        case 'selling_price':
        case 'special_selling_price':
          path = 'price.update_selling_price'
          break
      }

      if (field === 'take_rate') {
        const selling = this.getEditLockValueByPath({
          sku_id,
          path: 'price.update_selling_price'
        })
        const cost = this.getEditLockValueByPath({
          sku_id,
          path: 'price.update_cost_price'
        })

        if (selling.status && cost.status) {
          return cost
        }
      } else if (field === 'inv_quantity' && allowed_presale) {
        return {
          status: false,
          tips: ''
        }
      } else if (path) {
        return this.getEditLockValueByPath({
          sku_id,
          path
        })
      }

      return {
        status: false,
        tips: ''
      }
    },
    getEditLockValueByPath(args) {
      return getEditLockValueByPath({
        data: this.skuFresherSettingData,
        ...args
      })
    },
    getPackageFresherLastDate(sku_id) {
      if (!sku_id) {
        return ''
      }

      const fresherData = _.get(this, 'skuFresherSettingData', {})
      const lastDate = getPackageFresherLastDate(fresherData, sku_id)
      return lastDate || ''
    },
    getComboDisabled,
    getFieldShow(key, sku) {
      return !(sku.is_combo && key === 'cost_price')
    },
    getNameByTicketStatusOnCostField({ field, ticket_status }) {
      return this.openApprovalDetailPopover && field === 'cost_price' && ticket_status === 'PendingApproval'
    },
    initViewItemSizeList() {
      let { CellHeight, allData, activateDisplayTableIndexByFilter, displayTableIndexByFilter } = this

      let { viewItemSizeList } = allData.reduce(
        (acc, curr) => {
          const singleRowData = {
            uuid: curr.uuid, // row index
            height: CellHeight,
            top: acc.preHeight,
            bottom: acc.preHeight + CellHeight
          }

          if (!activateDisplayTableIndexByFilter || displayTableIndexByFilter.includes(curr.uuid)) {
            acc.viewItemSizeList.push(singleRowData)
            acc.preHeight += CellHeight
          }

          return acc
        },
        {
          preHeight: 0,
          viewItemSizeList: []
        }
      )

      this.$set(this, 'viewItemSizeList', viewItemSizeList)
    },
    updateSspData: _.debounce(async function (options, arr) {
      if (!arr || !arr.length) return
      await this.bindSspList2mixin(arr, options)
    }, 300),
    async getAllRates2mixin(skus) {
      for (let i in skus) {
        await this.getPriceRates2mixin(skus[i].selling_currency)
      }
    },
    hasUpdateRecord() {
      return this.allSku.some((sku) => {
        let currentRecord = this.updateTableCellRecordList[sku.sku_id] || {}

        return Object.keys(currentRecord).some((key) => currentRecord[key].length)
      })
    },
    horizontalScroll: _.throttle(function (e) {
      this.tableScrollFixedFlag = e.target.scrollLeft > 0
    }, 100),
    getEditableCellCache(index, field, sku_id) {
      let currentSku = this.cacheVisibleData[index][sku_id]

      if (isOnPriceField(field)) {
        return currentSku.price[field]
      }

      return currentSku?.[field]
    },
    getTips(sku, field, returnBoolean = true) {
      let content = ''
      // inv_model: 1,// 1: 日历库存; 2: 无限库存; 3: 全局库存; 4: 时间段库存,
      // pricing_model: 1,// 套餐价格模型, -1: 未定义, 0: 固定价格, 1: 日历价格,
      // share_rel_ship.share_rel_ship: 1,// 0 无共享， 1 子sku, 2 主sku,
      if (field === 'inv_quantity' || isOnPriceField(field)) {
        let { pricing_model, inv_model } = sku

        if (inv_model === 3 && pricing_model === 0) {
          content = this.$t('29201')
        } else {
          if (inv_model === 3 && field === 'inv_quantity') {
            content = this.$t('29202')
          }

          if (pricing_model === 0 && isOnPriceField(field)) {
            content = this.$t('29203')
          }
        }
      }

      if (returnBoolean) {
        return Boolean(content)
      }
      return content
    },
    /**
     * 初始化当前 sku 数据的批量表格数据
     * @param {Array} calendar - current sku timeslot
     * @param {Object} skuInfo
     * @returns {object} - Initialize all fields of the current sku
     */
    getInitBulkFormDataBySku(calendar, skuInfo) {
      return this.platformBulkViewColumns.reduce((colAcc, colCurr) => {
        let data = {
          ...colAcc,
          [colCurr.key]: colCurr.key === 'special_selling_price' ? [] : '',
          // cut_off_time
          days: '',
          time: ''
        }

        if (Array.isArray(calendar) && calendar.length) {
          let temp = calendar[0]
          data.price_can_edit = temp.price_can_edit
          data.timeslot_can_edit = temp.timeslot_can_edit
          data.quantity_can_edit = temp.quantity_can_edit
          // Linkage
          data.cost_currency = skuInfo.cost_currency
          data.selling_currency = skuInfo.selling_currency
          // get current sku auto fill data
          data.auto_fill_schedule_item = null
          for (let currentCalendar of calendar) {
            let { auto_fill_schedule_item } = currentCalendar
            if (auto_fill_schedule_item) {
              if (auto_fill_schedule_item.fix_price) {
                let fixedLinkageData = priceLinkageUpdate({
                  ...auto_fill_schedule_item.fix_price,
                  originField: 'cost_price',
                  exchange_rate: this.exchange_rate
                })

                if (fixedLinkageData.updateField) {
                  auto_fill_schedule_item.fix_price.take_rate = fixedLinkageData[fixedLinkageData.updateField]
                } else {
                  auto_fill_schedule_item.fix_price.take_rate = undefined
                }

                if (!auto_fill_schedule_item.fix_price.special_selling_price) {
                  auto_fill_schedule_item.fix_price.special_selling_price = []
                }
              }

              data.auto_fill_schedule_item = auto_fill_schedule_item
              break
            }
          }
        }

        return data
      }, {})
    },
    // 获得所有 sku 的从 cost 到 selling 的利率转换关系
    async getAllExchangeRate(allSku = []) {
      let exchange_rate = []
      for (let sku of allSku) {
        let { cost_currency, selling_currency } = sku
        let res = _.find(exchange_rate, {
          from_currency: cost_currency,
          to_currency: selling_currency
        })

        if (!res) {
          if (cost_currency === selling_currency) {
            exchange_rate.push({
              from_currency: cost_currency,
              to_currency: selling_currency,
              from_value: 1,
              to_value: 1
            })
          } else {
            const rate = await ajax.get(ADMIN_API.act.get_exchange_rate, {
              params: {
                currency_from: cost_currency,
                currency_to_list: selling_currency
              }
            })

            exchange_rate.push(rate[0])
          }
        }
      }

      this.exchange_rate = exchange_rate
    },
    // 从 tbody 获得的 scrollTop，用于 tableFixed 同步滚动
    changeScrollTop(v) {
      this.scrollTop = v
    },
    // 从 tbody 获得的虚拟高度，用于 tableFixed 同步扩高
    changePhantomHeight(v) {
      this.phantomHeight = v
    },
    // 从 tbody 获得的内容位移值，用于 tableFixed 同步
    changeTransformVal(v) {
      this.transformY = v
    },
    forceUpdateViewData() {
      this.changeViewIndex(this.viewIndexPosition)
    },
    // 从 tbody 获得的内容可视索引，用于获得可视数据
    changeViewIndex({ start, end }) {
      this.viewIndexPosition = {
        start,
        end
      }

      let filterAllData = this.allData
      const { activateDisplayTableIndexByFilter, displayTableIndexByFilter, allData } = this

      if (activateDisplayTableIndexByFilter) {
        filterAllData = allData.filter((item) => displayTableIndexByFilter.includes(item.uuid))
      }

      const visibleData = filterAllData.slice(start, end + 1)
      this.$set(this, 'visibleData', visibleData)
      this.cacheVisibleData = this.localCacheAllData.slice(start, end + 1)
    },
    /**
     *
     * @returns {Object} 关联数据对象
     * data: 当前字段值的寄主对象
     * value: 当前字段值
     * skuData: 当前行的 sku 数据
     */
    getCellDataByPosition({ sku_id, field, uuid, source = this.allData } = {}) {
      return getCellDataByPosition({ sku_id, field, uuid, source })
    },
    // 用于滚动到某一行
    scrollToTableRowByIndex(index = 0, cb = null) {
      this.$refs.tbody && this.$refs.tbody.scrollToTableRowByIndex(index, cb)
    },
    // 重置表格的状态信息、记录数据
    resetAllData() {
      // init all data

      this.selectAll = false
      this.customChecked = [] // reset indeterminate
      this.currentEditCell = {}
      this.tableScrollFixedFlag = false

      // this.updateTableCellRecordList = {}
      this.deleteTableRowUUIDList = []
      this.invalidTableCellUUIDList = []

      this.$refs.thead && this.$refs.thead.resetState()
    },
    presaleWarning() {
      return new Promise((resolve) => {
        this.$confirm({
          type: 'warning',
          title: this.$t('205215'),
          content: this.$t('205220'),
          okText: this.$t('global.confirm'),
          cancelText: this.$t('17057'),
          onOk() {
            return resolve(true)
          },
          onCancel() {
            return resolve(false)
          }
        })
      })
    },
    // 提交操作执行顺序：先调用 triggerToSubmit ，待对数据进行整理和判定完成，会 emit 'confirmToSubmit'
    async triggerToSubmit({ passPriceFillReason = false, hasTakeRateFieldData = false } = {}) {
      this.spinning = true
      let recordList = [] // 用于记录当前行的 sku 是否已被收录
      let postData = []
      let invalidFiledList = []
      let hasChangePrice = false
      let mainSkuHasChaneState = false
      let subSkuHasChangeState = false
      let isPresaleChangedToActive = false // 手动从 presale 切到 active 会卡单，需要提醒

      let invalidTableCellUUIDList = []
      this.deleteTableRowUUIDList.forEach((uuid) => {
        // inv_model: 1,// 1: 日历库存; 2: 无限库存; 3: 全局库存; 4: 时间段库存,
        // pricing_model: 1,// 套餐价格模型, -1: 未定义, 0: 固定价格, 1: 日历价格,
        // share_rel_ship.share_rel_ship: 1,// 0 无共享， 1 子sku, 2 主sku,
        let index = this.parseRowIndexByUUID(uuid)
        this.allSku.forEach((sku) => {
          let { sku_id } = sku
          let curr = _.find(postData, {
            sku_id
          })
          let { start_time, start_date } = this.allData[index]
          start_time = start_time + ':00'
          let data = {
            index,
            delete: true,
            start_time: [start_date, start_time].join(' ')
          }

          if (curr) {
            curr.schedules.push(data)
          } else {
            postData.push({
              sku_id: sku.sku_id,
              schedules: [data]
            })
          }

          // this.allSkuModelObject[sku_id].share_rel_ship.share_rel_ship === 2
          // 不赋值的原因是只有当 mainSkuHasChaneState = false 的时候才会执行
          if (!mainSkuHasChaneState && this.allSkuModelObject[sku_id].share_rel_ship.share_rel_ship === 2) {
            mainSkuHasChaneState = true
          }

          if (!subSkuHasChangeState && this.allSkuModelObject[sku_id].share_rel_ship.share_rel_ship !== 2) {
            subSkuHasChangeState = true
          }
        })
      })

      let updateTableCellRecordList = this.allSku.reduce((acc, curr) => {
        let sku_id = curr.sku_id
        let currentRecord = this.updateTableCellRecordList[sku_id]

        Object.keys(currentRecord).forEach((field) => {
          currentRecord[field].forEach((uuid) => {
            acc.push({
              sku_id,
              field,
              uuid
            })
          })
        })

        return acc
      }, [])
      for (let record of updateTableCellRecordList) {
        let { sku_id, uuid, field } = record
        let index = this.parseRowIndexByUUID(uuid)
        let basicData = {
          sku_id,
          index
        }
        let currentRowData = _.cloneDeep(this.allData[index])
        let currentSkuRowData = currentRowData[sku_id]

        // 判断用于 presale 切换的弹窗提示
        if (!isPresaleChangedToActive) {
          const { publish_status: prev_publish_status, sales } =
            this.localCacheAllData?.[index]?.[sku_id] || {}
          const publish_status = currentSkuRowData?.publish_status

          if (sales > 0 && isStatusPresale(prev_publish_status) && isStatusPublished(publish_status)) {
            isPresaleChangedToActive = true
          }
        }

        // 判断用于弹窗二次确认
        if (
          // 判断是否有修改价格的操作
          !hasChangePrice &&
          isOnPriceField(field) &&
          isValidTableCellData(
            // 如果初始值是有效的，且有更新记录，则说明此值是更新而不是新创建或者固定值激活状态
            this.localCacheAllData[index][sku_id].price[field],
            field
          ) &&
          this.allSkuModelObject[sku_id].pricing_model !== 0
        ) {
          hasChangePrice = true
        }

        if (
          // 判断主套餐
          !mainSkuHasChaneState &&
          this.allSkuModelObject[sku_id].share_rel_ship.share_rel_ship === 2 &&
          !isOnPriceField(field)
        ) {
          mainSkuHasChaneState = true
        }

        if (
          // 判断子套餐
          !subSkuHasChangeState &&
          this.allSkuModelObject[sku_id].share_rel_ship.share_rel_ship !== 2 &&
          !isOnPriceField(field)
        ) {
          subSkuHasChangeState = true
        }

        if (
          _.find(recordList, basicData) || // 当前行的 sku 信息已经被收录
          this.deleteTableRowUUIDList.includes(uuid) // 当前行都不需要校验了，已经被删除
        ) {
          // pass
        } else {
          let platformBulkViewColumns = this.platformBulkViewColumns.filter(
            (item) => !['publish_status', 'cut_off_time', 'special_selling_price'].includes(item.key)
          )

          let valueState = platformBulkViewColumns.map((item) => {
            let field = item.key

            // 锁定的不需要校验了
            if (this.skuFieldDisabledStateObj[sku_id][field]) {
              return true
            }

            let value
            if (this.isOnPriceField(field)) {
              value = currentSkuRowData.price[field]
            } else {
              value = currentSkuRowData[field]
            }

            if (field === 'take_rate') {
              // 这个 take tate 仅仅是前端联动计算用
              return true
            }

            return this.isValidTableCellData(value, field)
          })

          if (valueState.every((item) => item) || valueState.every((item) => !item)) {
            recordList.push(basicData) // Record the row of current sku have update

            let schedule = this.getScheduleData(
              currentSkuRowData,
              this.allSkuModelObject[sku_id].pricing_model,
              hasTakeRateFieldData
            )
            let currentPostSku = _.find(postData, {
              sku_id
            })

            if (!currentPostSku) {
              postData.push({
                sku_id,
                schedules: [schedule]
              })
            } else {
              currentPostSku.schedules.push(schedule)
            }
          } else {
            valueState.forEach((flag, idx) => {
              if (!flag) {
                let invalidFiled = platformBulkViewColumns[idx].key

                invalidTableCellUUIDList = _.uniq([
                  ...invalidTableCellUUIDList,
                  {
                    uuid,
                    sku_id,
                    field: invalidFiled
                  }
                ])

                invalidFiledList.push(invalidFiled)
              }
            })
          }
        }
      }

      if (invalidTableCellUUIDList.length) {
        this.invalidTableCellUUIDList = invalidTableCellUUIDList
        this.$refs.tbody.scrollToTableRowByIndex(invalidTableCellUUIDList[0].uuid.slice(1))
        this.$message.error(__('package_error_mandatory'))

        let differenceField = _.difference(
          _.uniq(invalidFiledList),
          this.viewBulkColumnField.map((item) => item.key)
        )
        if (differenceField.length) {
          this.$emit('differenceField', differenceField)
        }
        this.forceUpdateViewData()

        this.spinning = false
      } else {
        // 主 sku 的修改优先级高于 sub sku
        let content = ''
        if (mainSkuHasChaneState) {
          content = this.$t('27817')
        } else if (subSkuHasChangeState) {
          content = this.$t('27818')
        }

        if (content) {
          let confirmRes = await this.pmsConfirm(content)
          if (!confirmRes) {
            this.spinning = false
            return
          }
        }

        if (isPresaleChangedToActive) {
          const presaleCheck = await this.presaleWarning()
          if (!presaleCheck) {
            this.spinning = false
            return
          }
        }

        if (this.pageRole === 'merchant' || !hasChangePrice || passPriceFillReason) {
          this.priceReason.data = postData

          this.spinning = false

          return this.handleConfirmToSubmit()
        } else {
          this.$set(this, 'priceReason', {
            visible: true,
            data: postData
          })
        }

        this.spinning = false
      }
    },
    handleConfirmToSubmit(note = null) {
      const data = {
        sku_list: this.priceReason.data,
        ...(note
          ? {
              note
            }
          : {})
      }

      this.$emit('confirmToSubmit', data)
      return data
    },
    // 唤起 SSP 的编辑
    handleBulkEditSSP(sku) {
      let { selling_price, special_selling_price } = this.bulkForm[sku.sku_id] || {}
      this.$set(this, 'specialSellingPrice', {
        selling_price,
        list: special_selling_price || [],
        visible: true,
        sellingCurrency: sku.selling_currency,
        position: {
          sku_id: sku.sku_id
        },
        isFromBulk: true,
        skuId: sku.sku_id,
        timePoints: this.customChecked.map((uuid) => {
          let index = parseRowIndexByUUID(uuid)
          let { start_date, start_time } = this.allData[index]

          return `${start_date} ${start_time}:00`
        })
      })
    },
    handleSSPDrawerConfirm(data) {
      let { position, isFromBulk } = this.specialSellingPrice
      let { uuid, sku_id } = position

      if (isFromBulk) {
        this.bulkForm[sku_id]['special_selling_price'] = data
        this.sendToWorker({
          field: 'special_selling_price',
          sku_id,
          isFromFixed: false,
          updateData: {
            special_selling_price: data
          },
          shouldUpdateUUIDList: this.customChecked
        })
      } else {
        let index = this.parseRowIndexByUUID(uuid)
        this.allData[index][sku_id].price['special_selling_price'] = _.cloneDeep(data)

        this.setUpdateTableCellRecordList([
          {
            sku_id,
            uuid,
            field: 'special_selling_price'
          }
        ])
      }

      // reset
      this.$set(this, 'specialSellingPrice', this.$options.data().specialSellingPrice)
    },
    emitTableState: _.debounce(function () {
      this.$emit('tableState', {
        updated: this.hasUpdateRecord(),
        deleted: this.deleteTableRowUUIDList.length
      })
    }, 200),
    setUpdateTableCellRecordList(data) {
      data.forEach((item) => {
        let { uuid, sku_id, field } = item
        if (!this.updateTableCellRecordList[sku_id][field]) {
          this.updateTableCellRecordList[sku_id][field] = []
        }
        let current = this.updateTableCellRecordList[sku_id][field]

        this.updateTableCellRecordList[sku_id][field] = [...new Set([...current, uuid])]
      })
      // this.updateTableCellRecordList.push(...data)
      this.emitTableState()
    },
    // 先用 localClearUpdateRecordList 记录要清楚的记录信息，再统一去重
    clearUpdateTableCellRecordList(data) {
      data.forEach((item) => {
        let { uuid, sku_id, field } = item
        let current = this.updateTableCellRecordList[sku_id][field]

        this.updateTableCellRecordList[sku_id][field] = current.filter((curr) => curr !== uuid)
      })

      this.emitTableState()
    },
    /**
     * 将价格数据为空的设置为 0（后端类型强校验）
     * 清除不必要的字段数据、重写计算 cut_off_time 时间戳
     * @param {object} hasTakeRateFieldData - post data 是否带上 take rate 字段数据
     * @returns {object} - schedule data, including formatted cut off time and price value
     */
    getScheduleData(currentSkuRowData, pricing_model, hasTakeRateFieldData = false) {
      const isFixedPrice = pricing_model == 0
      const isTour = this.isTour
      const keyList = Object.keys(currentSkuRowData)
      return keyList.reduce((acc, curr) => {
        if (curr === 'price') {
          const priceData = Object.keys(currentSkuRowData[curr]).reduce((accPrice, currPriceKey) => {
            if (currPriceKey.includes('take_rate')) {
              return hasTakeRateFieldData
                ? {
                    ...accPrice,
                    [currPriceKey]: String(currentSkuRowData[curr][currPriceKey])
                  }
                : accPrice
            }

            if (currentSkuRowData[curr][currPriceKey] === '' && this.isOnPriceField(currPriceKey)) {
              return {
                ...accPrice,
                [currPriceKey]: 0 // back-end number validator
              }
            }

            return {
              ...accPrice,
              [currPriceKey]: currentSkuRowData[curr][currPriceKey]
            }
          }, {})
          if (!isFixedPrice && isTour) {
            priceData.retail_price = priceData.selling_price
          }
          return {
            ...acc,
            [curr]: priceData
          }
        }

        if (curr === 'days') {
          return {
            ...acc,
            cut_off_time: getTimestampByDate({
              days: currentSkuRowData.days,
              time: currentSkuRowData.time
            })
          }
        }

        // presale 使用的是 publish_status 字段，此时需要将 published 字段置为 false
        if (curr === 'published' && keyList.includes('publish_status')) {
          return {
            ...acc,
            [curr]: false
          }
        }

        return ['isChange', 'days', 'time'].includes(curr)
          ? acc
          : {
              ...acc,
              [curr]: currentSkuRowData[curr]
            }
      }, {})
    },
    clearEditTableCell() {
      this.currentEditCell = {}
    },
    // 批量处理里清除固定值触发
    clearBulkFixedValue({ fieldList, sku_id, field }) {
      this.changeBulkEditForm({
        sku_id,
        field,
        isFromFixed: true,
        data: fieldList.reduce(
          (acc, curr) => ({
            ...acc,
            [curr]: null
          }),
          {}
        ),
        shouldUpdateUUIDList: this.customChecked
      })
    },
    clearFixedValue({ field, sku_id, uuid }) {
      let fields = this.isOnPriceField(field) ? priceFieldList : ['inv_quantity']

      fields.forEach((key) => {
        this.blurTableCellEdit({
          position: { field: key, sku_id, uuid },
          shouldReset: true
        })
      })

      this.clearUpdateTableCellRecordList(
        fields.map((key) => ({
          field: key,
          uuid,
          sku_id
        }))
      )
    },
    // For reset data when blur
    blurTableCellEdit({ position, shouldReset }) {
      if (shouldReset) {
        let { field } = position

        let { data } = this.getCellDataByPosition(position)
        let cacheValue = this.getCellDataByPosition({
          ...position,
          source: this.localCacheAllData
        }).value

        data[field] = _.cloneDeep(cacheValue)
      }
    },
    changeBulkEditForm({ sku_id, field, data, isFromFixed = false } = {}) {
      let allCheckedUUID = this.customChecked
      // let currentViewRowUUID = []
      // currentViewRowUUID = this.customChecked.filter((uuid) => {
      //   let index = this.parseRowIndexByUUID(uuid)
      //   let { start, end } = this.$refs.tbody
      //
      //   return index >= start && index <= end
      // })

      // Object.entries(data).forEach((item) => {
      //   let [updateField, value] = item
      //
      //   this.bulkForm[sku_id][updateField] = value // update thead form
      //
      //   currentViewRowUUID.forEach((item) =>
      //     this.updateTableCell({
      //       sku_id,
      //       value,
      //       field: updateField,
      //       uuid: item,
      //       isFromFixed,
      //       isFromBulkEdit: true
      //     })
      //   )
      // })

      this.sendToWorker({
        field,
        sku_id,
        isFromFixed,
        updateData: data,
        // shouldUpdateUUIDList: _.xor(allCheckedUUID, currentViewRowUUID)
        shouldUpdateUUIDList: allCheckedUUID
      })
    },
    // 单个表格的数据更新
    updateTableCell({ sku_id, uuid, value, field, isFromFixed = false, isFromBulkEdit = false } = {}) {
      if (!(field && sku_id)) {
        return
      }

      let index = this.parseRowIndexByUUID(uuid)
      let currentSkuData = this.allData[index][sku_id]
      if (field === 'selling_price') {
        let sspList = _.cloneDeep(_.get(currentSkuData, 'price.special_selling_price', []))
        _.set(currentSkuData, 'price.special_selling_price', sspList)
        sspList.length &&
          this.updateSspData(
            {
              price: value,
              sellingCurrency: _.get(currentSkuData, 'price.selling_currency', '')
            },
            sspList
          )
      }
      if (!currentSkuData.is_self_point) {
        return
      }

      let position = {
        uuid,
        sku_id,
        field
      }

      if (!isFromFixed) {
        let { isValid } = getFixedValueByRecord({
          currentSkuData,
          field
        })

        if (this.skuFieldDisabledStateObj[sku_id][field] && !isValid) {
          return
        }
      }

      let currentCellData = this.getCellDataByPosition(position)
      let cacheCellData = this.getCellDataByPosition({
        ...position,
        source: this.localCacheAllData
      })

      if (value !== '' && isOnPriceField(field)) {
        // value === null, a-input-number default value
        if (field !== 'special_selling_price') {
          value = value === null ? null : +(+value).toFixed(this.toFixedConstant)
        }
      }

      let updateVal = value
      // reset cache data
      if (isFromBulkEdit) {
        if (updateVal === null || updateVal === '') {
          updateVal = _.cloneDeep(cacheCellData.value) // ssp<Array>
        }
      } else if (updateVal === null) {
        updateVal = ''
      }

      currentCellData.data[field] = updateVal
      // 如果值跟缓存的不一致，发生了更新，则需要同步更改 updateTableCellRecordList 和 invalidTableCellUUIDList
      let isEqual = _.isEqual(updateVal, cacheCellData.value)
      if (!isEqual) {
        this.setUpdateTableCellRecordList([position])
      } else {
        this.clearUpdateTableCellRecordList([position])
      }

      // 固定价格是统一赋值，不需要再联动了
      if (this.isOnPriceField(field) && !isFromFixed) {
        this.priceUpdateLinkage(currentCellData, cacheCellData, position, field)
      }
    },
    // 价格联动刚更新
    priceUpdateLinkage(currentCellData, cacheCellData, position, originalField) {
      let { cost_price, selling_price, take_rate, cost_currency, selling_currency } = currentCellData.data
      if (['cost_price', 'selling_price', 'take_rate'].includes(originalField)) {
        let { updateField, ...newData } = priceLinkageUpdate({
          cost_price,
          selling_price,
          take_rate,
          originField: originalField,
          cost_currency,
          selling_currency,
          exchange_rate: this.exchange_rate || []
        })

        let newValue = newData[updateField]
        if (updateField) {
          let currentPosition = {
            ...position,
            field: updateField
          }

          currentCellData.data[updateField] = newValue
          this.forceUpdateViewData()
          if (!_.isEqual(newValue, cacheCellData.data[updateField])) {
            this.setUpdateTableCellRecordList([currentPosition])
          } else {
            this.clearUpdateTableCellRecordList([currentPosition])
          }
        }
      }
    },
    // 点击编辑表格
    handelEditCell(event) {
      let path = [...event.composedPath()]
      let deepLen = 10

      for (let i = 1; i < deepLen; i++) {
        // Event delegation
        let curr = path[i]
        if (curr.classList.contains('table-row-cell')) {
          i = deepLen

          if (curr.classList.contains('cell-is-disabled') && !curr.classList.contains('cell-is-auto-fill')) {
            return
          }

          let uuid = curr.getAttribute('data-uuid')
          let sku_id = +curr.getAttribute('data-sku_id')
          let field = curr.getAttribute('data-field')
          let is_combo = curr.getAttribute('data-is_combo') === 'true' ? true : false
          if (!(uuid && sku_id && field)) {
            return
          }

          let index = parseRowIndexByUUID(uuid)
          let currentRowData = this.allData[index]
          let currentSkuData = currentRowData[sku_id]
          if (
            this.setFixedValue(currentSkuData, {
              field,
              uuid,
              sku_id
            })
          ) {
            return
          }

          let position = {
            uuid,
            sku_id,
            field
          }
          let className = this.getTableCellClassName(position)
          if (className.includes('cell-is-disabled') || className.includes('cell-is-del-2row')) {
            return
          }

          this.currentEditCell = position

          if (field === 'special_selling_price' && !is_combo) {
            let { selling_price, selling_currency, special_selling_price } = currentSkuData.price

            let { start_date, start_time } = currentRowData

            this.$set(this, 'specialSellingPrice', {
              selling_price,
              list: _.cloneDeep(special_selling_price),
              visible: true,
              sellingCurrency: selling_currency,
              position,
              isFromBulk: false,
              skuId: sku_id,
              timePoints: [`${start_date} ${start_time}:00`]
            })
          }
        } else {
          this.currentEditCell = {}
        }
      }
    },
    setFixedValue(currentSkuData, { field, sku_id, uuid }) {
      let data = setFixedValueByRecord({
        currentSkuData,
        field
      })

      if (data) {
        let keys = Object.keys(data)
        if (isOnPriceField(field)) {
          Object.assign(currentSkuData.price, data)
        } else {
          Object.assign(currentSkuData, data)
        }

        let positionList = keys.map((key) => ({
          field: key,
          sku_id,
          uuid
        }))
        this.setUpdateTableCellRecordList(positionList)

        return true
      }

      return false
    },
    /**
     * 判断当前表格是否值与缓存值不同
     * @returns {Boolean}
     */
    getTableCellChange({ uuid, sku_id, field, isDelete = false } = {}) {
      if (isDelete) {
        return false
      }

      let index = uuid.slice(1)
      let path = `${index}.${sku_id}.${field}`
      let path2 = ''

      if (isOnPriceField(field)) {
        path = `${index}.${sku_id}.price.${field}`
      }

      if (field === 'cut_off_time') {
        path = `${index}.${sku_id}.days`
        path2 = `${index}.${sku_id}.time`
      }

      let current = _.get(this.allData, path, null)
      let cache = _.get(this.localCacheAllData, path, false)

      return (
        !_.isEqual(current, cache) ||
        (path2 && !_.isEqual(_.get(this.allData, path2, null), _.get(this.localCacheAllData, path2, false)))
      )
    },
    getTableCellClassName({ uuid, sku_id, field } = {}) {
      let index = this.parseRowIndexByUUID(uuid)
      let record = this.allData[index]
      let className = []

      let disabled = !this.getCellEditableFlag({
        sku: this.allSku.find((sku) => sku.sku_id === sku_id),
        field,
        rowData: record
      })
      if (disabled) {
        className.push('cell-is-disabled')
      }

      // let isUpdated = this.getTableCellChange({ uuid, sku_id, field })
      let isUpdated
      let currentSkuUpdateState = this.updateTableCellRecordList[sku_id]
      if (field === 'cut_off_time') {
        isUpdated =
          currentSkuUpdateState['days'].includes(uuid) || currentSkuUpdateState['time'].includes(uuid)
      } else {
        isUpdated = currentSkuUpdateState[field].includes(uuid)
      }
      if (isUpdated) {
        className.push('cell-is-change')
      }

      if (record.isDelete) {
        className.push('cell-is-del-2row')
      }

      if (record[sku_id]?.isChildSku && !isOnPriceField(field)) {
        className.push('cell-is-child-sku')
      }

      let invalid = _.find(this.invalidTableCellUUIDList, {
        uuid,
        field,
        sku_id
      })
      if (invalid) {
        className.push('cell-is-invalid')
      }

      // inv_model: 1,// 1: 日历库存; 2: 无限库存; 3: 全局库存; 4: 时间段库存,
      // pricing_model: 1,// 套餐价格模型, -1: 未定义, 0: 固定价格, 1: 日历价格,
      // share_rel_ship.share_rel_ship: 1,// 0 无共享， 1 子sku, 2 主sku,
      let { inv_model, pricing_model } = this.allSkuModelObject[sku_id]
      if (field === 'inv_quantity') {
        className.push('cell-flex-end')
        if (inv_model === 3) {
          className.push('cell-is-fixed')
        }
      }

      if (isOnPriceField(field)) {
        className.push('cell-flex-end')
        if (pricing_model === 0) {
          className.push('cell-is-fixed')
        }
      }

      return _.uniq(className)
    },
    // @param {object} - select state
    changeCustomChecked({ checked, value }) {
      if (checked) {
        this.customChecked.push(value)
      } else {
        this.customChecked = this.customChecked.filter((uuid) => uuid !== value)
      }
    },
    confirmSelectAll() {
      this.$set(this, 'bulkForm', _.cloneDeep(this.cacheBulkForm || {}))

      if (!this.selectAll) {
        this.customChecked = []
      } else if (this.activateDisplayTableIndexByFilter) {
        this.customChecked = [...this.displayTableIndexByFilter]
      } else {
        this.customChecked = this.allData.map((item) => item.uuid)
      }
      this.$tracker.track('action', '#select_all_spm')
    },
    deleteSelectedRow({ uuid }) {
      let uuidList = uuid ? [uuid] : this.customChecked

      this.deleteTableRowUUIDList = [...new Set([...this.deleteTableRowUUIDList, ...uuidList])]

      uuidList.forEach((item) => {
        let index = this.parseRowIndexByUUID(item)

        this.allData[index].isDelete = true
      })

      this.customChecked = this.customChecked.filter((item) => item !== uuid)
    },
    undoDeleteRow({ uuid }) {
      let uuidList = uuid ? [uuid] : this.deleteTableRowUUIDList

      this.deleteTableRowUUIDList = _.difference(this.deleteTableRowUUIDList, uuidList)

      uuidList.forEach((item) => {
        let index = this.parseRowIndexByUUID(item)

        this.allData[index].isDelete = false
      })
    },
    pmsConfirm(content) {
      return new Promise((resolve, reject) => {
        this.$confirm({
          content,
          okText: __('global_confirm'),
          cancelText: __('global_cancel'),
          onOk() {
            return resolve(true)
          },
          onCancel() {
            return resolve(false)
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.bulkTable-container {
  margin-bottom: 72px;
  .table-container {
    width: 100%;
    overflow: auto;
    .table {
      display: table;
      width: 100%;
      border: 1px solid #e5e4e5;
      border-right: 1px solid #e5e4e5;
    }
  }
}

.scroll-table {
  border: 1px solid #e5e4e5;
  border-right: none;
  border-top: none;
}

::v-deep .scroll-table-container {
  .is-published {
    color: #36b37e;
  }

  .is-unpublished {
    color: #ffab00;
  }

  .is-presale {
    color: #437dff;
  }
}

.bulk-edit__input,
::v-deep .ant-input-number {
  width: 100px;
}
</style>

<style lang="scss">
.bulkTable-container {
  .table-row {
    display: flex;
    flex-wrap: nowrap;
  }
  .table-sku {
    display: inherit;
  }
  .table-row-cell,
  .thead-row-cell {
    width: 150px;
    min-width: 150px;
    flex-grow: 1;
    padding: 12px 8px;
    cursor: pointer;
    font-size: 12px;
    border-right: 1px solid #e5e4e5;
    border-bottom: 1px solid #e5e4e5;
    transition: border-color 0.3s;
  }
  .table-row-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    width: 100%;
    height: 100%;
  }
  .thead-row-actions {
    background-color: #fff;
    .thead-row-cell {
      height: 80px;
      &.thead-row-field {
        position: relative;
        display: flex;
        flex: 1;
        flex-wrap: wrap;
        align-items: center;
      }
    }
  }
  .thead-row-cell {
    min-height: 45px;
    text-align: center;
    &.cell-fixed-width {
      border: none;
    }
  }
  .cell-fixed-width {
    min-width: 150px;
    max-width: 150px;
  }
  .scroll-table-container {
    min-height: fit-content;
    border-top: none;
    overflow: auto;
    position: relative;
    -webkit-overflow-scrolling: touch;
  }
  .scroll-table-phantom {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    z-index: -1;
  }
  .scroll-table {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    overflow-x: hidden;
    will-change: transform;
    z-index: 1;
  }
}
</style>

<style lang="scss">
.bulkTable-container {
  .table-row {
    transition: background-color 0.3s ease;
  }
  .row-is-checked {
    background-color: #f2f9ff;
  }
  .row-is-calm:hover {
    background-color: rgba(0, 145, 255, 0.05);
  }
  .row-is-checked {
    background-color: rgba(0, 145, 255, 0.05);
  }
  .row-is-delete {
    background-color: #fff0ec;
  }
  .cell-is-change {
    border: 1px solid #36b37e;
    background-color: rgba(54, 179, 126, 0.05);
  }
  .cell-is-invalid {
    border: 1px solid #ff5630;
  }
  div.cell-is-disabled {
    background-color: #f0f0f0;
    cursor: not-allowed;
  }
  div.cell-is-fixed {
    background-color: rgba(255, 171, 0, 0.1);
  }
  div.cell-is-child-sku {
    background-color: #f6f6f6;
  }
  div.cell-is-del-2row {
    background-color: #fff0ec;
  }
  .tips {
    width: 100%;
    padding: 8px;
    margin: 20px 0;
    color: #ffab00;
    background-color: rgba(255, 171, 0, 0.05);
  }
  .table-scroll-fixed {
    box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.15);
  }
  .scroll-table-container {
    width: 100%;
    &.exist-scroll-bar {
      width: calc(100% + 4px);
    }
  }
}
</style>
