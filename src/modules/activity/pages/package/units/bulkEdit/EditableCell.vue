<template>
  <div
    class="editableCell-container"
    :class="{
      disabled: getDisabled(field),
      'cell-is-auto-fill': getEditIconFlag()
    }"
    v-tooltip="{
      visible: currentFiledLock.status,
      content: currentFiledLock.tips,
      placement: 'bottom',
      clearOtherTooltips: true
    }"
  >
    <!-- disabled or can not edit -->
    <!--<component-->
    <!--v-if="!editable || showTextFlag"-->
    <!--:is="hasTips ? 'a-tooltip' : 'div'"-->
    <!--class="table-cell-content"-->
    <!--:class="{-->
    <!--'cell-flex-end': !['published', 'cut_off_time'].includes(field)-->
    <!--}"-->
    <!--:mouseEnterDelay="0.6"-->
    <!--&gt;-->
    <div
      v-if="!currentEditable || showTextFlag"
      class="table-cell-content"
      :class="{
        'cell-flex-end': flexEndClassNameFlag,
        'cell-show-pen': getEditIconFlag()
      }"
    >
      <!--<template slot="title">-->
      <!--{{ tips }}-->
      <!--</template>-->

      <!-- priority: fixed value > xxx_can_edit field -->
      <template v-if="fixed2Disabled">
        <span v-if="field !== 'special_selling_price'" v-html="tableCellInputData"></span>
        <SSPCellShow v-else :value="tableCellInputData" />
        <a-tooltip placement="top" :title="$t('29194')">
          <i class="gg-undo" @click.stop.prevent="handleClearFixedValue"></i>
        </a-tooltip>
        <a-tooltip v-if="costInputSuffixData.visible" placement="top" :title="costInputSuffixData.title">
          <span class="cost-input-suffix --left" :class="costInputSuffixData.className"></span>
        </a-tooltip>
      </template>

      <!-- editable but is empty -->
      <template v-else-if="getEditIconFlag()">
        <span class="table-cell--text">
          <i class="gg-pen"></i>
        </span>
      </template>

      <!-- show text -->
      <span v-else class="table-cell--text dd">
        <template v-if="field === 'publish_status'">
          <i v-if="record[sku_id].visible" class="gg-eye eye-on"></i>
          <span
            v-else
            v-tooltip="{
              visible: record[sku_id].visible_tips,
              placement: 'topLeft',
              content: record[sku_id].visible_tips,
              theme: 'dark'
            }"
            class="gg-eye-off"
          >
            <span class="eye-off">/</span>
            <i class="gg-eye"></i>
          </span>

          <span
            :class="{
              'is-unpublished': isStatusDeactive(record[sku_id].publish_status),
              'is-published': isStatusPublished(record[sku_id].publish_status),
              'is-presale': isStatusPresale(record[sku_id].publish_status)
            }"
          >
            {{ tableCellInputData }}
          </span>
        </template>

        <template v-else-if="field === 'special_selling_price'">
          <SSPCellShow :value="record[sku_id].price[field]"></SSPCellShow>
        </template>

        <template v-else>
          {{ tableCellInputData }}
        </template>

        <a-tooltip v-if="costInputSuffixData.visible" placement="top" :title="costInputSuffixData.title">
          <span class="cost-input-suffix --left" :class="costInputSuffixData.className"></span>
        </a-tooltip>
      </span>
    </div>

    <!-- edit state -->
    <div v-else class="table-cell--actions">
      <a-select
        v-if="field === 'publish_status'"
        class="bulk-edit__input"
        size="small"
        style="width: 100%"
        :options="filteredPublishStatusOpts"
        :default-open="true"
        :value="Number(record[sku_id].publish_status)"
        @change="
          (value) =>
            updateTableCell({
              value: value
            })
        "
      >
      </a-select>

      <template v-else-if="field === 'cut_off_time'">
        <a-select
          :value="record[sku_id].days"
          class="bulk-edit__input"
          size="small"
          :options="cutOffTimeOpts"
          @change="
            (value) =>
              updateTableCell({
                field: 'days',
                value: value
              })
          "
        ></a-select>
        <a-time-picker
          class="bulk-edit__input"
          format="HH:mm"
          valueFormat="HH:mm"
          size="small"
          :open.sync="openTimePicker"
          :style="{
            marginLeft: '4px'
          }"
          :allow-clear="false"
          :value="record[sku_id].time"
          @change="
            (value) =>
              updateTableCell({
                field: 'time',
                value: value
              })
          "
        >
          <a-button slot="addon" size="small" type="primary" @click="openTimePicker = false">
            {{ $t('global_button_ok') }}
          </a-button>
        </a-time-picker>
      </template>

      <template v-else-if="field === 'special_selling_price'"></template>

      <template v-else>
        <a-input-number
          size="small"
          auto-focus
          defaultValue=""
          v-bind="getPriceFieldBind(field)"
          :allow-clear="true"
          :value="currentValue"
          :disabled="getDisabled(field)"
          @blur="blurTableCellEdit"
          @change="
            (value) =>
              updateTableCell({
                value: value
              })
          "
        >
        </a-input-number>
      </template>
    </div>
  </div>
</template>

<script>
import {
  publishStatusOpts,
  isStatusPublished,
  isStatusPresale,
  isStatusDeactive,
  cutOffTimeOpts,
  isOnPriceField,
  getPriceFieldBind,
  getTableCellText,
  isEmptyValue,
  getFixedValueByRecord,
  isValidTableCellData
} from './utils'
import maps from '@activity/utils/maps.js'
import SSPCellShow from './SSPCellShow'
import { mapState } from 'vuex'
import { formatPriceByComma } from '@activity/pages/package/units/utils'

export default {
  name: 'EditableCell',
  components: {
    SSPCellShow
  },
  props: {
    scrollEventCalculating: {
      type: Boolean,
      default: false
    },
    record: {
      // current row
      type: Object,
      required: true
    },
    sku_id: {
      type: Number,
      required: true
    },
    field: {
      type: String,
      required: true
    },
    currentEditCell: {
      type: [Object, null],
      required: true
    },
    onlyShowText: {
      type: Boolean,
      default: false
    },
    isInvalidCell: {
      type: Boolean,
      default: false
    },
    isChange: {
      type: Boolean,
      default: false
    },
    hasTips: {
      type: Boolean,
      default: false
    },
    editable: {
      type: Boolean,
      default: true
    },
    tips: {
      type: String,
      default: ''
    },
    cacheData: {
      required: true
    },
    invModel: {
      // 1: 日历库存; 2: 无限库存; 3: 全局库存; 4: 时间段库存,
      type: Number,
      default: 1
    },
    isDelete: {
      type: Boolean,
      default: false
    },
    isPresale: {
      type: Boolean,
      default: false
    },
    showCostInputSuffixData: {
      type: Boolean,
      default: true
    },
    lockEmptyDataFlag: {
      type: Boolean,
      default: false
    },
    fieldLock: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      publishStatusOpts,
      cutOffTimeOpts,

      fixedVal: null,
      fixIsValid: false,

      currentValue: null,

      openTimePicker: false
    }
  },
  watch: {
    record: {
      immediate: true,
      // 不加 deep 是因为仅仅需要在初始化 record 的时候触发，后续不需要跟进
      handler(newV, oldV) {
        if (newV && oldV === undefined) {
          this.initFixedValue()
        }
      }
    },
    currentData: {
      immediate: true,
      deep: true,
      handler() {
        this.currentValue = this.currentData?.[this.field] ?? null
      }
    }
  },
  computed: {
    ...mapState(['isMerchant']),
    lockAddNewOnEmpty() {
      if (!this.lockEmptyDataFlag) {
        return false
      }

      let { sku_id, record } = this

      return record[sku_id].is_empty
    },
    currentEditable() {
      return this.editable && !this.lockAddNewOnEmpty && !this.currentFiledLock.status
    },
    currentFiledLock() {
      if (this.isPresale && isStatusPresale(this.currentData.publish_status)) {
        return {
          status: false,
          tips: ''
        }
      }

      return this.fieldLock
    },
    flexEndClassNameFlag() {
      return !['publish_status', 'cut_off_time'].includes(this.field)
    },
    filteredPublishStatusOpts() {
      return publishStatusOpts.filter((item) => this.isPresale || item.value !== 2)
    },
    fixed2Disabled() {
      if (this.currentOnlyShowText) {
        return false
      }

      return (
        isValidTableCellData(this.fixedVal, this.field) &&
        isValidTableCellData(this.currentValue, this.field) &&
        _.isEqual(this.fixedVal, this.currentValue) &&
        !_.isEqual(this.fixedVal, this.cacheData) // 等于 cache 的数据不需要 undo
      )
    },
    currentData() {
      let { field, sku_id, record } = this

      return isOnPriceField(field) ? record[sku_id].price : record[sku_id]
    },
    position() {
      return {
        field: this.field,
        sku_id: this.sku_id,
        uuid: this.record.uuid
      }
    },
    showTextFlag() {
      return !(this.currentEditCell && _.isEqual(this.currentEditCell, this.position))
    },
    tableCellInputData() {
      return getTableCellText({
        field: this.field,
        data: this.currentData,
        value: this.currentValue,
        invModel: this.invModel
      })
    },
    costInputSuffixData() {
      const { record, sku_id, field } = this
      const unitData = record[sku_id]
      const { price } = unitData
      const currentStatus = _.find(maps.ticket_status_dot, { value: unitData.ticket_status })

      if (
        this.showCostInputSuffixData &&
        this.isMerchant &&
        -1 !== field.indexOf('cost_price') &&
        this.tableCellInputData !== '' &&
        price &&
        !isNaN(price.current_cost_price) &&
        price.current_cost_price !== -1 && // -1: 不存在草稿价格, -2: 不存在产线价格, 其他值则: 存在草稿和产线价格
        currentStatus
      ) {
        const { current_cost_price } = price
        const className = currentStatus.class + ' ticket-status-circle-suffix'

        return {
          visible: true,
          title: `${__('48089')}: ${
            current_cost_price === -2 ? '-' : formatPriceByComma(current_cost_price)
          }`,
          className
        }
      }

      return {
        visible: false
      }
    },
    currentOnlyShowText() {
      return this.isDelete || this.lockAddNewOnEmpty || this.currentFiledLock.status || this.onlyShowText
    }
  },
  methods: {
    isStatusPublished,
    isStatusPresale,
    isStatusDeactive,
    getDisabled(field = '') {
      const disabled = ['inv_quantity'].includes(field)
      const inv_model = this.invModel === 4
      return disabled && inv_model
    },
    // for a-input-number
    getPriceFieldBind,
    getTableCellText,
    initFixedValue() {
      // Get fixed data and status
      let { fixedVal, isValid } = getFixedValueByRecord({
        field: this.field,
        currentSkuData: this.record[this.sku_id]
      })

      this.fixedVal = fixedVal
      this.fixIsValid = isValid
    },
    handleClearFixedValue() {
      this.$emit('clearFixedValue', this.position)
    },
    updateTableCell({ type, value, field = this.field } = {}) {
      if (value !== '') {
        if (type === 'boolean') {
          // Format data for number, because the back-end data is boolean,
          // but the ant-select only support string or number
          value = Boolean(value)
        }
      }

      this.$emit('updateTableCell', {
        ...this.position,
        field,
        type,
        value
      })
    },
    // Editable and value is empty
    getEditIconFlag() {
      if (this.currentOnlyShowText) {
        return false
      }

      // fixIsValid 优先级会高于 editable ( 即 can_edit 字段)
      if (this.fixIsValid) {
        return true
      }

      if (!this.currentEditable) {
        return false
      }

      let { price_can_edit, quantity_can_edit, timeslot_can_edit } = this.record[this.sku_id] || {}
      let { field } = this

      if (isOnPriceField(field) && !(price_can_edit === undefined || price_can_edit)) {
        return false
      }
      if (field === 'inv_quantity' && !quantity_can_edit) {
        return false
      } else if (field === 'cut_off_time' && !timeslot_can_edit) {
        return false
      }

      return isEmptyValue({
        value: this.currentValue,
        field: this.field
      })
    },
    // For reset data when blur
    blurTableCellEdit() {
      let shouldReset = false
      if (this.currentValue === null) {
        shouldReset = true
      }

      this.$emit('clearEditTableCell')
      this.$emit('blurTableCell', {
        position: this.position,
        shouldReset
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../unit-list/calendar-setting/mixins.scss';

.editableCell-container {
  font-size: 12px;
  cursor: pointer;

  &.disabled {
    background: #f5f5f5;
    cursor: not-allowed;
  }

  .table-cell-content {
    position: relative;
    width: 100%;
    /*height: 100%;*/
    /*display: inline-flex;*/
    /*align-items: center;*/
    /*justify-content: center;*/
    &.cell-flex-end {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
    &.cell-show-pen {
      cursor: pointer;
    }
    ::v-deep .anticon-undo {
      margin-left: 4px;
    }
  }
  .cell-flex-end .table-cell--text {
    justify-content: flex-end;
  }
  .table-cell--text {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 18px;
  }
  .table-cell--actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  ::v-deep {
    .ant-input-number {
      width: 100%;
    }
    .ant-time-picker {
      width: 100px;
    }
  }

  @include ticket-status-suffix;
  .ticket-status-circle-suffix:after {
    left: 12px;
  }
}
</style>
