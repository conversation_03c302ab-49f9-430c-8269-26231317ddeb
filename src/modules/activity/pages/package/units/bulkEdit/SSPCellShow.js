import SspBlockList from '@activity/pages/components/SspBlockList.vue'
export default {
  name: 'SSPCellShow',
  functional: true,
  props: {
    value: {
      type: Array,
      required: true
    }
  },
  render: (h, ctx) => {
    let { value } = ctx.props
    value = Array.isArray(value) ? value : []
    let getNodes = (arr = [], obj = {}) => {
      let { hideIcon, showIconInfo } = obj
      return (
        <span class="ssp-cell-box" style="display: inline;">
          <SspBlockList
            styleType="style-cell"
            hideIcon={hideIcon}
            showIconInfo={showIconInfo}
            showBtnSsp={false}
            sspList={arr}
          />
        </span>
      )
    }

    if (value.length <= 2) {
      return getNodes(value)
    }
    return (
      <a-tooltip
        title={getNodes(value, { showIconInfo: false })}
        overlayClassName="common-tooltip-style width-auto"
      >
        {getNodes([value[0]], { hideIcon: true })}
        ...
      </a-tooltip>
    )
  }
}
