<template>
  <div class="searchForm-container">
    <a-form-model
      ref="form"
      :model="form"
      label-width="100px"
      layout="horizontal"
      label-align="left"
      :labelCol="{ span: 4 }"
      :wrapperCol="{ span: 20 }"
    >
      <a-form-model-item prop="sku_ids" :label="$t('SKU_ID')" required>
        <a-tree-select
          v-model="form.sku_ids"
          tree-checkable
          search-placeholder="Please select"
          :tree-data="treeData"
        />
      </a-form-model-item>

      <a-form-model-item prop="date" :label="$t('package_xinfo_date')" required>
        <a-range-picker
          v-model="form.date"
          ref="picker"
          dropdownClassName="bulk-edit-search-form-range"
          :disabled-date="disabledDate"
          :defaultValue="[moment().add(1, 'days'), moment().endOf('month')]"
          @calendarChange="calendarChange"
          @openChange="openChange"
        >
          <div slot="renderExtraFooter" class="range-picker-footer">
            <span class="has-filled-dot"></span>{{ $t('30286') }}
          </div>
          <template slot="dateRender" slot-scope="current">
            <div
              class="search-form-picker-cell"
              :class="{
                'has-inv': hasInvStyle(current)
              }"
            >
              {{ current.date() }}
            </div>
          </template>
        </a-range-picker>
      </a-form-model-item>

      <div v-show="viewMore">
        <a-form-model-item prop="time_points" :label="$t('package_timeslot')">
          <all-selector
            v-model="form.time_points"
            :options="timeslotOpts"
            :show-bulk-opt="false"
          ></all-selector>
        </a-form-model-item>

        <a-form-model-item prop="weekly" :label="$t('package_timeslot_by_weekly')">
          <all-checkbox v-model="form.weekly" :options="weeklyOpts"></all-checkbox>
        </a-form-model-item>

        <a-form-model-item prop="time_slot_status" :label="$t('27809')">
          <all-checkbox
            v-model="form.time_slot_status"
            GTMKey="Price&Inventory|ListScheduleStatusFilter_Click"
            :options="statusOpts"
          ></all-checkbox>
        </a-form-model-item>

        <a-form-model-item prop="empty" :label="$t('27810')">
          <all-checkbox
            v-model="form.empty"
            GTMKey="Price&Inventory|ListScheduleIfEmptyFilter_Click"
            :options="scheduleOpts"
          ></all-checkbox>
        </a-form-model-item>
      </div>

      <div class="view-more" @click="handleViewMore">
        <span style="cursor: pointer;">
          {{ viewMore ? $t('18719') : $t('18718') }}
          <svg-icon
            class="__icon"
            :class="{
              '--fullscreen': viewMore
            }"
            icon-name="view_more"
          />
        </span>
      </div>
    </a-form-model>

    <footer>
      <a-button type="primary" @click="handleEmitSearch">
        {{ $t('global_button_search') }}
      </a-button>

      <a-button
        type="link"
        v-if="showResetFlag"
        :style="{
          marginLeft: '12px'
        }"
        @click="handleReset"
      >
        {{ $t('global_reset') }}
      </a-button>
    </footer>
    <!-- 埋点 -->
    <div id="sku_ids_spm" :data-spm-module="getSkuIdsSpm" data-spm-virtual-item="__virtual"></div>
    <div id="weekly_spm" :data-spm-module="getWeeklySpm" data-spm-virtual-item="__virtual"></div>
    <div id="schedule_spm" :data-spm-module="getScheduleSpm" data-spm-virtual-item="__virtual"></div>
    <div id="if_empty_spm" :data-spm-module="getIfEmptySpm" data-spm-virtual-item="__virtual"></div>
  </div>
</template>

<script>
import moment from 'moment'
import AllSelector from './AllSelector'
import AllCheckbox from './AllCheckbox'
import { weeklyOpts, statusOpts, scheduleOpts } from './utils'

export default {
  name: 'SearchForm',
  inject: ['activity_id'],
  components: {
    AllCheckbox,
    AllSelector
  },
  props: {
    skuData: {
      type: Array,
      required: true
    }
  },
  data() {
    this.MAXMUM_DAYS = 365

    return {
      form: {
        sku_ids: [],
        date: [],
        time_points: ['selectAll'],
        weekly: [0, 1, 2, 3, 4, 5, 6],
        time_slot_status: [1, 2],
        empty: [1, 2]
      },
      cacheForm: null,

      treeData: [],
      timeslotOpts: [],

      startDate: null,

      weeklyOpts,
      statusOpts,
      scheduleOpts,

      viewMore: false,

      cacheDateRangePickerInvState: {}
    }
  },
  watch: {
    skuData: {
      deep: true,
      handler() {
        this.initTreeData()
      }
    },
    'form.sku_ids': {
      immediate: true,
      deep: true,
      handler(v) {
        this.cacheDateRangePickerInvState = {}

        if (v && v.length > 1) {
          this.$tracker.track('action', '#sku_ids_spm')
        }
      }
    },
    'form.weekly': {
      deep: true,
      handler() {
        this.$tracker.track('action', '#weekly_spm')
      }
    },
    'form.time_slot_status': {
      deep: true,
      handler() {
        this.$tracker.track('action', '#schedule_spm')
      }
    },
    'form.empty': {
      deep: true,
      handler() {
        this.$tracker.track('action', '#if_empty_spm')
      }
    }
  },
  computed: {
    getIfEmptySpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `IfEmptyFilter?oid=${oid}&trg=manual`
    },
    getScheduleSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `ScheduleStatusFilter?oid=${oid}&trg=manual`
    },
    getWeeklySpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `WeeklyFilter?oid=${oid}&trg=manual`
    },
    getSkuIdsSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `MultiSKUSelection?oid=${oid}&trg=manual`
    },
    currentDateRangePickerInvState() {
      return Object.values(this.cacheDateRangePickerInvState).reduce((acc, curr) => [...acc, ...curr], [])
    },
    showResetFlag() {
      return this.cacheForm && !_.isEqual(this.cacheForm, this.form)
    },
    sku_id() {
      return this.$route.query.sku_id
    },
    start_date() {
      return this.$route.query.start_date
    },
    end_date() {
      return this.$route.query.end_date
    }
  },
  beforeMount() {
    this.initData()
    this.$on('debounceGetCalendarByDayRangeYear', this.debounceGetCalendarByDayRangeYear)
  },
  beforeDestroy() {
    this.$off('debounceGetCalendarByDayRangeYear')
  },
  methods: {
    moment,
    handleViewMore() {
      this.viewMore = !this.viewMore
    },
    async initData() {
      await this.initTimeSlotData()

      this.initFormData()

      this.$nextTick(() => {
        this.handleEmitSearch()
        this.cacheForm = _.cloneDeep(this.form)
      })
    },
    async initTimeSlotData() {
      let { time_points } =
        (await ajax.get(ADMIN_API.act.get_activity_timeslot_list, {
          params: {
            activity_id: +this.activity_id
          }
        })) || {}

      this.timeslotOpts = (time_points || []).map((item) => {
        return {
          label: item === '00:00' ? this.$t('27726') : item,
          value: item
        }
      })
    },
    initTreeData() {
      this.treeData = this.skuData.reduce((acc, curr, index) => {
        let { package_id, sku_id } = curr
        let pkg = _.find(acc, {
          package_id
        })
        let sku = {
          value: +sku_id,
          key: sku_id,
          title: `${sku_id} - ${curr.unit_name}`
        }

        if (pkg) {
          pkg.children.push(sku)
        } else {
          acc.push({
            value: package_id + '-' + index,
            key: package_id + '-' + index,
            package_id: package_id,
            title: `${package_id} - ${curr.package_name}`,
            children: [sku]
          })
        }

        return acc
      }, [])
    },
    /*
     * Initialize the form with url parameters
     */
    initFormData() {
      let form = this.$options.data().form

      if (this.start_date && this.end_date) {
        let validStartTime = moment().startOf('day')
        let validEndTime = moment(validStartTime)
          .add(this.MAXMUM_DAYS, 'days')
          .endOf('day')

        let start_date = moment(this.start_date).startOf('day')
        let end_date = moment(this.end_date).endOf('day')

        // 只有当 end_date 和 end_date 交集同时存在在有效区间内才计算
        if (!(start_date > validEndTime || end_date < validStartTime)) {
          form.date = [moment(Math.max(start_date, validStartTime)), moment(Math.min(end_date, validEndTime))]
        }
      } else {
        form.date = [moment().add(1, 'days'), moment().endOf('month')]
      }

      this.$set(this, 'form', {
        ...form,
        ...(this.sku_id
          ? {
              sku_ids: [+this.sku_id]
            }
          : {})
      })
    },
    debounceGetCalendarByDayRangeYear: _.debounce(function(year) {
      this.getCalendarByDayRangeYear(year)
    }, 30),
    async getCalendarByDayRangeYear(year) {
      if (!hasOwnProperty.call(this.cacheDateRangePickerInvState, year)) {
        let res = await ajax.get(ADMIN_API.act.get_calendar_over_view_by_day, {
          params: {
            sku_ids: this.form.sku_ids.join(),
            start_time: `${year}-01-01 00:00:00`,
            end_time: `${year}-12-31 23:59:59`
          }
        })

        let data = _.uniq(
          // 去重年月日
          (res.has_inv_days || []).reduce((acc, curr) => {
            let result = curr.match(/\d{4}-\d{2}-\d{2}/)

            if (result) {
              return [...acc, result[0]]
            }

            return acc
          }, [])
        ).map(
          // 获得时间戳
          (item) => +moment(item)
        )

        this.$set(this.cacheDateRangePickerInvState, year, data)

        return data
      } else {
        return this.cacheDateRangePickerInvState[year]
      }
    },
    openChange(v) {
      if (v) {
        this.$nextTick(async () => {
          let startDate = this.startDate ? this.startDate : Date()
          let startYear = moment(startDate)
            .startOf('day')
            .year()
          let endYear = moment(startDate)
            .add(this.MAXMUM_DAYS, 'days')
            .year()
          let yearList = Array(endYear - startYear + 1)
            .fill()
            .map((t, index) => startYear + index)

          await Promise.all(yearList.map((year) => this.getCalendarByDayRangeYear(year)))
        })
      } else {
        setTimeout(() => {
          this.$set(this, 'cacheDateRangePickerInvState', {})
        })
      }
    },
    hasInvStyle(current) {
      return this.currentDateRangePickerInvState.includes(+moment(current).startOf('date'))
    },
    // To 90 day limit
    calendarChange(date) {
      this.startDate = date[0]
    },
    disabledDate(current) {
      let year = moment(current).year()
      if (!_.get(this.cacheDateRangePickerInvState, year, null)) {
        this.$emit('debounceGetCalendarByDayRangeYear', year)
      }
      if (current) {
        let start = current < moment().startOf('day')
        let end =
          (this.startDate &&
            current >
              moment(this.startDate)
                .add(this.MAXMUM_DAYS, 'days')
                .endOf('day')) ||
          false

        return start || end
      }

      return false
    },
    handleReset() {
      this.initFormData()
      this.handleEmitSearch()
    },
    handleEmitSearch() {
      this.$refs?.form?.validate((valid) => {
        if (!valid) return

        let { date, sku_ids, time_slot_status, empty, time_points, ...data } = this.form
        let [start_time, end_time] = date

        let package_ids = this.treeData.reduce((acc, curr) => {
          let skuIds = curr.children.map((sku) => sku.value)

          if (_.intersection(skuIds, sku_ids).length) {
            acc.push(curr.package_id) // package_id
          }

          return acc
        }, [])

        if (_.isEqual(['selectAll'], time_points)) {
          time_points = []
        }

        this.$emit('search', {
          ...data,
          package_ids,
          sku_ids,
          activity_id: this.activity_id,
          time_points: this.timeslotOpts.length === time_points.length ? [] : time_points,
          start_time: this.moment(start_time)
            .startOf('day')
            .format('YYYY-MM-DD HH:mm:ss'),
          end_time: this.moment(end_time)
            .endOf('day')
            .format('YYYY-MM-DD HH:mm:ss'),
          empty: empty.length === 1 ? empty[0] : 0, // 0 is all, empty is all
          time_slot_status:
            time_slot_status.length === 1 ? time_slot_status[0] : time_slot_status.length ? 0 : ''
        })
      })
    }
  }
}
</script>

<style lang="scss">
.bulk-edit-search-form-range {
  .ant-calendar-disabled-cell {
    position: relative;
    width: auto;
    color: rgba(0, 0, 0, 0.25);
    background: #f5f5f5;
    border-radius: 0;
    cursor: not-allowed;
  }
  .ant-calendar-body .ant-calendar-cell.ant-calendar-selected-start-date::before {
    left: 0;
  }
  .search-form-picker-cell {
    position: relative;
    z-index: 1;
    cursor: pointer;
    &.has-inv:after {
      content: ' ';
      position: absolute;
      top: 100%;
      left: 50%;
      display: inline-block;
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background-color: #36b37e;
      transform: translateX(-50%);
      z-index: 999;
    }
  }
}
</style>

<style lang="scss" scoped>
%dot {
  width: 4px;
  height: 4px;
  display: inline-flex;
  border-radius: 50%;
  background-color: #36b37e;
}

.has-filled-dot {
  @extend %dot;
  margin-right: 6px;
}

.range-picker-footer {
  display: flex;
  align-items: center;
}

.searchForm-container {
  padding: 12px;
  background-color: #fafafa;
}

.view-more {
  padding: 12px 0;
  text-align: center;
  .__icon {
    font-size: 12px;
    transform-origin: center;
    transition: transform 0.3s;
  }
  .--fullscreen {
    transform: rotate(180deg);
  }
}
</style>
