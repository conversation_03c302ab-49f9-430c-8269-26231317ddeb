<template>
  <table class="tableFix-container">
    <thead class="thead">
      <tr class="thead-row" v-for="(item, index) in Array(headerEmptyRowAmount).fill()" :key="item">
        <!-- 处理由于 ApprovalOperatorBar 导致的操作高度撑高的问题，待优化 -->
        <td
          class="thead-row-cell cell-fixed-width cell-border-none"
          :class="{
            'should-open-height': index === 2 && shouldOpenHeight
          }"
        ></td>
      </tr>
      <tr class="thead-row thead-row-actions tr-border-bottom">
        <td class="thead-row-cell cell-fixed-width">
          <div class="select-stat">
            <span>
              {{ $t('act_lan_both') }}: <b>{{ viewItemSizeList.length }}</b>
            </span>
            <span>
              {{ $t('global_selected') }}: <b>{{ customChecked.length }}</b>
            </span>
          </div>
          <div class="thead-select-all">
            <div class="__item">{{ $t('act_blocklist_select_all') }}</div>
            <div class="__item">
              <a-button
                v-if="deleteTableRowUUIDList.length"
                size="small"
                type="link"
                style="margin-right: 8px;"
                @click="handleUndoDelete"
              >
                {{ $t('29194') }}
              </a-button>

              <a-tooltip v-if="hasChecked && !isBdAudit && canBeDelete">
                <template slot="title">
                  {{ isDeletable.title }}
                </template>
                <span
                  class="icon-trash"
                  :class="{
                    disabled: !isDeletable.deletable || lockDeleteBtn.status
                  }"
                  v-tooltip="{
                    visible: lockDeleteBtn.status,
                    content: lockDeleteBtn.tips,
                    placement: 'bottom',
                    clearOtherTooltips: true
                  }"
                  @click="deleteSelectedRow({ sendGTM: true })"
                >
                  <i class="gg-trash"></i>
                </span>
              </a-tooltip>

              <a-checkbox
                :indeterminate="indeterminate"
                :checked="selectAll"
                @click="confirmSelectAll"
              ></a-checkbox>
            </div>
          </div>
        </td>
      </tr>
    </thead>
    <div
      class="scroll-table-container"
      ref="list"
      :style="{
        height: height
      }"
    >
      <div
        ref="phantom"
        class="scroll-table-phantom"
        :style="{
          height: phantomHeight + 'px'
        }"
      ></div>
      <table
        class="scroll-table"
        :style="{
          transform: `translate3d(0, ${transformY}px, 0)`
        }"
      >
        <!-- tag 需要为 tbody，不然会出现 tr 错位渲染的问题 -->
        <tbody type="transition" name="flip-list" is="transition-group">
          <tr
            class="cell-fixed-width"
            v-for="(record, index) in visibleFmtData"
            :key="record.uuid"
            :data-row-key="record.uuid"
            :class="{
              'row-is-delete': record.isDelete
            }"
          >
            <td
              v-if="record.rowSpan"
              :rowspan="record.rowSpan"
              :class="{
                'table-row-cell': record.rowSpan !== 1
              }"
            >
              <!-- td height rely on div -->
              <div
                v-if="record.rowSpan === 1"
                :style="{
                  height: getRowHeightByIndex(index)
                }"
                class="table-row-cell cell-is-date"
              >
                <span>{{ record.start_date }}&nbsp;&nbsp;</span>
                <span>({{ record.week_day }})</span>
              </div>
              <template v-else>
                {{ record.start_date }}
              </template>
            </td>
            <td
              class="table-row-cell cell-flex-space"
              :style="{
                height: getRowHeightByIndex(index)
              }"
            >
              <span>{{ record.start_time }}</span>

              <span v-if="!record.isDelete" class="__actions">
                <a-tooltip v-if="!isBdAudit && canBeDelete">
                  <template slot="title">
                    {{ isDeletable.title }}
                  </template>
                  <span
                    class="icon-trash"
                    :class="{
                      disabled: !isDeletable.deletable || lockDeleteBtn.status
                    }"
                    v-tooltip="{
                      visible: lockDeleteBtn.status,
                      content: lockDeleteBtn.tips,
                      placement: 'bottom',
                      clearOtherTooltips: true
                    }"
                    @click="
                      deleteSelectedRow({
                        uuid: record.uuid
                      })
                    "
                  >
                    <i class="gg-trash"></i>
                  </span>
                </a-tooltip>

                <a-checkbox
                  :value="record.uuid"
                  :checked="customChecked.includes(record.uuid)"
                  @change="changeCustomChecked"
                />
              </span>
              <a-button
                v-else
                size="small"
                @click="
                  handleUndoDelete({
                    uuid: record.uuid
                  })
                "
              >
                Undo
              </a-button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </table>
</template>

<script>
export default {
  name: 'BulkEditTableFix',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Boolean,
      required: true
    },
    customChecked: {
      type: Array,
      required: true
    },
    visibleData: {
      type: Array,
      required: true
    },
    viewItemSizeList: {
      type: Array,
      required: true
    },
    height: {
      type: String,
      default: '100%'
    },
    transformY: {
      type: Number,
      default: 0
    },
    phantomHeight: {
      type: Number,
      default: 0
    },
    scrollTop: {
      type: Number,
      default: 0
    },
    indeterminate: {
      type: Boolean,
      default: false
    },
    hasChecked: {
      type: Boolean,
      default: false
    },
    deleteTableRowUUIDList: {
      type: Array,
      required: true
    },
    // 删除按钮是否可点击（是否可进行删除操作）
    isDeletable: {
      type: Object,
      default: () => {
        return {
          deletable: true,
          title: __('global_delete')
        }
      }
    },
    // 有 sku ticket 操作的 bar，需要撑开高度
    shouldOpenHeight: {
      type: Boolean,
      default: false
    },
    hiddenActPkgInfo: {
      type: Boolean,
      default: false
    },
    showHeaderOperatorRow: {
      type: Boolean,
      default: true
    },
    canBeDelete: {
      type: Boolean,
      default: true
    },
    lockDeleteBtn: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    scrollTop(v) {
      this.$refs.list.scrollTop = v
    }
  },
  computed: {
    headerEmptyRowAmount() {
      let amount = 3

      if (!this.showHeaderOperatorRow) {
        amount -= 1
      }

      if (this.hiddenActPkgInfo) {
        amount -= 2
      }

      return amount
    },
    isBdAudit() {
      return klook.getPlatformRoleKey() === 'bd_audit'
    },
    visibleFmtData() {
      return this.visibleData.map((item) => ({
        ...item,
        rowSpan: 1
      }))
    },
    selectAll: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('change', v)
      }
    }
  },
  methods: {
    getRowHeightByIndex(index) {
      let currentSize = this.viewItemSizeList[index]
      if (currentSize) {
        return currentSize.height + 'px'
      }

      return '40px'
    },
    handleUndoDelete({ uuid }) {
      this.$emit('undoDeleteRow', { uuid })
    },
    deleteSelectedRow({
      uuid = null, // single or bulk delete
      sendGTM = false
    } = {}) {
      if (!this.isDeletable.deletable || this.lockDeleteBtn.status) {
        return
      }
      this.$emit('deleteSelectedRow', { uuid })

      if (sendGTM) {
        this.GTMHookItem('Price&Inventory|ListBulkDelete_Click')
      }
    },
    changeCustomChecked(e) {
      let { checked, value } = e.target

      this.$emit('changeCustomChecked', {
        checked,
        value
      })
    },
    confirmSelectAll() {
      this.selectAll = !this.selectAll
      this.$emit('confirmSelectAll')
    }
  }
}
</script>

<style lang="scss" scoped>
.tableFix-container {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  /* 1px 用于覆盖 border-right */
  width: 301px;
  background-color: #fff;
  border: 1px solid #e5e4e5;
  border-bottom: none;
  transition: box-shadow 0.3s;
  .scroll-table-container {
    border: none;
    overflow: hidden;
  }
  .thead-select-all {
    display: flex;
    justify-content: space-between;
    .__item {
      display: inline-flex;
      align-items: center;
    }
  }
  .tr-border-bottom {
    border-bottom: 1px solid #e5e4e5;
  }
  .cell-flex-space {
    display: inline-flex;
    justify-content: space-between;
    span.__actions {
      display: inline-flex;
      align-items: center;
    }
  }
  .table-row-cell.cell-is-date {
    display: inline-flex;
    justify-content: space-between;
    padding: 8px 15px;
  }
  .select-stat {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
  }
  .cell-border-none {
    min-height: 45px;
  }

  .cell-border-none {
    height: 45px;
  }
  .should-open-height {
    height: 200px;
  }
}
</style>
