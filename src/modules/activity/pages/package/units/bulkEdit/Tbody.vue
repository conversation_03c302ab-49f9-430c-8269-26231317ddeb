<template>
  <div
    ref="list"
    :style="{
      height: visibleData.length ? height : '150px'
    }"
    class="tbody scroll-table-container"
    @scroll="scrollEvent"
  >
    <div ref="phantom" class="scroll-table-phantom"></div>
    <div ref="content" class="scroll-table">
      <template v-if="visibleData.length">
        <div
          class="scroll-table-item"
          ref="items"
          v-for="(item, index) in visibleData"
          :key="item.uuid"
          :id="item.uuid"
          :data-row-key="item.uuid"
        >
          <slot ref="slot" :item="item" :index="index" :scrollEventCalculating="scrollEventCalculating">
          </slot>
        </div>
      </template>

      <a-empty
        v-else
        :style="{
          marginTop: '8px'
        }"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'BulkEditTbody',
  model: {
    prop: 'value', // scrollEventCalculating
    event: 'change'
  },
  props: {
    value: {
      type: <PERSON>ole<PERSON>,
      required: true
    },
    viewItemSizeList: {
      type: Array,
      required: true
    },
    visibleData: {
      type: Array,
      required: true
    },
    estimatedItemSize: {
      type: Number,
      required: true
    },
    height: {
      type: [Number, String],
      default: '100%'
    }
  },
  data() {
    // Virtual scrolling table does not take effect if the number of rows is less then NUMBER_OF_CRITICAL
    this.NUMBER_OF_CRITICAL = 200
    this.preScrollTop = 0
    // The number of rows cached before and after the table scrolling outside the visible area
    this.RESERVED = 10

    return {
      screenHeight: 0,
      // Table data effective index
      start: 0,
      end: 0,
      // Compatible with page zoom in and out
      zoom: 1
    }
  },
  computed: {
    // stop the judge event when scroll calculating
    scrollEventCalculating: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('change', v)
      }
    },
    visibleCount() {
      return Math.ceil(this.screenHeight / (this.estimatedItemSize * this.zoom))
    },
    list_length() {
      return this.viewItemSizeList.length
    },
    biggestStartIndex() {
      if (this.list_length) {
        return Math.max(this.list_length - this.visibleCount - 1, 0)
      }
      return 999999
    }
  },
  watch: {
    list_length() {
      this.resetScrollState()
      this.setIndexByStart(0)
    }
  },
  beforeMount() {
    this.zoom = this.getZoom()
  },
  async mounted() {
    await this.$nextTick()
    this.screenHeight = this.$el.clientHeight
  },
  methods: {
    getZoom() {
      let tbody = document.querySelector('.tbody.scroll-table-container')
      let bgColor = (tbody && getComputedStyle(tbody).backgroundColor) || false
      let retina = 1

      if (bgColor === 'rgb(255, 255, 254)') {
        retina = 2
      }

      if (bgColor === 'rgb(255, 255, 253)') {
        retina = 3
      }

      return Math.round(window.devicePixelRatio * 100) / 100 / retina
    },
    // Called when bulkTable.vue watch list to reset the table state
    resetTableState() {
      this.resetScrollState()
      this.refreshTable()
      this.screenHeight = this.$el.clientHeight
    },
    // Update the height of the virtual scroll list
    refreshTable() {
      let phantom = this.$refs.phantom
      // Does not need to take effect
      if (!this.list_length || this.list_length < this.NUMBER_OF_CRITICAL) {
        phantom && (phantom.style.height = 0 + 'px')
        this.$emit('changePhantomHeight', 0)

        return
      }

      let height = this.viewItemSizeList[this.list_length - 1].bottom * this.zoom

      phantom.style.height = height + 'px'
      this.$emit('changePhantomHeight', height)
    },
    scrollToTableRowByIndex(index = 0, cb = null) {
      this.$refs.list.scrollTop = (this.viewItemSizeList[index].top - this.estimatedItemSize) * this.zoom

      setTimeout(() => {
        const dom = document.querySelector('.cell-is-invalid')
        dom &&
          dom.scrollIntoView({
            block: 'center',
            inline: 'center'
          })
      }, 100)

      typeof cb === 'function' && cb()
    },
    resetScrollState() {
      this.$refs.list.scrollTop = 0
      this.setIndexByStart(0)
      this.setStartOffset()
    },
    // To optimize
    // If the scroll height is small, can the index self-increase or self-decreasing
    getStartIndex(scrollTop = 0) {
      return this.binarySearch(this.viewItemSizeList, scrollTop)
    },
    binarySearch(list, scrollTop) {
      // let isDown = this.preScrollTop < scrollTop

      let start = 0
      let end = list.length - 1
      // if (isDown) {
      //   start = this.start
      //   end = list.length - 1
      // } else {
      //   // up
      //   start = 0
      //   end = this.end
      // }
      let tempIndex = null
      while (start <= end) {
        let midIndex = parseInt((start + end) / 2)
        let { bottom, top } = list[midIndex]
        bottom = bottom * this.zoom
        top = top * this.zoom
        if (top <= scrollTop && bottom > scrollTop) {
          return midIndex
        } else if (bottom === scrollTop) {
          return midIndex + 1
        } else if (bottom < scrollTop) {
          start = midIndex + 1
        } else if (bottom > scrollTop) {
          if (tempIndex === null || tempIndex > midIndex) {
            tempIndex = midIndex
          }
          end = end - 1
        }
      }

      return tempIndex
    },
    setStartOffset() {
      let startOffset = this.start >= 1 ? this.viewItemSizeList[this.start].bottom : 0
      startOffset *= this.zoom

      this.$refs.content.style.transform = `translate3d(0, ${startOffset}px, 0)`
      // this.$refs.content.style.marginTop = `${startOffset}px`

      this.$emit('changeTransformVal', startOffset)
    },
    scrollEvent: _.throttle(function () {
      this.scrollEventCalculating = true

      const listRef = this.$refs.list
      if (!listRef) {
        return
      }

      let scrollTop = listRef.scrollTop
      // Sync fixed table scrolling action
      this.$emit('changeScrollTop', scrollTop)

      if (this.list_length < this.NUMBER_OF_CRITICAL) {
        this.scrollEventCalculating = false

        return
      }

      this.$emit('clearEditTableCell')

      let start
      if (this.preScrollTop === scrollTop) {
        start = this.start
      } else {
        start = this.getStartIndex(scrollTop)
        this.preScrollTop = scrollTop
      }

      this.setIndexByStart(start)
      this.setStartOffset()

      this.clearScrollEventCalculating()
    }, 20),
    setIndexByStart(start, reserved = this.RESERVED) {
      if (this.list_length < this.NUMBER_OF_CRITICAL) {
        this.start = 0
        this.end = this.list_length - 1
      } else {
        start = start - reserved
        this.start = Math.min(Math.max(start, 0), this.biggestStartIndex)

        this.end = Math.min(this.start + this.visibleCount + reserved * 2, this.list_length)
      }

      this.$emit('changeViewIndex', {
        start: this.start,
        end: this.end
      })
    },
    clearScrollEventCalculating: _.debounce(function () {
      this.scrollEventCalculating = false
    }, 120)
  }
}
</script>

<style scoped>
.tbody {
  overflow-y: overlay;
}
.scroll-table-item {
  color: #555;
  box-sizing: border-box;
}

@media screen and (-webkit-min-device-pixel-ratio: 2), screen and (min-device-pixel-ratio: 2) {
  .scroll-table-container {
    background-color: rgb(255, 255, 254);
  }
}

@media screen and (-webkit-min-device-pixel-ratio: 3), screen and (min-device-pixel-ratio: 3) {
  .scroll-table-container {
    background-color: rgb(255, 255, 253);
  }
}
</style>
