<template>
  <div class="thead">
    <!-- package/sku name -->
    <!-- eslint-disable vue/no-use-v-if-with-v-for,vue/no-confusing-v-for-v-if -->
    <div class="thead-row" v-for="chunk of ['pkg', 'sku']" :key="chunk" v-if="!hiddenActPkgInfo">
      <div class="thead-row-cell cell-is-empty cell-fixed-width"></div>
      <div class="thead-row-cell cell-is-empty cell-fixed-width" width="150"></div>

      <div
        v-for="sku in allSku"
        :key="sku.sku_id"
        class="thead-row-cell thead-cell-title"
        :colspan="viewFields.length"
        :style="{
          height: chunk === 'sku' && isMerchant && shouldOperatorSkuList.includes(sku.sku_id) ? '200px' : '',
          width: `${sku.is_combo ? (viewFields.length - 1) * 150 : viewFields.length * 150}px`
        }"
      >
        <span class="thead-title sp">
          {{ chunk === 'pkg' ? getPackageName(sku.sku_id) : getSkuNameById(sku.sku_id) }}
        </span>

        <span v-if="chunk === 'sku' && !isMerchant && sku.inv_model === 4">
          {{ $t('48293') }}: {{ sku.item_id }}
          <QuestionIcon :message="$t('48294')" />
        </span>

        <ApprovalOperatorBar
          v-if="chunk === 'sku' && isMerchant && shouldOperatorSkuList.includes(sku.sku_id)"
          style="margin-top: 12px; height: 120px"
          :unitData="sku"
          :actions-is-inline="true"
        />
      </div>
    </div>
    <!-- all field title -->
    <div class="thead-row">
      <div class="thead-row-cell cell-fixed-width dd"></div>
      <div class="thead-row-cell cell-fixed-width style-border-right"></div>

      <template v-for="sku in allSku">
        <div
          v-for="field in viewFields"
          v-if="getFieldShow(field.key, sku)"
          :key="`${sku.sku_id}-${field.key}`"
          class="thead-row-cell thead-title"
          :style="field.style"
        >
          {{ getFieldTitle(field, sku) }}
        </div>
      </template>
    </div>
    <!-- all field key -->
    <!--<div class="thead-row thead-row-actions">-->
    <component
      :is="disabled ? 'a-tooltip' : 'div'"
      v-if="showHeaderOperatorRow"
      class="thead-row thead-row-actions"
      placement="bottom"
    >
      <template slot="title">
        {{ $t('30289') }}
      </template>
      <span
        :style="{
          display: disabled ? 'flex' : 'contents'
        }"
      >
        <div class="thead-row-cell cell-fixed-width style-border-bottom"></div>
        <div class="thead-row-cell cell-fixed-width style-border-bottom style-border-right"></div>

        <template v-for="sku in allSku">
          <div
            v-for="field in viewFields"
            v-if="getFieldShow(field.key, sku)"
            :key="`${sku.sku_id}-${field.key}`"
            v-tooltip="{
              visible: getLock({
                field: field.key,
                sku_id: sku.sku_id
              }).status,
              content: getLock({
                field: field.key,
                sku_id: sku.sku_id
              }).tips,
              placement: 'bottom'
            }"
            class="thead-row-cell thead-row-field"
            :class="{
              'cell-bg-white': !disabled
            }"
            :style="field.style"
          >
            <span
              v-if="hasFixedValueOnCell(field.key, sku.sku_id)"
              :class="{
                'cell-flex-end': isOnPriceField(field.key) || field.key === 'inv_quantity'
              }"
            >
              <template v-if="getFixedIncludesBySkuField(field.key, sku.sku_id)">
                <SSPCellShow
                  v-if="field.key === 'special_selling_price'"
                  :value="bulkForm[sku.sku_id][field.key]"
                />
                <span
                  v-else
                  class="cell-overflow-ellipsis"
                  v-html="
                    tableCellInputData({
                      field: field.key,
                      data: bulkForm[sku.sku_id],
                      sku_id: sku.sku_id
                    })
                  "
                ></span>
                <i class="gg-undo" @click.stop.prevent="handleClearFixedValue(field.key, sku.sku_id)"></i>
              </template>
              <span v-else class="table-cell--text" @click="handleEditFixedCell(field.key, sku.sku_id)">
                <i class="gg-pen"></i>
              </span>
            </span>

            <template v-else>
              <template v-if="field.key === 'special_selling_price'">
                <span
                  v-if="!getDisabled(field.key, sku.sku_id, sku)"
                  class="table-cell--text cell-flex-end"
                  @click="handleEditSSP(sku, field.key, sku.sku_id)"
                >
                  <SSPCellShow :value="bulkForm[sku.sku_id][field.key]"></SSPCellShow>
                  <i v-show="!bulkForm[sku.sku_id][field.key].length" class="gg-pen"></i>
                </span>
              </template>
              <a-select
                v-else-if="field.key === 'publish_status'"
                class="bulk-edit__input"
                size="small"
                :style="{
                  width: '100%'
                }"
                :options="getFilteredPublishStatus(sku)"
                :disabled="getDisabled(field.key, sku.sku_id)"
                :value="setSelectNumber(bulkForm[sku.sku_id][field.key])"
                @change="
                  (value) =>
                    updateTableCell({
                      value: value,
                      field: field.key,
                      sku_id: sku.sku_id
                    })
                "
              >
              </a-select>

              <template v-else-if="field.key === 'cut_off_time'">
                <a-select
                  v-model="bulkForm[sku.sku_id].days"
                  class="bulk-edit__input"
                  size="small"
                  :disabled="getDisabled(field.key, sku.sku_id, sku)"
                  :options="cutOffTimeOpts"
                  @change="
                    (value) =>
                      updateTableCell({
                        field: 'days',
                        value: value,
                        sku_id: sku.sku_id
                      })
                  "
                ></a-select>
                <a-time-picker
                  class="bulk-edit__input"
                  format="HH:mm"
                  valueFormat="HH:mm"
                  size="small"
                  :style="{
                    marginTop: '4px'
                  }"
                  :disabled="getDisabled(field.key, sku.sku_id, sku)"
                  :allow-clear="false"
                  :open.sync="openTimePicker[sku.sku_id]"
                  v-model="bulkForm[sku.sku_id].time"
                  @change="
                    (value) =>
                      updateTableCell({
                        field: 'time',
                        value: value,
                        sku_id: sku.sku_id
                      })
                  "
                >
                  <a-button
                    slot="addon"
                    size="small"
                    type="primary"
                    @click="openTimePicker[sku.sku_id] = false"
                  >
                    {{ $t('global_button_ok') }}
                  </a-button>
                </a-time-picker>
              </template>

              <template v-else>
                <a-input-number
                  class="bulk-edit__input"
                  size="small"
                  defaultValue=""
                  v-bind="getPriceFieldBind(field.key)"
                  :allow-clear="true"
                  :disabled="getDisabled(field.key, sku.sku_id, sku)"
                  v-model="bulkForm[sku.sku_id][field.key]"
                  @change="
                    (value) =>
                      updateTableCell({
                        value: value,
                        field: field.key,
                        sku_id: sku.sku_id,
                        type: 'inputNumber'
                      })
                  "
                >
                </a-input-number>
              </template>
            </template>
          </div>
        </template>
      </span>
    </component>

    <!-- 埋点 -->
    <div id="cut_off_time_spm" :data-spm-module="getCutoffTimeSpm" data-spm-virtual-item="__virtual"></div>
    <div id="inv_quantity_spm" :data-spm-module="getInvSpm" data-spm-virtual-item="__virtual"></div>
    <div id="publish_status_spm" :data-spm-module="getPublishedSpm" data-spm-virtual-item="__virtual"></div>
    <div id="cost_price_spm" :data-spm-module="getCostSpm" data-spm-virtual-item="__virtual"></div>
    <div id="retail_price_spm" :data-spm-module="getRetailSpm" data-spm-virtual-item="__virtual"></div>
    <div id="selling_price_spm" :data-spm-module="getSellingSpm" data-spm-virtual-item="__virtual"></div>
  </div>
</template>

<script>
import {
  publishStatusOpts,
  cutOffTimeOpts,
  getPriceFieldBind,
  getTableCellText,
  priceLinkageUpdate,
  isOnPriceField,
  getFixedValueByRecord,
  setFixedValueByRecord,
  getBulkViewColumnsByPlatform
} from './utils'
import SSPCellShow from './SSPCellShow'

import QuestionIcon from '@activity/pages/package/units/components/QuestionIcon.vue'

import ApprovalOperatorBar from '../unit-list/calendar-setting/components/approvalOperatorBar'
import { mapState } from 'vuex'

export default {
  name: 'bulkFormThead',
  components: {
    SSPCellShow,
    QuestionIcon,
    ApprovalOperatorBar
  },
  props: {
    value: {
      // bulkForm
      type: Object,
      required: true
    },
    allSku: {
      type: Array,
      required: true
    },
    viewFields: {
      type: Array,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    exchange_rate: {
      type: Array,
      required: true
    },
    customChecked: {
      type: Array,
      required: true
    },
    skuFieldDisabledStateObj: {
      type: Object,
      required: true
    },
    skuFieldLockStateObj: {
      type: Object,
      default: () => ({})
    },
    shouldOperatorSkuList: {
      type: Array,
      default: () => []
    },
    hiddenActPkgInfo: {
      type: Boolean,
      default: false
    },
    showHeaderOperatorRow: {
      type: Boolean,
      default: true
    }
    // validWorkerQueue: {
    //   type: Array,
    //   required: true
    // }
  },
  data() {
    this.publishStatusOpts = publishStatusOpts
    this.cutOffTimeOpts = cutOffTimeOpts

    return {
      openTimePicker: {},
      selectAll: false,
      fixedFieldRecordList: {} // record fixed state to show Undo
    }
  },
  watch: {
    customChecked: {
      deep: true,
      handler(newV) {
        // if the custom check is set to a empty array,
        if (newV.length === 0) {
          this.$set(this, 'fixedFieldRecordList', {})
        }
      }
    },
    allSku: {
      deep: true,
      immediate: true,
      handler() {
        this.openTimePicker = this.allSku.reduce(
          (acc, curr) => ({
            ...acc,
            [curr.sku_id]: false
          }),
          {}
        )
      }
    }
  },
  computed: {
    ...mapState(['isMerchant']),
    pageRole() {
      return klook.getPlatformRoleKey()
    },
    allSkuModelObject() {
      return this.allSku.reduce((acc, curr) => {
        return {
          ...acc,
          [curr.sku_id]: curr
        }
      }, {})
    },
    bulkForm: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('change', v)
      }
    },
    platformBulkViewColumns() {
      return getBulkViewColumnsByPlatform()
    },
    getCutoffTimeSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `BulkModifyCutOffTime?oid=${oid}&trg=manual`
    },
    getPublishedSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `BulkModifyStatus?oid=${oid}&trg=manual`
    },
    getInvSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `BulkModifyInventory?oid=${oid}&trg=manual`
    },
    getCostSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `BulkModifyCost?oid=${oid}&trg=manual`
    },
    getRetailSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `BulkModifyRetailPrice?oid=${oid}&trg=manual`
    },
    getSellingSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `BulkModifySellingPrice?oid=${oid}&trg=manual`
    }
  },
  methods: {
    fieldsClick(field) {
      switch (field) {
        case 'cut_off_time':
        case 'days':
        case 'time':
          this.$tracker.track('action', '#cut_off_time_spm')
          break
        case 'publish_status':
          this.$tracker.track('action', '#publish_status_spm')
          break
        case 'inv_quantity':
          this.$tracker.track('action', '#inv_quantity_spm')
          break
        case 'cost_price':
          this.$tracker.track('action', '#cost_price_spm')
          break
        case 'retail_price':
          this.$tracker.track('action', '#retail_price_spm')
          break
        case 'selling_price':
          this.$tracker.track('action', '#selling_price_spm')
          break
      }
    },
    getTableCellText,
    getPriceFieldBind,
    isOnPriceField,
    getFieldShow(key, sku) {
      return !(sku.is_combo && key === 'cost_price')
    },
    resetState() {
      this.fixedFieldRecordList = {}
    },
    /**
     * @param {string} field
     * @param {object} data - bulk edit data of current sku
     * @returns the filed of bulk edit form
     */
    tableCellInputData({ field, data, sku_id }) {
      let invModel = this.allSkuModelObject[sku_id].inv_model

      return getTableCellText({
        field,
        data,
        value: data[field],
        invModel
      })
    },
    handleClearFixedValue(field, sku_id) {
      let currentSkuData = this.bulkForm[sku_id]

      let data = setFixedValueByRecord({
        currentSkuData,
        field
      })
      let fieldList = Object.keys(data)
      for (let i of fieldList) {
        currentSkuData[i] = ''
      }

      this.fixedFieldRecordList[sku_id] = this.fixedFieldRecordList[sku_id].filter(
        (field) => !fieldList.includes(field)
      )
      this.$forceUpdate()

      this.$emit('clearBulkFixedValue', {
        fieldList,
        sku_id,
        field
      })
    },
    hasFixedValueOnCell(field, sku_id) {
      let currentSkuData = this.bulkForm[sku_id]

      if (!currentSkuData) {
        return false
      }

      let { isValid } = getFixedValueByRecord({
        currentSkuData,
        field
      })

      return isValid
    },
    handleEditFixedCell: _.debounce(function (field, sku_id) {
      if (this.disabled) return

      let currentSkuData = this.bulkForm[sku_id]
      let data = setFixedValueByRecord({
        currentSkuData,
        field
      })

      if (data) {
        Object.assign(currentSkuData, data)
        this.setFixedFieldRecordList(sku_id, Object.keys(data))

        this.$emit('changeBulkEditForm', {
          data,
          sku_id,
          field,
          isFromFixed: true
        })

        // for (let [k, v] of Object.entries(data)) {
        //   this.$emit('changeBulkEdit', {
        //     sku_id,
        //     field: k,
        //     value: v,
        //     isFromFixed: true
        //   })
        // }
      }
    }, 250),
    setFixedFieldRecordList(sku_id, fields = []) {
      if (!hasOwnProperty.call(this.fixedFieldRecordList, sku_id)) {
        this.fixedFieldRecordList[sku_id] = []
      }

      this.fixedFieldRecordList[sku_id] = [...new Set([...this.fixedFieldRecordList[sku_id], ...fields])]

      this.$forceUpdate()
    },
    handleEditSSP(sku, field, sku_id) {
      if (
        this.disabled ||
        this.getLock({
          field,
          sku_id
        }).status
      ) {
        return
      }
      this.$emit('bulkEditSSP', sku)
    },
    getFixedIncludesBySkuField(field, sku_id) {
      let fields = _.get(this.fixedFieldRecordList, sku_id, [])

      return fields.includes(field)
    },
    // pic 库存不能编辑的项目
    picDisabled(field = '', sku = {}) {
      const disabled = ['inv_quantity'].includes(field)
      const inv_model = sku.inv_model === 4
      return disabled && inv_model
    },
    getDisabled(field, sku_id, sku = {}) {
      // combo
      let comboDisabled = false
      if (sku.is_combo) {
        comboDisabled = sku.combo_selling_price_type === 3 && field === 'selling_price' ? false : true
      }

      if (
        _.get(this.skuFieldDisabledStateObj, `${sku_id}.${field}`, false) ||
        this.getLock({ sku_id, field }).status
      ) {
        return true
      }

      let currentSkuData = this.bulkForm[sku_id]

      let { fixedVal, isValid } = getFixedValueByRecord({
        currentSkuData,
        field
      })

      let fixedDisabled =
        isValid && (currentSkuData[field] === fixedVal || this.getFixedIncludesBySkuField(field, sku_id))

      return this.disabled || fixedDisabled || this.picDisabled(field, sku) || comboDisabled
    },
    getLock({ sku_id, field }) {
      return _.get(this.skuFieldLockStateObj, `${sku_id}.${field}`, {
        status: false,
        tips: ''
      })
    },
    getFieldTitle(field, sku) {
      let { key, title } = field
      if (['selling_price', 'retail_price'].some((item) => key.includes(item))) {
        return `${title}(${sku.selling_currency})`
      }

      if (['cost_price'].some((item) => key.includes(item))) {
        return `${title}(${sku.cost_currency})`
      }

      return title
    },
    // Format data for number, because the back-end data is boolean,
    // but the ant-select only support string or number
    /*
     * @returns {number|empty string} - select value
     */
    setSelectNumber(data) {
      return data === '' ? data : Number(data)
    },
    updateTableCell: _.debounce(function ({ value, field, sku_id, type }) {
      if (value !== '') {
        if (type === 'boolean') {
          // Format data for number, because the back-end data is boolean,
          // but the ant-select only support string or number
          value = Boolean(value)
        }
      }

      let skuData = this.bulkForm[sku_id]
      let { special_selling_price: sspList, selling_currency } = skuData
      if (field === 'selling_price' && sspList && sspList.length) {
        this.$attrs.bindSspList2mixin(sspList, {
          price: value,
          sellingCurrency: selling_currency
        })
      }
      /**
       * 当输入框框没有失去焦点的时候，去点击全选，会导致该列的所有数据都更新
       * 原因：ant input 失去焦点时会触发 onBlur，会重新获取有效的值并重置当前的 input，进而又触发了 input 的 change 事件，
       * updateTableCell 是 debounce 0.3s ，而此时全选已经赋值成功，执行在 updateTableCell 之前，所有会当前失去焦点列都被更新了
       * 源码部分：ant input onBlur
       */
      if (type === 'inputNumber' && !_.isEqual(skuData[field], value)) {
        return
      }

      skuData[field] = value

      if (['cost_price', 'selling_price', 'take_rate'].includes(field)) {
        let { cost_price, selling_price, take_rate, cost_currency, selling_currency } = skuData
        let linkageData = priceLinkageUpdate({
          originField: field,
          cost_price,
          selling_price,
          take_rate,
          cost_currency,
          selling_currency,
          exchange_rate: this.exchange_rate
        })

        if (linkageData.updateField) {
          skuData[linkageData.updateField] = linkageData[linkageData.updateField]
        }
      }

      this.$emit('changeBulkEditForm', {
        sku_id,
        field,
        data: {
          [field]: value
        },
        isFromFixed: false
      })
      // 埋点
      this.fieldsClick(field)
    }, 300),
    getPackageName(sku_id = '') {
      let res = _.find(this.allSku, { sku_id })

      return res ? res.package_id + ' - ' + res.package_name : ''
    },
    getSkuNameById(sku_id) {
      let res = _.find(this.allSku, {
        sku_id
      })

      return res ? res.sku_id + ' - ' + res.unit_name : ''
    },
    getFilteredPublishStatus(sku) {
      return publishStatusOpts.filter((item) => sku?.allowed_presale || item.value !== 2)
    }
  }
}
</script>

<style lang="scss" scoped>
.thead {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  .thead-row {
    display: flex;
    min-width: 100%;
  }
  .thead-cell-title {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }
  th {
    font-weight: normal;
  }
  .thead-row {
    background-color: #fafafa;
  }
  .bulk-edit__input {
    display: block;
    width: 100%;
  }
  .cell-flex-end {
    width: 100%;
    height: 40px;
    display: inline-flex;
    justify-content: flex-end;
    align-items: center;
  }
  .thead-row-bottom-none {
    border-bottom: none;
  }
  .cell-bg-white {
    background-color: white;
  }
  .table-fixed {
    box-shadow: 8px 0 6px -4px rgba(0, 0, 0, 0.15);
  }
  .cell-overflow-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .thead-title {
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    flex: 1;
    &.sp {
      padding: 0 10px;
      flex: 1;
    }
  }
  .style-border-bottom {
    border-bottom: 1px solid #e5e4e5 !important;
  }

  .style-border-right {
    border-right: 1px solid #e5e4e5 !important;
  }
}
</style>
