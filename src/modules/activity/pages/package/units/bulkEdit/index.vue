<template>
  <div class="bulkEdit-container common-scrollbar" :data-spm-page="getPageSpm">
    <search-form ref="search" :sku-data="skuData" @search="handleSearch" />

    <action-bar
      ref="bar"
      v-model="viewField"
      :showFixedPriceBar="showFixedPriceBar"
      :showFixedInvBar="showFixedInvBar"
      :showSubSkuBar="showSubSkuBar"
      :exclude="excludeField"
      @fullscreen="handleFullscreen"
    >
      <template #other>
        <li class="explain--item" v-if="tableState.updated">
          <span
            class="rect content"
            :style="{
              backgroundColor: 'rgba(54, 179, 126, 0.05)',
              border: '1px solid #36b37e'
            }"
          ></span>
          <span class="fixed-tips"> {{ $t('30290') }}</span>
        </li>
        <li class="explain--item" v-if="tableState.deleted">
          <span
            class="rect content"
            :style="{
              backgroundColor: 'rgba(255, 86, 48, 0.05)'
            }"
          ></span>
          <span class="fixed-tips"> {{ $t('30291') }}</span>
        </li>
      </template>
      <template #fullscreen>
        <a-tooltip v-model="visibleTooltip">
          <template slot="title">
            {{ fullscreen ? $t('30288') : $t('30287') }}
          </template>
          <span class="fullscreen-action" @click.stop.prevent="handleFullscreen">
            <a-icon v-if="fullscreen" type="fullscreen-exit" />
            <a-icon v-else type="fullscreen" />
          </span>
        </a-tooltip>
      </template>
    </action-bar>

    <costDataSourceTip style="margin-bottom: 12px" />

    <a-spin :spinning="spinning" :delay="300">
      <bulk-table
        ref="table"
        :list="list"
        :sku-data="skuData"
        :key="refreshKey"
        :view-field="viewField"
        :shouldOperatorSkuList="shouldOperatorSkuList"
        :exclude-field="excludeField"
        @differenceField="changeDifferenceField"
        @tableState="changeCanSubmit"
        @confirmToSubmit="handleSubmitBulkTable"
      />
    </a-spin>

    <CommonFooter v-if="canSubmitFlag">
      <a-button @click="handleReset(true)">
        {{ $t('global_reset') }}
      </a-button>
      <a-button type="primary" @click="handleSave">
        {{ $t('global_button_save') }}
      </a-button>
    </CommonFooter>

    <!-- 埋点 -->
    <div id="full_screen_spm" :data-spm-module="getFullScreenSpm" data-spm-virtual-item="__virtual"></div>
  </div>
</template>

<script>
import SearchForm from './SearchForm'
import ActionBar from './ActionBar'
import BulkTable from './BulkTable'
import { getBulkViewColumnsByPlatform } from './utils'
import { getEditLang } from '@activity/utils'
import CommonFooter from '@activity/components/CommonFooter.vue'
import { isMerchant } from '@/env'
import { mapGetters, mapMutations } from 'vuex'
import costDataSourceTip from '../unit-list/calendar-setting/components/costDataSourceTip'
import { getApprovalOperatorBarFlag } from '../utils/index'
import { Max_Schedule_Limit } from '@activity/pages/package/package_const.js'
import { UNSAFE_PRICE_CODE, showUnsavePriceReasonModal } from '@activity/components/UnsavePriceReasonModal'

export default {
  name: 'BulkEdit',
  inject: ['page_from'],
  provide() {
    return {
      activity_id: +this.activity_id,
      bulkEditTableIndex: this
    }
  },
  components: {
    BulkTable,
    ActionBar,
    SearchForm,
    CommonFooter,
    costDataSourceTip
  },
  props: {
    activity_id: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      viewField: [],

      list: [],
      skuData: [],

      invTotal: {},

      fullscreen: false,
      tableState: {
        updated: false,
        deleted: false
      },

      spinning: false,

      showFixedPriceBar: false,
      showFixedInvBar: false,
      showSubSkuBar: false,
      showPICTips: false,

      refreshKey: 0,

      visibleTooltip: false,

      shouldOperatorSkuList: [],

      unsavePriceReason: {
        unsafe_price_reason_code: undefined,
        unsafe_price_reason_note: undefined
      }
    }
  },
  computed: {
    ...mapGetters(['isTour']),
    getFullScreenSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `FullscreenMode?oid=${oid}&trg=manual`
    },
    getPageSpm() {
      const oid = `activity_${this.activity_id || 0}`
      return `BulkEdit?oid=${oid}`
    },
    isBdAudit() {
      return klook.getPlatformRoleKey() === 'bd_audit'
    },
    local_storage_key() {
      return isMerchant ? 'merchant_bulk_edit_table_view_field_v2' : 'admin_bulk_edit_table_view_field_v2'
    },
    canSubmitFlag() {
      return Boolean(this.tableState.updated || this.tableState.deleted)
    },
    excludeField() {
      return this.isTour ? ['retail_price'] : []
    }
  },
  watch: {
    viewField: {
      deep: true,
      handler() {
        localStorage.setItem(this.local_storage_key, JSON.stringify(this.viewField))
      }
    },
    excludeField: {
      deep: true,
      handler() {
        this.initViewField()
      }
    }
  },
  async created() {
    this.initViewField()

    let key = this.isBdAudit ? 'audit_get_sku_list_for_batch_edit' : 'get_sku_list_for_batch_edit'
    let response =
      (await ajax.get(ADMIN_API.act[key], {
        params: {
          activity_id: +this.activity_id,
          language: getEditLang()
        }
      })) || {}

    this.skuData = response.list || []

    this.initBeforeEachInterceptor()
    window.addEventListener('beforeunload', this.onBeforeunload)
  },
  beforeDestroy() {
    this.clearInterceptorWhenLeave()
  },
  methods: {
    ...mapMutations(['setBeforeEachInterceptor', 'clearBeforeEachInterceptor']),
    initViewField() {
      let viewField = localStorage.getItem(this.local_storage_key)
      const exclude = this.excludeField
      if (viewField) {
        this.viewField = JSON.parse(viewField).filter((item) => {
          return !exclude.includes(item)
        })
      } else {
        this.viewField = getBulkViewColumnsByPlatform(exclude).map((item) => item.key)
        localStorage.setItem(this.local_storage_key, JSON.stringify(this.viewField))
      }
    },
    refreshTableData() {
      this.$refs.search.handleEmitSearch()
    },
    clearInterceptorWhenLeave() {
      this.clearBeforeEachInterceptor()
      window.removeEventListener('beforeunload', this.onBeforeunload)
    },
    initBeforeEachInterceptor() {
      this.setBeforeEachInterceptor({
        before: this.beforeUnload,
        callback: async (action) => {
          if (action === true) {
            // x close
            return true
          } else {
            // cancel or confirm
            return action === 'ok'
          }
        }
      })
    },
    // beforeEach interceptor
    beforeUnload() {
      return new Promise((resolve) => {
        if (this.canSubmitFlag) {
          this.$confirm({
            content: this.$t('act_leave_tips'),
            cancelText: this.$t('global_cancel'),
            onOk() {
              return resolve('ok')
            },
            onCancel() {
              return resolve('cancel')
            }
          })
        } else {
          resolve(true)
        }
      })
    },
    onBeforeunload(e) {
      if (this.canSubmitFlag) {
        e.preventDefault()
        e.returnValue = this.$t('act_leave_tips')
      } else {
        window.removeEventListener('beforeunload', this.onBeforeunload)
        delete e['returnValue']
      }
    },
    changeCanSubmit(v) {
      this.tableState = v
    },
    updateOpenerWindow() {
      const payload = {
        type: 'calendar',
        action: 'update'
      }
      window && window.opener && window.opener.postMessage(JSON.stringify(payload))
    },
    async successCallback(data) {
      let skuIds = data.map((item) => item.sku_id)
      let packageIds = this.skuData.reduce((acc, curr) => {
        if (skuIds.includes(curr.sku_id)) {
          return [...new Set([...acc, curr.package_id])]
        }

        return acc
      }, [])

      await Promise.all([
        ...skuIds.map((sku_id) => {
          return ajax.postBody(ADMIN_API.act.save_sku_step_status, {
            data: {
              step: 'sku_calendar',
              activity_id: this.activity_id,
              sku_id,
              language: 'all',
              status: 1
            }
          })
        }),
        ...packageIds.map((package_id) => {
          return this.$store.dispatch('updatePkgStepStatus2action', {
            step: 'price_inventory',
            activity_id: this.activity_id,
            package_id,
            language: 'all',
            status: 1
          })
        })
      ])

      klook.bus.$emit('updatePkgInfos2bus')
      klook.bus.$emit('refreshGuideStatus')
      this.updateOpenerWindow()
      this.handleReset()
    },
    checkSchedulesLength(postData = {}) {
      const sku_list = postData?.sku_list ?? []
      const isValid = sku_list.some((item) => {
        const schedules = item.schedules || []
        return schedules.length > Max_Schedule_Limit
      })
      return isValid
    },
    resetUnsavePriceReason() {
      this.unsavePriceReason = {
        unsafe_price_reason_code: undefined,
        unsafe_price_reason_note: undefined
      }
    },
    async handleSubmitBulkTable(data) {
      if (this.checkSchedulesLength(data)) {
        this.$message.error(this.$t('101042'))
        return
      }

      // 如果存在不合格价格理由，将其添加到请求中
      if (this.unsavePriceReason?.unsafe_price_reason_note) {
        data.unsafe_price_reason_note = this.unsavePriceReason.unsafe_price_reason_note
        data.unsafe_price_reason_code = this.unsavePriceReason.unsafe_price_reason_code
      }

      this.$refs.table.spinning = true

      const res = await ajax.postBody(
        ADMIN_API.act.create_or_update_multi_schedule,
        {
          data,
          noDefaultResponseInterceptor: true
        },
        {
          msgOpt: {
            isErrMsg: false
          }
        }
      )

      if (res.error?.code === UNSAFE_PRICE_CODE) {
        // 如果价格校验不通过，唤起 UnSavePriceReasonModal
        const { confirm, cancel } = await showUnsavePriceReasonModal({
          warningMessage: res.error.message
        })
        if (!cancel) {
          const { reasonCode, reasonNote } = confirm
          this.unsavePriceReason = {
            // 清空之前的理由
            unsafe_price_reason_code: reasonCode,
            unsafe_price_reason_note: reasonNote
          }
          await this.handleSubmitBulkTable(data)
          return
        }
      }

      if (res?.success) {
        const successTips = res?.result?.success_tips || ''
        this.$message.success(`${this.$t('global_modify_success')}${successTips ? ', ' + successTips : ''}`)
        this.successCallback(data.sku_list)
      } else {
        this.$message.error(res?.error?.message || this.$t('global_error'))
      }

      this.resetUnsavePriceReason()
      this.$refs.table.spinning = false
    },
    changeDifferenceField(v) {
      this.$set(this, 'viewField', [...this.viewField, ...v])
    },
    handleReset(sendGTM = false) {
      this.refreshTableData()

      if (sendGTM) {
        this.GTMHookItem('Price&Inventory|ListReset_Click')
      }
    },
    handleSave() {
      this.$refs.table.triggerToSubmit()
    },
    handleFullscreen() {
      this.visibleTooltip = false
      this.$nextTick(() => {
        this.fullscreen = !this.fullscreen
        let dom = document.querySelector('.bulkEdit-container')
        let app = document.querySelector('#app')

        if (this.fullscreen) {
          dom.classList.add('is-fullscreen')
          app.classList.add('is-fullscreen')
        } else {
          dom.classList.remove('is-fullscreen')
          app.classList.remove('is-fullscreen')
        }
      })
      this.$tracker.track('action', '#full_screen_spm')
    },
    async handleSearch(form) {
      form.schedule = form.schedule === '' ? 0 : form.schedule
      form.time_slot_status = form.time_slot_status === '' ? 0 : form.time_slot_status

      this.spinning = true

      let data = {}
      const postData = {
        ...form
      }
      if (this.isBdAudit) {
        data = await ajax.post(ADMIN_API.act.calendar_audit_get_calendar_by_filter, {
          data: postData
        })
      } else {
        data = await ajax.post(ADMIN_API.act.get_calendar_by_filter, {
          data: postData
        })
      }

      if (!data) {
        return
      }

      let list = data.sku_list || []

      this.$set(
        this,
        'shouldOperatorSkuList',
        list.reduce((acc, curr) => {
          if (getApprovalOperatorBarFlag(curr.ticket_status)) {
            return [...acc, curr.sku_id]
          }

          return acc
        }, [])
      )

      // inv_model: 1,// 1: 日历库存; 2: 无限库存; 3: 全局库存; 4: 时间段库存,
      // pricing_model: 1,// 套餐价格模型, -1: 未定义, 0: 固定价格, 1: 日历价格,
      // share_rel_ship.share_rel_ship: 1,// 0 无共享， 1 子sku, 2 主sku,
      this.showFixedPriceBar = list.some((item) => item.pricing_model === 0)
      this.showFixedInvBar = list.some((item) => item.inv_model === 3)
      this.showSubSkuBar = list.some((item) => _.get(item, 'share_rel_ship.share_rel_ship', 0) === 1)
      this.showPICTips = list.some((item) => item.inv_model === 4)

      this.refreshKey += 1
      this.$set(this, 'list', data.sku_list || [])

      this.spinning = false
    }
  }
}
</script>

<style lang="scss" scoped>
.antd-admin-layout {
  .bulkEdit-container {
    left: 56px !important;
  }
}
.bulkEdit-container {
  padding: 32px 16px;
  background-color: #fff;
  &.is-fullscreen {
    position: fixed;
    left: 0;
    top: 0;
    min-height: 100vh;
    right: 0;
    bottom: 0;
    overflow-y: scroll;
    margin-top: 50px;
  }
  .fullscreen-action {
    display: inline-block;
    margin-right: 12px;
    cursor: pointer;
    font-size: 16px;
  }
}
</style>

<style lang="scss">
#app.is-fullscreen {
  .js-is-fullscreen-hide,
  .sidebar-container {
    display: none;
  }
  .main-container {
    margin-left: 0;
  }
  .fixed-header {
    left: 0;
    width: 100vw;
  }
}

.gg-eye-off {
  position: relative;
  display: inline-flex;
  .eye-off {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

.gg-eye {
  position: relative;
  transform: scale(0.5);
  width: 24px;
  height: 18px;
  border-bottom-right-radius: 100px;
  border-bottom-left-radius: 100px;
  overflow: hidden;
  box-sizing: border-box;
  text-align: center;
}

.gg-eye::after,
.gg-eye::before {
  content: '';
  display: block;
  border-radius: 100px;
  position: absolute;
  box-sizing: border-box;
}

.gg-eye::after {
  top: 2px;
  box-shadow: inset 0 -8px 0 2px, inset 0 0 0 2px;
  width: 24px;
  height: 24px;
}

.gg-eye::before {
  width: 8px;
  height: 8px;
  border: 2px solid;
  bottom: 4px;
  left: 8px;
}

.overflow-text {
  width: 100%;
  min-width: 100px;
  display: inline-block;
  font-size: 13px;
  color: #000;
}

.icon-trash {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin-right: 8px;
  background-color: #ffab00;
  padding: 4px;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  color: #fff;
  &.disabled {
    background-color: #eee;
  }
}

span.table-cell--text {
  height: 40px;
  display: inline-flex;
  align-items: center;
  width: 100%;
}

.cell-flex-end span.table-cell--text {
  justify-content: flex-end;
}

.gg-trash {
  box-sizing: border-box;
  position: relative;
  display: block;
  transform: scale(0.5);
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  box-shadow: 0 0 0 2px, inset 4px 0 0, inset 2px 0 0;
  border-bottom-left-radius: 1px;
  border-bottom-right-radius: 1px;
  margin-top: 3px;
}
.gg-trash::after,
.gg-trash::before {
  content: '';
  display: block;
  box-sizing: border-box;
  position: absolute;
}
.gg-trash::after {
  background: currentColor;
  border-radius: 3px;
  width: 18px;
  height: 2px;
  top: -4px;
  left: -7px;
}
.gg-trash::before {
  width: 10px;
  height: 4px;
  border: 2px solid;
  border-bottom: transparent;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
  top: -7px;
  left: -3px;
}
.gg-pen {
  box-sizing: border-box;
  position: relative;
  display: block;
  transform: rotate(-45deg) scale(0.5);
  width: 14px;
  height: 4px;
  border-right: 2px solid transparent;
  box-shadow: 0 0 0 2px, inset -2px 0 0;
  border-top-right-radius: 1px;
  border-bottom-right-radius: 1px;
  margin-right: -2px;
}
.gg-pen::after,
.gg-pen::before {
  content: '';
  display: block;
  box-sizing: border-box;
  position: absolute;
}
.gg-pen::before {
  background: currentColor;
  border-left: 0;
  right: -6px;
  width: 3px;
  height: 4px;
  border-radius: 1px;
  top: 0;
}
.gg-pen::after {
  width: 8px;
  height: 7px;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-right: 7px solid;
  left: -11px;
  top: -2px;
}

.gg-undo {
  box-sizing: border-box;
  position: relative;
  display: block;
  width: 14px;
  height: 14px;
  border: 2px solid;
  border-left-color: transparent;
  border-radius: 100px;
  transform: scale(0.75);
  margin-left: 6px;
}

.gg-undo::before {
  content: '';
  display: block;
  box-sizing: border-box;
  position: absolute;
  width: 6px;
  height: 6px;
  border-top: 2px solid;
  border-left: 2px solid;
  top: -3px;
  left: -1px;
  transform: rotate(-68deg);
}
</style>
