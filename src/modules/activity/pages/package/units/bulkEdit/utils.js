import { checkAuth } from '@/plugins/authDirective'

// Useful on BulkEdit and autoExtend/index.vue
export const weeklyOpts = [
  {
    label: __('global_week_sun'),
    value: 0,
    short: __('global_week_abbr_sun')
  },
  {
    label: __('global_week_mon'),
    value: 1,
    short: __('global_week_abbr_mon')
  },
  {
    label: __('global_week_tue'),
    value: 2,
    short: __('global_week_abbr_tue')
  },
  {
    label: __('global_week_wed'),
    value: 3,
    short: __('global_week_abbr_wed')
  },
  {
    label: __('global_week_thu'),
    value: 4,
    short: __('global_week_abbr_thu')
  },
  {
    label: __('global_week_fri'),
    value: 5,
    short: __('global_week_abbr_fri')
  },
  {
    label: __('global_week_sat'),
    value: 6,
    short: __('global_week_abbr_sat')
  }
]

export const statusOpts = [
  {
    label: __('28793'),
    value: 1
  },
  {
    label: __('28794'),
    value: 2
  }
]

export const scheduleOpts = [
  {
    label: __('27811'), // If empty
    value: 2,
    tooltip: __('27812')
  },
  {
    label: __('27813'), // 'Non-empty schedule',
    value: 1,
    tooltip: __('27814')
  }
]

// approveStatus 0: none, 1: pending, 2: reject, 3: resolve
const merchantDisabledFactory = ({ role, approveStatus }) => {
  if (
    approveStatus !== 3 || // 如果不是 resolve 状态，不需要锁定逻辑，走旧逻辑
    role !== 'merchant' // 不是 merchant 端不需要考虑草稿数据的切换
  ) {
    return false
  }

  return false
}

/**
 * inv_model: 1,// 1: 日历库存; 2: 无限库存; 3: 全局库存; 4: 时间段库存,
 * pricing_model: 1,// 套餐价格模型, -1: 未定义, 0: 固定价格, 1: 日历价格,
 * share_rel_ship.share_rel_ship: 1,// 0 无共享， 1 子sku, 2 主sku,
 * approveStatus: 0: none, 1: pending, 2: reject, 3: resolve
 * @type {*[]}
 */
export const bulkColumns = [
  {
    key: 'publish_status',
    title: __('27809'),
    disabledFn({ share_rel_ship, role, approveStatus }) {
      return (
        share_rel_ship === 1 || role === 'bd_audit' || (role === 'merchant' && [1].includes(approveStatus))
      )
    }
  },
  {
    key: 'cut_off_time',
    title: __('package_cut_off_time'),
    disabledFn({ share_rel_ship, role, approveStatus }) {
      let merchantRes = merchantDisabledFactory({
        role,
        approveStatus
      })

      return (
        share_rel_ship === 1 ||
        role === 'bd_audit' ||
        (role === 'merchant' && [1].includes(approveStatus)) ||
        merchantRes
      )
    }
  },
  {
    key: 'inv_quantity',
    title: __('package_inventory'),
    disabledFn({ share_rel_ship, inv_model, role, approveStatus }) {
      return (
        share_rel_ship === 1 ||
        [2, 3].includes(inv_model) ||
        role === 'bd_audit' ||
        (role === 'merchant' && [1].includes(approveStatus))
      )
    }
  },
  {
    key: 'cost_price',
    title: __('package_price_cost'),
    disabledFn({ pricing_model, role, approveStatus, ticketStatus, merchantActHaveBeenApproved }) {
      if (ticketStatus === 'PendingApproval') {
        return true
      }

      let merchantRes = merchantDisabledFactory({
        role,
        approveStatus
      })

      return (
        pricing_model === 0 || // 0: 固定价格
        role === 'bd_audit' ||
        (role === 'merchant' && [1].includes(approveStatus)) ||
        merchantRes ||
        (merchantActHaveBeenApproved && !checkAuth('superPriceEdit')) // 商户活动或套餐被 BD approved 后，需要有 superPriceEdit 才能修改
      )
    }
  },
  {
    key: 'retail_price',
    title: __('package_price_retail'),
    disabledFn({ pricing_model, role, approveStatus }) {
      let merchantRes = merchantDisabledFactory({
        role,
        approveStatus
      })

      return (
        pricing_model === 0 ||
        role === 'bd_audit' ||
        (role === 'merchant' && [1].includes(approveStatus)) ||
        merchantRes
      )
    }
  },
  {
    key: 'selling_price',
    title: __('package_price_selling'),
    hidden: ['merchant'],
    disabledFn({ pricing_model, role }) {
      return (
        pricing_model !== 1 || role === 'merchant' // 1: 日历价格
      )
    }
  },
  {
    key: 'take_rate',
    title: __('act_list_take_rate'),
    hidden: ['merchant'],
    disabledFn({ pricing_model, role }) {
      return (
        pricing_model !== 1 || role === 'merchant' // 1: 日历价格
      )
    }
  },
  {
    key: 'special_selling_price',
    title: __('28682'),
    hidden: ['merchant'],
    disabledFn({ pricing_model, role }) {
      return (
        pricing_model !== 1 || role === 'merchant' // 1: 日历价格
      )
    }
  }
]

export const getBulkViewColumnsByPlatform = (exclude = []) => {
  let role = klook.getPlatformRoleKey()

  return bulkColumns.reduce((acc, curr) => {
    if (exclude.includes(curr.key)) {
      return acc
    }
    if (Array.isArray(curr.hidden) && curr.hidden.includes(role)) {
      return acc
    }

    return [...acc, curr]
  }, [])
}

export const priceFieldList = [
  'cost_price',
  'retail_price',
  'selling_price',
  'take_rate',
  'special_selling_price'
]

export const isOnPriceField = (field) => {
  return priceFieldList.some((item) => field.includes(item))
}

export const publishStatusOpts = [
  {
    label: __('28793'),
    value: 1
  },
  {
    label: __('196165'),
    value: 2
  },
  {
    label: __('28794'),
    value: 0
  }
]

export const isStatusPublished = (value) => Number(value) === 1
export const isStatusPresale = (value) => Number(value) === 2
export const isStatusDeactive = (value) => Number(value) === 0

export const cutOffTimeOpts = Array(32)
  .fill()
  .map((_, i) => ({
    label: i,
    value: i
  }))

// bulk edit

// uuid e.g. '_{index}'
export const parseRowIndexByUUID = (uuid) => {
  return +uuid.slice(1)
}

export const getCellDataByPosition = ({ sku_id, field, uuid, source }) => {
  if (!(sku_id && field && uuid)) {
    throw new TypeError('Sku id or field or uuid')
  }

  let index = parseRowIndexByUUID(uuid)
  let data = isOnPriceField(field) ? source[index][sku_id].price : source[index][sku_id]

  return {
    data,
    value: data[field],
    skuData: source[index][sku_id]
  }
}

export const isValidTableCellData = (value, field) => {
  let commonValid = !(
    value === null ||
    value === '' ||
    value === undefined ||
    value === -Infinity ||
    'NaN' === String(value)
  )

  let customValid = true
  if (['cost_price', 'retail_price', 'selling_price', 'inv_quantity'].includes(field)) {
    customValid = value >= 0
  }

  return commonValid && customValid
}

export const isEmptyValue = ({ value, field }) => {
  if (value === null) return true

  if (isOnPriceField(field)) {
    if (field === 'special_selling_price') {
      return value.length === 0
    }
    return value === ''
  } else if (field === 'inv_quantity') {
    return -1 === value
  } else {
    return value === ''
  }
}

// According to the current sku data and field, determine whether there is a corresponding fixed value
// @returns {Object} - fixed info
// @fixedData: external object data, @fixedVal: current field fixed value, @isValid: is valid value
export const getFixedValueByRecord = ({ currentSkuData, field }) => {
  let { auto_fill_schedule_item } = currentSkuData || {}
  let fixedVal = null
  let fixedData = null

  if (auto_fill_schedule_item) {
    if (isOnPriceField(field)) {
      fixedData = auto_fill_schedule_item.fix_price
      if (fixedData) {
        fixedVal = auto_fill_schedule_item.fix_price[field]
      }
    } else if (field === 'inv_quantity') {
      fixedData = auto_fill_schedule_item
      if (auto_fill_schedule_item['inv_quantity'] !== -1) {
        fixedVal = auto_fill_schedule_item['inv_quantity']
      }
    }
  }

  let isValid = !isEmptyValue({ value: fixedVal, field }) && isValidTableCellData(fixedVal, field)

  return {
    fixedData,
    fixedVal,
    isValid
  }
}

// @returns {Object} - update object e.g. { [${updateField}]: ${updateValue} }
export const setFixedValueByRecord = ({ currentSkuData, field }) => {
  let { fixedData, fixedVal, isValid } = getFixedValueByRecord({
    currentSkuData,
    field
  })

  let data = null

  if (isValid) {
    data = {}

    if (isOnPriceField(field)) {
      for (let updateField of priceFieldList) {
        let value = fixedData[updateField]
        if (isValidTableCellData(value, updateField)) {
          data[updateField] = _.cloneDeep(value)
        }
      }
    } else {
      if (isValidTableCellData(fixedVal, 'inv_quantity')) {
        data['inv_quantity'] = fixedVal
      }
    }
  }

  return data
}

/*
  关于take rate计算&反写的场景
  成本价为0时
  1.不管售卖价为多少，take rate都展示为100%
  2.除了修改成本价之外，其他操作都不触发take rate&售卖价之间的联动（即修改售卖价其take rate也展示为100%；无法修改take rate）
  3.修改成本价之后，根据修改后的成本&售卖价计算take rate，ps：只有在修改take rate的时候才会反写售卖价，改成本价或售卖价的时候都重算take rate
  4.成本价为0，售卖价为0，take rate也为0

  售卖价为0但成本价不为0时
  1.不管成本价为多少，take rate都展示为undefined
  2.修改成本价，take rate依旧展示为undefined，不反写售卖价
  3.修改售卖价不为0之后，根据修改后的售卖价与对应成本价重算take rate（考虑此时成本价为0的场景）

  @returns {Object} - 如果联动的计算结果是有效的，则 updateField 为因联动而更新的字段名，否则为空

  ### 这部分的代码若更新了，需要 ctrl + v 到 worker 里
 */
export const priceLinkageUpdate = ({
  cost_price = '',
  selling_price = '',
  take_rate = '',
  toFixed = 2,
  cost_currency = '',
  selling_currency = '',
  originField,
  exchange_rate = []
} = {}) => {
  let result = {
    cost_price,
    selling_price,
    take_rate,
    updateField: ''
  }
  // linkage need cost
  if (cost_price === '' || cost_price === undefined || cost_price === null) {
    return result
  }
  // 特殊场景
  if (['selling_price', 'cost_price'].includes(originField)) {
    if (cost_price === 0) {
      result.updateField = 'take_rate'
      result.take_rate = 100

      return result
    } else {
      if (selling_price === 0) {
        result.updateField = 'take_rate'
        result.take_rate = undefined

        return result
      }
    }
  }

  // to_value: cost to selling rate
  // from_value: selling to cost rate
  let { to_value, from_value } = _.find(exchange_rate, {
    from_currency: cost_currency,
    to_currency: selling_currency
  })

  if (originField === 'take_rate') {
    if (cost_price !== '') {
      // take_rate + cost_price => selling_price
      result.selling_price = ((cost_price * to_value * 100) / (100 - take_rate)).toFixed(toFixed)
      result.updateField = 'selling_price'
    } else if (selling_price !== '') {
      // take_rate + selling_price => cost_price
      result.cost_price = ((selling_price * from_value * (100 - take_rate)) / 100).toFixed(toFixed)
      result.updateField = 'cost_price'
    }
  } else {
    if (selling_price !== '' && cost_price !== '') {
      // selling_price + cost_price => take_rate
      result.take_rate = +(((selling_price - cost_price * to_value) / selling_price) * 100).toFixed(toFixed)
      result.updateField = 'take_rate'
    } else if (take_rate !== '' && selling_price !== '') {
      // selling_price + take_rate => cost_price
      result.cost_price = ((selling_price * from_value * (100 - take_rate)) / 100).toFixed(toFixed)
      result.updateField = 'cost_price'
    } else if (take_rate !== '' && cost_price !== '') {
      // cost_price + take_rate => selling_price
      result.selling_price = ((cost_price * to_value * 100) / (100 - take_rate)).toFixed(toFixed)
      result.updateField = 'selling_price'
    }
  }

  let value = result[result.updateField]
  if (result.updateField) {
    // denominator === 0， e.g. (100 - take_rate) === 0
    if (typeof value === 'string') {
      if (value.indexOf('Infinity') !== -1) {
        result[result.updateField] = 0
      } else {
        result[result.updateField] = +result[result.updateField]
      }
    } else if (isNaN(value)) {
      result[result.updateField] = ''
    }
  }

  return result
}

// e.g. '0:0' -> '00:00'
export const fmtTimeBitFill = (value) => {
  value = String(value)

  return value.length === 1 ? '0' + value : value
}

import moment from 'moment'
export const getDateByTimestamp = (timestamp) => {
  timestamp *= 1000

  let days = Math.floor(moment.duration(timestamp).asDays())
  let remain = timestamp - days * 24 * 60 * 60 * 1000
  let hour = Math.floor(moment.duration(remain).asHours())
  remain = remain - hour * 60 * 60 * 1000
  let minute = Math.floor(moment.duration(remain).asMinutes())

  hour = fmtTimeBitFill(hour)
  minute = fmtTimeBitFill(minute)

  return {
    days,
    time: `${hour}:${minute}`
  }
}

// second
export const getTimestampByDate = ({ days, time }) => {
  let [hour, minute] = time.split(':')

  return (days * 24 * 60 + hour * 60 + +minute) * 60
}

// for a-input-number
export const getPriceFieldBind = (field) => {
  return {
    precision: 'inv_quantity' === field ? 0 : 2,
    step: 'inv_quantity' === field ? '1' : '0.01',
    min: 'take_rate' === field ? -9999 : 0,
    max: 'inv_quantity' === field ? 999999 : 9999999999,
    formatter: (value) => {
      if (value) {
        return 'take_rate' === field ? `${value}%` : value
      }

      return value
    },
    parser: (value) => {
      if (value) {
        if ('take_rate' === field) {
          return value.replace('%', '')
        }
      }

      if (!isNaN(value)) {
        value = String(value).replace(/\.([0-9]{0,2}).*$/g, ($1, $2) => '.' + $2)
      }

      return value
    }
  }
}

// @param {number} invModel - 1: 日历库存; 2: 无限库存; 3: 全局库存; 4: 时间段库存
export const getTableCellText = ({ data, field, value, invModel }) => {
  switch (field) {
    case 'publish_status': {
      const target = publishStatusOpts.find((item) => item.value === Number(value))
      return target ? target.label : __('28794')
    }
    case 'cut_off_time':
      return `${data.days} ${__('package_day_s')} ${data.time}`

    case 'inv_quantity':
      return -1 === value ? '' : invModel === 2 ? __('27793') : value

    case 'take_rate':
    case 'current_take_rate':
      return value ? value + '%' : value

    default:
      return value
  }
}

export const fmtI18nKey = (str, data = {}) =>
  str.replace(/\{([^}]+)\}/g, ($1, $2) => (data[$2] ? data[$2] : $1))

// end bulk edit

export const getComboDisabled = (key, sku = {}) => {
  const { combo_selling_price_type, is_combo } = sku
  if (!is_combo) {
    return false
  }
  if (key === 'publish_status') {
    return false
  }
  if (key === 'selling_price' && combo_selling_price_type === 3) {
    return false
  }
  return true
}
