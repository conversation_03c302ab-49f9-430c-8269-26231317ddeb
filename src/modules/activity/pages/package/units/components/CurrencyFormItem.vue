<template>
  <div style="display: flex">
    <a-form-model-item
      prop="merchant_currency"
      :rules="{
        required: true,
        message: this.$t('global_please_select'),
        trigger: 'change'
      }"
      :label="$t('package_merchant_currency')"
      style="width: 300px; margin-right: 24px;"
    >
      <a-select v-model="form.merchant_currency" disabled style="width: 100%">
        <a-select-option v-for="currency in SUPPORT_CURRENCY_SYMBO" :key="currency[0]" :value="currency[0]">
          {{ currency[0] }}
        </a-select-option>
      </a-select>
    </a-form-model-item>
    <a-form-model-item
      :label="$t('package_retail_selling_currency')"
      :rules="{
        required: true,
        message: this.$t('global_please_select'),
        trigger: 'change'
      }"
      prop="selling_currency"
      style="width: 300px;"
    >
      <a-tooltip placement="top" arrow-point-at-center>
        <template slot="title">
          {{ $t('29439') }}
        </template>
        <a-select
          v-model="form.selling_currency"
          :disabled="disabled || disabledSellingCurrency"
          show-search
          :filter-option="filterOption"
          placeholder="Select or Input"
          style="width: 100%"
        >
          <a-select-option v-for="currency in SUPPORT_CURRENCY_SYMBO" :key="currency[0]" :value="currency[0]">
            {{ currency[0] }}
          </a-select-option>
        </a-select>
      </a-tooltip>
    </a-form-model-item>
  </div>
</template>

<script>
import { SUPPORT_CURRENCY_SYMBO } from '@activity/pages/package/package_const.js'

export default {
  name: 'CurrencyFormItem',
  props: {
    form: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    disabledSellingCurrency: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      SUPPORT_CURRENCY_SYMBO
    }
  },
  methods: {
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }
  }
}
</script>

<style lang="scss" scoped></style>
