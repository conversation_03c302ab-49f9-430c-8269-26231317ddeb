<template>
  <a-tooltip placement="top" arrow-point-at-center>
    <template slot="title">
      {{ $t('package_cut_off_time_hint') }}
    </template>
    <span>
      <a-select
        v-model="form[dayField]"
        placeholder="dd"
        show-search
        :disabled="disabled"
        :dropdownStyle="{ minWidth: '166px' }"
        :style="dayStyle"
        @blur="$emit('blur')"
      >
        <a-select-option
          v-for="cutoffOption in cutoffDayList"
          :key="cutoffOption.value"
          :value="cutoffOption.value"
        >
          {{ cutoffOption.text }}
        </a-select-option>
      </a-select>
      <a-time-picker
        :value="getTimeslot(form[timeField])"
        :disabled="disabled"
        :style="timeStyle"
        :default-open-value="defaultTimePickerValue"
        format="HH:mm"
        placeholder="hh:mm"
        @blur="$emit('blur')"
        @change="(time, timeString) => handleChange(timeField, timeString)"
      />
    </span>
  </a-tooltip>
</template>

<script>
import moment from 'moment'
import { cutoffDayList } from '@activity/pages/package/package_const.js'
import { defaultTimePickerValue } from '@activity/pages/package/units/utils'

export default {
  name: 'CutoffForm',
  props: {
    form: {
      type: Object,
      required: true
    },
    dayField: {
      type: String,
      default: 'cutoffDay'
    },
    timeField: {
      type: String,
      default: 'cutoffTime'
    },
    dayStyle: {
      type: String,
      default: ''
    },
    timeStyle: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      cutoffDayList,
      defaultTimePickerValue
    }
  },
  methods: {
    getTimeslot(str) {
      return str ? moment(str, 'HH:mm') : ''
    },
    handleChange(field, value) {
      this.form[field] = value
      this.$emit('blur')
    }
  }
}
</script>

<style lang="scss" scoped></style>
