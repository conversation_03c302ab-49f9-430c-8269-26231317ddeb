<template>
  <a-dropdown v-model="visible">
    <a-menu slot="overlay" v-bind="$attrs" @click="handleMenus">
      <a-menu-item v-for="menu of menus" :key="menu[keyField]">
        <div class="menu-item">
          {{ menu[labelField] }}
          <ShimAntdTooltip v-if="menu.description" :title="menu.description || ''">
            <a-icon type="info-circle" />
          </ShimAntdTooltip>
        </div>
      </a-menu-item>
    </a-menu>

    <slot name="default" :visible="visible"></slot>
  </a-dropdown>
</template>

<script>
import ShimAntdTooltip from '@activity/components/shimAntdTooltip'

export default {
  name: 'DropdownMenus',
  components: {
    ShimAntdTooltip
  },
  props: {
    menus: {
      type: [Array, Object],
      required: true
    },
    keyField: {
      type: String,
      default: 'key'
    },
    labelField: {
      type: String,
      default: 'label'
    }
  },
  data() {
    return {
      visible: false
    }
  },
  watch: {
    visible() {
      this.$emit('changeVisible', this.visible)
    }
  },
  methods: {
    handleMenus(curr) {
      this.$emit('change', curr.key)
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
