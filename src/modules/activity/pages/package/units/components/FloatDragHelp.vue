<template>
  <div
    id="js-float-help"
    class="float-help"
    :style="{ ...styleObj, opacity: moving ? 0.5 : 1 }"
    draggable="true"
    :data-spm-module="getHelpSpm"
    data-spm-virtual-item="__virtual"
    @dragstart="handleDragstart"
  >
    <div class="float-help-wrap" @click="handleOpenHelp">
      <a-popover v-if="!$root.isMerchant" placement="top">
        <template slot="content">
          <div style="width: 180px;" v-html="helpText" />
        </template>
        <svg-icon class="float-help-icon" icon-name="question-circle" />
      </a-popover>
      <span class="float-help-text">
        {{ $t('117148') }}
      </span>
    </div>
  </div>
</template>

<script>
import { FLOAT_HELP_URL_MERCHANT, FLOAT_HELP_URL } from '@activity/pages/package/package_const.js'

export const getPlatformGuideMaps = (isMerchant) => {
  const merchantGuideMaps = [
    {
      path: '/act/activity/basic/',
      link:
        'https://docs.google.com/presentation/d/*****************************-05FtpDMb1h1JZg/edit#slide=id.g11bff86b949_4_4241'
    },
    {
      path: '/act/activity/detail/',
      link:
        'https://docs.google.com/presentation/d/*****************************-05FtpDMb1h1JZg/edit#slide=id.g12c40ee3da3_0_0'
    },
    {
      path: '/act/package/info/',
      link:
        'https://docs.google.com/presentation/d/*****************************-05FtpDMb1h1JZg/edit#slide=id.g11bff86b949_4_4275'
    },
    {
      path: '/act/package/detail/',
      link:
        'https://docs.google.com/presentation/d/*****************************-05FtpDMb1h1JZg/edit#slide=id.g20f3485e2c8_1_0'
    },
    {
      path: '/act/package/itinerary/',
      link:
        'https://docs.google.com/presentation/d/*****************************-05FtpDMb1h1JZg/edit#slide=id.g19567002655_0_1'
    },
    {
      path: '/act/package/extra/',
      link:
        'https://docs.google.com/presentation/d/*****************************-05FtpDMb1h1JZg/edit#slide=id.g129ee4e87b4_0_675'
    },
    {
      path: '/act/package/unit/',
      link:
        'https://docs.google.com/presentation/d/*****************************-05FtpDMb1h1JZg/edit#slide=id.g129ee4e87b4_0_305'
    }
  ]
  if (isMerchant) {
    return _.cloneDeep(merchantGuideMaps)
  }
}
export default {
  name: 'FloatDragHelp',
  props: {
    guideConfig: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      moving: false,

      styleObj: {
        right: '20px',
        bottom: '56px'
      }
    }
  },
  computed: {
    calcHelpUrl() {
      const { guideConfig } = this
      if (guideConfig?.link) {
        return guideConfig.link
      }
      return this.$root.isMerchant ? FLOAT_HELP_URL_MERCHANT : FLOAT_HELP_URL
    },
    helpText() {
      return this.$t('28232').replace('MULTILANG_URL_PLACEHOLDER', this.calcHelpUrl)
    },
    getHelpSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `Help?oid=${oid}&trg=manual`
    }
  },
  created() {
    const floatHelpPosition = window.localStorage.getItem('float_help_position')

    if (floatHelpPosition) {
      this.parseFloatHelpPosition(floatHelpPosition)
    }
  },
  mounted() {
    this.resetFloatHelpPosition()

    if (document) {
      document.addEventListener('dragenter', this.handleDragenter)
      document.addEventListener('dragover', this.handleDragover)
      document.addEventListener('dragleave', this.handleDragleave)
      document.addEventListener('drop', this.handleDrop)
    }
  },
  beforeDestroy() {
    if (document) {
      document.removeEventListener('dragenter', this.handleDragenter)
      document.removeEventListener('dragover', this.handleDragover)
      document.removeEventListener('dragleave', this.handleDragleave)
      document.removeEventListener('drop', this.handleDrop)
    }
  },
  methods: {
    handleDragstart(e) {
      this.moving = true
      const dragDom = document.querySelector('#js-float-help')
      this.diffX = e.clientX - dragDom.offsetLeft
      this.diffY = e.clientY - dragDom.offsetTop
      // console.log('handleDragStart', e)
    },
    handleDragover(e) {
      e.preventDefault()
      // console.log('handleDragOver', e)
    },
    handleDragenter(e) {
      e.preventDefault()
      // console.log('handleDragenter', e);
    },
    handleDragleave(e) {
      e.preventDefault()
      // console.log('handleDragleave', e);
    },
    handleDrop(e) {
      if (this.moving) {
        const { diffX, diffY } = this

        let left = e.clientX - diffX
        let top = e.clientY - diffY

        // 控制拖拽物体的范围只能在浏览器视窗内
        if (left < 0) {
          left = 0
        }

        if (top < 0) {
          top = 0
        }

        this.styleObj = {
          left: `${left}px`,
          top: `${top}px`
        }

        this.moving = false
        this.saveToLocalStorage()
      }
    },
    saveToLocalStorage() {
      setTimeout(() => {
        const { left, top } = this.styleObj || {}
        if (left && top) {
          window.localStorage.setItem('float_help_position', JSON.stringify({ left, top }))
        }
      }, 0)
    },
    parseFloatHelpPosition(position) {
      try {
        const { left, top } = JSON.parse(position) || {}

        if (left && top) {
          this.styleObj = { left, top }
        }
      } catch (error) {
        console.log('floatHelpPosition', error)
      }
    },
    resetFloatHelpPosition() {
      // 判断是否在窗口外，如果是，则重置位置
      const windowWidth = window.innderWidth || document.documentElement.clientWidth
      const windowHeight = window.innerHeight || document.documentElement.clientHeight
      const { left, top } = this.styleObj

      if (parseInt(left) + 20 > windowWidth || parseInt(top) + 20 > windowHeight) {
        this.styleObj = {
          right: '0',
          bottom: '43px'
        }
      }
    },
    handleOpenHelp() {
      window.open(this.calcHelpUrl, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.float-help {
  position: fixed;
  height: 40px;
  width: fit-content;
  padding: 10px 25px;

  border-radius: 20px;
  background: #0091ff;
  cursor: grab;
  z-index: 1000;

  // 硬件加速
  transform: translateZ(0);

  &-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  &-icon {
    margin-right: 6px;
    color: #fff;
    font-size: 20px;
  }

  &-text {
    color: #fff;
    font-weight: 600;
    font-size: 14px;
    line-height: 18px;
  }
}
</style>
