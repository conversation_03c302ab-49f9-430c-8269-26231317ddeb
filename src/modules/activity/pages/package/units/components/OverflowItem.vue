<template>
  <div class="overflow-item">
    <OverflowTextTooltip class="overflow-item-title">
      <span style="cursor: text;" @click.stop>
        {{ title }}
        <slot v-if="!title" name="title" />
      </span>
    </OverflowTextTooltip>
    <OverflowTextTooltip class="overflow-item-text">
      <span style="cursor: text;" @click.stop>
        {{ content }}
        <slot v-if="!content" name="content" />
      </span>
    </OverflowTextTooltip>
  </div>
</template>

<script>
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'

export default {
  name: 'OverflowItem',
  props: {
    title: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    }
  },
  components: {
    OverflowTextTooltip
  }
}
</script>

<style lang="scss" scoped>
.overflow-item {
  &-title {
    margin: 4px 0;
    display: block;
    font-size: 10px;
    line-height: 15px;
    font-weight: normal;
    color: rgba(0, 0, 0, 0.45);

    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &-text {
    margin: 0;
    display: block;
    font-size: 12px;
    line-height: 18px;
    color: rgba(0, 0, 0, 0.85);

    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
