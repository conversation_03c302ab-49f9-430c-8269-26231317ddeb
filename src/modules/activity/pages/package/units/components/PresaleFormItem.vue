<template>
  <div class="presale-form">
    <div class="presale-form-title">
      {{ $t('196161') }}
    </div>
    <a-form-model-item prop="presale_allowed" :label="$t('196177')">
      <a-switch v-model="form.presale_allowed" :disabled="disabled || presaleDisabled" />
    </a-form-model-item>
    <template v-if="form.presale_allowed">
      <a-form-model-item
        prop="presale_stock_out_date_type"
        :rules="{
          required: true,
          validator: stockDateValidator
        }"
        :label="$t('196162')"
      >
        <PresaleStockOut :form="form" :disabled="disabled || presaleDisabled" />
      </a-form-model-item>
      <div style="display: flex">
        <a-form-model-item
          prop="presale_exposure_start_time"
          :label="$t('196169')"
          style="width: 260px; margin-right: 24px"
        >
          <a-date-picker
            v-model="form.presale_exposure_start_time"
            format="YYYY-MM-DD HH:mm:ss"
            valueFormat="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledStartTime"
            :placeholder="$t('196171')"
            :show-time="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"
            style="width: 100%"
          />
        </a-form-model-item>
        <a-form-model-item prop="presale_exposure_end_time" :label="$t('196170')" style="width: 260px">
          <a-date-picker
            v-model="form.presale_exposure_end_time"
            format="YYYY-MM-DD HH:mm:ss"
            valueFormat="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledEndTime"
            :placeholder="$t('196172')"
            :show-time="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"
            style="width: 100%"
          />
        </a-form-model-item>
      </div>
    </template>
  </div>
</template>

<script>
import moment from 'moment'
import { SUPPORT_CURRENCY_SYMBO } from '@activity/pages/package/package_const.js'
import PresaleStockOut from '@activity/pages/package/units/components/PresaleStockOut.vue'

// presale_config: {
//   presale_allowed: false,
//   presale_disabled: false,
//   exposure_start_time: '',
//   exposure_end_time: '',
//   stock_out_date_type: 1, // 1,2,3
//   stock_out_date_value: '', // '20'、'20-30'、'2024-03-01'
// }

export default {
  name: 'PresaleFormItem',
  components: {
    PresaleStockOut
  },
  props: {
    form: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      SUPPORT_CURRENCY_SYMBO
    }
  },
  computed: {
    presaleDisabled() {
      return !!this.form?.presale_disabled
    },
    stockOutDateWithinText() {
      return this.$t('196164').split('{x}')
    },
    stockOutDateBetweenText() {
      return this.$t('196163').split('{x}-{y}')
    },
    stockOutDateOnText() {
      return this.$t('200766').replace('YYYY-MM-DD', '')
    }
  },
  methods: {
    moment,
    stockDateValidator(_rule, type, cb) {
      const value = this.form?.presale_stock_out_date_value
      if (!type || !value) {
        cb(new Error('Please select'))
      } else {
        cb()
      }
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    disabledStartTime(current) {
      const { presale_exposure_end_time } = this.form
      if (presale_exposure_end_time) {
        return current.isAfter(presale_exposure_end_time, 'second')
      }
      return false
    },
    disabledEndTime(current) {
      const { presale_exposure_start_time } = this.form
      if (presale_exposure_start_time) {
        return current.isBefore(moment(presale_exposure_start_time), 'second')
      }
      return current.isBefore(moment(), 'day')
    }
  }
}
</script>

<style lang="scss" scoped>
.presale-form {
  &-title {
    margin-bottom: 8px;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);

    &:before {
      color: #f5222d;
      content: '*';
      margin-right: 4px;
      font-family: SimSun, sans-serif;
      font-size: 14px;
      line-height: 1;
      display: inline-block;
    }
  }
}
</style>
