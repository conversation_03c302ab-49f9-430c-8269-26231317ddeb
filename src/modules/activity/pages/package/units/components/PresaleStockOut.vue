<template>
  <a-radio-group v-model="type" :disabled="disabled" @change="handleTypeChange">
    <a-row>
      <a-radio :value="STOCK_OUT_DATE_MODE.Within">
        <span v-if="type === STOCK_OUT_DATE_MODE.Within">
          {{ stockOutDateWithinText[0] }}
          <a-select
            v-model="stockOutDateWithin"
            :disabled="disabled"
            style="width: 66px"
            @change="handleValueChange"
          >
            <a-select-option v-for="i in 100" :key="i" :value="i">
              {{ i }}
            </a-select-option>
          </a-select>
          {{ stockOutDateWithinText[1] }}
        </span>
        <span v-else>{{ $t('196164') }}</span>
      </a-radio>
    </a-row>
    <a-row style="margin-top: 8px">
      <a-radio :value="STOCK_OUT_DATE_MODE.Between">
        <span v-if="type === STOCK_OUT_DATE_MODE.Between">
          {{ stockOutDateBetweenText[0] }}
          <a-select
            v-model="stockOutDateFrom"
            :disabled="disabled"
            style="width: 66px"
            @change="handleValueChange"
          >
            <a-select-option
              v-for="i in 100"
              :key="i"
              :value="i"
              :disabled="stockOutDateTo && i >= stockOutDateTo"
            >
              {{ i }}
            </a-select-option>
          </a-select>
          -
          <a-select
            v-model="stockOutDateTo"
            :disabled="disabled"
            style="width: 66px"
            @change="handleValueChange"
          >
            <a-select-option
              v-for="i in 100"
              :key="i"
              :value="i"
              :disabled="stockOutDateFrom && i <= stockOutDateFrom"
            >
              {{ i }}
            </a-select-option>
          </a-select>
          {{ stockOutDateBetweenText[1] }}
        </span>
        <span v-else>{{ $t('196163') }}</span>
      </a-radio>
    </a-row>
    <a-row style="margin-top: 8px">
      <a-radio :value="STOCK_OUT_DATE_MODE.On">
        <span v-if="type === STOCK_OUT_DATE_MODE.On">
          {{ stockOutDateOnText }}
          <a-date-picker
            v-model="stockOutDateOn"
            format="YYYY-MM-DD"
            valueFormat="YYYY-MM-DD"
            :disabled-date="disabledStockOutDate"
            placeholder="YYYY-MM-DD"
            style="width: 160px"
            @change="handleValueChange"
          />
        </span>
        <span v-else>{{ $t('200766') }}</span>
      </a-radio>
    </a-row>
  </a-radio-group>
</template>

<script>
const STOCK_OUT_DATE_MODE = {
  Within: 1,
  Between: 2,
  On: 3
}

export default {
  name: 'PresaleStockOut',
  props: {
    form: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      STOCK_OUT_DATE_MODE,
      stockOutDateWithin: null,
      stockOutDateFrom: null,
      stockOutDateTo: null,
      stockOutDateOn: null
    }
  },
  computed: {
    type: {
      get() {
        return this.form?.presale_stock_out_date_type
      },
      set(val) {
        this.$set(this.form, 'presale_stock_out_date_type', val)
      }
    },
    value: {
      get() {
        return this.form?.presale_stock_out_date_value
      },
      set(val) {
        this.$set(this.form, 'presale_stock_out_date_value', val)
      }
    },
    stockOutDateWithinText() {
      return this.$t('196164').split('{x}')
    },
    stockOutDateBetweenText() {
      return this.$t('196163').split('{x}-{y}')
    },
    stockOutDateOnText() {
      return this.$t('200766').replace('{YYYY-MM-DD}', '').replace('YYYY-MM-DD', '')
    }
  },
  mounted() {
    const { type, value } = this
    switch (Number(type || 0)) {
      case STOCK_OUT_DATE_MODE.Within:
        this.stockOutDateWithin = value ? Number(value) : null
        break
      case STOCK_OUT_DATE_MODE.Between:
        if (value) {
          const [from, to] = value.split('-')
          this.stockOutDateFrom = Number(from)
          this.stockOutDateTo = Number(to)
        }
        break
      case STOCK_OUT_DATE_MODE.On:
        this.stockOutDateOn = value ? value : null
        break
      default:
        break
    }
  },
  methods: {
    disabledStockOutDate(current) {
      return current.isBefore(moment(), 'day')
    },
    handleTypeChange() {
      this.handleEmit()
    },
    handleValueChange() {
      this.handleEmit()
    },
    handleEmit() {
      const { type, stockOutDateWithin, stockOutDateFrom, stockOutDateTo, stockOutDateOn } = this
      switch (Number(type || 0)) {
        case STOCK_OUT_DATE_MODE.Within:
          this.value = stockOutDateWithin ? String(stockOutDateWithin) : ''
          break
        case STOCK_OUT_DATE_MODE.Between:
          this.value = stockOutDateFrom && stockOutDateTo ? `${stockOutDateFrom}-${stockOutDateTo}` : ''
          break
        case STOCK_OUT_DATE_MODE.On:
          this.value = stockOutDateOn ? String(stockOutDateOn) : ''
          break
        default:
          break
      }

      this.$emit('change')
    }
  }
}
</script>

<style lang="scss" scoped></style>
