<template>
  <a-modal
    class="price-reason-modal"
    title="Update Price Reason"
    destroyOnClose
    :visible="visible"
    :width="500"
    :ok-text="$t('global_confirm')"
    :cancel-text="$t('global_button_cancel')"
    @ok="handleConfirm"
    @cancel="handleClose"
  >
    <div>
      This is a modal popover to ensure that the information is clearly communicated.So please simply write
      down the message you want to convey to the user.
    </div>
    <a-form-model
      ref="form"
      layout="horizontal"
      :labelCol="{ span: 10 }"
      :wrapperCol="{ span: 14 }"
      :model="form"
      :rules="rules"
    >
      <a-form-model-item :label="$t('reason_for_price_changing')" prop="reason">
        <a-select v-model="form.reason" @change="handleReasonSelect">
          <a-select-option v-for="reason in priceChangeReasonList" :key="reason.value" :value="reason.value">
            <OverflowTextTooltip style="display: block; width: 100%;">
              {{ reason.text }}
            </OverflowTextTooltip>
            <!-- {{ reason.text }} -->
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item v-if="form.reason === 0" label=" " prop="reasonText" :colon="false">
        <a-textarea
          id="js-reason-text"
          v-model="form.reasonText"
          :placeholder="$t('global_please_input')"
          :rows="4"
          :max-length="100"
        />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
import { priceChangeReasonList } from '@activity/pages/package/package_const.js'
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'

export default {
  name: 'PriceReasonModal',
  components: {
    OverflowTextTooltip
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      priceChangeReasonList,

      form: {
        reason: undefined,
        reasonText: undefined
      },
      rules: {
        reason: [
          {
            required: true,
            message: __('global_please_select'),
            trigger: 'blur'
          }
        ],
        reasonText: [
          {
            required: true,
            message: __('global_please_input'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    async handleConfirm() {
      const valid = await this.$refs.form.validate().catch(() => false)

      if (valid) {
        let note = this.form.reasonText

        if (this.form.reason !== 0) {
          const target = this.priceChangeReasonList.find((item) => item.value === this.form.reason)

          note = target ? target.text : ''
        }

        this.$emit('confirm', note)
        this.handleClose()
      }
    },
    handleReasonSelect() {
      if (this.form.reason === 0) {
        this.$nextTick(() => {
          const reasonTextDom = document.querySelector('#js-reason-text')
          reasonTextDom && reasonTextDom.scrollIntoView()
        })
      }
    },
    handleClose() {
      this.$emit('update:visible', false)
      this.form = this.$options.data().form
    }
  }
}
</script>

<style lang="scss" scoped></style>
