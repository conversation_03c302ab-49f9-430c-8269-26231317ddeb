<template>
  <component :is="component" :placement="placement" arrow-point-at-center :overlay-style="overlayStyle">
    <template slot="title">
      <div class="text-wrap" v-html="message"></div>
      <slot></slot>
    </template>
    <a-icon type="question-circle" theme="filled" style="margin-left: 4px; color: rgba(0, 0, 0, 0.25);" />
  </component>
</template>

<script>
export default {
  name: 'QuestionIcon',
  props: {
    message: {
      type: String,
      required: true
    },
    placement: {
      type: String,
      default: 'top'
    },
    overlayStyle: {
      type: Object,
      default: () => {}
    },
    component: {
      type: String,
      default: 'a-tooltip'
    }
  }
}
</script>

<style lang="scss" scoped>
.text-wrap {
  text-align: left;
}
</style>
