<template>
  <a-drawer
    class="sku-model-drawer"
    :width="1000"
    :visible="visible"
    :header-style="drawerHeaderStyle"
    :body-style="drawerBodyStyle"
    placement="right"
    @close="close"
  >
    <div slot="title">
      <span>{{ $t('27723') }}</span>
      <a-button v-if="isSimOrWifi" class="copy-button" @click.stop="handleShowCopy">
        {{ $t('104033') }}
      </a-button>
    </div>
    <a-spin :spinning="loading" :data-spm-page="getSpm">
      <SkuModelForm
        v-if="form"
        ref="form"
        :frozen="frozen"
        :form="form"
        :frozen-week="frozenWeek"
        :frozen-max-row="frozenMaxRow"
        :is-pass="isPass"
        @toggleModel="handleToggleModel"
      />
    </a-spin>
    <div class="sku-model-drawer-footer">
      <a-button style="margin-right: 8px" @click="close">
        {{ $t('global_button_cancel') }}
      </a-button>
      <a-button type="primary" :disabled="!canEdit && !inventoryCanEdit" @click="handleSave">
        {{ $t('global_button_save') }}
      </a-button>
    </div>

    <a-modal
      width="600px"
      :title="$t('30295')"
      :visible.sync="modal.visible"
      :footer="null"
      @cancel="handleCancel"
    >
      <p v-html="modal.tips"></p>

      <a-table
        style="margin: 12px 0"
        :bordered="true"
        :pagination="false"
        :columns="modal.columns"
        :data-source="modal.data"
      ></a-table>

      <p v-if="modal.ref_num_gt_50_hint">{{ modal.ref_num_gt_50_hint }}</p>
    </a-modal>

    <!-- copy  -->
    <a-modal
      v-model="showCopyModal"
      width="780px"
      :title="$t('104033')"
      @cancel="showCopyModal = false"
      @ok="handleCopyOk"
    >
      <PackageCopyForm
        ref="packageCopyForm"
        :show-from="showFrom"
        :options="options"
        @change="handleChange"
      />
    </a-modal>
  </a-drawer>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { getRuleModelParams, getRuleModelForm } from '@activity/pages/package/units/utils/sku-model'
import SkuModelForm from '@activity/pages/package/units/components/SkuModelForm.vue'
import { messageConfirm } from '@activity/pages/package/units/utils/message.js'
import { isTimeslotValid } from '@activity/pages/package/units/utils/sku-model.js'
import { pmsConfirm, getEditLockValueByPath } from '@activity/utils'
import { isAdmin } from '@/env'
import copySkuModel from '@activity/pages/package/units/mixins/copy-sku-model.js'

export default {
  name: 'SkuModelDrawer',
  components: {
    SkuModelForm
  },
  mixins: [copySkuModel],
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      drawerHeaderStyle: {
        borderBottom: 'none',
        paddingLeft: '16px',
        paddingRight: '16px'
      },
      drawerBodyStyle: {
        padding: '60px 16px'
      },

      loading: false,
      form: null,
      frozenWeek: [],
      frozenMaxRow: 0,
      saveSuccessMessage: '',
      frozen: false,

      shouldSecondaryConfirm: false,

      modal: {
        visible: false,
        tips: '',
        columns: [
          {
            title: __('48222'),
            dataIndex: 'sku_id_name',
            key: 'sku_id_name'
          },
          {
            title: 'Schedule',
            dataIndex: 'start_time',
            key: 'start_time'
          },
          {
            title: __('package_inventory'),
            dataIndex: 'inventory',
            key: 'inventory'
          },
          {
            title: __('package_price_cost'),
            dataIndex: 'cost_price',
            key: 'cost_price'
          }
        ],
        data: [],
        ref_num_gt_50_hint: ''
      }
    }
  },
  computed: {
    ...mapState({
      canEdit: (state) => state.canEdit,
      inventoryCanEdit: (state) => state.inventoryCanEdit,
      subCategoryId: (state) => state.categoryInfo?.sub_category_id || 0,
      skuModel: (state) => state.skuModel
    }),
    ...mapGetters(['isTripMapped', 'packageProductType']),
    isPass() {
      const categoryIds = process.env.NODE_ENV === 'production' ? [527] : [446]
      return categoryIds.includes(this.subCategoryId)
    },
    packageId() {
      return +this.$route.query.package_id
    },
    activityId() {
      return +this.$route.params.id || 0
    },
    getSpm() {
      const oid = `activity_${this.activityId || 0}`
      return `PriceInventory?oid=${oid}&trg=manual`
    },
    isCombo() {
      return this.packageProductType === 1
    },
    isOpenDateTickey() {
      return this.skuModel.is_open_date_without_calendar
    },
    showFrom() {
      const {
        isTripMapped,
        isCombo,
        isOpenDateTickey,
        inventoryCanEdit,
        canEdit,
        disabledDeleteBtn,
        disabledUpdateTimeslot,
        disabledAddTimeslot
      } = this
      const inv_model_change_forbid = this.skuModel?.inv_model_change_forbid || false
      // 权限
      if (disabledAddTimeslot || disabledUpdateTimeslot || disabledDeleteBtn) {
        return false
      }
      // 编辑状态
      if (!canEdit || inv_model_change_forbid || isTripMapped || isCombo || isOpenDateTickey) {
        return false
      }

      if (!canEdit && !inventoryCanEdit) {
        return false
      }
      return true
    },
    disabledDeleteBtn() {
      return this.getEditLockValueByPath({ path: 'sku_model.del_timeslot_model' }).status
    },
    disabledUpdateTimeslot() {
      return this.getEditLockValueByPath({ path: 'sku_model.update_timeslot_model' }).status
    },
    disabledAddTimeslot() {
      return this.getEditLockValueByPath({ path: 'sku_model.add_timeslot_model' }).status
    }
  },
  watch: {
    visible: {
      immediate: true,
      async handler(val) {
        if (val) {
          await this.initForm()
        }
      }
    }
  },
  methods: {
    getEditLockValueByPath(...args) {
      return getEditLockValueByPath({
        data: _.get(this, 'packageFresherSettingData', {}),
        ...args
      })
    },
    async handleCancel() {
      this.modal.visible = false
      await this.initForm()
    },
    showErrorModal(result) {
      let { in_use_timeslot, in_use_list, ref_num_gt_50_hint } = result
      this.modal.visible = true
      // format i18n weekday
      const inUseTimeslot = (in_use_timeslot || []).map((timeslot) => {
        let [weekday, time] = timeslot.split(' ')
        weekday = this.$t(`global_week_abbr_${weekday.toLowerCase()}`)

        return `${weekday} ${time}`
      })

      this.$set(this, 'modal', {
        ...this.modal,
        visible: true,
        tips: this.$t('30296').replace(
          /\{.+\}/g,
          '<b style="word-break: break-word;">' + inUseTimeslot.join(' / ') + '</b>'
        ),
        data: in_use_list,
        ref_num_gt_50_hint
      })
    },
    handleToggleModel() {
      if (this.shouldSecondaryConfirm) {
        this.$confirm({
          content: this.$t('30298'),
          onOk: async () => {
            this.shouldSecondaryConfirm = false
            await ajax.postBody(ADMIN_API.act.update_sku_model_to_by_time, {
              data: {
                package_id: this.packageId
              }
            })
            await this.initForm()
          },
          onCancel: () => {
            this.form.timeslot_type = 0
          }
        })
      }
    },
    close() {
      this.$emit('update:visible', false)
    },
    async initForm() {
      const { packageId } = this

      this.loading = true
      await this.$store.dispatch('actionGetSkuModel', { packageId })
      this.loading = false

      this.form = getRuleModelForm(this.skuModel)
      this.cacheForm = _.cloneDeep(this.form)
      this.frozenWeek = []

      if (this.form.timeslot_type === 0) {
        // this.frozenWeek = this.form.byDayModel.reduce(
        //   (accu, curr, index) => (curr.isByDay ? [...accu, 1 + index] : accu),
        //   []
        // )
        this.frozen = false
        this.shouldSecondaryConfirm = true
        this.frozenMaxRow = 0
      } else {
        // by time
        this.frozen = true
        this.frozenMaxRow = this.form.byTimeModel.length
      }
    },
    async handleSave() {
      const valid = await this.$refs.form.validateForm()

      if (!valid) {
        return
      }

      if (isAdmin && this.cacheForm.price_model !== this.form.price_model) {
        let temp = await pmsConfirm.call(this, {
          content: __('48155')
        })

        if (!temp) {
          return
        }
      }

      // presale 从开启改成关闭的时候，需要二次确认
      if (this.cacheForm.presale_allowed && !this.form.presale_allowed) {
        let temp = await pmsConfirm.call(this, {
          title: __('201137'),
          content: __('201138'),
          okText: __('global.confirm'),
          cancelText: __('17057')
        })

        if (!temp) {
          return
        }
      }

      // pass不能选择价格日历
      if (this.isPass && this.form.price_model === 1) {
        this.$message.error(this.$t('102416'))
        return
      }
      // 重置saveSuccessMessage
      this.saveSuccessMessage = ''

      const message = this.getConfirmMessage()

      if (message) {
        const result = await messageConfirm(this, message)
        result && (await this.saveSkuModel())
      } else {
        await this.saveSkuModel()
      }
    },
    async saveSkuModel() {
      let form = _.cloneDeep(this.form)
      form.byTimeModel = form.byTimeModel.map((row) => {
        return row.map((col) => {
          if (col.isDeleted) {
            return {
              cutoffDay: undefined,
              cutoffTime: undefined,
              startTime: undefined
            }
          }
          delete col.isDeleted

          return col
        })
      })
      const params = getRuleModelParams(form)
      params.package_id = this.packageId

      this.loading = true
      const res =
        (await ajax.postBody(ADMIN_API.act.update_sku_model, {
          data: params
        })) || {}
      this.loading = false
      // const res = await new Promise((resolve) =>
      //   setTimeout(() => resolve({ success: true }), 500)
      // )

      if (res.success) {
        let in_use_list = _.get(res, 'result.in_use_list', [])
        if (Array.isArray(in_use_list) && in_use_list.length) {
          this.showErrorModal(res.result)
        } else {
          const { packageId } = this
          await this.$store.dispatch('actionGetSkuModel', { packageId })
          klook.bus.$emit('refreshUnitList')
          // this.$message.success(
          //   this.saveSuccessMessage || this.$t('global_modify_success')
          // )
          this.$message.success((h) =>
            h('span', {
              domProps: {
                innerHTML: this.saveSuccessMessage || this.$t('global_modify_success')
              }
            })
          )

          this.$nextTick(() => {
            this.close()
          })
        }
      } else {
        this.$message.error(this.$t('global_modify_error'))
      }
    },
    checkTimeslotModified(data, originalData) {
      // by day
      if (data.byDayModel && originalData.byDayModel) {
        let result = false

        originalData.byDayModel.forEach((day, dayIndex) => {
          if (!day.isByDay && data.byDayModel[dayIndex].isByDay) {
            // false -> true
            result = true
          }
        })

        if (result) {
          return true
        }
      }

      // by timeslot
      if (data.byTimeModel && originalData.byTimeModel) {
        let result = false
        const originalLength = originalData.byTimeModel.length

        data.byTimeModel.forEach((row, rowIndex) => {
          if (rowIndex >= originalLength) {
            // 需要验证新增加的是否有效
            row.forEach((day) => {
              if (isTimeslotValid(day)) {
                result = true
              }
            })
          }
        })

        if (result) {
          return result
        }
      }

      return false
    },
    checkCutoffTimeModified(data, originalData) {
      // by day
      if (
        data.byDayCutoffDay !== originalData.byDayCutoffDay ||
        data.byDayCutoffTime !== originalData.byDayCutoffTime
      ) {
        return true
      }

      // by timeslot
      if (data.byTimeModel && originalData.byTimeModel) {
        let result = false

        originalData.byTimeModel.forEach((row, rowIndex) => {
          row.forEach((day, dayIndex) => {
            // 这里因为不能删除，所以 modifiedDay 一定存在
            const modifiedDay = data.byTimeModel[rowIndex][dayIndex]

            // 由于startTime不能编辑，所以不需要比较里面的cutoffDay和cutoffTime
            if (!modifiedDay.isDeleted && !_.isEqual(day, modifiedDay)) {
              result = true
            }
          })
        })

        if (result) {
          return true
        }
      }

      return false
    },
    getConfirmMessage() {
      // 需要判断修改来展示不同的message
      let message = ''
      const data = this.form
      const originalData = getRuleModelForm(this.skuModel)

      // timeslot 改变
      if (this.checkTimeslotModified(data, originalData)) {
        message = `${message}${message ? '<br /><br />' : ''}${this.$t('27730')}`
      }

      // cutoff time 改变
      if (this.checkCutoffTimeModified(data, originalData)) {
        message = `${message}${message ? '<br /><br />' : ''}${this.$t('27731')}`
      }

      // inv_model
      if (data.inv_model !== originalData.inv_model) {
        message = `${message}${message ? '<br /><br />' : ''}${this.$t('27744')}`
        this.saveSuccessMessage = this.$t('27745').replace('<br/><br/>', '<br/>')
      }

      // price_model
      if (data.price_model !== originalData.price_model) {
        message = `${message}${message ? '<br /><br />' : ''}${this.$t('27746')}`
        this.saveSuccessMessage = this.$t('27747').replace('<br/><br/>', '<br/>')
      }

      return message
    }
  }
}
</script>

<style lang="scss" scoped>
.copy-button {
  margin-left: 16px;
}
.sku-model-drawer {
  &-footer {
    position: absolute;
    padding: 10px 16px;
    right: 0;
    bottom: 0;
    width: 100%;

    border-top: 1px solid #e9e9e9;
    background: #fff;
    text-align: right;
    z-index: 3;
  }
}
</style>
