<template>
  <a-form-model ref="form" class="timeslot-inventory-form" layout="vertical" :model="form">
    <GeneralAlert :show-icon="false" banner style="margin-bottom: 12px">
      <div v-html="$t('27724')" />
    </GeneralAlert>
    <div v-if="isCreate && pkgList.length > 1 && !isCombo" class="copy-btn">
      <a-button
        :data-spm-module="getCopyTimeslotSpm"
        data-spm-virtual-item="__virtual"
        type="default"
        @click="handleShowCopyModal"
      >
        {{ $t('30292') }}
      </a-button>
    </div>

    <copy-modal v-if="copyModalVisible" v-model="copyModalVisible" v-on="$listeners"></copy-modal>

    <a-form-model-item
      :label="$t('27725')"
      prop="timeslot_type"
      :rules="{
        required: isOpenDateTickey ? false : true,
        message: $t('global_please_select'),
        trigger: 'change'
      }"
    >
      <a-radio-group
        v-model="form.timeslot_type"
        v-tooltip="{
          visible: disabledUpdateTimeslot.status,
          content: disabledUpdateTimeslot.tips,
          placement: 'bottom'
        }"
        :disabled="(!canEdit && !inventoryCanEdit) || frozen || isTripMapped || isOpenDateTickey || isCombo"
        :title="isTripMapped ? $t('28910') : ''"
        @change="handleChangeTimeslotType"
      >
        <a-radio :value="0">
          <a-tooltip placement="top" arrow-point-at-center>
            <template slot="title">
              {{ $t('29434') }}
            </template>
            {{ $t('27726') }}
          </a-tooltip>
        </a-radio>
        <a-radio :value="1">
          <a-tooltip placement="top" arrow-point-at-center>
            <template slot="title">
              {{ $t('29435') }}
            </template>
            {{ $t('27727') }}
          </a-tooltip>
        </a-radio>
      </a-radio-group>
    </a-form-model-item>

    <div style="margin-bottom: 12px">
      <RawTable
        v-if="[0, 1].includes(form.timeslot_type)"
        :min-width="form.timeslot_type === 0 ? '800px' : '1400px'"
      >
        <template v-if="form.timeslot_type === 0">
          <tr>
            <td width="100px" style="background-color: #fafafa; font-weight: 600">
              {{ $t('27729') }}
            </td>
            <td v-if="isCreate" width="80px" class="white-cell">
              {{ $t('global_button_all') }}
              <a-checkbox
                :indeterminate="indeterminate"
                :checked="checkAll"
                :disabled="(!canEdit && !inventoryCanEdit) || isTripMapped || isOpenDateTickey || isCombo"
                @change="changeCheckAll"
              ></a-checkbox>
            </td>
            <td
              v-for="(dayModel, dayIndex) in form.byDayModel"
              :key="dayIndex"
              width="100px"
              class="white-cell"
            >
              <a-form-model-item
                :prop="`byDayModel[${dayIndex}].isByDay`"
                :rules="{
                  required: true,
                  message: $t('global_please_select'),
                  trigger: 'change'
                }"
                style="margin-bottom: 0; padding-bottom: 0"
              >
                <div
                  class="flex-between pointer"
                  :class="{ 'is-forbidden': frozenWeek.includes(1 + dayIndex) }"
                  @click="handleByDayClick(dayModel, frozenWeek.includes(1 + dayIndex))"
                >
                  <span>{{ weekAbbrList[dayIndex] }}</span>
                  <a-checkbox
                    :disabled="
                      (!canEdit && !inventoryCanEdit) ||
                      frozenWeek.includes(1 + dayIndex) ||
                      isTripMapped ||
                      isOpenDateTickey ||
                      isCombo
                    "
                    :checked="dayModel.isByDay"
                    :title="isTripMapped ? $t('28910') : ''"
                  />
                </div>
              </a-form-model-item>
            </td>
          </tr>
        </template>
        <template v-else-if="form.timeslot_type === 1">
          <tr>
            <th width="120px"></th>
            <th v-for="item in [0, 1, 2, 3, 4, 5, 6]" :key="item" width="120px">
              {{ weekAbbrList[item] }}
            </th>
          </tr>
          <tr v-for="(weekModel, weekIndex) in form.byTimeModel" :key="weekIndex">
            <td style="background-color: #fafafa">
              <div class="title-wrap">
                <p style="margin: 0; font-weight: 600">{{ $t('27728') }}</p>
                <CircleDeleteButton
                  v-if="weekIndex >= frozenMaxRow"
                  v-tooltip="{
                    visible: disabledDeleteBtn.status,
                    content: disabledDeleteBtn.tips
                  }"
                  :disabled="form.byTimeModel.length <= 1 || disabledDeleteBtn.status || isCombo"
                  class="circle-delete-button"
                  @click="handleTimeslotDelete(weekIndex)"
                />
              </div>
            </td>
            <td
              v-for="(dayModel, dayIndex) in weekModel"
              :key="dayIndex"
              style="vertical-align: top; padding-bottom: 4px"
              :class="{
                'is-deleted': dayModel.isDeleted
              }"
            >
              <a-form-model-item
                :ref="`timeslots-${weekIndex}-${dayIndex}`"
                :prop="`byTimeModel[${weekIndex}][${dayIndex}].startTime`"
                :rules="generateByTimeRules(dayModel, dayIndex, weekIndex)"
                style="margin-bottom: 0; padding-bottom: 0"
              >
                <TimeslotFormItem
                  v-tooltip="{
                    visible: disabledUpdateTimeslot.status,
                    content: disabledUpdateTimeslot.tips,
                    placement: 'bottom'
                  }"
                  :day-model="dayModel"
                  :frozen="weekIndex < frozenMaxRow"
                  :disabled="
                    (!canEdit && !inventoryCanEdit) ||
                    isTripMapped ||
                    disabledUpdateTimeslot.status ||
                    isCombo
                  "
                  :time-picker-attrs="timeSlotFormTimePickerAttrs"
                />
              </a-form-model-item>
              <div
                v-if="weekIndex >= frozenMaxRow && !isCombo && !disabledUpdateTimeslot.status"
                class="form-operations"
              >
                <svg-icon
                  v-show="!isTimeslotBlank(dayModel)"
                  icon-name="close"
                  style="cursor: pointer; font-size: 24px"
                  @click="handleClear(weekModel, dayIndex, `timeslots-${weekIndex}-${dayIndex}`)"
                />
                <a-popover
                  v-if="!isTimeslotBlank(dayModel)"
                  :visible="currentCopyView[`${weekIndex}-${dayIndex}`]"
                  trigger="click"
                  overlay-class-name="copy-popover"
                  placement="topRight"
                >
                  <div slot="content" class="copy-popover-content">
                    <a-checkbox-group v-model="copyCheckList" style="display: flex; flex-direction: column">
                      <template v-for="(week, idx) in weekAbbrList">
                        <a-checkbox v-if="idx !== dayIndex" :key="week" :value="idx">{{ week }} </a-checkbox>
                      </template>
                    </a-checkbox-group>
                    <footer class="footer">
                      <a-button type="link" size="small" @click="resetCopyData">{{
                        $t('global_reset')
                      }}</a-button>
                      <a-button
                        type="primary"
                        size="small"
                        :disabled="!copyCheckList.length"
                        @click="
                          handleCopyToOther(weekIndex, dayIndex, `timeslots-${weekIndex}-${dayIndex + 1}`)
                        "
                      >
                        {{ $t('voucher.copy') }}
                      </a-button>
                    </footer>
                  </div>

                  <a-tooltip placement="top">
                    <template slot="title">
                      <span>{{ $t('30294') }}</span>
                    </template>
                    <svg-icon
                      icon-name="copy"
                      :data-spm-module="getCopyViewSpm"
                      data-spm-virtual-item="__virtual"
                      style="margin-left: 4px; cursor: pointer; font-size: 24px"
                      @click="handleCopyView(weekIndex, dayIndex)"
                    />
                  </a-tooltip>
                </a-popover>
              </div>
            </td>
          </tr>
        </template>
      </RawTable>

      <div v-if="showAddTimeslot">
        <label
          v-tooltip="{
            visible: disabledAddTimeslot.status,
            content: disabledAddTimeslot.tips,
            placement: 'bottom'
          }"
        >
          <a-button
            type="link"
            icon="plus"
            style="padding: 0; font-size: 14px"
            :disabled="
              (!canEdit && !inventoryCanEdit) || isTripMapped || disabledAddTimeslot.status || isCombo
            "
            :title="isTripMapped ? $t('28910') : ''"
            @click="handleAddTimeslot"
          >
            {{ $t('global_add') }}
          </a-button>
        </label>
      </div>
    </div>

    <a-form-model-item
      v-if="form.timeslot_type === 0"
      :label="$t('package_cut_off_time')"
      prop="byDayCutoffTime"
      :rules="generateByDayRules(form.byDayCutoffDay, form.byDayCutoffTime)"
    >
      <CutoffForm
        :form="form"
        :disabled="(!canEdit && !inventoryCanEdit) || isTripMapped || isOpenDateTickey || isCombo"
        :title="isTripMapped ? $t('28910') : ''"
        day-field="byDayCutoffDay"
        time-field="byDayCutoffTime"
        day-style="width: 200px;margin-right: 8px;"
        time-style="width: 270px;"
        @blur="$emit('blur')"
      />
    </a-form-model-item>

    <a-form-model-item
      v-if="!isPICSku"
      :label="$t('27732')"
      prop="inv_model"
      :rules="{
        required: true,
        message: $t('global_please_select'),
        trigger: 'change'
      }"
    >
      <BasicFormPopover
        :message="$t('27743')"
        :auto-adjust-overflow="false"
        :show-popover="skuModel.inv_model_change_forbid"
        trigger="hover"
        placement="right"
      >
        <a-radio-group
          v-model="form.inv_model"
          :disabled="!canEdit || skuModel.inv_model_change_forbid || isTripMapped || isCombo"
          :title="isTripMapped ? $t('28910') : ''"
        >
          <a-radio
            v-for="item in INVENTORY_MODELS"
            :key="item.value"
            :value="item.value"
            :disabled="disableInventoryModelItem(item.value)"
          >
            <a-tooltip placement="top" arrow-point-at-center>
              <template slot="title">
                {{ item.tip }}
              </template>
              {{ item.text }}
            </a-tooltip>
            <!-- <QuestionIcon :message="item.tip" /> -->
          </a-radio>
        </a-radio-group>
      </BasicFormPopover>
    </a-form-model-item>

    <a-form-model-item
      :label="$t('pm_price_model')"
      prop="price_model"
      :rules="{
        required: isOpenDateTickey ? false : true,
        message: $t('global_please_select'),
        trigger: 'change'
      }"
    >
      <BasicFormPopover
        :message="$t('27742')"
        :auto-adjust-overflow="false"
        :show-popover="skuModel.price_model_change_forbid"
        trigger="hover"
        placement="right"
      >
        <a-radio-group
          v-model="form.price_model"
          :disabled="
            !canEdit || skuModel.price_model_change_forbid || isTripMapped || isOpenDateTickey || isCombo
          "
          :title="isTripMapped ? $t('28910') : ''"
        >
          <a-radio
            v-for="item in PRICE_MODELS"
            :key="item.value"
            :value="item.value"
            :disabled="isPass && item.value === 1"
          >
            <a-tooltip placement="top" arrow-point-at-center>
              <template slot="title">
                {{ item.tip }}
              </template>
              {{ item.text }}
            </a-tooltip>
            <!-- <QuestionIcon :message="item.tip" /> -->
          </a-radio>
        </a-radio-group>
      </BasicFormPopover>
    </a-form-model-item>

    <CurrencyFormItem
      :form="form"
      :disabled="!canEdit || frozen || isMerchant || isTripMapped"
      :disabled-selling-currency="disabledSellingCurrency"
    />

    <PresaleFormItem v-if="form.presale_visible" :form="form" :disabled="!canEdit || isTotalInventory" />
  </a-form-model>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import {
  INVENTORY_MODELS,
  PRICE_MODELS,
  SIM_SUB_CATEGORY_IDS,
  WIFI_SUB_CATEGORY_IDS
} from '@activity/pages/package/package_const.js'
import RawTable from '@activity/pages/package/units/components/raw-table'
import TimeslotFormItem from '@activity/pages/package/units/components/TimeslotFormItem.vue'
import CurrencyFormItem from '@activity/pages/package/units/components/CurrencyFormItem.vue'
import PresaleFormItem from '@activity/pages/package/units/components/PresaleFormItem.vue'
import CutoffForm from '@activity/pages/package/units/components/CutoffForm.vue'
import CircleDeleteButton from '@activity/components/CircleDeleteButton.vue'
import GeneralAlert from '@activity/pages/components/GeneralAlert.vue'
import BasicFormPopover from '@activity/pages/components/BasicFormPopover.vue'
import { isBlank, isTimeslotValid } from '@activity/pages/package/units/utils/sku-model.js'
import CopyModal from './copyModal'
// import QuestionIcon from '@activity/pages/package/units/components/QuestionIcon.vue'
import { getEditLockValueByPath } from '@activity/utils'

export default {
  name: 'SkuModelForm',
  components: {
    CopyModal,
    RawTable,
    TimeslotFormItem,
    CurrencyFormItem,
    PresaleFormItem,
    CutoffForm,
    CircleDeleteButton,
    GeneralAlert,
    BasicFormPopover
    // QuestionIcon
  },
  props: {
    // timeslot_type，inv_model，price_model，byDayModel，byTimeModel
    // merchant_currency，selling_currency
    form: {
      type: Object,
      required: true
    },
    frozen: {
      type: Boolean,
      default: false
    },
    isPass: {
      type: Boolean,
      default: false
    },
    frozenWeek: {
      type: Array,
      default: () => []
    },
    frozenMaxRow: {
      type: Number,
      default: 0
    },
    isCreate: {
      type: Boolean,
      default: false
    }
  },
  provide() {
    return {
      getEditLockValueByPath: this.getEditLockValueByPath
    }
  },
  data() {
    return {
      INVENTORY_MODELS,
      PRICE_MODELS,

      checkAll: false,
      indeterminate: true,

      currentCopyView: {},
      copyCheckList: [],

      copyModalVisible: false
    }
  },

  computed: {
    subCategoryId() {
      return this.$store.state.categoryInfo?.sub_category_id ?? 0
    },
    isWifiSim() {
      return [...SIM_SUB_CATEGORY_IDS, ...WIFI_SUB_CATEGORY_IDS].includes(this.subCategoryId)
    },
    isWifiSimByDay() {
      return this.isWifiSim && this.form.timeslot_type === 1
    },
    timeSlotFormTimePickerAttrs() {
      return this.isWifiSimByDay
        ? {
            hourStep: 24,
            minuteStep: 60
          }
        : {}
    },
    defaultTimeslot() {
      return {
        startTime: undefined,
        cutoffDay: undefined,
        cutoffTime: undefined
      }
    },
    showAddTimeslot() {
      return this.form.timeslot_type === 1
    },
    getCopyTimeslotSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `CopyPackageTimeslot?oid=${oid}&trg=manual`
    },
    getCopyViewSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `CopyTimeslot?oid=${oid}&trg=manual`
    },
    ...mapState({
      canEdit: (state) => state.canEdit,
      inventoryCanEdit: (state) => state.inventoryCanEdit,
      skuModel: (state) => state.skuModel,
      isMerchant: (state) => state.isMerchant,
      clickTarget: (state) => state.clickTarget,
      packageFresherSettingData: 'packageFresherSettingData'
    }),
    // 是否是pic库存
    isPICSku() {
      let { stock_out_type = '', support_time_range_inv_model = false } = this.skuModel
      let { inv_model = -1 } = this.form
      return (stock_out_type === 'INVENTORY' || inv_model === 4) && support_time_range_inv_model
    },
    // open date ticket
    isOpenDateTickey() {
      return this.skuModel.is_open_date_without_calendar
    },
    // combo
    isCombo() {
      return this.packageProductType === 1
    },
    isPresaleOpen() {
      return this.form.presale_allowed
    },
    isTotalInventory() {
      return this.form.inv_model === 3
    },
    disabledSellingCurrency() {
      return !this.isCreate && !!this.form.selling_currency
    },
    ...mapGetters(['isTripMapped', 'pkgList', 'packageProductType']),
    weekAbbrList() {
      return [
        __('global_week_abbr_mon'),
        __('global_week_abbr_tue'),
        __('global_week_abbr_wed'),
        __('global_week_abbr_thu'),
        __('global_week_abbr_fri'),
        __('global_week_abbr_sat'),
        __('global_week_abbr_sun')
      ]
    },
    disabledDeleteBtn() {
      return this.getEditLockValueByPath({ path: 'sku_model.del_timeslot_model' })
    },
    disabledUpdateTimeslot() {
      return this.getEditLockValueByPath({ path: 'sku_model.update_timeslot_model' })
    },
    disabledAddTimeslot() {
      return this.getEditLockValueByPath({ path: 'sku_model.add_timeslot_model' })
    }
  },
  watch: {
    isOpenDateTickey: {
      immediate: true,
      handler(v) {
        if (v) {
          this.form.price_model = 0
          this.form.timeslot_type = 0
          this.form.byDayCutoffDay = 0
          this.form.byDayCutoffTime = '00:00'
          this.changeCheckAll({ target: { checked: true } })
          // this.form.byDayModel = [1, 2, 3, 4, 5, 6, 7].map(() => {
          //   return { isByDay: true }
          // })
        }
      }
    },
    'form.byDayModel': {
      deep: true,
      immediate: true,
      handler() {
        let checkedList = this.form.byDayModel.filter((item) => item.isByDay)
        this.indeterminate = !!checkedList.length && checkedList.length < this.form.byDayModel.length
        this.checkAll = checkedList.length === this.form.byDayModel.length
      }
    },
    clickTarget(target) {
      if (target) {
        if (![...document.querySelectorAll('.copy-popover')].some((item) => item.contains(target))) {
          this.$set(
            this,
            'currentCopyView',
            Object.keys(this.currentCopyView).reduce(
              (acc, curr) => ({
                ...acc,
                [curr]: false
              }),
              {}
            )
          )
        }
      }
    }
  },
  methods: {
    getEditLockValueByPath(...args) {
      return getEditLockValueByPath({
        data: _.get(this, 'packageFresherSettingData', {}),
        ...args
      })
    },
    handleShowCopyModal() {
      this.copyModalVisible = true
    },
    handleCopyView(weekIndex, dayIndex) {
      setTimeout(() => {
        this.resetCopyData()
        this.$set(this, 'currentCopyView', {
          ...Object.keys(this.currentCopyView).reduce(
            (acc, curr) => ({
              ...acc,
              [curr]: false
            }),
            {}
          ),
          [`${weekIndex}-${dayIndex}`]: true
        })
      }, 60)
    },
    resetCopyData() {
      this.$set(
        this,
        'copyCheckList',
        Array(7)
          .fill()
          .map((c, i) => i)
      )
    },
    changeCheckAll(evt) {
      this.form.byDayModel.forEach((item) => {
        item.isByDay = evt.target.checked
      })
    },
    generateByDayRules(cutoffDay, cutoffTime) {
      const validator = (rule, value, callback) => {
        if (_.isUndefined(cutoffDay) || _.isUndefined(cutoffTime)) {
          callback(new Error(this.$t('global_please_select')))
        } else {
          callback()
        }
      }

      return [
        {
          validator,
          required: this.isOpenDateTickey ? false : true,
          trigger: 'blur'
        }
      ]
    },
    generateByTimeRules(dayModel, dayIndex, weekIndex) {
      const validator = (rule, value, callback) => {
        const { byTimeModel } = this.form
        const { startTime, cutoffDay, cutoffTime } = dayModel
        const isAllBlank = isBlank(startTime) && isBlank(cutoffDay) && isBlank(cutoffTime)
        const isAllNotBlank = !isBlank(startTime) && !isBlank(cutoffDay) && !isBlank(cutoffTime)

        // 检测：填了就一定要填完
        if (!isAllBlank && !isAllNotBlank) {
          callback(new Error(this.$t('global_please_select')))
          return
        }

        let isDuplicate = false
        byTimeModel.forEach((weekModel, index) => {
          if (weekIndex !== index && weekModel[dayIndex].startTime === startTime) {
            isDuplicate = true
          }
        })

        if (isAllNotBlank && isDuplicate) {
          callback(new Error('Duplicate start time!'))
          return
        }

        callback()
      }

      return [
        {
          validator,
          required: true,
          trigger: 'blur'
        }
      ]
    },
    handleClear(weekModel, dayIndex, refname) {
      weekModel.splice(dayIndex, 1, _.cloneDeep(this.defaultTimeslot))
      this.$nextTick(() => {
        this.$refs[refname][0].onFieldBlur()
      })
    },
    handleCopyToOther(weekIndex, dayIndex) {
      let currentCopy = this.form.byTimeModel[weekIndex][dayIndex]
      this.$set(
        this.form.byTimeModel,
        weekIndex,
        this.form.byTimeModel[weekIndex].map((item, index) => {
          if (this.copyCheckList.includes(index)) {
            return _.cloneDeep(currentCopy)
          }

          return item
        })
      )

      this.$nextTick(() => {
        this.$set(this.currentCopyView, `${weekIndex}-${dayIndex}`, false)
        this.$message.success(this.$t('global_copy_success'))
        // this.$refs[refname][0].onFieldBlur()
      })
      this.GTMHookItem('Price&Inventory|TimeslotRuleDuplicate_Click')
    },
    handleAddTimeslot() {
      const newTimeslots = Array(7)
        .fill(0)
        .map(() => _.cloneDeep(this.defaultTimeslot))
      this.form.byTimeModel.push(newTimeslots)
    },
    validateForm() {
      return this.$refs.form.validate().catch(() => false)
    },
    handleChangeTimeslotType() {
      this.$emit('toggleModel')
    },
    handleTimeslotDelete(weekIndex) {
      this.form.byTimeModel.splice(weekIndex, 1)
    },
    handleByDayClick(dayModel, disabled) {
      const editable =
        (!this.canEdit && !this.inventoryCanEdit) ||
        disabled ||
        this.isTripMapped ||
        this.isOpenDateTickey ||
        this.isCombo

      if (!editable) {
        dayModel.isByDay = !dayModel.isByDay
      }
    },
    isTimeslotBlank(timeslot) {
      return !isTimeslotValid(timeslot)
    },
    disableInventoryModelItem(value) {
      return (this.isOpenDateTickey && value === 1) || (this.isPresaleOpen && value === 3)
    }
  }
}
</script>

<style lang="scss">
.copy-popover {
  .ant-popover-inner-content {
    padding: 0;
  }
  .copy-popover-content {
    width: max-content;
  }
  .ant-checkbox-group {
    padding: 12px;
  }
  .ant-checkbox-wrapper {
    margin-left: 0;
    margin-bottom: 8px;
  }
  .footer {
    display: flex;
    justify-content: space-around;
    padding: 8px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>

<style lang="scss" scoped>
.timeslot-inventory-form {
  .pointer {
    cursor: pointer;
  }
  .flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .form-operations {
    margin-top: 4px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  .title-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .circle-delete-button {
      font-size: 10px;
      width: 16px;
      height: 16px;
      min-width: 16px;
    }
  }
  .is-forbidden {
    color: rgba(0, 0, 0, 0.45);
    cursor: not-allowed;
  }
  .white-td {
    background-color: #fff;
  }
  .is-deleted {
    background-color: #fff6f5;
  }
}

.copy-btn {
  position: absolute;
  right: 12px;
  z-index: 999;
}
</style>
