<template>
  <a-drawer
    class="special-selling-price-drawer"
    :title="$t('package_custom_selling')"
    destroyOnClose
    :width="900"
    :visible="visible"
    :headerStyle="drawerHeaderStyle"
    :bodyStyle="drawerBodyStyle"
    placement="right"
    @close="handleClose"
  >
    <div class="ta-right">
      <a
        href="https://orion.myklook.com/workplace/feedback/add/all?feedbackType=0&subjectId=245"
        target="_blank"
        ><a-button type="link">{{ $t('30131') }}</a-button></a
      >
    </div>
    <div class="alert-warn-wrap">
      <a-alert type="warning" class="common-alert-style">
        <div slot="message" class="alert-warn-box">
          <a-icon type="sound" theme="filled" />
          <span class="text">{{ $t('30132') }}</span>
          <a-tooltip placement="top" :title="$t('30154')">
            <a-icon type="info-circle" />
          </a-tooltip>
        </div>
      </a-alert>
    </div>
    <div>
      <a-spin :spinning="isTakeRateLoading">
        <TakeRateTips
          v-if="overrideTakeRate || lastTakeRate"
          style="margin: 0 0 10px;"
          :override-take-rate="overrideTakeRate"
          :last-take-rate="lastTakeRate"
        />
      </a-spin>
      <a-form-model ref="form" :model="form">
        <a-form-model-item :label="$t('package_custom_selling')" prop="specialSellingPriceList">
          <SpecialSellingPriceList
            ref="ssp"
            :price="price"
            :selling-currency="sellingCurrency"
            :special-selling-price-list="form.specialSellingPriceList"
          />
        </a-form-model-item>
      </a-form-model>
    </div>

    <div class="drawer-footer">
      <a-button :style="{ marginRight: '8px' }" @click="handleClose">
        {{ $t('global_button_cancel') }}
      </a-button>
      <a-button type="primary" @click="handleSave">
        {{ $t('29280') }}
      </a-button>
    </div>
  </a-drawer>
</template>

<script>
import TakeRateTips from '@activity/pages/components/TakeRateTips.vue'
import SpecialSellingPriceList from '@activity/pages/components/SpecialSellingPriceList.vue'

export default {
  name: 'SpecialSellingPriceDrawer',
  components: {
    TakeRateTips,
    SpecialSellingPriceList
  },
  props: {
    price: {
      type: [Number, String],
      default: 0
    },
    sellingCurrency: {
      type: String,
      required: true
    },
    skuId: {
      type: [Number, String],
      default: null
    },
    timePoints: {
      type: Array,
      default: () => []
    },
    visible: {
      type: Boolean,
      default: false
    },
    initialValue: {
      type: Array,
      default: () => []
    },
    takeRate: {
      type: Object,
      required: false,
      default: undefined
    }
  },
  data() {
    return {
      isTakeRateLoading: false,
      overrideTakeRate: '',
      lastTakeRate: '',

      form: {
        specialSellingPriceList: []
      },

      drawerHeaderStyle: {
        borderBottom: 'none',
        paddingLeft: '16px',
        paddingRight: '16px'
      },
      drawerBodyStyle: {
        padding: '60px 16px'
      }
    }
  },
  watch: {
    async visible(val) {
      if (val) {
        this.init()
        await this.getTakeRate()
      }
    }
  },
  methods: {
    async getTakeRate() {
      let { takeRate } = this
      if (takeRate) {
        // 外部传入
        this.overrideTakeRate = takeRate.overrideTakeRate
        this.lastTakeRate = takeRate.lastTakeRate
        return
      }
      this.isTakeRateLoading = true

      const res = await ajax.post(ADMIN_API.act.get_sku_take_rate_by_time, {
        data: {
          sku_id: Number(this.skuId),
          time_points: this.timePoints
        }
      })

      this.isTakeRateLoading = false
      this.overrideTakeRate = res.override_take_rate
      this.lastTakeRate = res.last_take_rate
    },
    async handleSave() {
      // const valid = await this.$refs.form.validate().catch(() => false)
      const valid = await this.$refs.ssp.validate().catch(() => false)
      if (valid) {
        this.$emit('confirm', this.$refs.ssp.getSspData())
        this.handleClose()
      }
    },
    init() {
      this.form.specialSellingPriceList = _.cloneDeep(this.initialValue || [])
    },
    handleClose() {
      this.$emit('update:visible', false)
      this.form = this.$options.data().form
    }
  }
}
</script>

<style lang="scss" scoped>
.special-selling-price-drawer {
  .alert-warn-wrap {
    margin: 14px 0 12px 0;
  }
  .alert-warn-box {
    font-size: 12px;
    color: #000;
    line-height: 14px;
    .text {
      margin: 0 12px;
      vertical-align: middle;
    }
    i {
      font-size: 14px;
      vertical-align: middle;
      &.anticon-sound {
        color: #faad14;
      }
      &.anticon-info-circle {
        color: rgba(0, 0, 0, 0.65);
      }
    }
  }
  .drawer-footer {
    position: absolute;
    padding: 10px 16px;
    right: 0;
    bottom: 0;
    width: 100%;

    border-top: 1px solid #e9e9e9;
    background: #fff;
    text-align: right;
    z-index: 3;
  }
}
</style>
