<template>
  <div class="timeslot-form-item" @click="handleClick">
    <template v-if="frozen">
      <div v-show="isValid">
        <p style="margin-bottom: 6px;">
          {{ dayModel.startTime }}
        </p>
        <CutoffForm
          v-show="isEditing"
          :form="dayModel"
          :disabled="disabled"
          dayStyle="width: 80px;margin-right: 6px;"
          timeStyle="width: 80px;"
          @blur="$emit('blur')"
        />
        <div v-show="!isEditing">
          {{ dayModel.cutoffDay }} {{ $t('global_day') }}
          {{ dayModel.cutoffTime }}
          <div class="delete-icon" @click.stop="handleDelete(dayModel)">
            <a-tooltip placement="top" v-if="!dayModel.isDeleted" :disabled="disabledDelete.status">
              <template slot="title">{{ $t('global_delete') }}</template>
              <svg-icon
                icon-name="delete-trash"
                v-tooltip="{
                  visible: disabledDelete.status,
                  content: disabledDelete.tips,
                  placement: 'bottom',
                  clearOtherTooltips: true
                }"
              ></svg-icon>
            </a-tooltip>
            <a-button
              v-if="hasOwnProperty.call(dayModel, 'isDeleted') && dayModel.isDeleted"
              type="default"
              size="small"
              @click.stop="handleCancelDelete(dayModel)"
              >{{ $t('29194') }}</a-button
            >
          </div>
        </div>
      </div>
    </template>
    <div v-else style="margin-bottom: 12px;">
      <a-time-picker
        :value="getTimeslot(dayModel.startTime)"
        :default-open-value="defaultTimePickerValue"
        :disabled="disabled"
        format="HH:mm"
        style="width: 100%; margin-bottom: 6px;"
        v-bind="timePickerAttrs"
        @blur="$emit('blur')"
        @change="(time, timeString) => handleChange('startTime', timeString)"
      />
      <CutoffForm
        :form="dayModel"
        :disabled="disabled"
        dayStyle="width: 80px;margin-right: 6px;"
        timeStyle="width: 80px;"
        @blur="$emit('blur')"
      />
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { mapState } from 'vuex'
import { defaultTimePickerValue } from '@activity/pages/package/units/utils'
import CutoffForm from '@activity/pages/package/units/components/CutoffForm.vue'

export default {
  name: 'TimeslotFormItem',
  inject: ['getEditLockValueByPath'],
  components: {
    CutoffForm
  },
  props: {
    dayModel: {
      type: Object,
      required: true
    },
    frozen: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    timePickerAttrs: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      defaultTimePickerValue,

      isEditing: false
    }
  },
  computed: {
    ...mapState({
      clickTarget: (state) => state.clickTarget
    }),
    isValid() {
      // 只要不是undefined即可
      const { startTime, cutoffDay, cutoffTime } = this.dayModel
      return !_.isUndefined(startTime) && !_.isUndefined(cutoffDay) && !_.isUndefined(cutoffTime)
    },
    disabledDelete() {
      return this.getEditLockValueByPath({ path: 'sku_model.del_timeslot_model' })
    }
  },
  watch: {
    clickTarget(target) {
      if (target && this.isEditing) {
        const formDom = document.querySelector('.timeslot-inventory-form')
        const elDom = this.$el

        const isInForm = formDom && formDom.contains(target)
        const isInEl = elDom && elDom.contains(target)

        if ((isInForm && !isInEl) || this.dayModel.isDeleted) {
          this.isEditing = false
        }
      }
    }
  },
  methods: {
    handleCancelDelete(dayModel) {
      this.$set(dayModel, 'isDeleted', false)
    },
    handleDelete(dayModel) {
      if (this.disabledDelete.status) {
        return
      }
      this.$set(dayModel, 'isDeleted', true)
    },
    handleClick() {
      this.isEditing = true
    },
    getTimeslot(str) {
      return str ? moment(str, 'HH:mm') : ''
    },
    handleChange(field, value) {
      this.dayModel[field] = value
      this.$emit('blur')
    }
  }
}
</script>

<style lang="scss">
.timeslot-form-item {
  p {
    margin: 0;
  }
}
</style>

<style lang="scss" scoped>
.timeslot-form-item {
  width: 166px;
  .delete-icon {
    display: flex;
    justify-content: flex-end;
    padding: 8px 0;
    ::v-deep .svg-icon {
      font-size: 18px;
    }
  }
}
</style>
