<template>
  <div class="add-schedule-form-item-container">
    <div class="form-item-row-box">
      <a-form-model-item
        v-if="showInventory"
        prop="inv_quantity"
        required
        :class="{ '--is-fixed': invCannotEdit }"
      >
        <template #label>
          <FormLabel :label="$t('package_inventory')" :label-value="invCannotEdit ? form.inv_quantity : ''" />
        </template>
        <PriceInput
          v-model="form.inv_quantity"
          :disabled="invCannotEdit || isSubSku"
          :show-text="invCannotEdit"
          :is-int="true"
          :max-value="MAX_INVENTORY"
          :tips="invQuantityTips"
          :text-style="inputStyle"
          :tooltip-title="isSubSku ? $t('27805') : ''"
          :placeholder="isSubSku ? $t('package_inventory') : ''"
          @edit="editRuleSetting('inventory')"
        />
      </a-form-model-item>

      <a-form-model-item
        prop="cost_price"
        required
        :class="{ '--is-fixed': showPriceText }"
        :colon="!getLabel('cost_price', 'cost_currency')"
      >
        <template #label>
          <FormLabel
            :label="$t('81823')"
            :label-value="getLabel('cost_price', 'cost_currency')"
            :tips="$t('112108')"
          />
        </template>
        <PriceInput
          v-model="form.cost_price"
          :show-text="showPriceText"
          :prefix="priceData.cost_currency"
          :tips="fixedPriceModelTips"
          :text-style="inputStyle"
          @edit="editRuleSetting('price')"
        />
      </a-form-model-item>

      <a-form-model-item
        v-if="!isTour"
        prop="retail_price"
        required
        :class="{ '--is-fixed': showPriceText }"
        :colon="!getLabel('retail_price', 'selling_currency')"
      >
        <template #label>
          <FormLabel
            :label="$t('81824')"
            :label-value="getLabel('retail_price', 'selling_currency')"
            :tips="$t('112107')"
          />
        </template>
        <PriceInput
          v-model="form.retail_price"
          :show-text="showPriceText"
          :prefix="priceData.selling_currency"
          :tips="fixedPriceModelTips"
          @edit="editRuleSetting('price')"
        />
      </a-form-model-item>
    </div>

    <a-form-model-item prop="cut_off_day" :label="$t('80903')" required class="form-item-long single-line">
      <a-select
        v-model="form.cut_off_day"
        :disabled="isSubSku"
        :placeholder="$t('global_select')"
        class="cutoff-select"
        @dropdownVisibleChange="(visible) => $emit('holdExpansion', visible)"
      >
        <a-select-option
          v-for="cutoffOption in cutoffDayList"
          :key="cutoffOption.value"
          :value="cutoffOption.value"
        >
          {{ cutoffOption.text }}
        </a-select-option>
      </a-select>
      <a-time-picker
        v-model="form.cut_off_date"
        class="time-picker"
        format="HH:mm"
        value-format="HH:mm"
        placeholder="hh:mm"
        :disabled="isSubSku"
        :default-open-value="defaultOpenValue"
        @openChange="(visible) => $emit('holdExpansion', visible)"
      />
    </a-form-model-item>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import PriceInput from '@activity/pages/package/units/new-components/price-input/index'
import FormLabel from '@activity/pages/package/units/new-components/form-label/index'
import { cutoffDayList, MAX_INVENTORY } from '@activity/pages/package/package_const.js'

export default {
  name: 'AddScheduleFormItem',
  inject: ['$t'],
  components: {
    PriceInput,
    FormLabel
  },
  model: {
    prop: 'data',
    event: 'change'
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    unitData: {
      type: Object,
      default: () => ({})
    },
    priceData: {
      type: Object,
      required: true
    },
    skuRuleSetting: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      MAX_INVENTORY,
      defaultOpenValue: moment('00:00', 'hh:mm'),
      cutoffDayList,
      inputStyle: {
        paddingBottom: '10px'
      }
    }
  },
  computed: {
    ...mapGetters(['isTour']),
    form: {
      get() {
        return this.data
      },
      set(v) {
        this.$emit('change', v)
      }
    },
    isMainSku() {
      return this.unitData.share_rel_ship === 2
    },
    isSubSku() {
      return this.unitData.share_rel_ship === 1
    },
    inventoryModel() {
      const { inv_model } = this.skuRuleSetting || {}

      return inv_model
    },
    showInventory() {
      const { inventoryModel } = this

      return inventoryModel === 3 || inventoryModel === 1
    },
    invCannotEdit() {
      const { inventoryModel } = this

      return inventoryModel === 3
    },
    isFixedPrice() {
      const { price_model } = this.skuRuleSetting || {}

      return price_model === 0
    },
    showPriceText() {
      const { isFixedPrice } = this

      return isFixedPrice
    },
    fixedPriceModelTips() {
      // 82676
      return klook.parseStr1(this.$t('111885'), {
        variable: `<span>${this.$t('111886')}</span>`
      })
    },
    invQuantityTips() {
      // 82675
      return klook.parseStr1(this.$t('111887'), {
        variable: `<span>${this.$t('111886')}</span>`
      })
    }
  },
  methods: {
    editRuleSetting(types) {
      klook.bus.$emit('showRuleSetting', {
        types,
        drawerConf: {
          placement: 'top',
          height: 320
        }
      })
    },
    getLabel(key, prefix) {
      if (this.isFixedPrice) {
        const pf = prefix ? _.get(this.form, prefix, '') + ' ' : ''
        return pf + _.get(this.form, key, '')
      }
      return ''
    }
  }
}
</script>

<style lang="scss" scoped>
.add-schedule-form-item-container {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-right: 180px;

  .form-item-row-box {
    display: flex;
    gap: 12px;

    ::v-deep .ant-form-item {
      flex: 0 0 212px;

      &.--is-fixed {
        flex: 1;
        max-width: 300px;
      }

      .text {
        line-height: 20px;
      }
    }
  }

  .form-item-long ::v-deep .ant-form-item-children {
    display: inline-flex;
    gap: 12px;
    width: 312px;
  }

  .single-line {
    width: 100%;
  }
}
</style>
