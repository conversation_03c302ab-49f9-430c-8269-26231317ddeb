<template>
  <div
    class="bulk-manage-schedule-form-container"
    :class="{
      '--is-selected': !!dateRange.length,
      '--is-hover': isHover
    }"
  >
    <div class="form-box">
      <a-form-model ref="form" label-align="left" :rules="rules" :model="form">
        <div class="title">{{ $t('89085') }}</div>

        <a-form-model-item :colon="false" class="form-item-inline">
          <div slot="label" class="form-item-label">
            {{ $t('27985') }}
          </div>
          <PartialDateRangePicker
            ref="partialDateRangePicker"
            v-model="dateRange"
            :exclude-date-list.sync="excludeDateList"
            @holdExpansion="onHoldExpansion"
            @change="onChangeDataRange"
          />
        </a-form-model-item>

        <template v-if="dateRange.length">
          <a-form-model-item v-if="excludeDateList.length" :colon="false" class="form-item-inline">
            <div slot="label" class="form-item-label">
              {{ $t('95932') }}
            </div>
            <div class="exclude-box">
              <div class="exclude-date-list">
                <a-tag v-for="date in excludeDateList" :key="date" closable @close="deleteExcludeDate(date)">
                  {{ date }}
                </a-tag>
              </div>
              <a-icon
                class="clear-exclude-date"
                type="close-circle"
                theme="filled"
                @click="clearExcludeDate"
              />
            </div>
          </a-form-model-item>

          <a-form-model-item :colon="false" prop="weekdays">
            <template #label></template>
            <WeekdaysTimeslot
              ref="weekdaysTimeslot"
              :data.sync="form.weekdays"
              :is-by-hours="isByHours"
              @validateField="onValidateField"
              @holdExpansion="onHoldExpansion"
            />
          </a-form-model-item>

          <AddScheduleFormItem
            v-if="BULK_MANAGE_SCHEDULE_KEYS.add.key === bulkType"
            ref="addScheduleFormItem"
            v-model="form"
            :unit-data="unitData"
            :price-data="priceData"
            :sku-rule-setting="skuRuleSetting"
            @holdExpansion="onHoldExpansion"
          />
        </template>
      </a-form-model>

      <span class="fold-mask"></span>

      <div class="actions">
        <a-button @click="onCancel">
          {{ $t('global_cancel') }}
        </a-button>
        <a-button :type="confirmBtnType" :disabled="!dateRange.length" @click="handleConfirm">
          {{ confirmBtnLabel }}
        </a-button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import { getDateRangeValues, getWeekdayValueByDayIdx } from '@activity/utils/index'
import { getCutOffDay, getCutOffTime, getRepeatTimeslot } from '@activity/pages/package/units/utils/index.js'
import { week } from '@activity/pages/package/package_const.js'
import { BULK_MANAGE_SCHEDULE_KEYS } from '@activity/pages/package/units/utils/index.js'

import PartialDateRangePicker from '@activity/components/partial-date-range-picker/index'
import WeekdaysTimeslot from './weekdaysTimeslot'
import AddScheduleFormItem from './addScheduleFormItem.vue'
import moment from 'moment'

export default {
  name: 'BatchDateManageForm',
  inject: ['$t'],
  components: {
    PartialDateRangePicker,
    WeekdaysTimeslot,
    AddScheduleFormItem
  },
  props: {
    unitData: {
      type: Object,
      default: () => ({})
    },
    globalDataState: {
      type: Object,
      default: () => {
        return {}
      }
    },
    isAdd: {
      type: Boolean,
      default: false
    },
    onConfirm: {
      type: Function,
      required: true
    },
    onCancel: {
      type: Function,
      required: true
    }
  },
  data() {
    this.BULK_MANAGE_SCHEDULE_KEYS = BULK_MANAGE_SCHEDULE_KEYS

    const numberValidate = (r, v, callback) => {
      if (this.isSubSku) {
        callback()
        return
      }
      if (!v) {
        callback(new Error(this.$t('global_please_input')))
        return
      }
      callback()
    }

    const priceValidate = (r, v, callback) => {
      const { isFixedPrice } = this
      if (isFixedPrice) {
        callback()
        return
      }

      if (v === undefined || v === '') {
        callback(new Error(this.$t('global_please_input')))
        return
      }
      callback()
    }

    const cutoffValidate = (r, v, callback) => {
      if (this.isSubSku) {
        callback()
        return
      }
      const { cut_off_day, cut_off_date } = this.form
      if ((!cut_off_day && cut_off_day !== 0) || !cut_off_date) {
        callback(new Error(this.$t('global_select')))
        return
      }
      callback()
    }

    const weekdaysValidate = (r, v, callback) => {
      const { weekdays } = this.form
      const noChoice = weekdays.every((weekday) => !weekday.checked)
      const notFilled = this.form.weekdays.some((item) => {
        if (item.checked) {
          return this.isByHours && !item.timeslots.length
        }

        return false
      })

      if (noChoice || notFilled) {
        callback(new Error(this.$t('global_select')))
        return
      }

      callback()
    }

    return {
      isHover: false,

      form: {
        cost_price: undefined,
        retail_price: undefined,
        inv_quantity: undefined,
        cut_off_day: undefined,
        cut_off_date: undefined,
        published: true,
        weekdays: []
      },
      priceData: {},

      rules: {
        cost_price: [
          {
            validator: priceValidate,
            trigger: 'change'
          }
        ],
        inv_quantity: [
          {
            validator: numberValidate,
            trigger: 'change'
          }
        ],
        retail_price: [
          {
            validator: priceValidate,
            trigger: 'change'
          }
        ],
        cut_off_day: [
          {
            validator: cutoffValidate,
            trigger: 'change'
          }
        ],
        weekdays: [
          {
            validator: weekdaysValidate,
            trigger: 'change'
          }
        ]
      }
    }
  },
  computed: {
    ...mapState({
      skuRuleSetting: 'skuRuleSetting'
    }),
    // Object.keys BULK_MANAGE_SCHEDULE_KEYS
    bulkType() {
      return this.$store.state.bulkManageSchedule.type
    },
    confirmBtnLabel() {
      return BULK_MANAGE_SCHEDULE_KEYS[this.bulkType]?.label ?? ''
    },
    confirmBtnType() {
      return [BULK_MANAGE_SCHEDULE_KEYS.delete.key, BULK_MANAGE_SCHEDULE_KEYS.deactivate.key].includes(
        this.bulkType
      )
        ? 'danger'
        : 'primary'
    },
    dateRange: {
      get() {
        return this.$store.state.bulkManageSchedule.dateRange
      },
      set(dateRange) {
        let dateRangeValues = getDateRangeValues(dateRange)
        this.setBulkManageSchedule({ dateRange, dateRangeValues })
      }
    },
    bulkDateRangeValues() {
      return this.$store.state.bulkManageSchedule.dateRangeValues
    },
    validWeekdayValues() {
      let list = []
      const length = this.dateRange.length

      if (length) {
        const xor = _.xor(this.bulkDateRangeValues, this.excludeDateList)

        for (let date of xor) {
          list = [...new Set([...list, getWeekdayValueByDayIdx(moment(date).day())])]
          if (list.length === 7) break
        }

        return list
      }

      return []
    },
    excludeDateList: {
      get() {
        return this.$store.state.bulkManageSchedule.excludeDateList
      },
      set(excludeDateList) {
        this.setBulkManageSchedule({ excludeDateList })
      }
    },
    isMainSku() {
      return this.unitData.share_rel_ship === 2
    },
    isSubSku() {
      return this.unitData.share_rel_ship === 1
    },
    isByHours() {
      const { skuRuleSetting } = this
      const schedule_type = skuRuleSetting?.schedule_type ?? ''

      return schedule_type === 1 && !this.showRepeatTimeslot
    },
    replaceTimeslotValues() {
      const { by_time_repeat_timeslot, repeat_day } = this.skuRuleSetting || {}

      return getRepeatTimeslot(by_time_repeat_timeslot, repeat_day)
    },
    isDiffTimeslot() {
      return this.replaceTimeslotValues.length > 1
    },
    isFixedPrice() {
      const { price_model } = this.skuRuleSetting || {}

      return price_model === 0
    }
  },
  watch: {
    'form.cut_off_date': {
      handler(v) {
        const form = this.$refs.form
        if (form) {
          form.validateField('cut_off_day')
        }
      }
    },
    priceData: {
      deep: true,
      immediate: true,
      handler(v) {
        const data = v || {}
        const { default_cut_off_time = '' } = data
        this.form = Object.assign({}, this.form, getCutOffDay(default_cut_off_time), data)
      }
    },
    validWeekdayValues: {
      deep: true,
      immediate: true,
      handler(newVal) {
        for (let item of this.form.weekdays) {
          const isValid = newVal.includes(item.value)
          Object.assign(item, {
            isValid,
            ...(isValid
              ? {}
              : {
                  checked: false,
                  timeslots: []
                })
          })
        }
      }
    }
  },
  beforeMount() {
    this.initPriceData()
  },
  methods: {
    ...mapMutations(['setBulkManageSchedule']),
    initPriceData() {
      const { globalDataState, skuRuleSetting, isSubSku } = this
      const data = globalDataState[ADMIN_API.act.get_calendar_by_sku_id] || {}
      const { default_cut_off_time, selling_currency, cost_currency } = data
      let {
        sku_fix_price,
        global_inv_quantity,
        repeat_day = [],
        merchant_currency,
        selling_currency: selling_currency_rules
      } = skuRuleSetting || {}
      const { cost_price, retail_price, retail_currency, cost_currency: cost_currency_rules } =
        sku_fix_price || {}

      let inv = global_inv_quantity > 0 ? global_inv_quantity : ''
      if (isSubSku) {
        inv = this.$t('package_inventory')
      }

      const weekdays = week
        .filter((item) => repeat_day.includes(item.value))
        .map((item) => {
          const result = this.replaceTimeslotValues.find((curr) => curr.groupList.includes(item.value))

          return {
            ...item,
            checked: false,
            timeslots: [],
            isValid: true,
            options: (result?.repeat_timeslot ?? []).map((item) => ({ label: item, value: item }))
          }
        })

      this.$set(this, 'priceData', {
        default_cut_off_time,
        cost_price,
        retail_price,
        inv_quantity: inv,
        selling_currency: selling_currency_rules || retail_currency || selling_currency,
        cost_currency: merchant_currency || cost_currency_rules || cost_currency,
        weekdays
      })
    },
    onChangeDataRange(data) {
      try {
        const [start] = data
        const year = moment(start).year()
        const month = moment(start).month() + 1
        const yearMonth = { year, month }

        if (!_.isEqual(this.preSetTheYearMonthData, yearMonth)) {
          this.preSetTheYearMonthData = yearMonth
          klook.bus.$emit('setTheYearMonth', yearMonth)
        }
      } catch (err) {
        //
      }
    },
    deleteExcludeDate(date) {
      this.excludeDateList = this.excludeDateList.filter((item) => item !== date)
    },
    clearExcludeDate() {
      this.excludeDateList = []
    },
    // 避免 hover height 失效
    onHoldExpansion(visible) {
      if (visible) {
        this.isHover = true
      } else {
        setTimeout(() => {
          this.isHover = false
        }, 2500)
      }
    },
    onValidateField(field) {
      this.$refs.form?.validateField?.(field)
    },
    handleConfirm() {
      this.$refs.form.validate((valid) => {
        if (!valid) {
          this.$message.warn('Please fill in the form')
          return
        }

        const { isSubSku } = this
        const { inv_model } = this.skuRuleSetting
        const { cut_off_day, cut_off_date, inv_quantity, cost_price, retail_price, weekdays } = this.form

        let data = {
          published: true,
          isByHours: this.isByHours,
          cut_off_time: isSubSku ? 0 : getCutOffTime({ cut_off_day, cut_off_date }),
          price: {
            cost_price: +cost_price,
            retail_price: +retail_price,
            note: ''
          },
          weekdays
        }

        if (inv_model !== 2) {
          data.inv_quantity = isSubSku ? 0 : +inv_quantity
        }

        this.onConfirm({ data })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../../../../../src/assets/css/lib/mixins';

.bulk-manage-schedule-form-container {
  position: fixed;
  top: 0;
  display: flex;
  height: 100%;
  align-items: flex-end;
  background-color: #fff;
  transition: max-height 0.5s ease-in-out;
  z-index: 2;

  &.--is-selected {
    height: auto !important;
    align-items: inherit;

    &:hover {
      max-height: 2000px !important;

      .fold-mask {
        opacity: 0;
      }
    }
  }

  &.--is-hover {
    max-height: 2000px !important;
  }

  .form-box {
    width: 100%;
    padding: 24px 20px;
    border-radius: 8px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.14);
    border: 1px solid #0091ff;
    overflow: hidden;

    .title {
      margin-bottom: 12px;
      font-weight: 600;
      font-size: 16px;
      line-height: 150%;
      color: #000;
    }
  }

  ::v-deep .ant-form {
    position: relative;
    flex: 1;
    max-height: calc(40vh - 48px);
    overflow: hidden auto;
    @include mixin-hover-display-scrollbar;
    /* .ant-form-item {
      .ant-form-item-label {
        flex: 0 0 160px;
        & > label.ant-form-item-no-colon::after {
          display: none;
        }
      }
    } */
  }

  .fold-mask {
    position: absolute;
    bottom: 1px;
    width: 100%;
    height: 40px;
    opacity: 1;
    background-image: linear-gradient(hsla(0, 0%, 100%, 0.1), #ffffff);
    transition: opacity 2s ease-in-out 1s;
  }

  .form-item-inline {
    display: flex;
    max-width: calc(100% - 272px);
    align-items: center;

    ::v-deep .ant-form-item-label {
      flex: 0 0 160px;
      padding: 0 12px 0 0;

      & > label.ant-form-item-no-colon::after {
        display: none;
      }
    }
  }

  .form-item-label {
    flex: 0 0 160px;
  }

  .exclude-box {
    display: flex;
    align-items: center;

    .clear-exclude-date {
      margin-left: 12px;
      color: #ff4d4f;
      opacity: 0;
      cursor: pointer;
      transition: opacity 0.6s;
    }

    &:hover .clear-exclude-date {
      opacity: 1;
    }
  }

  .exclude-date-list {
    display: flex;
    flex-wrap: nowrap;
    width: 520px;
    max-height: 36px;
    padding: 4px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: border-color 0.6s;
    overflow: hidden;
    gap: 4px;

    @include mixin-hover-display-scrollbar;
  }

  .exclude-box:hover .exclude-date-list {
    flex-wrap: wrap;
    border-color: #437dff;
    max-height: 110px;
    overflow: hidden auto;
  }

  .actions {
    position: absolute;
    bottom: 40px;
    right: 30px;
    display: inline-flex;
    align-items: flex-end;
    gap: 8px;
  }
}
</style>
