<template>
  <div class="bulk-manage-schedule-container">
    <div
      v-if="maskPosition.left"
      class="mask --left"
      :style="{
        width: maskPosition.left + 'px'
      }"
    ></div>
    <div
      v-if="maskPosition.right"
      class="mask --right"
      :style="{
        width: maskPosition.right + 'px'
      }"
    ></div>
    <div
      v-if="maskPosition.top"
      class="mask --top"
      :style="{
        height: maskPosition.top + 'px'
      }"
    ></div>
    <div
      v-if="maskPosition.bottom"
      class="mask --bottom"
      :style="{
        height: maskPosition.bottom + 'px'
      }"
    ></div>

    <Form
      :unit-data="unitData"
      :global-data-state="globalDataState"
      :is-add="isAdd"
      :on-confirm="onConfirm"
      :on-cancel="onCancel"
      :style="contentStyle"
    />
  </div>
</template>

<script>
import Form from './form.vue'

export default {
  name: 'MultipleDateSelection',
  components: {
    Form
  },
  provide() {
    return {
      $t: (...data) => {
        return __(...data)
      }
    }
  },
  props: {
    maskPosition: {
      type: Object,
      default: () => ({})
    },
    contentStyle: {
      type: Object,
      default: () => ({})
    },
    unitData: {
      type: Object,
      default: () => ({})
    },
    globalDataState: {
      type: Object,
      default: () => {
        return {}
      }
    },
    isAdd: {
      type: Boolean,
      default: false
    },
    onConfirm: {
      type: Function,
      default: () => {}
    },
    onCancel: {
      type: Function,
      default: () => {}
    }
  }
}
</script>

<style lang="scss">
.bulk-manage-schedule-container {
  position: relative;
  /* drawer z-index: 111 */
  z-index: 100;

  .mask {
    position: fixed;
    background-color: #fff;
    opacity: 0.85;

    &.--left {
      top: 0;
      left: 0;
      height: 100vh;
    }

    &.--right {
      top: 0;
      right: 0;
      height: 100vh;
    }

    &.--top {
      width: 100vw;
      top: 0;
      left: 0;
    }

    &.--bottom {
      bottom: 0;
      left: 0;
      width: 100vw;
    }
  }
}
</style>
