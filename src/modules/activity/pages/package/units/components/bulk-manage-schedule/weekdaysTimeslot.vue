<template>
  <div class="weekdays-timeslot-container">
    <p>{{ $t('95933') }}</p>
    <ul class="weekday-list">
      <template v-for="weekday of weekdays">
        <li
          v-if="weekday.isValid"
          :key="weekday.value"
          v-tooltip="{
            visible: !isCNLang,
            content: weekday.label
          }"
          class="weekday-item"
          :class="{ 'is-checked': weekday.checked }"
          @click="handleCheckWeekday(weekday)"
        >
          <span :class="{ 'font-shrink-max': isThLang, 'font-shrink': fontShrink }">{{
            fmtWeekdayInitials(weekday.label)
          }}</span>
        </li>
      </template>
    </ul>

    <ul v-if="isByHours" class="weekday-timeslot-list">
      <template v-for="weekday in weekdays">
        <li v-if="weekday.checked" :key="weekday.key" class="timeslot-item">
          <div class="timeslot-item-label">{{ weekday.label }}</div>
          <div span="16">
            <AllSelector
              v-model="weekday.timeslots"
              mode="multiple"
              allow-clear
              show-arrow
              :class="{
                'is-filled': !!weekday.timeslots.length
              }"
              :should-spm="false"
              :options="weekday.options"
              @dropdownVisibleChange="dropdownVisibleChange"
            ></AllSelector>
          </div>
        </li>
      </template>
    </ul>
  </div>
</template>

<script>
import AllSelector from '@activity/pages/package/units/bulkEdit/AllSelector.vue'
const WEEKDAY_LIST = [
  { label: __('21890'), value: 'Sun' },
  { label: __('18064'), value: 'Mon' },
  { label: __('26613'), value: 'Tue' },
  { label: __('17965'), value: 'Wed' },
  { label: __('18288'), value: 'Thu' },
  { label: __('18331'), value: 'Fri' },
  { label: __('18107'), value: 'Sat' }
]

export default {
  name: 'WeekdaysTimeslot',
  inject: ['$t'],
  components: { AllSelector },
  props: {
    data: {
      type: Array,
      required: true
    },
    isByHours: {
      type: Boolean,
      default: false
    }
  },
  data() {
    this.WEEKDAY_LIST = WEEKDAY_LIST

    return {}
  },
  computed: {
    isThLang() {
      return ['th'].includes(window.KLK_LANG)
    },
    isCNLang() {
      return ['zh-CN', 'zh-TW'].includes(window.KLK_LANG)
    },
    isEnLang() {
      return ['en'].includes(window.KLK_LANG)
    },
    fontShrink() {
      return this.isThLang || this.isCNLang || this.isEnLang
    },
    weekdays: {
      get() {
        return this.data
      },
      set() {}
    }
  },
  watch: {
    weekdays: {
      deep: true,
      handler() {
        this.$emit('validateField', 'weekdays')
      }
    }
  },
  methods: {
    fmtWeekdayInitials(label) {
      if (this.isCNLang) {
        return label.substr(0, 2).toUpperCase()
      } else if (this.isThLang) {
        return label.substr(3).toUpperCase()
      } else if (this.isEnLang) {
        return label.substr(0, 3)
      }

      return label.substr(0, 1).toUpperCase()
    },
    handleCheckWeekday({ value }) {
      const newData = this.weekdays.map((weekday) => {
        if (weekday.value === value) {
          const checked = !weekday.checked

          return {
            ...weekday,
            checked,
            timeslots: checked ? [...weekday.options.map((item) => item.value)] : []
          }
        }

        return weekday
      })

      this.$emit('update:data', newData)
    },
    dropdownVisibleChange(visible) {
      this.$emit('holdExpansion', visible)
    }
  }
}
</script>

<style lang="scss" scoped>
.weekdays-timeslot-container {
  .weekday-list {
    display: flex;
    gap: 20px;

    .weekday-item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      line-height: 36px;
      text-align: center;
      font-weight: 400;
      font-size: 16px;
      color: #8c8c8c;
      background-color: #f0f0f0;
      border-radius: 50%;
      user-select: none;
      overflow: hidden;
      transition: background-color 0.5s ease-in-out, color 0.5s ease-in-out;
      cursor: pointer;

      &:hover {
        opacity: 0.75;
      }

      .font-shrink {
        font-size: 12px;
      }

      .font-shrink-max {
        transform: scale(0.8);
      }

      &.is-checked {
        color: #fff;
        background-color: #437dff;
      }
    }
  }

  .weekday-timeslot-list {
    display: flex;
    flex-direction: column;
    margin-top: 20px;

    .timeslot-item {
      display: flex;

      &-label {
        width: 160px;
      }

      .is-filled {
        ::v-deep .ant-select {
          .ant-select-selection {
            border-color: #d9d9d9;
          }

          .ant-select-arrow {
            color: rgba(0, 0, 0, 0.25);
          }
        }
      }

      ::v-deep .ant-select {
        width: 342px;
      }
    }
  }
}
</style>
