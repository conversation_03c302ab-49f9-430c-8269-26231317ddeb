<template>
  <a-modal :title="$t('30292')" :visible="visible" :footer="false" @cancel="handleCancel">
    <p>{{ $t('30293') }}</p>
    <a-select
      v-model="currentValue"
      :placeholder="$t('package_please_choose')"
      show-search
      style="width: 100%;"
      :filter-option="false"
    >
      <a-select-option v-for="item in options" :key="item.package_id" :value="item.package_id">
        {{ item.package_id }} - {{ item.package_name }}
      </a-select-option>
    </a-select>
    <footer class="footer">
      <a-button @click="handleCancel">{{ $t('29166') }}</a-button>
      <a-button type="primary" :disabled="!currentValue" @click="handleConfirm">{{
        $t('taxonomy_dest_confirm')
      }}</a-button>
    </footer>
  </a-modal>
</template>

<script>
import { mapState } from 'vuex'

export default {
  model: {
    event: 'change',
    prop: 'value'
  },
  props: {
    value: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      options: [],
      currentValue: ''
    }
  },
  watch: {
    value: {
      immediate: true,
      handler() {
        this.getSkuModels()
      }
    }
  },
  computed: {
    ...mapState({
      skuModel: (state) => state.skuModel
    }),
    actId() {
      return +this.$route.params.id
    },
    packageId() {
      return +this.$route.query.package_id
    },
    visible: {
      get() {
        return this.value
      },
      set(v) {
        return this.$emit('change', v)
      }
    }
  },
  methods: {
    async getSkuModels() {
      const list =
        (await ajax.get(ADMIN_API.act.get_sku_models_by_activity_id, {
          params: {
            activity_id: this.actId
          }
        })) || []
      // 只能copy相同类型的套餐
      const { package_ticket_type, unschedule } = this.skuModel
      this.options = list.filter(
        (item) =>
          item.package_id !== this.packageId &&
          item.package_ticket_type === package_ticket_type &&
          item.unschedule === unschedule
      )
    },
    handleCancel() {
      this.visible = false
    },
    handleConfirm() {
      this.$emit(
        'confirmCopy',
        _.find(this.options, {
          package_id: this.currentValue
        })
      )
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  ::v-deep .ant-btn:first-child {
    margin-right: 16px;
  }
}
</style>
