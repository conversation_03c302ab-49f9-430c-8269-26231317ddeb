import PackageCopyForm from '@activity/components/package-copy-form/index.vue'
import { isMerchant } from '@/env'
import { mapGetters } from 'vuex'

export default {
  components: {
    PackageCopyForm
  },
  data() {
    return {
      showCopyModal: false,
      copyType: undefined,
      allPackages: []
    }
  },
  computed: {
    ...mapGetters(['isSimOrWifi']),
    options() {
      const { copyType, copyOptions } = this
      if (!copyType) {
        return []
      }
      if (copyType === 1) {
        return copyOptions.filter((item) => {
          const { copy_sku_model_forbid } = item
          return !copy_sku_model_forbid
        })
      }
      return copyOptions
    },
    activityId() {
      return +this.$route.params.id || 0
    },
    packageId() {
      return +this.$route.query.package_id
    },
    currentPackage() {
      return this.allPackages.find((item) => item.package_id === this.packageId)
    },
    copyOptions() {
      return this.allPackages.filter((item) => {
        const { package_ticket_type, unschedule } = this.currentPackage
        return (
          item.package_id !== this.packageId &&
          (item.package_ticket_type === package_ticket_type || item.package_ticket_type === -1) &&
          unschedule === item.unschedule
        )
      })
    }
  },
  methods: {
    handleShowCopy() {
      this.showCopyModal = true
      this.getSkuModels()
    },
    async getSkuModels() {
      if (this.copyOptions.length) {
        return
      }
      const list =
        (await ajax.get(ADMIN_API.act.get_sku_models_by_activity_id, {
          params: {
            activity_id: this.activityId
          }
        })) || []

      this.allPackages = list.map((item) => {
        const { package_id, package_name } = item
        return {
          ...item,
          key: package_id,
          value: `${package_id}-${package_name}`
        }
      })
    },
    async handleCopyOk() {
      const packageCopyForm = this.$refs.packageCopyForm
      const validate = await packageCopyForm.validateForm()
      if (!validate) {
        return
      }
      const data = packageCopyForm.getData()
      const postData = this.getPostData(data)

      this.setCopyData(postData, data.type === 2)
    },
    handleChange({ type }) {
      this.copyType = type
    },
    getPostData({ type, value }) {
      const activity_id = this.activityId
      return type === 1
        ? { target_package_ids: value, from_package_id: this.packageId, activity_id }
        : { target_package_ids: [this.packageId], from_package_id: value, activity_id }
    },
    async setCopyData(data, isCopyFrom) {
      const res = await ajax.postBody(
        ADMIN_API.act.copy_sku_model,
        {
          data
        },
        {
          loading: true
        }
      )
      if (res.success) {
        this.showCopyModal = false
        const result = res?.result ?? []
        const { failed, success } = result.reduce(
          (acc, curr) => {
            const success = curr.success
            if (success) {
              acc.success.push(curr)
            } else {
              acc.failed.push(curr)
            }
            return acc
          },
          { success: [], failed: [] }
        )
        if (failed.length) {
          const message = failed.map((item) => {
            return (
              <div>
                {item.package_id}: {item.err_message || ''}
              </div>
            )
          })
          this.$warning({
            title: this.$t('act_copy_to_other_pkg'),
            content: () => {
              return (
                <div>
                  <h5>{this.$t('104035', { package_id: '' })}</h5>
                  <div>{message}</div>
                </div>
              )
            }
          })
        } else {
          if (isCopyFrom) {
            this.$message.success(this.$t('104037', { package_id: data.from_package_id }))
            if (isMerchant) {
              this.updateSuccessCb()
              this.reloadPage2provide()
            } else {
              const { packageId } = this
              await this.$store.dispatch('actionGetSkuModel', { packageId })
              klook.bus.$emit('refreshUnitList')
              this.$nextTick(() => {
                this.close()
              })
            }
          } else {
            this.$message.success(this.$t('104034', { number: success.length }))
          }
        }
      } else {
        this.$message.error(this.$t('104036'))
      }
    }
  }
}
