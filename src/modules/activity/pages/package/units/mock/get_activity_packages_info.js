export default {
  package_info: [
    {
      package_id: 75880,
      package_type: 1,
      data_status: 0,
      main_package_id: 0,
      publish_status: 0,
      priority: 0,
      tips: '',
      package_local: [
        {
          language_type: 'en_US',
          name: 'yangzhou开发用勿动2',
          subname: ''
        }
      ]
    },
    {
      package_id: 75900,
      package_type: 1,
      data_status: 0,
      main_package_id: 0,
      publish_status: 0,
      priority: 0,
      tips: '',
      package_local: [
        {
          language_type: 'en_US',
          name: 'yangzhou开发用勿动3',
          subname: ''
        }
      ]
    },
    {
      package_id: 75737,
      package_type: 1,
      data_status: 0,
      main_package_id: 0,
      publish_status: 0,
      priority: 0,
      tips: '',
      package_local: [
        {
          language_type: 'en_US',
          name: 'yangzhou开发用勿动1',
          subname: ''
        }
      ]
    }
  ]
}
