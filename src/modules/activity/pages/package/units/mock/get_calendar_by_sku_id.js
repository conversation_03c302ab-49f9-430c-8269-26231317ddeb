export default {
  sku_id: 123456, // sku ID,
  destination_now: '2021-06-02 00:00:00', // 活动目的地当前时间 格式 2021-01-01 00:00:00,
  inv_model: 1, // 1: 日历库存; 2: 无限库存; 3: 全局库存; 4: 时间段库存,
  pricing_model: 1, // 套餐价格模型, -1: 未定义, 0: 固定价格, 1: 日历价格,
  calendar: [
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: true,
      published: true,
      start_time: '2021-05-30 00:01:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: {
        cost_currency: 'HKD', // 成本价货币,
        cost_price: 123, // 成本价,
        retail_price: 123, // 市场价,
        selling_currency: 'MXN', // 基准价货币,
        selling_price: 123, // 基准价,
        special_selling_price: [
          // 特殊货币售卖价,
          {
            currency: 'MXN', // 货币,
            price: 123 // 金额,
          }
        ]
      }
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: true,
      published: true,
      start_time: '2021-05-30 00:10:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: {
        cost_currency: 'HKD', // 成本价货币,
        cost_price: 123, // 成本价,
        retail_price: 123, // 市场价,
        selling_currency: 'MXN', // 基准价货币,
        selling_price: 123, // 基准价,
        special_selling_price: [
          // 特殊货币售卖价,
          {
            currency: 'MXN', // 货币,
            price: 123 // 金额,
          }
        ]
      }
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: true,
      published: true,
      start_time: '2021-05-30 00:20:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: true,
      published: true,
      start_time: '2021-05-31 00:01:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: {
        cost_currency: 'HKD', // 成本价货币,
        cost_price: 123, // 成本价,
        retail_price: 123, // 市场价,
        selling_currency: 'MXN', // 基准价货币,
        selling_price: 123, // 基准价,
        special_selling_price: [
          // 特殊货币售卖价,
          {
            currency: 'MXN', // 货币,
            price: 123 // 金额,
          }
        ]
      }
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: true,
      published: true,
      start_time: '2021-05-31 00:10:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: true,
      published: true,
      start_time: '2021-05-31 00:20:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: true,
      published: true,
      start_time: '2021-06-01 00:01:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: true,
      published: true,
      start_time: '2021-06-01 00:10:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: {
        cost_currency: 'HKD', // 成本价货币,
        cost_price: 123, // 成本价,
        retail_price: 123, // 市场价,
        selling_currency: 'MXN', // 基准价货币,
        selling_price: 123, // 基准价,
        special_selling_price: [
          // 特殊货币售卖价,
          {
            currency: 'MXN', // 货币,
            price: 123 // 金额,
          }
        ]
      }
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: true,
      published: true,
      start_time: '2021-06-01 00:20:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: true,
      published: true,
      start_time: '2021-06-03 00:01:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: true,
      published: true,
      start_time: '2021-06-03 00:10:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: true,
      published: true,
      start_time: '2021-06-03 00:20:00',
      cut_off_time: 3600,
      inv_quantity: 1234,
      sales: 123,
      price: {
        cost_currency: 'HKD', // 成本价货币,
        cost_price: 123, // 成本价,
        retail_price: 123, // 市场价,
        selling_currency: 'MXN', // 基准价货币,
        selling_price: 123, // 基准价,
        special_selling_price: [
          // 特殊货币售卖价,
          {
            currency: 'MXN', // 货币,
            price: 123 // 金额,
          }
        ]
      }
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: true,
      published: true,
      start_time: '2021-06-04 00:01:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: true,
      published: true,
      start_time: '2021-06-04 00:10:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: {
        cost_currency: 'HKD', // 成本价货币,
        cost_price: 123, // 成本价,
        retail_price: 123, // 市场价,
        selling_currency: 'MXN', // 基准价货币,
        selling_price: 123, // 基准价,
        special_selling_price: [
          // 特殊货币售卖价,
          {
            currency: 'MXN', // 货币,
            price: 123 // 金额,
          }
        ]
      }
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: true,
      published: true,
      start_time: '2021-06-04 00:20:00',
      cut_off_time: 3600,
      inv_quantity: 1000,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: true,
      published: true,
      start_time: '2021-06-05 00:01:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: true,
      published: true,
      start_time: '2021-06-05 00:10:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: true,
      published: true,
      start_time: '2021-06-05 00:20:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: false,
      price_can_edit: false,
      visible: true,
      published: true,
      start_time: '2021-06-06 00:01:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: {
        cost_currency: 'HKD', // 成本价货币,
        cost_price: 123, // 成本价,
        retail_price: 123, // 市场价,
        selling_currency: 'MXN', // 基准价货币,
        selling_price: 123, // 基准价,
        special_selling_price: [
          // 特殊货币售卖价,
          {
            currency: 'MXN', // 货币,
            price: 123 // 金额,
          }
        ]
      }
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: true,
      start_time: '2021-06-06 00:10:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: true,
      start_time: '2021-06-06 00:20:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: true,
      start_time: '2021-06-07 00:01:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: true,
      start_time: '2021-06-07 00:10:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: {
        cost_currency: 'HKD', // 成本价货币,
        cost_price: 123, // 成本价,
        retail_price: 123, // 市场价,
        selling_currency: 'MXN', // 基准价货币,
        selling_price: 123, // 基准价,
        special_selling_price: [
          // 特殊货币售卖价,
          {
            currency: 'MXN', // 货币,
            price: 123 // 金额,
          }
        ]
      }
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: false,
      start_time: '2021-06-07 00:20:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: true,
      start_time: '2021-06-08 00:01:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: true,
      start_time: '2021-06-08 00:10:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: {
        cost_currency: 'HKD', // 成本价货币,
        cost_price: 123, // 成本价,
        retail_price: 123, // 市场价,
        selling_currency: 'MXN', // 基准价货币,
        selling_price: 123, // 基准价,
        special_selling_price: [
          // 特殊货币售卖价,
          {
            currency: 'MXN', // 货币,
            price: 123 // 金额,
          }
        ]
      }
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: true,
      start_time: '2021-06-08 00:20:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: true,
      start_time: '2021-06-10 00:01:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: true,
      start_time: '2021-06-10 00:10:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: true,
      start_time: '2021-06-10 00:20:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: {
        cost_currency: 'HKD', // 成本价货币,
        cost_price: 123, // 成本价,
        retail_price: 123, // 市场价,
        selling_currency: 'MXN', // 基准价货币,
        selling_price: 123, // 基准价,
        special_selling_price: null
      }
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: false,
      start_time: '2021-06-11 00:01:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: false,
      start_time: '2021-06-11 00:10:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: false,
      start_time: '2021-06-11 00:20:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: false,
      start_time: '2021-06-12 00:01:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: false,
      start_time: '2021-06-12 00:10:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: {
        cost_currency: 'HKD', // 成本价货币,
        cost_price: 123, // 成本价,
        retail_price: 123, // 市场价,
        selling_currency: 'MXN', // 基准价货币,
        selling_price: 123, // 基准价,
        special_selling_price: null
      }
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: false,
      start_time: '2021-06-12 00:20:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: false,
      start_time: '2021-06-13 00:01:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: false,
      start_time: '2021-06-13 00:10:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: false,
      start_time: '2021-06-13 00:20:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: {
        cost_currency: 'HKD', // 成本价货币,
        cost_price: 123, // 成本价,
        retail_price: 123, // 市场价,
        selling_currency: 'MXN', // 基准价货币,
        selling_price: 123, // 基准价,
        special_selling_price: [
          // 特殊货币售卖价,
          {
            currency: 'MXN', // 货币,
            price: 123 // 金额,
          }
        ]
      }
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: false,
      start_time: '2021-06-14 00:01:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 2,
      price: {
        cost_currency: 'HKD', // 成本价货币,
        cost_price: 123, // 成本价,
        retail_price: 123, // 市场价,
        selling_currency: 'MXN', // 基准价货币,
        selling_price: 123, // 基准价,
        special_selling_price: [
          // 特殊货币售卖价,
          {
            currency: 'MXN', // 货币,
            price: 123 // 金额,
          }
        ]
      }
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: false,
      start_time: '2021-06-14 00:10:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    },
    {
      is_empty: true,
      inv_can_edit: true,
      price_can_edit: true,
      visible: false,
      published: false,
      start_time: '2021-06-14 00:20:00',
      cut_off_time: 3600,
      inv_quantity: 0,
      sales: 123,
      price: null
    }
  ]
}
