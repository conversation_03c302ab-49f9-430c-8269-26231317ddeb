export default {
  package_id: 12,
  units: [
    {
      inv_model: 1,
      local: [
        // unit name,
        {
          language: 'en_US',
          unit_name: 'Person'
        }
      ],
      max_age_range: 0, // 年龄最大值,
      is_from_trip_api: false,
      max_num: 0, // 最大购买,
      min_age_range: 0, // 年龄最小值,
      min_num: 0, // 最小购买,
      price_model: 1, // 价格模型 0: 固定价格； 1: 日历价格,
      published: false, // 发布状态true: 发布，false: 下架,
      required: 0, // 是否为必,
      share_rel_ship: 0, // 0 无共享， 1 子sku, 2 主sku,
      sku_id: 822428019999, // sku id,
      step: 'sku_share_setting', // 完成进度,
      unit_type: 1, // unit类型,0: adult 1: child 2: infant 3: youth 4: senior 5: other,
      use_customized_name: false
    },
    {
      inv_model: 1,
      local: [
        // unit name,
        {
          language: 'en_US',
          unit_name: 'Person'
        }
      ],
      max_age_range: 0, // 年龄最大值,
      is_from_trip_api: false,
      max_num: 0, // 最大购买,
      min_age_range: 0, // 年龄最小值,
      min_num: 0, // 最小购买,
      price_model: 1, // 价格模型 0: 固定价格； 1: 日历价格,
      published: false, // 发布状态true: 发布，false: 下架,
      required: 0, // 是否为必,
      share_rel_ship: 0, // 0 无共享， 1 子sku, 2 主sku,
      sku_id: 822428019910, // sku id,
      step: '', // 完成进度,
      unit_type: 2, // unit类型,0: adult 1: child 2: infant 3: youth 4: senior 5: other,
      use_customized_name: false
    }
  ]
}
