<template>
  <div class="header-input-container">
    <span class="header-text">
      {{ title }}
      <a-icon v-if="openEdit && !isActive" type="edit" class="edit-btn" @click="handle2Edit(true)" />
    </span>

    <div v-if="isActive" class="header-input">
      <a-input slot="customRenderInv" v-model="currValue" class="table-input" @keyup.native="onKeyUp" />
      <a-icon type="close-circle" theme="filled" @click="handle2Edit(false)" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'HeaderInput',
  props: {
    title: {
      type: String,
      required: true
    },
    field: {
      type: String,
      required: true
    },
    currentEditField: {
      type: String,
      required: true
    },
    formatValue: {
      type: Function,
      required: true
    },
    openEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currValue: ''
    }
  },
  computed: {
    isActive() {
      return this.currentEditField === this.field
    }
  },
  watch: {
    openEdit(val) {
      if (!val) {
        this.handle2Edit(false)
      }
    },
    isActive() {
      this.currValue = ''
    }
  },
  methods: {
    handle2Edit(flag) {
      this.$emit('update:currentEditField', flag ? this.field : '')
    },
    onKeyUp() {
      this.currValue = this.formatValue?.(this.currValue) || this.currValue
      this.emitChange()
    },
    emitChange: _.debounce(function emitChange() {
      this.$emit('change', {
        field: this.field,
        value: this.currValue
      })
    }, 30)
  }
}
</script>

<style lang="scss" scoped>
.header-input-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0 11px;

  .edit-btn {
    color: #437dff;
  }

  .header-text {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .header-input {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}
</style>
