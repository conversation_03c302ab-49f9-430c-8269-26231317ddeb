<template>
  <a-table
    class="bulk-edit-table-container"
    :columns="currColumns"
    :data-source="tableData"
    :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
    :pagination="false"
    :scroll="{ x: 736, y: 384 }"
  >
    <HeaderInput
      slot="customInvTitle"
      title="Inventory"
      field="inv_quantity"
      :current-edit-field.sync="currentEditData.field"
      :format-value="formatInv"
      :open-edit="!!selectedRowKeys.length"
      @change="changeHeaderValue"
    />
    <span
      slot="customRenderInv"
      slot-scope="text, row, index"
      class="table-input-box"
      :class="{
        'has-error': invalidCellList.includes(`row-${index}-inv_quantity`)
      }"
    >
      <a-input
        v-model="row.inv_quantity"
        class="table-input"
        @change="onChange(index, 'inv_quantity')"
        @keyup.native="changeInv(row)"
      />
    </span>

    <HeaderInput
      slot="customCostTitle"
      title="Cost price"
      field="cost_price"
      :current-edit-field.sync="currentEditData.field"
      :format-value="formatPrice"
      :open-edit="!!selectedRowKeys.length"
      @change="changeHeaderValue"
    />
    <span
      slot="customRenderCost"
      slot-scope="text, row, index"
      class="table-input-box"
      :class="{
        'has-error': invalidCellList.includes(`row-${index}-cost_price`)
      }"
    >
      <a-input
        v-model="row.cost_price"
        class="table-input"
        @change="onChange(index, 'cost_price')"
        @keyup.native="changePrice(row, 'cost_price')"
      />
    </span>

    <HeaderInput
      slot="customRetailTitle"
      title="Retail price"
      field="retail_price"
      :current-edit-field.sync="currentEditData.field"
      :format-value="formatPrice"
      :open-edit="!!selectedRowKeys.length"
      @change="changeHeaderValue"
    />
    <span
      slot="customRenderRetail"
      slot-scope="text, row, index"
      class="table-input-box"
      :class="{
        'has-error': invalidCellList.includes(`row-${index}-retail_price`)
      }"
    >
      <a-input
        v-model="row.retail_price"
        class="table-input"
        @change="onChange(index, 'retail_price')"
        @keyup.native="changePrice(row, 'retail_price')"
      />
    </span>
  </a-table>
</template>

<script>
import { round2Decimal } from '@activity/utils'
import { MAX_PRICE, MAX_INVENTORY } from '@activity/pages/package/package_const.js'
import HeaderInput from './headerInput.vue'

const columns = [
  {
    title: 'Schedule',
    dataIndex: 'schedule',
    key: 'schedule'
  },
  {
    dataIndex: 'inv_quantity',
    key: 'inv_quantity',
    slots: { title: 'customInvTitle' },
    scopedSlots: { customRender: 'customRenderInv' }
  },
  {
    dataIndex: 'cost_price',
    key: 'cost_price',
    slots: { title: 'customCostTitle' },
    scopedSlots: { customRender: 'customRenderCost' }
  },
  {
    dataIndex: 'retail_price',
    key: 'retail_price',
    slots: { title: 'customRetailTitle' },
    scopedSlots: { customRender: 'customRenderRetail' }
  }
]

export default {
  name: 'BulkEditTable',
  components: {
    HeaderInput
  },
  props: {
    tableData: {
      type: Array,
      required: true
    },
    fields: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      selectedRowKeys: [],

      currentEditData: {
        field: '',
        value: ''
      },

      invalidCellList: [] // string[]: row-{rowIndex}-{fieldKey}
    }
  },
  computed: {
    currColumns() {
      return columns.filter((item) => item.key === 'schedule' || this.fields.includes(item.key))
    }
  },
  methods: {
    onChange(index, field) {
      const value = `row-${index}-${field}`
      if (this.tableData[index][field] === void 0) {
        this.invalidCellList.push(value)
      } else {
        this.invalidCellList = this.invalidCellList.filter((item) => item !== value)
      }
    },
    validator() {
      let invalidCellList = []
      const { tableData, fields } = this
      tableData.forEach((item, index) => {
        for (let field of fields) {
          if (item[field] === void 0) {
            invalidCellList.push(`row-${index}-${field}`)
          }
        }
      })

      this.$set(this, 'invalidCellList', invalidCellList)

      return !invalidCellList.length
    },
    changeHeaderValue({ field, value }) {
      const tableData = this.tableData.map((item, index) => {
        if (this.selectedRowKeys.includes(index)) {
          item[field] = value
        }

        return item
      })

      this.$emit('update:tableData', tableData)
    },
    handleSetEditField(field) {
      Object.assign(this.currentEditData, {
        field,
        value: ''
      })
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    formatInv(value) {
      value = String(value).replace(/[^\d]/g, '')

      return +value > MAX_INVENTORY ? MAX_INVENTORY : value
    },
    changeInv(item) {
      item.inv_quantity = this.formatInv(item.inv_quantity)
    },
    formatPrice(value) {
      value = round2Decimal(value)

      return +value > MAX_PRICE ? MAX_PRICE : value
    },
    changePrice(item, field) {
      item[field] = this.formatPrice(item[field])
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../../../../../src/assets/css/lib/mixins';

.bulk-edit-table-container {
  width: 752px;

  .table-input-box {
    position: relative;

    .table-input {
      border-color: transparent;
      &:focus {
        border-color: transparent;
        outline: none;
        box-shadow: none;
      }
    }

    &.has-error {
      .table-input {
        border-radius: 0;
        border-bottom: 1px solid #f5222d;
      }
    }
  }

  ::v-deep {
    .ant-table {
      padding: 10px;
      background-color: #f0f0f0;
      border-radius: 6px;
    }

    .ant-table-fixed-header > .ant-table-content > .ant-table-scroll > .ant-table-body {
      overflow-x: hidden !important;
      @include mixin-hover-display-scrollbar;
    }

    .ant-table-thead > tr > th .ant-table-header-column {
      width: 100%;
    }
  }
}
</style>
