<template>
  <div class="popover-wrap">
    <PopoverContainer
      :title="title"
      :disabled="disabledConfirm"
      @close="$emit('close')"
      @confirm="handleSave"
    >
      <a-form-model ref="form" layout="vertical" :rules="rules" :model="form" class="form">
        <a-form-model-item v-if="!disabledInventory" prop="inv_quantity" required>
          <template #label>
            <FormLabel
              :label="$t('package_inventory')"
              :label-value="invCannotEdit ? form.inv_quantity : ''"
            />
          </template>
          <PriceInput
            v-model="form.inv_quantity"
            :disabled="isSubSku"
            :show-text="invCannotEdit"
            :is-int="true"
            :max-value="MAX_INVENTORY"
            :tips="invQuantityTips"
            :text-style="inputStyle"
            :tooltip-title="isSubSku ? $t('27805') : ''"
            :placeholder="isSubSku ? $t('package_inventory') : ''"
            @edit="editRuleSetting('inventory')"
            @change="handleChange('inv_quantity')"
          />
        </a-form-model-item>
        <a-form-model-item prop="cost_price" required>
          <template #label>
            <FormLabel
              :label="$t('81823')"
              :label-value="getLabel('cost_price', 'cost_currency')"
              :tips="$t('112108')"
            />
          </template>
          <PriceInput
            v-model="form.cost_price"
            :prefix="getPrefix('cost_currency')"
            :show-text="showPriceText"
            :disabled-edit="disabledEdit"
            :disabled-edit-placeholder="$t('global_please_input')"
            :tips="fixedPriceModelTips"
            :text-style="inputStyle"
            :disabled="disabledPriceChange"
            @edit="editRuleSetting('price')"
            @fill="fillPrice"
            @change="handleChange('cost_price')"
          />
        </a-form-model-item>
        <a-form-model-item v-if="!isTour" prop="retail_price" required>
          <template #label>
            <FormLabel
              :label="$t('81824')"
              :label-value="getLabel('retail_price', 'selling_currency')"
              :tips="$t('112107')"
            />
          </template>
          <PriceInput
            v-model="form.retail_price"
            :prefix="getPrefix('selling_currency')"
            :show-text="showPriceText"
            :disabled-edit="disabledEdit"
            :disabled-edit-placeholder="$t('global_please_input')"
            :tips="fixedPriceModelTips"
            :disabled="disabledPriceChange"
            @edit="editRuleSetting('price')"
            @fill="fillPrice"
            @change="handleChange('retail_price')"
          />
        </a-form-model-item>
        <a-form-model-item :label="$t('80903')" required>
          <a-select v-model="form.cut_off_day" :disabled="isSubSku" class="cutoff-select">
            <a-select-option
              v-for="cutoffOption in cutoffDayList"
              :key="cutoffOption.value"
              :value="cutoffOption.value"
            >
              {{ cutoffOption.text }}
            </a-select-option>
          </a-select>
          <a-time-picker
            v-model="form.cut_off_date"
            class="time-picker"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="hh:mm"
            :disabled="isSubSku"
          />
        </a-form-model-item>
        <a-form-model-item v-if="!isContentBlank" required>
          <div
            slot="label"
            class="label-wrap"
            v-bind="$root.bindIHTrack({ type: 'module', spm: 'Deactivate_Schedule' })"
          >
            <span class="label-text">{{ $t('30409') }}</span>
            <a-switch v-model="form.published" :disabled="isSubSku"></a-switch>
          </div>
        </a-form-model-item>
      </a-form-model>
      <div class="tag-setting">
        <TagSetting :marks="initialValue.marks" />
      </div>
      <div
        v-if="!isBdAudit && !isSubSku && !initialValue.is_empty && !isComboSku"
        slot="footerExtra"
        v-bind="$root.bindIHTrack({ type: 'module', spm: 'Delete_singleschedule' })"
      >
        <a-popconfirm
          :title="$t('27808')"
          :ok-text="$t('global_button_ok')"
          :cancel-text="$t('global_button_cancel')"
          :overlay-style="{ maxWidth: '250px' }"
          placement="top"
          @confirm="handleDelete"
        >
          <a-icon class="delete" type="delete" />
        </a-popconfirm>
      </div>
    </PopoverContainer>
    <div
      id="edit_single_schedule_spm"
      v-bind="$root.bindIHTrack({ type: 'module', spm: 'EditSingleSchedule' })"
    ></div>
  </div>
</template>
<script>
import PriceInput from '../price-input'
import FormLabel from '../form-label'
import PopoverContainer from '../popover-container'
import { cutoffDayList, MAX_INVENTORY } from '@activity/pages/package/package_const.js'
import { mapState, mapGetters } from 'vuex'
import { messageConfirm } from '@activity/pages/package/units/utils/message.js'
import { getCutOffDay, getCutOffTime } from '@activity/pages/package/units/utils/index.js'
import TagSetting from '@activity/pages/components/guardGroup/index.vue'
import { UNSAFE_PRICE_CODE, showUnsavePriceReasonModal } from '@activity/components/UnsavePriceReasonModal'
export default {
  name: 'CalendarItemPopoverMerchant',
  inject: ['skuId', 'calendar', 'unitBar'],
  components: {
    PriceInput,
    FormLabel,
    PopoverContainer,
    TagSetting
  },
  props: {
    initialValue: {
      type: Object,
      default: () => ({})
    },
    unitData: {
      type: Object,
      required: true
    }
  },
  data() {
    const numberValidate = (r, v, callback) => {
      if (!v) {
        callback(new Error(this.$t('global_please_input')))
        return
      }
      callback()
    }
    const priceValidate = (r, v, callback) => {
      const { isFixedPrice } = this
      if (isFixedPrice) {
        callback()
        return
      }
      const isNum = typeof v === 'number'
      if (!v && !isNum) {
        callback(new Error(this.$t('global_please_input')))
        return
      }
      callback()
    }
    const cutoffValidate = (r, v, callback) => {
      const { cut_off_day, cut_off_date } = this.form
      if ((!cut_off_day && cut_off_day !== 0) || !cut_off_date) {
        callback(new Error(this.$t('global_select')))
        return
      }
      callback()
    }
    return {
      MAX_INVENTORY,
      showDrawer: true,
      form: {},
      cutoffDayList,
      rules: {
        cost_price: [
          {
            required: true,
            message: this.$t('global_please_input')
          },
          {
            validator: priceValidate,
            trigger: 'change'
          }
        ],
        inv_quantity: [
          {
            required: true,
            message: this.$t('global_please_input')
          },
          {
            validator: numberValidate,
            trigger: 'change'
          }
        ],
        retail_price: [
          {
            required: true,
            message: this.$t('global_please_input')
          },
          {
            validator: priceValidate,
            trigger: 'change'
          }
        ],
        cut_off_day: [
          {
            validator: cutoffValidate,
            trigger: 'change'
          }
        ]
      },
      priceFilled: false,
      inputStyle: {
        paddingBottom: '10px'
      },

      unsavePriceReason: {
        unsafe_price_reason_code: undefined,
        unsafe_price_reason_note: undefined
      }
    }
  },
  computed: {
    ...mapState({
      isBdAudit: (state) => state.isBdAudit,
      isMerchant: (state) => state.isMerchant,
      skuRuleSetting: 'skuRuleSetting'
    }),
    ...mapGetters(['packageProductType', 'isTour']),
    isComboSku() {
      return this.packageProductType === 1
    },
    disabledConfirm() {
      return this.isSubSku && this.isContentBlank
    },
    isSubSku() {
      // 子sku
      return this.unitData.share_rel_ship === 1
    },
    inventoryModel() {
      const { inv_model } = this.unitData || {}
      return inv_model
    },
    invCannotEdit() {
      const { inventoryModel } = this
      return inventoryModel === 3
    },
    disabledInventory() {
      const { inventoryModel } = this
      return inventoryModel === 2
    },
    isFixedPrice() {
      const { price_model } = this.unitData || {}
      return price_model === 0
      // return price_model != 0
    },
    isContentBlank() {
      // 库存为 -1 表示未设置
      return !this.initialValue || (!this.initialValue.price && this.initialValue.inv_quantity === -1)
    },
    showPriceText() {
      const { isFixedPrice, isContentBlank, priceFilled } = this
      return (isFixedPrice && !isContentBlank) || priceFilled
    },
    title() {
      return this.isContentBlank ? this.$t('82667') : this.$t('82668')
    },
    disabledEdit() {
      return this.isContentBlank && this.isFixedPrice
    },
    disabledPriceChange() {
      const { approvalStatus } = this
      return approvalStatus === 'PendingApproval'
      // return approvalStatus === 'ToBeSubmitted'
    },
    approvalStatus() {
      return this.unitData?.ticket_status ?? ''
    },
    fixedPriceModelTips() {
      // 82676
      return klook.parseStr1(this.$t('111885'), {
        variable: `<span>${this.$t('111886')}</span>`
      })
    },
    invQuantityTips() {
      // 82675
      return klook.parseStr1(this.$t('111887'), {
        variable: `<span>${this.$t('111886')}</span>`
      })
    }
  },
  created() {
    this.initForm()
  },
  mounted() {
    this.initPriceStatus()
  },
  methods: {
    handleChange(prop) {
      const form = this.$refs.form
      // ['cost_price', 'retail_price', 'inv_quantity']
      form.validateField(prop)
    },
    initPriceStatus() {
      const { isFixedPrice, isContentBlank } = this
      if (isFixedPrice && isContentBlank) {
        const form = this.$refs.form
        form.validateField(['cost_price', 'retail_price'])
      }
    },
    fillPrice() {
      if (this.disabledConfirm) {
        return
      }
      const { sku_fix_price = {} } = this.skuRuleSetting || {}
      const { cost_price, retail_price } = sku_fix_price || {}
      this.$set(this.form, 'cost_price', cost_price)
      this.$set(this.form, 'retail_price', retail_price)
      this.priceFilled = true
      this.validateForm()
    },
    editRuleSetting(types) {
      klook.bus.$emit('showRuleSetting', { types })
      this.$emit('close')
    },
    initForm() {
      const initialValue = _.cloneDeep(this.initialValue || {})
      const { global_inv_quantity, merchant_currency, selling_currency } = this.skuRuleSetting || {}
      const { inv_model } = this.unitData || {}
      const price = initialValue.price || { cost_currency: merchant_currency, selling_currency }
      const inv_quantity = this.getInventory(initialValue, inv_model, global_inv_quantity)

      const cutOffData = getCutOffDay(initialValue.cut_off_time || 0)
      this.form = {
        ...initialValue,
        ...price,
        ...cutOffData,
        inv_quantity: inv_quantity < 0 ? undefined : inv_quantity
      }
    },
    getInventory(data, inv_model, global_inv_quantity) {
      if (inv_model === 3) {
        return global_inv_quantity
      }
      return inv_model === 2 ? this.$t('package_inventory_unlimited') : data.inv_quantity
    },
    getPrefix(prefix) {
      const { sku_fix_price } = this.skuRuleSetting || {}
      const { cost_currency, retail_currency } = sku_fix_price || {}
      const { price } = this.initialValue || {}
      const obj = price || { cost_currency, selling_currency: retail_currency }
      return obj[prefix] || ''
    },
    getLabel(key, prefix) {
      if (!this.showPriceText) {
        return ''
      }

      if (this.isFixedPrice) {
        const pf = prefix ? _.get(this.form, prefix, '') + ' ' : ''
        return pf + _.get(this.form, key, '')
      }
      return ''
    },
    resetUnsavePriceReason() {
      this.unsavePriceReason = {
        unsafe_price_reason_code: undefined,
        unsafe_price_reason_note: undefined
      }
      this.unsavePriceReasonVisible = false
    },
    async handleSave() {
      const validate = await this.validateForm()

      if (!validate) {
        return
      }
      // 如果是主sku，则要检测cut off time、库存、上下架状态、删除的变动，然后显示提示弹窗
      const { share_rel_ship } = this.unitData || {}
      if (share_rel_ship === 2) {
        const result = await messageConfirm(this, this.$t('27807'))

        if (!result) {
          return
        }
      }
      const params = this.getParams()

      // 如果存在不合格价格理由，将其添加到请求中
      if (params.price && this.unsavePriceReason?.unsafe_price_reason_note) {
        params.price.unsafe_price_reason_note = this.unsavePriceReason.unsafe_price_reason_note
        params.price.unsafe_price_reason_code = this.unsavePriceReason.unsafe_price_reason_code
      }
      const apiPath = this.isBdAudit
        ? ADMIN_API.act.creates_or_update_single_schedule_audit
        : ADMIN_API.act.creates_or_update_single_schedule
      const res = await ajax.postBody(
        apiPath,
        { data: params, noDefaultResponseInterceptor: true },
        { loading: true, msgOpt: { isErrMsg: false } }
      )

      if (res.error?.code === UNSAFE_PRICE_CODE) {
        // 如果价格校验不通过，唤起 UnSavePriceReasonModal
        const { confirm, cancel } = await showUnsavePriceReasonModal({
          warningMessage: res.error.message
        })
        if (!cancel) {
          const { reasonCode, reasonNote } = confirm
          this.unsavePriceReason = {
            // 清空之前的理由
            unsafe_price_reason_code: reasonCode,
            unsafe_price_reason_note: reasonNote
          }
          await this.handleSave()
          return
        }
      }

      if (res && res.success) {
        this.$message.success(this.$t('global_modify_success'))
        this.$emit('close')
        this.calendar.update()
        klook.bus.$emit('getPackageSkuDataBus')
        return
      }
      this.$message.error(res?.error?.message || this.$t('global_modify_error'))
      this.$root.trackIHEvent('#edit_single_schedule_spm', {
        err_message: res?.error?.message ?? 'EditSingleSchedule Error'
      })
      this.resetUnsavePriceReason()
    },
    validateForm() {
      const form = this.$refs.form
      return form.validate().catch(() => false)
    },
    getParams() {
      const { inv_model } = this.unitData || {}
      const formData = this.form
      const { isTour, isFixedPrice } = this
      const cut_off_time = getCutOffTime(formData)
      const inv_quantity = inv_model === 2 ? 0 : Number(formData.inv_quantity)
      const retail_price =
        isTour && !isFixedPrice ? Number(formData.cost_price) : Number(formData.retail_price)
      const params = {
        sku_id: this.skuId,
        start_time: formData.start_time,
        inv_quantity,
        published: formData.published,
        price: {
          cost_currency: formData.cost_currency,
          cost_price: Number(formData.cost_price),
          retail_price: retail_price
          // selling_currency: formData.selling_currency,
          // selling_price: Number(formData.selling_price),
          // special_selling_price: this.initialValue.price ? this.initialValue.price.special_selling_price : []
        },
        cut_off_time
      }

      if (this.initialValue.marks) {
        params.marks = this.initialValue.marks.map((item) => ({ key: item.key, value: item.value }))
      }

      // 在商户端，如果 costPrice 和 retailPrice 都没填，则传 null
      if (this.isMerchant && formData.cost_price === '' && formData.retail_price === '') {
        params.price = null
      }

      return params
    },
    async handleDelete() {
      // 如果是主sku，则要检测cut off time、库存、上下架状态、删除的变动，然后显示提示弹窗
      const { share_rel_ship } = this.unitData || {}
      if (share_rel_ship === 2) {
        const result = await messageConfirm(this, this.$t('27807'))

        if (!result) {
          return
        }
      }
      const res = await ajax.postBody(
        ADMIN_API.act.destroy_single_schedule,
        {
          data: {
            sku_id: this.skuId,
            start_time: this.form.start_time
          }
        },
        {
          loading: true
        }
      )

      if (res.success) {
        this.$message.success(this.$t('global_delete_success'))
        this.$emit('close')
        this.calendar.update()
      } else {
        this.$message.error(this.$t('global_delete_error'))
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.form {
  padding: 20px 24px 6px 24px;
}

.popover-wrap {
  width: 360px;
}
.cutoff-select {
  width: 200px;
  margin-right: 8px;
}
.time-picker {
  width: 104px;
}
.delete {
  color: #ff4d4f;
  font-size: 14px;
  cursor: pointer;
}
.label-wrap {
  display: inline-flex;
  align-items: center;
  .label-text {
    padding-right: 4px;
  }
}
.tag-setting {
  margin-top: -2px;
  padding: 0px 24px 20px;
}
</style>
