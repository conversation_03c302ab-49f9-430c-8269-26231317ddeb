<template>
  <a-modal
    :title="$t('80910')"
    :visible="visible"
    width="448px"
    :cancel-text="$t('global_cancel')"
    :ok-text="$t('global_confirm')"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form-model ref="form" layout="vertical" :model="form" :rules="rules" class="form">
      <a-form-model-item :label="$t('83099')" prop="from_sku_id" required>
        <a-cascader
          v-model="form.from_sku_id"
          class="cascader"
          :options="options"
          popup-class-name="popup-class-name__copy-unit"
          :placeholder="$t('global_select')"
        />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    skuId: {
      type: [String, Number],
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {
        from_sku_id: []
      },
      rules: {
        from_sku_id: [
          {
            required: true,
            message: this.$t('global_please_select'),
            trigger: 'change'
          },
          {
            validator: (r, value, callback) => {
              if (!value || !value.length) {
                return callback(new Error(this.$t('global_please_select')))
              }
              callback()
            },
            trigger: 'change'
          }
        ]
      }
    }
  },
  methods: {
    async handleOk() {
      const validate = await this.validateForm()
      if (!validate) {
        return
      }
      // eslint-disable-next-line no-unused-vars
      const [pkg, sku] = this.form.from_sku_id
      const data = {
        from_sku_id: sku,
        to_sku_id: this.skuId
      }
      const res = await ajax.postBody(
        ADMIN_API.act.copy_sku_calendar,
        {
          data,
          noDefaultResponseInterceptor: true
        },
        { loading: true }
      )
      if (res && res.success) {
        this.$message.success(this.$t('global_copy_success'))
        this.$emit('copySuccess')
      } else {
        this.$emit('copy-error', res?.error?.message ?? 'CopySchedule Error')
      }
    },
    validateForm() {
      const form = this.$refs.form
      return form.validate().catch(() => false)
    },
    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
.cascader {
  width: 400px;
}
</style>

<style lang="scss">
.popup-class-name__copy-unit {
  .ant-cascader-menu {
    max-width: 400px;
    .ant-cascader-menu-item {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
