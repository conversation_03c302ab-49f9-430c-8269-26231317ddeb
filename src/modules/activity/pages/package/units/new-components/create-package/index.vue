<template>
  <div class="create-package-container">
    <a-modal
      :visible="visible"
      width="800px"
      :title="$t('btn.createPkg')"
      :ok-text="confirmText"
      :cancel-text="cancelText"
      @cancel="cancel"
    >
      <a-steps :current="current" progress-dot style="padding: 10px 40px" @change="change">
        <a-step :title="$t('80894')" :status="current == 0 ? 'process' : 'finish'" />
        <a-step :title="$t('80895')" :status="current == 1 ? 'process' : 'wait'" />
      </a-steps>
      <div v-show="current === 0">
        <a-form-model ref="form" layout="vertical" :model="form" class="form" :rules="rules">
          <a-form-model-item :label="$t('package_info_name')" prop="package_name" required>
            <a-tooltip :title="$t('83100')" :overlay-style="{ maxWidth: '400px' }">
              <a-input
                v-model="form.package_name"
                class="form-ctrl"
                :placeholder="$t('global_please_input')"
              />
            </a-tooltip>
          </a-form-model-item>
        </a-form-model>
        <CreateUnit ref="units" :list="unitList" />
      </div>
      <div v-show="current === 1" class="form">
        <RuleSettings ref="ruleSettings" :list="unitList" />
      </div>
      <div slot="footer">
        <a-button @click="checkCancel">{{ cancelText }}</a-button>
        <a-button type="primary" :loading="loading" @click="handleConfirm">{{ confirmText }}</a-button>
      </div>
    </a-modal>
    <div id="add_new_package_spm" v-bind="$root.bindIHTrack({ type: 'module', spm: 'AddNewPackage' })"></div>
  </div>
</template>

<script>
import CreateUnit from '../create-unit/index.vue'
import RuleSettings from '../rule-settings/index.vue'
import { getDefaultUnitForm } from '@activity/pages/package/units/utils'
import { STORAGE_KEY_DICT } from '@activity/utils/const.js'

export default {
  name: 'CreatePackage',
  provide() {
    return {
      activityId: this.activityId
    }
  },
  components: {
    CreateUnit,
    RuleSettings
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validator = (r, v, callback) => {
      if (!v.trim()) {
        return callback(new Error(this.$t('global_please_input')))
      }
      callback()
    }
    return {
      loading: false,
      unitList: [getDefaultUnitForm()],
      form: {
        package_name: ''
      },
      current: 0,
      rules: {
        package_name: [
          {
            required: true,
            validator: validator
          }
        ]
      }
    }
  },
  computed: {
    cancelText() {
      return this.current == 0 ? this.$t('global_button_cancel') : this.$t('44926')
    },
    confirmText() {
      return this.current == 0 ? this.$t('global.user_guide_next') : this.$t('82183')
    },
    activityId() {
      return +this.$route.params.id
    }
  },
  methods: {
    async change(v) {
      if (v === 1) {
        const validate = await Promise.all([this.validateForm(), this.validateUnits()])
        if (!validate.every((item) => item)) {
          return
        }
      }
      this.current = v
    },
    checkCancel() {
      if (this.current == 1) {
        this.current = 0
        return
      }
      this.cancel()
    },
    cancel() {
      this.reset()
      this.$emit('cancel')
    },
    validateRuleSettings() {
      const ruleSettings = this.$refs.ruleSettings
      return ruleSettings.validateForm()
    },
    validateUnits() {
      const units = this.$refs.units
      return units.validateUnits().catch(() => false)
    },
    validateForm() {
      const form = this.$refs.form
      return form.validate().catch(() => false)
    },
    getUnitData() {
      const units = this.$refs.units
      return units.getData()
    },
    scrollHasError(el = '.js-create-package-node .has-error') {
      setTimeout(() => {
        const dom = el && document.querySelector(el)
        if (dom) {
          dom?.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })
        }
      }, 1)
    },
    async handleConfirm() {
      if (this.current === 0) {
        const validate = await Promise.all([this.validateForm(), this.validateUnits()])
        if (!validate.every((item) => item)) {
          this.scrollHasError()
          return
        }
        this.current = 1
      } else if (this.current === 1) {
        const validate = await Promise.all([
          this.validateForm(),
          this.validateUnits(),
          this.validateRuleSettings()
        ])
        if (!validate.every((item) => item)) {
          this.scrollHasError()
          return
        }
        this.loading = true
        await this.postData()
        this.loading = false

        const res = localStorage.getItem(STORAGE_KEY_DICT.first_create_pkg)
        if (!res) {
          localStorage.setItem(STORAGE_KEY_DICT.first_create_pkg, 'true')
        }
      }
    },
    async postData() {
      const { package_name } = this.form
      const ruleSettings = this.$refs.ruleSettings
      const rule_setting = ruleSettings.getData()
      const units = this.$refs.units
      const skus = units.getData()
      const data = {
        activity_id: +this.activityId,
        language: klook.getEditLang(),
        package_name: package_name.trim(),
        skus,
        rule_setting
      }
      const res = await ajax.postBody(
        ADMIN_API.act.create_package_with_skus,
        {
          data,
          noDefaultResponseInterceptor: true
        },
        { loading: true }
      )
      if (res && res.success && res.result.package_id) {
        const package_id = res.result.package_id
        this.routerInfo(package_id)
        this.$emit('cancel')
        this.reset()
      } else {
        this.$root.trackIHEvent('#add_new_package_spm', {
          err_message: res?.error?.message ?? 'AddNewPackage Error'
        })
      }
    },
    reset() {
      this.unitList = [getDefaultUnitForm()]
      this.current = 0
      const form = this.$refs.form
      form.resetFields()
      const ruleSettings = this.$refs.ruleSettings
      ruleSettings.resetForm()
    },
    routerInfo(package_id) {
      klook.bus.$emit('createPkgAndUnit2bus', package_id)
      let query = {
        ...this.$route.query,
        sku_id: undefined,
        package_id: '' + package_id
      }
      if (!_.isEqual(query, this.$route.query)) {
        // 旧数据存在需要重新选 package_type 的情况
        this.$router.replace({
          path: `/act/package/info/${this.activityId}`,
          query
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .ant-steps-item-content {
  width: 160px;
}
.form {
  padding: 24px 0;
  .form-ctrl {
    width: 400px;
  }
}
</style>
