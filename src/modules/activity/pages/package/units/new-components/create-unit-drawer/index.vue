<template>
  <a-modal
    :visible="visible"
    :title="$t('82181')"
    :ok-text="$t('82181')"
    :cancel-text="$t('global_button_cancel')"
    width="800px"
    @cancel="cancel"
    @ok="handleConfirm"
  >
    <CreateUnit ref="units" :show-add="false" :list="unitList" />
  </a-modal>
</template>
<script>
import CreateUnit from '../create-unit'
import { getDefaultUnitForm } from '@activity/pages/package/units/utils'

export default {
  provide() {
    return {
      activityId: this.activityId
    }
  },
  components: {
    CreateUnit
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    packageId: {
      type: [Number, String],
      default: 0
    }
  },
  data() {
    return {
      unitList: [getDefaultUnitForm()]
    }
  },
  computed: {
    activityId() {
      return this.$route.params.id
    }
  },
  methods: {
    cancel() {
      this.$emit('cancel')
    },
    validateUnits() {
      const units = this.$refs.units
      return units.validateUnits().catch(() => false)
    },
    async handleConfirm() {
      const validate = await this.validateUnits()
      if (!validate) {
        return
      }
      const units = this.$refs.units
      const skus = units.getData()
      const package_id = +this.packageId
      const res = await ajax.postBody(
        ADMIN_API.act.create_multi_skus,
        {
          data: {
            activity_id: +this.activityId,
            package_id,
            skus
          }
        },
        { loading: true }
      )
      if (res && res.success) {
        klook.bus.$emit('getPackageSkuDataBus')
        const sku_id = (res.result?.sku_ids || [])[0] || ''
        this.$emit('cancel')
        this.unitList = [getDefaultUnitForm()]
        // 新建sku自动弹出add schedule 弹窗
        window.sessionStorage.setItem('created_new_sku', sku_id)
        this.routerInfo({ package_id, sku_id })
      }
    },
    routerInfo(options) {
      let query = {
        ...this.$route.query,
        ...(options || {})
      }
      if (!_.isEqual(query, this.$route.query)) {
        // 旧数据存在需要重新选 package_type 的情况
        this.$router.replace({
          path: `/act/package/unit/${this.activityId}`,
          query
        })
      }
    }
  }
}
</script>
