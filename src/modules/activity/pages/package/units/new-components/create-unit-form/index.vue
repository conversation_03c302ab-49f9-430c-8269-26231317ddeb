<template>
  <div class="form-wrap" :class="{ 'settings-form-wrap': isSettings }">
    <a-form-model ref="form" layout="vertical" :model="form" :rules="rules">
      <a-form-model-item
        v-if="!isSettings"
        class="form-item"
        :class="form.use_customized_name && 'use-customized-name'"
        :label="$t('80896')"
        prop="unit_type"
        required
      >
        <div @click.stop="openSelect = !openSelect">
          <a-select
            ref="select"
            v-model="form.value"
            :options="unitTypeList"
            class="form-ctrl"
            :placeholder="$t('global_please_select')"
            :open="openSelect"
            @change="handleChange"
          >
            <div slot="dropdownRender" slot-scope="menu">
              <v-nodes :vnodes="menu" />
              <div class="dropdown-btn-wrap" @click.stop>
                <a-input
                  v-model="optionName"
                  class="dropdown-text"
                  :placeholder="$t('global_please_input')"
                ></a-input>
                <a-button class="btn" icon="plus" type="link" @click="addOptions">
                  {{ $t('82184') }}
                </a-button>
              </div>
            </div>
          </a-select>
        </div>
      </a-form-model-item>
      <a-form-model-item v-if="form.use_customized_name" :label="$t('84347')" prop="unit_name" required>
        <a-input v-model="form.unit_name" :placeholder="$t('global_please_input')"></a-input>
      </a-form-model-item>
      <a-form-model-item
        v-if="canShowAgeList.includes(form.unit_type)"
        class="form-item"
        :label="$t('package_min_max_age')"
        prop="min_age_range"
        required
      >
        <a-input-number v-model="form.min_age_range" class="form-ctrl-half" />
        -
        <a-input-number v-model="form.max_age_range" class="form-ctrl-half" />
        age(s)
      </a-form-model-item>
      <div v-show="showMore || isSettings">
        <a-form-model-item class="form-item" :label="$t('80897')" required>
          <a-switch v-model="form.required" @change="handleRequiredChange"></a-switch>
        </a-form-model-item>
        <a-form-model-item class="form-item" :label="$t('package_unit_number')" prop="is_limited" required>
          <a-tooltip placement="top" :title="$t('29438')" arrow-point-at-center>
            <a-radio-group v-model="form.is_limited" @change="handleLimitedChange">
              <a-radio :value="false">
                {{ $t('package_unlimited') }}
              </a-radio>
              <a-radio :value="true">
                {{ $t('package_unit_limited') }}
              </a-radio>
            </a-radio-group>
          </a-tooltip>
        </a-form-model-item>
        <a-form-model-item
          v-if="form.is_limited"
          class="form-item"
          :label="$t('package_min_max_units')"
          prop="min_num"
          required
        >
          <a-input-number v-model="form.min_num" :min="0" :max="MAX_UNIT - 1" class="form-ctrl-half" />
          -
          <a-input-number v-model="form.max_num" :min="0" :max="MAX_UNIT - 1" class="form-ctrl-half" />
        </a-form-model-item>
      </div>
      <a-button v-if="!isSettings" class="toggle-btn" type="link" @click="toggleMore">
        {{ showMore ? $t('17049') : $t('global.more') }}
        <a-icon :type="showMore ? 'up' : 'down'" />
      </a-button>
    </a-form-model>
  </div>
</template>
<script>
import {
  MAX_AGE,
  MAX_UNIT,
  getUnitTypeList,
  DEFAULT_ARG_RANGE
} from '@activity/pages/package/package_const.js'
import { getDefaultUnitForm } from '@activity/pages/package/units/utils'
import { mapState } from 'vuex'
import { getUuid } from '@activity/utils'

export default {
  components: {
    // UnitBarForm,
    VNodes: {
      functional: true,
      render: (h, ctx) => ctx.props.vnodes
    }
  },
  model: {
    prop: 'data',
    event: 'change'
  },
  props: {
    subCategoryId: {
      type: Number,
      default: 0
    },
    isSettings: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => getDefaultUnitForm()
    }
  },
  data() {
    const validateMinNum = (rule, value, callback) => {
      if (this.form.required && this.form.min_num < 1) {
        callback(new Error(this.$t('package_unit_min_num_when_required')))
      } else if (!this.form.is_limited) {
        callback()
      } else if (!this.form.min_num && this.form.min_num !== 0) {
        callback(new Error(this.$t('global_please_input')))
      } else if (!_.isInteger(this.form.min_num)) {
        callback(new Error(this.$t('package_unit_num_integer')))
      } else if (!this.form.max_num && this.form.max_num !== 0) {
        callback(new Error(this.$t('global_please_input')))
      } else if (this.form.max_num === 0) {
        callback(new Error(this.$t('80128')))
      } else if (!_.isInteger(this.form.max_num)) {
        callback(new Error(this.$t('package_unit_num_integer')))
      } else if (this.form.min_num > this.form.max_num) {
        callback(new Error(this.$t('package_unit_num_comparison')))
      } else {
        callback()
      }
    }
    const validateMinAge = (rule, value, callback) => {
      if (!this.form.min_age_range && this.form.min_age_range !== 0) {
        callback(new Error(this.$t('global_please_input')))
      } else if (!_.isInteger(this.form.min_age_range)) {
        callback(new Error(this.$t('package_unit_age_integer')))
      } else if (this.showMaxAge && !this.form.max_age_range) {
        callback(new Error(this.$t('global_please_input')))
      } else if (this.showMaxAge && !_.isInteger(this.form.max_age_range)) {
        callback(new Error(this.$t('package_unit_age_integer')))
      } else if (this.showMaxAge && this.form.min_age_range > this.form.max_age_range) {
        callback(new Error(this.$t('package_unit_age_comparison')))
      } else {
        callback()
      }
    }
    const validateNumLimited = (rule, value, callback) => {
      if (this.form.required && !this.form.is_limited) {
        callback(new Error(this.$t('package_unit_limited_when_required')))
      } else {
        callback()
      }
    }

    const validateUnitName = (rule, value, callback) => {
      if (!value.trim()) {
        callback(new Error(this.$t('global_please_input')))
        return
      }
      callback()
      // const { unitName, unitNameAdditional } = this.form
      // if (!unitName) {
      //   callback(new Error(this.$t('global_please_input')))
      // } else if (this.useUnitNameRef && !this.isUnitNameRefDisabled && !this.form.unitNameRef) {
      //   callback(new Error(this.$t('global_please_input')))
      // } else if (this.useUnitNameAdditional && !unitNameAdditional) {
      //   callback(new Error(this.$t('global_please_input')))
      // } else {
      //   callback()
      // }
    }
    return {
      openSelect: false,
      optionName: '',
      newOptions: [],
      canShowAgeList: [1, 2, 3, 4, 17],
      showMore: false,
      MAX_AGE,
      MAX_UNIT,
      rules: {
        unit_name: [
          { validator: validateUnitName, trigger: 'change' },
          {
            required: true,
            message: this.$t('global_please_input'),
            trigger: 'change'
          }
        ],
        unit_type: [
          {
            validator: (rule, value, callback) => {
              if (typeof value === 'undefined') {
                return callback(new Error(this.$t('global_please_select')))
              }
              callback()
            },
            trigger: 'blur'
          }
        ],
        min_num: [
          { validator: validateMinNum, trigger: 'blur' },
          {
            required: true,
            message: this.$t('global_please_input'),
            trigger: 'blur'
          }
        ],
        min_age_range: [
          { validator: validateMinAge, trigger: 'blur' },
          {
            required: true,
            message: this.$t('global_please_input'),
            trigger: 'blur'
          }
        ],
        is_limited: [{ validator: validateNumLimited, trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapState({
      categoryInfo: (state) => state.categoryInfo,
      customUnitType: (state) => state.customUnitType
    }),
    form: {
      get() {
        return this.data
      },
      set() {
        this.$emit('change')
      }
    },
    defaultUintTypeValue() {
      return this.defaultUintType.map((item) => item.value)
    },
    defaultUintType() {
      const sub_category_id = this.subCategoryId || this.categoryInfo?.sub_category_id
      return getUnitTypeList(sub_category_id).map((item) => {
        return {
          ...item,
          unit_type: item.value
        }
      })
    },
    unitTypeList() {
      return [...this.newOptions, ...this.defaultUintType, ...(this.customUnitType || [])].map((item) => {
        const { text, value, unit_type, local = [] } = item
        return {
          unit_type,
          value,
          label: text,
          local
        }
      })
    }
  },
  watch: {
    'form.unit_type': {
      handler(val) {
        const result = this.defaultUintType.find((item) => item.value === val) || DEFAULT_ARG_RANGE
        this.form.min_age_range = result.min
        this.form.max_age_range = result.max
      }
    }
  },
  mounted() {
    document.body.addEventListener('click', this.hideDropDown)
  },
  beforeDestroy() {
    document.body.removeEventListener('click', this.hideDropDown)
  },
  methods: {
    hideDropDown() {
      this.openSelect = false
    },
    handleChange(v) {
      this.openSelect = false
      const option = this.unitTypeList.find((item) => item.value === v)
      if (option) {
        this.form.unit_type = option.unit_type
        this.form.unit_name = option.label
        this.form.local = option.local
      }
    },
    toggleMore() {
      this.showMore = !this.showMore
    },
    addOptions() {
      const optionName = this.optionName.trim()
      if (!optionName) {
        return
      }
      const v = getUuid()
      const newOpt = {
        unit_type: 5,
        text: optionName,
        value: v,
        local: [
          {
            language: klook.getEditLang(),
            unit_name: optionName
          }
        ]
      }
      this.newOptions.push(newOpt)
      this.form.unit_type = newOpt.unit_type
      this.form.value = newOpt.value
      this.form.unit_name = newOpt.text
      this.form.local = newOpt.local
      this.optionName = ''
      this.openSelect = false
    },
    validateForm() {
      const form = this.$refs.form
      const res = new Promise((resolve) => {
        return form.validate((valid, errObj) => {
          if (!valid) {
            const res = ['is_limited', 'min_num'].some((key) => {
              return errObj[key]
            })
            res && (this.showMore = true)
          }
          return resolve(valid)
        })
      })
      return res
    },
    resetForm() {
      const form = this.$refs.form
      return form.resetFields()
    },
    handleRequiredChange(v) {
      if (v) {
        this.form.is_limited = true
      }
      this.$refs.form.validateField('is_limited')
    },
    handleLimitedChange() {
      this.$refs.form.validateField('is_limited')
    }
  }
}
</script>
<style lang="scss" scoped>
.form-wrap {
  background: #fafafa;
  min-height: 120px;
  padding: 10px;
  .form-item {
    padding-bottom: 14px;
  }
  &.settings-form-wrap {
    background: #fff;
    padding: 24px;
    width: 360px;
    .form-ctrl-half {
      width: 125px;
    }
  }
  .form-ctrl {
    width: 400px;
  }
  .form-ctrl-half {
    width: 169px;
  }
}
.dropdown-btn-wrap {
  display: flex;
  align-items: center;
  padding: 8px 0 8px 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  .dropdown-text {
    flex: 1;
  }
}

.toggle-btn {
  padding: 0;
}
</style>
