<template>
  <div>
    <div v-if="showAdd" class="head">
      <div>{{ $t('83140') }}</div>
      <a-button type="primary" icon="plus" @click="handleAdd">{{ $t('global_add') }}</a-button>
    </div>
    <div v-for="(item, index) in unitList" :key="item.id" class="unit-wrap">
      <div class="unit-opear-wrap">
        <a-icon v-show="unitList.length > 1" class="icon del" type="delete" @click="deleteForm(index)" />
        <template v-if="!hideSort">
          <a-icon v-show="index > 0" class="icon arr" type="arrow-up" @click="handleSort(-1, index)" />
          <a-icon
            v-show="index < unitList.length - 1"
            class="icon arr"
            type="arrow-down"
            @click="handleSort(1, index)"
          />
        </template>
      </div>
      <UnitForm ref="units" :data="item" :subCategoryId="subCategoryId" :isSettings="isSettings" />
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import { getDefaultUnitForm, getUnitFormData } from '@activity/pages/package/units/utils'

import UnitForm from '../create-unit-form/index.vue'
export default {
  inject: ['activityId'],
  components: {
    UnitForm
  },
  model: {
    prop: 'list',
    event: 'change'
  },
  props: {
    hideSort: {
      type: Boolean,
      default: false
    },
    isSettings: {
      type: Boolean,
      default: false
    },
    subCategoryId: {
      type: Number,
      default: 0
    },
    list: {
      type: Array,
      default: () => []
    },
    showAdd: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      defaultEditLang: 'en_US'
    }
  },
  computed: {
    unitList: {
      get() {
        return this.list
      },
      set() {
        this.$emit('change')
      }
    }
  },
  created() {
    if (this.activityId) {
      this.actionGetCustomUnitType({
        activityId: this.activityId
      })
    }
  },
  methods: {
    ...mapActions(['actionGetCustomUnitType']),
    async validateUnits() {
      const units = this.$refs.units
      const res = await Promise.all(units.map((item) => item.validateForm()))
      return res.every((item) => item)
    },
    getData() {
      return this.unitList.map((item) => {
        return getUnitFormData(item)
      })
    },
    handleAdd() {
      this.unitList.push(getDefaultUnitForm())
    },
    handleSort(sort, index) {
      const list = _.cloneDeep(this.unitList)
      const from = index
      const to = sort + index
      const toData = list[to]
      const fromData = list[from]
      this.$set(this.unitList, from, toData)
      this.$set(this.unitList, to, fromData)
    },
    deleteForm(index) {
      this.unitList.splice(index, 1)
    }
  }
}
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 500;
  font-size: 14px;
  color: #000;
  padding-bottom: 16px;
}
.unit-wrap {
  padding-bottom: 16px;
  position: relative;
  .unit-opear-wrap {
    position: absolute;
    top: 26px;
    right: 10px;
    font-size: 16px;
    z-index: 2;
    .icon {
      margin-left: 20px;
      cursor: pointer;
      color: #595959;

      &.del:hover {
        color: red;
      }

      &.arr:hover {
        color: #1890ff;
      }
    }
  }
}
</style>
