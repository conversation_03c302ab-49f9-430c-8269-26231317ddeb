<template>
  <Drawer
    :visible="visible"
    :is-delete="true"
    :title="$t('80919')"
    :confirm-text="$t('82670')"
    :popconfirm="true"
    @cancel="cancel"
    @confirm="handleConfirm"
  >
    <a-alert :message="$t('80917')" banner style="margin-bottom: 16px" />
    <a-form-model ref="form" layout="vertical" :model="form" :rules="rules" class="form">
      <a-form-model-item v-if="visible" :label="$t('81822')" prop="start_date" required>
        <a-range-picker class="cascader" value-format="YYYY-MM-DD" @change="onChange">
          <a-icon slot="suffixIcon" type="calendar" />
        </a-range-picker>
      </a-form-model-item>
      <a-spin :spinning="loading">
        <a-form-model-item :label="$t('82669')" prop="sku_ids" required>
          <a-checkbox v-model="checkedAll" :indeterminate="indeterminate" @change="selectAll">
            {{ $t('48131') }}
          </a-checkbox>
          <a-tree
            v-model="form.sku_ids"
            :tree-data="treeData"
            checkable
            :expanded-keys="expandedKeys"
            @expand="onExpand"
          >
            <template slot="title" slot-scope="data">
              <a-tooltip :title="data.tips">
                {{ data.title }}
              </a-tooltip>
            </template>
          </a-tree>
        </a-form-model-item>
      </a-spin>
    </a-form-model>
    <div slot="footerExtra">
      <DeletePopconfirm :title="$t('js_confirm_delete')" @confirm="handleDelete">
        <a-icon class="del-button" type="delete" />
      </DeletePopconfirm>
    </div>
  </Drawer>
</template>
<script>
import Drawer from '../drawer'
import DeletePopconfirm from '../delete-popconfirm'
import { messageConfirm } from '@activity/pages/package/units/utils/message.js'

export default {
  components: {
    Drawer,
    DeletePopconfirm
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  inject: ['activityId'],
  data() {
    return {
      form: {
        sku_ids: [],
        start_date: '',
        end_date: ''
      },
      treeData: [],
      expandedKeys: [],
      allSkus: [],
      rules: {
        start_date: [
          {
            required: true,
            message: this.$t('global_please_select'),
            trigger: 'change'
          },
          {
            validator: (r, value, callback) => {
              const { start_date, end_date } = this.form
              if (!start_date || !end_date) {
                return callback(new Error(this.$t('global_please_select')))
              }
              callback()
            },
            trigger: 'change'
          }
        ],
        sku_ids: [
          {
            required: true,
            message: this.$t('global_please_select'),
            trigger: 'change'
          },
          {
            validator: (r, value, callback) => {
              if (!value.length) {
                return callback(new Error(this.$t('global_please_select')))
              }
              callback()
            },
            trigger: 'change'
          }
        ]
      },
      loading: true,
      checkedAll: false,
      indeterminate: false
    }
  },
  watch: {
    'form.sku_ids': {
      deep: true,
      immediate: true,
      handler() {
        const skus = this.filterSkus()
        if (skus.length === 0) {
          this.checkedAll = false
          this.indeterminate = false
        } else if (skus.length === this.allSkus.length) {
          this.checkedAll = true
          this.indeterminate = false
        } else {
          this.checkedAll = false
          this.indeterminate = true
        }
      }
    }
  },
  created() {
    this.getSkus()
  },
  methods: {
    selectAll(e) {
      const checked = this.checkedAll
      this.indeterminate = false
      if (!checked) {
        this.form.sku_ids = []
        return
      }
      const treeData = this.treeData
      this.form.sku_ids = treeData.reduce((acc, item) => {
        const { key, children } = item
        const childKey = children.map((item) => item.key)
        return [...acc, key, ...childKey]
      }, [])
    },
    onChange(value) {
      const [start_date = '', end_date = ''] = value
      this.form.start_date = start_date
      this.form.end_date = end_date
    },
    cancel() {
      const form = this.$refs.form
      form.resetFields()
      this.$emit('cancel')
    },
    validateForm() {
      const form = this.$refs.form
      return form.validate().catch(() => false)
    },
    filterSkus() {
      const { sku_ids } = this.form
      return this.allSkus.reduce((acc, curr) => {
        const { sku_id } = curr
        if (sku_ids.includes(sku_id)) {
          acc.push(sku_id)
        }
        return acc
      }, [])
    },
    getFormData() {
      const { start_date, end_date } = this.form
      return {
        start_date: `${start_date} 00:00:00`,
        end_date: `${end_date} 23:59:59`,
        sku_ids: this.filterSkus()
      }
    },
    async postData({ url, message }) {
      const validate = await this.validateForm()
      if (!validate) {
        return
      }
      const { allSkus } = this
      const data = this.getFormData()
      const sku_ids = data.sku_ids
      const skus = allSkus.filter((item) => sku_ids.includes(item.sku_id))

      // 如果选中的sku包含主sku，需要二次确认
      const needConfirm = skus.some((item) => {
        const shareRelShip = item.share_rel_ship?.share_rel_ship ?? ''
        return shareRelShip === 2
      })

      if (needConfirm) {
        const result = await messageConfirm(this, this.$t('27807'))

        if (!result) {
          return
        }
      }

      const res = await ajax.postBody(
        url,
        {
          data
        },
        { loading: true }
      )
      if (!res.success) {
        return false
      }
      this.cancel()
      this.$message.success(message)
      return true
    },
    async handleDelete() {
      await this.postData({
        url: ADMIN_API.act.destroy_schedule_by_date_range,
        message: this.$t('global_delete_success')
      })
    },
    async handleConfirm() {
      await this.postData({
        url: ADMIN_API.act.deactivate_schedule_by_date_range,
        message: this.$t('global_modify_success')
      })
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys
    },
    async getSkus() {
      const res =
        (await ajax.get(ADMIN_API.act.get_sku_simple_by_activity_id, {
          params: {
            activity_id: Number(this.activityId),
            language: klook.getEditLang()
          }
        })) || {}
      const list = res.list || []
      const expandedKeys = []
      this.treeData = list.reduce((acc, curr) => {
        const { package_id, package_name, unit_name, sku_id, share_rel_ship = {} } = curr
        const target = acc.find((item) => item.key === package_id)
        const shareRelShip = share_rel_ship?.share_rel_ship ?? ''
        // 子sku不允许修改
        const disableCheckbox = shareRelShip === 1
        const tips = disableCheckbox ? this.$t('83078') : ''
        if (target) {
          target.children.push({
            key: sku_id,
            title: `${sku_id}-${unit_name}`,
            disableCheckbox,
            tips
          })
        } else {
          acc.push({
            key: package_id,
            title: `${package_id}-${package_name}`,
            children: [
              {
                key: sku_id,
                title: `${sku_id}-${unit_name}`,
                disableCheckbox,
                tips
              }
            ]
          })
          expandedKeys.push(package_id)
        }
        return acc.map((item) => {
          const children = item.children
          item.disabled = children.every((child) => child.disableCheckbox)
          return item
        })
      }, [])
      this.expandedKeys = expandedKeys
      this.allSkus = list
      this.loading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.cascader {
  width: 400px;

  ::v-deep .ant-calendar-range-picker-input {
    text-align: left;
  }

  ::v-deep .ant-calendar-range-picker-separator {
    padding: 0 10px;
  }
}
.del-button {
  color: #ff4d4f;
  font-size: 14px;
  cursor: pointer;
}
</style>
