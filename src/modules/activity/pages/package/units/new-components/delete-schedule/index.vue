<template>
  <a-modal
    :title="$t('80918')"
    :visible="visible"
    width="448px"
    :cancel-text="$t('global_cancel')"
    :ok-text="'Deactivate'"
    ok-type="danger"
    @cancel="handleCancel"
  >
    <a-alert banner style="margin-bottom: 16px">
      <template #message>
        <span v-html="$t('82721')"></span>
      </template>
    </a-alert>
    <a-form-model ref="form" layout="vertical" :model="form" :rules="rules" class="form">
      <a-form-model-item :label="$t('81822')" prop="start_date" required>
        <a-range-picker class="cascader" value-format="YYYY-MM-DD" @change="onChange">
          <a-icon slot="suffixIcon" type="calendar" />
        </a-range-picker>
      </a-form-model-item>
    </a-form-model>
    <div slot="footer" class="footer">
      <div class="extra-wrap">
        <DeletePopconfirm :title="$t('js_confirm_delete')" @confirm="handleDelete">
          <a-icon class="del-button" type="delete" />
        </DeletePopconfirm>
        <!-- <div class="del-button" @click="handleDelete">{{ $t('global_delete') }}</div> -->
      </div>
      <div>
        <a-button style="margin-right: 8px;" @click="handleCancel">
          {{ $t('global_button_cancel') }}
        </a-button>
        <DeletePopconfirm :title="$t('82670')" @confirm="handleConfirm">
          <a-button type="danger">
            {{ $t('82670') }}
          </a-button>
        </DeletePopconfirm>
      </div>
    </div>
  </a-modal>
</template>
<script>
import { messageConfirm } from '@activity/pages/package/units/utils/message.js'
import DeletePopconfirm from '../delete-popconfirm'
export default {
  components: {
    DeletePopconfirm
  },
  inject: ['reloadPage2provide'],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    skuId: {
      type: [String, Number],
      default: ''
    },
    unitData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      options: [],
      form: {
        start_date: '',
        end_date: ''
      },
      rules: {
        start_date: [
          {
            required: true,
            message: this.$t('global_please_select'),
            trigger: 'change'
          },
          {
            validator: (r, value, callback) => {
              const { start_date, end_date } = this.form
              if (!start_date || !end_date) {
                return callback(new Error(this.$t('global_please_select')))
              }
              callback()
            },
            trigger: 'change'
          }
        ]
      }
    }
  },
  methods: {
    handleCancel() {
      this.$emit('cancel')
    },
    onChange(value) {
      const [start_date = '', end_date = ''] = value
      this.form.start_date = start_date
      this.form.end_date = end_date
    },
    validateForm() {
      const form = this.$refs.form
      return form.validate().catch(() => false)
    },
    getFormData() {
      const { start_date, end_date } = this.form
      return {
        start_date: `${start_date} 00:00:00`,
        end_date: `${end_date} 23:59:59`,
        sku_ids: [this.skuId]
      }
    },
    async postData({ url, message }) {
      const validate = await this.validateForm()
      if (!validate) {
        return
      }
      // 如果是主sku，则要检测cut off time、库存、上下架状态、删除的变动，然后显示提示弹窗
      const { share_rel_ship } = this.unitData || {}
      if (share_rel_ship === 2) {
        const result = await messageConfirm(this, this.$t('27807'))

        if (!result) {
          return
        }
      }
      const data = this.getFormData()
      const res = await ajax.postBody(
        url,
        {
          data
        },
        { loading: true }
      )
      if (!res.success) {
        return false
      }
      this.$message.success(message)
      this.handleCancel()
      this.reloadPage2provide()
      return true
    },
    async handleDelete() {
      await this.postData({
        url: ADMIN_API.act.destroy_schedule_by_date_range,
        message: this.$t('global_delete_success')
      })
    },
    async handleConfirm() {
      await this.postData({
        url: ADMIN_API.act.deactivate_schedule_by_date_range,
        message: this.$t('global_modify_success')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cascader {
  width: 400px;

  ::v-deep .ant-calendar-range-picker-input {
    text-align: left;
  }

  ::v-deep .ant-calendar-range-picker-separator {
    padding: 0 10px;
  }
}
.footer {
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-content: center;
  .extra-wrap {
    display: flex;
    align-items: center;
    .del-button {
      color: #ff4d4f;
      font-size: 14px;
      cursor: pointer;
    }
  }
}
</style>
