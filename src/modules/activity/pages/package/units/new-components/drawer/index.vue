<template>
  <a-drawer v-bind="drawerConf" :visible="visible" width="800px" :z-index="zIndex" @close="handleCancel">
    <template slot="title">
      <slot name="title">{{ title }}</slot>
    </template>
    <div class="slot-wrap">
      <slot></slot>
    </div>
    <div class="footer">
      <div class="extra-wrap">
        <slot name="footerExtra"></slot>
      </div>
      <div>
        <a-button style="margin-right: 8px;" @click="handleCancel">
          {{ $t('global_button_cancel') }}
        </a-button>
        <DeletePopconfirm v-if="popconfirm" :title="$t('82670')" @confirm="handleSave">
          <a-button :type="isDelete ? 'danger' : 'primary'">
            {{ confirmButtonText }}
          </a-button>
        </DeletePopconfirm>
        <a-button v-else :type="isDelete ? 'danger' : 'primary'" @click="handleSave">
          {{ confirmButtonText }}
        </a-button>
      </div>
    </div>
  </a-drawer>
</template>
<script>
import DeletePopconfirm from '../delete-popconfirm'

export default {
  components: {
    DeletePopconfirm
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    isDelete: {
      type: Boolean,
      default: false
    },
    confirmText: {
      type: String,
      default: ''
    },
    zIndex: {
      type: Number,
      default: 100
    },
    popconfirm: {
      type: Boolean,
      default: false
    },
    drawerConf: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    confirmButtonText() {
      const confirmText = this.confirmText.trim()
      return confirmText ? confirmText : this.$t('global_confirm')
    }
  },
  methods: {
    handleCancel() {
      this.$emit('cancel')
    },
    handleSave() {
      this.$emit('confirm')
    }
  }
}
</script>

<style scoped lang="scss">
.slot-wrap {
  padding: 50px 0;
}

.footer {
  position: absolute;
  padding: 10px 16px;
  right: 0;
  bottom: 0;
  width: 100%;

  border-top: 1px solid #e9e9e9;
  background: #fff;
  text-align: right;
  z-index: 3;
  display: flex;
  justify-content: space-between;
  align-content: center;
  .extra-wrap {
    display: flex;
    align-items: center;
  }
}
</style>
