<template>
  <div>
    <Drawer
      :visible="visible"
      :title="$t('81804')"
      :confirm-text="$t('global_button_save')"
      @cancel="cancel"
      @confirm="handleSave"
    >
      <a-alert v-if="!isSubSku" :message="$t('80924')" banner style="margin-bottom: 16px" />
      <a-alert v-if="isMainSku" :message="$t('80922')" banner style="margin-bottom: 16px" />
      <a-alert v-if="isSubSku" :message="$t('83068')" banner style="margin-bottom: 16px" />
      <a-form-model ref="form" layout="vertical" :rules="rules" :model="form" class="form">
        <a-form-model-item v-if="showRepeatTimeslot" :label="$t('81820')" prop="repeat_day_timeslot" required>
          <div>
            <div class="repeat-all-wrap">
              <a-checkbox :checked="checkedAll" :indeterminate="indeterminate" @change="handleCheckedAll">
                {{ $t('48131') }}
              </a-checkbox>
            </div>
            <div v-for="(item, index) in byTimeRepeatTimeslot" :key="index" class="repeat-wrap">
              <div class="repeat-label">
                <a-checkbox
                  :checked="item.checked"
                  :indeterminate="item.indeterminate"
                  @change="handleItemChecked(item)"
                >
                  {{ item.label }}
                </a-checkbox>
              </div>
              <a-select
                v-model="item.value"
                mode="multiple"
                :options="item.options"
                :placeholder="$t('global_select')"
                allow-clear
                class="cascader"
                @change="handleSelectChange(item)"
              ></a-select>
            </div>
          </div>
        </a-form-model-item>
        <a-form-model-item v-if="!showRepeatTimeslot" :label="$t('81820')" prop="repeat_day" required>
          <a-select
            v-model="form.repeat_day"
            mode="multiple"
            :max-tag-count="4"
            :placeholder="$t('global_select')"
            allow-clear
            class="cascader"
          >
            <a-select-option v-for="item in currentWeek" :key="item.value" :value="item.value">
              {{ item.value }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item v-if="isByHours" :label="$t('48119')" prop="by_time_repeat_timeslot" required>
          <a-select
            v-model="form.by_time_repeat_timeslot"
            mode="multiple"
            :max-tag-count="4"
            :placeholder="$t('global_select')"
            allow-clear
            class="cascader"
          >
            <a-select-option v-for="item in timeslotList" :key="item.id" :value="item.value">
              {{ item.value }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :label="$t('81822')" prop="start_date" required>
          <a-range-picker
            class="cascader"
            :disabled-date="disabledDate"
            value-format="YYYY-MM-DD"
            @change="onChange"
          >
            <a-icon slot="suffixIcon" type="calendar" />
          </a-range-picker>
        </a-form-model-item>
        <a-form-model-item v-if="showInventory" prop="inv_quantity" required>
          <template #label>
            <FormLabel
              :label="$t('package_inventory')"
              :label-value="invCannotEdit ? form.inv_quantity : ''"
            />
          </template>
          <PriceInput
            v-model="form.inv_quantity"
            :disabled="invCannotEdit || isSubSku"
            :show-text="invCannotEdit"
            :is-int="true"
            :max-value="MAX_INVENTORY"
            :tips="invQuantityTips"
            :text-style="inputStyle"
            :tooltip-title="isSubSku ? $t('27805') : ''"
            :placeholder="isSubSku ? $t('package_inventory') : ''"
            @edit="editRuleSetting('inventory')"
          />
        </a-form-model-item>
        <a-form-model-item prop="cost_price" required>
          <template #label>
            <FormLabel
              :label="$t('81823')"
              :label-value="getLabel('cost_price', 'cost_currency')"
              :tips="$t('112108')"
            />
          </template>
          <PriceInput
            v-model="form.cost_price"
            :show-text="showPriceText"
            :prefix="priceData.cost_currency"
            :tips="fixedPriceModelTips"
            :text-style="inputStyle"
            @edit="editRuleSetting('price')"
          />
        </a-form-model-item>
        <a-form-model-item prop="retail_price" required>
          <template #label>
            <FormLabel
              :label="$t('81824')"
              :label-value="getLabel('retail_price', 'selling_currency')"
              :tips="$t('112107')"
            />
          </template>
          <PriceInput
            v-model="form.retail_price"
            :show-text="showPriceText"
            :prefix="priceData.selling_currency"
            :tips="fixedPriceModelTips"
            @edit="editRuleSetting('price')"
          />
        </a-form-model-item>
        <a-form-model-item prop="cut_off_day" :label="$t('80903')" required>
          <a-select
            v-model="form.cut_off_day"
            :disabled="isSubSku"
            :placeholder="$t('global_select')"
            class="cutoff-select"
          >
            <a-select-option
              v-for="cutoffOption in cutoffDayList"
              :key="cutoffOption.value"
              :value="cutoffOption.value"
            >
              {{ cutoffOption.text }}
            </a-select-option>
          </a-select>
          <a-time-picker
            v-model="form.cut_off_date"
            class="time-picker"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="hh:mm"
            :disabled="isSubSku"
            :default-open-value="defaultOpenValue"
          />
        </a-form-model-item>
        <a-form-model-item v-if="!isAdd" required>
          <div slot="label" class="label-wrap">
            <span class="label-text">{{ $t('28792') }}</span>
            <a-switch v-model="form.published" :disabled="isSubSku"></a-switch>
          </div>
        </a-form-model-item>
      </a-form-model>
    </Drawer>
    <div
      id="save-edit-schedule-spm"
      v-bind="$root.bindIHTrack({ type: 'module', spm: 'AddEditSchedule' })"
    ></div>
  </div>
</template>
<script>
import Drawer from '../drawer'
import PriceInput from '../price-input'
import FormLabel from '../form-label'
import { week, cutoffDayList, hours, MAX_INVENTORY } from '@activity/pages/package/package_const.js'
import {
  getCutOffDay,
  getCutOffTime,
  getTimeslotList,
  getRepeatTimeslot
} from '@activity/pages/package/units/utils/index.js'
import { mapState } from 'vuex'
import moment from 'moment'
import { messageConfirm } from '@activity/pages/package/units/utils/message.js'

export default {
  name: 'MerchantEditScheduleDrawer',
  components: {
    Drawer,
    PriceInput,
    FormLabel
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    unitData: {
      type: Object,
      default: () => ({})
    },
    globalDataState: {
      type: Object,
      default: () => {
        return {}
      }
    },
    isAdd: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const arrayValidate = (r, v, callback) => {
      if (!v.length) {
        callback(new Error(this.$t('global_select')))
        return
      }
      callback()
    }
    const numberValidate = (r, v, callback) => {
      if (this.isSubSku) {
        callback()
        return
      }
      if (!v) {
        callback(new Error(this.$t('global_please_input')))
        return
      }
      callback()
    }
    const priceValidate = (r, v, callback) => {
      const { isFixedPrice } = this
      if (isFixedPrice) {
        callback()
        return
      }
      if (!v) {
        callback(new Error(this.$t('global_please_input')))
        return
      }
      callback()
    }
    const dateValidate = (r, v, callback) => {
      const { start_date, end_date } = this.form
      if (!start_date || !end_date) {
        callback(new Error(this.$t('global_select')))
        return
      }
      callback()
    }
    const cutoffValidate = (r, v, callback) => {
      if (this.isSubSku) {
        callback()
        return
      }
      const { cut_off_day, cut_off_date } = this.form
      if ((!cut_off_day && cut_off_day !== 0) || !cut_off_date) {
        callback(new Error(this.$t('global_select')))
        return
      }
      callback()
    }

    const repeatDayTimeslotValidate = (r, v, callback) => {
      const { allValues } = this
      if (!allValues.length) {
        callback(new Error(this.$t('global_select')))
        return
      }
      callback()
    }

    return {
      MAX_INVENTORY,
      defaultOpenValue: moment('00:00', 'hh:mm'),
      form: {
        repeat_day: [],
        start_date: undefined,
        end_date: undefined,
        by_time_repeat_timeslot: [],
        cost_price: undefined,
        retail_price: undefined,
        inv_quantity: undefined,
        cut_off_day: undefined,
        cut_off_date: undefined,
        calendar_header: {},
        published: true,
        repeat_day_timeslot: true
      },
      currentWeek: [],
      cutoffDayList,
      hours,
      rules: {
        repeat_day_timeslot: [
          {
            validator: repeatDayTimeslotValidate,
            trigger: 'change'
          }
        ],
        cost_price: [
          {
            validator: priceValidate,
            trigger: 'change'
          }
        ],
        inv_quantity: [
          {
            validator: numberValidate,
            trigger: 'change'
          }
        ],
        retail_price: [
          {
            validator: priceValidate,
            trigger: 'change'
          }
        ],
        repeat_day: [
          {
            validator: arrayValidate,
            trigger: 'change'
          }
        ],
        by_time_repeat_timeslot: [
          {
            validator: arrayValidate,
            trigger: 'change'
          }
        ],
        cut_off_day: [
          {
            validator: cutoffValidate,
            trigger: 'change'
          }
        ],
        start_date: [
          {
            validator: dateValidate,
            trigger: 'change'
          }
        ]
      },
      inputStyle: {
        paddingBottom: '10px'
      },
      checkedAll: true,
      byTimeRepeatTimeslot: [],
      indeterminate: false
    }
  },
  computed: {
    ...mapState({
      skuRuleSetting: 'skuRuleSetting'
    }),
    isSubSku() {
      // 子sku
      return this.unitData.share_rel_ship === 1
    },
    isMainSku() {
      // 子sku
      return this.unitData.share_rel_ship === 2
    },
    inventoryModel() {
      const { inv_model } = this.skuRuleSetting || {}
      return inv_model
    },
    showInventory() {
      const { inventoryModel } = this
      return inventoryModel === 3 || inventoryModel === 1
    },
    invCannotEdit() {
      const { inventoryModel } = this
      return inventoryModel === 3
    },
    isFixedPrice() {
      const { price_model } = this.skuRuleSetting || {}
      return price_model === 0
      // return price_model != 0
    },
    showPriceText() {
      const { isFixedPrice } = this
      return isFixedPrice
    },
    isByHours() {
      const { skuRuleSetting } = this
      const schedule_type = skuRuleSetting?.schedule_type ?? ''
      return schedule_type === 1 && !this.showRepeatTimeslot
    },
    priceData() {
      const { globalDataState, skuRuleSetting, isSubSku } = this
      const data = globalDataState[ADMIN_API.act.get_calendar_by_sku_id] || {}
      const { calendar_header = {}, default_cut_off_time, selling_currency, cost_currency } = data
      const {
        sku_fix_price,
        global_inv_quantity,
        repeat_day = [],
        by_time_repeat_timeslot,
        merchant_currency,
        selling_currency: selling_currency_rules
      } = skuRuleSetting || {}
      const { cost_price, retail_price, retail_currency, cost_currency: cost_currency_rules } =
        sku_fix_price || {}

      let inv = global_inv_quantity > 0 ? global_inv_quantity : ''
      if (isSubSku) {
        inv = this.$t('package_inventory')
      }
      return {
        calendar_header,
        default_cut_off_time,
        cost_price,
        retail_price,
        inv_quantity: inv,
        repeat_day: [...repeat_day],
        by_time_repeat_timeslot: getTimeslotList(by_time_repeat_timeslot).map((item) => item.value),
        selling_currency: selling_currency_rules || retail_currency || selling_currency,
        cost_currency: merchant_currency || cost_currency_rules || cost_currency,
        by_time_repeat_timeslot_object: by_time_repeat_timeslot
      }
    },
    timeslotList() {
      const { by_time_repeat_timeslot } = this.skuRuleSetting || {}
      return getTimeslotList(by_time_repeat_timeslot)
    },
    showRepeatTimeslot() {
      const { by_time_repeat_timeslot, repeat_day } = this.skuRuleSetting || {}
      return getRepeatTimeslot(by_time_repeat_timeslot, repeat_day).length > 1
    },
    allOptions() {
      return this.byTimeRepeatTimeslot.reduce((acc, curr) => {
        return [...acc, ...curr.options]
      }, [])
    },
    allValues() {
      return this.byTimeRepeatTimeslot.reduce((acc, curr) => {
        return [...acc, ...curr.value]
      }, [])
    },
    fixedPriceModelTips() {
      // 82676
      return klook.parseStr1(this.$t('111885'), {
        variable: `<span>${this.$t('111886')}</span>`
      })
    },
    invQuantityTips() {
      // 82675
      return klook.parseStr1(this.$t('111887'), {
        variable: `<span>${this.$t('111886')}</span>`
      })
    }
  },
  watch: {
    priceData: {
      deep: true,
      immediate: true,
      handler(v) {
        const data = v || {}
        const { default_cut_off_time = '', by_time_repeat_timeslot_object } = data
        this.form = Object.assign({}, this.form, getCutOffDay(default_cut_off_time), data)
        this.currentWeek = week.filter((item) => v.repeat_day.includes(item.value))
        this.byTimeRepeatTimeslot = this.getByTimeRepeatTimeslot(by_time_repeat_timeslot_object)
      }
    },
    byTimeRepeatTimeslot: {
      immediate: true,
      deep: true,
      handler() {
        const values = this.allValues
        const allOptions = this.allOptions
        if (!values.length) {
          this.checkedAll = false
          this.indeterminate = false
          return
        }
        if (allOptions.length === values.length) {
          this.checkedAll = true
          this.indeterminate = false
          return
        }
        this.indeterminate = true
      }
    }
  },
  methods: {
    handleSelectChange(item) {
      const { value, options } = item
      if (!value.length) {
        item.checked = false
        item.indeterminate = false
        return
      }
      if (options.length === value.length) {
        item.checked = true
        item.indeterminate = false
        return
      }
      item.indeterminate = true
    },
    handleItemChecked(item) {
      const checked = !item.checked
      item.checked = checked
      if (checked) {
        item.value = item.options.map((it) => it.value)
      } else {
        item.value = []
      }
    },
    handleCheckedAll(e) {
      const v = e.target.checked
      this.checkedAll = v
      this.byTimeRepeatTimeslot = this.byTimeRepeatTimeslot.map((item) => {
        item.checked = v
        if (v) {
          item.value = item.options.map((it) => it.value)
        } else {
          item.value = []
        }
        return item
      })
    },
    getByTimeRepeatTimeslot(object = {}) {
      const list = []
      for (const key in object) {
        if (Object.hasOwnProperty.call(object, key)) {
          const element = object[key]
          // const element = [...object[key], '12:00', '13:00']
          const label = week.find((item) => item.value === key).label
          list.push({
            label,
            key,
            checked: true,
            options: element.map((item) => ({ label: item, value: item })),
            value: [...element],
            indeterminate: false
          })
        }
      }
      return list
    },
    disabledDate(current) {
      return current && current < moment().subtract(1, 'day')
    },
    getTimeSlot() {
      const { showRepeatTimeslot, byTimeRepeatTimeslot } = this
      if (showRepeatTimeslot) {
        return byTimeRepeatTimeslot.reduce((acc, curr) => {
          const { key, value } = curr
          if (!value.length) {
            return acc
          }
          acc[key] = [...value]
          return acc
        }, {})
      }
      const { by_time_repeat_timeslot, repeat_day } = this.form
      const data = repeat_day.reduce((acc, curr) => {
        acc[curr] = [...by_time_repeat_timeslot]
        return acc
      }, {})
      return data
    },
    getParams() {
      const {
        repeat_day,
        start_date,
        end_date,
        cost_price,
        retail_price,
        inv_quantity,
        cut_off_day,
        cut_off_date,
        published
      } = this.form
      const { showPriceText, isSubSku } = this
      const { schedule_type, inv_model } = this.skuRuleSetting
      const { sku_id } = this.unitData
      const params = {
        sku_id,
        start_date: `${start_date} 00:00:00`,
        end_date: `${end_date} 23:59:59`,
        repeat_day,
        cut_off_time: isSubSku ? 0 : getCutOffTime({ cut_off_day, cut_off_date }),
        published
      }
      if (!showPriceText) {
        params.cost_price = cost_price
        params.retail_price = retail_price
      }

      if (inv_model !== 2) {
        params.inv_quantity = isSubSku ? 0 : +inv_quantity
      }

      if (schedule_type !== 0) {
        params.by_time_repeat_timeslot = this.getTimeSlot()
      }

      return params
    },
    async handleSave() {
      const validate = await this.validateForm()

      if (!validate) {
        return
      }
      // 如果是主sku，则要检测cut off time、库存、上下架状态、删除的变动，然后显示提示弹窗
      const { share_rel_ship } = this.unitData || {}
      if (share_rel_ship === 2) {
        const result = await messageConfirm(this, this.$t('27807'))

        if (!result) {
          return
        }
      }
      const data = this.getParams()
      const res = await ajax.postBody(
        ADMIN_API.act.creates_or_update_schedule_by_date_range,
        {
          data,
          noDefaultResponseInterceptor: true
        },
        { loading: true }
      )
      if (res && res.success) {
        this.$emit('confirm')
        this.$message.success(this.$t('global_success'))
      } else {
        this.$root.trackIHEvent('#save-edit-schedule-spm', {
          err_message: res?.error?.message ?? 'AddEditSchedule Error'
        })
      }
    },
    validateForm() {
      const form = this.$refs.form
      return form.validate().catch(() => false)
    },
    editRuleSetting(types) {
      // this.cancel()
      klook.bus.$emit('showRuleSetting', types)
    },
    getLabel(key, prefix) {
      if (this.isFixedPrice) {
        const pf = prefix ? _.get(this.form, prefix, '') + ' ' : ''
        return pf + _.get(this.form, key, '')
      }
      return ''
    },
    getPrefix(prefix) {
      const { price } = this.initialValue || {}
      const obj = price || {}
      return obj[prefix] || ''
    },
    onChange(value) {
      const [start_date = '', end_date = ''] = value
      this.form.start_date = start_date
      this.form.end_date = end_date
    },
    cancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
.form {
  width: 400px;
}
.repeat-all-wrap {
  padding: 14px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  margin-bottom: 14px;
}
.repeat-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 14px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  .repeat-label {
    width: 160px;
    ::v-deep .ant-checkbox-wrapper {
      color: rgba(0, 0, 0, 0.85);
    }
  }
}
.cutoff-select {
  width: 240px;
  margin-right: 8px;
}
.time-picker {
  width: 152px;
}

.cascader {
  width: 400px;

  ::v-deep .ant-calendar-range-picker-input {
    text-align: left;
  }

  ::v-deep .ant-calendar-range-picker-separator {
    padding: 0 10px;
  }
}

.label-wrap {
  display: inline-flex;
  align-items: center;
  .label-text {
    padding-right: 4px;
  }
}
</style>
