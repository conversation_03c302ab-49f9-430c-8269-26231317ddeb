<template>
  <a-modal
    width="800px"
    :title="$t('30295')"
    :visible.sync="visible"
    :footer="$slots.footer || null"
    :row-key="(r, i) => i"
    @cancel="$emit('cancel')"
  >
    <p v-html="failedData.tips"></p>

    <slot name="warn"></slot>

    <a-table
      style="margin: 12px 0;"
      :bordered="true"
      :pagination="false"
      :columns="columns"
      :data-source="failedData.data"
    ></a-table>

    <p v-if="failedData.ref_num_gt_50_hint">{{ failedData.ref_num_gt_50_hint }}</p>
  </a-modal>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      visible: true,
      tips: '',
      columns: [
        {
          title: __('48222'),
          dataIndex: 'sku_id_name',
          key: 'sku_id_name'
        },
        {
          title: 'Schedule',
          dataIndex: 'start_time',
          key: 'start_time'
        },
        {
          title: __('package_inventory'),
          dataIndex: 'inventory',
          key: 'inventory'
        },
        {
          title: __('package_price_cost'),
          dataIndex: 'cost_price',
          key: 'cost_price'
        }
      ],
      ref_num_gt_50_hint: ''
    }
  },
  computed: {
    failedData() {
      const { data } = this
      let { in_use_timeslot, in_use_list, ref_num_gt_50_hint } = data
      // format i18n weekday
      const inUseTimeslot = (in_use_timeslot || []).map((timeslot) => {
        let [weekday, time] = timeslot.split(' ')
        weekday = this.$t(`global_week_abbr_${weekday.toLowerCase()}`)

        return `${weekday} ${time}`
      })

      return {
        tips: this.$t('30296').replace(
          /\{.+\}/g,
          '<b style="word-break: break-word;">' + inUseTimeslot.join(' / ') + '</b>'
        ),
        data: in_use_list,
        ref_num_gt_50_hint
      }
    }
  }
}
</script>
