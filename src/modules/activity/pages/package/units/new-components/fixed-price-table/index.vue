<template>
  <table class="table">
    <thead class="thead bg">
      <tr class="tr">
        <th class="table-cell border"></th>
        <template v-if="editType === 'inventory'">
          <th class="table-cell" width="608">{{ $t('package_inventory') }}</th>
        </template>
        <template v-else>
          <th class="table-cell" :width="isTour ? 608 : 304">
            <a-tooltip :title="$t('82910')">{{ $t('81823') }} {{ getCurrency('cost_price') }}</a-tooltip>
            <a-tooltip :title="$t('112108')" placement="top">
              <a-icon class="table-cell-icon" type="info-circle" theme="filled" />
            </a-tooltip>
          </th>
          <th v-if="!isTour" class="table-cell" width="304">
            <a-tooltip :title="$t('82910')">{{ $t('81824') }} {{ getCurrency('retail_price') }}</a-tooltip>
            <a-tooltip :title="$t('112107')" placement="top">
              <a-icon class="table-cell-icon" type="info-circle" theme="filled" />
            </a-tooltip>
          </th>
        </template>
      </tr>
    </thead>
    <tbody>
      <template v-if="editType === 'inventory'">
        <tr v-for="(item, index) in list" :key="index" class="tr">
          <td class="table-cell bg fb">{{ item.unit_name }}</td>
          <td class="table-cell" width="608">
            <a-input
              v-model="item.inv_quantity"
              class="input"
              :class="{ 'not-error': !!item.cost_price }"
              :placeholder="$t('global_please_input')"
              @keyup.native="hangdleInv(item)"
            />
          </td>
        </tr>
      </template>
      <template v-else>
        <tr v-for="(item, index) in list" :key="index" class="tr">
          <td class="table-cell bg fb">{{ item.unit_name }}</td>
          <td class="table-cell" :class="{ disabled: disabled }" width="304">
            <a-input
              v-model="item.cost_price"
              class="input"
              :class="{ 'not-error': !!item.cost_price }"
              :placeholder="$t('global_please_input')"
              :disabled="disabled"
              @keyup.native="handleRound(item, 'cost_price')"
            />
          </td>
          <td v-if="!isTour" class="table-cell" :class="{ disabled: disabled }" width="304">
            <a-input
              v-model="item.retail_price"
              class="input"
              :class="{ 'not-error': !!item.retail_price }"
              :placeholder="$t('global_please_input')"
              :disabled="disabled"
              @keyup.native="handleRound(item, 'retail_price')"
            />
          </td>
        </tr>
      </template>
    </tbody>
  </table>
</template>
<script>
import { round2Decimal } from '@activity/utils'
import { MAX_PRICE, MAX_INVENTORY } from '@activity/pages/package/package_const.js'

export default {
  model: {
    prop: 'dataSource',
    event: 'change'
  },
  props: {
    dataSource: {
      type: Array,
      default: () => []
    },
    currencyData: {
      type: Object,
      default: () => null
    },
    editType: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    isTour: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {
    list: {
      get() {
        return this.dataSource
      },
      set() {
        this.$emit('change')
      }
    }
  },
  methods: {
    getCurrency(key) {
      const currencyData = this.currencyData
      if (!currencyData) {
        return ''
      }
      return currencyData[key] ? `(${currencyData[key]})` : ''
    },
    hangdleInv(item) {
      const inv_quantity = String(item.inv_quantity)
      const v = inv_quantity.replace(/[^\d]/g, '')
      item.inv_quantity = +v > MAX_INVENTORY ? MAX_INVENTORY : v
    },
    handleRound(item, field) {
      const v = round2Decimal(item[field])
      item[field] = +v > MAX_PRICE ? MAX_PRICE : v
    }
  }
}
</script>

<style lang="scss" scoped>
.table {
  width: 100%;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}

.border {
  position: relative;
}

.border::after {
  position: absolute;
  display: block;
  content: '';
  height: 22px;
  width: 1px;
  background: rgba(0, 0, 0, 0.06);
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  z-index: 11;
}

.bg {
  background: #fafafa;
}

.fb {
  font-weight: 500;
}

.thead {
  font-weight: 500;
  height: 48px;
}

.tr {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.table-cell {
  padding: 8px 16px;
  &.disabled {
    background-color: #f5f5f5;
  }

  &-icon {
    margin-left: 4px;
  }
}

.input {
  padding: 4px 0;
  border-color: transparent;
  width: 100%;
}

.has-error .input {
  padding: 4px 8px;
  border-color: #f5222d;
}

.has-error .input.not-error {
  border-color: transparent;
  padding: 4px 0;
}

.input:focus {
  border-color: transparent;
  outline: none;
  box-shadow: none;
}
</style>
