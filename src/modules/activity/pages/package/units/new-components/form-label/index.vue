<template>
  <div class="label-wrap">
    <div>{{ label }}</div>
    <template v-if="labelValue">
      <div>:</div>
      <div class="label-value">{{ labelValue }}</div>
    </template>
    <a-tooltip placement="right" overlay-class-name="common-tooltip-style">
      <template slot="title">{{ tips }}</template>
      <a-icon v-if="!!tips" class="label-wrap-icon" type="info-circle" theme="filled" />
    </a-tooltip>
  </div>
</template>
<script>
export default {
  props: {
    label: {
      type: String,
      default: ''
    },
    labelValue: {
      type: [String, Number],
      default: ''
    },
    tips: {
      type: String,
      default: ''
    }
  }
}
</script>
<style lang="scss" scoped>
.label-wrap {
  display: inline-flex;
  align-items: center;
  .label-value {
    font-weight: 500;
    padding-left: 8px;
  }

  &-icon {
    margin-left: 4px;
  }
}
</style>
