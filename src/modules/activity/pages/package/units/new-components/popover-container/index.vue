<template>
  <div class="popover-wrap">
    <div class="popover-header">
      <div class="title">{{ title }}</div>
      <a-icon type="close" @click="$emit('close')" />
    </div>
    <div>
      <slot></slot>
    </div>
    <div class="popover-footer">
      <div>
        <slot name="footerExtra"></slot>
      </div>
      <div>
        <a-button class="button" @click="$emit('close')">{{ $t('global_button_cancel') }}</a-button>
        <a-button class="button" type="primary" :disabled="disabled" @click="$emit('confirm')">
          {{ $t('global_confirm') }}
        </a-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.popover-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: inset 0px -1px 0px #f0f0f0;
  padding: 16px 24px;
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.45);
  .title {
    font-size: 16px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }
}

.popover-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  box-shadow: inset 0px 1px 0px #f0f0f0;
  .button {
    margin-left: 8px;
  }
}
</style>
