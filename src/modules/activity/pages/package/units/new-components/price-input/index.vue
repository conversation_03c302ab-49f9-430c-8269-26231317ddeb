<template>
  <div>
    <div v-if="showText" ref="text" :style="textStyle" class="text" v-html="tips">
      <!-- {{ tips }} -->
      <!-- 固定价格类型请点击<span @click="$emit('edit')">edit rule settings</span>进行修改 -->
    </div>
    <template v-else>
      <div v-if="disabledEdit" class="edit-wrap" :class="{ empty: !values }" @click="handleFill">
        <span>{{ fillText }}</span>
        <!-- <svg-icon v-show="!values" class="icon-edit" icon-name="edit" /> -->
      </div>
      <template v-else>
        <a-tooltip :title="tooltipTitle">
          <a-input
            v-model="values"
            :class="{ 'has-prefix': !!prefix }"
            class="input"
            :placeholder="inputPlaceholder"
            :disabled="disabled"
            @change="$emit('change')"
          >
            <span slot="prefix">{{ prefix }}</span>
          </a-input>
        </a-tooltip>
      </template>
    </template>
  </div>
</template>
<script>
import { round2Decimal } from '@activity/utils'
import { MAX_PRICE } from '@activity/pages/package/package_const.js'

export default {
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    showText: {
      type: Boolean,
      default: false
    },
    value: {
      type: [String, Number],
      default: ''
    },
    prefix: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    isInt: {
      type: Boolean,
      default: false
    },
    maxValue: {
      type: Number,
      default: MAX_PRICE
    },
    disabledEdit: {
      type: Boolean,
      default: false
    },
    disabledEditPlaceholder: {
      type: String,
      default: ''
    },
    tips: {
      type: String,
      default: ''
    },
    textStyle: {
      type: Object,
      default: () => ({})
    },
    tooltipTitle: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  computed: {
    inputPlaceholder() {
      const { placeholder } = this
      if (placeholder) {
        return placeholder
      }
      return __('global_please_input')
    },
    caclTooltipTitle() {
      const { tooltipTitle, disabled } = this
      if (disabled) {
        return tooltipTitle
      }
      return ''
    },
    fillText() {
      const { prefix, disabledEditPlaceholder, values } = this
      if (!values) {
        return disabledEditPlaceholder
      }
      return `${prefix} ${values}`
    },
    values: {
      set(v) {
        let value = ''
        if (this.isInt) {
          value = String(v).replace(/[^\d]/g, '')
        } else {
          value = round2Decimal(v)
        }
        if (+value > this.maxValue) {
          value = this.maxValue
        }
        this.$emit('input', value)
      },
      get() {
        return this.value + ''
      }
    }
  },
  mounted() {
    this.bindEvents()
  },
  updated() {
    this.bindEvents()
  },
  methods: {
    handleFill() {
      this.$emit('fill')
    },
    editEvent() {
      this.$emit('edit')
    },
    bindEvents() {
      if (!this.showText) {
        return
      }
      const text = this.$refs.text
      const span = text.querySelector('span')
      if (span) {
        span.removeEventListener('click', this.editEvent)
        span.addEventListener('click', this.editEvent)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.edit-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 5px 0 0;
  width: 160px;
  height: 32px;
  padding: 5px 5px 5px 12px;
  border-radius: 2px;
  font-weight: 600;
  font-size: 14px;
  background-color: #fff;
  width: 100%;
  border: 1px solid #f5222d;
  cursor: not-allowed;

  &.empty {
    font-weight: normal;
    color: rgba(0, 0, 0, 0.25);
    cursor: pointer;
  }

  .icon-edit {
    width: 18px;
    height: 18px;
  }
}
.input {
  width: 100%;
}
.text {
  font-size: 12px;
  color: #8c8c8c;
  ::v-deep span {
    color: #437dff;
    padding: 0 4px;
    cursor: pointer;
    font-weight: normal !important;
  }
}
::v-deep .has-prefix.ant-input-affix-wrapper .ant-input:not(:first-child) {
  padding-left: 46px;
}

::v-deep .ant-input-affix-wrapper .ant-input:not(:first-child) {
  padding-left: 11px;
}
</style>
