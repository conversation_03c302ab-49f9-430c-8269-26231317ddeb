<template>
  <div>
    <Drawer
      :visible="visible"
      :z-index="111"
      :drawer-conf="drawerConf"
      :confirm-text="$t('global_button_save')"
      @cancel="cancel"
      @confirm="handleSave"
    >
      <div slot="title" class="title-wrap">
        <div>{{ $t('81802') }}</div>
        <a-button v-if="!isAidPath" class="copy-button" :disabled="disabledCopy" @click.stop="handleShowCopy">
          {{ $t('104033') }}
        </a-button>
      </div>
      <RuleSettings
        ref="ruleSettings"
        :is-edit="true"
        :edit-type="editType"
        :init-data="skuRuleSetting"
        :list="unitList"
        :unit-data="unitData"
      />
    </Drawer>

    <FailedToSave v-if="visibleFailed" :data="failedData" @cancel="cancelFailed">
      <div v-if="supportDelete" slot="warn" class="failed-warn-text">
        {{ $t('89084') }}
      </div>

      <div v-if="supportDelete" slot="footer">
        <a-button @click="cancelFailed">
          {{ $t('global_cancel') }}
        </a-button>
        <a-popconfirm
          :title="$t('89111')"
          :ok-text="$t('global_confirm')"
          :cancel-text="$t('global_cancel')"
          :overlay-style="{
            maxWidth: '320px'
          }"
          @confirm="confirmDelFailed"
        >
          <a-button type="danger">
            {{ $t('89143') }}
          </a-button>
        </a-popconfirm>
      </div>
    </FailedToSave>
    <div
      id="save-rule-setting-spm"
      v-bind="$root.bindIHTrack({ type: 'module', spm: 'SaveEditRuleSetting' })"
    ></div>

    <!-- copy  -->
    <a-modal
      v-model="showCopyModal"
      width="780px"
      :title="$t('104033')"
      @cancel="showCopyModal = false"
      @ok="handleCopyOk"
    >
      <PackageCopyForm ref="packageCopyForm" :options="options" :show-to="false" @change="handleChange" />
    </a-modal>
  </div>
</template>
<script>
import Drawer from '../drawer'
import RuleSettings from '../rule-settings'
import FailedToSave from '../failed-to-save'
import { mapState, mapActions } from 'vuex'
import copySkuModel from '@activity/pages/package/units/mixins/copy-sku-model.js'

export default {
  name: 'RuleSettingsDrawer',
  inject: {
    reloadPage2provide: {
      default: () => {}
    }
  },
  components: {
    Drawer,
    RuleSettings,
    FailedToSave
  },
  mixins: [copySkuModel],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    unitData: {
      type: Object,
      default: () => ({})
    },
    editType: {
      type: String,
      default: ''
    },
    drawerConf: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      failedData: {},
      visibleFailed: false,
      supportDelete: false
    }
  },
  computed: {
    ...mapState({
      skuRuleSetting: 'skuRuleSetting'
    }),
    skuId() {
      return this.unitData.sku_id
    },
    unitList() {
      const { sku_fix_price, global_inv_quantity } = this.skuRuleSetting || {}
      const { cost_price, retail_price } = sku_fix_price || {}
      const { unit_name_format, unit_type } = this.unitData
      return [
        {
          unit_name: unit_name_format,
          cost_price,
          retail_price,
          unit_type,
          inv_quantity: global_inv_quantity < 0 ? '' : global_inv_quantity
        }
      ]
    },
    isSubSku() {
      // 子sku
      return this.unitData?.share_rel_ship === 1
    },
    disabledCopy() {
      const { isSubSku } = this
      return isSubSku
    },
    isAidPath() {
      return this.$route.path.includes('/aid')
    }
  },
  methods: {
    ...mapActions(['actionGetSkuRuleSetting']),
    validateRuleSettings() {
      const ruleSettings = this.$refs.ruleSettings
      return ruleSettings.validateForm()
    },
    getFormData() {
      const ruleSettings = this.$refs.ruleSettings
      const rule_setting = ruleSettings.getData()
      return rule_setting
    },
    async handleSave() {
      const validate = await this.validateRuleSettings()
      if (!validate) {
        return
      }
      const data = this.getFormData()
      const { sku_fix_price } = this.skuRuleSetting
      const { repeat_day, schedule_type, by_time_repeat_timeslot, inv_model, price_model, skus_fix_price } =
        data
      const params = {
        sku_id: this.skuId,
        repeat_day,
        schedule_type,
        by_time_repeat_timeslot,
        inv_model,
        price_model
      }
      // 全局库存数量才传
      if (inv_model === 3) {
        params.global_inv_quantity = data.global_inv_quantity
      }
      // 固定价格才传
      if (price_model === 0) {
        const { cost_price, retail_price } = skus_fix_price[0] || sku_fix_price
        params.sku_fix_price = {
          cost_price,
          retail_price
        }
      }
      const res = await ajax.postBody(
        ADMIN_API.act.update_sku_rule_setting,
        {
          data: params,
          noDefaultResponseInterceptor: true
        },
        { loading: true }
      )
      if (res && res.success) {
        // Use to delete being used in the units
        this.cacheUpdateSkuRuleSettingData = params
        this.updateSuccessCb(res)
      } else {
        this.$root.trackIHEvent(' #save-rule-setting-spm', {
          err_message: res?.error?.message ?? 'SaveEditRuleSetting Error'
        })
      }
    },
    updateSuccessCb(res) {
      this.cancel()
      this.actionGetSkuRuleSetting({
        sku_id: this.skuId,
        language: klook.getEditLang(),
        refresh: true
      })
      const result = res?.result ?? {}
      const {
        in_use_list = [],
        in_use_timeslot,
        ref_num_gt_50_hint,
        support_delete_in_use_timeslot,
        in_use_sku_ids
      } = result

      this.supportDelete = support_delete_in_use_timeslot
      this.inUseSkuIds = in_use_sku_ids

      if (Array.isArray(in_use_list) && in_use_list.length) {
        this.failedData = {
          in_use_list,
          in_use_timeslot,
          ref_num_gt_50_hint
        }

        this.$emit('update:visible', false)
        this.visibleFailed = true
        return
      }

      this.reloadPage2provide?.()
      klook.bus.$emit('getPackageSkuDataBus')
    },
    cancelFailed() {
      this.visibleFailed = false
      this.$emit('update:visible', true)
    },
    async confirmDelFailed() {
      const data = {
        package_id: +this.$route.query.package_id,
        sku_ids: this.inUseSkuIds,
        in_use_timeslot: this.failedData.in_use_timeslot
      }
      const response = await ajax.postBody(
        ADMIN_API.act.delete_sku_in_use_timeslot,
        {
          data
        },
        { loading: true }
      )

      if (response) {
        if (this.cacheUpdateSkuRuleSettingData) {
          const res = await ajax.postBody(
            ADMIN_API.act.update_sku_rule_setting,
            {
              data: this.cacheUpdateSkuRuleSettingData
            },
            { loading: true }
          )
          if (res && res.success) {
            this.updateSuccessCb(res)
            this.cacheUpdateSkuRuleSettingData = null
          }
        }
        this.cancelFailed()
        this.reloadPage2provide()
      }
    },
    resetForm() {
      this.$refs.ruleSettings?.resetForm?.()
    },
    cancel() {
      this.$emit('cancel')
      this.resetForm()
    }
  }
}
</script>

<style lang="scss" scoped>
.title-wrap {
  display: flex;
  align-items: center;

  .copy-button {
    margin-left: 30px;
  }
}

.failed-warn-text {
  font-weight: 600;
  line-height: 150%;
  color: #ff4d4f;
}
</style>
