<template>
  <a-form-model ref="form" layout="vertical" :model="form" :rules="rules">
    <div v-if="!editType">
      <a-alert v-if="isEdit" :message="$t('80911')" banner class="alert-tips" />
      <div v-show="showRepeatdayText" class="text">
        <div class="text-title">{{ $t('80901') }}:</div>
        <div>
          <span v-for="(obj, i) in calcRepeatTimeslot" :key="i" class="text-group">
            {{ `${obj.groupList.join(', ')}: ${obj.repeatTimeslotStr}` }}
          </span>
        </div>
        <a-button class="button" type="link" icon="redo" @click="handleReset">Reset timeslot</a-button>
      </div>
      <div v-show="!showRepeatdayText">
        <a-form-model-item :label="$t('80901')" prop="repeat_day" required>
          <div class="checkbox-wrap hr">
            <a-checkbox
              :disabled="isSubSku"
              :checked="checkedAll"
              :indeterminate="indeterminate"
              @change="handleCheckedAll"
            >
              {{ $t('48131') }}
            </a-checkbox>
          </div>
          <div class="checkbox-wrap">
            <div v-for="(item, index) in week" :key="index" class="checkbox" @click="handleCheckbox(item)">
              <a-checkbox
                :checked="form.repeat_day && form.repeat_day.includes(item.value)"
                :disabled="isSubSku"
              ></a-checkbox>
              <span class="label-text">{{ item.label }}</span>
            </div>
          </div>
        </a-form-model-item>
        <a-form-model-item class="form-item" :label="$t('80898')" prop="schedule_type" required>
          <a-radio-group v-model="form.schedule_type" size="large" :disabled="isSubSku">
            <a-radio :value="0">
              {{ $t('80899') }}
            </a-radio>
            <a-radio :value="1">
              {{ $t('80900') }}
            </a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item
          v-if="form.schedule_type === 1"
          class="form-item"
          :label="$t('81819')"
          prop="by_time_repeat_timeslot"
          required
        >
          <div v-for="(item, index) in form.by_time_repeat_timeslot" :key="item.id" class="time-picker-wrap">
            <a-time-picker
              v-model="item.value"
              class="time-picker"
              value-format="HH:mm"
              format="HH:mm"
              placeholder="hh:mm"
              :default-open-value="defaultOpenValue"
              :disabled="item.disabled || isSubSku"
              v-bind="timePickerAttrs"
            />
            <i
              v-show="form.by_time_repeat_timeslot && form.by_time_repeat_timeslot.length > 1"
              class="common-delete-btn"
              @click="deleteItem(index)"
            >
              <svg-icon icon-name="trash" />
            </i>
          </div>
          <a-button
            v-if="!(isWifiSim && form.by_time_repeat_timeslot.length === 1)"
            class="add-btn"
            type="link"
            icon="plus"
            @click="addTimeslot"
            >Add</a-button
          >
        </a-form-model-item>
      </div>
      <a-form-model-item v-if="!isTotalInv" class="form-item" :label="$t('80904')" prop="inv_model" required>
        <a-radio-group v-model="form.inv_model" size="large" :disabled="isSubSku">
          <a-radio :value="1">
            {{ $t('80905') }}
          </a-radio>
          <a-radio :value="2">
            {{ $t('80906') }}
          </a-radio>
        </a-radio-group>
      </a-form-model-item>
      <a-form-model-item
        v-if="isTotalInv"
        class="form-item"
        :label="$t('27733')"
        prop="global_inv_quantity"
        required
      >
        <FixedPriceTable
          :data-source="form.skus_fix_price"
          edit-type="inventory"
          :disabled="isSubSku"
          :currency-data="currencyData"
          :is-tour="isTour"
        />
      </a-form-model-item>
      <a-form-model-item class="form-item" :label="$t('80907')" prop="price_model" required>
        <a-radio-group v-model="form.price_model" size="large" :disabled="isSubSku">
          <a-radio :value="0">
            {{ $t('80908') }}
          </a-radio>
          <a-radio :value="1">
            {{ $t('192743') }}
          </a-radio>
        </a-radio-group>
      </a-form-model-item>
    </div>
    <a-form-model-item
      v-if="showInventoryTable"
      class="form-item"
      :label="$t('27733')"
      prop="global_inv_quantity"
      required
    >
      <FixedPriceTable
        :data-source="form.skus_fix_price"
        edit-type="inventory"
        :disabled="isSubSku"
        :currency-data="currencyData"
        :is-tour="isTour"
      />
    </a-form-model-item>
    <a-form-model-item
      v-if="showPriceTable"
      :label="isTour ? $t('81823') : $t('82222')"
      prop="skus_fix_price"
      required
    >
      <FixedPriceTable
        :data-source="form.skus_fix_price"
        edit-type="price"
        :disabled="disabledPriceChange"
        :currency-data="currencyData"
        :is-tour="isTour"
      />
    </a-form-model-item>
  </a-form-model>
</template>
<script>
import FixedPriceTable from '../fixed-price-table'
import { week, SIM_SUB_CATEGORY_IDS, WIFI_SUB_CATEGORY_IDS } from '@activity/pages/package/package_const.js'
import { getDefaultTimeslot, getTimeslotList, getRepeatTimeslot } from '@activity/pages/package/units/utils'
import { mapGetters } from 'vuex'
import moment from 'moment'
import * as merLogin from '@klook/merchant-login'

export default {
  name: 'RuleSettings',
  components: {
    FixedPriceTable
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    initData: {
      type: Object,
      default: () => null
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    editType: {
      type: String,
      default: ''
    },
    unitData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    const defaultValidateFun = (r, v, callback) => {
      if (typeof v !== 'number') {
        return callback(new Error(this.$t('global_please_select')))
      }
      callback()
    }

    const priceValidateFun = (r, v, callback) => {
      const { isTotalInv, isFixedPrice } = this
      const validatePrice = v.every((item) => {
        const { cost_price, retail_price } = item
        if (!this.isTour) {
          return cost_price !== '' && retail_price !== ''
        }
        return cost_price !== ''
      })
      if (!validatePrice && isFixedPrice) {
        return callback(new Error(this.$t('global_please_input')))
      }
      const validateInv = v.every((item) => {
        const { inv_quantity } = item
        return inv_quantity
      })
      if (!validateInv && isTotalInv && this.editType === 'inventory') {
        return callback(new Error(this.$t('global_please_input')))
      }
      callback()
    }

    const validateInv = (r, v, callback) => {
      const {
        isTotalInv,
        form: { skus_fix_price = [] }
      } = this
      if (!isTotalInv) {
        callback()
        return
      }

      const validateInv = skus_fix_price.every((item) => {
        const { inv_quantity } = item
        return !!inv_quantity
      })

      if (!validateInv) {
        return callback(new Error(this.$t('global_please_input')))
      }
      callback()
    }

    return {
      defaultOpenValue: moment('00:00', 'hh:mm'),
      form: {
        repeat_day: [],
        schedule_type: 0,
        inv_model: 1,
        price_model: 0,
        by_time_repeat_timeslot: [getDefaultTimeslot()],
        skus_fix_price: []
      },
      week: _.cloneDeep(week),
      checkedAll: false,
      indeterminate: false,
      rules: {
        skus_fix_price: [
          {
            validator: priceValidateFun,
            trigger: 'change'
          }
        ],
        by_time_repeat_timeslot: [
          {
            validator: (r, v, callback) => {
              const value = v.map((item) => {
                return item.value
              })

              if (value.some((item) => !item)) {
                return callback(new Error(this.$t('global_please_select')))
              }

              let isDuplicate = false
              let obj = {}
              for (let index = 0; index < value.length; index++) {
                const element = value[index]
                if (obj[element]) {
                  isDuplicate = true
                  break
                }
                obj[element] = true
              }
              if (isDuplicate) {
                return callback(new Error('Duplicate time !'))
              }

              callback()
            },
            trigger: 'change'
          }
        ],
        repeat_day: [
          {
            validator: (r, v, callback) => {
              if (v?.length) {
                return callback()
              }
              callback(new Error(this.$t('global_please_select')))
            },
            trigger: 'change'
          }
        ],
        schedule_type: [
          {
            validator: defaultValidateFun,
            trigger: 'change'
          }
        ],
        inv_model: [
          {
            validator: defaultValidateFun,
            trigger: 'change'
          }
        ],
        price_model: [
          {
            validator: defaultValidateFun,
            trigger: 'change'
          }
        ],
        global_inv_quantity: [
          {
            validator: validateInv,
            trigger: 'change'
          }
        ]
      },
      reset: false,
      userInfo: {}
    }
  },
  computed: {
    ...mapGetters(['merchantCurrency', 'isTour']),
    disabledPriceChange() {
      const { approvalStatus, isEdit } = this
      return isEdit && approvalStatus === 'PendingApproval'
      // return isEdit && approvalStatus === 'ToBeSubmitted'
    },
    isSubSku() {
      // 子sku
      return this.unitData?.share_rel_ship === 1
    },
    approvalStatus() {
      return this.unitData?.ticket_status ?? ''
    },
    showRepeatdayText() {
      const { calcRepeatTimeslot, isEdit, form, reset } = this
      return calcRepeatTimeslot.length > 1 && isEdit && form.schedule_type === 1 && !reset
    },
    showPriceTable() {
      const { isFixedPrice, editType } = this
      return isFixedPrice && editType != 'inventory'
    },
    showInventoryTable() {
      const { editType, isTotalInv } = this
      const types = ['new', 'inventory']
      return types.includes(editType) && isTotalInv
    },
    isTotalInv() {
      return this.form.inv_model === 3
    },
    isFixedPrice() {
      const { form } = this
      return form.price_model === 0
    },
    currencyData() {
      const {
        merchantCurrency,
        initData,
        userInfo: { currency }
      } = this
      const { selling_currency, merchant_currency } = initData || {}
      return {
        cost_price: merchant_currency || merchantCurrency || currency,
        retail_price: selling_currency || merchantCurrency || currency
      }
    },
    calcRepeatTimeslot() {
      const { by_time_repeat_timeslot, repeat_day } = this.initData || {}
      return getRepeatTimeslot(by_time_repeat_timeslot, repeat_day)
    },
    subCategoryId() {
      return this.$store.state.categoryInfo?.sub_category_id ?? 0
    },
    isWifiSim() {
      return [...SIM_SUB_CATEGORY_IDS, ...WIFI_SUB_CATEGORY_IDS].includes(this.subCategoryId)
    },
    timePickerAttrs() {
      return this.isWifiSim
        ? {
            hourStep: 24,
            minuteStep: 60
          }
        : {}
    }
  },
  watch: {
    form: {
      deep: true,
      async handler() {
        const isb = await this.validateForm({ clearValidate: true })
        this.$emit('updateFinished', isb)
      }
    },
    list: {
      deep: true,
      immediate: true,
      handler(v) {
        if (v && v.length) {
          this.form.skus_fix_price = v.map((item) => {
            const { cost_price, retail_price, inv_quantity } = item
            return {
              sku_id: item.sku_id || 0,
              unit_type: item.unit_type,
              unit_name: item.unit_name,
              cost_price: this.isNumber(cost_price),
              retail_price: this.isNumber(retail_price),
              inv_quantity: this.isNumber(inv_quantity)
            }
          })
        }
      }
    },
    'form.repeat_day': {
      deep: true,
      immediate: true,
      handler(v) {
        if (v?.length === this.week?.length) {
          this.checkedAll = true
          this.indeterminate = false
        } else {
          this.checkedAll = false
          if (v?.length) {
            this.indeterminate = true
          } else {
            this.indeterminate = false
          }
        }
      }
    },
    initData: {
      deep: true,
      immediate: true,
      handler(v) {
        if (!v) {
          return
        }
        this.initForm(v)
      }
    },
    'form.skus_fix_price': {
      deep: true,
      immediate: true,
      handler(v) {
        if (this.isTotalInv) {
          const data = v[0]
          this.form.global_inv_quantity = data?.inv_quantity ?? ''
        }
      }
    }
  },
  async created() {
    this.userInfo = await merLogin.getUserInfo()
  },
  methods: {
    isNumber(v) {
      if (_.isNumber(v)) {
        return v
      }
      if (!v) {
        return ''
      }
      return v
    },
    handleReset() {
      this.reset = true
      this.form.by_time_repeat_timeslot = [getDefaultTimeslot()]
      this.form.repeat_day = []
      this.form.schedule_type = undefined
    },
    resetForm() {
      const form = this.$refs.form
      form.resetFields()
    },
    initForm(data = {}) {
      const {
        repeat_day = [],
        schedule_type,
        inv_model,
        price_model,
        by_time_repeat_timeslot,
        global_inv_quantity
      } = data
      const timeslot = getTimeslotList(by_time_repeat_timeslot, this.isEdit)
      this.form = {
        ...this.form,
        repeat_day,
        schedule_type,
        inv_model,
        price_model,
        by_time_repeat_timeslot: timeslot,
        global_inv_quantity: global_inv_quantity < 0 ? undefined : global_inv_quantity
      }
    },
    getData() {
      const { schedule_type, inv_model, price_model, by_time_repeat_timeslot, repeat_day } = this.form
      const isEdit = this.isEdit
      const data = {
        repeat_day,
        schedule_type,
        inv_model,
        price_model,
        skus_fix_price: price_model === 0 ? this.getSkusPrice() : [],
        by_time_repeat_timeslot: this.getTimeslot(schedule_type, by_time_repeat_timeslot)
      }
      if (isEdit) {
        data.global_inv_quantity = this.getInv()
      }
      return data
    },
    getInv() {
      const { global_inv_quantity, skus_fix_price } = this.form
      const { isTotalInv } = this
      if (isTotalInv) {
        const data = skus_fix_price[0]
        const num = data?.inv_quantity ?? global_inv_quantity
        return +num
      }
      return +global_inv_quantity
    },
    getTimeslot(type = 0, time_slot = []) {
      if (type === 0) {
        return []
      }
      return time_slot.map((item) => item.value)
    },
    getSkusPrice() {
      const isTour = this.isTour
      return this.form.skus_fix_price.map((item) => {
        const { unit_type, cost_price, retail_price, sku_id } = item

        return {
          sku_id: sku_id || 0,
          unit_type,
          cost_price: +cost_price,
          retail_price: isTour ? +cost_price : +retail_price
        }
      })
    },
    deleteItem(index) {
      this.form.by_time_repeat_timeslot.splice(index, 1)
    },
    addTimeslot() {
      this.form.by_time_repeat_timeslot.push(getDefaultTimeslot())
    },
    validateForm(options) {
      const form = this.$refs.form
      const res = form?.validate().catch(() => false)
      options?.clearValidate && form?.clearValidate && form.clearValidate()
      return res
    },
    handleCheckedAll(event) {
      const checked = event.target.checked
      this.checkedAll = checked
      if (checked) {
        this.form.repeat_day = this.week.map((item) => item.value)
      } else {
        this.form.repeat_day = []
      }
    },
    handleCheckbox(item) {
      if (this.isSubSku) return

      const repeat_day = this.form.repeat_day
      const value = item.value
      if (repeat_day?.includes(value)) {
        this.form.repeat_day = repeat_day.filter((it) => it != value)
      } else {
        this.form.repeat_day = [...new Set([...repeat_day, value])]
      }
      this.$nextTick(() => {
        this.$refs.form.validateField('repeat_day')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.alert-tips {
  margin-bottom: 24px;
}
.form-item {
  margin-bottom: 20px;
}

.form-input {
  width: 400px;
}

.checkbox-wrap {
  padding: 10px 0;
  display: flex;
  align-items: center;
  .checkbox {
    margin-right: 10px;
    cursor: pointer;
    .label-text {
      padding: 0 8px;
    }
  }
  &.hr {
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  }
}
.time-picker-wrap {
  display: flex;
  align-items: center;
  padding: 8px 0;
  .time-picker {
    width: 200px;
    margin-right: 8px;
  }
  .common-delete-btn {
    width: 24px;
    height: 24px;
    margin: 4px 8px 4px 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.add-btn {
  padding: 0;
  display: block;
}

.button {
  padding: 10px 0;
  color: #437dff;
  font-weight: 600;
}

.text {
  font-size: 14px;
  line-height: 22px;
  font-weight: 500;
  padding-bottom: 32px;
  color: #000;

  .text-title {
    font-weight: 400;
  }

  .text-group:not(:last-of-type) {
    margin-right: 32px;
    &::after {
      content: '';
      position: relative;
      left: 16px;
      display: inline-block;
      width: 1px;
      height: 14px;
      background-color: #d9d9d9;
      vertical-align: middle;
    }
  }
}
</style>
