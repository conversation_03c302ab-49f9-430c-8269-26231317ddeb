<template>
  <a-popover
    :visible="visible"
    placement="leftTop"
    class="merchant-unit-settings-popover-containter"
    overlay-class-name="merchant-unit-settings-popover"
    :destroy-tooltip-on-hide="true"
  >
    <template slot="content">
      <PopoverContainer :title="$t('81803')" @close="$emit('close')" @confirm="handleSave">
        <UnitForm ref="unit" :is-settings="true" :data="unitForm" />
      </PopoverContainer>
    </template>
    <slot></slot>
  </a-popover>
</template>

<script>
import { mapState } from 'vuex'
import PopoverContainer from '@activity/pages/package/units/new-components/popover-container'
import UnitForm from '@activity/pages/package/units/new-components/create-unit-form'
import { getUnitFormData, initUnitForm } from '@activity/pages/package/units/utils'

export default {
  components: {
    UnitForm,
    PopoverContainer
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    unitData: {
      type: Object,
      default: () => ({})
    },
    skuId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      unitForm: {}
    }
  },
  computed: {
    ...mapState({
      clickTarget: (state) => state.clickTarget
    })
  },
  watch: {
    unitData: {
      deep: true,
      immediate: true,
      handler(v) {
        this.initData(v)
      }
    },
    clickTarget(target) {
      // 弹窗打开的时候才检查，提高性
      if (target && this.visible) {
        const appDom = document.querySelector('#app')
        const panelDom = this.$el

        const isInApp = appDom && appDom.contains(target)
        const isInPanel = panelDom && panelDom.contains(target)

        if (isInApp && !isInPanel) {
          this.$emit('close')
        }
      }
    },
    visible(v) {
      if (!v) {
        this.$nextTick(() => {
          this.initData(this.unitData)
          this.resetForm()
        })
      }
    }
  },
  methods: {
    initData(unitData) {
      this.unitForm = initUnitForm(unitData)
    },
    resetForm() {
      const form = this.$refs.unit
      return form.resetForm()
    },
    validateForm() {
      const form = this.$refs.unit
      return form.validateForm()
    },
    async handleSave() {
      const validate = await this.validateForm()
      const data = getUnitFormData(this.unitForm)
      if (!validate) {
        return
      }
      const res = await ajax.postBody(
        ADMIN_API.act.update_sku,
        {
          data: {
            ...data,
            sku_id: this.skuId
          }
        },
        {
          loading: true
        }
      )
      if (res && res.success) {
        this.$message.success(this.$t('global_success'))
        this.$emit('close')
        klook.bus.$emit('getPackageSkuDataBus')
        return
      }
      this.$message.error(this.$t('global_failure'))
    }
  }
}
</script>

<style lang="scss">
.merchant-unit-settings-popover .ant-popover-inner-content {
  padding: 0;
}
</style>
