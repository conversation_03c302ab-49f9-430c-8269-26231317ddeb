<template>
  <a-modal
    class="sync-api-setting-container"
    :visible.sync="_visible"
    :width="920"
    :title="$t('73518')"
    @cancel="handleClose"
  >
    <header class="header">
      {{ $t('73519') }}
    </header>

    <div class="body">
      <div class="date-range">
        <span class="__label">{{ $t('73521') }}</span>
        <a-range-picker v-model="dateRange" :disabled="!checkedList.length" :disabled-date="disabledDate" />
      </div>
      <div class="operator">
        <p class="__label">Select sku to sync for package date range</p>

        <div class="__checkbox">
          <a-checkbox :indeterminate="indeterminate" :checked="checkAll" @change="onCheckAllChange">
            Check all
          </a-checkbox>

          <span class="__selected">Selected {{ checkedList.length }} items</span>
        </div>
      </div>

      <div class="content">
        <a-checkbox-group v-model="checkedList" @change="onChange" class="unit-list">
          <div
            v-for="unit in unitList"
            :key="unit.sku_id"
            class="unit-item"
            :class="{
              'is-published': unit.published
            }"
          >
            <div class="unit-item-content">
              <a-checkbox :value="unit.sku_id" :disabled="!supportedSkuIds.includes(unit.sku_id)" />

              <OverflowItem :content="getUnitName(unit)" class="__unit">
                <template #title>
                  Sku id:
                  <span style="color: rgba(0, 0, 0, 0.85);">
                    {{ unit.sku_id || '-' }}
                  </span>
                </template>
              </OverflowItem>

              <OverflowItem
                :title="`${$t('package_unit_type')}:`"
                :content="getUnitTypeName(unit)"
                style="width: 90px"
              />

              <OverflowItem :title="`${$t('package_list_status')}:`" style="width: 90px;">
                <template #content>
                  <span :style="{ color: unit.published ? '#36B37E' : 'rgba(0, 0, 0, .45)' }">
                    {{
                      unit.published ? $t('package_list_unit_published') : $t('package_list_unit_unpublished')
                    }}
                  </span>
                </template>
              </OverflowItem>

              <OverflowItem :title="`${$t('27753')}:`" style="width: 150px">
                <template #content>
                  {{ getRelationshipText(unit) }}
                  <a-tooltip placement="top" arrow-point-at-center v-if="getRelationshipTip(unit)">
                    <template slot="title">
                      <span>{{ getRelationshipTip(unit) }}</span>
                    </template>
                    <a-icon type="info-circle" theme="filled" style="margin-left: 4px; color: #FF9C00;" />
                  </a-tooltip>
                </template>
              </OverflowItem>
            </div>

            <div class="__no-sync-warn" v-if="!supportedSkuIds.includes(unit.sku_id)">{{ $t('73522') }}</div>
          </div>
        </a-checkbox-group>
      </div>
    </div>

    <template slot="footer">
      <a-button @click="handleClose">
        {{ $t('global_cancel') }}
      </a-button>
      <a-button type="primary" :disabled="!canBeSubmit" @click="handleConfirm">
        {{ $t('global_submit') }}
      </a-button>
    </template>
  </a-modal>
</template>

<script>
import { mapState } from 'vuex'
import OverflowItem from '../../components/OverflowItem'
import { bestMatchLang } from '@activity/utils'
import {
  isSPUType,
  isSPUTypeDefault,
  isHotelType,
  isNotSPUType,
  SHARE_RELATIONSHIPS
} from '@activity/pages/package/package_const.js'
import moment from 'moment'
import { pmsConfirm } from '@activity/utils'

export default {
  name: 'ApiSyncSetting',
  components: {
    OverflowItem
  },
  inject: ['packageId'],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    unitList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      checkedList: [],
      checkAll: true,
      indeterminate: false,

      dateRange: []
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(v) {
        if (v) {
          Object.assign(this, {
            checkedList: _.cloneDeep(this.supportedSkuIds),
            checkAll: true,
            indeterminate: false,
            dateRange: [moment(), moment().add(3, 'months')]
          })
        }
      }
    }
  },
  computed: {
    ...mapState({
      packageFresherSettingData: 'packageFresherSettingData'
    }),
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    supportedSkuIds() {
      return (
        _.get(
          this.packageFresherSettingData,
          'package.page_setting.api_sync_button.data.supported_sku_ids',
          []
        ) || []
      )
    },
    canBeSubmit() {
      return this.dateRange?.length === 2 && this.checkedList.length
    }
  },
  methods: {
    disabledDate(current) {
      return (
        current &&
        ((this.dateRange[0] &&
          current >
            moment()
              .add(6, 'months')
              .startOf('day')) ||
          current < moment().startOf('day') ||
          current >
            moment()
              .add(10, 'years')
              .startOf('day'))
      )
    },
    getRelationshipText(data) {
      const { step, share_rel_ship } = data
      // 如果step没数据，就显示-
      if (!step) {
        return '-'
      }

      const target = SHARE_RELATIONSHIPS.find((item) => item.value === share_rel_ship)

      return target ? target.text : '-'
    },
    getRelationshipTip(data) {
      const { share_rel_ship } = data

      if (share_rel_ship === 1) {
        return this.$t('73522')
      }

      return ''
    },
    getUnitTypeName(data) {
      const { unitType, unit_type, local } = data || {}

      const unitTypeList = [...isSPUType, ...isSPUTypeDefault, ...isHotelType, ...isNotSPUType]
      const target = unitTypeList.find((item) => item.value === (unitType == null ? unit_type : unitType))

      if (target) {
        return target.text
      }

      // 已保存的
      return bestMatchLang('unit_name', 'language', local || []) || '-'
    },
    getUnitName({ local }) {
      return bestMatchLang('unit_name', 'language', local || []) || '-'
    },
    handleClose() {
      this._visible = false
    },
    async handleConfirm() {
      let temp = await pmsConfirm.call(this, {
        content: this.$t('73523')
      })

      if (!temp) {
        return
      }

      let [start_date, end_date] = this.dateRange

      start_date = moment(start_date).format('YYYY-MM-DD')
      end_date = moment(end_date).format('YYYY-MM-DD')

      await ajax.postBody(ADMIN_API.act.sync_skus_inv_price, {
        data: {
          package_id: +this.packageId,
          start_date,
          end_date,
          skus: this.checkedList.map((sku_id) => ({
            sku_id
          }))
        }
      })

      this.$message.success(this.$t('global_success'))
      klook.bus.$emit('refreshUnitList', { clearActiveId: false })
      this.handleClose()
    },
    onChange(checkedList) {
      this.indeterminate = !!checkedList.length && checkedList.length < this.supportedSkuIds.length
      this.checkAll = checkedList.length === this.supportedSkuIds.length
    },
    onCheckAllChange(e) {
      Object.assign(this, {
        checkedList: e.target.checked ? _.cloneDeep(this.supportedSkuIds) : [],
        indeterminate: false,
        checkAll: e.target.checked
      })
    }
  }
}
</script>

<style scoped lang="scss">
.sync-api-setting-container {
  ::v-deep {
    .ant-modal-header {
      border-bottom: 1px solid #f0f0f0;
    }
    .ant-modal-footer {
      padding: 10px 16px;
      border-top: 1px solid #f0f0f0;
    }
  }

  .header {
    padding: 10px 16px;
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
    border-radius: 2px;
  }

  .body {
    margin: 20px 0;

    .date-range {
      margin-bottom: 20px;

      .__label {
        display: block;
        margin-bottom: 2px;
        font-weight: 400;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
      }
    }

    .operator {
      margin-bottom: 10px;

      .__label {
        font-weight: 500;
        color: #212121;
      }

      .__checkbox {
        padding: 0 12px;

        .__selected {
          margin-left: 16px;
          color: #757575;
        }
      }
    }

    .unit-list {
      width: 100%;
    }

    .unit-item {
      padding: 20px 12px;
      margin-bottom: 10px;
      background-color: #fafafa;
      border: 1px solid #e0e0e0;
      border-radius: 4px;

      .__unit {
        flex: 1;
        min-width: 160px;
      }

      &.is-published {
        background-color: #f0fffa;
        border: 1px solid #757575;
      }

      &-content {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;

        ::v-deep .overflow-item {
          margin: 0 12px;
        }
      }

      .__no-sync-warn {
        margin-top: 20px;
        padding: 10px 16px;
        background-color: #fff1f0;
        border: 1px solid #ffccc7;
        border-radius: 2px;

        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
}
</style>
