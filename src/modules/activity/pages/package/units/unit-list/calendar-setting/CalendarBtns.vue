<template>
  <div class="calendar-btns-container">
    <div id="js-calendar-header-buttons" class="calendar-header-buttons">
      <CalendarButton ref="setting" :unit-data="unitData" :calendar-setting-data="calendarSettingData" />

      <a-button
        v-if="!isSubSku && !isComboSku"
        class="btn-margin-right"
        :disabled="(!canEdit && !inventoryCanEdit) || disabledAddTimeslot"
        :data-spm-module="getAddTimeslotSpm"
        data-spm-virtual-item="__virtual"
        @click="handleAddTimeslot"
      >
        <a-icon type="plus" style="color: #1790ff;" />
        {{ $t('27787') }}
        <QuestionIcon :message="$t('27790')" placement="top" />
      </a-button>

      <label
        v-if="!lockAutoExtend.hidden"
        v-tooltip="{
          visible: !!lockAutoExtend.disabled,
          content: lockAutoExtend.tips
        }"
      >
        <a-button
          :disabled="
            (!canEdit && !auditCanEdit && !inventoryCanEdit) ||
              isTripMapped ||
              skuModel.inv_model === 4 ||
              lockAutoExtend.disabled ||
              isComboSku
          "
          :title="isTripMapped ? $t('28910') : ''"
          :data-spm-module="getAutoExtSpm"
          data-spm-virtual-item="__virtual"
          class="btn-margin-right"
          @click="handleAutoExtend"
        >
          <a-icon type="edit" style="color: #1790ff;" />
          {{ $t('27788') }}
        </a-button>
      </label>

      <a-button
        v-if="!isMerchant && !isComboSku"
        :disabled="(!canEdit && !auditCanEdit && !inventoryCanEdit) || isTripMapped"
        :title="isTripMapped ? $t('28910') : ''"
        class="btn-margin-right"
        :data-spm-module="getMSPSpm"
        data-spm-virtual-item="__virtual"
        @click="handleMSP"
      >
        <a-icon type="edit" style="color: #1790ff;" />
        {{ $t('44909') }}
      </a-button>

      <a-popover
        :visible="noviceTipsVisible"
        arrow-point-at-center
        placement="topRight"
        overlayClassName="bulk-modification-popover"
        :getPopupContainer="getBulkModificationPopoverContainer"
      >
        <div slot="content" style="font-size: 16px; width: 220px;">
          <a-icon type="info-circle" theme="filled" style="color: #FFAB00;" />
          <span style="font-size: 14px;">
            {{ $t('29258') }}
          </span>
        </div>
        <a-button
          :disabled="!canEdit && !auditCanEdit && !inventoryCanEdit"
          type="primary"
          :data-spm-module="getEditbulkSpm"
          data-spm-virtual-item="__virtual"
          @click="handleBulkModification"
        >
          <a-icon type="edit" />
          {{ $t('27789') }}
        </a-button>
      </a-popover>
      <!-- <a-button
        :disabled="!canEdit && !auditCanEdit"
        type="primary"
        @click="handleBulkModification"
      >
        <a-icon type="edit" />
        {{ $t('27789') }}
      </a-button> -->
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import CalendarButton from './CalendarButton.vue'
import QuestionIcon from '@activity/pages/package/units/components/QuestionIcon.vue'

export default {
  name: 'CalendarBtns',
  components: {
    CalendarButton,
    QuestionIcon
  },
  inject: ['calendar', 'getEditLockValueByPath'],
  props: {
    unitData: {
      type: Object,
      required: true
    },
    calendarSettingData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      noviceTipsVisible: false // 新手提示
    }
  },
  watch: {
    clickTarget() {
      this.noviceTipsVisible = false
    }
  },
  computed: {
    ...mapState({
      canEdit: (state) => state.canEdit,
      auditCanEdit: (state) => state.auditCanEdit,
      inventoryCanEdit: (state) => state.inventoryCanEdit,
      clickTarget: (state) => state.clickTarget,
      skuModel: (state) => state.skuModel,
      isMerchant: 'isMerchant'
    }),
    ...mapGetters(['isTripMapped', 'packageProductType']),
    // combo
    isComboSku() {
      return this.packageProductType === 1
    },
    isSubSku() {
      const { share_rel_ship } = this.unitData
      return share_rel_ship === 1
    },
    disabledAddTimeslot() {
      return this.unitData.ticket_status === 'PendingApproval' && this.calendarSettingData.is_auto_extend
    },
    lockAutoExtend() {
      return this.getEditLockValueByPath({
        sku_id: this.unitData.sku_id,
        path: 'auto_extend_button',
        base_path: 'page_setting'
      })
    },
    getAutoExtSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `AutoExtend?oid=${oid}&trg=manual`
    },
    getMSPSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `MSPSetting?oid=${oid}&trg=manual`
    },
    getEditbulkSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `BulkEdit?oid=${oid}&trg=manual`
    },
    getAddTimeslotSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `AddTimeslot?oid=${oid}&trg=manual`
    }
  },
  mounted() {
    setTimeout(() => {
      const { step } = this.unitData
      if (!step || !step.includes('sku_calendar')) {
        this.noviceTipsVisible = true
      }
    }, 200)
  },
  methods: {
    openCalendarSetting() {
      const calendarSetting = this.$refs.setting
      calendarSetting && calendarSetting.handleOpenSetting()
    },
    handleAddTimeslot() {
      this.calendar.openSkuModel()
      this.GTMHookItem('Price&Inventory|CalendarAddTimeslot_Click')
    },
    handleAutoExtend() {
      this.$emit('showAutoExtend', true)
      this.GTMHookItem('Price&Inventory|CalendarAutoExtend_Click')
    },
    handleMSP() {
      this.$emit('showMSP', true)
    },
    getBulkModificationPopoverContainer() {
      return this.$el.querySelector('#js-calendar-header-buttons')
    },
    handleBulkModification() {
      this.calendar.openBulkModification()
    }
  }
}
</script>

<style lang="scss" scoped>
$marginBottom: 10px;
.calendar-btns-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: $marginBottom;
  .btn-margin-right {
    margin-right: $marginBottom;
  }

  .calendar-header-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;

    ::v-deep .ant-btn {
      margin-bottom: 10px;
      margin-right: 10px;
    }
  }
}
</style>
