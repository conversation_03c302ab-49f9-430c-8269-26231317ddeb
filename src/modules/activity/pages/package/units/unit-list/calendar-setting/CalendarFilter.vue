<template>
  <div class="calendar-filter">
    <div class="calendar-filter-tips">
      <div class="calendar-filter-tips-common">
        <div v-if="priceText || inventoryText" class="calendar-filter-tips-item">
          <span class="calendar-filter-tips-paint yellow" />
          <span>
            <span>
              {{ priceText }}
              <QuestionIcon
                v-if="unitData.price_model === 0"
                :message="$t('27739')"
                style="font-size: 12px;"
              />
            </span>
            <span v-if="priceText && inventoryText">
              /
            </span>
            <span>
              {{ inventoryText }}
              <QuestionIcon v-if="unitData.inv_model === 3" :message="$t('27734')" style="font-size: 12px;" />
            </span>
          </span>
        </div>
        <div v-if="relationshipText" class="calendar-filter-tips-item">
          <span class="calendar-filter-tips-paint grey" />
          <span>{{ relationshipText }}</span>
        </div>
        <div class="calendar-filter-tips-item">
          <svg-icon icon-name="eye-legend" style="font-size: 28px; margin-right: 4px;" />
          <span>
            {{ $t('27798') }}
            <QuestionIcon :message="$t('29095')" placement="top" />
          </span>
        </div>
        <div class="calendar-filter-tips-item">
          <span class="legend-hh-mm">hh:mm</span>
          <span>{{ $t('27799') }}</span>
        </div>
        <div class="calendar-filter-tips-item">
          <span class="legend-remaining"> 0<span style="color: #000;">/99</span> </span>
          <span>
            {{ $t('28158') }}
            <QuestionIcon :message="$t('28795')" placement="top" />
          </span>
        </div>
      </div>
      <div class="calendar-filter-tips-price">
        <slot name="priceTips" :legendColors="legendColors">
          <div class="calendar-filter-tips-item" v-for="item of legendColors" :key="item.icon">
            <svg-icon :icon-name="item.icon" class="legend-item-icon"></svg-icon>
            {{ item.label }}
            <QuestionIcon v-if="item.message" :message="item.message" placement="top" />
          </div>
        </slot>
      </div>

      <costDataSourceTip />
    </div>

    <slot name="filter">
      <a-popover v-model="filterVisible" trigger="click" placement="bottomRight">
        <div slot="title" style="margin-top: 7px; text-align: left;">
          Filter by Schedule
        </div>
        <div slot="content" class="filter-popover-container">
          <div v-if="!isBdAudit" class="filter-row">
            <span class="filter-label">{{ $t('27810') }}</span>
            <div class="filter-content">
              <a-checkbox
                :indeterminate="indeterminate"
                :checked="checkAll"
                style="margin: 0 0 12px 0;"
                @change="handleCheckAll"
              >
                {{ $t('global_button_all') }}
              </a-checkbox>
              <a-checkbox-group v-model="checkedList" @change="handleCheckChange">
                <a-checkbox
                  v-for="option in optionList"
                  :key="option.value"
                  :value="option.value"
                  style="display: block; margin: 0 0 12px 0;"
                >
                  <a-tooltip placement="top" arrow-point-at-center>
                    <template slot="title">
                      {{ option.tip }}
                    </template>
                    {{ option.text }}
                  </a-tooltip>
                </a-checkbox>
              </a-checkbox-group>
            </div>
          </div>
          <div class="filter-row">
            <span class="filter-label">Items</span>
            <div class="filter-content">
              <LegendItem
                v-for="legend in localLegendColors"
                v-model="legend.checked"
                itemBackgroundColor="transparent"
                checkboxDirection="start"
                :key="legend.label"
                :label="legend.label"
                :label-color="legend.color"
                :icon="legend.icon"
                @sendSpm="sendItemSpm"
              >
              </LegendItem>
            </div>
          </div>
          <footer class="filter-footer">
            <a-button size="small" style="margin-right: 8px;" @click="handleResetFilter(true)">{{
              $t('global_reset')
            }}</a-button>
            <a-button size="small" type="primary" @click="handleSaveFilter">{{ $t('global_save') }}</a-button>
          </footer>
        </div>
        <div
          class="calendar-filter-btn"
          :class="{ 'calendar-filter-btn__active': filterVisible }"
          :data-spm-module="getAddTimeslotSpm"
          data-spm-virtual-item="__virtual"
        >
          <svg-icon :icon-name="filtered ? 'filter-on' : 'filter'" style="font-size: 18px;"></svg-icon>
          {{ $t('redemptions_filter_text') }}
        </div>
      </a-popover>
    </slot>
    <div id="filter_ifempty_spm" :data-spm-module="getIfemptySpm" data-spm-virtual-item="__virtual"></div>
    <div id="filter_item_spm" :data-spm-module="getItemSpm" data-spm-virtual-item="__virtual"></div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import QuestionIcon from '@activity/pages/package/units/components/QuestionIcon.vue'
import { SHARE_RELATIONSHIPS, INVENTORY_MODELS, PRICE_MODELS } from '@activity/pages/package/package_const.js'
import LegendItem from './components/LegendItem.vue'
import costDataSourceTip from './components/costDataSourceTip'

export default {
  name: 'CalendarFilter',
  inject: ['legendColors', 'calendarSetting'],
  components: {
    costDataSourceTip,
    QuestionIcon,
    LegendItem
  },
  props: {
    unitData: {
      type: Object,
      required: true
    },
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      filterVisible: false,

      indeterminate: false,
      checkAll: true,

      optionList: [
        {
          text: __('27811'),
          tip: __('27812'),
          value: 'empty'
        },
        {
          text: __('27813'),
          tip: __('27814'),
          value: 'inempty'
        }
      ],

      checkedList: [],
      localLegendColors: []
    }
  },
  watch: {
    filterVisible(v) {
      if (v) {
        this.localLegendColors = _.cloneDeep(this.legendColors)
        this.checkedList = _.cloneDeep(this.value)
      }
    }
  },
  computed: {
    getAddTimeslotSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `FiltercCalendar?oid=${oid}&trg=manual`
    },
    getIfemptySpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `IfEmptyFilter?oid=${oid}&trg=manual`
    },
    getItemSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `ItemFilter?oid=${oid}&trg=manual`
    },
    ...mapState({
      isMerchant: 'isMerchant',
      isBdAudit: (state) => state.isBdAudit
    }),
    filtered() {
      if (this.filterVisible) {
        return this.checkedList.length !== 2 || this.localLegendColors.some((color) => !color.checked)
      }

      return this.value.length !== 2 || this.legendColors.some((color) => !color.checked)
    },
    relationshipText() {
      const { share_rel_ship } = this.unitData

      // 子 sku
      if (share_rel_ship !== 1) {
        return ''
      }

      const target = SHARE_RELATIONSHIPS.find((item) => item.value === share_rel_ship)
      return target ? target.text : '-'
    },
    inventoryText() {
      const { inv_model } = this.unitData

      if (inv_model !== 3) {
        return ''
      }

      const target = INVENTORY_MODELS.find((item) => item.value === inv_model)
      return target ? target.text : '-'
    },
    priceText() {
      const { price_model } = this.unitData

      if (price_model !== 0) {
        return ''
      }

      const target = PRICE_MODELS.find((item) => item.value === price_model)
      return target ? target.text : '-'
    }
  },
  methods: {
    sendItemSpm() {
      this.$tracker.track('action', '#filter_item_spm')
    },
    handleResetFilter() {
      this.indeterminate = false
      this.checkAll = true
      this.checkedList = ['empty', 'inempty']
      this.localLegendColors.forEach((item) => {
        item.checked = true
      })
    },
    handleSaveFilter() {
      this.$emit('input', this.checkedList)
      this.localLegendColors.forEach((item, index) => {
        this.legendColors[index].checked = item.checked
      })
      this.filterVisible = false
    },
    handleCheckAll(e) {
      const allOptionsValue = this.optionList.map((item) => item.value)
      this.indeterminate = false
      this.checkAll = e.target.checked
      this.checkedList = e.target.checked ? allOptionsValue : []
      this.GTMHookItem('Price&Inventory|CalendarScheduleIfEmptyFilter_Click')
      this.$tracker.track('action', '#filter_ifempty_spm')
    },
    handleCheckChange(val) {
      const { optionList } = this
      this.indeterminate = !!val.length && val.length < optionList.length
      this.checkAll = val.length === optionList.length
      this.GTMHookItem('Price&Inventory|CalendarScheduleIfEmptyFilter_Click')
      this.$tracker.track('action', '#filter_ifempty_spm')
    }
  }
}
</script>

<style lang="scss" scoped>
$marginStandardPixel: 12px;

.calendar-filter {
  display: flex;
  justify-content: space-between;
  background-color: #fafafa;
  padding: 12px;
  .calendar-filter-tips-common,
  .calendar-filter-tips-price {
    display: flex;
    align-items: center;
  }
  .calendar-filter-tips-price {
    margin-top: 12px;
  }
  .legend-item-icon {
    font-size: 14px;
    margin-right: 6px;
  }
  &-tips {
    display: flex;
    flex-direction: column;
    &-item {
      margin-right: 24px;
      display: flex;
      align-items: center;
      color: #000;
      font-size: 10px;
      line-height: 16px;
      .legend-hh-mm {
        margin-right: 4px;
        color: rgba(0, 0, 0, 0.45);
        text-decoration-line: line-through;
      }

      .legend-remaining {
        margin-right: 4px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
    &-paint {
      display: inline-block;
      margin-right: 4px;
      width: 32px;
      height: 16px;
      border-radius: 2px;
      &.yellow {
        background-color: rgba(255, 171, 0, 0.1);
      }

      &.grey {
        background-color: rgba(0, 0, 0, 0.09);
      }
    }
  }
  &-btn {
    height: fit-content;
    font-size: 16px;
  }
}

.filter-row {
  .filter-label {
    display: block;
    margin-bottom: $marginStandardPixel;
  }
  .filter-content {
    display: flex;
    flex-direction: column;
  }
}

.calendar-filter-btn {
  position: relative;
  top: 2px;
  right: 12px;
  user-select: none;
  cursor: pointer;
  &__active {
    color: rgb(0, 145, 255);
  }
}

.filter-popover-container {
  min-width: 300px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.filter-footer {
  display: flex;
  justify-content: flex-end;
}

.color-branch-group {
  display: flex;
  width: 100%;
  flex-direction: column;
  padding: 6px;
  margin: 10px 0;
  background-color: #fafafa;
  ::v-deep .ant-radio-wrapper {
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
