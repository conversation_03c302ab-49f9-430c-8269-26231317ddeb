<template>
  <section class="calendar-table">
    <div v-for="(dateRow, index) in filteredDataList" :key="index" class="calendar-table-wrap">
      <div class="calendar-table-row">
        <!-- :key="item.id" -->
        <!-- :key="item.date" -->
        <!-- :key="`${item.date}-${item.data.length}`" -->
        <CalendarTableItem
          v-for="item in dateRow"
          :key="item.date"
          :data="item"
          :unit-data="unitData"
          :date-range="dateRange"
          :more="moreArr[index]"
          v-bind="$attrs"
        />
      </div>

      <!-- 更多 -->
      <div v-if="showMore(dateRow)" class="calendar-more" @click="handleMore(index)">
        <svg-icon icon-name="page-down" class="calendar-more-icon" :class="{ rotate: !moreArr[index] }" />
      </div>
    </div>

    <!-- 埋点 -->
    <div id="ssp_setting_spm" :data-spm-module="getSspSpm" data-spm-virtual-item="__virtual"></div>
    <div id="modify_inv_spm" :data-spm-module="getInvSpm" data-spm-virtual-item="__virtual"></div>
    <div id="modify_cost_spm" :data-spm-module="getCostSpm" data-spm-virtual-item="__virtual"></div>
    <div id="modify_rp_spm" :data-spm-module="getRpSpm" data-spm-virtual-item="__virtual"></div>
    <div id="modify_sp_spm" :data-spm-module="getSpSpm" data-spm-virtual-item="__virtual"></div>
    <div id="del_schedule_spm" :data-spm-module="getDelScheduleSpm" data-spm-virtual-item="__virtual"></div>
    <div id="act_schedule_spm" :data-spm-module="getActScheduleSpm" data-spm-virtual-item="__virtual"></div>
  </section>
</template>

<script>
import { mapState } from 'vuex'
import { getDateFromStartTime } from './utils'
import CalendarTableItem from './calendar-table-item/index.vue'

// 组件刷新用
let id = 0

export default {
  name: 'CalendarTable',
  inject: ['calendar'],
  components: {
    CalendarTableItem
  },
  props: {
    data: {
      type: Array,
      required: true
    },
    dateList: {
      type: Array,
      required: true
    },
    dateRange: {
      type: Array,
      required: true
    },
    filterByEmpty: {
      type: Array,
      required: true
    },
    unitData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      moreNum: 4, // 超过 4 个就显示 more
      moreArr: [undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined],
      isFolded: true, // 是否初始折叠
      maxLen: 1000 // data长度大于这个值就一定折叠
    }
  },
  computed: {
    getSspSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `SSPSetting?oid=${oid}&trg=manual`
    },
    getInvSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `ModifyInventory?oid=${oid}&trg=manual`
    },
    getCostSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `ModifyCost?oid=${oid}&trg=manual`
    },
    getRpSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `ModifyRetailPrice?oid=${oid}&trg=manual`
    },
    getSpSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `ModifySellingPrice?oid=${oid}&trg=manual`
    },
    getDelScheduleSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `DeleteSchedule?oid=${oid}&trg=manual`
    },
    getActScheduleSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `ActiveSchedule?oid=${oid}&trg=manual`
    },
    ...mapState({
      destinationToday: (state) => state.destinationToday
    }),
    dataList() {
      if (!this.data) {
        return this.dateList
      }

      // 给 dateList 填入数据
      // 暂存 data 的 index，每次遍历都从上次的 index 开始
      // 这里已优化达到极致性能
      let dataIndex = 0
      const { data, dateList, destinationToday } = this
      const dataLen = data.length
      const list = _.cloneDeep(dateList)
      const destinationDate = moment(destinationToday)

      list.forEach((weekItem) => {
        weekItem.forEach((item) => {
          // 刷新用
          item.id = id
          id += 1

          // 加上标记
          item.isOutdate = destinationDate.isAfter(item.date, 'day')
          item.isToday = destinationDate.isSame(item.date, 'day')

          // 填充数据
          while (dataIndex < dataLen) {
            const dataItem = data[dataIndex]
            const date = getDateFromStartTime(dataItem.start_time)

            if (date && date === item.date) {
              item.originalData.push(dataItem)
              item.data.push(dataItem)
              dataIndex += 1
            } else {
              break
            }
          }
        })
      })

      return list
    },
    filteredDataList() {
      // 筛选数据（originalData里面储存着全数据）
      // const list = [...this.dataList]
      const isEmpty = this.filterByEmpty.includes('empty')
      const isInEmpty = this.filterByEmpty.includes('inempty')

      return this.dataList.map((weekItem) =>
        weekItem.map((item) => {
          if (isEmpty && isInEmpty) {
            item.data = [...item.originalData]
          } else if (isEmpty) {
            item.data = item.originalData.filter((dataItem) => dataItem.is_empty)
          } else if (isInEmpty) {
            item.data = item.originalData.filter((dataItem) => !dataItem.is_empty)
          } else {
            item.data = []
          }
          return { ...item }
        })
      )

      // list.forEach((weekItem) => {
      //   weekItem.forEach((item) => {
      //     if (isEmpty && isInEmpty) {
      //       item.data = [...item.originalData]
      //     } else if (isEmpty) {
      //       item.data = item.originalData.filter(
      //         (dataItem) => dataItem.is_empty
      //       )
      //     } else if (isInEmpty) {
      //       item.data = item.originalData.filter(
      //         (dataItem) => !dataItem.is_empty
      //       )
      //     } else {
      //       item.data = []
      //     }
      //   })
      // })

      // return list
    }
  },
  watch: {
    filteredDataList() {
      // 是否自动折叠
      if (this.data && this.data.length > this.maxLen) {
        this.moreArr = this.moreArr.map(() => true)
      } else {
        this.moreArr = this.moreArr.map(() => this.isFolded)
      }
    }
  },
  created() {
    // 埋点
    klook.bus.$on('sendSspSpm', () => {
      this.$tracker.track('action', '#ssp_setting_spm')
    })
    klook.bus.$on('sendInvSpm', () => {
      this.$tracker.track('action', '#modify_inv_spm')
    })
    klook.bus.$on('sendCostSpm', () => {
      this.$tracker.track('action', '#modify_cost_spm')
    })
    klook.bus.$on('sendRpSpm', () => {
      this.$tracker.track('action', '#modify_rp_spm')
    })
    klook.bus.$on('sendSpSpm', () => {
      this.$tracker.track('action', '#modify_sp_spm')
    })
    klook.bus.$on('sendDelScheduleSpm', () => {
      this.$tracker.track('action', '#del_schedule_spm')
    })
    klook.bus.$on('sendActScheduleSpm', () => {
      this.$tracker.track('action', '#act_schedule_spm')
    })
  },
  beforeDestroy() {
    // 销毁埋点数据，避免多次订阅
    klook.bus.$off('sendSspSpm')
    klook.bus.$off('sendInvSpm')
    klook.bus.$off('sendCostSpm')
    klook.bus.$off('sendRpSpm')
    klook.bus.$off('sendSpSpm')
    klook.bus.$off('sendDelScheduleSpm')
    klook.bus.$off('sendActScheduleSpm')
  },
  methods: {
    showMore(row) {
      // 这里不能根据 more 来判断显示
      return row.some((item) => item.data.length > this.moreNum)
    },
    handleMore(index) {
      this.$set(this.moreArr, index, !this.moreArr[index])
    }
  }
}
</script>

<style lang="scss" scoped>
.calendar-table {
  width: 100%;
  padding: 0 50px 24px;
  position: relative;
  background-color: #fafafa;

  &-wrap {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &-row {
    display: flex;
    justify-content: space-between;
  }

  .calendar-more {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 8px;

    width: 100%;
    height: 20px;
    text-align: center;
    background-color: #fff;
    border-radius: 4px;
    cursor: pointer;

    &-icon.rotate {
      transform: rotate(180deg);
    }
  }
}
</style>
