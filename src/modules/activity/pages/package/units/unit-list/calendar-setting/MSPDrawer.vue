<!-- original: klk-admin msp  -->
<template>
  <a-drawer
    class="msp-drawer-container"
    :title="$t('44910')"
    :width="1000"
    :visible="visible"
    :headerStyle="drawerHeaderStyle"
    :bodyStyle="drawerBodyStyle"
    placement="right"
    @close="close"
  >
    <a-spin :spinning="loading">
      <div class="body">
        <div v-show="step === 0" style="display: flex">
          <label class="amount-title">{{ $t('44911') }}</label>
          <div>
            <div class="amount-list">
              <a-form-model ref="form" :model="form" layout="horizontal">
                <a-form-model-item
                  v-for="(msp, index) in form.prices"
                  :key="msp.id"
                  class="amount-item"
                  :prop="'prices.' + index + '.amount'"
                  :rules="{
                    required: true,
                    trigger: ['blur', 'change'],
                    index: index,
                    validator: validatePrice
                  }"
                >
                  <a-input
                    type="number"
                    v-model="msp.amount"
                    :placeholder="$t('44912')"
                    @keyup.native="proving(msp)"
                  ></a-input>
                  <a-select
                    v-model="msp.currency"
                    :placeholder="$t('44913')"
                    filterable
                    show-search
                    style="margin-right: 10px"
                    @change="changeCurrency(msp, index)"
                  >
                    <a-select-option
                      v-for="curr in currencyOptions"
                      :value="curr.value"
                      :key="curr.value"
                      :disabled="currencies.includes(curr.value)"
                    >
                      {{ curr.label }}
                    </a-select-option>
                  </a-select>
                  <circle-delete-button class="__del" @click="deletePrice(msp, index)" />
                </a-form-model-item>
              </a-form-model>
            </div>
            <div style="cursor: pointer; color: #0091ff" @click="addPrice">+ {{ $t('44914') }}</div>
          </div>
        </div>
        <div v-show="step === 1" style="position: relative">
          <msp-warn
            :alert_type="alert_type"
            :total="tableTotal"
            :price_model="price_model"
            :tableData="tableData"
          />
        </div>
      </div>
    </a-spin>
    <div class="footer">
      <template v-if="step === 0">
        <a-button @click="close">{{ $t('44916') }}</a-button>
        <a-button type="primary" @click="submitPrices">{{ $t('44915') }}</a-button>
      </template>

      <template v-else>
        <a-button @click="step = 0">{{ $t('44926') }}</a-button>
        <a-button type="primary" @click="close">{{ $t('44925') }}</a-button>
      </template>
    </div>
  </a-drawer>
</template>

<script>
import { mapState } from 'vuex'
import { SUPPORT_CURRENCY_SYMBO } from '@activity/pages/package/package_const.js'
import CircleDeleteButton from '../../../../../components/CircleDeleteButton'
import MspWarn from './components/MSPWarn'

export default {
  name: 'MspDrawer',
  components: {
    MspWarn,
    CircleDeleteButton
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    price_model: Number,
    unitData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      drawerHeaderStyle: {
        borderBottom: 'none',
        paddingLeft: '16px',
        paddingRight: '16px'
      },
      drawerBodyStyle: {
        padding: '60px 16px'
      },

      loading: false,

      dataCopy: [],
      alert_type: 1,
      step: 0,
      tableData: [],

      form: {
        prices: [{ currency: '', amount: '', flag: 0 }]
      },

      tableTotal: 0
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(v) {
        if (v) {
          this.getPrices()
        }
      }
    }
  },
  computed: {
    ...mapState({
      canEdit: (state) => state.canEdit,
      inventoryCanEdit: (state) => state.inventoryCanEdit,
      skuModel: (state) => state.skuModel
    }),
    currencies() {
      return this.form.prices.map((i) => i.currency)
    },
    title() {
      return this.step ? this.$t('44919') : this.$t('44910')
    },
    packageId() {
      return +this.$route.query.package_id
    },
    selectedCurrencyList() {
      return this.specialSellingPriceList.map((item) => item.currency)
    },
    currencyOptions() {
      return SUPPORT_CURRENCY_SYMBO.map((item) => item[0])
        .sort()
        .map((v) => ({
          label: v,
          value: v
        }))
    }
  },
  methods: {
    validatePrice(rule, value, callback) {
      const { index } = rule

      const { currency, amount } = this.form.prices[index]

      if ((!currency && amount === '') || (currency && amount !== '')) {
        callback()
      } else {
        callback(new Error('Please fill in this.'))
      }
    },
    close() {
      this.$emit('update:visible', false)
    },
    proving(msp) {
      if (typeof msp.amount !== 'string') {
        msp.amount = msp.amount.toString()
      }
      msp.amount = msp.amount.replace(/[^.\d]/g, '') //清除"数字"和"."以外的字符
      msp.amount = msp.amount.replace(/^\./g, '') //验证第一个字符是数字而不是.
      msp.amount = msp.amount.replace(/\.{2,}/g, '.') //只保留第一个. 清除多余的.
      msp.amount = msp.amount.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.') //只允许输入一个小数点
      msp.amount = msp.amount.replace(/^(-)*(\d+)\.(\d\d).*$/, '$1$2.$3') //只能输入两个小数
    },
    changeCurrency(msp, index) {
      this.$refs.form.validateField(`prices.${index}.amount`)

      if (index < this.dataCopy.length && msp.currency !== this.dataCopy[index].currency) {
        this.dataCopy[index].flag = 1
        delete this.form.prices[index].id
      }
    },
    async getPrices() {
      let res = await ajax.get(ADMIN_API.act.get_msp, {
        params: {
          sku_id: this.unitData.sku_id
        }
      })

      if (Array.isArray(res) && res.length > 0) {
        this.form.prices = res
        this.dataCopy = _.cloneDeep(res)
      } else {
        this.form.prices = [{ currency: '', amount: '', flag: 0 }]
        this.dataCopy = []
      }
    },
    addPrice() {
      this.form.prices.push({ currency: '', amount: '', flag: 0 })
    },
    async submitPrices() {
      const valid = await this.$refs.form.validate().catch(() => false)

      if (!valid) {
        return
      }

      let allPrices = []
      let allIds = []
      let prices = this.form.prices.filter((i) => (!!i.currency && !!i.amount) || (!i.currency && !i.amount))
      prices = prices.filter((i) => !!i.currency && !!i.amount)
      prices.concat(this.dataCopy).forEach((i) => {
        if (!i.id) {
          allPrices.push(i)
        } else if (!allIds.includes(i.id)) {
          allPrices.push(i)
          allIds.push(i.id)
        }
      })
      let curr_ids = prices.map((i) => i.id)
      let pre_ids = this.dataCopy.map((i) => i.id)
      let msps = allPrices.map((price) => {
        price.amount = +price.amount
        if (!curr_ids.includes(price.id) && pre_ids.includes(price.id)) {
          price.flag = 1
        } else {
          price.flag = 0
        }
        return price
      })
      // if (!msps.length) {
      //   this.visible = false
      //   return
      // }
      let data = {
        activity_id: +this.$route.params.id,
        sku_id: this.unitData.sku_id,
        price: msps
      }
      let res = (
        await ajax.postBody(ADMIN_API.act.update_msp, {
          data
        })
      ).result

      if (res.alert_msg) {
        this.$message.warn(res.alert_msg)
      }

      this.alert_type = res?.alert_type
      if (this.alert_type === undefined) return
      if (this.alert_type > 0) {
        this.step = 1
        if (res.price_msg && res.price_msg.length > 0) {
          this.tableTotal = res.price_msg.length
          this.tableData = res.price_msg
        }
      } else {
        this.resetDialog()
        this.$message.success('Success')
      }
    },
    deletePrice(msp, index) {
      if (this.form.prices.length > 1) {
        this.form.prices.splice(index, 1)
      } else {
        this.form.prices = [{ currency: '', amount: '', flag: 0 }]
      }
    },
    resetDialog() {
      this.step = 0
      this.tableData = []
      this.form.prices = []
      this.$emit('update:visible', false)
      this.tableTotal = 0
    }
  }
}
</script>

<style lang="scss" scoped>
$standardPixel: 20px;

.msp-drawer-container {
  .amount-title {
    position: relative;
    top: 10px;
    margin-right: $standardPixel;
    font-weight: 600;
  }
  .amount-item {
    display: inline-flex;
    align-items: center;
    margin-bottom: $standardPixel;
    margin-right: 2 * $standardPixel;
    .ant-input {
      width: 216px;
    }
    .ant-select {
      width: 128px;
      margin: 0 $standardPixel;
    }
    .__del {
      display: inline-block;
    }
  }
  .body {
    padding-top: $standardPixel;
    border-top: 1px solid #f0f0f0;
  }
  .footer {
    position: absolute;
    padding: 10px 16px;
    right: 0;
    bottom: 0;
    width: 100%;

    border-top: 1px solid #e9e9e9;
    background: #fff;
    text-align: right;
    z-index: 3;
    .ant-btn {
      margin-left: 12px;
      &:first-child {
        margin-left: 0;
      }
    }
  }
}
</style>
