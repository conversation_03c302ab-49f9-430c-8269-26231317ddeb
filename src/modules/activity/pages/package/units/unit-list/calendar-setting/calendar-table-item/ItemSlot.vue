<template>
  <a-popover
    class="calendar-table-item-slot"
    destroyTooltipOnHide
    :overlay-class-name="overlayClassName"
    :visible="visible && !disabledAddNew"
    :placement="placement"
  >
    <template slot="content">
      <CalendarItemPopoverMerchant
        v-if="isMerchant"
        :initial-value="data"
        :unit-data="unitData"
        @close="visible = false"
      />
      <CalendarTableItemSlotPopover
        v-else
        :initial-value="data"
        :unit-data="unitData"
        :is-presale="isPresale"
        @close="visible = false"
      />
    </template>
    <CalendarTableItemSlotPanel
      class="calendar-table-item-slot-panel"
      ref="panel"
      :data="data"
      :more="more"
      :unit-data="unitData"
      :is-outdate="isOutdate"
      @click.native="handleOpen"
      v-tooltip="{
        visible: lockAddNewValue.status,
        content: lockAddNewValue.tips,
        placement: 'bottom',
        clearOtherTooltips: true
      }"
    />
  </a-popover>
</template>

<script>
import moment from 'moment'
import { mapState } from 'vuex'
import CalendarTableItemSlotPanel from './ItemSlotPanel.vue'
import CalendarTableItemSlotPopover from './ItemSlotPopover.vue'
import CalendarItemPopoverMerchant from '@activity/pages/package/units/new-components/calendar-item-popover-merchant/index.vue'

/**
 * 这里收到的数据结构：
 * is_empty: 是否是空的
 * quantity_can_edit: 库存是否可以编辑
 * price_can_edit: 价格是否可以编辑
 * timeslot_can_edit: 是否可以改timeslot和cutofftime（发布状态）
 * visible: C端用户是否可见
 * published: 是否发布
 * start_time: 开始时间
 * cut_off_time: 自动延续时间
 * inv_quantity: 库存
 * sales: 销售量
 * price: 价格相关
 */

export default {
  name: 'CalendarTableItemSlot',
  inject: ['getEditLockValueByPath', 'getPackageFresherLastDate'],
  components: {
    CalendarTableItemSlotPanel,
    CalendarTableItemSlotPopover,
    CalendarItemPopoverMerchant
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    more: {
      type: Boolean,
      default: false
    },
    isOutdate: {
      type: Boolean,
      default: false
    },
    isPresale: {
      type: Boolean,
      default: false
    },
    // 星期几
    weekNum: {
      type: [String, Number],
      default: 0
    },
    unitData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      visible: false
    }
  },
  computed: {
    ...mapState({
      clickTarget: (state) => state.clickTarget
    }),
    placement() {
      if ([0, 1, 2].includes(this.weekNum)) {
        return 'right'
      }

      return 'left'
    },
    disabledAddNew() {
      return !this.isOutdate && this?.data?.is_empty && this.lockAddNewValue.status
    },
    lockAddNewValue() {
      const { sku_id } = this.unitData
      const { start_time } = this.data || {}

      // presale 的时候如果晚于 last_date 则放开 add timeslot 限制
      if (this.isPresale && start_time) {
        const lastDate = this.getPackageFresherLastDate(sku_id)

        if (moment(start_time).isAfter(lastDate, 'day')) {
          return {
            status: false,
            tips: ''
          }
        }
      }

      return this.getEditLockValueByPath({
        sku_id: sku_id,
        path: 'inventory.add_timeslot'
      })
    },
    isMerchant() {
      return this.$root.isMerchant
    },
    overlayClassName() {
      return this.isMerchant ? 'calendar-table-item-slot-wrap-merchant' : 'calendar-table-item-slot-wrap'
    }
  },
  watch: {
    clickTarget(target) {
      // 弹窗打开的时候才检查，提高性
      if (target && this.visible) {
        const appDom = document.querySelector('#app')
        const panelDom = this.$el

        const isInApp = appDom && appDom.contains(target)
        const isInPanel = panelDom && panelDom.contains(target)

        if (isInApp && !isInPanel) {
          this.visible = false
        }
      }
    }
  },
  methods: {
    handleOpen() {
      const panel = this.$refs.panel
      if (panel && !panel.checkDisabled()) {
        this.visible = true
      }
    }
  }
}
</script>
<style lang="scss">
.calendar-table-item-slot-wrap {
  z-index: 9;
}

.calendar-table-item-slot-wrap-merchant .ant-popover-inner-content {
  padding: 0;
}
</style>
