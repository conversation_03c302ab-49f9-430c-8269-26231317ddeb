<template>
  <div
    class="calendar-table-slot-panel"
    :class="{
      'is-unpublished': !data || !data.published,
      'is-presale': data && isStatusPresale(data.publish_status),
      'is-outdate': isOutdate,
      'is-content-blank': isContentBlank,
      'is-blank': isBlank,
      'is-disabled': isDisabled,
      'is-red-border': isRedBorder
    }"
  >
    <div class="panel-title one-line">
      <svg-icon
        v-tooltip="{
          visible: data && data.visible_tips,
          placement: 'topLeft',
          content: data && data.visible_tips,
          theme: 'dark'
        }"
        class="panel-title-icon"
        :icon-name="data && data.visible ? 'eye' : 'eye-off'"
        style="font-size: 18px"
      />
      <span class="panel-title-text">
        {{ isTitleBlank ? 'null' : formatTimeslot(data) }}
      </span>
    </div>
    <div class="panel-content">
      <template v-for="color in realLegendColors">
        <div v-if="getColorShow(color)" v-show="color.checked" :key="color.label" class="panel-content-row">
          <template v-if="getCostInputSuffixData(data, color).visible">
            <a-tooltip placement="top" :title="getCostInputSuffixData(data, color).title">
              <span class="cost-input-suffix" :class="getCostInputSuffixData(data, color).className"></span>
            </a-tooltip>
          </template>
          <div v-if="!isDisabled" class="panel-content-icon">
            <svg-icon :icon-name="color.icon" />
          </div>
          <div
            class="panel-content-text-wrap"
            :class="{
              'is-fixed': judgeFixed(color.attr),
              'is-forbidden': judgeForbidden(color.attr)
            }"
          >
            <!--<svg-icon-->
            <!--v-if="judgeAttrBlank(color.attr)"-->
            <!--class="panel-content-edit"-->
            <!--:icon-name="isBlank || isOutdate ? 'edit-black' : 'edit'"-->
            <!--style="font-size: 16px;"-->
            <!--/>-->
            <OverflowTextTooltip
              class="panel-content-text one-line"
              style="line-height: 16px"
              :style="{ color: color.color }"
            >
              <i v-if="!isDisabled" style="color: rgba(0, 0, 0, 0.45); font-style: normal">
                {{ getCalendarContentPrefix(data, color) }}
              </i>
              <label v-html="getCalendarContentText(data, color)"></label>
            </OverflowTextTooltip>
          </div>
        </div>
      </template>
      <div v-if="!isMerchant" class="panel-content-row flex-end">
        <a-popover v-if="showSsp" placement="top">
          <template slot="content">
            <div class="popover-ssp">
              <SspBlockList :showBtnSsp="false" :sspList="data.price.special_selling_price" />
            </div>
          </template>
          <template slot="title">
            <span style="font-size: 14px">
              {{ $t('package_special_selling_price') }}
            </span>
          </template>
          <a-button type="link" size="small" class="panel-content-ssp" @click.stop="sendSspSpm">
            {{ $t('28682') }} >>
          </a-button>
        </a-popover>
        <a-button v-else type="link" size="small" class="panel-content-ssp is-blank" @click.stop="sendSspSpm">
          {{ $t('28682') }} >>
        </a-button>
      </div>
      <div v-if="showMarks" class="tag-box" style="margin-top: 8px">
        <Tag v-for="tag in data.marks" :key="tag.key" :tag="tag" :show-tag="tag.value" />
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { formatThousands } from '@activity/utils'
import { SUPPORT_CURRENCY_SYMBO } from '@activity/pages/package/package_const.js'
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'
import SspBlockList from '@activity/pages/components/SspBlockList.vue'
import maps from '@activity/utils/maps.js'
import Tag from '@activity/pages/components/guardGroup/tag.vue'
import { formatPriceByComma } from '@activity/pages/package/units/utils'
import { isStatusPresale } from '@activity/pages/package/units/bulkEdit/utils'

export default {
  name: 'CalendarTableItemSlotPanel',
  components: {
    SspBlockList,
    OverflowTextTooltip,
    Tag
  },
  inject: ['legendColors'],
  filters: {
    formatThousands
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    more: {
      type: Boolean,
      default: false
    },
    isOutdate: {
      type: Boolean,
      default: false
    },
    unitData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      EMPTY_PRICE_VALUE: '¥ -'
    }
  },
  computed: {
    ...mapState({
      isMerchant: (state) => state.isMerchant,
      isBdAudit: (state) => state.isBdAudit,
      canEdit: (state) => state.canEdit,
      auditCanEdit: (state) => state.auditCanEdit,
      skuModel: (state) => state.skuModel,
      inventoryCanEdit: (state) => state.inventoryCanEdit
    }),
    realLegendColors() {
      const { legendColors, isTour } = this
      if (isTour) {
        return legendColors.filter((item) => item.attr != 'retail_price')
      }
      return legendColors
    },
    showMarks() {
      return this.data?.marks?.length && !this.isDisabled
    },
    isTitleBlank() {
      return !this.data || !this.data.start_time
    },
    isContentBlank() {
      // 库存为 -1 表示未设置
      return !this.data || (!this.data.price && this.data.inv_quantity === -1)
    },
    isBlank() {
      return this.isTitleBlank && this.isContentBlank
    },
    isDisabled() {
      const { isOutdate, data, canEdit, auditCanEdit, inventoryCanEdit } = this
      return (!canEdit && !auditCanEdit && !inventoryCanEdit) || isOutdate || !data
    },
    isRedBorder() {
      const { isOutdate, isContentBlank, realLegendColors } = this

      // 过期的没有内容的不展示红框
      if (isOutdate || isContentBlank) {
        return false
      }

      const attrList = realLegendColors.map((item) => item.attr)
      return attrList.some((attr) => this.judgeAttrBlank(attr))
    },
    slicedData() {
      return this.more ? this.data.data.slice(0, 4) : this.data.data
    },
    showSsp() {
      return (
        this.data &&
        this.data.price &&
        this.data.price.special_selling_price &&
        this.data.price.special_selling_price.length > 0
      )
    },
    ...mapGetters(['packageProductType', 'isTour']),
    isComboSku() {
      return this.packageProductType === 1
    },
    isAdmin() {
      return this.$root.isAdmin
    }
  },
  methods: {
    isStatusPresale,
    sendSspSpm() {
      klook.bus.$emit('sendSspSpm')
    },
    getColorShow(color) {
      return !(this.isComboSku && color.attr === 'cost_price')
    },
    getCostInputSuffixData(data, color) {
      if (!data) return ''
      const { price } = data
      const currentStatus = _.find(maps.ticket_status_dot, { value: this.unitData.ticket_status })

      if (
        this.isMerchant &&
        color.attr === 'cost_price' &&
        this.getCalendarContentText(data, color) !== this.EMPTY_PRICE_VALUE &&
        price &&
        !isNaN(price.current_cost_price) &&
        price.current_cost_price !== -1 && // -1: 不存在草稿价格, -2: 不存在产线价格, 其他值则: 存在草稿和产线价格
        currentStatus
      ) {
        const { current_cost_price } = price
        const className = currentStatus.class + ' ticket-status-circle-suffix'

        return {
          visible: true,
          title: `${__('48089')}: ${
            current_cost_price === -2 ? '-' : formatPriceByComma(current_cost_price)
          }`,
          className
        }
      }

      return {
        visible: false
      }
    },
    judgeAttrBlank(attr) {
      const { inv_quantity, price } = this.data || {}
      if (['inventory'].includes(attr)) {
        return inv_quantity === -1
      }

      if (['cost_price', 'retail_price', 'selling_price'].includes(attr)) {
        return !price || _.isUndefined(price[attr])
      }
    },
    getCalendarContentPrefix(data, color) {
      if (!data) {
        return ''
      }

      const { sales, inv_quantity } = data
      const showPrefix = color.attr === 'inventory'
      if (showPrefix) {
        return this.isComboSku ? '' : `${formatThousands(sales || 0)} / `
      } else {
        // 当 inv_quantity 小于 0 的时候，展示如 blank content
        return this.isContentBlank || inv_quantity < 0 ? '¥ -' : ''
      }
    },
    getCalendarContentText(data, color) {
      if (color.attr === 'inventory') {
        return this.getInventoryText(data)
      }

      const inv_quantity = data?.inv_quantity
      if (inv_quantity < 0) {
        return ''
      }

      if (data && data.price) {
        const currencyTarget = SUPPORT_CURRENCY_SYMBO.find(
          (item) => item[0] === data.price[color.currencyAttr]
        )
        const currency = currencyTarget ? currencyTarget[1] : ''
        const priceNum = formatThousands(data.price[color.attr] || 0)
        return `<span style="color: rgba(0, 0, 0, 0.45);">${currency}</span> ${priceNum}`
      }

      return this.EMPTY_PRICE_VALUE
    },
    formatTimeslot(data) {
      const splitedTime = data.start_time.split(' ')
      return !splitedTime[1] || splitedTime[1] === '00:00:00'
        ? this.$t('package_timeslot_by_day')
        : splitedTime[1].substring(0, 5)
    },
    getInventoryText(data) {
      // 作为占位符
      if (!data) {
        return '0'
      }

      // 无限库存
      const { inv_model } = this.skuModel || {}
      if (inv_model === 2) {
        return this.$t('package_unlimited')
      }

      const { inv_quantity } = data

      return inv_quantity === 0
        ? this.$t('global_sold_out')
        : inv_quantity < 0
        ? ''
        : formatThousands(inv_quantity)
    },
    judgeFixed(attr) {
      const { price_model, inv_model } = this.unitData || {}

      if (['inventory'].includes(attr)) {
        return inv_model === 3
      }

      if (['cost_price', 'retail_price', 'selling_price'].includes(attr)) {
        return price_model === 0
      }
    },
    judgeForbidden(attr) {
      // 如果是子sku的时候，库存需要变成灰色
      const { share_rel_ship } = this.unitData
      return ['inventory'].includes(attr) && share_rel_ship === 1
    },
    checkDisabled() {
      return this.isDisabled
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../mixins.scss';

.calendar-table-slot-panel {
  width: 100%;
  padding: 8px;
  margin-top: 8px;
  position: relative;

  background-color: #fff;
  border-radius: 4px;
  cursor: pointer;

  @include ticket-status-suffix;

  p {
    margin: 0;
  }

  .one-line {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .panel-title {
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
    line-height: 20px;
    font-weight: 600;

    // 只展示一行
    overflow: hidden; //超出的文本隐藏
    text-overflow: ellipsis; //溢出用省略号显示
    white-space: nowrap; //溢出不换行

    &-text {
      color: #0091ff;
    }
  }

  .panel-content {
    &-row.flex-end {
      justify-content: flex-end;
    }

    &-left {
      width: 44%;
    }

    &-right {
      width: 55%;
    }

    &-block {
      min-height: 28px;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    &-row {
      position: relative;
      padding: 2px 0;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 12px;
    }

    &-title {
      font-size: 10px;
      line-height: 12px;
      color: rgba(0, 0, 0, 0.45);
    }

    &-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      margin-right: 8px;
    }

    &-text-wrap {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      /*width: calc(100% - 20px);*/
      width: 100%;

      &.is-fixed {
        background-color: rgba(255, 171, 0, 0.1);
      }
      &.is-forbidden {
        background-color: rgba(0, 0, 0, 0.09);
      }
    }

    &-text {
      font-size: 12px;
      line-height: 20px;
      color: #000;
      font-weight: 600;

      &.sold-out {
        color: #ff5630;
      }
    }

    &-ssp {
      font-size: 12px;
      line-height: 18px;
      padding: 0;

      &.is-blank {
        color: transparent;
        cursor: not-allowed;
        display: none;
        // pointer-events: none;
      }
    }
  }

  &.is-unpublished:not(.is-presale) {
    cursor: pointer;

    .panel-title,
    .panel-title-text {
      color: rgba(0, 0, 0, 0.25) !important;
    }
    .panel-title-text {
      text-decoration: line-through;
    }
  }

  // outdate的样式需要放在blank样式上面
  &.is-outdate {
    cursor: not-allowed;

    .panel-title,
    .panel-title-text,
    .panel-content-text {
      color: rgba(0, 0, 0, 0.25) !important;
    }
    .panel-title-icon {
      visibility: hidden;
    }
    .panel-title-text {
      text-decoration: inherit;
    }
    .panel-content-ssp {
      color: rgba(0, 0, 0, 0.25);
    }
    .panel-content-edit {
      opacity: 0.5;
    }
  }

  &.is-content-blank {
    // .panel-title-text {
    //   color: rgba(0, 0, 0, 0.25) !important;
    // }

    // .panel-title-icon {
    //   visibility: hidden;
    // }

    // .panel-title-text {
    //   text-decoration: inherit;
    // }

    .panel-content-block,
    .panel-content-title,
    .panel-content-text,
    .panel-content-row .ant-btn {
      color: transparent !important;
    }
    .panel-content-text-wrap.is-fixed {
      background-color: inherit;
    }
    .panel-content-text-wrap.is-forbidden {
      background-color: inherit;
    }
    .panel-content-edit {
      visibility: hidden;
    }
  }

  &.is-disabled {
    cursor: not-allowed;
  }

  &.is-blank {
    background-color: transparent !important;
    cursor: default;
    user-select: none;

    .panel-title,
    .panel-title-text {
      color: transparent !important;
    }

    .panel-title-icon {
      visibility: hidden;
    }
  }

  &.is-red-border {
    box-sizing: border-box;
    border: 1px solid #ff5630;
  }
}

.ant-popover-content {
  .popover-ssp {
    max-height: 300px;
    overflow-y: auto;

    p {
      margin: 0;
      font-size: 14px;
      line-height: 26px;
    }
  }
}
</style>
