<template>
  <div class="calendar-table-item-title">
    <CalendarTableItemTitlePanel v-if="data.isOutdate" :data="data" :date-range="dateRange" />
    <a-popover v-else :visible="visible">
      <template slot="content">
        <div class="calendar-table-item-title-popover" style="width: 250px;">
          <p style="font-size: 14px; font-weight: 600; color: #000;">
            {{ $t('27803') }}
          </p>
          <!-- <p
            style="font-size: 12px; font-weight: normal; color: rgba(0, 0, 0, 0.45);"
          >
            The Price & Inventory of need to be input for the selected date(s).
          </p> -->
          <div style="display: flex; justify-content: center;">
            <a-button :style="{ marginRight: '8px' }" size="small" @click="handleCancel">
              {{ $t('global_button_cancel') }}
            </a-button>
            <a-button type="primary" size="small" @click="handleConfirm">
              {{ $t('27804') }}
            </a-button>
          </div>
        </div>
      </template>
      <!-- 新手提示 -->
      <!-- <a-tooltip
        v-if="noviceTipsVisible"
        :visible="true"
        :key="1"
        placement="top"
      >
        <template slot="title">
          <span>{{ $t('27800') }}</span>
        </template>
        <CalendarTableItemTitlePanel
          class="calendar-table-item-title-panel"
          :data="data"
          :date-range="dateRange"
          @click.native="handleClick"
        />
      </a-tooltip> -->
      <!-- <template v-else> -->
      <!-- 正常hover提示 -->
      <a-tooltip v-show="!hideTooltip" :key="2" placement="top">
        <template slot="title">
          <span>{{ tooltipText }}</span>
        </template>
        <CalendarTableItemTitlePanel
          class="calendar-table-item-title-panel"
          :data="data"
          :date-range="dateRange"
          @click.native="handleClick"
        />
      </a-tooltip>

      <!-- 没有hover提示 -->
      <CalendarTableItemTitlePanel
        v-show="hideTooltip"
        class="calendar-table-item-title-panel"
        :data="data"
        :date-range="dateRange"
        @click.native="handleClick"
      />
      <!-- </template> -->
    </a-popover>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import CalendarTableItemTitlePanel from './ItemTitlePanel.vue'

export default {
  name: 'CalendarTableItemTitle',
  inject: ['calendar'],
  components: {
    CalendarTableItemTitlePanel
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    dateRange: {
      type: Array,
      required: true
    },
    unitData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      visible: false
      // noviceTipsVisible: false // 新手提示
    }
  },
  computed: {
    ...mapState({
      clickTarget: (state) => state.clickTarget
    }),
    tooltipText() {
      const { date } = this.data
      const [startDate, endDate] = this.dateRange
      const isDateTheSameOrAfterStartDate = startDate && moment(date).isSameOrAfter(startDate, 'day')
      return isDateTheSameOrAfterStartDate && !endDate ? this.$t('27802') : this.$t('27801')
    },
    hideTooltip() {
      if (this.$attrs.closeSetDateRange) {
        return true
      }

      // 当选了范围之后，隐藏tooltip
      const [startDate, endDate] = this.dateRange
      return startDate && endDate
    }
  },
  watch: {
    clickTarget(target) {
      // popover
      if (target && this.visible) {
        const appDom = document.querySelector('#app')
        const panelDom = this.$el && this.$el.querySelector('.calendar-table-item-title-panel')
        const popoverDom = document.querySelector('.calendar-table-item-title-popover')

        const isInApp = appDom && appDom.contains(target)
        const isInPanel = panelDom && panelDom.contains(target)
        const isInPopover = popoverDom && popoverDom.contains(target)

        if (isInApp && !isInPanel && !isInPopover) {
          this.visible = false
        }
      }

      // 关闭新手引导
      // this.noviceTipsVisible = false
    }
  },
  // mounted() {
  //   setTimeout(() => {
  //     const { step } = this.unitData
  //     const { isToday } = this.data
  //     if (isToday && (!step || !step.includes('sku_calendar'))) {
  //       this.noviceTipsVisible = true
  //     }
  //   }, 200)
  // },
  methods: {
    handleClick() {
      this.calendar.setDateRange(this.data.date)

      this.$nextTick(() => {
        const [startDate, endDate] = this.dateRange
        if (startDate && endDate) {
          this.visible = true
        }
      })
    },
    handleCancel() {
      this.visible = false
      this.calendar.clearDateRange()
    },
    handleConfirm() {
      this.GTMHookItem('Price&Inventory|CalendarDateRangeConfirm_Click')
      const [startDate, endDate] = this.dateRange
      this.handleCancel()
      this.calendar.openBulkModification(startDate, endDate)
    }
  }
}
</script>
