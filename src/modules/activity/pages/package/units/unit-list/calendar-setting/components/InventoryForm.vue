<template>
  <a-form-model class="inventory-form" ref="form" :model="form" :rules="rules">
    <a-form-model-item :label="$t('27783')" prop="inventory" ref="inventory">
      <a-tooltip placement="top" arrow-point-at-center>
        <template slot="title">
          {{ $t('29440') }}
        </template>
        <a-input-number
          v-model="form.inventory"
          style="width: 320px;"
          :max="MAX_INVENTORY"
          :disabled="(!canEdit && !inventoryCanEdit) || isSubSku"
          :min="0"
          @blur="handleRound2Integer('inventory')"
        />
      </a-tooltip>
    </a-form-model-item>
  </a-form-model>
</template>

<script>
import { mapState } from 'vuex'
import { MAX_INVENTORY } from '@activity/pages/package/package_const.js'

export default {
  name: 'InventoryForm',
  inject: ['unitBar', 'calendarSetting'],
  props: {
    unitData: {
      type: Object,
      required: true
    },
    calendarSettingData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      MAX_INVENTORY,
      isSubSku: false,
      form: {
        inventory: undefined
      },
      rules: {
        inventory: [
          {
            required: true,
            message: this.$t('global_please_input'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    ...mapState({
      canEdit: (state) => state.canEdit,
      inventoryCanEdit: (state) => state.inventoryCanEdit
    })
  },
  created() {
    // 子sku要默认填充主sku的库存
    const { unitData, calendarSettingData } = this
    const { global_inv_quantity } = calendarSettingData

    this.isSubSku = unitData.share_rel_ship === 1

    // 自动填充库存
    if (global_inv_quantity >= 0) {
      this.form.inventory = global_inv_quantity
    }
  },
  methods: {
    validateForm() {
      return this.$refs.form.validate().catch(() => false)
    },
    // initForm(calendarSettingData) {
    //   const { global_inv_quantity } = calendarSettingData
    //   this.form = {
    //     inventory: global_inv_quantity || undefined
    //   }
    // },
    getData() {
      return this.form
    },
    handleRound2Integer(field) {
      this.form[field] = parseInt(this.form[field] || 0)
      this.$refs.inventory && this.$refs.inventory.onFieldBlur()
    }
  }
}
</script>

<style lang="scss" scoped></style>
