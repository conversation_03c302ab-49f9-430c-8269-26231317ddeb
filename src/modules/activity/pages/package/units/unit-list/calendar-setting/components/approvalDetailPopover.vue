<template>
  <a-popover
    overlayClassName="dark-theme"
    v-bind="{
      trigger: 'hover',
      disabled: false,
      destroyTooltipOnHide: true,
      mouseEnterDelay: 0.3,
      ...$attrs
    }"
  >
    <div slot="content">
      <slot name="content">
        <div>{{ contentText }}</div>
        <div style="text-align: right;">
          <a-button type="link" @click="handleGoToApprovalDetail">{{ goToText }}</a-button>
        </div>
      </slot>
    </div>

    <slot name="default"></slot>
  </a-popover>
</template>

<script>
import { isMerchant } from '@/env'

export default {
  name: 'approvalDetailPopover',
  props: {
    ticketId: {
      type: [Number, String],
      required: true
    }
  },
  computed: {
    contentText() {
      if (isMerchant) {
        return this.$t('48077')
      }

      return this.$t('48152')
    },
    goToText() {
      if (isMerchant) {
        return this.$t('48079')
      }

      return this.$t('48153')
    }
  },
  methods: {
    handleGoToApprovalDetail() {
      const url = this.$router.resolve({
        name: 'ticketDetail',
        params: {
          ticket_id: this.ticketId
        }
      })

      window.open(url.href)
    }
  }
}
</script>

<style lang="scss">
.ant-popover.dark-theme {
  width: 258px;
  z-index: 1061;
  .ant-popover-arrow {
    background-color: rgb(38, 38, 38);
    border-color: rgb(38, 38, 38);
    bottom: 8.2px !important;
  }
  .ant-popover-inner-content {
    color: #ffffff;
    background-color: rgba(0, 0, 0, 0.85);
  }
}
</style>
