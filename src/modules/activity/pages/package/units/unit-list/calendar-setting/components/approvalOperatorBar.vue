<template>
  <div class="approval-operator-bar-container">
    <div class="__tips">
      <a-icon type="exclamation-circle" theme="filled" style="color: #fbad15;" />
      <slot name="tips">
        <span class="__content">{{ tips }}</span>
      </slot>
    </div>

    <slot name="actions">
      <div
        class="__actions"
        :class="{
          '--is-inline': actionsIsInline,
          '--is-block': !actionsIsInline
        }"
      >
        <!-- 待提交审核内容 / 拒绝工单 -->
        <template v-if="['ToBeSubmitted', 'Rejected'].includes(ticketStatus)">
          <a-button
            :data-spm-module="getSubmitCostSpm"
            data-spm-virtual-item="__virtual"
            :size="size"
            type="primary"
            @click="handleSubmitCost"
          >
            {{ submitCostText }}
          </a-button>
          <a-popconfirm
            :ok-text="$t('48085')"
            :cancel-text="$t('48086')"
            placement="topRight"
            @confirm="handleCleanDraft"
          >
            <div slot="title" style="max-width: 300px;">
              {{ $t('48084') }}
            </div>
            <a-button :data-spm-module="getCleanSpm" data-spm-virtual-item="__virtual" :size="size">
              {{ cleanDraftText }}
            </a-button>
          </a-popconfirm>
        </template>
        <!-- Pending -->
        <template v-if="['PendingApproval'].includes(ticketStatus)">
          <a-button
            :data-spm-module="getWithdrawSpm"
            data-spm-virtual-item="__virtual"
            :size="size"
            @click="handleWithdraw"
          >
            {{ $t('48078') }}
          </a-button>
        </template>
        <a-button
          :data-spm-module="getTicketDetailSpm"
          data-spm-virtual-item="__virtual"
          :size="size"
          @click="handleApprovalDetails"
        >
          {{ detailText }}
        </a-button>
      </div>
    </slot>

    <SubmitApproveDrawer v-bind="{ ...$attrs, ...submit }" :visible.sync="submit.visible" />
  </div>
</template>

<script>
import SubmitApproveDrawer from './submitApproveDrawer'
export default {
  name: 'ApprovalOperatorBar',
  components: {
    SubmitApproveDrawer
  },
  provide() {
    return {
      refreshAllLinkedData: this.refreshAllLinkedData
    }
  },
  inject: {
    calendarSetting: {
      default: undefined
    },
    bulkEditTableIndex: {
      default: undefined
    },
    refreshAutoExtend: {
      default: undefined
    }
  },
  props: {
    actionsIsInline: {
      type: Boolean,
      default: false
    },
    unitData: {
      type: Object,
      default: () => ({})
    },
    size: {
      type: String,
      default: 'small'
    }
  },
  data() {
    return {
      submit: {
        visible: false,
        sku_id: undefined
      }
    }
  },
  computed: {
    getSubmitCostSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `SubmitTicket?oid=${oid}&trg=manual`
    },
    getCleanSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `Clean?oid=${oid}&trg=manual`
    },
    getWithdrawSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `Withdraw?oid=${oid}&trg=manual`
    },
    getTicketDetailSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `TicketDetail?oid=${oid}&trg=manual`
    },
    submitCostText() {
      return this.ticketStatus === 'Rejected' ? this.$t('48081') : this.$t('48074')
    },
    cleanDraftText() {
      return this.ticketStatus === 'Rejected' ? this.$t('48082') : this.$t('48075')
    },
    detailText() {
      return {
        ToBeSubmitted: this.$t('48076'),
        PendingApproval: this.$t('48079'),
        Rejected: this.$t('48083')
      }[this.ticketStatus]
    },
    ticketStatus() {
      return _.get(this.unitData, 'ticket_status', 'ToBeSubmitted')
    },
    tips() {
      switch (this.ticketStatus) {
        case 'ToBeSubmitted':
          return this.$t('48073')
        case 'PendingApproval':
          return this.$t('48077')
        case 'Rejected':
          return this.$t('48080')
        default:
          return ''
      }
    }
  },
  methods: {
    refreshAllLinkedData() {
      this.bulkEditTableIndex?.refreshTableData?.()
      this.refreshAutoExtend?.refresh?.()
      klook.bus.$emit('refreshCalendarSetting')
      klook.bus.$emit('refreshUnitList', { clearActiveId: false })
    },
    handleSubmitCost() {
      this.submit.visible = true
      this.submit.sku_id = this.unitData.sku_id
    },
    async handleCleanDraft() {
      await ajax.postBody(ADMIN_API.act.update_ticket_status, {
        data: {
          sku_id: this.unitData.sku_id,
          note: '',
          action: 2
        }
      })

      this.refreshAllLinkedData()
    },
    handleApprovalDetails() {
      const { href } = this.$router.resolve({
        name: 'ticketDetail',
        params: {
          ticket_id: this.unitData.ticket_id
        }
      })

      this.refreshAllLinkedData()
      window.open(href, '_blank')
    },
    async handleWithdraw() {
      await ajax.postBody(ADMIN_API.act.update_ticket_status, {
        data: {
          sku_id: this.unitData.sku_id,
          note: '',
          action: 3
        }
      })
      this.refreshAllLinkedData()
    }
  }
}
</script>

<style lang="scss" scoped>
.approval-operator-bar-container {
  width: 100%;
  display: flex;
  align-items: center;

  padding: 12px 18px;
  background-color: #fffbe6;

  .__tips {
    display: inline-flex;
    align-items: center;
    flex: 1;

    margin-right: 18px;

    font-size: 14px;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.85);

    .__content {
      text-align: left;
      margin-left: 12px;
    }
  }
  .__actions {
    width: fit-content;
    align-items: center;
    text-align: center;

    &.--is-inline {
      ::v-deep .ant-btn {
        margin-right: 6px;
        &:last-child {
          margin-right: 0;
        }
      }
    }

    &.--is-block {
      display: inline-flex;
      flex-direction: column;
      ::v-deep .ant-btn {
        margin-bottom: 6px;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
