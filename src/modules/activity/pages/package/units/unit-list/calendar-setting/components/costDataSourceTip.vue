<template>
  <div class="cost-data-source-tip" v-if="showSwitch">
    <slot name="body">
      <label
        v-for="(item, index) of ticketStatusGuideList"
        style="margin-right: 12px; font-size: 12px;"
        :key="index"
      >
        <span class="ticket-status-circle" :class="item.class"></span>{{ item.label }}
      </label>
    </slot>
  </div>
</template>

<script>
import maps from '@activity/utils/maps.js'
import { mapState, mapGetters } from 'vuex'

export default {
  name: 'costDataSourceTip',
  computed: {
    ...mapState(['isMerchant']),
    ...mapGetters(['getCurrentPackageApproveStatus']),
    showSwitch() {
      return this.getCurrentPackageApproveStatus === 3 && this.isMerchant
    },
    ticketStatusGuideList() {
      return maps.approval_status.filter((item) => [0, 1].includes(item.value))
    }
  }
}
</script>

<style lang="scss">
@import '../mixins.scss';

.cost-data-source-tip {
  margin-top: 12px;

  @include ticket-status-circle;
}
</style>
