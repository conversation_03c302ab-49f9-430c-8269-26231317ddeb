<template>
  <div class="switch-cost-price-source" v-if="showSwitch">
    <a-radio-group v-model="value" style="display: flex;">
      <a-radio :value="0" style="margin-right: 12px;">
        <svg-icon icon-name="finance" class="legend-item-icon"></svg-icon> Draft cost price
        <QuestionIcon :message="$t('49365')" placement="top" />
      </a-radio>

      <slot name="body">
        <label
          v-for="(item, index) of ticketStatusGuideList"
          style="margin-right: 12px; font-size: 12px;"
          :key="index"
        >
          <span class="ticket-status-circle" :class="item.class"></span>{{ item.label }}
        </label>
      </slot>

      <a-radio :value="1" style="margin-left: 12px;">
        <svg-icon icon-name="finance" class="legend-item-icon"></svg-icon> Published cost price
        <QuestionIcon :message="$t('49365')" placement="top" />
      </a-radio>
    </a-radio-group>
  </div>
</template>

<script>
import QuestionIcon from '../../../components/QuestionIcon'
import maps from '@activity/utils/maps.js'
import { mapState, mapGetters } from 'vuex'

export default {
  name: 'SwitchCostDataSource',
  model: {
    prop: 'value',
    event: 'change'
  },
  components: {
    QuestionIcon
  },
  computed: {
    ...mapState(['isMerchant', 'currentSkuSourceType']),
    ...mapGetters(['getCurrentPackageApproveStatus']),
    showSwitch() {
      return this.getCurrentPackageApproveStatus === 3 && this.isMerchant
    },
    ticketStatusGuideList() {
      return maps.approval_status.filter((item) => [0, 1].includes(item.value))
    },
    value: {
      get() {
        return this.currentSkuSourceType
      },
      set(v) {
        this.$store.commit('setCurrentSkuSourceType', v)
      }
    }
  }
}
</script>

<style lang="scss">
@import '../mixins.scss';

.switch-cost-price-source {
  margin-top: 12px;

  @include ticket-status-circle;
}
</style>
