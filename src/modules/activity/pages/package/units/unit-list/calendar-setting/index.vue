<template>
  <div class="calendar-setting">
    <a-spin :spinning="loading">
      <div v-if="calendarSettingData" class="calendar-setting-wrap">
        <!-- 因Unit为新建，暂无可关联的PIC库存， schedule及库存将在PIC完成入库后自动生成，届时将邮件通知并提醒完成价格设置 -->
        <a-alert
          v-if="isPICSku && !showForm"
          type="warning"
          style="margin-bottom: 10px; font-size: 12px; display: inline-block"
          :message="$t('48291')"
        ></a-alert>
        <div v-if="showForm || noneCalendar" class="calendar-setting-form">
          <!-- 因Unit为新建，暂无可关联的PIC库存， 请先完成固定价格设置，schedule及库存将在PIC完成入库后自动生成 -->
          <a-alert
            v-if="isPICSku"
            type="warning"
            style="margin-bottom: 10px; font-size: 12px; display: inline-block"
            :message="$t('48290')"
          ></a-alert>
          <CalendarForm ref="form" :unit-data="data" :calendar-setting-data="calendarSettingData" />
          <div class="calendar-setting-form-operations">
            <a-button style="margin-right: 12px" @click="handleCancel">
              {{ $t('global_button_cancel') }}
            </a-button>
            <a-button type="primary" @click="handleSave">
              {{ $t('global_save') }}
            </a-button>
          </div>
        </div>
        <Calendar
          v-else
          ref="calendar"
          :unit-data="data"
          :calendar-date="calendarDate"
          :calendar-setting-data="calendarSettingData"
        />
      </div>
    </a-spin>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import CalendarForm from './CalendarForm.vue'
import Calendar from './Calendar.vue'
import { UNSAFE_PRICE_CODE, showUnsavePriceReasonModal } from '@activity/components/UnsavePriceReasonModal'

export default {
  name: 'CalendarSetting',
  inject: ['skuId', 'unitBar'],
  provide() {
    return {
      calendarSetting: this
    }
  },
  components: {
    CalendarForm,
    Calendar
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    calendarDate: {
      type: Date,
      default: new Date()
    }
  },
  data() {
    return {
      loading: true,
      calendarSettingData: null,
      unsavePriceReason: {
        unsafe_price_reason_code: undefined,
        unsafe_price_reason_note: undefined
      }
    }
  },
  computed: {
    ...mapState({
      skuModel: (state) => state.skuModel,
      isBdAudit: (state) => state.isBdAudit
    }),
    showForm() {
      const { is_completed } = this.calendarSettingData
      const { price_model, inv_model } = this.data

      // 价格模型为固定价格 或者 全局库存
      const needForm = price_model === 0 || inv_model === 3

      return !is_completed && needForm
    },
    // pic库存
    isPICSku() {
      return this.data.inv_model === 4
    },
    // open date 无日历展示条件
    noneCalendar() {
      return this.skuModel.is_open_date_without_calendar
    }
  },
  async mounted() {
    this.loading = true
    await this.getExchangeRate()
    await this.getCalendarSetting()
    klook.bus.$off('refreshCalendarSetting').$on('refreshCalendarSetting', this.refresh)
  },
  beforeDestroy() {
    klook.bus.$off('refreshCalendarSetting')
  },
  methods: {
    refresh() {
      this.getCalendarSetting()
      this.$refs?.calendar?.getCalendarSettingData()
    },
    async getExchangeRate() {
      const res = await ajax.get(ADMIN_API.act.get_exchange_rate, {
        params: {
          currency_from: this.skuModel.merchant_currency,
          currency_to_list: this.skuModel.selling_currency
        }
      })
      this.$store.commit('setExchangeRate', res[0].to_value)
    },
    getCalendarSetting: _.debounce(async function () {
      this.loading = true

      this.calendarSettingData =
        (await ajax.get(ADMIN_API.act.get_calendar_setting, {
          params: {
            sku_id: this.skuId
          }
        })) || {}
      this.loading = false
    }, 100),
    resetUnsavePriceReason() {
      this.unsavePriceReason = {
        unsafe_price_reason_code: undefined,
        unsafe_price_reason_note: undefined
      }
    },
    async handleSave() {
      const valid = await this.$refs.form.validateForm()

      if (!valid) {
        return
      }

      const params = {
        ...this.$refs.form?.getData(),
        sku_id: this.skuId
      }
      if (params?.fix_price) {
        // 将提交参数包含不合格理由
        const { unsafe_price_reason_note, unsafe_price_reason_code } = this.unsavePriceReason || {}
        if (unsafe_price_reason_note) {
          params.fix_price.unsafe_price_reason_note = unsafe_price_reason_note
          params.fix_price.unsafe_price_reason_code = unsafe_price_reason_code
        }
      }

      this.loading = true
      const apiPath = this.isBdAudit
        ? ADMIN_API.act.update_calendar_setting_audit
        : ADMIN_API.act.update_calendar_setting
      const res =
        (await ajax.postBody(
          apiPath,
          {
            data: {
              ...params
            },
            noDefaultResponseInterceptor: true
          },
          {
            msgOpt: {
              isErrMsg: false
            }
          }
        )) || {}
      this.loading = false

      if (res.error?.code === UNSAFE_PRICE_CODE) {
        // 如果价格校验不通过，唤起 UnSavePriceReasonModal
        const { confirm, cancel } = await showUnsavePriceReasonModal({
          warningMessage: res.error.message
        })
        if (!cancel) {
          const { reasonCode, reasonNote } = confirm
          this.unsavePriceReason = {
            // 清空之前的理由
            unsafe_price_reason_code: reasonCode,
            unsafe_price_reason_note: reasonNote
          }
          await this.handleSave()
          return
        }
      }

      if (res.success) {
        this.$message.success(this.$t('global_create_success'))
        await this.updateStep()
        this.unitBar.$emit('update')
        // await this.checkCalendarSettingStep()
        await this.getCalendarSetting()
      } else {
        this.$message.error(res?.error?.message || this.$t('global_create_error'))
      }
      this.resetUnsavePriceReason()
    },
    async updateStep() {
      // 如果是pic库存或者open date无日历直接更新步骤状态
      if (this.isPICSku || this.noneCalendar) {
        await Promise.all([
          this.unitBar.saveSkuStep('sku_calendar', true),
          this.unitBar.savePkgStep('price_inventory', true)
        ])
        return
      }
      let res = await ajax.get({
        url: ADMIN_API.act.get_has_completed_schedule,
        params: {
          sku_id: this.skuId
        }
      })

      if (_.get(res, 'has_completed_schedule', false)) {
        await Promise.all([
          this.unitBar.saveSkuStep('sku_calendar', true),
          this.unitBar.savePkgStep('price_inventory', true)
        ])
      }
    },
    // async checkCalendarSettingStep() {
    //   // 固定价格模型，子SKU的场景下，填完固定价格后 因为会自动填充到所有共享的日期里，calendar setting这个步骤应该算是完成的
    //   const { price_model, share_rel_ship } = this.data
    //   if (price_model === 0 && share_rel_ship === 1) {
    //     await this.unitBar.saveSkuStep('sku_calendar')
    //   }
    // },
    handleCancel() {
      this.unitBar.$emit('toggleActiveId', null)
    },
    getCalendarSettingData() {
      return this.calendarSettingData
    }
  }
}
</script>

<style lang="scss" scoped>
.calendar-setting {
  padding: 20px;
  width: 100%;

  &-wrap {
    width: 100%;
  }

  &-form-operations {
    margin-top: 16px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
</style>
