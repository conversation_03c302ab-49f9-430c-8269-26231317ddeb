$approved: #36b37e;

@mixin mixin-status-text($padding: 4px, $isTransparentBackground: false) {
  height: fit-content;
  padding: $padding;

  @if $isTransparentBackground {
    background-color: transparent !important;
  }

  &.to_be_submitted {
    background-color: rgba(0, 184, 217, 0.05);
    color: #00b8d9;
  }

  &.pending_approval {
    background-color: rgba(255, 171, 0, 0.05);
    color: #ffab00;
  }

  &.rejected {
    background-color: rgba(255, 86, 48, 0.05);
    color: #ff5630;
  }

  &.approved {
    background-color: rgba(54, 179, 126, 0.05);
    color: $approved;
  }

  &.closed {
    background-color: rgba(0, 0, 0, 0.04);
    color: #000;
  }

  &.unpublished {
    background-color: rgba(0, 0, 0, 0.04);
    color: rgba(0, 0, 0, 0.85);
  }

  &.published {
    background-color: rgba(54, 179, 126, 0.05);
    color: $approved;
  }

  &.creating {
    background-color: rgba(255, 158, 0, 0.05);
    color: #b37736;
  }
}

@mixin mixin-status-text-circle() {
  &.to_be_submitted {
    background-color: #e6fffb;
    color: #08979c;
    border: 1px solid #87e8de;
  }

  &.pending_approval {
    background-color: #fff7e6;
    color: #d46b08;
    border: 1px solid #ffd591;
  }

  &.rejected {
    background-color: #fff1f0;
    color: #cf1322;
    border: 1px solid #ffa39e;
  }

  &.approved {
    background-color: #f6ffed;
    color: #389e0d;
    border: 1px solid #b7eb8f;
  }

  &.closed {
    background-color: #fafafa;
    color: #000000d9;
    border: 1px solid #d9d9d9;
  }
}

@mixin mixin-status-icon-color() {
  &.to_be_submitted {
    color: #08979c;
  }

  &.pending_approval {
    color: #faad14;
  }

  &.rejected {
    color: #ff4d4f;
  }

  &.approved {
    color: #52c41a;
  }

  &.closed {
    color: #00000073;
  }
}

@mixin mixin-status-circle {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #fafafa;
}

@mixin to_be_submitted {
  background-color: #36cfc9;
}

@mixin pending_approval {
  background-color: #faad14;
}

// 作为圆点出现
@mixin ticket-status-circle {
  .ticket-status-circle {
    @include mixin-status-circle;
    margin-right: 4px;
    margin-bottom: 1px;

    &.to_be_submitted {
      @include to_be_submitted;
    }

    &.pending_approval {
      @include pending_approval;
    }
  }
}

// 圆后缀，用于 tooltip
// 使用的元素需要加上 className ticket-status-circle-suffix 才生效
@mixin ticket-status-suffix {
  .ticket-status-circle-suffix {
    width: 16px;
    height: 16px;

    display: flex;
    align-items: center;
    justify-content: center;

    position: absolute;
    top: 50%;
    right: 6px;
    transform: translateY(-50%);

    &:before {
      content: "";
      @include mixin-status-circle;
    }

    &.to_be_submitted:before {
      @include to_be_submitted;
    }

    &.pending_approval:before {
      @include pending_approval;
    }

    &.--left {
      left: 20px;
      right: 0;
    }
  }
}
