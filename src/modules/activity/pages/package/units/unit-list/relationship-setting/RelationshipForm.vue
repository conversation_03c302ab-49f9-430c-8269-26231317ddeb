<template>
  <div class="relationship-form">
    <a-spin :spinning="loading">
      <a-form-model class="relationship-form" ref="form" layout="vertical" :model="form" :rules="rules">
        <GeneralAlert v-if="isSupplyApi" :show-icon="false" banner style="margin-bottom: 10px">
          <div v-html="$t('28084')" />
        </GeneralAlert>
        <a-form-model-item :label="$t('27766')" prop="shareStatus">
          <BasicFormPopover
            :message="$t('27978')"
            :auto-adjust-overflow="false"
            :show-popover="unitData.published"
            trigger="hover"
            placement="right"
          >
            <a-radio-group
              v-model="form.shareStatus"
              :disabled="!canEdit || disabled || isTripMapped"
              :title="isTripMapped ? $t('28910') : ''"
              @change="handleRelationStatusChange"
            >
              <a-radio :value="true" :disabled="isMainSku">
                {{ $t('27804') }}
                <QuestionIcon v-if="isMainSku && !form.shareStatus" :message="$t('27841')" />
              </a-radio>
              <a-radio :value="false">
                {{ $t('taxonomy_filter_city_no') }}
              </a-radio>
            </a-radio-group>
          </BasicFormPopover>
        </a-form-model-item>
        <template v-if="form.shareStatus">
          <a-spin :spinning="pkgSkuOptionsLoading">
            <a-form-model-item :label="$t('27768')" prop="sharedSku">
              <BasicFormPopover
                :message="$t('27978')"
                :auto-adjust-overflow="false"
                :show-popover="unitData.published"
                trigger="hover"
                placement="right"
              >
                <a-cascader
                  v-model="form.sharedSku"
                  :options="pkgSkuOptions || []"
                  :show-search="{ filter: handleFilter }"
                  :placeholder="$t('global_please_select')"
                  :disabled="!canEdit || disabled || isTripMapped"
                  :title="isTripMapped ? $t('28910') : ''"
                  style="width: 400px"
                />
              </BasicFormPopover>
            </a-form-model-item>
          </a-spin>
        </template>
        <RelationshipTips />
        <div style="display: flex; justify-content: flex-end; margin-top: 16px">
          <a-button v-if="isSaved" style="margin-right: 12px" @click="handleCancel">
            {{ $t('global_button_cancel') }}
          </a-button>
          <a-popconfirm
            v-if="isSaved && !disabled && canEdit && !isTripMapped"
            :title="$t('27774')"
            :ok-text="$t('global_button_ok')"
            :cancel-text="$t('global_button_cancel')"
            placement="topRight"
            @confirm="handleSave"
          >
            <a-button type="primary">
              {{ $t('global_save') }}
            </a-button>
          </a-popconfirm>
          <a-button
            v-else
            type="primary"
            :disabled="!canEdit || disabled || isTripMapped"
            :title="isTripMapped ? $t('28910') : ''"
            @click="handleSave"
          >
            {{ $t('global_save') }}
          </a-button>
        </div>
      </a-form-model>
    </a-spin>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { getEditLang } from '@activity/utils'
import QuestionIcon from '@activity/pages/package/units/components/QuestionIcon.vue'
import BasicFormPopover from '@activity/pages/components/BasicFormPopover.vue'
import RelationshipTips from './RelationshipTips.vue'
import { messageConfirm } from '@activity/pages/package/units/utils/message.js'
import GeneralAlert from '@activity/pages/components/GeneralAlert.vue'
import { pmsConfirm } from '@activity/utils'
import { isAdmin } from '@/env'
import { ticket_type } from '@activity/pages/package/package_const'

export default {
  name: 'RelationshipForm',
  inject: ['skuId', 'unitBar', 'activityId', 'relationship', 'packageId'],
  components: {
    RelationshipTips,
    QuestionIcon,
    BasicFormPopover,
    GeneralAlert
  },
  props: {
    data: {
      type: Object,
      default: null
    },
    unitData: {
      type: Object,
      required: true
    }
  },
  data() {
    const validateSharedSku = (rule, value, callback) => {
      if (!value || value.length === 0) {
        callback(new Error(this.$t('global_please_select')))
      } else {
        callback()
      }
    }

    return {
      loading: false,
      form: {
        shareStatus: false,
        sharedSku: undefined
      },
      rules: {
        shareStatus: [
          {
            required: true,
            message: this.$t('global_please_input'),
            trigger: 'change'
          }
        ],
        sharedSku: [
          { validator: validateSharedSku, trigger: 'change' },
          {
            required: true,
            message: this.$t('global_please_input'),
            trigger: 'change'
          }
        ]
      },

      pkgSkuOptions: undefined,
      pkgSkuOptionsLoading: false
    }
  },
  computed: {
    ...mapState({
      canEdit: (state) => state.canEdit,
      skuModel: (state) => state.skuModel
    }),

    needUpdateStep() {
      return this.skuModel.inv_model === 4 && this.skuModel.price_model != 0
    },
    ...mapGetters(['isTripMapped']),
    isSaved() {
      return !!this.data
    },
    disabled() {
      return !!this.unitData.published
    },
    isMainSku() {
      return this.unitData.share_rel_ship === 2
    },
    isSupplyApi() {
      const { unitData, isTripMapped } = this
      return unitData.is_api_stock_out_type && !isTripMapped
    },
    canShareBetweenDiffTicketType() {
      return !!this.data?.can_share_between_diff_ticket_type
    }
  },
  watch: {
    data: {
      immediate: true,
      async handler() {
        if (this.data) {
          const { main_sku, share_rel_ship } = this.data
          this.form.shareStatus = share_rel_ship === 1

          if (this.form.shareStatus) {
            this.form.sharedSku = [main_sku.package_id, main_sku.sku_id]
            await this.getSkus()
          }

          this.cacheForm = _.cloneDeep(this.form)
        }
      }
    }
  },
  methods: {
    async getSkus() {
      this.pkgSkuOptionsLoading = true
      const res =
        (await ajax.get(ADMIN_API.act.get_sku_simple_by_activity_id, {
          params: {
            activity_id: Number(this.activityId),
            language: getEditLang()
          }
        })) || {}
      // const res = await new Promise((resolve) =>
      //   setTimeout(() => resolve(get_skus), 500)
      // )
      this.pkgSkuOptionsLoading = false

      let currentTicketType = ''
      let currentUnschedule = ''
      const list = res.list.reduce((accu, curr) => {
        const ticket_type = curr.package_ticket_type
        const unschedule = curr.unschedule
        // 过滤自己
        if (curr.sku_id === this.skuId) {
          currentTicketType = ticket_type
          currentUnschedule = unschedule
          return accu
        }

        const { package_id, sku_id, package_name, package_name_en, unit_name, unit_name_en } = curr
        const prefix = package_id === this.packageId ? `(${this.$t('29252')})` : ''
        const parentLabel = `${prefix}${package_id}-${package_name || package_name_en}`
        const childLabel = `${sku_id}-${unit_name || unit_name_en}`
        const target = accu.find((item) => item.value === package_id)

        if (target) {
          target.children.push({
            value: sku_id,
            label: childLabel
          })
        } else {
          accu.push({
            value: package_id,
            label: parentLabel,
            ticket_type: ticket_type,
            unschedule: unschedule,
            children: [
              {
                value: sku_id,
                label: childLabel
              }
            ]
          })
        }

        return accu
      }, [])

      // 白名单活动中，fixed_date可以与open_ticket共享库存
      const isFixedDateAndOpenDateCombo = (itemType, currentType) => {
        const isFixedAndOpen = (type1, type2) =>
          (type1 === ticket_type['fixed_date'] && type2 === ticket_type['open_ticket']) ||
          (type1 === ticket_type['open_ticket'] && type2 === ticket_type['fixed_date'])

        return this.canShareBetweenDiffTicketType && isFixedAndOpen(itemType, currentType)
      }

      this.pkgSkuOptions = list.filter((item) => {
        // 排期类型必须相同
        if (item.unschedule !== currentUnschedule) {
          return false
        }

        return (
          item.ticket_type === currentTicketType ||
          isFixedDateAndOpenDateCombo(item.ticket_type, currentTicketType)
        )
      })
    },
    async handleSave() {
      const valid = await this.$refs.form.validate().catch(() => false)

      if (!valid) {
        return
      }

      const currSharedSku = this?.form?.sharedSku
      const cacheSharedSku = this?.cacheForm?.sharedSku
      if (isAdmin && currSharedSku && cacheSharedSku && !_.isEqual(currSharedSku, cacheSharedSku)) {
        let temp = await pmsConfirm.call(this, {
          content: this.$t('49366')
        })

        if (!temp) {
          return
        }
      }

      // 从“No”改成“Yes”时，如果calendar有数据，则出二次确认弹窗
      const { share_rel_ship, step } = this.unitData
      if (share_rel_ship !== 1 && this.form.shareStatus && step && step.includes('sku_calendar')) {
        const result = await messageConfirm(this, this.$t('27775'))
        result && (await this.setRelationShip())
        return
      }

      await this.setRelationShip()
    },
    async setRelationShip() {
      this.loading = true
      const res =
        (await ajax.postBody(ADMIN_API.act.set_inv_share_rel_ship, {
          data: {
            sku_id: this.skuId,
            main_sku_id: this.form.shareStatus ? this.form.sharedSku[this.form.sharedSku.length - 1] : 0
          }
        })) || {}
      // const res = await new Promise((resolve) =>
      //   setTimeout(() => resolve({ success: true }), 500)
      // )
      this.loading = false

      if (res.success) {
        await this.unitBar.saveSkuStep('sku_share_setting')
        this.$message.success(this.$t('global_modify_success'))

        // 如果是pic库存直接更新步骤状态
        if (this.needUpdateStep) {
          await Promise.all([
            this.unitBar.saveSkuStep('sku_calendar', true),
            this.unitBar.savePkgStep('price_inventory', true)
          ])
        }
        this.$nextTick(() => {
          this.handleCancel()
          this.unitBar.$emit('update', `${this.skuId}+calendar`)
        })
      } else {
        this.$message.error(this.$t('global_modify_error'))
      }
    },
    async handleRelationStatusChange() {
      if (this.form.shareStatus && _.isUndefined(this.pkgSkuOptions)) {
        await this.getSkus()
      }
    },
    handleCancel() {
      this.unitBar.$emit('toggleActiveId', null)
    },
    handleFilter(inputValue, path) {
      return path.some((option) => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1)
    }
  }
}
</script>

<style lang="scss" scoped>
.relationship-form {
  padding: 10px;
  background-color: #fafafa;
}
</style>
