<template>
  <div class="relationship-table">
    <h3 class="relationship-table-title">
      {{ $t('27769') }}
    </h3>
    <div class="relationship-table-header">
      <h4 class="header-title">
        {{ $t('27754') }}
      </h4>
      <div class="header-wrap">
        <a-button type="link" style="margin-right: 10px" @click="handleOpenSku(data.main_sku)">
          {{ data.main_sku.sku_id }}-{{ data.main_sku.unit_name }}
        </a-button>
        <a-tag v-if="data.main_sku.is_current" color="rgba(0, 145, 255, 0.65)" style="margin-right: 10px">
          {{ $t('27770') }}
        </a-tag>
        <PublishText :published="data.main_sku.published" />
        <span style="margin-left: 50px">
          <span>{{ $t('global_package') }}:</span>
          <a-button type="link" style="padding: 0 4px" @click="handleOpenPkg(data.main_sku)">
            {{ data.main_sku.package_id }}-{{ data.main_sku.package_name }}
          </a-button>
        </span>
        <a-popconfirm
          v-if="data.main_sku.is_current"
          :title="$t('27773')"
          :ok-text="$t('global_button_ok')"
          :cancel-text="$t('global_button_cancel')"
          :disabled="!canEdit"
          placement="topRight"
          @confirm="handleRemoveAll"
        >
          <a-button
            type="danger"
            :ghost="canEdit"
            :loading="loading"
            :disabled="!canEdit || isTripMapped"
            :title="isTripMapped ? $t('28910') : ''"
            style="margin-left: 40px"
          >
            {{ $t('27772') }}
          </a-button>
        </a-popconfirm>
      </div>
    </div>
    <a-table
      class="relationship-table-content"
      :columns="columns"
      :data-source="tableData"
      :scroll="{ x: true }"
      bordered
      :pagination="showPagination ? pagination : false"
    >
      <template slot="skuName" slot-scope="text, record">
        <div style="display: flex; justify-content: space-between; align-items: center">
          <a-button type="link" @click="handleOpenSku(record)">
            {{ text }}
          </a-button>
          <a-tag v-if="record.is_current" color="rgba(0, 145, 255, 0.65)" style="margin-left: 10px">
            {{ $t('27770') }}
          </a-tag>
        </div>
      </template>
      <template slot="status" slot-scope="text, record">
        <PublishText :published="record.published" />
      </template>
      <template slot="blue-text" slot-scope="text, record">
        <a-button type="link" @click="handleOpenPkg(record)">
          {{ text }}
        </a-button>
      </template>
    </a-table>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import PublishText from '@activity/pages/package/units/components/PublishText'

export default {
  name: 'RelationshipTable',
  inject: ['skuId', 'unitBar', 'relationship'],
  components: {
    PublishText
  },
  props: {
    data: {
      type: Object,
      required: true
    }
  },
  data() {
    const columns = [
      {
        title: 'Sub SKU',
        dataIndex: 'skuName',
        scopedSlots: { customRender: 'skuName' }
      },
      {
        title: 'Status',
        dataIndex: 'published',
        scopedSlots: { customRender: 'status' }
      },
      {
        title: 'Package',
        dataIndex: 'packageName',
        scopedSlots: { customRender: 'blue-text' }
      }
    ]

    return {
      columns,
      loading: false,
      pagination: {
        current: 1,
        pageSize: 20,
        showSizeChanger: true,
        pageSizeOptions: ['20', '50', '100'],
        showSizeChange: this.handleSizeChange
      }
    }
  },
  computed: {
    ...mapState({
      canEdit: (state) => state.canEdit
    }),
    ...mapGetters(['isTripMapped']),
    spuIdList() {
      return this.$store.state.categoryInfo.spu_id_list || []
    },
    tableData() {
      return this.data.sub_sku.map((item) => ({
        key: item.sku_id,
        sku_id: item.sku_id,
        package_id: item.package_id,
        skuName: `${item.sku_id}-${item.unit_name}`,
        published: item.published,
        packageName: `${item.package_id}-${item.package_name}`,
        is_current: item.is_current
      }))
    },
    showPagination() {
      return this.tableData.length > 20
    }
  },
  methods: {
    handleSizeChange(current, pageSize) {
      this.pagination.pageSize = pageSize
    },
    async handleRemoveAll() {
      this.loading = true
      const res =
        (await ajax.postBody(ADMIN_API.act.remove_share_rel_ship_by_main_sku, {
          data: {
            main_sku_id: this.skuId
          }
        })) || {}
      // const res = await new Promise((resolve) =>
      //   setTimeout(() => resolve({ success: true }), 500)
      // )
      this.loading = false

      if (res.success) {
        this.unitBar.$emit('update')
        this.$message.success(this.$t('global_modify_success'))
        await this.relationship.getRelationshipSetting()
      } else {
        this.$message.error(this.$t('global_modify_error'))
      }
    },
    getOldAdminUrlByPackageId(packageId, path) {
      const actId = +this.$route.params.id
      const isLocalhost = window.location.host.includes('localhost')
      const search = new URLSearchParams(location.search)
      search.set('package_id', packageId)

      const baseURL = isLocalhost
        ? `${window.location.origin}${path}/${actId}`
        : `${window.location.origin}/mspa/experiencesadmincommon${path}/${actId}`

      return `${baseURL}?${search.toString()}`
    },
    handleOpenSku({ package_id, sku_id }) {
      if (this.spuIdList.includes(package_id)) {
        const url = this.getOldAdminUrlByPackageId(package_id, '/aid/tours/inventory-schedule')
        window.open(url, '_blank')
        return
      }

      const { name, query } = this.$route
      const routeData = this.$router.resolve({
        name,
        query: {
          ...query,
          package_id,
          sku_id,
          sku_step: 'sku_share_setting'
        }
      })
      window.open(routeData.href, '_blank')
    },
    handleOpenPkg({ package_id }) {
      if (this.spuIdList.includes(package_id)) {
        const url = this.getOldAdminUrlByPackageId(package_id, '/aid/tours/basic')
        window.open(url, '_blank')
        return
      }

      const { name, query } = this.$route
      const routeData = this.$router.resolve({
        name,
        query: {
          ...query,
          package_id
        }
      })
      window.open(routeData.href, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.relationship-table {
  margin-top: 32px;
  padding: 20px;
  border: 1px solid #e8e8e8;

  &-title {
    color: #000;
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
  }

  &-header {
    margin-top: 24px;

    .header-title {
      color: #000;
      font-weight: 600;
      font-size: 14px;
      line-height: 18px;
    }
    .header-wrap {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      flex-wrap: wrap;
    }
  }

  &-content {
    margin-top: 16px;
  }
}
</style>
