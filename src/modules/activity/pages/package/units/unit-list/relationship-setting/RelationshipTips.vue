<template>
  <div class="calendar-relationship-tips" v-html="tips" />
</template>

<script>
export default {
  name: 'RelationshipTips',
  computed: {
    tips() {
      return this.$t('27767')
        .split('<br/>')
        .map((msg) => `<p>${msg}</p>`)
        .join('')
    }
  }
}
</script>

<style lang="scss">
.calendar-relationship-tips {
  padding: 8px 0 8px 24px;
  background-color: rgba(255, 171, 0, 0.05);

  p {
    position: relative;
    margin-bottom: 0;
    font-weight: normal;
    font-size: 12px;
    line-height: 16px;
    color: #ffab00;

    &:before {
      position: absolute;
      content: '';
      width: 4px;
      height: 4px;
      left: -8px;
      top: 6px;
      border-radius: 50%;
      background-color: #ffab00;
    }
  }
}
</style>
