<template>
  <div class="relationship-setting">
    <a-spin :spinning="loading">
      <div>
        <RelationshipForm :data="relationshipData" :unit-data="unitData" />
        <RelationshipTable v-if="showTable" :data="relationshipData" />
      </div>
    </a-spin>
  </div>
</template>

<script>
import { getEditLang } from '../../../../../utils'
import RelationshipForm from './RelationshipForm.vue'
import RelationshipTable from './RelationshipTable.vue'
// import get_inv_share_rel_ship from '@activity/pages/package/units/mock/get_inv_share_rel_ship'

export default {
  name: 'RelationshipSetting',
  inject: ['skuId'],
  provide() {
    return {
      relationship: this
    }
  },
  components: {
    RelationshipForm,
    RelationshipTable
  },
  props: {
    isSaved: {
      type: Boolean,
      required: true
    },
    unitData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      relationshipData: null
    }
  },
  computed: {
    showTable() {
      const { main_sku, sub_sku } = this.relationshipData || {}
      return main_sku && sub_sku
    }
  },
  async mounted() {
    if (this.isSaved) {
      await this.getRelationshipSetting()
    }
  },
  methods: {
    async getRelationshipSetting() {
      this.loading = true
      this.relationshipData =
        (await ajax.get(ADMIN_API.act.get_inv_share_rel_ship, {
          params: {
            sku_id: this.skuId,
            language: getEditLang()
          }
        })) || {}
      // this.relationshipData = await new Promise((resolve) =>
      //   setTimeout(() => resolve(get_inv_share_rel_ship), 500)
      // )
      this.loading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.relationship-setting {
  padding: 20px;
}
</style>
