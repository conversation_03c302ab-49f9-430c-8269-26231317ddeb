<template>
  <div class="calendar-container">
    <div class="rate-plan-alert" v-if="ratePlanAlertMessage">
      <a-icon type="exclamation-circle" theme="filled" style="color: #FAAD14;" />
      <div v-html="ratePlanAlertMessage" class="alert-content"></div>
    </div>

    <!-- bd audit 隐藏 filter -->
    <CalendarHeader
      v-model="filterByEmpty"
      v-if="!$attrs.hideCalendarHeader"
      :unit-data="unitData"
      :date-range="dateRange"
      :calendar-date="calendarDate"
      :auto-extend-visible.sync="autoExtendVisible"
      :skuId="skuId"
      :date-list="dateList"
      v-bind="$attrs"
    />
    <a-spin :spinning="loading">
      <CalendarTable
        :data="data"
        :unit-data="unitData"
        :date-list="dateList"
        :date-range="dateRange"
        :filter-by-empty="filterByEmpty"
        :closeSetDateRange="true"
        :hideTitlePopover="true"
      />
    </a-spin>
    <SkuModelDrawer :visible.sync="skuModelVisible" />
    <MspDrawer v-if="mspVisible" :unit-data="unitData" :visible.sync="mspVisible" />
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { isMerchant } from '@/env'
import { range as rangeArr, getFirstDayOfMonth, getPrevMonthLastDays, getMonthDays } from './utils'
import CalendarHeader from './CalendarHeader.vue'
import CalendarTable from './CalendarTable.vue'
import SkuModelDrawer from '@activity/pages/package/units/components/SkuModelDrawer.vue'
import MspDrawer from './MSPDrawer'
import { action as pkgsAction } from '@activity/store/pkgs-cache-observable.js'

const colors = [
  {
    icon: 'inventory-outlined',
    label: __('81821'),
    color: '#696969',
    attr: 'inventory',
    currencyAttr: '',
    checked: true,
    message: __('49363')
  },
  {
    iconStyle: { fontSize: '16px' },
    icon: 'icon_shopping_price_xs',
    label: __('package_price_cost'),
    color: '#696969',
    attr: 'cost_price',
    currencyAttr: 'cost_currency',
    checked: true,
    message: __('49362')
  },
  {
    iconStyle: { fontSize: '16px' },
    icon: 'icon_shopping_retail_price_xs',
    label: __('package_price_retail'),
    color: '#696969',
    attr: 'retail_price',
    currencyAttr: 'selling_currency',
    checked: true,
    message: __('49364')
  }
]

const legendDefineMap = {
  deactivatedType: {
    iconType: 'a-icon',
    icon: 'stop',
    label: __('73231'),
    color: '#696969'
  }
}

const otherColors = [
  {
    icon: 'Icon_common_currency_yuan',
    label: __('package_price_selling'),
    color: '#000',
    attr: 'selling_price',
    currencyAttr: 'selling_currency',
    checked: true
  }
]

if (!isMerchant) {
  colors.push(...otherColors)
}

export default {
  name: 'Calendar',
  components: {
    MspDrawer,
    CalendarHeader,
    CalendarTable,
    SkuModelDrawer
  },
  inject: ['skuId', 'unitBar', 'packageId', 'calendarSetting'],
  provide() {
    return {
      calendar: this,
      legendColors: this.colors,
      legendDefineMap: this.legendDefineMap
    }
  },
  props: {
    unitData: {
      type: Object,
      required: true
    },
    calendarSettingData: {
      type: Object,
      required: true
    },
    calendarDate: {
      type: Date,
      default: new Date()
    }
  },
  data() {
    return {
      isMerchant,

      legendDefineMap,

      skuModelVisible: false,
      loading: false,
      data: [],

      filterByEmpty: ['empty', 'inempty'],
      dateRange: [null, null],
      firstDayOfWeek: 0, // 日历从周几开始

      lazyShowMsg: false,
      isPageHidden: false,

      autoExtendVisible: false,

      mspVisible: false
    }
  },
  computed: {
    ...mapState({
      windowMessage: (state) => state.windowMessage,
      destinationToday: (state) => state.destinationToday,
      isBdAudit: (state) => state.isBdAudit,
      clickTarget: (state) => state.clickTarget,
      packageSkuData: 'packageSkuData'
    }),
    ...mapGetters(['isTour']),
    colors() {
      const exclude = this.isTour ? ['retail_price'] : []
      return colors.filter((item) => !exclude.includes(item.attr))
    },
    ratePlanAlertMessage() {
      const { rate_plan_id } = this.packageSkuData
      const { rate_plan_id: sku_rate_plan_id } = this.unitData
      const urlHost = process.env.VUE_APP_ADMIN_ROOTPATH

      if (this.isMerchant && (rate_plan_id?.length || sku_rate_plan_id?.length)) {
        return __('76683')
      }

      if (rate_plan_id?.length) {
        return klook.parseStr1(__('76554'), {
          ID: `
            <span style="color: #1890ff; font-weight: 700;">
              <a target="_blank" href="${urlHost}/act/rate/plan/list?keyword=${
            this.packageId
          }&rate_plan_id=${rate_plan_id}">
                ${rate_plan_id.join()}
              </a>
            </span>
          `
        })
      }

      if (sku_rate_plan_id?.length) {
        return klook.parseStr1(__('76554'), {
          ID: `
            <span style="color: #1890ff; font-weight: 700;">
              <a target="_blank" href="${urlHost}/act/rate/plan/list?keyword=${
            this.skuId
          }&rate_plan_id=${sku_rate_plan_id}">
                ${sku_rate_plan_id.join()}
              </a>
            </span>
          `
        })
      }

      return ''
    },
    yearAndLastMonth() {
      return moment(this.calendarDate)
        .subtract(1, 'months')
        .format('YYYY-MM')
    },
    yearAndMonth() {
      return moment(this.calendarDate).format('YYYY-MM')
    },
    yearAndNextMonth() {
      return moment(this.calendarDate)
        .add(1, 'months')
        .format('YYYY-MM')
    },
    dateList() {
      let days = []
      let firstDay = getFirstDayOfMonth(this.calendarDate) // 本月第一天是周几

      const prevMonthDays = getPrevMonthLastDays(this.calendarDate, firstDay - this.firstDayOfWeek).map(
        (day) => ({
          date: `${this.yearAndLastMonth}-${this.formatDay(day)}`,
          text: this.formatDay(day),
          type: 'prev',
          originalData: [], // 备份数据，用来执行timslot筛选
          data: []
        })
      )
      const currentMonthDays = getMonthDays(this.calendarDate).map((day) => ({
        date: `${this.yearAndMonth}-${this.formatDay(day)}`,
        text: this.formatDay(day),
        type: 'current',
        originalData: [], // 备份数据，用来执行timslot筛选
        data: []
      }))
      days = [...prevMonthDays, ...currentMonthDays]

      const nextMonthDays = rangeArr((7 - (days.length % 7)) % 7).map((_, index) => ({
        date: `${this.yearAndNextMonth}-${this.formatDay(index + 1)}`,
        text: this.formatDay(index + 1),
        type: 'next',
        originalData: [], // 备份数据，用来执行timslot筛选
        data: []
      }))
      days = days.concat(nextMonthDays)

      return this.toNestedArr(days)
    }
  },
  watch: {
    dateList: {
      immediate: true,
      handler() {
        if (this.dateList.length > 0) {
          // 使用 debounce 防止频繁切换
          this.debounceGetCalendarSettingData()
        }
      }
    },
    clickTarget(target) {
      // 重置选中区域
      const [startDate, endDate] = this.dateRange
      if (startDate || endDate) {
        const appDom = document.querySelector('#app')
        const isInApp = appDom && appDom.contains(target)
        const isInCalendarItemTitle =
          this.isActiveCalendarTableItemTitle(target) ||
          this.isActiveCalendarTableItemTitle(target.parentNode)

        if (isInApp && !isInCalendarItemTitle && !target.classList.contains('date-text')) {
          this.clearDateRange()
        }
      }
    },
    async windowMessage(val) {
      const { action, type } = val || {}
      if (type === 'calendar' && action === 'update') {
        this.unitBar.$emit('update')
        await this.debounceGetCalendarSettingData()
        klook.bus.$emit('updatePkgInfos2bus')

        // 为了防止 lazyShowMsg 为 true 之前切回来，这里再发一次
        if (!this.isPageHidden) {
          this.$message.success(this.$t('29254'))
        } else {
          this.lazyShowMsg = true
        }
      }
    }
  },
  mounted() {
    document && document.addEventListener('visibilitychange', this.handleVisibilityChange)
  },
  beforeDestroy() {
    document && document.removeEventListener('visibilitychange', this.handleVisibilityChange)
  },
  methods: {
    handleVisibilityChange() {
      if (document) {
        this.isPageHidden = document.hidden
        if (!this.isPageHidden && this.lazyShowMsg) {
          this.$message.success(this.$t('29254'))
        }
      }
      this.lazyShowMsg = false
    },
    isActiveCalendarTableItemTitle(dom) {
      const { classList } = dom || {}
      let domClassList = classList || []
      return domClassList.contains('calendar-table-item-title-panel') && !domClassList.contains('is-outdate')
    },
    toNestedArr(days) {
      return rangeArr(days.length / 7).map((_, index) => {
        const start = index * 7
        return days.slice(start, start + 7).map((item, index) => ({
          ...item,
          weekNum: index
        }))
      })
    },
    formatDay(day) {
      return day > 9 ? String(day) : `0${day}`
    },
    async update() {
      await this.getCalendarSettingData()
    },
    async getCalendarSettingData() {
      this.loading = true

      const apiPath = this.isBdAudit
        ? ADMIN_API.act.get_calendar_by_sku_id_audit
        : ADMIN_API.act.get_calendar_by_sku_id

      let params = {
        sku_id: this.skuId,
        start_time: `${this.dateList[0][0].date} 00:00:00`,
        end_time: `${this.dateList[this.dateList.length - 1][6].date} 23:59:59`
      }

      const res = await ajax.get(apiPath, {
        params
      })
      this.loading = false

      this.data = res.calendar || []
      pkgsAction.setGlobalData(apiPath, res)
      this.$store.commit('setDestinationToday', res.destination_now)
    },
    debounceGetCalendarSettingData: _.debounce(function() {
      this.getCalendarSettingData()
    }, 500),
    setDateRange(date) {
      if (this.$attrs.closeSetDateRange) return true

      const [startDate, endDate] = this.dateRange
      if (startDate && !endDate) {
        const isPositiveSelection = moment(date).isSameOrAfter(startDate, 'day')
        this.dateRange = isPositiveSelection ? [startDate, date] : [date, startDate]
      } else {
        this.dateRange = [date, null]
      }
    },
    clearDateRange() {
      this.dateRange = []
    },
    openSkuModel() {
      this.skuModelVisible = true
    },
    openCalendarSetting() {
      const btns = this.$refs.btns
      btns && btns.openCalendarSetting()
    },
    getCurrentMonthRange() {
      // 逻辑为当前价格日历的那个月，如果有过期的日期则减掉
      let start = moment(this.calendarDate)
        .startOf('month')
        .format('YYYY-MM-DD')
      let end = moment(this.calendarDate)
        .endOf('month')
        .format('YYYY-MM-DD')

      if (moment(start).diff(this.destinationToday, 'days') < 0) {
        start = moment(this.destinationToday).format('YYYY-MM-DD')
      }

      if (moment(end).diff(this.destinationToday, 'days') < 0) {
        end = moment(this.destinationToday).format('YYYY-MM-DD')
      }

      return [start, end]
    },
    openBulkModification(startDate, endDate) {
      const { params, query } = this.$route
      // 默认打开当前月的
      const [start, end] = this.getCurrentMonthRange()
      const routeData = this.$router.resolve({
        name: 'packageBulkEdit',
        params,
        query: {
          ...query,
          package_id: this.packageId,
          sku_id: this.skuId,
          start_date: startDate || start,
          end_date: endDate || end
        }
      })
      window.open(routeData.href, '_blank')
    },
    showAutoExtend() {
      this.autoExtendVisible = true
    },
    showMSP() {
      this.mspVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.rate-plan-alert {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 28px;
  padding: 8px 16px;
  border: 1px solid #ffe58f;
  border-radius: 2px;
  color: rgba(0, 0, 0, 0.85);

  .alert-content {
    margin-left: 12px;
  }
}
</style>
