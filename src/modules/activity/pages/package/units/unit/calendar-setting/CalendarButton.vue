<template>
  <div v-if="!!buttonText" class="calendar-setting-button">
    <a-button
      :data-spm-module="getPriceSettingSpm"
      data-spm-virtual-item="__virtual"
      @click="handleOpenSetting"
    >
      <!--<a-icon type="edit" />-->
      {{ buttonText }}
    </a-button>

    <a-drawer
      :title="$t('27784')"
      :width="944"
      :visible="visible"
      :headerStyle="drawerHeaderStyle"
      :bodyStyle="drawerBodyStyle"
      placement="right"
      destroyOnClose
      @close="handleDrawerClose"
    >
      <ApprovalOperatorBar
        v-if="isMerchant && showApprovalOperatorBar"
        :actions-is-inline="true"
        :unitData="unitData"
        style="margin-bottom: 32px"
      />
      <!-- 提示说明和成本显示切换 -->
      <costDataSourceTip :unitData="unitData" style="margin-bottom: 12px" />

      <a-spin :spinning="loading">
        <CalendarForm ref="form" :unit-data="unitData" :calendar-setting-data="calendarSettingData" />
      </a-spin>
      <div class="calendar-setting-button-footer">
        <a-button :style="{ marginRight: '8px' }" @click="handleDrawerClose">
          {{ $t('global_button_cancel') }}
        </a-button>
        <a-button
          :disabled="!canEdit && !auditCanEdit && !inventoryCanEdit"
          type="primary"
          @click="handleSave"
        >
          {{ $t('global_save') }}
        </a-button>
      </div>
    </a-drawer>

    <PriceReasonModal :visible.sync="priceReasonVisible" @confirm="handlePriceReasonConfirm" />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import CalendarForm from './CalendarForm.vue'
import {
  isPriceDifferent,
  getCalendarButtonText
} from '@activity/pages/package/units/unit-list/calendar-setting/utils'
import { messageConfirm } from '@activity/pages/package/units/utils/message.js'
import PriceReasonModal from '@activity/pages/package/units/components/PriceReasonModal.vue'
import ApprovalOperatorBar from './components/approvalOperatorBar'
import { getApprovalOperatorBarFlag } from '../../utils/index'
import maps from '@activity/utils/maps.js'
import costDataSourceTip from './components/costDataSourceTip'
import { UNSAFE_PRICE_CODE, showUnsavePriceReasonModal } from '@activity/components/UnsavePriceReasonModal'

export default {
  name: 'CalendarButton',
  inject: ['skuId', 'calendar', 'calendarSetting', 'unitBar'],
  components: {
    costDataSourceTip,
    ApprovalOperatorBar,
    CalendarForm,
    PriceReasonModal
  },
  props: {
    unitData: {
      type: Object,
      required: true
    },
    calendarSettingData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      visible: false,
      priceReasonVisible: false,
      loading: false,

      drawerHeaderStyle: {
        borderBottom: 'none',
        paddingLeft: '16px',
        paddingRight: '16px'
      },
      drawerBodyStyle: {
        padding: '60px 16px'
      },

      unsavePriceReason: {
        unsafe_price_reason_code: undefined,
        unsafe_price_reason_note: undefined
      }
    }
  },
  watch: {
    unitData: {
      deep: true,
      handler() {
        this.loading = false
      }
    }
  },
  computed: {
    ...mapState({
      isMerchant: (state) => state.isMerchant,
      canEdit: (state) => state.canEdit,
      isBdAudit: (state) => state.isBdAudit,
      auditCanEdit: (state) => state.auditCanEdit,
      inventoryCanEdit: (state) => state.inventoryCanEdit
    }),
    buttonText() {
      return getCalendarButtonText(this.unitData)
    },
    showApprovalOperatorBar() {
      return getApprovalOperatorBarFlag(this.unitData.ticket_status)
    },
    ticketStatusGuideList() {
      return maps.sku_status.filter((item) => [0, 1].includes(item.value))
    },
    getPriceSettingSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `FixedPriceTotalInventorySetting?oid=${oid}&trg=manual`
    }
  },
  methods: {
    async handleSave() {
      const valid = await this.$refs.form.validateForm()

      if (!valid) {
        return
      }

      const params = this.$refs.form.getData()

      if (this.$root.isAdmin && isPriceDifferent(params.fix_price, this.calendarSettingData.fix_price)) {
        this.priceReasonVisible = true
      } else {
        await this.checkMainSkuAndSaveCalendarSetting()
      }
    },
    async handlePriceReasonConfirm(note) {
      await this.checkMainSkuAndSaveCalendarSetting(note)
    },
    async checkMainSkuAndSaveCalendarSetting(note) {
      // 如果是主sku，则要检测cut off time、库存、上下架状态、删除的变动，然后显示提示弹窗
      const { share_rel_ship } = this.unitData || {}
      if (share_rel_ship === 2) {
        // global_inv_quantity
        const { global_inv_quantity: inventory } = this.$refs.form.getData()
        const { global_inv_quantity } = this.calendarSettingData

        if (!_.isUndefined(inventory) && String(inventory) !== String(global_inv_quantity)) {
          const result = await messageConfirm(this, this.$t('27807'))
          result && (await this.saveCalendarSetting(note))
        } else {
          await this.saveCalendarSetting(note)
        }
      } else {
        await this.saveCalendarSetting(note)
      }
    },
    resetUnsavePriceReason() {
      this.unsavePriceReason = {
        unsafe_price_reason_code: undefined,
        unsafe_price_reason_note: undefined
      }
      this.unsavePriceReasonVisible = false
    },
    async saveCalendarSetting(note) {
      const params = {
        sku_id: this.skuId,
        ...this.$refs.form.getData()
      }

      if (note && params.fix_price) {
        params.fix_price.note = note
      }

      const { unsafe_price_reason_note, unsafe_price_reason_code } = this.unsavePriceReason || {}
      if (unsafe_price_reason_note && params.fix_price) {
        params.fix_price.unsafe_price_reason_note = unsafe_price_reason_note
        params.fix_price.unsafe_price_reason_code = unsafe_price_reason_code
      }

      this.loading = true
      const apiPath = this.isBdAudit
        ? ADMIN_API.act.update_calendar_setting_audit
        : ADMIN_API.act.update_calendar_setting
      const res =
        (await ajax.postBody(
          apiPath,
          { data: params, noDefaultResponseInterceptor: true },
          {
            msgOpt: {
              isErrMsg: false
            }
          }
        )) || {}
      this.loading = false

      if (res.error?.code === UNSAFE_PRICE_CODE) {
        // 如果价格校验不通过，唤起 UnSavePriceReasonModal
        const { confirm, cancel } = await showUnsavePriceReasonModal({
          warningMessage: res.error.message
        })
        if (!cancel) {
          const { reasonCode, reasonNote } = confirm
          this.unsavePriceReason = {
            // 清空之前的理由
            unsafe_price_reason_code: reasonCode,
            unsafe_price_reason_note: reasonNote
          }
          await this.saveCalendarSetting(note)
          return
        }
      }
      if (res.success) {
        this.$message.success(this.$t('global_modify_success'))
        await this.updateStep()
        this.$nextTick(() => {
          this.handleDrawerClose()
        })

        this.unitBar.$emit('update')
        await this.calendarSetting.getCalendarSetting()
        await this.calendar.getCalendarSettingData()
      } else {
        this.$message.error(res?.error?.message || this.$t('global_modify_error'))
      }
      this.resetUnsavePriceReason()
    },
    async updateStep() {
      let res = await ajax.get({
        url: ADMIN_API.act.get_has_completed_schedule,
        params: {
          sku_id: this.skuId
        }
      })

      if (_.get(res, 'has_completed_schedule', false)) {
        await Promise.all([
          this.unitBar.saveSkuStep('sku_calendar', true),
          this.unitBar.savePkgStep('price_inventory', true)
        ])
      }
    },
    handleOpenSetting() {
      this.visible = true
    },
    handleDrawerClose() {
      this.$nextTick(() => {
        this.visible = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import './mixins.scss';

.calendar-setting-button {
  &-footer {
    position: absolute;
    padding: 10px 16px;
    right: 0;
    bottom: 0;
    width: 100%;

    border-top: 1px solid #e9e9e9;
    background: #fff;
    text-align: right;
    z-index: 3;
  }
}
</style>
