<template>
  <div class="calendar-form">
    <FixedPriceForm
      v-if="showFixedPriceForm"
      ref="form"
      :unit-data="unitData"
      :calendar-setting-data="calendarSettingData"
      v-bind="$attrs"
    />
    <InventoryForm
      v-else-if="showInventoryForm"
      ref="form"
      :unit-data="unitData"
      :calendar-setting-data="calendarSettingData"
    />
  </div>
</template>

<script>
import FixedPriceForm from './components/FixedPriceForm.vue'
import InventoryForm from './components/InventoryForm.vue'

export default {
  name: 'CalendarForm',
  components: {
    FixedPriceForm,
    InventoryForm
  },
  props: {
    unitData: {
      type: Object,
      required: true
    },
    calendarSettingData: {
      type: Object,
      required: true
    }
  },
  computed: {
    showFixedPriceForm() {
      return this.unitData.price_model === 0
    },
    // inv_model: 1,// 1: 日历库存; 2: 无限库存; 3: 全局库存; 4: 时间段库存,
    // pricing_model: 1,// 套餐价格模型, -1: 未定义, 0: 固定价格, 1: 日历价格,
    showInventoryForm() {
      const { price_model, inv_model } = this.unitData
      return price_model === 1 && inv_model === 3
    }
  },
  methods: {
    // initForm(calendarSettingData) {
    //   this.$refs.form && this.$refs.form.initForm(calendarSettingData)
    // },
    validateForm() {
      return this.$refs.form.validateForm()
    },
    getData() {
      const formData = this.$refs.form.getData()
      const params = {}

      if (formData.inventory) {
        params.global_inv_quantity = formData.inventory
      }

      if (this.showFixedPriceForm) {
        params.fix_price = {
          cost_currency: formData.costCurrency,
          selling_currency: formData.sellingCurrency,
          cost_price: Number(formData.costPrice),
          retail_price: Number(formData.retailPrice),
          selling_price: Number(formData.sellingPrice),
          special_selling_price: formData.specialSellingPriceList
        }
      }

      return params
    }
  }
}
</script>

<style lang="scss" scoped></style>
