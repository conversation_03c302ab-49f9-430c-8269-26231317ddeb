<template>
  <a-affix v-if="showAffix" :offset-top="64" class="calendar-header">
    <div class="calendar-header-wrap">
      <div class="calendar-header-section space-between">
        <CalendarFilter
          v-if="!$attrs.hideCalendarFilter"
          v-bind="$attrs"
          v-on="$listeners"
          :unit-data="unitData"
          class="calendar-filter-wrap"
        />
        <!-- <div class="calendar-header-selected-date">
          <span v-if="!!selectedDateText">{{ $t('global.selected_date') }}</span>
          {{ selectedDateText }}
        </div> -->
        <div class="calendar-header-year">
          <a-icon type="double-left" class="mr-25" @click.stop="handleTimeChange('prev-month')" />
          <span
            class="calendar-header-year-text mr-25"
            :data-spm-module="getYearSpm"
            data-spm-virtual-item="__virtual"
          >
            <year-calendar-popover
              :skuId="skuId"
              :year="currentYear"
              :month="currentMonth"
              :dateText="dateText"
              @setTheYearMonth="setTheYearMonth"
            />
          </span>
          <a-icon type="double-right" @click.stop="handleTimeChange('next-month')" />
        </div>
      </div>
      <div class="calendar-header-week">
        <div v-for="(text, index) in weekName" :key="text" class="calendar-header-week-text">
          <a-checkbox
            v-if="existWeekdayList.includes(index)"
            style="margin-right: 4px;"
            v-bind="getWeekdayCheckedData(index)"
            @change="changeWeekdayChecked($event, index)"
          ></a-checkbox>
          {{ text }}
        </div>
      </div>

      <template>
        <a-button class="calendar-header-left-button" @click.stop="handleTimeChange('prev-month')">
          <a-icon type="left" />
        </a-button>
        <a-button class="calendar-header-right-button" @click.stop="handleTimeChange('next-month')">
          <a-icon type="right" />
        </a-button>
      </template>
    </div>

    <auto-extend
      v-if="currentAutoExtendVisible"
      v-model="currentAutoExtendVisible"
      :skuId="skuId"
      :unit-data="unitData"
    />
  </a-affix>
</template>

<script>
import 'moment/locale/zh-tw'
import 'moment/locale/zh-cn'
import 'moment/locale/ja'
import 'moment/locale/ko'
import 'moment/locale/th'
import { mapState, mapMutations } from 'vuex'
import { isAdmin } from '@/env'
import { weekAbbrList } from '@activity/pages/package/package_const.js'
import AutoExtend from '../../autoExtend/index.vue'
import YearCalendarPopover from './YearCalendarPopover'
import CalendarFilter from './CalendarFilter.vue'
import moment from 'moment'
import { getExistWeekdayListByRange } from '@activity/utils/index'

if (moment && moment.locale) {
  moment.locale('en')
}

export default {
  name: 'CalendarHeader',
  inject: ['legendColors', 'calendar', 'packageId', 'skuId', 'unitBar'],
  components: {
    YearCalendarPopover,
    AutoExtend,
    CalendarFilter
  },
  props: {
    dateList: {
      type: Array,
      required: true
    },
    unitData: {
      type: Object,
      required: true
    },
    dateRange: {
      type: Array,
      required: true
    },
    calendarDate: {
      type: Date,
      default: new Date()
    },
    autoExtendVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isAdmin,

      observer: null,
      showAffix: true
    }
  },
  computed: {
    getYearSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `MonthDropDownList?oid=${oid}&trg=manual`
    },
    ...mapState({
      scrollNum: (state) => state.scrollNum
    }),
    currentAutoExtendVisible: {
      get() {
        return this.autoExtendVisible
      },
      set(v) {
        this.$emit('update:autoExtendVisible', v)
      }
    },
    selectedDateText() {
      const [startDate, endDate] = this.dateRange
      const formatString = 'YYYY-MM-DD(ddd)'

      if (startDate && !endDate) {
        return `${this.getMomentLocale(startDate, formatString)}-`
      }

      if (startDate && endDate) {
        return `${this.getMomentLocale(startDate, formatString)}-${this.getMomentLocale(
          endDate,
          formatString
        )}`
      }

      return ''
    },
    dateText() {
      return this.getMomentLocale(this.calendarDate, 'MMMM YYYY')
    },
    currentMonth() {
      return moment(this.calendarDate).month()
    },
    currentYear() {
      return moment(this.calendarDate).year()
    },
    weekName() {
      return weekAbbrList
    },
    bulkDateRangeValues() {
      return this.$store.state.bulkManageSchedule.dateRangeValues
    },
    bulkDateRange() {
      return this.$store.state.bulkManageSchedule.dateRange
    },
    bulkExcludeDateList: {
      get() {
        return this.$store.state.bulkManageSchedule.excludeDateList
      },
      set(excludeDateList) {
        this.setBulkManageSchedule({ excludeDateList })
      }
    },
    existWeekdayList() {
      const length = this.bulkDateRange.length

      if (length) {
        const startDate = this.bulkDateRange[0]

        return getExistWeekdayListByRange(startDate, this.bulkDateRange.length)
      }

      return []
    }
  },
  watch: {
    scrollNum() {
      const elem = document.querySelector('.calendar-table')

      if (elem && elem.getBoundingClientRect) {
        const rect = elem.getBoundingClientRect()

        if (rect.bottom < 250) {
          this.showAffix = false
        } else {
          this.showAffix = true
        }
      }
    }
  },
  mounted() {
    klook.bus.$off('setTheYearMonth').$on('setTheYearMonth', this.setTheYearMonth)
    this.$once('hook:beforeDestroy', () => {
      klook.bus.$off('setTheYearMonth')
    })
  },
  methods: {
    ...mapMutations(['setBulkManageSchedule']),
    changeWeekdayChecked($event, index) {
      const { checked } = $event.target

      const weekdayColData = this.dateList.reduce((acc, curr) => [...acc, curr[index]], [])
      let excludeDateList = _.cloneDeep(this.bulkExcludeDateList)
      for (let item of weekdayColData) {
        if (this.bulkDateRangeValues.includes(item.date)) {
          if (checked) {
            excludeDateList = excludeDateList.filter((curr) => curr !== item.date)
          } else {
            excludeDateList.push(item.date)
          }
        }
      }

      this.setBulkManageSchedule({ excludeDateList: [...new Set(excludeDateList)] })
    },
    getWeekdayCheckedData(index) {
      const weekdayColData = this.dateList.reduce((acc, curr) => [...acc, curr[index]], [])
      let allDateCount = 0
      let excludeDateCount = 0

      for (let colItem of weekdayColData) {
        if (this.bulkDateRangeValues.includes(colItem.date)) {
          allDateCount += 1
          if (this.bulkExcludeDateList.includes(colItem.date)) {
            excludeDateCount += 1
          }
        }
      }

      if (excludeDateCount === 0) {
        return {
          checked: true,
          indeterminate: false
        }
      }

      if (allDateCount === excludeDateCount) {
        return {
          checked: false,
          indeterminate: false
        }
      }

      return {
        checked: false,
        indeterminate: true
      }
    },
    setTheYearMonth({ month, year }) {
      this.unitBar.setCalendarDate(moment(`${year}/${month}/01`).toDate())
    },
    getMomentLocale(date, formatString) {
      try {
        const lang = this.$i18n.locale.toLowerCase()
        return moment(date)
          .locale(lang)
          .format(formatString)
      } catch (error) {
        console.log('dateText error', error)
        return moment(date).format(formatString)
      }
    },
    handleTimeChange(type) {
      let date = this.calendarDate

      if (type === 'prev-year') {
        date = moment(this.calendarDate)
          .subtract(1, 'years')
          .toDate()
      } else if (type === 'prev-month') {
        date = moment(this.calendarDate)
          .subtract(1, 'months')
          .toDate()
      } else if (type === 'next-year') {
        date = moment(this.calendarDate)
          .add(1, 'years')
          .toDate()
      } else if (type === 'next-month') {
        date = moment(this.calendarDate)
          .add(1, 'months')
          .toDate()
      }

      this.unitBar.setCalendarDate(date)
    }
  }
}
</script>

<style lang="scss">
.bulk-modification-popover {
  .ant-popover-arrow {
    background-color: #fff4dc;
    border-color: #fff4dc !important;
  }
  .ant-popover-inner-content {
    background-color: #fff4dc;
  }
}
</style>

<style lang="scss" scoped>
.calendar-header {
  position: relative;
  background-color: #fafafa;

  &-wrap {
    padding: 0 16px 12px 16px;
    background-color: #fafafa;
    position: relative;

    .calendar-header-left-button {
      position: absolute;
      top: 420px;
      left: 0;
      padding: 1px 8px;
    }

    .calendar-header-right-button {
      position: absolute;
      top: 420px;
      right: 0;
      padding: 1px 8px;
    }
  }

  &-section {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    position: relative;

    &.space-between {
      justify-content: space-between;
    }

    &.flex-end {
      justify-content: flex-end;
    }
  }

  &-block {
    margin-right: 24px;
    display: flex;
    align-items: center;
    color: #000;
    font-size: 10px;
    line-height: 16px;

    .legend-hh-mm {
      margin-right: 4px;
      color: rgba(0, 0, 0, 0.45);
      text-decoration-line: line-through;
    }

    .legend-remaining {
      margin-right: 4px;
      color: rgba(0, 0, 0, 0.45);
    }
  }

  &-paint {
    margin-right: 4px;
    width: 32px;
    height: 16px;
    border-radius: 2px;

    &.yellow {
      background-color: rgba(255, 171, 0, 0.1);
    }

    &.grey {
      background-color: rgba(0, 0, 0, 0.09);
    }
  }

  &-legends {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  &-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .ant-btn i {
      pointer-events: auto;
    }
  }

  &-selected-date {
    width: 100%;
    height: 30px;

    text-align: center;
    font-size: 14px;
    line-height: 30px;
    font-weight: 600;
    color: #666;
  }

  &-year {
    display: flex;
    align-items: center;
    margin: 5px 16px;

    &-text {
      width: 220px;

      text-align: center;
      font-weight: 600;
      font-size: 24px;
      line-height: 30px;
      color: #000;
    }

    .mr-25 {
      margin-right: 25px;
    }
  }

  &-week {
    margin-top: 32px;
    padding: 0 38px;
    display: flex;
    justify-content: space-between;

    &-text {
      width: calc(100% / 7 - 8px);
      color: #000;

      font-weight: 600;
      font-size: 12px;
      line-height: 14px;
      text-align: right;
    }
  }
}
</style>
