<template>
  <section class="calendar-table">
    <div>
      <div v-for="(dateRow, index) in filteredDataList" :key="index" class="calendar-table-wrap">
        <div class="calendar-table-row">
          <!-- :key="item.id" -->
          <!-- :key="item.date" -->
          <!-- :key="`${item.date}-${item.data.length}`" -->
          <template v-for="item in dateRow">
            <CalendarTableItem
              :key="item.date"
              :data="item"
              :unit-data="unitData"
              :date-range="dateRange"
              :more="moreArr[index]"
              :more-num="moreNum"
              v-bind="$attrs"
            />
            <a-checkbox
              v-if="item.shouldOperateRowBulkDate"
              :key="item.date + index"
              class="row-bulk-date-manage-checkbox"
              v-bind="getRowDateCheckedData(dateRow)"
              @click="handleRowSpm"
              @change="changeRowDateChecked($event, dateRow)"
            />
          </template>
        </div>

        <!-- 更多 -->
        <div v-if="showMore(dateRow)" class="calendar-more" @click="handleMore(index)">
          <svg-icon
            icon-name="page-down-custom"
            class="calendar-more-icon"
            :class="{ rotate: !moreArr[index] }"
          />
        </div>
      </div>
    </div>

    <!-- 埋点 -->
    <div id="row_bulk_spm" :data-spm-module="getRowBulkSpm" data-spm-virtual-item="__virtual"></div>
    <div id="ssp_setting_spm" :data-spm-module="getSspSpm" data-spm-virtual-item="__virtual"></div>
    <div id="modify_inv_spm" :data-spm-module="getInvSpm" data-spm-virtual-item="__virtual"></div>
    <div id="modify_cost_spm" :data-spm-module="getCostSpm" data-spm-virtual-item="__virtual"></div>
    <div id="modify_rp_spm" :data-spm-module="getRpSpm" data-spm-virtual-item="__virtual"></div>
    <div id="modify_sp_spm" :data-spm-module="getSpSpm" data-spm-virtual-item="__virtual"></div>
    <div id="del_schedule_spm" :data-spm-module="getDelScheduleSpm" data-spm-virtual-item="__virtual"></div>
    <div id="act_schedule_spm" :data-spm-module="getActScheduleSpm" data-spm-virtual-item="__virtual"></div>
  </section>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import { getDateFromStartTime } from './utils'
import CalendarTableItem from './calendar-table-item/index.vue'

// 组件刷新用
let id = 0

export default {
  name: 'CalendarTable',
  inject: ['calendar'],
  components: {
    CalendarTableItem
  },
  props: {
    data: {
      type: Array,
      required: true
    },
    dateList: {
      type: Array,
      required: true
    },
    dateRange: {
      type: Array,
      required: true
    },
    filterByEmpty: {
      type: Array,
      required: true
    },
    unitData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      moreNum: 3, // 超过 N 个就显示 more
      moreArr: [undefined, undefined, undefined, undefined, undefined, undefined, undefined, undefined],
      isFolded: true, // 是否初始折叠
      maxLen: 1000 // data长度大于这个值就一定折叠
    }
  },
  computed: {
    getRowBulkSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `Select_Week?oid=${oid}&trg=manual`
    },
    getSspSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `SSPSetting?oid=${oid}&trg=manual`
    },
    getInvSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `ModifyInventory?oid=${oid}&trg=manual`
    },
    getCostSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `ModifyCost?oid=${oid}&trg=manual`
    },
    getRpSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `ModifyRetailPrice?oid=${oid}&trg=manual`
    },
    getSpSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `ModifySellingPrice?oid=${oid}&trg=manual`
    },
    getDelScheduleSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `DeleteSchedule?oid=${oid}&trg=manual`
    },
    getActScheduleSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `ActiveSchedule?oid=${oid}&trg=manual`
    },
    ...mapState({
      destinationToday: (state) => state.destinationToday
    }),
    ...mapGetters(['getBulkManageSchedule']),
    shouldOperateBulkDateManage() {
      return this.getBulkManageSchedule.type && this.getBulkManageSchedule.dateRange.length
    },
    bulkDateRangeValues() {
      return this.getBulkManageSchedule.dateRangeValues
    },
    bulkExcludeDateList: {
      get() {
        return this.getBulkManageSchedule.excludeDateList
      },
      set(excludeDateList) {
        this.setBulkManageSchedule({ excludeDateList })
      }
    },
    dataList() {
      if (!this.data) {
        return this.dateList
      }

      // 给 dateList 填入数据
      // 暂存 data 的 index，每次遍历都从上次的 index 开始
      // 这里已优化达到极致性能
      let dataIndex = 0
      const { data, dateList, destinationToday } = this
      const dataLen = data.length
      const list = _.cloneDeep(dateList)
      const destinationDate = moment(destinationToday)

      list.forEach((weekItem) => {
        weekItem.forEach((item, idx, arr) => {
          // 刷新用
          item.id = id
          id += 1

          // 加上标记
          item.isOutdate = destinationDate.isAfter(item.date, 'day')
          item.isToday = destinationDate.isSame(item.date, 'day')
          // bulk date manage
          item.displayDateMangeCheckbox =
            this.shouldOperateBulkDateManage && this.bulkDateRangeValues.includes(item.date)
          item.checked = !this.bulkExcludeDateList.includes(item.date)
          const isLastDay = arr.length - 1 === idx
          if (isLastDay && this.shouldOperateBulkDateManage) {
            item.shouldOperateRowBulkDate = arr.some((item) => item.displayDateMangeCheckbox)
          }

          // 填充数据
          while (dataIndex < dataLen) {
            const dataItem = data[dataIndex]
            const date = getDateFromStartTime(dataItem.start_time)

            if (date && date === item.date) {
              item.originalData.push(dataItem)
              item.data.push(dataItem)
              dataIndex += 1
            } else {
              break
            }
          }
        })
      })

      return list
    },
    filteredDataList() {
      // 筛选数据（originalData里面储存着全数据）
      // const list = [...this.dataList]
      const isEmpty = this.filterByEmpty.includes('empty')
      const isInEmpty = this.filterByEmpty.includes('inempty')

      return this.dataList.map((weekItem) =>
        weekItem.map((item) => {
          if (isEmpty && isInEmpty) {
            item.data = [...item.originalData]
          } else if (isEmpty) {
            item.data = item.originalData.filter((dataItem) => dataItem.is_empty)
          } else if (isInEmpty) {
            item.data = item.originalData.filter((dataItem) => !dataItem.is_empty)
          } else {
            item.data = []
          }
          return { ...item }
        })
      )

      // list.forEach((weekItem) => {
      //   weekItem.forEach((item) => {
      //     if (isEmpty && isInEmpty) {
      //       item.data = [...item.originalData]
      //     } else if (isEmpty) {
      //       item.data = item.originalData.filter(
      //         (dataItem) => dataItem.is_empty
      //       )
      //     } else if (isInEmpty) {
      //       item.data = item.originalData.filter(
      //         (dataItem) => !dataItem.is_empty
      //       )
      //     } else {
      //       item.data = []
      //     }
      //   })
      // })

      // return list
    }
  },
  watch: {
    filteredDataList() {
      // 是否自动折叠
      if (this.data && this.data.length > this.maxLen) {
        this.moreArr = this.moreArr.map(() => true)
      } else {
        this.moreArr = this.moreArr.map(() => this.isFolded)
      }
    }
  },
  created() {
    // 埋点
    klook.bus.$on('sendSspSpm', () => {
      this.$tracker.track('action', '#ssp_setting_spm')
    })
    klook.bus.$on('sendInvSpm', () => {
      this.$tracker.track('action', '#modify_inv_spm')
    })
    klook.bus.$on('sendCostSpm', () => {
      this.$tracker.track('action', '#modify_cost_spm')
    })
    klook.bus.$on('sendRpSpm', () => {
      this.$tracker.track('action', '#modify_rp_spm')
    })
    klook.bus.$on('sendSpSpm', () => {
      this.$tracker.track('action', '#modify_sp_spm')
    })
    klook.bus.$on('sendDelScheduleSpm', () => {
      this.$tracker.track('action', '#del_schedule_spm')
    })
    klook.bus.$on('sendActScheduleSpm', () => {
      this.$tracker.track('action', '#act_schedule_spm')
    })
  },
  beforeDestroy() {
    // 销毁埋点数据，避免多次订阅
    klook.bus.$off('sendSspSpm')
    klook.bus.$off('sendInvSpm')
    klook.bus.$off('sendCostSpm')
    klook.bus.$off('sendRpSpm')
    klook.bus.$off('sendSpSpm')
    klook.bus.$off('sendDelScheduleSpm')
    klook.bus.$off('sendActScheduleSpm')
  },
  methods: {
    ...mapMutations(['setBulkManageSchedule']),
    handleRowSpm() {
      this.$tracker.track('action', '#row_bulk_spm')
    },
    changeRowDateChecked($event, dateRow) {
      const { checked } = $event.target

      let excludeDateList = _.cloneDeep(this.bulkExcludeDateList)
      for (let item of dateRow) {
        if (this.bulkDateRangeValues.includes(item.date)) {
          if (checked) {
            excludeDateList = excludeDateList.filter((curr) => curr !== item.date)
          } else {
            excludeDateList.push(item.date)
          }
        }
      }

      this.setBulkManageSchedule({ excludeDateList: [...new Set(excludeDateList)] })
    },
    getRowDateCheckedData(dateRow) {
      let allDateCount = 0
      let excludeDateCount = 0

      dateRow.forEach((item) => {
        if (item.displayDateMangeCheckbox) {
          allDateCount += 1
          if (this.bulkExcludeDateList.includes(item.date)) {
            excludeDateCount += 1
          }
        }
      })

      if (excludeDateCount === 0) {
        return {
          checked: true,
          indeterminate: false
        }
      }

      if (allDateCount === excludeDateCount) {
        return {
          checked: false,
          indeterminate: false
        }
      }

      return {
        checked: false,
        indeterminate: true
      }
    },
    showMore(row) {
      // 这里不能根据 more 来判断显示
      return row.some((item) => item.data.length > this.moreNum)
    },
    handleMore(index) {
      this.$set(this.moreArr, index, !this.moreArr[index])
    }
  }
}
</script>

<style lang="scss" scoped>
.calendar-table {
  width: 100%;
  padding: 0 50px 0 50px;
  position: relative;
  background-color: #fafafa;

  &-wrap {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &-row {
    position: relative;
    display: flex;
    justify-content: space-between;

    .row-bulk-date-manage-checkbox {
      position: absolute;
      top: 3px;
      right: -20px;
    }
  }

  .calendar-more {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 8px;

    width: 100%;
    height: 20px;
    text-align: center;
    background-color: #fff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    color: #8c8c8c;
    &:hover {
      color: #437dff;
    }

    &-icon.rotate {
      transform: rotate(180deg);
    }
  }
}
</style>
