<template>
  <a-popover
    v-model="currentVisible"
    trigger="click"
    overlayClassName="year-calendar-popover-container"
    placement="bottom"
  >
    <header slot="title" class="header">
      <a-icon
        :class="{
          'btn-hidden': minimumYear === currentYear
        }"
        type="double-left"
        class="mr-25"
        @click.stop="handleTimeChange()"
      />
      <a-select v-model="currentYear" size="small">
        <a-select-option v-for="year in yearOptions" :value="year" :key="year">
          {{ year }}
        </a-select-option>
      </a-select>
      <a-icon
        :class="{
          'btn-hidden': maximumYear === currentYear
        }"
        type="double-right"
        @click.stop="handleTimeChange(true)"
      />
    </header>
    <div slot="content" class="content">
      <a-spin :spinning="spinning" size="small">
        <ul class="month-list">
          <li v-for="(monthItem, index) of monthOptions" :key="index" class="month-item">
            <p
              class="month-cell"
              :class="{
                'has-filled':
                  invInfoData.includes(`${currentYear}-${getMonthIndex(index)}`) && !isExpires(index),
                'current-month': index === month && year === currentYear,
                'is-expires': isExpires(index)
              }"
              @click="handleChoiceMonth(index + 1)"
            >
              {{ monthItem }}
            </p>
          </li>
        </ul>
      </a-spin>
      <footer class="footer"><span class="has-filled-dot"></span> {{ $t('30285') }}</footer>
    </div>
    <div class="date-text">
      {{ dateText }}
      <a-icon :type="currentVisible ? 'up' : 'down'" style="font-size: 13px;" class="date-icon-direction" />
    </div>
  </a-popover>
</template>

<script>
const admin_const = require('@/admin_const')

export default {
  model: {
    value: 'visible',
    event: 'change'
  },
  props: {
    skuId: {
      type: Number,
      required: true
    },
    year: {
      type: Number,
      required: true
    },
    month: {
      type: Number,
      required: true
    },
    dateText: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      YEAR_RANGE: 20,
      YEAR_PAST: 10,
      invInfoData: [],
      currentYear: null,
      currentVisible: false,
      spinning: false
    }
  },
  watch: {
    year: {
      immediate: true,
      handler() {
        this.currentYear = this.year
      }
    },
    currentYear: {
      immediate: true,
      handler(v) {
        if (v) {
          this.getInvInfoData()
        }
      }
    }
  },
  computed: {
    minimumYear() {
      return this.yearOptions[0]
    },
    maximumYear() {
      return this.yearOptions[this.yearOptions.length - 1]
    },
    yearOptions() {
      let year = this.year - this.YEAR_PAST
      return Array(this.YEAR_RANGE)
        .fill()
        .map((item, index) => year + index)
    },
    monthOptions() {
      let language = ''

      if (KLK_LANG === 'en') {
        language = 'default'
      } else {
        language = lang_conf.getLangObj('F_LANG', 'B_LANG')[KLK_LANG] || 'default'
      }

      return admin_const.MONTHS[language]
    }
  },
  methods: {
    handleTimeChange(isNext = false) {
      if (isNext) {
        this.currentYear += 1
      } else {
        this.currentYear -= 1
      }
    },
    isExpires(index) {
      let now = new Date()
      let year = now.getYear() + 1900
      let month = now.getMonth()

      return year > this.currentYear || (year === this.currentYear && index < month)
    },
    getMonthIndex(index) {
      let month = index + 1
      return month < 10 ? '0' + month : month
    },
    getInvInfoData: _.debounce(async function() {
      this.spinning = true
      const res = await ajax.get(ADMIN_API.act.get_calendar_over_view_by_month, {
        params: {
          sku_id: this.skuId,
          start_time: `${this.currentYear}-01-01 00:00:00`,
          end_time: `${this.currentYear}-12-31 23:59:59`
        }
      })

      this.invInfoData = res.has_inv_months || []
      this.spinning = false
    }, 60),
    handleChoiceMonth(v) {
      this.$emit('setTheYearMonth', {
        month: v,
        year: this.currentYear
      })
      this.currentVisible = false
    }
  }
}
</script>

<style lang="scss">
.year-calendar-popover-container .ant-popover-inner-content {
  padding: 0;
}
</style>

<style lang="scss" scoped>
%dot {
  width: 4px;
  height: 4px;
  display: inline-flex;
  border-radius: 50%;
  background-color: #36b37e;
}

.is-expires {
  color: rgba(0, 0, 0, 0.25);
}

.has-filled:after {
  @extend %dot;
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
}

.has-filled-dot {
  @extend %dot;
  margin-right: 6px;
}

.header {
  display: flex;
  margin: 8px 0 4px;
  align-items: center;
  justify-content: space-between;
  ::v-deep .ant-select-selection {
    border: none;
  }
}

.month-list {
  width: 300px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  padding: 8px;
  border-top: #f0f0f0 solid 1px;
  border-bottom: #f0f0f0 solid 1px;
  text-align: center;
  .month-item {
    display: inline-flex;
    width: 33%;
    height: 60px;
    align-items: center;
  }
  .month-cell {
    position: relative;
    width: 100%;
    height: 30px;
    line-height: 30px;
    margin-bottom: 0;
    cursor: pointer;
    &.current-month {
      color: #fff;
      background-color: #1890ff;
    }
  }
}

.btn-hidden {
  visibility: hidden;
}

.date-text {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  cursor: pointer;
  user-select: none;
}

.footer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px;
  text-align: center;
}
</style>
