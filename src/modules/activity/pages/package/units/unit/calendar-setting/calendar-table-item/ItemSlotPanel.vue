<template>
  <div
    class="calendar-table-slot-panel"
    :class="{
      'is-unpublished': !data || !data.published,
      'is-deactivated': calcIsDeactivated,
      'is-select': isSelect,
      'is-outdate': isOutdate,
      'is-content-blank': isContentBlank,
      'is-blank': isBlank,
      'is-can-edit': !isDisabled,
      'is-disabled': isDisabled,
      'is-red-border': isRedBorder
    }"
  >
    <div v-if="calcIsDeactivated" class="bgc-box">
      <a-icon type="stop" />
    </div>
    <div class="panel-title one-line">
      <!-- <svg-icon
        class="panel-title-icon"
        :icon-name="data && data.visible ? 'eye' : 'eye-off'"
        style="font-size: 18px;"
      /> -->
      <span class="panel-title-text">
        {{ isTitleBlank ? 'null' : formatTimeslot(data) }}
      </span>
    </div>
    <div class="panel-content">
      <template v-for="color in legendColors">
        <div v-if="getColorShow(color)" v-show="color.checked" :key="color.label" class="panel-content-row">
          <template v-if="getCostInputSuffixData(data, color).visible">
            <a-tooltip placement="top" :title="getCostInputSuffixData(data, color).title">
              <span class="cost-input-suffix" :class="getCostInputSuffixData(data, color).className"></span>
            </a-tooltip>
          </template>
          <div v-if="!isDisabled || (isDisabled && !isContentBlank)" class="panel-content-row__prefix-icon">
            <svg-icon :icon-name="color.icon" />
          </div>
          <div class="panel-content-text-wrap">
            <!--<svg-icon-->
            <!--v-if="judgeAttrBlank(color.attr)"-->
            <!--class="panel-content-edit"-->
            <!--:icon-name="isBlank || isOutdate ? 'edit-black' : 'edit'"-->
            <!--style="font-size: 16px;"-->
            <!--/>-->
            <OverflowTextTooltip
              class="panel-content-text one-line"
              style="line-height: 16px"
              :style="{ color: color.color }"
            >
              <i v-if="!isDisabled" style="color: rgba(0, 0, 0, 0.45); font-style: normal">
                {{ getCalendarContentPrefix(data, color) }}
              </i>
              <label v-html="getCalendarContentText(data, color)"></label>
            </OverflowTextTooltip>
          </div>
        </div>
      </template>
      <!-- <div v-if="!isMerchant" class="panel-content-row flex-end">
        <a-popover v-if="showSsp" placement="top">
          <template slot="content">
            <div class="popover-ssp">
              <SspBlockList :showBtnSsp="false" :sspList="data.price.special_selling_price" />
            </div>
          </template>
          <template slot="title">
            <span style="font-size: 14px;">
              {{ $t('package_special_selling_price') }}
            </span>
          </template>
          <a-button type="link" size="small" class="panel-content-ssp" @click.stop="sendSspSpm">
            {{ $t('28682') }} >>
          </a-button>
        </a-popover>
        <a-button v-else type="link" size="small" class="panel-content-ssp is-blank" @click.stop="sendSspSpm">
          {{ $t('28682') }} >>
        </a-button>
      </div> -->
      <div v-if="showMarks" class="tag-box" style="margin-top: 8px; text-align: left">
        <Tag v-for="tag in cloneData.marks" :key="tag.key" :tag="tag" :show-tag="tag.value" />
      </div>
    </div>
    <div v-if="!disabled" class="hover-box">
      <div class="hover-box__content">
        <a-icon type="edit" style="margin-right: 8px" />
        {{ $t(isContentBlank ? 'global_add' : '76522') }}
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { formatThousands } from '@activity/utils'
import { SUPPORT_CURRENCY_SYMBO } from '@activity/pages/package/package_const.js'
import OverflowTextTooltip from '@activity/components/OverflowTextTooltip.vue'
// import SspBlockList from '@activity/pages/components/SspBlockList.vue'
import maps from '@activity/utils/maps.js'
import { formatPriceByComma } from '@activity/pages/package/units/utils'
import Tag from '@activity/pages/components/guardGroup/tag.vue'

export default {
  name: 'CalendarTableItemSlotPanel',
  components: {
    // SspBlockList,
    OverflowTextTooltip,
    Tag
  },
  inject: ['legendColors'],
  filters: {
    formatThousands
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    more: {
      type: Boolean,
      default: false
    },
    isOutdate: {
      type: Boolean,
      default: false
    },
    isSelect: {
      type: Boolean,
      default: false
    },
    unitData: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      EMPTY_PRICE_VALUE: '¥ -',
      cloneData: {}
    }
  },
  computed: {
    ...mapState({
      isMerchant: (state) => state.isMerchant,
      isBdAudit: (state) => state.isBdAudit,
      canEdit: (state) => state.canEdit,
      auditCanEdit: (state) => state.auditCanEdit,
      skuModel: (state) => state.skuModel,
      inventoryCanEdit: (state) => state.inventoryCanEdit
    }),
    calcIsTimeslot() {
      return this.skuModel?.timeslot_model?.timeslot_type === 1
    },
    calcIsDeactivated() {
      const { isOutdate, data } = this
      return !isOutdate && data && !data.published
    },
    isTitleBlank() {
      return !this.data || !this.data.start_time
    },
    showMarks() {
      return this.data?.marks?.length && !this.isDisabled
    },
    isContentBlank() {
      // 库存为 -1 表示未设置
      return !this.data || (!this.data.price && this.data.inv_quantity === -1)
    },
    isBlank() {
      return this.isTitleBlank && this.isContentBlank
    },
    isDisabled() {
      const { isOutdate, data, canEdit, auditCanEdit, inventoryCanEdit } = this
      return (!canEdit && !auditCanEdit && !inventoryCanEdit) || isOutdate || !data
    },
    isRedBorder() {
      const { isOutdate, isContentBlank, legendColors } = this

      // 过期的没有内容的不展示红框
      if (isOutdate || isContentBlank) {
        return false
      }

      const attrList = legendColors.map((item) => item.attr)
      return attrList.some((attr) => this.judgeAttrBlank(attr))
    },
    slicedData() {
      return this.more ? this.data.data.slice(0, 4) : this.data.data
    },
    showSsp() {
      return (
        this.data &&
        this.data.price &&
        this.data.price.special_selling_price &&
        this.data.price.special_selling_price.length > 0
      )
    },
    ...mapGetters(['packageProductType']),
    isComboSku() {
      return this.packageProductType === 1
    }
  },
  watch: {
    data: {
      handler(val, oldval) {
        if (val !== oldval) {
          this.cloneData = _.cloneDeep(val) || {}
        }
      },
      deep: true
    }
  },
  mounted() {
    this.cloneData = _.cloneDeep(this.data) || {}
  },
  methods: {
    sendSspSpm() {
      klook.bus.$emit('sendSspSpm')
    },
    getColorShow(color) {
      return !(this.isComboSku && color.attr === 'cost_price')
    },
    getCostInputSuffixData(data, color) {
      if (!data) return ''
      const { price } = data
      const currentStatus = _.find(maps.ticket_status_dot, { value: this.unitData.ticket_status })

      if (
        this.isMerchant &&
        color.attr === 'cost_price' &&
        this.getCalendarContentText(data, color) !== this.EMPTY_PRICE_VALUE &&
        price &&
        !isNaN(price.current_cost_price) &&
        price.current_cost_price !== -1 && // -1: 不存在草稿价格, -2: 不存在产线价格, 其他值则: 存在草稿和产线价格
        currentStatus
      ) {
        const { current_cost_price } = price
        const className = currentStatus.class + ' ticket-status-circle-suffix'

        return {
          visible: true,
          title: `${__('48089')}: ${
            current_cost_price === -2 ? '-' : formatPriceByComma(current_cost_price)
          }`,
          className
        }
      }

      return {
        visible: false
      }
    },
    judgeAttrBlank(attr) {
      const { inv_quantity, price } = this.data || {}
      if (['inventory'].includes(attr)) {
        return inv_quantity === -1
      }

      if (['cost_price', 'retail_price', 'selling_price'].includes(attr)) {
        return !price || _.isUndefined(price[attr])
      }
    },
    getCalendarContentPrefix(data, color) {
      if (!data) {
        return ''
      }

      const { sales, inv_quantity } = data
      const showPrefix = color.attr === 'inventory'
      if (showPrefix) {
        return this.isComboSku ? '' : `${formatThousands(sales || 0)} / `
      } else {
        // 当 inv_quantity 小于 0 的时候，展示如 blank content
        return this.isContentBlank || inv_quantity < 0 ? '¥ -' : ''
      }
    },
    getCalendarContentText(data, color) {
      if (color.attr === 'inventory') {
        return this.getInventoryText(data)
      }

      const inv_quantity = data?.inv_quantity
      if (inv_quantity < 0) {
        return ''
      }

      if (data && data.price) {
        const currencyTarget = SUPPORT_CURRENCY_SYMBO.find(
          (item) => item[0] === data.price[color.currencyAttr]
        )
        const currency = currencyTarget ? currencyTarget[1] : ''
        const priceNum = formatThousands(data.price[color.attr] || 0)
        return `<span style="color: rgba(0, 0, 0, 0.45);">${currency}</span> ${priceNum}`
      }

      return this.EMPTY_PRICE_VALUE
    },
    formatTimeslot(data) {
      const splitedTime = data.start_time.split(' ')
      const str = splitedTime[1].substring(0, 5)
      return !splitedTime[1] || splitedTime[1] === '00:00:00' ? (this.calcIsTimeslot ? str : '') : str
    },
    getInventoryText(data) {
      // 作为占位符
      if (!data) {
        return '0'
      }

      // 无限库存
      const { inv_model } = this.skuModel || {}
      if (inv_model === 2) {
        return this.$t('package_unlimited')
      }

      const { inv_quantity } = data

      return inv_quantity === 0
        ? this.$t('global_sold_out')
        : inv_quantity < 0
        ? ''
        : formatThousands(inv_quantity)
    },
    judgeFixed(attr) {
      const { price_model, inv_model } = this.unitData || {}

      if (['inventory'].includes(attr)) {
        return inv_model === 3
      }

      if (['cost_price', 'retail_price', 'selling_price'].includes(attr)) {
        return price_model === 0
      }
    },
    judgeForbidden(attr) {
      // 如果是子sku的时候，库存需要变成灰色
      const { share_rel_ship } = this.unitData
      return ['inventory'].includes(attr) && share_rel_ship === 1
    },
    checkDisabled() {
      return this.isDisabled
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../mixins.scss';

.calendar-table-slot-panel {
  position: relative;
  width: 100%;
  padding: 8px;
  margin-top: 8px;
  position: relative;

  background-color: #fff;
  border-radius: 4px;
  cursor: pointer;

  @include ticket-status-suffix;
  .hover-box {
    box-sizing: border-box;
    display: none;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 3;
    width: 100%;
    height: 100%;
    color: #437dff;
    border-radius: 4px;
    background-color: #f0f7ff;
  }
  .hover-box__content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    border-radius: 4px;
    border: 1px solid #437dff;
  }
  p {
    margin: 0;
  }

  .one-line {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .panel-title {
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
    line-height: 20px;
    font-weight: 600;

    // 只展示一行
    overflow: hidden; //超出的文本隐藏
    text-overflow: ellipsis; //溢出用省略号显示
    white-space: nowrap; //溢出不换行

    &-text {
      color: rgba(0, 0, 0, 0.45);
    }
  }

  .panel-content {
    &-row.flex-end {
      justify-content: flex-end;
    }

    &-left {
      width: 44%;
    }

    &-right {
      width: 55%;
    }

    &-block {
      min-height: 28px;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    &-row {
      position: relative;
      padding: 2px 0;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 12px;
      &__prefix-icon {
        margin-right: 6px;
      }
    }

    &-title {
      font-size: 10px;
      line-height: 12px;
      color: rgba(0, 0, 0, 0.45);
    }

    &-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      margin-right: 8px;
      color: #8c8c8c;
    }

    &-text-wrap {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      /*width: calc(100% - 20px);*/
      width: 100%;

      &.is-fixed {
        background-color: rgba(255, 171, 0, 0.1);
      }
      &.is-forbidden {
        background-color: rgba(0, 0, 0, 0.09);
      }
    }

    &-text {
      font-size: 12px;
      line-height: 20px;
      color: #000;
      font-weight: 600;

      &.sold-out {
        color: #ff5630;
      }
    }

    &-ssp {
      font-size: 12px;
      line-height: 18px;
      padding: 0;

      &.is-blank {
        color: transparent;
        cursor: not-allowed;
        // pointer-events: none;
      }
    }
  }

  &.is-deactivated {
    cursor: pointer;
    &:hover {
      .hover-box {
        display: flex;
      }
    }
    .bgc-box {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      color: rgba(191, 191, 191, 0.25);
      i {
        box-sizing: border-box;
        display: block;
        width: 100%;
        height: 100%;
        padding: 6px 0;
        ::v-deep svg {
          display: block;
          width: 100%;
          height: 100%;
        }
      }
    }
    .panel-content-icon,
    .panel-content-text,
    .panel-content-text i,
    .panel-content-text label ::v-deep span,
    .panel-title,
    .panel-title-text {
      color: rgba(0, 0, 0, 0.25) !important;
    }
    // .panel-title-text {
    //   text-decoration: line-through;
    // }
  }

  &.is-select {
    cursor: pointer;
    .hover-box {
      display: flex;
      border: 2px solid rgba(67, 125, 255, 0.2);
    }
    .bgc-box {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      color: rgba(191, 191, 191, 0.25);
      i {
        box-sizing: border-box;
        display: block;
        width: 100%;
        height: 100%;
        padding: 6px 0;
        ::v-deep svg {
          display: block;
          width: 100%;
          height: 100%;
        }
      }
    }
    .panel-content-icon,
    .panel-content-text,
    .panel-content-text i,
    .panel-content-text label ::v-deep span,
    .panel-title,
    .panel-title-text {
      color: rgba(0, 0, 0, 0.25) !important;
    }
    // .panel-title-text {
    //   text-decoration: line-through;
    // }
  }

  &.is-unpublished {
    cursor: pointer;
    .panel-title,
    .panel-title-text {
      color: rgba(0, 0, 0, 0.25) !important;
    }
    // .panel-title-text {
    //   text-decoration: line-through;
    // }
  }

  // outdate的样式需要放在blank样式上面
  &.is-outdate {
    cursor: not-allowed;

    .panel-title,
    .panel-title-text,
    .panel-content-text {
      color: rgba(0, 0, 0, 0.25) !important;
    }
    .panel-title-icon {
      visibility: hidden;
    }
    .panel-title-text {
      text-decoration: inherit;
    }
    .panel-content-ssp {
      color: rgba(0, 0, 0, 0.25);
    }
    .panel-content-edit {
      opacity: 0.5;
    }
  }

  &.is-can-edit {
    &:hover {
      .hover-box {
        display: flex;
      }
    }
    .bgc-box {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      color: rgba(191, 191, 191, 0.25);
      i {
        box-sizing: border-box;
        display: block;
        width: 100%;
        height: 100%;
        padding: 6px 0;
        ::v-deep svg {
          display: block;
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  &.is-content-blank {
    // .panel-title-text {
    //   color: rgba(0, 0, 0, 0.25) !important;
    // }

    // .panel-title-icon {
    //   visibility: hidden;
    // }

    // .panel-title-text {
    //   text-decoration: inherit;
    // }

    .panel-content-block,
    .panel-content-title,
    .panel-content-text,
    .panel-content-row .ant-btn {
      color: transparent !important;
    }
    .panel-content-text-wrap.is-fixed {
      background-color: inherit;
    }
    .panel-content-text-wrap.is-forbidden {
      background-color: inherit;
    }
    .panel-content-edit {
      visibility: hidden;
    }
  }

  &.is-disabled {
    cursor: not-allowed;
  }

  &.is-blank {
    background-color: transparent !important;
    cursor: default;
    user-select: none;

    .panel-title,
    .panel-title-text {
      color: transparent !important;
    }

    .panel-title-icon {
      visibility: hidden;
    }
  }

  &.is-red-border {
    box-sizing: border-box;
    border: 1px solid #ff5630;
  }
}

.ant-popover-content {
  .popover-ssp {
    max-height: 300px;
    overflow-y: auto;

    p {
      margin: 0;
      font-size: 14px;
      line-height: 26px;
    }
  }
}
</style>
