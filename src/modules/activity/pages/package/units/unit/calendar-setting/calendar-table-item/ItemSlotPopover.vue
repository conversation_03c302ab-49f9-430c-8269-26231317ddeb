<template>
  <div class="calendar-table-item-slot-popover">
    <div>{{ data.date }}</div>
    <a-spin :spinning="loading">
      <div>
        <div class="flex space-between popover-header">
          <svg-icon :icon-name="data.visible ? 'eye' : 'eye-off'" style="font-size: 18px" />
          <div style="line-height: 20px">
            <span style="font-size: 12px; color: rgba(0, 0, 0, 0.25); margin-right: 4px">
              {{ weekText }}
            </span>
            <span style="font-size: 16px; color: #000; font-weight: 600">
              {{ dateText }}
            </span>
          </div>
          <a-button ghost type="link" size="small" style="padding: 0" @click="handleClose">
            <svg-icon icon-name="close-blank" style="font-size: 20px" />
          </a-button>
        </div>

        <div class="popover-content">
          <h4 class="popover-content-title">
            {{ formatTimeslot(data) }}
          </h4>
          <CalendarTableItemSlotPopoverForm
            ref="form"
            :data="data"
            :unit-data="unitData"
            :cost-currency="costCurrency"
            :selling-currency="sellingCurrency"
            :autofill-data="initialValue.auto_fill_schedule_item"
            :disabled="disabled"
            @autofill="handleAutofill"
            @updateForm="updateFormHandle"
          />

          <template v-if="!isMerchant && !isComboSku">
            <a-button
              v-if="data.price_can_edit"
              :disabled="(!canEdit && !auditCanEdit) || isPriceProtected || disabled"
              type="link"
              size="small"
              class="popover-content-ssp-btn"
              @click="handleOpenSsp"
            >
              {{ $t('28682') }} >>
            </a-button>
            <div v-else style="display: inline-block" @click="sendSpm('sendSspSpm')">
              <SkuModelTooltip v-if="showTooltip" @confirm="$emit('close')">
                <a-button type="link" size="small" disabled class="popover-content-ssp-btn">
                  {{ $t('28682') }} >>
                </a-button>
              </SkuModelTooltip>
              <a-button v-else type="link" size="small" disabled class="popover-content-ssp-btn">
                {{ $t('28682') }} >>
              </a-button>
            </div>
            <div class="popover-content-ssp">
              <SspBlockList
                :sspStyle="{
                  maxWidth: '100%'
                }"
                :showBtnSsp="false"
                :sspList="specialSellingPriceList"
              />
            </div>
          </template>
        </div>

        <div class="flex space-between" style="margin-top: 12px; font-size: 14px">
          <span>
            {{ $t('28792') }}
          </span>
          <a-switch
            v-model="data.published"
            :checked-children="$t('28793')"
            :un-checked-children="$t('28794')"
            :disabled="
              (!canEdit && !inventoryCanEdit) ||
              !data.timeslot_can_edit ||
              isTripMapped ||
              isPublishForbidden ||
              approvalIsNotOver ||
              disabled ||
              isCreateOnMerchant
            "
            :title="isTripMapped ? $t('28910') : ''"
            @click="sendSpm('sendActScheduleSpm')"
          />
        </div>
        <div v-if="!disabled" class="flex space-between" style="margin-top: 12px">
          <div @click="sendSpm('sendDelScheduleSpm')">
            <a-tooltip v-if="skuModel.inv_model === 4" :title="$t('48468')">
              <a-button size="small" :disabled="true" style="padding: 0; border: none">
                <svg-icon icon-name="delete" style="font-size: 20px" />
              </a-button>
            </a-tooltip>
            <a-popconfirm
              v-else-if="!isBdAudit && !isSubSku && !data.is_empty && !isComboSku"
              :title="$t('27808')"
              :ok-text="$t('global_button_ok')"
              :cancel-text="$t('global_button_cancel')"
              :overlay-style="{ maxWidth: '250px' }"
              :disabled="
                (!canEdit && !auditCanEdit && !inventoryCanEdit) ||
                isEmptySubSku ||
                disabled ||
                lockDelTimesolt.status
              "
              placement="top"
              @confirm="handleDelete"
            >
              <label
                v-tooltip="{
                  visible: lockDelTimesolt.status,
                  content: lockDelTimesolt.tips,
                  placement: 'bottom',
                  clearOtherTooltips: true
                }"
              >
                <a-button ghost type="link" size="small" style="padding: 0">
                  <svg-icon
                    :icon-name="lockDelTimesolt.status ? 'disabled-trash' : 'delete-trash'"
                    style="font-size: 20px"
                  />
                </a-button>
              </label>
            </a-popconfirm>
          </div>

          <a-button
            type="primary"
            size="small"
            :disabled="(!canEdit && !auditCanSave && !inventoryCanEdit) || isEmptySubSku || disabled"
            @click="handleConfirm"
          >
            {{ $t('global_save') }}
          </a-button>
        </div>
      </div>
    </a-spin>

    <SpecialSellingPriceDrawer
      :price="calcSellingPrice"
      :visible.sync="drawerVisible"
      :initial-value="specialSellingPriceList"
      :selling-currency="sellingCurrency"
      :sku-id="skuId"
      :time-points="timePoints"
      @confirm="handleDrawerConfirm"
    />

    <PriceReasonModal :visible.sync="priceReasonVisible" @confirm="handlePriceReasonConfirm" />
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { formatThousands } from '@activity/utils'
import { isPriceDifferent } from '@activity/pages/package/units/unit-list/calendar-setting/utils'
import SpecialSellingPriceDrawer from '@activity/pages/package/units/components/SpecialSellingPriceDrawer.vue'
import PriceReasonModal from '@activity/pages/package/units/components/PriceReasonModal.vue'
import CalendarTableItemSlotPopoverForm from './ItemSlotPopoverForm.vue'
import SkuModelTooltip from '../components/SkuModelTooltip.vue'
import { messageConfirm } from '@activity/pages/package/units/utils/message.js'
import SspBlockList from '@activity/pages/components/SspBlockList.vue'
import actMixins from '@activity/mixins/index.js'
import { UNSAFE_PRICE_CODE, showUnsavePriceReasonModal } from '@activity/components/UnsavePriceReasonModal'

export default {
  name: 'CalendarTableItemSlotPopover',
  inject: ['skuId', 'calendar', 'unitBar', 'getEditLockValueByPath'],
  mixins: [actMixins.sspPriceMixin],
  provide() {
    return {
      calendarItemPopover: this
    }
  },
  components: {
    SspBlockList,
    SpecialSellingPriceDrawer,
    CalendarTableItemSlotPopoverForm,
    PriceReasonModal,
    SkuModelTooltip
  },
  filters: {
    formatThousands
  },
  props: {
    initialValue: {
      type: Object,
      default: () => ({})
    },
    unitData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      cacheForm: {},
      data: {},

      loading: false,
      drawerVisible: false,
      priceReasonVisible: false,

      unsavePriceReason: {
        unsafe_price_reason_code: undefined,
        unsafe_price_reason_note: undefined
      }
    }
  },
  computed: {
    ...mapState({
      canEdit: (state) => state.canEdit,
      skuModel: (state) => state.skuModel,
      isMerchant: (state) => state.isMerchant,
      isBdAudit: (state) => state.isBdAudit,
      auditCanEdit: (state) => state.auditCanEdit,
      inventoryCanEdit: (state) => state.inventoryCanEdit,
      currentMerchantActApproveStatus: 'currentMerchantActApproveStatus'
    }),
    ...mapGetters(['isTripMapped', 'packageProductType']),
    isComboSku() {
      return this.packageProductType === 1
    },
    // 日历 item 编辑控制
    disabled() {
      return this.isMerchant && this.currentMerchantActApproveStatus === 1 // pkg pending approve
    },
    calcSellingPrice() {
      let { sellingPrice } = this.cacheForm
      let price = sellingPrice > 0 ? sellingPrice : '0'
      this.updateSspData({
        price,
        sellingCurrency: this.sellingCurrency
      })
      return price
    },
    isPriceProtected() {
      if (this.isTripMapped) {
        return this.unitData.is_private_price
      }
      return false
    },
    isSubSku() {
      // 子sku不能删除
      return this.unitData.share_rel_ship === 1
    },
    costCurrency() {
      return this.skuModel.merchant_currency || ''
    },
    sellingCurrency() {
      return this.skuModel.selling_currency || ''
    },
    weekText() {
      return moment(this.data.start_time).format('ddd')
    },
    dateText() {
      return moment(this.data.start_time).format('DD')
    },
    specialSellingPriceList() {
      if (!this.data.price) {
        return []
      }

      return this.data.price.special_selling_price || []
    },
    isPublishForbidden() {
      // 在inventory权限下，如果sku是发布状态，并且价格数据不完整，则不能修改发布状态，并且发布状态为false
      if (this.inventoryCanEdit && this.unitData.published && !this.data.price) {
        return true
      }

      return false
    },
    approvalIsNotOver() {
      return this.unitData.approval_status === 1
    },
    // 新增的时候，merchant 端不可激活
    isCreateOnMerchant() {
      return this.isMerchant && this.initialValue.is_empty
    },
    auditCanSave() {
      // 审核端只有数据完整才能save
      return !!this.initialValue.price && this.auditCanEdit
    },
    isEmptySubSku() {
      return this.isSubSku && this.initialValue.is_empty
    },
    showTooltip() {
      const { price_model } = this.unitData || {}
      return price_model === 0
    },
    timePoints() {
      // ssp 抽屉用来求 tr
      return [this.initialValue.start_time]
    },
    lockDelTimesolt() {
      return this.getLockByPathKey('inventory.del_timeslot')
    }
  },
  watch: {
    isPublishForbidden: {
      immediate: true,
      handler(val) {
        if (val) {
          this.data.published = true
        }
      }
    }
  },
  created() {
    // data是展示的数据，initialValue是原始数据，互不影响
    this.data = _.cloneDeep(this.initialValue || {})
  },
  methods: {
    sendSpm(event = '') {
      klook.bus.$emit(event)
    },
    getLockByPathKey(path) {
      return typeof this.getEditLockValueByPath === 'function'
        ? this.getEditLockValueByPath({
            sku_id: this.skuId,
            path
          })
        : {
            status: false,
            tips: ''
          }
    },
    updateSspData: _.debounce(async function (options) {
      let arr = this.specialSellingPriceList
      if (!arr || !arr.length) return
      await this.bindSspList2mixin(arr, options)
    }, 300),
    updateFormHandle(obj) {
      this.cacheForm = obj
    },
    async handleDelete() {
      // 如果是主sku，则要检测cut off time、库存、上下架状态、删除的变动，然后显示提示弹窗
      const { share_rel_ship } = this.unitData || {}
      if (share_rel_ship === 2) {
        const result = await messageConfirm(this, this.$t('27807'))

        if (!result) {
          return
        }
      }

      this.loading = true
      const res = await ajax.postBody(ADMIN_API.act.destroy_single_schedule, {
        data: {
          sku_id: this.skuId,
          start_time: this.data.start_time
        }
      })
      // const res = await new Promise((resolve) =>
      //   setTimeout(() => resolve({ success: true }), 500)
      // )
      this.loading = false

      if (res.success) {
        this.$message.success(this.$t('global_delete_success'))
        this.$emit('close')
        this.calendar.update()
      } else {
        this.$message.error(this.$t('global_delete_error'))
      }
    },
    resetUnsavePriceReason() {
      this.unsavePriceReason = {
        unsafe_price_reason_code: undefined,
        unsafe_price_reason_note: undefined
      }
    },
    async handleConfirm() {
      this.GTMHookItem('Price&Inventory|CalendarModifySave_Click')
      const valid = await this.$refs.form.validateForm()

      if (!valid) {
        return
      }

      // 如果是第一次，则不校验价格改变，直接save
      if (!this.initialValue.price) {
        await this.checkMainSkuAndSave()
        return
      }

      const params = this.getParams()

      if (this.$root.isAdmin && isPriceDifferent(params.price, this.initialValue.price)) {
        this.priceReasonVisible = true
      } else {
        await this.checkMainSkuAndSave()
      }
    },
    async checkMainSkuAndSave(note) {
      // 如果是主sku，则要检测cut off time、库存、上下架状态、删除的变动，然后显示提示弹窗
      const { share_rel_ship } = this.unitData || {}
      if (share_rel_ship === 2) {
        // global_inv_quantity
        const { published } = this.data
        const { inventory } = this.$refs.form.getData()
        const { inv_quantity, published: initialPublished } = this.initialValue

        if (published !== initialPublished || String(inventory) !== String(inv_quantity)) {
          const result = await messageConfirm(this, this.$t('27807'))
          result && (await this.createOrSave(note))
        } else {
          await this.createOrSave(note)
        }
      } else {
        await this.createOrSave(note)
      }

      klook.bus.$emit('refreshUnitList', { clearActiveId: false })
    },
    async createOrSave(note) {
      const params = this.getParams()

      if (note) {
        params.price.note = note
      }

      const { unsafe_price_reason_note, unsafe_price_reason_code } = this.unsavePriceReason || {}
      if (unsafe_price_reason_note) {
        params.price.unsafe_price_reason_note = unsafe_price_reason_note
        params.price.unsafe_price_reason_code = unsafe_price_reason_code
      }

      this.loading = true
      const apiPath = this.isBdAudit
        ? ADMIN_API.act.creates_or_update_single_schedule_audit
        : ADMIN_API.act.creates_or_update_single_schedule
      const res =
        (await ajax.postBody(
          apiPath,
          { data: params, noDefaultResponseInterceptor: true },
          {
            msgOpt: {
              isErrMsg: false
            }
          }
        )) || {}
      // const res = await new Promise((resolve) =>
      //   setTimeout(() => resolve({ success: true }), 500)
      // )
      this.loading = false

      if (res.error?.code === UNSAFE_PRICE_CODE) {
        // 如果价格校验不通过，唤起 UnSavePriceReasonModal
        const { confirm, cancel } = await showUnsavePriceReasonModal({
          warningMessage: res.error.message
        })
        if (!cancel) {
          const { reasonCode, reasonNote } = confirm
          this.unsavePriceReason = {
            // 清空之前的理由
            unsafe_price_reason_code: reasonCode,
            unsafe_price_reason_note: reasonNote
          }
          await this.createOrSave(note)
          return
        }
      }

      if (res.success) {
        this.$message.success(this.$t('global_modify_success'))
        this.$emit('close')
        this.calendar.update()

        // 侧栏的step
        await Promise.all([
          this.unitBar.saveSkuStep('sku_calendar'),
          this.unitBar.savePkgStep('price_inventory')
        ])
      } else {
        this.$message.error(res?.error?.message || this.$t('global_modify_error'))
      }
      this.resetUnsavePriceReason()
    },
    async handlePriceReasonConfirm(note) {
      await this.checkMainSkuAndSave(note)
    },
    handleAutofill() {
      const { auto_fill_schedule_item } = this.initialValue

      if (auto_fill_schedule_item && auto_fill_schedule_item.fix_price) {
        const { cost_price, retail_price, selling_price, special_selling_price } =
          auto_fill_schedule_item.fix_price

        if (!this.data.price) {
          this.data.price = _.cloneDeep(auto_fill_schedule_item.fix_price)
        } else {
          this.data.price.cost_price = cost_price
          this.data.price.retail_price = retail_price
          this.data.price.selling_price = selling_price

          if (special_selling_price) {
            this.data.price.special_selling_price = _.cloneDeep(special_selling_price)
          }
        }
      }

      if (auto_fill_schedule_item && auto_fill_schedule_item.inv_quantity !== -1) {
        this.data.inv_quantity = auto_fill_schedule_item.inv_quantity
      }

      this.$refs.form && this.$refs.form.init()
    },
    getParams() {
      const formData = this.$refs.form.getData()
      const params = {
        sku_id: this.skuId,
        start_time: this.data.start_time,
        inv_quantity: Number(formData.inventory),
        published: this.data.published,
        price: {
          cost_currency: this.costCurrency,
          selling_currency: this.sellingCurrency,
          cost_price: Number(formData.costPrice),
          retail_price: Number(formData.retailPrice),
          selling_price: Number(formData.sellingPrice),
          special_selling_price: this.data.price ? this.data.price.special_selling_price : []
        }
      }

      // 在商户端，如果 costPrice 和 retailPrice 都没填，则传 null
      if (this.isMerchant && formData.costPrice === '' && formData.retailPrice === '') {
        params.price = null
      }

      return params
    },
    formatTimeslot(item) {
      const splitedTime = item.start_time.split(' ')
      return !splitedTime[1] || splitedTime[1] === '00:00:00'
        ? this.$t('package_timeslot_by_day')
        : splitedTime[1].substring(0, 5)
    },
    handleDrawerConfirm(val) {
      if (this.data.price) {
        this.data.price.special_selling_price = [...val]
      } else {
        this.$set(this.data, 'price', {})
        this.$set(this.data.price, 'special_selling_price', [...val])
      }
    },
    handleClose() {
      this.$emit('close')
    },
    handleOpenSsp() {
      this.drawerVisible = true
      this.sendSpm('sendSspSpm')
    }
  }
}
</script>

<style lang="scss" scoped>
.calendar-table-item-slot-popover {
  width: 200px;

  .flex {
    display: flex;
    align-items: center;

    &.space-between {
      justify-content: space-between;
    }
  }

  .popover-content {
    margin-top: 14px;
    padding: 0 0 10px;

    max-height: 350px;
    overflow-y: auto;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    &-title {
      margin: 0 0 4px;

      color: #0091ff;
      font-size: 14px;
      line-height: 16px;
      text-align: center;
    }

    &-ssp-btn {
      padding: 0;
      font-weight: 600;
      font-size: 14px;
      line-height: 14px;
    }

    &-ssp {
      p {
        margin: 0;
        font-size: 14px;
        line-height: 20px;
      }
    }
  }

  .calendar-table-item-slot-form-input[disabled] {
    background-color: #eee !important;
  }
}
</style>
