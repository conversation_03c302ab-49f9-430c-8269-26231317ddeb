<template>
  <a-form-model class="calendar-table-item-slot-form" ref="form" :model="form" :rules="rules">
    <div class="item-slot-form-wrap">
      <a-tooltip placement="top" :title="$t('package_inventory')">
        <svg-icon class="form-item-icon" :icon-name="getIcon('inventory')" />
      </a-tooltip>
      <a-form-model-item prop="inventory">
        <CalendarTableItemSlotPopoverFormInput
          v-model="form.inventory"
          type="inventory"
          :fixed="!data.quantity_can_edit"
          :max="MAX_INVENTORY"
          :color="getColor('inventory')"
          :prefix="isComboSku ? '' : getInventoryPrefix()"
          :disabled="
            disabled ||
              inventoryDisabled ||
              isPICSku ||
              isComboSku ||
              getLockByPathKey('inventory.update_inv_quantity').status
          "
          :title="isTripMapped ? $t('28910') : ''"
          :placeholder="$t('package_inventory')"
          :show-edit="!!autofillData && autofillData.inv_quantity !== -1"
          :unit-data="unitData"
          class="slot-input-inventory"
          v-tooltip="{
            visible: getLockByPathKey('inventory.update_inv_quantity').status,
            content: getLockByPathKey('inventory.update_inv_quantity').tips,
            placement: 'right'
          }"
          @keyup="handleRound2Integer('inventory')"
          @autofill="autofill"
          @blur="sendSpm('sendInvSpm', form.inventory, data.inv_quantity)"
        />
      </a-form-model-item>
    </div>

    <div v-if="!isComboSku" class="item-slot-form-wrap">
      <a-tooltip placement="top" :title="$t('package_price_cost')">
        <svg-icon class="form-item-icon" :icon-name="getIcon('costPrice')" />
      </a-tooltip>
      <a-form-model-item prop="costPrice">
        <component
          :ticket-id="unitData.ticket_id"
          :is="disabledCostPriceBySkuTicketStatus ? 'approvalDetailPopover' : 'span'"
        >
          <CalendarTableItemSlotPopoverFormInput
            v-model="form.costPrice"
            :fixed="!data.price_can_edit"
            :max="MAX_PRICE"
            :color="getColor('costPrice')"
            :prefix="getPricePrefix(costCurrency)"
            :tooltip-msg="takeRateMsg"
            :disabled="disabled || costPriceDisabled || getLockByPathKey('price.update_cost_price').status"
            :title="isTripMapped ? $t('28910') : ''"
            :placeholder="$t('package_price_cost')"
            :show-edit="!!autofillData && !!autofillData.fix_price"
            :unit-data="unitData"
            class="slot-input-cost-price"
            v-tooltip="{
              visible: getLockByPathKey('price.update_cost_price').status,
              content: getLockByPathKey('price.update_cost_price').tips,
              placement: 'right',
              clearOtherTooltips: true
            }"
            @keyup="handleRound('costPrice')"
            @autofill="autofill"
            @blur="sendSpm('sendCostSpm', form.costPrice, data.price ? data.price.cost_price : '')"
          >
            <a-tooltip
              slot="suffix"
              v-if="costInputSuffixData.visible"
              placement="top"
              :title="costInputSuffixData.title"
            >
              <span class="cost-input-suffix" :class="costInputSuffixData.className"></span>
            </a-tooltip>
          </CalendarTableItemSlotPopoverFormInput>
        </component>
      </a-form-model-item>
    </div>

    <div class="item-slot-form-wrap">
      <a-tooltip placement="top" :title="$t('package_price_retail')">
        <svg-icon class="form-item-icon" :icon-name="getIcon('retailPrice')" />
      </a-tooltip>
      <a-form-model-item prop="retailPrice">
        <CalendarTableItemSlotPopoverFormInput
          v-model="form.retailPrice"
          :fixed="!data.price_can_edit"
          :max="MAX_PRICE"
          :color="getColor('retailPrice')"
          :prefix="getPricePrefix(sellingCurrency)"
          :disabled="disabled || retailPriceDisabled || getLockByPathKey('price.update_retail_price').status"
          :title="isTripMapped ? $t('28910') : ''"
          :placeholder="$t('package_price_retail')"
          :show-edit="!!autofillData && !!autofillData.fix_price"
          :unit-data="unitData"
          class="slot-input-retail-price"
          v-tooltip="{
            visible: getLockByPathKey('price.update_retail_price').status,
            content: getLockByPathKey('price.update_retail_price').tips,
            placement: 'right',
            clearOtherTooltips: true
          }"
          @keyup="handleRound('retailPrice')"
          @autofill="autofill"
          @blur="sendSpm('sendRpSpm', form.retailPrice, data.price ? data.price.retail_price : '')"
        />
      </a-form-model-item>
    </div>

    <div v-if="!isMerchant" class="item-slot-form-wrap">
      <a-tooltip placement="top" :title="$t('package_base_selling')">
        <svg-icon class="form-item-icon" :icon-name="getIcon('sellingPrice')" />
      </a-tooltip>
      <a-form-model-item prop="sellingPrice">
        <CalendarTableItemSlotPopoverFormInput
          v-model="form.sellingPrice"
          :fixed="!data.price_can_edit"
          :max="MAX_PRICE"
          :color="getColor('sellingPrice')"
          :prefix="getPricePrefix(sellingCurrency)"
          :tooltip-msg="takeRateMsg"
          :disabled="
            disabled || sellingPriceDisabled || getLockByPathKey('price.update_selling_price').status
          "
          :title="isTripMapped ? $t('28910') : ''"
          :placeholder="$t('package_price_selling')"
          :show-edit="!!autofillData && !!autofillData.fix_price"
          :unit-data="unitData"
          class="slot-input-selling-price"
          v-tooltip="{
            visible: getLockByPathKey('price.update_selling_price').status,
            content: getLockByPathKey('price.update_selling_price').tips,
            placement: 'right',
            clearOtherTooltips: true
          }"
          @keyup="handleRound('sellingPrice')"
          @autofill="autofill"
          @blur="sendSpm('sendSpSpm', form.sellingPrice, data.price ? data.price.selling_price : '')"
        />
      </a-form-model-item>
    </div>
  </a-form-model>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { round2Decimal, computeTR } from '@activity/utils'
import { MAX_PRICE, MAX_INVENTORY } from '@activity/pages/package/package_const.js'
import { getCurrencyPrefix, formatPriceByComma } from '@activity/pages/package/units/utils'
import CalendarTableItemSlotPopoverFormInput from './ItemSlotPopoverFormInput.vue'
import maps from '@activity/utils/maps.js'
import { checkAuth } from '@/plugins/authDirective'
import ApprovalDetailPopover from '../components/approvalDetailPopover'

export default {
  name: 'CalendarTableItemSlotForm',
  inject: ['legendColors', 'getEditLockValueByPath'],
  components: {
    ApprovalDetailPopover,
    CalendarTableItemSlotPopoverFormInput
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    costCurrency: {
      type: String,
      required: true
    },
    sellingCurrency: {
      type: String,
      required: true
    },
    unitData: {
      type: Object,
      required: true
    },
    autofillData: {
      type: Object,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validateInventory = (rule, value, callback) => {
      if (this.isComboSku) {
        callback()
      } else if (this.isPICSku && !value && value !== 0) {
        // pic库存要进行非空校验
        callback(new Error(this.$t('global_please_input')))
      } else if (!value && value !== 0 && this.data.quantity_can_edit && !this.inventoryDisabled) {
        callback(new Error(this.$t('global_please_input')))
      } else {
        callback()
      }
    }

    const validateCostPrice = (rule, value, callback) => {
      if (!value && value !== 0 && this.data.price_can_edit && !this.costPriceDisabled) {
        callback(new Error(this.$t('global_please_input')))
      } else {
        callback()
      }
    }

    const validateRetailPrice = (rule, value, callback) => {
      if (!value && value !== 0 && this.data.price_can_edit && !this.retailPriceDisabled) {
        callback(new Error(this.$t('global_please_input')))
      } else {
        callback()
      }
    }

    const validateSellingPrice = (rule, value, callback) => {
      if (!value && value !== 0 && this.data.price_can_edit && !this.sellingPriceDisabled) {
        callback(new Error(this.$t('global_please_input')))
      } else {
        callback()
      }
    }

    return {
      MAX_PRICE,
      MAX_INVENTORY,

      form: {
        inventory: '',
        costPrice: '',
        retailPrice: '',
        sellingPrice: '',
        takeRate: ''
      },
      rules: {
        inventory: [
          {
            validator: validateInventory,
            message: this.$t('global_please_input'),
            trigger: 'blur'
          }
        ],
        costPrice: [
          {
            validator: validateCostPrice,
            message: this.$t('global_please_input'),
            trigger: 'blur'
          }
        ],
        retailPrice: [
          {
            validator: validateRetailPrice,
            message: this.$t('global_please_input'),
            trigger: 'blur'
          }
        ],
        sellingPrice: [
          {
            validator: validateSellingPrice,
            message: this.$t('global_please_input'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  watch: {
    form: {
      deep: true,
      handler(obj) {
        this.$emit('updateForm', _.cloneDeep(obj))
      }
    }
  },
  computed: {
    ...mapState({
      canEdit: (state) => state.canEdit,
      exchangeRate: (state) => state.exchangeRate,
      isMerchant: (state) => state.isMerchant,
      isBdAudit: (state) => state.isBdAudit,
      auditCanEdit: (state) => state.auditCanEdit,
      inventoryCanEdit: (state) => state.inventoryCanEdit,
      skuModel: (state) => state.skuModel,
      merchantActHaveBeenApproved: 'merchantActHaveBeenApproved'
    }),
    ...mapGetters(['isTripMapped', 'packageProductType']),
    isComboSku() {
      return this.packageProductType === 1
    },
    takeRateMsg() {
      if (this.$root.isAdmin) {
        return `${this.$t('act_list_take_rate')}: ${this.form.takeRate}%`
      }
      return ''
    },
    isPriceProtected() {
      if (this.isTripMapped) {
        return this.unitData.is_private_price
      }

      return false
    },
    inventoryDisabled() {
      return (!this.canEdit && !this.inventoryCanEdit) || this.isTripMapped
    },
    costPriceDisabled() {
      return (
        !this.canEdit ||
        this.isTripMapped ||
        // pending approval
        this.disabledCostPriceBySkuTicketStatus ||
        // 商户活动或套餐被 BD approved 后，需要有 superPriceEdit 才能修改
        (this.merchantActHaveBeenApproved && !checkAuth('superPriceEdit')) ||
        this.priceDisabled.costPrice
      )
    },
    disabledCostPriceBySkuTicketStatus() {
      return this.unitData.ticket_status === 'PendingApproval'
    },
    retailPriceDisabled() {
      return !this.canEdit || this.priceDisabled.retailPrice
    },
    sellingPriceDisabled() {
      return (!this.canEdit && !this.auditCanEdit) || this.isPriceProtected || this.priceDisabled.sellingPrice
    },
    isPICSku() {
      return this.skuModel.inv_model === 4
    },
    // costInputSuffixClassName
    costInputSuffixData() {
      const { price } = this.data
      const currentStatus = _.find(maps.ticket_status_dot, { value: this.unitData.ticket_status })

      if (
        this.isMerchant &&
        price &&
        !isNaN(price.current_cost_price) &&
        price.current_cost_price !== -1 && // -1: 不存在草稿价格, -2: 不存在产线价格, 其他值则: 存在草稿和产线价格
        currentStatus
      ) {
        const { current_cost_price } = price
        const className = currentStatus.class + ' ticket-status-circle-suffix'

        return {
          title: `${__('48089')}: ${
            current_cost_price === -2 ? '-' : formatPriceByComma(current_cost_price)
          }`,
          className,
          visible: true
        }
      }

      return {
        visible: false
      }
    },
    priceDisabled() {
      const { combo_info } = this.unitData
      if (!combo_info) {
        return {}
      }
      const priceType = combo_info.selling_price_setting.type
      if (priceType === 3) {
        return {
          retailPrice: true,
          costPrice: true
        }
      }
      return {
        retailPrice: true,
        sellingPrice: true,
        costPrice: true
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    autofill() {
      this.$emit('autofill')
      // 埋点
      // ['sendInvSpm', 'sendCostSpm', 'sendRpSpm', 'sendSpSpm']
      const autofillData = this.autofillData
      if (!autofillData) {
        return
      }
      const { inv_quantity, fix_price } = autofillData
      const isMerchant = this.isMerchant
      if (inv_quantity !== -1) {
        klook.bus.$emit('sendInvSpm')
      }
      if (fix_price) {
        klook.bus.$emit('sendCostSpm')
        klook.bus.$emit('sendRpSpm')
        !isMerchant && klook.bus.$emit('sendSpSpm')
      }
    },
    sendSpm(event = '', formData, propData) {
      if (formData == propData) {
        return
      }
      klook.bus.$emit(event)
    },
    getLockByPathKey(path) {
      return typeof this.getEditLockValueByPath === 'function'
        ? this.getEditLockValueByPath({
            sku_id: this.unitData.sku_id,
            path
          })
        : {
            status: false,
            tips: ''
          }
    },
    init() {
      const { inv_quantity, price } = this.data || {}
      const { cost_price, retail_price, selling_price } = price || {}
      const { inventory, costPrice, retailPrice, sellingPrice } = this.form
      this.form = {
        inventory: this.fillNumber(inv_quantity, inventory),
        costPrice: this.fillNumber(cost_price, costPrice),
        retailPrice: this.fillNumber(retail_price, retailPrice),
        sellingPrice: this.fillNumber(selling_price, sellingPrice),
        takeRate: ''
      }
      this.computeTakeRate()
    },
    fillNumber(num, value) {
      const formatedNum = _.isUndefined(num) || num < 0 ? '' : num
      // 只有当没有值的时候才自动填充
      return value === '' ? formatedNum : value
    },
    validateForm() {
      return this.$refs.form.validate().catch(() => false)
    },
    getData() {
      return this.form
    },
    getColor(attr) {
      const target = this.legendColors.find((item) => _.camelCase(item.attr) === attr)
      return target ? target.color : '#000'
    },
    getIcon(attr) {
      const target = this.legendColors.find((item) => _.camelCase(item.attr) === attr)
      return target ? target.icon : ''
    },
    getInventoryPrefix() {
      return `${this.data.sales} /`
    },
    getPricePrefix(currency) {
      return getCurrencyPrefix(currency)
    },
    handleRound2Integer(field) {
      this.form[field] = Math.max(parseInt(this.form[field] || 0), 0)
    },
    handleRound(field) {
      this.form[field] = round2Decimal(this.form[field])

      if (['costPrice', 'sellingPrice'].includes(field)) {
        this.computeTakeRate()
      }
    },
    computeTakeRate() {
      this.form.takeRate = computeTR(this.form.costPrice, this.form.sellingPrice, this.exchangeRate)
    }
  }
}
</script>

<style lang="scss">
@import '../mixins.scss';

.calendar-table-item-slot-form {
  .slot-input-inventory {
    .ant-input:not(:first-child) {
      color: #000;
      padding-left: 45px;
    }
  }
  .slot-input-cost-price {
    position: relative;
    .ant-input:not(:first-child) {
      color: #6554c0;
      padding-left: 45px;
    }
  }
  .slot-input-retail-price {
    .ant-input:not(:first-child) {
      color: #00b8d9;
      padding-left: 45px;
    }
  }
  .slot-input-selling-price {
    .ant-input:not(:first-child) {
      color: #ff5630;
      padding-left: 45px;
    }
  }

  @include ticket-status-suffix;
}
</style>
<style lang="scss" scoped>
.calendar-table-item-slot-form {
  .item-slot-form-wrap {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;

    .form-item-icon {
      margin: 10px 4px 0 0;
      font-size: 20px;
    }
  }
}
</style>

<style lang="scss"></style>
