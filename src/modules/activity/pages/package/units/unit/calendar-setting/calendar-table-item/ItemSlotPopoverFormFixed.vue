<template>
  <div
    class="popover-form-fixed"
    :class="{ 'show-edit': showEdit, 'has-border': !backgroundColor }"
    :style="{ 'background-color': backgroundColor }"
    @click="handleClick"
  >
    <template v-if="text !== ''">
      <span>
        {{ prefix }}
      </span>
      <span :style="{ color: textColor }">
        {{ text }}
      </span>
    </template>
    <span v-else class="popover-form-fixed-placeholder">
      {{ placeholder }}
    </span>

    <svg-icon class="icon-edit" icon-name="edit" />
  </div>
</template>

<script>
export default {
  name: 'CalendarTableItemSlotFormFixed',
  props: {
    prefix: {
      type: String,
      required: true
    },
    text: {
      type: String,
      required: true
    },
    textColor: {
      type: String,
      required: true
    },
    placeholder: {
      type: String,
      default: ''
    },
    showEdit: {
      type: Boolean,
      default: false
    },
    backgroundColor: {
      type: String,
      default: ''
    }
  },
  methods: {
    handleClick() {
      this.$emit('click')
    }
  }
}
</script>

<style lang="scss" scoped>
.popover-form-fixed {
  position: relative;
  margin: 5px 0 0;
  width: 160px;
  height: 32px;
  padding: 5px 5px 5px 12px;

  border-radius: 2px;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  cursor: not-allowed;

  &.show-edit {
    cursor: pointer;

    .icon-edit {
      display: block;
    }
  }

  &.has-border {
    // 没有底色就加个框
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
  }

  &-placeholder {
    font-weight: normal;
    color: rgba(0, 0, 0, 0.25);
  }

  .icon-edit {
    display: none;
    position: absolute;
    top: 6px;
    right: 6px;
    width: 18px;
    height: 18px;
  }
}
</style>
