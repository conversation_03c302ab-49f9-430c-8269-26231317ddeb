<template>
  <div class="calendar-table-item-slot-form-input">
    <template v-if="fixed">
      <!-- 出现编辑笔的时候不显示弹窗 -->
      <CalendarTableItemSlotPopoverFormFixed
        v-if="(showEdit && realValueText === '') || !showTooltip"
        :show-edit="showEdit && realValueText === ''"
        :prefix="realPrefix"
        :text="realValueText"
        :text-color="color"
        :placeholder="placeholder"
        :background-color="backgroundColor"
        @click="handleClickFixed"
      />
      <SkuModelTooltip v-else :type="type" @confirm="closePopover">
        <CalendarTableItemSlotPopoverFormFixed
          :prefix="realPrefix"
          :text="realValueText"
          :text-color="color"
          :placeholder="placeholder"
          :background-color="backgroundColor"
        />
      </SkuModelTooltip>
    </template>
    <a-tooltip
      v-else-if="!!tooltipMsg"
      :trigger="['focus', 'click']"
      destroyTooltipOnHide
      placement="topLeft"
    >
      <span slot="title">
        {{ tooltipMsg }}
      </span>
      <a-input
        ref="input"
        v-model="realValue"
        :prefix="realPrefix"
        :placeholder="placeholder"
        v-bind="$attrs"
        style="width: 160px;"
        :style="{ color }"
        @change="$emit('change')"
        @blur="$emit('blur')"
        @focus="handleFocus"
        @keyup.native="handleKeyUp"
        @keydown.native="handleKeyDown"
      />
    </a-tooltip>
    <a-input
      v-else
      ref="input"
      v-model="realValue"
      :prefix="realPrefix"
      :placeholder="placeholder"
      v-bind="$attrs"
      style="width: 160px;"
      :style="{ color }"
      @change="$emit('change')"
      @blur="$emit('blur')"
      @focus="handleFocus"
      @keyup.native="handleKeyUp"
      @keydown.native="handleKeyDown"
    />
    <slot name="suffix"></slot>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { SUPPORT_CURRENCY_SYMBO } from '@activity/pages/package/package_const.js'
import { formatThousands } from '@activity/utils'
import SkuModelTooltip from '../components/SkuModelTooltip.vue'
import CalendarTableItemSlotPopoverFormFixed from './ItemSlotPopoverFormFixed.vue'

export default {
  name: 'CalendarTableItemSlotFormInput',
  inject: ['calendar', 'calendarItemPopover'],
  components: {
    SkuModelTooltip,
    CalendarTableItemSlotPopoverFormFixed
  },
  props: {
    value: {
      type: [String, Number],
      default: undefined
    },
    fixed: {
      type: Boolean,
      default: false
    },
    prefix: {
      type: String,
      default: ''
    },
    color: {
      type: String,
      default: 'rgba(0, 0, 0, 0.45)'
    },
    tooltipMsg: {
      type: String,
      default: ''
    },
    max: {
      type: Number,
      default: Infinity
    },
    type: {
      type: String,
      default: 'price'
    },
    showEdit: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: [String, Number],
      default: ''
    },
    unitData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      currentCursorStart: null,
      currentCursorEnd: null,
      currentValue: null
    }
  },
  computed: {
    ...mapState({
      skuModel: (state) => state.skuModel
    }),
    inputElem() {
      return this.$refs.input.$el.querySelector('input')
    },
    realValue: {
      get() {
        return `${this.value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        // return this.value
      },
      set(val) {
        // const prevVal = this.realValue
        // this.value = String(val).replace(/\$\s?|(,*)/g, '')
        this.$emit('input', String(val).replace(/\$\s?|(,*)/g, ''))
        this.fixCursor()
      }
    },
    realPrefix() {
      const { prefix } = this
      const target = SUPPORT_CURRENCY_SYMBO.find((item) => item[0] === prefix)
      return target ? target[1] : prefix
    },
    realValueText() {
      const { realValue, type } = this
      const { inv_model } = this.skuModel || {}

      // 空的边界情况
      if (realValue === '') {
        return ''
      }

      // 无限库存
      if (type === 'inventory' && inv_model === 2) {
        return this.$t('package_unlimited')
      }

      const parsedValue = this.numberParser(realValue)
      return formatThousands(parsedValue)
    },
    isSubSku() {
      const { share_rel_ship } = this.unitData
      return share_rel_ship === 1
    },
    backgroundColor() {
      const { type, isSubSku } = this
      const { price_model, inv_model } = this.unitData || {}

      // 如果是 inventory 并且是 子sku，则显示灰色 rgba(0, 0, 0, 0.09)
      if (type === 'inventory' && isSubSku) {
        return 'rgba(0, 0, 0, 0.09)'
      }

      // 如果是全局库存，则显示黄色 rgba(255, 171, 0, 0.1)
      if (type === 'inventory' && inv_model === 3) {
        return 'rgba(255, 171, 0, 0.1)'
      }

      // 如果是固定价格，则显示黄色 rgba(255, 171, 0, 0.1)
      if (type === 'price' && price_model === 0) {
        return 'rgba(255, 171, 0, 0.1)'
      }

      return ''
    },
    showTooltip() {
      const { type } = this
      const { price_model, inv_model, share_rel_ship } = this.unitData || {}

      // 对于库存来说，子sku或者全局库存需要展示tooltip
      if (type === 'inventory') {
        return share_rel_ship === 1 || inv_model === 3
      }

      // 对于价格来说，固定价格才需要展示tooltip
      if (type === 'price') {
        return price_model === 0
      }

      return false
    }
  },
  mounted() {
    // 根据prefix的宽度动态调整input的左padding
    const prefixDom = this.$el && this.$el.querySelector('.ant-input-prefix')
    const inputDom = this.$el && this.$el.querySelector('.ant-input')

    if (prefixDom && inputDom) {
      inputDom.style.paddingLeft = `${15 + prefixDom.offsetWidth}px`
    }
  },
  methods: {
    fixCursor(prevVal) {
      // 需要纠正光标的位置
      // 参考：https://github.com/vueComponent/ant-design-vue/blob/next/components/vc-input-number/src/index.js
      this.$nextTick(() => {
        const { currentCursorEnd, currentValue, realValue } = this
        const rightStr = String(currentValue).substring(currentCursorEnd)
        const index = realValue.lastIndexOf(rightStr)

        if (index > 0 && index + rightStr.length === realValue.length) {
          this.inputElem && this.inputElem.setSelectionRange(index, index)
        }
      })
    },
    recordCursorPosition() {
      this.currentCursorStart = this.inputElem && this.inputElem.selectionStart
      this.currentCursorEnd = this.inputElem && this.inputElem.selectionEnd
      this.currentValue = this.realValue
      // this.cursorBefore = this.realValue.substring(0, this.cursorStart)
      // this.cursorAfter = this.realValue.substring(this.cursorEnd)
    },
    handleFocus() {
      this.$emit('focus')
      this.recordCursorPosition()
    },
    numberParser(str) {
      const value = String(str).replace(/\$\s?|(,*)/g, '')
      return parseFloat(value)
    },
    handleConfirm() {
      this.calendar.openSkuModel()
    },
    handleKeyUp() {
      if (this.max < this.numberParser(this.realValue)) {
        this.realValue = this.max
      }

      this.$emit('keyup')
      this.recordCursorPosition()
    },
    handleKeyDown() {
      this.$emit('keydown')
      this.recordCursorPosition()
    },
    closePopover() {
      this.calendarItemPopover.$emit('close')
    },
    handleClickFixed() {
      if (this.showEdit && this.realValueText === '') {
        this.$emit('autofill')
      }
    }
  }
}
</script>
