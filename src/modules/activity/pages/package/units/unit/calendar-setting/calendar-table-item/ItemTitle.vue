<template>
  <div class="calendar-table-item-title">
    <CalendarTableItemTitlePanel class="is-outdate" :data="data" :date-range="dateRange" />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import CalendarTableItemTitlePanel from './ItemTitlePanel.vue'

export default {
  name: 'CalendarTableItemTitle',
  inject: ['calendar'],
  components: {
    CalendarTableItemTitlePanel
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    dateRange: {
      type: Array,
      required: true
    },
    unitData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      visible: false
      // noviceTipsVisible: false // 新手提示
    }
  },
  computed: {
    ...mapState({
      clickTarget: (state) => state.clickTarget
    }),
    tooltipText() {
      const { date } = this.data
      const [startDate, endDate] = this.dateRange
      const isDateTheSameOrAfterStartDate = startDate && moment(date).isSameOrAfter(startDate, 'day')
      return isDateTheSameOrAfterStartDate && !endDate ? this.$t('27802') : this.$t('27801')
    },
    hideTooltip() {
      if (this.$attrs.closeSetDateRange) {
        return true
      }

      // 当选了范围之后，隐藏tooltip
      const [startDate, endDate] = this.dateRange
      return startDate && endDate
    }
  },
  watch: {
    clickTarget(target) {
      // popover
      if (target && this.visible) {
        const appDom = document.querySelector('#app')
        const panelDom = this.$el && this.$el.querySelector('.calendar-table-item-title-panel')
        const popoverDom = document.querySelector('.calendar-table-item-title-popover')

        const isInApp = appDom && appDom.contains(target)
        const isInPanel = panelDom && panelDom.contains(target)
        const isInPopover = popoverDom && popoverDom.contains(target)

        if (isInApp && !isInPanel && !isInPopover) {
          this.visible = false
        }
      }

      // 关闭新手引导
      // this.noviceTipsVisible = false
    }
  },
  // mounted() {
  //   setTimeout(() => {
  //     const { step } = this.unitData
  //     const { isToday } = this.data
  //     if (isToday && (!step || !step.includes('sku_calendar'))) {
  //       this.noviceTipsVisible = true
  //     }
  //   }, 200)
  // },
  methods: {
    handleClick() {
      this.calendar.setDateRange(this.data.date)

      this.$nextTick(() => {
        const [startDate, endDate] = this.dateRange
        if (startDate && endDate) {
          this.visible = true
        }
      })
    },
    handleCancel() {
      this.visible = false
      this.calendar.clearDateRange()
    },
    handleConfirm() {
      this.GTMHookItem('Price&Inventory|CalendarDateRangeConfirm_Click')
      const [startDate, endDate] = this.dateRange
      this.handleCancel()
      this.calendar.openBulkModification(startDate, endDate)
    }
  }
}
</script>
