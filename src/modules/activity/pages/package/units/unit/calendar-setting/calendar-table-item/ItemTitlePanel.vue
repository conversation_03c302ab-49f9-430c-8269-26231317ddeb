<template>
  <div class="calendar-table-item-title-panel" :class="{ 'is-outdate': data.isOutdate }">
    <span>
      <a-checkbox v-if="data.displayDateMangeCheckbox" v-model="data.checked" @change="onChange"></a-checkbox>
    </span>
    <span>
      <span
        v-if="data.isToday"
        style="color: #437DFF; font-weight: normal; font-size: 12px; margin-right: 4px;"
      >
        {{ $t('package_schedule_today') }}
      </span>
      {{ data.text }}
    </span>
  </div>
</template>

<script>
import { mapMutations } from 'vuex'

export default {
  name: 'CalendarTableItemTitlePanel',
  inject: ['calendar'],
  props: {
    data: {
      type: Object,
      required: true
    },
    dateRange: {
      type: Array,
      required: true
    }
  },
  computed: {
    excludeDateList() {
      return this.$store.state.bulkManageSchedule.excludeDateList
    }
  },
  methods: {
    ...mapMutations(['setBulkManageSchedule']),
    onChange($event) {
      const { checked } = $event.target
      let excludeDateList = []
      if (checked) {
        excludeDateList = this.excludeDateList.filter((date) => date !== this.data.date)
      } else {
        excludeDateList = [...this.excludeDateList, this.data.date]
      }

      this.setBulkManageSchedule({ excludeDateList })
    }
  }
}
</script>

<style lang="scss">
.calendar-table-item-title-panel {
  position: relative;
  width: 100%;
  height: 27px;
  padding: 4px 6px;
  margin: 0;

  display: flex;
  align-items: center;
  justify-content: space-between;

  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  color: #000;
  text-align: right;
  cursor: pointer;

  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.04);

  &:hover {
    border-color: #0091ff;
    background: rgba(0, 145, 255, 0.1);
  }

  &.is-outdate {
    cursor: default;
    border-color: transparent;
    background-color: rgba(0, 0, 0, 0.04);
  }
}
</style>
