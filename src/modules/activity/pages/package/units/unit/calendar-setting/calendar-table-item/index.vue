<template>
  <div class="calendar-table-item" :class="bodyClass">
    <CalendarTableItemTitle :data="data" :unit-data="unitData" :date-range="dateRange" v-bind="$attrs" />
    <CalendarTableItemSlot
      v-for="(item, index) in slicedData"
      :key="item.time"
      :data="item"
      :unit-data="unitData"
      :is-outdate="data.isOutdate"
      :week-num="data.weekNum"
      :slot-id="`${data.date}-${index}`"
    />
    <CalendarTableItemSlot
      v-if="slicedData.length === 0"
      :data="null"
      :unit-data="unitData"
      :is-outdate="data.isOutdate"
      :week-num="data.weekNum"
      :slot-id="`${data.date}-0`"
    />
  </div>
</template>

<script>
import CalendarTableItemTitle from './ItemTitle.vue'
import CalendarTableItemSlot from './ItemSlot.vue'

/**
 * 这里接收到的数据结构：
 * weekNum: Number 一周中的星期几
 * date: String 日期，比如'2021-06-12'
 * text: String 几号，比如'03'
 * type: String 前一个月(prev)、当前月(current)、下个月(next)
 * isOutdate: 是否过期了
 * originalData: Array 备份数据，仅用来进行筛选
 * data: Array 数据
 */

export default {
  name: 'CalendarTableItem',
  inject: ['calendar'],
  components: {
    CalendarTableItemTitle,
    CalendarTableItemSlot
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    dateRange: {
      type: Array,
      required: true
    },
    more: {
      type: Boolean,
      default: false
    },
    moreNum: {
      type: Number,
      default: 3
    },
    unitData: {
      type: Object,
      required: true
    }
  },
  computed: {
    slicedData() {
      return this.more ? this.data.data.slice(0, this.moreNum) : this.data.data
    },
    bodyClass() {
      const { date, weekNum } = this.data
      const [startDate, endDate] = this.dateRange

      // 只选了一个
      if (startDate && !endDate) {
        return {
          'is-sole': moment(date).isSame(startDate, 'day')
        }
      }

      // 选了两个
      if (startDate && endDate) {
        const isStart = moment(date).isSame(startDate, 'day')
        const isBetween = moment(date).isAfter(startDate, 'day') && moment(date).isBefore(endDate, 'day')
        const isEnd = moment(date).isSame(endDate, 'day')

        let isSole = isStart && startDate === endDate
        let isHead = isStart && startDate !== endDate
        let isBody = isBetween
        let isTail = isEnd && startDate !== endDate

        // 处理边界情况
        const isSun = weekNum === 0
        const isSat = weekNum === 6
        isSole = isSole || (isSun && isTail) || (isSat && isHead)
        isHead = (isHead && !isSat) || (isSun && isBody)
        isTail = (isTail && !isSun) || (isSat && isBody)
        isBody = isBody && !isSat && !isSun

        return {
          'is-sole': isSole,
          'is-head': isHead,
          'is-body': isBody,
          'is-tail': isTail
        }
      }

      return {}
    }
  }
}
</script>

<style lang="scss" scoped>
.calendar-table-item {
  position: relative;
  width: calc(100% / 7);
  padding: 0 4px;
  z-index: 0;

  &:last-child {
    margin-right: 0;
  }

  &:after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;

    border-radius: 4px;
    box-sizing: border-box;
    border: 1px solid transparent;
    background-color: transparent;

    z-index: 2;
    pointer-events: none;
  }

  &.is-sole:after {
    border: 1px solid #0091ff;
    background-color: rgba(0, 145, 255, 0.1);
  }

  &.is-head:after {
    border-radius: 4px 0 0 4px;
    border: 1px solid #0091ff;
    border-right: none;
    background-color: rgba(0, 145, 255, 0.1);
  }

  &.is-tail:after {
    border-radius: 0 4px 4px 0;
    border: 1px solid #0091ff;
    border-left: none;
    background-color: rgba(0, 145, 255, 0.1);
  }

  &.is-body:after {
    border-radius: 0;
    border: 1px solid #0091ff;
    border-left: none;
    border-right: none;
    background-color: rgba(0, 145, 255, 0.1);
  }
}
</style>
