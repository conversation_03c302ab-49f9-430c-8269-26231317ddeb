<template>
  <div class="fixed-price-form">
    <a-spin :spinning="isTakeRateLoading">
      <TakeRateTips
        v-if="overrideTakeRate || lastTakeRate"
        :override-take-rate="overrideTakeRate"
        :last-take-rate="lastTakeRate"
      />
    </a-spin>

    <a-form-model ref="form" class="fixed-price-form-content" :model="form" :rules="rules">
      <div class="flex-wrap">
        <slot
          name="extra"
          :form="form"
          :costCurrency="costCurrency"
          :sellingCurrency="sellingCurrency"
        ></slot>
        <a-form-model-item
          v-if="!needHiddenField.includes('costPrice') && !isComboSku"
          style="margin-right: 24px;"
          prop="costPrice"
          :class="{
            'exist-input-suffix': costInputSuffixData.visible
          }"
        >
          <template #label>
            <slot name="costPriceLabel">
              {{ $t('package_price_cost') }}
            </slot>
          </template>
          <a-input
            v-model="form.costPrice"
            :suffix="costCurrency"
            :placeholder="$t('global_please_input')"
            :disabled="!canEdit || disabledCostPrice || disabled || lockCostPrice.status"
            :class="costInputSuffixData"
            style="width: 200px;"
            v-tooltip="{
              visible: lockCostPrice.status,
              content: lockCostPrice.tips,
              placement: 'bottom'
            }"
            @keyup.native="handleRound('costPrice')"
            @blur="handleCostPriceBlur"
          />
          <a-tooltip v-if="costInputSuffixData.visible" placement="top" :title="costInputSuffixData.title">
            <span class="cost-input-suffix" :class="costInputSuffixData.className"></span>
          </a-tooltip>
        </a-form-model-item>
        <a-form-model-item
          v-if="!needHiddenField.includes('retailPrice')"
          style="margin-right: 24px;"
          prop="retailPrice"
        >
          <template #label>
            <slot name="retailPriceLabel">
              {{ $t('package_price_retail') }}
            </slot>
          </template>
          <a-input
            v-model="form.retailPrice"
            :suffix="sellingCurrency"
            :placeholder="$t('global_please_input')"
            :disabled="!canEdit || disabled || lockRetailPrice.status || priceDisabled['retailPrice']"
            v-tooltip="{
              visible: lockRetailPrice.status,
              content: lockRetailPrice.tips,
              placement: 'bottom'
            }"
            style="width: 200px;"
            @keyup.native="handleRound('retailPrice')"
          />
        </a-form-model-item>
        <a-form-model-item
          v-if="!isMerchant && !needHiddenField.includes('sellingPrice')"
          style="margin-right: 24px;"
          prop="sellingPrice"
        >
          <template #label>
            <slot name="sellingPriceLabel">
              {{ $t('package_price_selling') }}
            </slot>
          </template>
          <a-input
            v-model="form.sellingPrice"
            :suffix="sellingCurrency"
            :placeholder="$t('global_please_input')"
            :disabled="
              (!canEdit && !auditCanEdit) ||
                disabled ||
                lockSellingPrice.status ||
                priceDisabled['sellingPrice']
            "
            v-tooltip="{
              visible: lockSellingPrice.status,
              content: lockSellingPrice.tips,
              placement: 'bottom'
            }"
            style="width: 200px;"
            @keyup.native="handleRound('sellingPrice')"
            @blur="computeTakeRate"
          />
        </a-form-model-item>
        <a-form-model-item v-if="!isMerchant && !needHiddenField.includes('takeRate')" prop="takeRate">
          <template #label>
            <slot name="takeRateLabel">
              {{ $t('new_take_rate') }}
            </slot>
          </template>
          <a-input
            v-model="form.takeRate"
            suffix="%"
            :placeholder="$t('global_please_input')"
            :disabled="
              (!canEdit && !auditCanEdit) || disabled || lockTakeRate.status || priceDisabled['takeRate']
            "
            v-tooltip="{
              visible: lockTakeRate.status,
              content: lockTakeRate.tips,
              placement: 'bottom'
            }"
            style="width: 200px;"
            @keyup.native="handleRoundTakeRate"
            @blur="handleTakeRateBlur"
          />
        </a-form-model-item>
      </div>
      <div v-if="!isMerchant && !needHiddenField.includes('ssp') && !isComboSku" class="ssp-block-list-box">
        <SspBlockList
          :sspStyle="{
            width: '192px'
          }"
          ref="sspBlockList"
          @click="specialSellingPrice.visible = true"
          :sspList="form.specialSellingPriceList"
          :showBtnSsp="$attrs.showBtnSsp || true"
        />
        <SpecialSellingPriceDrawer
          :price="form.sellingPrice"
          :visible.sync="specialSellingPrice.visible"
          :initial-value="form.specialSellingPriceList"
          :sellingCurrency="sellingCurrency"
          @confirm="handleSSPDrawerConfirm"
          :takeRate="{
            overrideTakeRate: overrideTakeRate,
            lastTakeRate: lastTakeRate
          }"
        />
      </div>
      <a-form-model-item
        v-if="unitData.inv_model === 3 && !needHiddenField.includes('inventory')"
        ref="inventory"
        :label="$t('27783')"
        prop="inventory"
      >
        <a-tooltip placement="top" arrow-point-at-center>
          <template slot="title">
            {{ $t('29440') }}
          </template>
          <a-input-number
            v-model="form.inventory"
            style="width: 320px;"
            :max="MAX_INVENTORY"
            :min="0"
            :disabled="
              (!canEdit && !inventoryCanEdit) || isSubSku || disabled || lockInvQuantity.status || isComboSku
            "
            v-tooltip="{
              visible: lockInvQuantity.status,
              content: lockInvQuantity.tips,
              placement: 'bottom'
            }"
            @blur="handleRound2Integer('inventory')"
          />
        </a-tooltip>
      </a-form-model-item>
    </a-form-model>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import {
  round2Decimal,
  provingTakeRate,
  computeTR,
  setTRAndGetBase,
  setCostAndGetBase
} from '@activity/utils'
import { MAX_INVENTORY } from '@activity/pages/package/package_const.js'
import TakeRateTips from '@activity/pages/components/TakeRateTips.vue'
import SspBlockList from '@activity/pages/components/SspBlockList.vue'
import SpecialSellingPriceDrawer from '@activity/pages/package/units/components/SpecialSellingPriceDrawer.vue'

import actMixins from '@activity/mixins/index.js'
import maps from '@activity/utils/maps.js'
import { checkAuth } from '@/plugins/authDirective'
import { formatPriceByComma } from '@activity/pages/package/units/utils'

export default {
  name: 'FixedPriceForm',
  inject: ['activityId', 'skuId', 'unitBar', 'calendarSetting', 'getEditLockValueByPath'],
  components: {
    SspBlockList,
    TakeRateTips,
    SpecialSellingPriceDrawer
  },
  mixins: [actMixins.sspPriceMixin],
  props: {
    unitData: {
      type: Object,
      required: true
    },
    calendarSettingData: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    needHiddenField: {
      type: Array,
      default: () => []
    },
    showCostInputSuffixData: {
      type: Boolean,
      default: true
    }
  },
  data() {
    const validateSpecialSellingPriceList = (rule, value, callback) => {
      const isNotValid = value.some((item) => !item.price && item.price !== 0)
      const currencyList = this.form.specialSellingPriceList.map((item) => item.currency)
      const isCurrencyDuplicated = _.uniq(currencyList).length < currencyList.length

      if (isCurrencyDuplicated) {
        callback(new Error('Duplicated currency'))
      } else if (isNotValid) {
        callback(new Error(this.$t('global_please_input')))
      } else {
        callback()
      }
    }

    return {
      specialSellingPrice: {
        visible: false
      },
      MAX_INVENTORY,

      isSubSku: false,
      isTakeRateLoading: false,
      overrideTakeRate: '',
      lastTakeRate: '',

      form: {
        costPrice: '',
        retailPrice: '',
        sellingPrice: '',
        takeRate: '',
        specialSellingPriceList: [],
        inventory: undefined
      },
      rules: {
        costPrice: [
          {
            required: true,
            message: this.$t('global_please_input'),
            trigger: 'blur'
          }
        ],
        retailPrice: [
          {
            required: true,
            message: this.$t('global_please_input'),
            trigger: 'blur'
          }
        ],
        sellingPrice: [
          {
            required: true,
            message: this.$t('global_please_input'),
            trigger: 'blur'
          }
        ],
        specialSellingPriceList: [
          {
            validator: validateSpecialSellingPriceList,
            trigger: 'blur'
          }
        ],
        inventory: [
          {
            required: true,
            message: this.$t('global_please_input'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    ...mapState({
      canEdit: (state) => state.canEdit,
      inventoryCanEdit: (state) => state.inventoryCanEdit,
      skuModel: (state) => state.skuModel,
      exchangeRate: (state) => state.exchangeRate,
      isMerchant: (state) => state.isMerchant,
      auditCanEdit: (state) => state.auditCanEdit,
      destinationToday: (state) => state.destinationToday,
      merchantActHaveBeenApproved: 'merchantActHaveBeenApproved'
    }),
    // combo
    isComboSku() {
      return this.packageProductType === 1
    },
    priceDisabled() {
      const { combo_info } = this.unitData
      if (!combo_info) {
        return {}
      }
      const priceType = combo_info.selling_price_setting.type
      if (priceType === 3) {
        return {
          retailPrice: true,
          takeRate: true
        }
      }
      return {
        retailPrice: true,
        sellingPrice: true,
        takeRate: true
      }
    },
    ...mapGetters(['packageProductType']),
    costCurrency() {
      return _.get(this.skuModel, 'merchant_currency', '')
    },
    sellingCurrency() {
      return _.get(this.skuModel, 'selling_currency', '')
    },
    disabledCostPrice() {
      return (
        this.unitData.ticket_status === 'PendingApproval' ||
        (this.merchantActHaveBeenApproved && !checkAuth('superPriceEdit'))
      ) // 商户活动或套餐被 BD approved 后，需要有 superPriceEdit 才能修改
    },
    costInputSuffixData() {
      const { fix_price } = this.calendarSettingData
      const currentStatus = _.find(maps.ticket_status_dot, { value: this.unitData.ticket_status })

      if (
        this.showCostInputSuffixData &&
        this.isMerchant &&
        fix_price &&
        !isNaN(fix_price.current_cost_price) &&
        fix_price.current_cost_price !== -1 &&
        currentStatus
      ) {
        const { current_cost_price } = fix_price
        const className = currentStatus.class + ' ticket-status-circle-suffix'

        return {
          visible: true,
          title: `${__('48089')}: ${
            current_cost_price === -2 ? '-' : formatPriceByComma(current_cost_price)
          }`,
          className
        }
      }

      return {
        visible: false
      }
    },
    lockCostPrice() {
      return this.getLockByPathKey('price.update_cost_price')
    },
    lockRetailPrice() {
      return this.getLockByPathKey('price.price.update_retail_price')
    },
    lockSellingPrice() {
      return this.getLockByPathKey('price.update_selling_price')
    },
    lockTakeRate() {
      const selling = this.getLockByPathKey('price.update_selling_price')
      const cost = this.getLockByPathKey('price.update_cost_price')

      if (selling.status && cost.status) {
        return selling
      }

      return {
        status: false,
        tips: ''
      }
    },
    lockInvQuantity() {
      return this.getLockByPathKey('inventory.update_inv_quantity')
    }
  },
  watch: {
    'form.sellingPrice'(value) {
      let sspList = _.get(this.form, 'specialSellingPriceList', [])
      sspList.length &&
        this.updateSspData(
          {
            price: value,
            sellingCurrency: this.sellingCurrency
          },
          sspList
        )
    },
    calendarSettingData: {
      deep: true,
      immediate: true,
      handler() {
        this.initData()
      }
    },
    unitData: {
      deep: true,
      immediate: true,
      handler() {
        this.initData()
      }
    }
  },
  async mounted() {
    if (this.$root.isAdmin) {
      await this.getTakeRate()
    }
  },
  methods: {
    getLockByPathKey(path) {
      return typeof this.getEditLockValueByPath === 'function'
        ? this.getEditLockValueByPath({
            sku_id: this.skuId,
            path
          })
        : {
            status: false,
            tips: ''
          }
    },
    initData: _.debounce(function() {
      // 子sku要默认填充主sku的库存
      const { unitData, calendarSettingData } = this
      const { fix_price, global_inv_quantity } = calendarSettingData

      this.isSubSku = unitData.share_rel_ship === 1

      // 自动填充价格
      if (fix_price) {
        let { cost_price, retail_price, selling_price, take_rate, special_selling_price } = fix_price
        take_rate = take_rate || ''

        this.$set(this, 'form', {
          costPrice: cost_price,
          retailPrice: retail_price,
          sellingPrice: selling_price,
          takeRate: take_rate,
          specialSellingPriceList: _.cloneDeep(special_selling_price || [])
        })

        if (isNaN(take_rate) || take_rate === '') {
          this.computeTakeRate()
        } else {
          this.handleTakeRateBlur()
        }
      }

      // 自动填充库存
      if (global_inv_quantity >= 0) {
        this.$set(this.form, 'inventory', global_inv_quantity)
        // this.form.inventory = global_inv_quantity
      }
    }, 100),
    updateSspData: _.debounce(async function(options, arr) {
      if (!arr || !arr.length) return
      await this.bindSspList2mixin(arr, options)
    }, 300),
    handleSSPDrawerConfirm(data) {
      this.$set(this.form, 'specialSellingPriceList', data)
    },
    async getTakeRate() {
      this.isTakeRateLoading = true

      const res = await ajax.post(ADMIN_API.act.get_sku_take_rate_by_time, {
        data: {
          sku_id: this.skuId,
          time_points: [this.destinationToday]
        }
      })

      this.isTakeRateLoading = false
      this.overrideTakeRate = res.override_take_rate
      this.lastTakeRate = res.last_take_rate
    },
    // initForm(calendarSettingData) {
    //   const { fix_price, global_inv_quantity } = calendarSettingData
    //   this.form = {
    //     costPrice: fix_price ? fix_price.cost_price : '',
    //     retailPrice: fix_price ? fix_price.retail_price : '',
    //     sellingPrice: fix_price ? fix_price.selling_price : '',
    //     specialSellingPriceList: _.cloneDeep(
    //       fix_price && fix_price.special_selling_price
    //         ? fix_price.special_selling_price
    //         : []
    //     ),
    //     inventory: global_inv_quantity
    //   }
    //   this.computeTakeRate()
    // },
    validateForm() {
      return this.$refs.form.validate().catch(() => false)
    },
    getData() {
      const data = {
        costCurrency: this.costCurrency,
        sellingCurrency: this.sellingCurrency,
        costPrice: this.form.costPrice,
        retailPrice: this.form.retailPrice,
        sellingPrice: this.form.sellingPrice,
        inventory: this.form.inventory
      }

      if (this.$refs.sspBlockList) {
        data.specialSellingPriceList = this.form.specialSellingPriceList
      }

      return data
    },
    handleRound(field) {
      this.form[field] = round2Decimal(this.form[field])
    },
    handleRoundTakeRate() {
      this.$set(this.form, 'takeRate', provingTakeRate(this.form.takeRate))
    },
    handleCostPriceBlur() {
      const res = setCostAndGetBase(
        this.form.costPrice,
        this.form.sellingPrice,
        this.form.takeRate,
        this.exchangeRate
      )

      this.$set(this.form, 'costPrice', res.cost)
      this.$set(this.form, 'sellingPrice', res.base)
      this.$set(this.form, 'takeRate', res.take_rate)
    },
    handleTakeRateBlur() {
      const res = setTRAndGetBase(
        this.form.costPrice,
        this.form.sellingPrice,
        this.form.takeRate,
        this.exchangeRate
      )

      this.$set(this.form, 'sellingPrice', res.base)
      this.$set(this.form, 'takeRate', res.take_rate)
    },
    computeTakeRate() {
      const takeRate = computeTR(this.form.costPrice, this.form.sellingPrice, this.exchangeRate)
      this.$set(this.form, 'takeRate', takeRate)
    },
    handleRound2Integer(field) {
      this.form[field] = parseInt(this.form[field] || 0)
      this.$refs.inventory && this.$refs.inventory.onFieldBlur()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../mixins.scss';

.fixed-price-form {
  .flex-wrap {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
  @include ticket-status-suffix;
}
.ssp-block-lis-box {
  margin: 15px 0;
}

.exist-input-suffix ::v-deep .ant-input-suffix {
  right: 20px;
}
</style>
