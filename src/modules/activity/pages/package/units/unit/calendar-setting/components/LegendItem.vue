<template>
  <div class="legend-item" :style="{ backgroundColor: itemBackgroundColor }">
    <div class="legend-item-content" @click="handleClick">
      <a-checkbox
        v-if="checkboxDirection === 'start'"
        class="legend-item-checkbox legend-item-start"
        :checked="value"
        @change="handleCheck"
      />
      <svg-icon class="legend-item-icon" :icon-name="icon" />
      <span class="legend-item-text" :style="{ color: labelColor }">
        {{ label }}
      </span>
      <template v-if="checkboxDirection === 'end'">
        <span class="legend-item-divider" />
        <a-checkbox class="legend-item-checkbox" :checked="value" @change="handleCheck" />
      </template>
    </div>
    <slot name="extra"></slot>
  </div>
</template>

<script>
export default {
  name: 'LegendItem',
  props: {
    icon: {
      type: String,
      required: true
    },
    label: {
      type: String,
      required: true
    },
    labelColor: {
      type: String,
      default: '#000'
    },
    value: {
      type: Boolean,
      default: false
    },
    checkboxDirection: {
      type: String,
      default: 'end'
    },
    itemBackgroundColor: {
      type: String,
      default: 'rgba(0, 0, 0, 0.04)'
    }
  },
  methods: {
    handleCheck(e) {
      this.$emit('input', e.target.checked)
    },
    handleClick() {
      this.$emit('input', !this.value)
      this.GTMHookItem('Price&Inventory|CalendarFieldsVisibility_Click')
      // 埋点
      this.$emit('sendSpm')
    }
  }
}
</script>

<style lang="scss" scoped>
.legend-item {
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: flex-start;

  cursor: pointer;
  border-radius: 2px;

  margin: 0 12px 12px 0;

  &-start {
    margin-right: 8px;
  }

  &-icon {
    font-size: 14px;
    margin-right: 4px;
  }

  &-text {
    font-size: 12px;
    line-height: 16px;
  }

  &-divider {
    margin: 0 4px;
    display: inline-block;
    width: 1px;
    height: 16px;
    background-color: rgba(0, 0, 0, 0.1);
  }

  &-checkbox {
    font-size: 12px;
  }
}
</style>
