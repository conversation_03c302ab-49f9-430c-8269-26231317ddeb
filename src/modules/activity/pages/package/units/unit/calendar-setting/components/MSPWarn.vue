<template>
  <div class="msp-warn-container">
    <p>{{ alertMessage }}</p>
    <time-zone
      v-if="[1, 2].includes(alert_type)"
      v-model="alert_timezone"
      :full-timezone="false"
      style="margin-bottom: 12px;"
      class="time-zone"
    ></time-zone>
    <div v-if="tableData.length && !noNeed">
      <a-table
        :data-source="tableData"
        :pagination="pagination"
        :key="alert_timezone"
        max-height="400"
        @change="handleTableChange"
      >
        <a-table-column :title="$t('44921')" v-if="[1, 2].includes(alert_type)" width="170px">
          <template slot-scope="scope">
            {{ dateConvertTimezone(alert_timezone, scope.start_time) }}
          </template>
        </a-table-column>
        <a-table-column
          :title="$t('44931')"
          v-if="[4, 5].includes(alert_type)"
          width="170px"
          data-index="start_time"
          key="start_time"
        >
        </a-table-column>
        <a-table-column
          prop="price_type"
          :title="$t('package_price_amount_type')"
          v-if="[2, 5].includes(alert_type)"
          width="120px"
        >
          <template slot-scope="scope">
            {{ price_map[scope.price_type] }}
          </template>
        </a-table-column>
        <a-table-column
          :title="$t('taxonomy_dialog_currency')"
          v-if="[2, 5].includes(alert_type)"
          data-index="currency"
          key="currency"
        >
        </a-table-column>
        <a-table-column :title="$t('44922')">
          <template slot-scope="scope">
            <span v-if="[1, 4].includes(alert_type)">{{ scope.currency }}</span>
            <span>{{ scope.amount }}</span>
          </template>
        </a-table-column>
        <a-table-column :title="$t('44923')" width="170px" data-index="created_time" key="created_time">
          <template slot-scope="scope">{{
            dateConvertTimezone(alert_timezone, scope.created_time)
          }}</template>
        </a-table-column>
        <a-table-column :title="$t('44924')" data-index="created_by" key="created_by"> </a-table-column>
      </a-table>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import TimeZone from '../../../../../../components/UtcDatetime/time-zone'

export default {
  name: 'mspWarn',
  components: {
    TimeZone
  },
  props: {
    alert_type: {
      type: Number,
      default: 1
    },
    total: {
      type: Number,
      default: 0
    },
    price_model: Number,
    tableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      alert_timezone: 8,

      pagination: {
        pageSizeOptions: ['10', '20', '50'],
        showSizeChanger: true,
        total: 0,
        current: 1,
        pageSize: 10
      },

      price_map: {
        1: __('package_price_cost'),
        3: __('45734'),
        4: __('package_special_selling_price')
      }
    }
  },
  computed: {
    noNeed() {
      return this.alert_type === 3 && this.price_model === 1
    },
    alertMessage() {
      return {
        1: this.$t('44920'),
        2: this.$t('44927'),
        3: this.$t('44930'),
        4: this.$t('44920'),
        5: this.$t('44927')
      }[this.alert_type]
    }
  },
  watch: {
    total: {
      immediate: true,
      handler() {
        this.pagination.total = this.total
      }
    }
  },
  methods: {
    dateConvertTimezone(to = 8, v = new Date(), fmt = 'YYYY-MM-DD HH:mm:ss') {
      return moment(v)
        .utcOffset(to)
        .format(fmt)
    },
    handleTableChange(pagination) {
      this.$set(this, 'pagination', {
        ...pagination,
        current: this.pagination.pageSize === pagination.pageSize ? pagination.current : 1
      })
    }
  }
}
</script>
