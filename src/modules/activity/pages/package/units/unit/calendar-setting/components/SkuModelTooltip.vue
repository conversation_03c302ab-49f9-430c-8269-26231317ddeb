<template>
  <a-tooltip :trigger="['hover']" destroyTooltipOnHide placement="topLeft">
    <div slot="title" style="max-width: 300px;">
      <div>{{ info }}</div>
      <div v-if="showButton" style="display: flex; justify-content: flex-end;">
        <a-button type="link" size="small" @click="handleConfirm">
          {{ $t('27804') }}
        </a-button>
      </div>
    </div>
    <slot />
  </a-tooltip>
</template>

<script>
import { getCalendarButtonText } from '@activity/pages/package/units/unit-list/calendar-setting/utils'

export default {
  name: 'SkuModelTooltip',
  inject: ['calendar', 'unitBar'],
  props: {
    type: {
      type: String,
      default: 'price'
    }
  },
  data() {
    return {
      info: __('27805'),
      showButton: false
    }
  },
  created() {
    const unitData = this.unitBar.getUnitData()
    const isSubSku = unitData && unitData.share_rel_ship === 1

    // 库存并且子sku的时候显示27805，其它时候显示27806
    if (this.type === 'price' || !isSubSku) {
      const text = getCalendarButtonText(unitData)
      this.info = __('27806').replace('{Total Inventory & Fixed Price Setting}', text)
      this.showButton = true
    }
  },
  methods: {
    handleConfirm() {
      this.calendar.openCalendarSetting()
      this.$emit('confirm')
    }
  }
}
</script>

<style lang="scss" scoped></style>
