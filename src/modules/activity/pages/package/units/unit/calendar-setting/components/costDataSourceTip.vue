<template>
  <div class="cost-data-source-tip" v-if="showSwitch">
    <slot name="body">
      <span
        v-for="(item, index) of ticketStatusGuideList"
        style="margin-right: 16px; font-size: 12px;"
        :key="index"
        class="label-box"
      >
        <span class="ticket-status-circle" :class="item.class"></span>{{ item.label }}
      </span>
    </slot>
  </div>
</template>

<script>
import maps from '@activity/utils/maps.js'
import { mapState } from 'vuex'

export default {
  name: 'costDataSourceTip',
  props: {
    unitData: {
      type: Object,
      required: true
    }
  },
  computed: {
    ...mapState(['isMerchant']),
    calcApproveStatus() {
      return this.unitData?.ticket_status
    },
    showSwitch() {
      return ['ToBeSubmitted', 'PendingApproval'].includes(this.calcApproveStatus) && this.isMerchant
    },
    ticketStatusGuideList() {
      return maps.ticket_status.filter((item) => this.calcApproveStatus === item.value)
    }
  }
}
</script>

<style lang="scss">
@import '../mixins.scss';

.cost-data-source-tip {
  margin-top: 0;

  @include ticket-status-circle;
}
</style>
