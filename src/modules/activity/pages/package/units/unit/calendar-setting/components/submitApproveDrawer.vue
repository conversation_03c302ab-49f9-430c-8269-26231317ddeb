<!-- original: klk-admin msp  -->
<template>
  <a-drawer
    class="submit-approve-drawer-container"
    title="Submit Cost"
    :width="600"
    :visible="visible"
    v-if="visible"
    :headerStyle="drawerHeaderStyle"
    :bodyStyle="drawerBodyStyle"
    placement="right"
    @close="handleClose"
  >
    <a-spin :spinning="loading">
      <a-collapse v-model="activeKey">
        <a-collapse-panel v-for="pkg in list" :key="'' + pkg.package_id" :header="pkg.package_name">
          <div class="sku-item" v-for="(sku, index) in pkg.sku_list" :key="sku.sku_id">
            <a-form-model label-width="0" ref="form" :model="sku">
              <a-checkbox v-model="sku.checked">{{ sku.sku_name }}</a-checkbox>

              <div class="sku-item-note-content">
                <div class="__left">
                  <a-form-model-item
                    prop="select"
                    :rules="{ required: sku.checked, message: 'Please select note' }"
                  >
                    <a-select
                      v-model="sku.select"
                      style="width: 356px;"
                      placeholder="Please select"
                      @change="() => changeNoteSelect(index)"
                    >
                      <a-select-option v-for="note in noteDict" :key="note.value" :value="note.value">
                        {{ note.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>

                  <a-form-model-item
                    prop="note"
                    :rules="{ required: sku.select === 4 && sku.checked, message: 'Please input note' }"
                  >
                    <a-textarea
                      v-model="sku.note"
                      v-if="sku.select === 4"
                      style="margin-top: 8px;"
                      placeholder="Please input note"
                    ></a-textarea>
                  </a-form-model-item>
                </div>

                <a-button class="__right" type="link" @click="handleViewDetail(sku)">View detail</a-button>
              </div>
            </a-form-model>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </a-spin>
    <div class="footer">
      <a-button @click="handleClose">{{ $t('44916') }}</a-button>
      <a-button type="primary" @click="handleSubmit">{{ $t('44915') }}</a-button>
    </div>
  </a-drawer>
</template>

<script>
const noteDict = [
  { label: 'Seasonality', value: 1 },
  { label: 'Promotion', value: 2 },
  { label: 'Change in operational cost/ strategy', value: 3 },
  { label: 'Other', value: 4 }
]

export default {
  name: 'submitApproveDrawer',
  components: {},
  inject: {
    refreshAllLinkedData: {
      default: undefined
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    activityId: {
      type: Number,
      default: undefined
    },
    packageId: {
      type: Number,
      default: undefined
    },
    onlyThisUnit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      noteDict,

      drawerHeaderStyle: {
        borderBottom: 'none',
        paddingLeft: '16px',
        paddingRight: '16px'
      },
      drawerBodyStyle: {
        padding: '60px 16px'
      },

      loading: false,

      list: [],
      activeKey: []
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(v) {
        v && this.initData()
      }
    }
  },
  computed: {
    activity_id() {
      return this.activityId || +this.$route.params.id
    },
    package_id() {
      return this.packageId || +this.$route.query.package_id
    },
    sku_id() {
      return this.$attrs.sku_id
    }
  },
  methods: {
    changeNoteSelect(index) {
      this.$nextTick(() => {
        this.$refs.form[index]?.validate?.()
      })
    },
    handleClose() {
      this.$emit('update:visible', false)
    },
    async handleSubmit() {
      this.loading = true
      const tem = this.list.reduce(
        (acc, curr) => {
          const { sku_list, package_id } = curr

          sku_list.forEach((sku) => {
            const { checked, select, note, sku_id } = sku

            if (checked) {
              if (((select === 4 && note) || select !== 4) && select !== undefined) {
                let temp = _.find(this.noteDict, { value: select })

                acc.data.push({
                  sku_id,
                  note: select === 4 ? note : temp.label
                })
              } else {
                acc.invalid.push(package_id)
              }
            }
          })

          return acc
        },
        {
          data: [],
          invalid: []
        }
      )

      const { data, invalid } = tem

      if (invalid.length) {
        this.loading = false
        this.$set(this, 'activeKey', [...new Set([...invalid, ...this.activeKey])])
        this.$message.warn(this.$t('package_error_mandatory'))

        this.$refs.form.map((form) => {
          form.validate()
        })

        await this.$nextTick()

        const target = document.querySelector('.submit-approve-drawer-container')
        klook.scrollElError(target, { block: 'start' })

        return
      }

      const res = await ajax.postBody(ADMIN_API.act.submit_cost_multi, {
        data: {
          sku_list: data
        }
      })

      this.loading = false
      this.$message.success(this.$t('global_success'))
      this.$emit('submitCompleted', res)
      this.handleClose()
      this.refreshAllLinkedData?.()
    },
    async initData() {
      this.loading = true

      let activeKey = []
      this.$set(this, 'activeKey', [])
      const { package_list } = await ajax.get(ADMIN_API.act.get_ticket_by_activity, {
        params: {
          activity_id: this.activity_id
        }
      })

      this.list = package_list.map((pkg) => {
        const includesPkg = pkg.package_id === this.package_id
        if (includesPkg) {
          activeKey.push(pkg.package_id)
        }

        return {
          ...pkg,
          sku_list: pkg.sku_list.map((sku) => {
            const includesSku = this.sku_id === sku.sku_id

            if (includesSku) {
              activeKey.push(pkg.package_id)
            }

            return {
              ...sku,
              select: undefined,
              checked: includesPkg || includesSku
            }
          })
        }
      })

      this.$set(this, 'activeKey', [...new Set(activeKey)])

      this.loading = false
    },
    handleViewDetail(data) {
      this.$router.push({
        name: 'ticketDetail',
        params: {
          ticket_id: data.ticket_id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.submit-approve-drawer-container {
  ::v-deep {
    .ant-collapse {
      border: none;
    }

    .ant-collapse-header {
      background-color: #fff;
    }

    .ant-collapse-content,
    .ant-collapse-item {
      border: none;
    }
  }

  .sku-item {
    padding: 8px 12px;
    margin-bottom: 10px;
    background-color: #fafafa;

    &-note-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-top: 16px;
    }
  }
  .footer {
    position: absolute;
    width: 100%;
    left: 0;
    bottom: 0;
    padding: 10px 22px;
    border: 1px solid #f0f0f0;
    text-align: right;

    ::v-deep .ant-btn {
      margin-left: 12px;
    }
  }
}
</style>
