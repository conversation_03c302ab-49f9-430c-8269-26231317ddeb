<template>
  <a-modal :visible.sync="_visible" @cancel="handleCancel">
    <span slot="footer" style="text-align: center">
      <a-button @click="handleCancel">{{ $t('global_cancel') }}</a-button>
    </span>
  </a-modal>
</template>

<script>
export default {
  name: 'submitAuditDialog',
  props: {
    visible: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {}
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    handleCancel() {
      this._visible = false
    }
  }
}
</script>

<style scoped lang="scss"></style>
