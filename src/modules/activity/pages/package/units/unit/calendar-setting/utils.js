/**
 * 日历相关
 * Date
 */
export const range = function(n) {
  // see https://stackoverflow.com/questions/3746725/create-a-javascript-array-containing-1-n
  return Array.apply(null, { length: n }).map((_, n) => n)
}

export const getFirstDayOfMonth = function(date) {
  const temp = new Date(date.getTime())
  temp.setDate(1)
  return temp.getDay()
}

export const getPrevMonthLastDays = (date, amount) => {
  if (amount <= 0) return []
  const temp = new Date(date.getTime())
  temp.setDate(0)
  const lastDay = temp.getDate()
  return range(amount).map((_, index) => lastDay - (amount - index - 1))
}

export const getMonthDays = (date) => {
  const temp = new Date(date.getFullYear(), date.getMonth() + 1, 0)
  const days = temp.getDate()
  return range(days).map((_, index) => index + 1)
}

export const validateRangeInOneMonth = function(start, end) {
  return start.getMonth() === end.getMonth() && start.getFullYear() === end.getFullYear()
}

/**
 * 日期相关
 * date
 */
export function getDateFromStartTime(startTime) {
  if (!startTime) return ''
  const timeArr = startTime.split(' ')
  return timeArr[0] ? timeArr[0] : ''
}

export function getTimeslotFromStartTime(startTime) {
  if (!startTime) return ''
  const timeArr = startTime.split(' ')
  return timeArr[1] ? timeArr[1].substring(0, 5) : '00:00'
}

/**
 * 价格相关
 * price
 */
export function isPriceDifferent(data, originalData) {
  // 比较价格是否修改，包括ssp
  if ((data && !originalData) || (!data && originalData)) {
    return true
  }

  if (data && originalData) {
    if (Number(data.cost_price) !== Number(originalData.cost_price)) {
      return true
    }

    if (Number(data.retail_price) !== Number(originalData.retail_price)) {
      return true
    }

    if (Number(data.selling_price) !== Number(originalData.selling_price)) {
      return true
    }

    if (!_.isEqual(data.special_selling_price || [], originalData.special_selling_price || [])) {
      return true
    }
  }

  return false
}

/**
 * 文案相关
 * text
 * price_model: 0是固定价格，1是日历价格
 * inv_model：1是日历库存，2是无限库存，3是全局库存
 * share_rel_ship：0是无关联，1是子sku，2是主sku
 * 27784： Total inventory & fixed price settings
 * 27786： Fixed price settings
 * 27785： Total inventory settings
 */
export function getCalendarButtonText(data) {
  const { price_model, inv_model } = data

  // 固定价格 + 全局库存
  if (price_model === 0 && inv_model === 3) {
    return __('27784')
  }

  // 固定价格
  if (price_model === 0) {
    return __('27786')
  }

  // 全局库存
  if (inv_model === 3) {
    return __('27785')
  }

  return ''
}
