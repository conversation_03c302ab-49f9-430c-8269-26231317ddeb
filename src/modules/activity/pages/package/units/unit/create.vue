<template>
  <div class="unit-create-box js-unit-create-box">
    <svg-icon class="not-found-icon" icon-name="addon-notdata" />
    <div class="text">{{ $t('82671') }}</div>
    <a-button v-if="options.length" class="button" type="primary" @click="handleCopy">
      {{ $t('80910') }}
    </a-button>
    <a-button
      class="button"
      type="link"
      v-bind="$root.bindIHTrack({ type: 'module', spm: 'Edit_Schedule' })"
      @click="handleEdit"
    >
      {{ BULK_MANAGE_SCHEDULE_KEYS.add.label }}
    </a-button>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { action as pkgsAction } from '@activity/store/pkgs-cache-observable.js'
import moment from 'moment'
import { BULK_MANAGE_SCHEDULE_KEYS } from '@activity/pages/package/units/utils/index.js'

export default {
  inject: ['activityId'],
  props: {
    skuId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    this.BULK_MANAGE_SCHEDULE_KEYS = BULK_MANAGE_SCHEDULE_KEYS

    return {
      options: []
    }
  },
  computed: {
    ...mapState({
      ruleSetting: (state) => state.skuRuleSetting
    }),
    calcFinished() {
      return !this.ruleSetting?.unfinished
    },
    packageId() {
      return this.$route.query.package_id || ''
    }
  },
  async created() {
    await this.getSkus()
    if (this.calcFinished) {
      await this.getCalendarSettingData()
    }
    // 新建sku自动弹出add schedule 弹窗
    this.handleCreated()
  },
  methods: {
    handleCreated() {
      const newSku = window.sessionStorage.getItem('created_new_sku') || ''
      if (newSku && newSku == this.skuId) {
        this.handleEdit()
      }
      window.sessionStorage.setItem('created_new_sku', '')
    },
    handleEdit() {
      this.$emit('bulkEdit', { type: BULK_MANAGE_SCHEDULE_KEYS.add.key, isCreate: true })
    },
    handleCopy() {
      this.$emit('copy', this.options)
    },
    async getSkus() {
      const res =
        (await ajax.get(
          ADMIN_API.act.get_sku_simple_by_activity_id,
          {
            params: {
              activity_id: Number(this.activityId),
              language: klook.getEditLang()
            }
          },
          {
            loading: true
          }
        )) || {}
      const list = res.list || []
      const skuId = this.skuId
      this.options = list.reduce((acc, curr) => {
        const { package_id, package_name, unit_name, sku_id } = curr
        if (skuId === sku_id) {
          return acc
        }
        const target = acc.find((item) => item.value === package_id)
        if (target) {
          target.children.push({
            value: sku_id,
            label: `${sku_id}-${unit_name}`
          })
        } else {
          const packageId = this.packageId
          const lb =
            packageId == package_id
              ? `${package_id}- (Current) ${package_name}`
              : `${package_id}-${package_name}`
          acc.push({
            value: package_id,
            label: lb,
            children: [
              {
                value: sku_id,
                label: `${sku_id}-${unit_name}`
              }
            ]
          })
        }
        return acc
      }, [])
    },
    async getCalendarSettingData() {
      const apiPath = this.isBdAudit
        ? ADMIN_API.act.get_calendar_by_sku_id_audit
        : ADMIN_API.act.get_calendar_by_sku_id

      const start_time = moment().format('YYYY-MM-DD')
      const end_time = moment()
        .add(1, 'months')
        .format('YYYY-MM-DD')

      let params = {
        sku_id: this.skuId,
        start_time: `${start_time} 00:00:00`,
        end_time: `${end_time} 00:00:00`
      }

      const res = await ajax.get(apiPath, {
        params
      })

      pkgsAction.setGlobalData(apiPath, res)
      this.$store.commit('setDestinationToday', res.destination_now)
    }
  }
}
</script>

<style lang="scss" scoped>
.unit-create-box {
  padding: 24px;
  min-height: 270px;
  .text {
    padding: 12px 0;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    text-align: center;
  }
  .not-found-icon {
    margin: 0 auto;
    width: 120px;
    height: 92px;
    display: block;
  }
  .button {
    display: block;
    margin: 0 auto 12px auto;
  }
}
</style>
