<template>
  <a-spin :spinning="loading" class="common-spin-block">
    <div v-if="!loading && calcUnitData" class="package-units" :data-spm-page="getSpm">
      <UnitMain
        class="unit-main-style"
        :unit-data="calcUnitData"
        :calendar-date="calendarDate"
        :global-data-state="globalDataState"
        :is-add="addSchedule"
        :show-calender="calcCalendarCompleted(calcUnitData)"
        @edit="handleEdit"
        @bulkEdit="handleBulkEdit"
        @delete="showDelete = true"
        @copy="handleCopy"
      />
      <FloatReportGuide v-if="!$root.isMerchant" />
      <!-- 编辑 -->
      <DeleteSchedule
        :visible="showDelete"
        :sku-id="sku_id"
        :unit-data="calcUnitData"
        @cancel="showDelete = false"
      />
      <!-- <EditSchedule
        :visible="showEdit"
        :global-data-state="globalDataState"
        :unit-data="calcUnitData"
        :is-add="addSchedule"
        @cancel="handleCancel"
        @confirm="scheduleConfirm"
      /> -->
      <CopyUnit
        v-if="createCopy"
        :visible="showCopy"
        :sku-id="sku_id"
        :options="copySkus"
        @cancel="showCopy = false"
        @copySuccess="copySuccess"
        @copy-error="copyError"
      />
      <RuleSettingsDrawer
        :visible.sync="showSettings"
        :edit-type="editType"
        :unit-data="calcUnitData"
        :drawer-conf="settingDrawerConf"
        @cancel="cancelSettings"
      />
    </div>
    <!-- 埋点 -->
    <div id="copy_error_spm" v-bind="$root.bindIHTrack({ type: 'module', spm: 'CopySchedule' })"></div>
  </a-spin>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex'
import { get } from 'lodash'
import UnitMain from '@activity/pages/package/units/unit/main/index.vue'
import { checkAuth } from '@/plugins/authDirective'
import FloatReportGuide from '@activity/components/float-report-guide/index.vue'
import { getEditLockValueByPath } from '@activity/utils'
import { state as pkgsState } from '@activity/store/pkgs-cache-observable.js'

import DeleteSchedule from '@activity/pages/package/units/new-components/delete-schedule'
// import EditSchedule from '@activity/pages/package/units/new-components/edit-schedule'
import CopyUnit from '@activity/pages/package/units/new-components/copy-unit/index.vue'
import { UNIT_STEPS, Max_Schedule_Limit } from '@activity/pages/package/package_const.js'
import RuleSettingsDrawer from '@activity/pages/package/units/new-components/rule-settings-drawer'

import DropdownMenus from '@activity/pages/package/units/components/DropdownMenus.vue'
import {
  initBatchDateManage,
  destroyInstance
} from '@activity/pages/package/units/unit/main/batchDateManage.js'
import { BULK_MANAGE_SCHEDULE_KEYS } from '@activity/pages/package/units/utils/index.js'
import { messageConfirm } from '@activity/pages/package/units/utils/message.js'
import { getWeekdayValueByDayIdx } from '@activity/utils/index'
import { UNSAFE_PRICE_CODE, showUnsavePriceReasonModal } from '@activity/components/UnsavePriceReasonModal'

const bulk_manage_schedule_mixin = {
  components: {
    DropdownMenus
  },
  computed: {
    bulkDateRangeValues() {
      return this.$store.state.bulkManageSchedule.dateRangeValues
    },
    bulkExcludeDateList() {
      return this.$store.state.bulkManageSchedule.excludeDateList
    }
  },
  methods: {
    ...mapMutations(['setBulkManageSchedule']),
    resetBulkManageData(type = '') {
      this.bulkEditType = type
      this.setBulkManageSchedule({
        type,
        dateRange: [],
        dateRangeValues: [],
        excludeDateList: []
      })
    },
    handleBulkEdit({ type, isCreate = false } = {}) {
      const check = this.checkSetting()

      if (!check) {
        this.showRuleSetting({ types: 'new' })
        return
      }

      if (this.instance) {
        this.destroyDateManage(() => {
          this.instance = null
          this.mountDateManage(type, isCreate)
        })
      } else {
        this.mountDateManage(type, isCreate)
      }
    },
    async mountDateManage(type, isCreate) {
      this.resetBulkManageData(type)

      // 获得批量管理浮窗高度
      let ManageHeight
      if (BULK_MANAGE_SCHEDULE_KEYS.add.key === type && isCreate) {
        ManageHeight =
          document.documentElement.scrollTop +
          document.querySelector('.js-unit-main-content-box').getBoundingClientRect().top -
          20
      } else {
        ManageHeight = 216
      }

      // 收起导航球
      klook.bus.$emit('switchGuideActiveState', false)

      setTimeout(() => {
        // 将日历滚动到视觉区
        const elm = document.querySelector('.js-calendar-setting')
        const top = elm?.getBoundingClientRect()?.top ?? 0
        document.documentElement.scrollTop += top
      })

      // 突出日历表
      await this.$nextTick()
      const $unitMainBox = document.querySelector('.js-unit-main-box')
      $unitMainBox.classList.add('fixed-to-view')
      const $calendarTable = $unitMainBox.querySelector('.calendar-table')
      if ($calendarTable) {
        this.preCssText = $calendarTable?.style?.cssText
        const $calendarHeaderHeight = document.querySelector('.calendar-header').clientHeight
        const height = document.body.clientHeight - ManageHeight - $calendarHeaderHeight - 48 // padding 20 * 2 - 8 pixel
        $calendarTable.style.cssText = `${this.preCssText}height: ${height}px;overflow: hidden auto;`
      }

      // 禁用滚动条, 采用日历表滚动
      this.preOverflowStyle = document.body.style.overflow
      document.body.style.overflow = 'hidden'

      const { left } = document.querySelector('.package-units').getBoundingClientRect()
      const right = 20
      const bottom = 20

      const { calcUnitData: unitData, globalDataState } = this
      this.instance = initBatchDateManage({
        propsData: {
          unitData,
          globalDataState,
          isAdd: false,
          maskPosition: {
            left,
            right,
            top: ManageHeight,
            bottom
          },
          contentStyle: {
            left: left + 'px',
            maxHeight: ManageHeight + 'px',
            width: `calc(100vw - ${left}px - ${right}px)`
          },
          onCancel: this.onCancel,
          onConfirm: this.onConfirm
        }
      })
    },
    destroyDateManage(destroyCb = null) {
      // body
      document.body.style.overflow = this.preOverflowStyle
      // unit main
      const $unitMainBox = document.querySelector('.js-unit-main-box')
      if ($unitMainBox) {
        $unitMainBox.classList.remove('fixed-to-view')
        // table
        const $calendarTable = $unitMainBox.querySelector('.calendar-table')
        if ($calendarTable) {
          $calendarTable.style.cssText = this.preCssText
        }
      }

      klook.bus.$emit('switchGuideActiveState', true)
      this.resetBulkManageData()

      destroyInstance(destroyCb)
    },
    onCancel() {
      this.destroyDateManage()
    },
    getBulkPostData(data) {
      const isAddType = BULK_MANAGE_SCHEDULE_KEYS.add.key === this.bulkEditType
      const { weekdays, isByHours, ...commonData } = data
      const weekdaysObj = weekdays.reduce(
        (acc, curr) => ({
          ...acc,
          [curr.value]: curr
        }),
        {}
      )

      const xorList = _.xor(this.bulkDateRangeValues, this.bulkExcludeDateList)
      const schedules = xorList.reduce((acc, currDate) => {
        const currWeekdayVal = getWeekdayValueByDayIdx(moment(currDate).day())
        let { checked = false, timeslots = [] } = weekdaysObj[currWeekdayVal] || {}
        if (checked) {
          // by day -> ['00:00']
          timeslots = isByHours ? timeslots : ['00:00']
          const schedules = timeslots.map((tiemslot) => {
            if (isAddType) {
              return {
                ...commonData,
                start_time: `${currDate} ${tiemslot}:00`
              }
            }

            return `${currDate} ${tiemslot}:00`
          })

          acc.push(...schedules)
        }

        return acc
      }, [])

      let postData
      if (isAddType) {
        postData = {
          sku_list: [
            {
              sku_id: this.calcUnitData.sku_id,
              schedules
            }
          ]
        }
      } else {
        postData = {
          sku_id: this.calcUnitData.sku_id,
          start_time_list: schedules
        }
      }

      return postData
    },
    checkSchedulesLength(postData = {}) {
      const sku_list = postData?.sku_list ?? []
      const isValid = sku_list.some((item) => {
        const schedules = item.schedules || []
        return schedules.length > Max_Schedule_Limit
      })
      return isValid
    },
    resetUnsavePriceReason() {
      this.unsavePriceReason = {
        unsafe_price_reason_code: undefined,
        unsafe_price_reason_note: undefined
      }
    },
    async confirmAddSchedules(data) {
      const { share_rel_ship } = this.calcUnitData || {}
      if (share_rel_ship === 2) {
        const result = await messageConfirm(this, this.$t('27807'))

        if (!result) {
          return
        }
      }

      const postData = this.getBulkPostData(data)

      // 如果存在不合格价格理由，将其添加到请求中
      if (this.unsavePriceReason?.unsafe_price_reason_note) {
        postData.unsafe_price_reason_note = this.unsavePriceReason.unsafe_price_reason_note
        postData.unsafe_price_reason_code = this.unsavePriceReason.unsafe_price_reason_code
      }

      if (this.checkSchedulesLength(postData)) {
        this.$message.error(this.$t('101042'))
        return
      }
      const res = await ajax.postBody(
        ADMIN_API.act.create_or_update_multi_schedule,
        { data: postData, noDefaultResponseInterceptor: true },
        { loading: true, msgOpt: { isErrMsg: false } }
      )

      if (res.error?.code === UNSAFE_PRICE_CODE) {
        // 如果价格校验不通过，唤起 UnSavePriceReasonModal
        const { confirm, cancel } = await showUnsavePriceReasonModal({
          warningMessage: res.error.message
        })
        if (!cancel) {
          const { reasonCode, reasonNote } = confirm
          this.unsavePriceReason = {
            // 清空之前的理由
            unsafe_price_reason_code: reasonCode,
            unsafe_price_reason_note: reasonNote
          }
          await this.confirmAddSchedules(data)
          return
        }
      }

      res?.success
        ? this.scheduleConfirm()
        : this.$message.error(res?.error?.message || this.$t('global_error'))

      this.resetUnsavePriceReason()
      return true
    },
    async confirmModifySchedules(data) {
      let postData = this.getBulkPostData(data)
      if (this.checkSchedulesLength(postData)) {
        this.$message.error(this.$t('101042'))
        return
      }
      let urlKey
      if (this.bulkEditType === BULK_MANAGE_SCHEDULE_KEYS.delete.key) {
        urlKey = 'destroy_multi_schedule_by_sku_id'
      } else {
        urlKey = 'update_multi_schedule_publish_status_by_sku_id'
        postData.published = this.bulkEditType === BULK_MANAGE_SCHEDULE_KEYS.activate.key
      }

      const res = await ajax.postBody(ADMIN_API.act[urlKey], { data: postData }, { loading: true })

      res && this.scheduleConfirm()
      return true
    },
    async onConfirm({ data }) {
      let resp
      if (this.bulkEditType === BULK_MANAGE_SCHEDULE_KEYS.add.key) {
        resp = await this.confirmAddSchedules(data)
      } else {
        resp = await this.confirmModifySchedules(data)
      }

      if (resp) {
        this.$message.success(this.$t('global_success'))
        this.destroyDateManage()
      }
    }
  }
}

export default {
  inject: ['reloadPage2provide'],
  name: 'PackageUnits',
  components: {
    UnitMain,
    FloatReportGuide,
    DeleteSchedule,
    // EditSchedule,
    CopyUnit,
    RuleSettingsDrawer
  },
  mixins: [bulk_manage_schedule_mixin],
  provide() {
    return {
      activityId: this.activity_id,
      packageId: this.package_id,
      skuId: this.sku_id,
      unitBar: this,
      getEditLockValueByPath: (args) => {
        return getEditLockValueByPath({
          data: _.get(this, 'packageFresherSettingData', {}),
          ...args
        })
      }
    }
  },
  props: {
    activity_id: {
      type: [Number, String],
      required: true
    },
    package_id: {
      type: [Number, String],
      required: true
    },
    sku_id: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      calendarDate: new Date(), // 每个sku要有自己的 calendarDate
      pkgsSkuListCacheState: pkgsState[ADMIN_API.act.query_package_sku_list],
      globalDataState: pkgsState.globalData,
      loading: true,
      showDelete: false,
      showEdit: false,
      showCopy: false,
      createCopy: false,
      showSettings: false,
      editType: '',
      copySkus: [],
      addSchedule: false,
      settingDrawerConf: {},

      unsavePriceReason: {
        unsafe_price_reason_code: undefined,
        unsafe_price_reason_note: undefined
      }
    }
  },
  computed: {
    ...mapState({
      pkgStepStatusMap: (state) => state.pkgStepStatusMap,
      isBdAudit: (state) => state.isBdAudit,
      isMerchant: (state) => state.isMerchant,
      isPkgApproved: (state) => state.isPkgApproved,
      isPkgPendingApproval: (state) => state.isPkgPendingApproval,
      packageFresherSettingData: 'packageFresherSettingData',
      skuRuleSetting: 'skuRuleSetting'
    }),
    ...mapGetters(['skuModelFinished']),
    calcCalendarCompleted() {
      return (unitItem) => {
        const { step, share_rel_ship } = unitItem || {}
        const isSubSku = share_rel_ship === 1
        return step?.includes(UNIT_STEPS[1]) || isSubSku
      }
    },
    calcUnitData() {
      const { pkgsSkuListCacheState, package_id, sku_id } = this
      const units = get(pkgsSkuListCacheState, `${package_id}.getQueryPackageSkuList.result.units`, [])
      const unitData = units.find((o) => o.sku_id === sku_id)
      return unitData
    },
    canEdit() {
      // 普通权限，在所有端都需要用的权限
      const { nonEdit2act, nonEdit2status } = this.$root
      return !nonEdit2act && !nonEdit2status
    },
    inventoryCanEdit() {
      // 特殊权限，仅在商户端、approved、有editInventory或edit的时候开放
      const { isMerchant, isPkgApproved } = this
      return isMerchant && isPkgApproved && (checkAuth('editInventory') || !this.$root.nonEdit2status)
    },
    auditCanEdit() {
      // 特殊权限，仅审核端、pendingApproval的时候开放
      const { isBdAudit, isPkgPendingApproval } = this
      return isBdAudit && isPkgPendingApproval && !this.$root.nonEdit2status
    },
    getSpm() {
      const oid = `activity_${this.activity_id || 0}`
      return `PriceInventory?oid=${oid}`
    },
    isTotalInv() {
      const inv_model = this.skuRuleSetting?.inv_model ?? ''
      return inv_model === 3
    },
    isFixedPrice() {
      const price_model = this.skuRuleSetting?.price_model ?? ''
      return price_model === 0
    }
  },
  watch: {
    sku_id: {
      handler() {
        this.refreshView()
      }
    },
    canEdit: {
      immediate: true,
      handler(val) {
        this.$store.commit('setCanEdit', val)
      }
    },
    inventoryCanEdit: {
      immediate: true,
      handler(val) {
        this.$store.commit('setInventoryCanEdit', val)
      }
    },
    auditCanEdit: {
      immediate: true,
      handler(val) {
        this.$store.commit('setAuditCanEdit', val)
      }
    }
  },
  async mounted() {
    this.initPage()

    klook.bus.$off('showRuleSetting').$on('showRuleSetting', this.showRuleSetting)
    klook.bus.$off('saveSkuStep').$on('saveSkuStep', this.saveSkuStep)
    klook.bus.$off('savePkgStep').$on('savePkgStep', this.savePkgStep)
  },
  beforeDestroy() {
    this.$store.commit('setSkuModel', null)
    klook.bus.$off('showRuleSetting').$off('savePkgStep').$off('saveSkuStep')
  },
  methods: {
    checkSetting(types = 'add') {
      const { isTotalInv, isFixedPrice, skuRuleSetting } = this
      const { global_inv_quantity, sku_fix_price } = skuRuleSetting || {}

      const checkPrice = isFixedPrice && !sku_fix_price
      const checkInv = isTotalInv && global_inv_quantity === -1
      if (checkPrice) {
        return false
      }
      if (checkInv && types === 'add') {
        return false
      }
      return true
    },
    handleCopy(options = []) {
      const check = this.checkSetting('copy')
      if (!check) {
        this.showRuleSetting({ types: 'copy' })
        return
      }
      this.copySkus = options
      this.showCopy = true
      this.createCopy = true
    },
    handleEdit(isAdd = false) {
      const check = this.checkSetting()
      if (!check) {
        this.showRuleSetting({ types: 'new' })
        return
      }
      this.showEdit = true
      this.addSchedule = isAdd
    },
    handleCancel() {
      this.showEdit = false
      this.addSchedule = false
    },
    cancelSettings() {
      this.showSettings = false
      this.editType = ''
    },
    showRuleSetting({ types = '', drawerConf = {} } = {}) {
      this.showSettings = true
      this.settingDrawerConf = drawerConf
      this.editType = types || ''
    },
    async scheduleConfirm() {
      this.showEdit = false
      await Promise.all([this.saveSkuStep('sku_calendar', true), this.savePkgStep('price_inventory', true)])
      if (!this.calcCalendarCompleted(this.calcUnitData)) {
        klook.bus.$emit('getPackageSkuDataBus')
        return
      }
      this.reloadPage2provide()
    },
    async copySuccess() {
      this.showCopy = false
      await this.saveSkuStep('sku_calendar', true)
      klook.bus.$emit('getPackageSkuDataBus')
    },
    copyError(message) {
      this.$root.trackIHEvent('#copy_error_spm', { err_message: message })
    },
    setCalendarDate(date) {
      this.calendarDate = date
    },
    refreshView() {
      this.loading = true
      this.$nextTick(() => {
        this.loading = false
      })
    },
    async initPage() {
      this.loading = true
      await Promise.all([
        this.$store.dispatch('actionGetSkuModel', {
          packageId: this.package_id
        }),
        this.$store.dispatch('getActCategory2action', {
          activity_id: this.activity_id
        })
      ])
      this.loading = false
    },
    getUnitData() {
      return this.calcUnitData
    },
    async savePkgStep(step) {
      let res = null

      const { price_inventory } = this.pkgStepStatusMap[this.packageId] || {}

      // 注意：这里的res不是后端返回的结构体
      if (!price_inventory) {
        res =
          (await this.$store.dispatch('updatePkgStepStatus2action', {
            step,
            activity_id: this.activity_id,
            package_id: this.package_id,
            language: 'all',
            status: 1
          })) || {}

        klook.bus.$emit('updatePkgInfos2bus', this.package_id)
        klook.bus.$emit('getPackageSkuDataBus', this.package_id)
      }

      return res
    },
    async saveSkuStep(step, refresh = false) {
      let res = null
      const currentStep = this.calcUnitData.step
      if (refresh || !currentStep || !currentStep.includes(step)) {
        res =
          (await ajax.postBody(ADMIN_API.act.save_sku_step_status, {
            data: {
              step,
              activity_id: this.activity_id,
              sku_id: this.sku_id,
              language: 'all',
              status: 1
            }
          })) || {}

        klook.bus.$emit('refreshGuideStatus')
      }
      return res
    }
  }
}
</script>

<style lang="scss" scoped>
.unit-main-style {
  margin: 4px 0 20px 0;
}
</style>
