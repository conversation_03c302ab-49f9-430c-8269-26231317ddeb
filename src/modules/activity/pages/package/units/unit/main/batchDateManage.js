import Vue from 'vue'
import BulkManageSchedule from '@activity/pages/package/units/components/bulk-manage-schedule/index.vue'

import Vuex from 'vuex'
import storeConf from '@activity/store/index.js'
Vue.use(Vuex)
const store = new Vuex.Store(storeConf)

let instance

const getInstance = (data = {}) => {
  if (instance) {
    return instance
  }

  const componentClass = Vue.extend(BulkManageSchedule)
  instance = new componentClass({
    store,
    ...data
  }).$mount()
  document.body.appendChild(instance.$el)

  return instance
}

export const initBatchDateManage = (data = {}) => {
  const instance = getInstance(data)

  return instance
}

export const destroyInstance = (callback) => {
  instance && instance.$el.remove()
  instance = null
  callback?.()
}
