<template>
  <div v-if="calcShowBox" class="unit-header-box">
    <div class="flex-box">
      <div class="flex-box__left-box">
        <p v-if="calcUnit.date_range" class="group-box">
          <span class="group-box__text">{{ $t('81822') }}:</span>
          <span class="group-box__value">{{ calcUnit.date_range }}</span>
        </p>
        <p v-if="calcUnit.cost_price" class="group-box">
          <span class="group-box__text">{{ $t('81823') }}:</span>
          <span class="group-box__value">{{ calcUnit.cost_price }}</span>
        </p>
        <p v-if="calcUnit.retail_price && !isTour" class="group-box">
          <span class="group-box__text">{{ $t('81824') }}:</span>
          <span class="group-box__value">{{ calcUnit.retail_price }}</span>
        </p>
      </div>
      <div v-if="calcShowOperate && !lockMerchantActEditing" class="flex-box__right-box">
        <DropdownMenus
          :menus="manageScheduleMenus"
          @change="changeScheduleType"
          @changeVisible="changeVisible"
        >
          <template #default="scoped">
            <a-button
              v-tooltip="{
                visible: true,
                placement: 'top',
                content: $t('30114'),
                theme: 'yellow'
              }"
            >
              {{ $t('89085') }} <a-icon :type="scoped.visible ? 'up' : 'down'" style="font-size: 12px;" />
            </a-button>
          </template>
        </DropdownMenus>

        <!-- <a-button
          v-if="!isSubSku"
          type="danger"
          ghost
          v-bind="$root.bindIHTrack({ type: 'module', spm: 'Deactivate&Delete_Schedule' })"
          class="common-button-danger ghost-bgc-fff"
          @click="$emit('delete')"
        >
          {{ $t('80918') }}
        </a-button> -->
        <a-button
          type="primary"
          icon="carry-out"
          v-bind="$root.bindIHTrack({ type: 'module', spm: 'Edit_Schedule' })"
          @click="changeScheduleType(BULK_MANAGE_SCHEDULE_KEYS.add.key)"
        >
          {{ $t('81804') }}
        </a-button>
      </div>
    </div>

    <div
      id="manage_schedule_spm"
      :data-spm-module="getManageScheduleSpm"
      data-spm-virtual-item="__virtual"
    ></div>
    <div id="bulk_delete_spm" :data-spm-module="getBulkDelSpm" data-spm-virtual-item="__virtual"></div>
    <div
      id="bulk_deactivate_spm"
      :data-spm-module="getBulkDeactivateSpm"
      data-spm-virtual-item="__virtual"
    ></div>
    <div id="bulk_activate_spm" :data-spm-module="getBulkActivateSpm" data-spm-virtual-item="__virtual"></div>
  </div>
</template>

<script>
import { mapState, mapMutations, mapGetters } from 'vuex'
import DropdownMenus from '@activity/pages/package/units/components/DropdownMenus.vue'
import { BULK_MANAGE_SCHEDULE_KEYS } from '@activity/pages/package/units/utils/index.js'

const manage_schedule_mixin = {
  components: {
    DropdownMenus
  },
  props: {
    unitData: {
      type: Object,
      default: () => ({})
    },
    globalDataState: {
      type: Object,
      default: () => {
        return {}
      }
    },
    isAdd: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      BULK_MANAGE_SCHEDULE_KEYS
    }
  },
  computed: {
    ...mapGetters(['isTour']),
    manageScheduleMenus() {
      // eslint-disable-next-line no-unused-vars
      const { add, delete: deleteItem, ...other } = BULK_MANAGE_SCHEDULE_KEYS

      if (!this.isSubSku) {
        other.delete = deleteItem
      }

      return other
    },
    getManageScheduleSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `ManageSchedule?oid=${oid}&trg=manual`
    },
    getBulkDelSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `BulkDelete?oid=${oid}&trg=manual`
    },
    getBulkDeactivateSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `BulkDeactivate?oid=${oid}&trg=manual`
    },
    getBulkActivateSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `BulkActivate?oid=${oid}&trg=manual`
    }
  },
  methods: {
    ...mapMutations(['setBulkManageSchedule']),
    changeVisible(visible) {
      if (visible) {
        this.$tracker.track('action', '#manage_schedule_spm')
      }
    },
    changeScheduleType(type) {
      switch (type) {
        case BULK_MANAGE_SCHEDULE_KEYS.delete.key: {
          this.$tracker.track('action', '#bulk_delete_spm')
          break
        }
        case BULK_MANAGE_SCHEDULE_KEYS.deactivate.key: {
          this.$tracker.track('action', '#bulk_deactivate_spm')
          break
        }
        case BULK_MANAGE_SCHEDULE_KEYS.activate.key: {
          this.$tracker.track('action', '#bulk_activate_spm')
          break
        }
      }

      this.$emit('bulkEdit', { type })
    }
  }
}

export default {
  name: 'MerchantUnitMainHeader',
  mixins: [manage_schedule_mixin],
  props: {
    compData: {
      type: Object,
      default: () => {}
    },
    showOperate: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      showDelete: false,
      showEdit: false
    }
  },
  computed: {
    ...mapState(['lockMerchantActEditing']),
    isSubSku() {
      // 子sku
      return this.compData.share_rel_ship === 1
    },
    calcShowOperate() {
      // const { showOperate, compData } = this
      // return showOperate && compData.calendarCompleted

      // 只要有日历就能使用批量的接口
      const { showOperate } = this
      return showOperate
    },
    calcShowBox() {
      const { calcUnit, compData } = this
      return calcUnit.date_range || calcUnit.cost_price || calcUnit.retail_price || compData.calendarCompleted
    },
    calcUnit() {
      const { calendar_header } = this.compData || {}
      const { date_range, cost_price, retail_price } = calendar_header || {}
      return {
        date_range,
        cost_price,
        retail_price
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.unit-header {
  &-box {
    padding: 12px 20px;
    background: linear-gradient(0deg, rgba(0, 145, 255, 0.05), rgba(0, 145, 255, 0.05)), #fff;
  }
}
.flex-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  &__left-box {
    display: flex;
    align-items: center;
    .group-box {
      margin: 0;
      color: #000;
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      &:not(:last-of-type) {
        margin-right: 40px;
      }
      &__value {
        font-weight: 500;
        margin-left: 8px;
      }
    }
  }
  &__right-box {
    flex: none;
    button {
      &:not(:last-of-type) {
        margin-right: 8px;
      }
    }
  }
}
</style>
