<template>
  <div class="unit-main-box js-unit-main-box" :data-spm-page="getPageSpm">
    <div class="unit-main__title-box">
      <span>{{ $t('81825') }}</span>
      <div class="btn-rule-settings-box">
        <span
          v-if="!lockMerchantActEditing"
          v-bind="$root.bindIHTrack({ type: 'module', spm: 'Edit_Rule' })"
          class="common-btn-link"
          @click="showSettings"
        >
          <a-icon class="btn-icon-left" type="setting" />
          <span class="btn-text">{{ $t('81802') }}</span>
        </span>
      </div>
    </div>
    <div
      class="unit-main__content-box js-unit-main-content-box"
      :class="{ empty: !showCalender, unfinished: calcUnfinished }"
    >
      <UnitMainHeader
        :comp-data="calcUnitPriceData"
        :show-operate="showCalender"
        :unit-data="unitData"
        :global-data-state="globalDataState"
        :is-add="isAdd"
        @edit="$emit('edit')"
        @bulkEdit="onEmitBulkEdit"
        @delete="$emit('delete')"
      />
      <UnitCalendarSetting v-if="showCalender" :data="unitData" :calendar-date="calendarDate" />
      <UnitCreate
        v-else-if="typeof calcUnfinished === 'boolean' && !calcUnfinished"
        class="unit-create-style"
        :sku-id="calcSkuId"
        @copy="handleCopy"
        @bulkEdit="onEmitBulkEdit"
      />
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import UnitMainHeader from '@activity/pages/package/units/unit/main/header.vue'
import UnitCalendarSetting from '@activity/pages/package/units/unit/calendar-setting/index.vue'
import { UNIT_STEPS } from '@activity/pages/package/package_const.js'
import UnitCreate from '@activity/pages/package/units/unit/create.vue'

export default {
  components: {
    UnitCalendarSetting,
    UnitMainHeader,
    UnitCreate
  },
  provide() {
    return {
      skuId: this.calcSkuId
    }
  },
  props: {
    globalDataState: {
      type: Object,
      default: () => {
        return {}
      }
    },
    unitData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    calendarDate: {
      type: Date,
      default: () => {
        return new Date()
      }
    },
    showCalender: {
      type: Boolean,
      default: false
    },
    isAdd: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {
    ...mapState({
      ruleSetting: (state) => state.skuRuleSetting
    }),
    ...mapState(['lockMerchantActEditing']),
    getPageSpm() {
      const oid = `activity_${this.$route.params.id || 0}`
      return `UnitCalendar?oid=${oid}`
    },
    calcUnfinished() {
      return this.ruleSetting?.unfinished
    },
    calcSkuId() {
      return klook.parse2id(this.$route.query.sku_id)
    },
    calcUnitPriceData() {
      const { globalDataState, unitData } = this
      const { calendar_header, sku_id } = globalDataState[ADMIN_API.act.get_calendar_by_sku_id] || {}
      if (sku_id !== this.calcSkuId) {
        return {}
      }
      const { share_rel_ship } = unitData
      const calendarCompleted = this.calcCalendarCompleted(unitData)
      return { calendar_header, calendarCompleted, share_rel_ship }
      // return {
      //   calendar_header: {
      //     date_range: '2022.11.04 - 2023.01.01',
      //     cost_price: 'CNY 200', // 空字符串 则前端不展示
      //     retail_price: 'CNY 300' // 空字符串 则前端不展示
      //   }
      // }
    },
    calcCalendarCompleted() {
      return (unitItem) => {
        const { step } = unitItem || {}
        return step?.includes(UNIT_STEPS[1])
      }
    }
  },
  methods: {
    onEmitBulkEdit(data) {
      this.$emit('bulkEdit', data)
    },
    handleCopy(options = []) {
      this.$emit('copy', options)
    },
    showSettings() {
      klook.bus.$emit('showRuleSetting')
    }
  }
}
</script>

<style lang="scss" scoped>
.unit-create-style {
  background-color: #fff;
}
.unit-main {
  &__content-box {
    border: 1px solid #0091ff;
    border-radius: 4px;
    overflow: hidden;
    &.empty {
      border: 1px solid #0091ff;
      border-radius: 4px;
      margin: 10px 0;
    }
    &.unfinished {
      border: none;
    }
  }
  &__title-box {
    font-size: 18px;
    font-weight: 600;
    line-height: 40px;
    color: rgba(0, 0, 0, 0.85);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.common-btn-link {
  color: #437dff;
  cursor: pointer;
  font-size: 14px;
  line-height: 22px;
  font-weight: 400;
  .btn-icon-left {
    margin-right: 8px;
  }
  &.danger {
    color: #ff4d4f;
  }
}

.btn-rule-settings-box {
  margin: 0 0 0 32px;
  flex: none;
}
</style>
