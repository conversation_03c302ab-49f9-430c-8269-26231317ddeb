# Advanced Filters 组件

一个可复用的高级筛选组件，支持 Unit 多选和 Variant 树形结构展示。

## 功能特性

- ✅ Unit 多选下拉框
- ✅ Variant 树形结构展示
- ✅ 支持传入数据或自动获取数据
- ✅ 使用 Ant Design Vue 组件库
- ✅ 良好的封装性，可在多处调用
- ✅ 支持加载状态和错误处理

## 使用方式

### 1. 基本用法（传入数据）

```vue
<template>
  <AdvancedFilters
    :units="unitData"
    :variants="variantData"
    :value="filters"
    @change="handleFiltersChange"
  />
</template>

<script>
import AdvancedFilters from '@aidRevamp/components/advanced-filters/index.vue'

export default {
  components: {
    AdvancedFilters
  },
  data() {
    return {
      unitData: [...], // Unit 数据
      variantData: [...], // Variant 数据
      filters: {
        units: [],
        variants: []
      }
    }
  },
  methods: {
    handleFiltersChange(filters) {
      this.filters = filters
      console.log('Selected filters:', filters)
    }
  }
}
</script>
```

### 2. 自动获取数据

```vue
<template>
  <AdvancedFilters
    :spu-id="12345"
    language="en_US"
    page-from="admin"
    :value="filters"
    @change="handleFiltersChange"
  />
</template>
```

### 3. 在 ScheduleList 中使用

```vue
<template>
  <ScheduleList
    :sku-list="skuList"
    :selected-sku-ids.sync="selectedSkuIds"
    :spu-id="spuId"
    :language="language"
    @advanced-filters-change="handleAdvancedFiltersChange"
  />
</template>
```

## Props

| 参数     | 类型          | 默认值                    | 说明                     |
| -------- | ------------- | ------------------------- | ------------------------ |
| units    | Array         | []                        | Unit 数据数组            |
| variants | Array         | []                        | Variant 数据数组         |
| value    | Object        | {units: [], variants: []} | 选中的筛选条件           |
| spuId    | Number/String | null                      | SPU ID，用于自动获取数据 |
| language | String        | 'en_US'                   | 语言设置                 |
| pageFrom | String        | 'admin'                   | 页面来源                 |

## Events

| 事件名 | 参数                                     | 说明               |
| ------ | ---------------------------------------- | ------------------ |
| change | filters: {units: Array, variants: Array} | 筛选条件变化时触发 |

## 数据格式

### Unit 数据格式

```javascript
{
  unit_type: number,
  unit_type_id: number,
  local_name: string,
  en_name: string,
  is_customise_unit_type: boolean,
  use_status: number,
  name_text_id: number,
  control?: {
    is_age_required: boolean
  }
}
```

### Variant 数据格式

```javascript
{
  variant_id: number,
  variant_name: string,
  variant_field_key: string,
  allow_customise_value: number,
  variant_priority: number,
  use_status: number,
  edit_permission: number,
  redirect_menu_key: string,
  redirect_module_name: string,
  redirect_menu_name: string,
  variant_value_list: [
    {
      variant_value_id: number,
      variant_value_name: string,
      variant_value_priority: number,
      use_status: number,
      variant_value_field_key: string,
      field_key_bind_id: number,
      edit_permission: number
    }
  ]
}
```

## API 依赖

组件依赖以下 API：

- `getUnitType(params)` - 获取 Unit 类型数据
- `getSkuAndVariant(params)` - 获取 Variant 数据

这些 API 来自 `@aidRevamp/api/inventory-schedule`。

## 样式定制

组件使用 SCSS 编写样式，支持以下 CSS 变量定制：

```scss
.advanced-filters {
  // 自定义样式
}
```

## 更新日志

### v1.2.0 - 2024-01-XX

#### Vuex Store 集成

- ✅ 创建了 `filters` Vuex 模块来管理 unit 和 variant 数据
- ✅ 支持数据缓存，避免重复请求
- ✅ ScheduleList 组件现在使用 store 中的数据替代 mock 数据
- ✅ 自动在组件创建时获取筛选数据

#### 搜索功能完善

- ✅ 实现了点击搜索按钮和按 Enter 键的搜索功能
- ✅ 搜索基于当前输入框中的文本进行关键字匹配
- ✅ 支持按 SKU ID 和 SKU 名称搜索
- ✅ 搜索结果通过 `filteredSkuList` 计算属性实时更新

### v1.1.0 - 2024-01-XX

#### ScheduleList 组件搜索功能增强

- ✅ 添加了防抖搜索功能（300ms 延迟）
- ✅ 支持按 SKU ID 和名称搜索
- ✅ 保留了原有的下拉功能

#### ApiSyncSetting 组件集成

- ✅ 在 ApiSyncSetting 组件中集成了 AdvancedFilters
- ✅ 支持按 Unit 类型筛选 unitList
- ✅ 自动转换 unitList 数据格式为筛选组件所需格式
- ✅ 筛选条件变化时自动更新选中状态

### 使用示例

#### 1. 使用 Vuex Store 的 ScheduleList

```vue
<template>
  <ScheduleList
    :sku-list="skuList"
    :selected-sku-ids.sync="selectedSkuIds"
    :spu-id="spuId"
    :language="language"
    @advanced-filters-change="handleAdvancedFiltersChange"
  />
</template>

<script>
export default {
  data() {
    return {
      spuId: 12345,
      language: 'en_US',
      selectedSkuIds: [],
      skuList: [...]
    }
  },
  methods: {
    handleAdvancedFiltersChange(filters) {
      console.log('Filters changed:', filters)
      // 处理筛选条件变化
    }
  }
}
</script>
```

#### 2. ScheduleList 搜索功能

```vue
<template>
  <ScheduleList
    :sku-list="skuList"
    :selected-sku-ids.sync="selectedSkuIds"
    :spu-id="spuId"
    :language="language"
    @advanced-filters-change="handleAdvancedFiltersChange"
  />
</template>
```

搜索功能特性：

- 输入时有 300ms 防抖延迟
- 支持按 SKU ID 搜索（数字匹配）
- 支持按 SKU 名称搜索（文本匹配）
- 搜索结果实时更新下拉选项

#### 2. ApiSyncSetting 筛选功能

```vue
<template>
  <ApiSyncSetting
    :visible.sync="visible"
    :unit-list="unitList"
    @updated="handleUpdated"
  />
</template>
```

筛选功能特性：

- 自动从 unitList 提取 Unit 类型信息
- 支持按 Unit 类型筛选显示的 SKU
- 筛选后自动更新全选/部分选中状态
- 保持原有的同步功能不变

## 注意事项

1. 如果同时传入了 `units`/`variants` 数据和 `spuId`，优先使用传入的数据
2. 组件会自动过滤 `use_status !== 1` 的数据
3. 树形结构默认展开所有节点
4. 支持搜索功能（Unit 下拉框）
5. ScheduleList 搜索功能使用防抖，避免频繁触发
6. ApiSyncSetting 筛选功能会自动处理选中状态的更新
