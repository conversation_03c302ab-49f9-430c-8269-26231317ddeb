<template>
  <div class="advanced-filters">
    <div class="controls-wrapper">
      <!-- 筛选按钮 -->
      <a-button type="primary" icon="filter" :class="{ 'has-filters': hasActiveFilters }" @click="showModal">
        Advanced filters
      </a-button>
      <!-- 发布状态快捷筛选 -->
      <div v-if="hasStatusFilter" class="status-filters">
        <a-checkbox
          :checked="selectedStatuses.includes(true)"
          @change="onStatusChange(true, $event.target.checked)"
        >
          Published
        </a-checkbox>
        <a-checkbox
          :checked="selectedStatuses.includes(false)"
          @change="onStatusChange(false, $event.target.checked)"
        >
          Unpublished
        </a-checkbox>
      </div>
    </div>

    <div v-if="selectedTagList.length" class="selected-tags">
      <span style="margin-right: 8px">Selected</span>
      <a-tag
        v-for="(tag, index) in selectedTagList"
        :key="index"
        closable
        style="margin-bottom: 4px"
        @close="removeTag(tag)"
      >
        {{ tag.label }}
      </a-tag>
    </div>

    <a-modal
      :visible="visible"
      title="Advanced filters"
      :width="800"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <div>
        <div class="filters-content">
          <!-- Unit 多选 -->
          <div class="filter-section">
            <h4 class="filter-title">Unit:</h4>
            <a-select
              v-model="selectedUnits"
              mode="multiple"
              placeholder="Type to select"
              style="width: 100%"
              allow-clear
              show-search
              :filter-option="filterUnitOption"
            >
              <a-select-option
                v-for="unit in unitOptions"
                :key="unit.unit_type_id"
                :value="unit.unit_type_id"
              >
                {{ unit.local_name || unit.en_name }}
              </a-select-option>
            </a-select>
          </div>

          <!-- Variant 树形结构 -->
          <div class="filter-section">
            <h4 class="filter-title">Variant value:</h4>
            <a-tree-select
              v-model="selectedVariants"
              style="width: 100%"
              :tree-data="variantTreeData"
              tree-checkable
              placeholder="Type to select"
              allow-clear
              :dropdown-style="{ maxHeight: '300px', overflow: 'auto' }"
              @change="onVariantSelectChange"
            />
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { getUnitType, getVariantList } from '@aidRevamp/api/commom'

export default {
  name: 'AdvancedFilters',
  props: {
    // Unit 数据
    units: {
      type: Array,
      default: () => []
    },
    // Variant 数据
    variants: {
      type: Array,
      default: () => []
    },
    value: {
      type: Object,
      default: () => ({
        units: [],
        variants: [],
        statuses: []
      })
    },
    hasStatusFilter: {
      type: Boolean,
      default: false
    },
    language: {
      type: String,
      default: 'en_US'
    },
    pageFrom: {
      type: String,
      default: 'admin'
    }
  },
  data() {
    return {
      visible: false,
      confirmLoading: false,
      // Real state
      selectedUnits: [],
      selectedVariants: [],
      selectedStatuses: [],
      // internal data
      internalUnits: [],
      internalVariants: [],
      loading: false,
      hasFetched: false
    }
  },
  computed: {
    // Unit 选项
    unitOptions() {
      const unitData = this.units.length ? this.units : this.internalUnits
      return unitData
    },
    // Variant 树形数据
    variantTreeData() {
      const useInternalData = !this.variants.length && this.internalVariants.length > 0
      const variantData = useInternalData ? this.internalVariants : this.variants

      if (useInternalData) {
        // 新接口数据结构
        return variantData.map((variant) => ({
          title: variant.en_name,
          key: variant.variant_id,
          value: variant.variant_id,
          children: (variant.value_list || []).map((value) => ({
            title: value.en_name,
            key: value.variant_value_id,
            value: value.variant_value_id,
            isLeaf: true
          }))
        }))
      }

      // 原始 props 数据结构
      return variantData?.map((variant) => ({
        title: variant.variant_name,
        key: variant.variant_id,
        value: variant.variant_id,
        children: variant.variant_value_list
          ? variant.variant_value_list
              .filter((value) => value.use_status === 1)
              .map((value) => ({
                title: value.variant_value_name,
                key: value.variant_value_id,
                value: value.variant_value_id,
                isLeaf: true
              }))
          : []
      }))
    },
    // 是否有激活的筛选条件
    hasActiveFilters() {
      return (
        this.selectedUnits.length > 0 || this.selectedVariants.length > 0 || this.selectedStatuses.length > 0
      )
    },
    // 选中标签列表
    selectedTagList() {
      const unitTags = this.selectedUnits.map((id) => {
        const unit = this.unitOptions.find((u) => u.unit_type_id === id) || {}
        return {
          key: `unit_${id}`,
          label: unit.local_name || unit.en_name || id,
          type: 'unit',
          id
        }
      })

      const variantValueMap = {}
      const useInternalData = !this.variants.length && this.internalVariants.length > 0
      const variantData = useInternalData ? this.internalVariants : this.variants

      if (useInternalData) {
        variantData.forEach((variant) => {
          ;(variant.value_list || []).forEach((val) => {
            variantValueMap[val.variant_value_id] = val.en_name
          })
        })
      } else {
        variantData?.forEach((variant) => {
          ;(variant.variant_value_list || []).forEach((val) => {
            variantValueMap[val.variant_value_id] = val.variant_value_name
          })
        })
      }

      const variantTags = this.selectedVariants.map((valueId) => {
        return {
          key: `variant_${valueId}`,
          label: variantValueMap[valueId] || valueId,
          type: 'variant',
          id: valueId
        }
      })

      const statusTags = this.selectedStatuses.map((status) => ({
        key: `status_${status}`,
        label: status ? 'Published' : 'Unpublished',
        type: 'status',
        id: status
      }))

      return [...statusTags, ...unitTags, ...variantTags]
    },
    // 是否需要获取数据
    needFetchData() {
      return !this.units.length || !this.variants.length
    },
    // a-select value for variants (保留兼容性)
    selectedVariantValueKeys() {
      // 不再需要过滤，直接返回所有选中的值
      return this.selectedVariants
    },
    // a-select options for variants
    allVariantValues() {
      const values = []
      const useInternalData = !this.variants.length && this.internalVariants.length > 0
      const variantData = useInternalData ? this.internalVariants : this.variants

      if (useInternalData) {
        variantData.forEach((variant) => {
          ;(variant.value_list || []).forEach((value) => {
            values.push({
              key: value.variant_value_id,
              label: value.en_name
            })
          })
        })
      } else {
        variantData.forEach((variant) => {
          if (variant.variant_value_list) {
            variant.variant_value_list.forEach((value) => {
              if (value.use_status === 1) {
                values.push({
                  key: value.variant_value_id,
                  label: value.variant_value_name
                })
              }
            })
          }
        })
      }
      return values
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal) {
          this.syncStateFromValue(newVal)
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    syncStateFromValue(value) {
      this.selectedUnits = [...(value.units || [])]
      this.selectedStatuses = [...(value.statuses || [])]
      this.selectedVariants = [...(value.variants || [])]
    },
    // 获取数据
    async fetchData() {
      this.loading = true
      try {
        await Promise.all([this.fetchUnits(), this.fetchVariants()])
        this.hasFetched = true
      } catch (error) {
        console.error('Failed to fetch filter data:', error)
        this.$message?.error('Failed to load filter data')
      } finally {
        this.loading = false
      }
    },
    async fetchUnits() {
      if (this.units.length) return
      const data = await getUnitType({
        language: this.language,
        page: 1,
        limit: 100,
        create_author_id: 0
      })
      if (data?.items)
        this.internalUnits = (data.items || []).map((item) => ({
          unit_type_id: item.unit_type_id,
          en_name: item.en_name
        }))
    },
    async fetchVariants() {
      if (this.variants.length) return
      const data = await getVariantList({
        language: this.language,
        page: 1,
        limit: 100,
        create_author_id: 0
      })
      if (data?.items) this.internalVariants = data.items || []
    },
    showModal() {
      this.visible = true

      if (this.needFetchData && !this.hasFetched) {
        this.fetchData()
      }
    },
    handleOk() {
      this.emitFilterChange()
      this.visible = false
    },
    handleCancel() {
      this.visible = false
    },
    filterUnitOption(input, option) {
      const text = option.componentOptions.children[0].text.toLowerCase()
      return text.includes(input.toLowerCase())
    },
    onVariantSelectChange(selectedKeys) {
      this.selectedVariants = selectedKeys
    },
    removeTag(tag) {
      if (tag.type === 'unit') {
        this.selectedUnits = this.selectedUnits.filter((id) => id !== tag.id)
      } else if (tag.type === 'variant') {
        this.selectedVariants = this.selectedVariants.filter((v) => v !== tag.id)
      } else if (tag.type === 'status') {
        this.selectedStatuses = this.selectedStatuses.filter((s) => s !== tag.id)
      }
      this.emitFilterChange() // 外部标签关闭时需要触发
    },
    onStatusChange(status, checked) {
      const newStatuses = new Set(this.selectedStatuses)
      if (checked) {
        newStatuses.add(status)
      } else {
        newStatuses.delete(status)
      }
      this.selectedStatuses = Array.from(newStatuses)
      this.emitFilterChange() // 状态变化时需要触发，因为这是外部的快捷筛选
    },
    emitFilterChange() {
      const payload = {
        units: [...this.selectedUnits],
        variants: [...this.selectedVariants],
        statuses: [...this.selectedStatuses]
      }

      this.$emit('change', payload)
    }
  }
}
</script>

<style lang="scss" scoped>
.advanced-filters {
  background-color: #e6f1ff;
  padding: 16px;
  .controls-wrapper {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .has-filters {
    background-color: #1890ff;
    border-color: #1890ff;
    position: relative;
  }

  .selected-tags {
    margin-top: 8px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;
  }

  .status-filters {
    display: flex;
    gap: 16px;
  }
}

.filters-content {
  .filter-section {
    display: flex;
    align-items: center;
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .filter-title {
    margin-bottom: 0;
    margin-right: 16px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    flex-basis: 110px;
    flex-shrink: 0;
  }
}

.variant-tree-dropdown {
  padding: 8px;
  max-height: 300px;
  overflow-y: auto;

  :deep(.ant-tree) {
    .ant-tree-node-content-wrapper {
      padding: 2px 4px;
      border-radius: 4px;

      &:hover {
        background-color: #f5f5f5;
      }
    }

    .ant-tree-checkbox {
      margin-right: 8px;
    }
  }
}
</style>
