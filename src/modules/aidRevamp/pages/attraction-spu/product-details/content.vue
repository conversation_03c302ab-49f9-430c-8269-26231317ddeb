<template>
  <div>
    <div class="tpl_package_copy-container common-copy-wrap">
      <a-button @click="handleShowCopyDetailModal">
        {{ $t('21857') }}
      </a-button>
    </div>
    <CopyDetailModal
      :package_id="package_id"
      :activity_id="activity_id"
      :visible.sync="copyDetailVisible"
      :group-list="groupList"
      :all-groups="allGroups"
      @refreshWhenFrom="refreshWhenFrom"
    />
    <slot />
  </div>
</template>

<script>
import packageCopyMixins from './package-copy-mixin'
import CopyDetailModal from './copy-detail-modal.vue'

export default {
  name: 'AttractionBaseInfoContent',
  components: {
    CopyDetailModal
  },
  mixins: [packageCopyMixins]
}
</script>
<style lang="scss" scoped></style>
